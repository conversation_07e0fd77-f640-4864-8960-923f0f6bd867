<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Taylor Series Expansions</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
        }
        h1 {
            color: #1a5276;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 0.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }
        h2 {
            color: #2471a3;
            border-bottom: 1px solid #a9cce3;
            padding-bottom: 0.2em;
        }
        h3 {
            color: #2e86c1;
        }
        .theorem {
            background-color: #eaf2f8;
            border-left: 4px solid #3498db;
            padding: 10px 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .note {
            background-color: #fef9e7;
            border-left: 4px solid #f1c40f;
            padding: 10px 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .example {
            background-color: #eafaf1;
            border-left: 4px solid #2ecc71;
            padding: 10px 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .definition {
            background-color: #f4ecf7;
            border-left: 4px solid #8e44ad;
            padding: 10px 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .visualization {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        .toc {
            background-color: #eaecee;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc a {
            color: #2980b9;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        .step {
            counter-increment: step-counter;
            margin-bottom: 20px;
        }
        .step::before {
            content: "Step " counter(step-counter) ": ";
            font-weight: bold;
            color: #2980b9;
        }
        .formula-box {
            overflow-x: auto;
            margin: 20px 0;
        }
    </style>
    <!-- MathJax Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                tags: 'ams'
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <!-- Load MathJax -->
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
</head>
<body>
    <h1>Understanding Taylor Series Expansions</h1>
    
    <div class="definition">
        <p>A <strong>Taylor series expansion</strong> is a powerful mathematical tool that approximates functions using polynomials. It's fundamental in calculus, numerical analysis, physics, and engineering.</p>
    </div>

    <div class="toc">
        <h3>Contents</h3>
        <ul>
            <li><a href="#introduction">1. Introduction to Taylor Series</a></li>
            <li><a href="#single-variable">2. Single Variable Taylor Expansions</a>
                <ul>
                    <li><a href="#basic-form">2.1 Basic Form</a></li>
                    <li><a href="#lagrange-form">2.2 Lagrange Form</a></li>
                    <li><a href="#remainder-bounds">2.3 Remainder Bounds</a></li>
                    <li><a href="#other-forms">2.4 Other Forms</a></li>
                </ul>
            </li>
            <li><a href="#multivariable">3. Multivariable Taylor Expansions</a>
                <ul>
                    <li><a href="#mv-basic-form">3.1 Basic Form</a></li>
                    <li><a href="#mv-lagrange-form">3.2 Lagrange Form</a></li>
                    <li><a href="#third-order">3.3 Third Order Expansions</a></li>
                </ul>
            </li>
            <li><a href="#visualization">4. Visual Understanding</a></li>
            <li><a href="#applications">5. Applications</a></li>
        </ul>
    </div>

    <h2 id="introduction">1. Introduction to Taylor Series</h2>

    <p>Taylor series expansions allow us to approximate complicated functions using polynomials. These approximations become increasingly accurate as we include more terms in our expansion.</p>

    <div class="visualization">
        <svg width="600" height="300" viewBox="0 0 600 300">
            <!-- Axes -->
            <line x1="50" y1="250" x2="550" y1="250" x2="550" stroke="black" stroke-width="2"/>
            <line x1="50" y1="50" x2="50" y1="250" x2="250" stroke="black" stroke-width="2"/>
            
            <!-- Original function: sin(x) -->
            <path d="M 50 150 Q 100 50, 150 150 Q 200 250, 250 150 Q 300 50, 350 150 Q 400 250, 450 150 Q 500 50, 550 150" 
                  fill="none" stroke="#1a5276" stroke-width="3"/>
            
            <!-- First-order approximation -->
            <line x1="150" y1="150" x2="350" y1="50" x2="250" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
            
            <!-- Second-order approximation -->
            <path d="M 150 150 Q 200 75, 250 150 Q 300 225, 350 150" 
                  fill="none" stroke="#27ae60" stroke-width="2.5" stroke-dasharray="3,3"/>
            
            <!-- Third-order approximation -->
            <path d="M 150 150 Q 183 83, 217 117 Q 250 150, 283 183 Q 317 217, 350 150" 
                  fill="none" stroke="#f39c12" stroke-width="2" stroke-dasharray="2,2"/>
            
            <!-- Legend -->
            <circle cx="480" cy="80" r="5" fill="#1a5276"/>
            <text x="490" y="85" font-family="Arial" font-size="12">Original Function</text>
            
            <line x1="460" y1="100" x2="480" y1="100" x2="100" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
            <text x="490" y="105" font-family="Arial" font-size="12">1st Order</text>
            
            <line x1="460" y1="120" x2="480" y1="120" x2="120" stroke="#27ae60" stroke-width="2.5" stroke-dasharray="3,3"/>
            <text x="490" y="125" font-family="Arial" font-size="12">2nd Order</text>
            
            <line x1="460" y1="140" x2="480" y1="140" x2="140" stroke="#f39c12" stroke-width="2" stroke-dasharray="2,2"/>
            <text x="490" y="145" font-family="Arial" font-size="12">3rd Order</text>
            
            <!-- Expansion point -->
            <circle cx="250" cy="150" r="5" fill="red"/>
            <text x="240" y="170" font-family="Arial" font-size="12">x₀</text>
        </svg>
    </div>

    <h2 id="single-variable">2. Single Variable Taylor Expansions</h2>
    
    <h3 id="basic-form">2.1 Basic Form</h3>
    
    <div class="step">
        <p>The Taylor series expansion approximates a function around a point $x_0$ using the function's derivatives at that point:</p>
        
        <div class="theorem">
            <p><strong>Theorem (Taylor):</strong> If $f: \mathbb{R} \rightarrow \mathbb{R}$ is $n$ times differentiable at a point $x_0$, then:</p>
            
            <div class="formula-box">
                $$f(x) = \sum_{k=0}^{n} \frac{f^{(k)}(x_0)}{k!}(x-x_0)^k + R_n(x,x_0)$$
            </div>
            
            <p>where $R_n$ is the remainder term that satisfies:</p>
            
            <div class="formula-box">
                $$R_n(x,x_0) = o(|x-x_0|^n) \text{ as } x \rightarrow x_0$$
            </div>
            
            <p>This form with the little-o notation is sometimes called the "Peano form" of the remainder.</p>
        </div>
    </div>
    
    <div class="example">
        <p><strong>Example:</strong> For $f(x) = e^x$ at $x_0 = 0$:</p>
        <p>$f(0) = 1$, $f'(0) = 1$, $f''(0) = 1$, and so on...</p>
        <p>The Taylor series is: $e^x = 1 + x + \frac{x^2}{2!} + \frac{x^3}{3!} + \ldots$</p>
    </div>
    
    <h3 id="lagrange-form">2.2 Lagrange Form</h3>
    
    <div class="step">
        <p>The Lagrange form gives a more explicit expression for the remainder term:</p>
        
        <div class="theorem">
            <p><strong>Theorem (Taylor with Lagrange Remainder):</strong> If $f^{(n+1)}$ exists over an open interval containing $(x,x_0)$, then there exists $\bar{x} \in (x,x_0)$ such that:</p>
            
            <div class="formula-box">
                $$R_n(x,x_0) = \frac{f^{(n+1)}(\bar{x})}{(n+1)!}(x-x_0)^{n+1}$$
            </div>
            
            <p>This is also known as the "mean-value form" since it relies on the mean value theorem.</p>
        </div>
        
        <div class="note">
            <p>Comparing the Basic and Lagrange forms for a second-order expansion:</p>
            
            <div class="formula-box">
                \begin{align}
                \text{(Basic) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(x_0)(x-x_0)^2 + o(|x-x_0|^2) \\
                \text{(Lagrange) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(\bar{x})(x-x_0)^2
                \end{align}
            </div>
            
            <p>In the Lagrange form, we get a simpler expression, but it requires $f''$ to exist along the entire interval from $x$ to $x_0$, not just at $x_0$.</p>
        </div>
    </div>
    
    <h3 id="remainder-bounds">2.3 Remainder Bounds</h3>
    
    <div class="step">
        <p>We can bound the remainder term if we can bound the derivative over the interval:</p>
        
        <div class="theorem">
            <p><strong>Theorem (Lagrange Error Bound):</strong> If $f^{(n+1)}$ is continuous over an open interval containing $(x,x_0)$ and there exists $M$ such that $|f^{(n+1)}(a)| \leq M$ for all $a \in (x,x_0)$, then:</p>
            
            <div class="formula-box">
                $$R_n(x,x_0) \leq \frac{M}{(n+1)!}|x-x_0|^{n+1}$$
            </div>
        </div>
        
        <p>This allows us to express Taylor's theorem using both little-o and big-O notation:</p>
        
        <div class="formula-box">
            \begin{align}
            \text{(Basic) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(x_0)(x-x_0)^2 + o(|x-x_0|^2) \\
            \text{(Big O) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(x_0)(x-x_0)^2 + O(|x-x_0|^3)
            \end{align}
        </div>
    </div>
    
    <h3 id="other-forms">2.4 Other Forms</h3>
    
    <div class="step">
        <p>The remainder term can be expressed in several other ways:</p>
        
        <div class="theorem">
            <p><strong>Theorem (Integral Form):</strong> If $f^{(n+1)}$ is continuous over an open interval containing $(x,x_0)$, then:</p>
            
            <div class="formula-box">
                $$R_n(x,x_0) = \int_{x_0}^{x} \frac{f^{(n+1)}(t)}{(n+1)!}(x-t)^n dt$$
            </div>
        </div>
        
        <p>Other forms include the Cauchy form and the Roche-Schlömilch form, each useful in different contexts.</p>
    </div>
    
    <h2 id="multivariable">3. Multivariable Taylor Expansions</h2>
    
    <p>The Taylor series concept extends to functions of multiple variables, where derivatives become gradients and Hessians.</p>
    
    <h3 id="mv-basic-form">3.1 Basic Form</h3>
    
    <div class="step">
        <p>For functions of multiple variables, the basic form uses vector and matrix notation:</p>
        
        <div class="theorem">
            <p><strong>Theorem (First-Order):</strong> If $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is differentiable at a point $x_0$, then:</p>
            
            <div class="formula-box">
                $$f(x) = f(x_0) + \nabla f(x_0)^\top (x-x_0) + o(\|x-x_0\|)$$
            </div>
            
            <p>where $\nabla f(x_0)$ is the gradient of $f$ at $x_0$.</p>
        </div>
        
        <div class="theorem">
            <p><strong>Theorem (Second-Order):</strong> If $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is twice differentiable at a point $x_0$, then:</p>
            
            <div class="formula-box">
                $$f(x) = f(x_0) + \nabla f(x_0)^\top (x-x_0) + \frac{1}{2}(x-x_0)^\top \nabla^2 f(x_0) (x-x_0) + o(\|x-x_0\|^2)$$
            </div>
            
            <p>where $\nabla^2 f(x_0)$ is the Hessian matrix of $f$ at $x_0$.</p>
        </div>
    </div>
    
    <h3 id="mv-lagrange-form">3.2 Lagrange Form</h3>
    
    <div class="step">
        <p>The Lagrange form also extends to multivariable functions:</p>
        
        <div class="note">
            <p>For multivariable theorems, "$\bar{x}$ on the line segment connecting $x$ and $x_0$" means there exists $w \in [0,1]$ such that $\bar{x} = wx + (1-w)x_0$.</p>
        </div>
        
        <div class="theorem">
            <p><strong>Theorem (First-Order):</strong> If $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is differentiable on $N_r(x_0)$ (a neighborhood of $x_0$ with radius $r$), then for any $x \in N_r(x_0)$, there exists $\bar{x}$ on the line segment connecting $x$ and $x_0$ such that:</p>
            
            <div class="formula-box">
                $$f(x) = f(x_0) + \nabla f(\bar{x})^\top (x-x_0)$$
            </div>
        </div>
        
        <div class="theorem">
            <p><strong>Theorem (Second-Order):</strong> If $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is twice differentiable on $N_r(x_0)$, then for any $x \in N_r(x_0)$, there exists $\bar{x}$ on the line segment connecting $x$ and $x_0$ such that:</p>
            
            <div class="formula-box">
                $$f(x) = f(x_0) + \nabla f(x_0)^\top (x-x_0) + \frac{1}{2}(x-x_0)^\top \nabla^2 f(\bar{x}) (x-x_0)$$
            </div>
        </div>
    </div>
    
    <h3 id="third-order">3.3 Third Order Expansions</h3>
    
    <div class="step">
        <p>For higher-order expansions, the notation becomes increasingly complex:</p>
        
        <div class="theorem">
            <p><strong>Theorem (Third-Order):</strong> If $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is three times differentiable on $N_r(x_0)$, then for any $x \in N_r(x_0)$, there exists $\bar{x}$ on the line segment connecting $x$ and $x_0$ such that:</p>
            
            <div class="formula-box">
                \begin{align}
                f(x) = f(x_0) &+ \sum_{j=1}^{d}\frac{\partial f(x_0)}{\partial x_j}(x_j-x_{0j}) \\
                &+ \frac{1}{2}\sum_{j=1}^{d}\sum_{k=1}^{d}\frac{\partial^2 f(x_0)}{\partial x_j \partial x_k}(x_j-x_{0j})(x_k-x_{0k}) \\
                &+ \frac{1}{6}\sum_{j=1}^{d}\sum_{k=1}^{d}\sum_{\ell=1}^{d}\frac{\partial^3 f(\bar{x})}{\partial x_j \partial x_k \partial x_\ell}(x_j-x_{0j})(x_k-x_{0k})(x_\ell-x_{0\ell})
                \end{align}
            </div>
            
            <p>where $\frac{\partial f(x_0)}{\partial x_j}$ is shorthand for $\frac{\partial f(x)}{\partial x_j}$ evaluated at $x_0$.</p>
        </div>
        
        <div class="note">
            <p>Important note: Lagrange-type results do not hold for vector-valued functions. For each component function, there exists a different point $\bar{x}$ where the remainder term is evaluated.</p>
        </div>
    </div>
    
    <h2 id="visualization">4. Visual Understanding</h2>
    
    <div class="visualization">
        <svg width="600" height="400" viewBox="0 0 600 400">
            <!-- 3D Coordinate system -->
            <line x1="100" y1="300" x2="500" y1="300" x2="300" stroke="black" stroke-width="1.5"/>
            <line x1="100" y1="300" x2="100" y1="100" x2="300" stroke="black" stroke-width="1.5"/>
            <line x1="100" y1="300" x2="300" y1="350" x2="350" stroke="black" stroke-width="1.5"/>
            
            <!-- Labels -->
            <text x="510" y="300" font-family="Arial" font-size="14">x</text>
            <text x="90" y="90" font-family="Arial" font-size="14">f(x)</text>
            <text x="310" y="360" font-family="Arial" font-size="14">y</text>
            
            <!-- Function surface (simplified representation) -->
            <path d="M 100 200 C 150 150, 200 170, 250 190 C 300 210, 350 170, 400 150 C 450 130, 500 170, 550 200" 
                  fill="none" stroke="#3498db" stroke-width="2"/>
            
            <!-- Tangent plane -->
            <polygon points="250,190 280,180 350,210 320,220" 
                    fill="#2ecc71" fill-opacity="0.3" stroke="#27ae60" stroke-width="1.5"/>
            
            <!-- Point of expansion -->
            <circle cx="250" cy="190" r="5" fill="#e74c3c"/>
            <text x="240" y="175" font-family="Arial" font-size="12">(x₀,y₀)</text>
            
            <!-- Second-order approximation -->
            <path d="M 200 170 Q 250 190, 300 210" 
                  fill="none" stroke="#f39c12" stroke-width="2.5" stroke-dasharray="3,3"/>
            
            <!-- Legend -->
            <rect x="420" y="100" width="150" height="80" fill="white" stroke="#95a5a6" stroke-width="1"/>
            
            <line x1="430" y1="120" x2="460" y1="120" x2="120" stroke="#3498db" stroke-width="2"/>
            <text x="470" y="125" font-family="Arial" font-size="12">Function f(x,y)</text>
            
            <rect x="430" y1="140" width="30" height="10" fill="#2ecc71" fill-opacity="0.3" stroke="#27ae60" stroke-width="1.5"/>
            <text x="470" y="145" font-family="Arial" font-size="12">First-order approx.</text>
            
            <line x1="430" y1="160" x2="460" y1="160" x2="160" stroke="#f39c12" stroke-width="2.5" stroke-dasharray="3,3"/>
            <text x="470" y="165" font-family="Arial" font-size="12">Second-order approx.</text>
        </svg>
    </div>
    
    <h2 id="applications">5. Applications</h2>
    
    <div class="step">
        <p>Taylor expansions are fundamental in many areas:</p>
        <ul>
            <li><strong>Numerical Analysis:</strong> Function approximation and solving differential equations</li>
            <li><strong>Physics:</strong> Approximating complex physical models</li>
            <li><strong>Statistics:</strong> Asymptotic theory and statistical inference</li>
            <li><strong>Engineering:</strong> Control systems and signal processing</li>
            <li><strong>Machine Learning:</strong> Optimization algorithms and model analysis</li>
        </ul>
    </div>
    
    <div class="example">
        <p><strong>Example (Statistics):</strong> In statistics, Taylor expansions are used to derive asymptotic properties of estimators:</p>
        <p>For a consistent estimator $\hat{\theta}$, we can use Taylor expansion to show:</p>
        <div class="formula-box">
            $$\frac{1}{n}u(\hat{\theta}) = \frac{1}{n}u(\theta^*) - \{I(\theta^*) + o_p(1)\}\sqrt{n}(\hat{\theta} - \theta^*)$$
        </div>
        <p>where $u$ is the score function and $I$ is the Fisher information.</p>
    </div>

    <h2>Conclusion</h2>
    
    <p>Taylor series expansions provide a powerful framework for approximating functions. By understanding the different forms of Taylor's theorem and their conditions, you can apply these approximations effectively across various domains.</p>
    
    <p>The key concepts to remember are:</p>
    <ul>
        <li>Taylor expansions approximate functions using polynomials based on derivatives</li>
        <li>The remainder term quantifies the approximation error</li>
        <li>Different forms (Basic, Lagrange, Integral) offer different insights</li>
        <li>Multivariable expansions extend these concepts to higher dimensions</li>
    </ul>
</body>
</html> 