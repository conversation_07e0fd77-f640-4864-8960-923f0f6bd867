<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Gradient Descent Techniques</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --info-color: #1abc9c;
            --light-bg: #f8f9fa;
            --dark-bg: #eaecee;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        h1, h2, h3, h4 {
            color: var(--secondary-color);
            margin-top: 1.5em;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 0.5em;
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 0.3em;
        }
        
        h2 {
            border-bottom: 1px solid var(--primary-color);
            padding-bottom: 0.2em;
            margin-top: 2em;
        }
        
        .concept-box {
            background-color: #f8f9fa;
            border-left: 5px solid var(--primary-color);
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .algorithm {
            background-color: #eaf7fd;
            border-left: 5px solid var(--info-color);
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .note {
            background-color: #fef9e7;
            border-left: 5px solid var(--warning-color);
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .definition {
            background-color: #eafaf1;
            border-left: 5px solid var(--success-color);
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .visualization {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            overflow-x: auto;
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }
        
        .toc {
            background-color: var(--dark-bg);
            padding: 20px;
            border-radius: 5px;
            margin: 30px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .toc h2 {
            margin-top: 0;
            border-bottom: 1px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .toc a {
            color: var(--primary-color);
            text-decoration: none;
            line-height: 1.8;
        }
        
        .toc a:hover {
            text-decoration: underline;
            color: var(--accent-color);
        }
        
        .step {
            counter-increment: step-counter;
            margin-bottom: 25px;
            padding: 15px;
            border-radius: 5px;
            background-color: var(--light-bg);
        }
        
        .step::before {
            content: "Step " counter(step-counter) ": ";
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background-color: var(--primary-color);
            color: white;
            padding: 12px 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: var(--light-bg);
        }
        
        .comparison-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .highlight {
            color: var(--accent-color);
            font-weight: bold;
        }
        
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            flex: 1 1 300px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            background-color: white;
            border-top: 4px solid var(--primary-color);
        }
        
        .card h3 {
            margin-top: 0;
            color: var(--primary-color);
        }
        
        .pros-cons {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .pros, .cons {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        
        .pros {
            background-color: #eafaf1;
            border-left: 4px solid var(--success-color);
        }
        
        .cons {
            background-color: #fdedec;
            border-left: 4px solid var(--accent-color);
        }
        
        @media (max-width: 768px) {
            .pros-cons {
                flex-direction: column;
            }
            
            .container {
                flex-direction: column;
            }
        }
    </style>
    <!-- MathJax Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                tags: 'ams'
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <!-- Load MathJax -->
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
</head>
<body>
    <h1>Advanced Gradient Descent Techniques</h1>
    
    <div class="concept-box">
        <p>This tutorial explores advanced optimization techniques for gradient descent when dealing with large datasets. We'll cover coordinate descent, stochastic gradient descent, and subgradient methods, with detailed explanations and visualizations.</p>
    </div>

    <!-- Table of Contents -->
    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#introduction">1. Introduction</a></li>
            <li><a href="#coordinate-descent">2. Coordinate Descent</a></li>
            <li><a href="#sgd">3. Stochastic Gradient Descent</a></li>
            <li><a href="#subgradient">4. Subgradient Method</a></li>
            <li><a href="#comparison">5. Comparison of Methods</a></li>
        </ul>
    </div>

    <!-- Introduction Section -->
    <section id="introduction">
        <h2>1. Introduction</h2>
        
        <p>Gradient descent is a fundamental optimization algorithm used to minimize a differentiable function. It works by iteratively moving in the direction of steepest descent (negative of the gradient) to reach a local minimum. While highly effective, standard gradient descent can be computationally expensive when dealing with large datasets.</p>
        
        <div class="step">
            <p>The computational cost of each iteration in standard gradient descent is $O(nd)$, where:</p>
            <ul>
                <li>$n$ is the number of samples in the dataset</li>
                <li>$d$ is the number of features or parameters</li>
            </ul>
            <p>For very large datasets (large $n$) or high-dimensional problems (large $d$), this cost becomes prohibitive, making each iteration slow.</p>
        </div>
        
        <div class="visualization">
            <svg width="600" height="400" viewBox="0 0 600 400">
                <!-- Gradient Descent Computational Cost Visualization -->
                <!-- Coordinate system -->
                <line x1="50" y1="350" x2="550" y1="350" x2="350" stroke="black" stroke-width="1.5"/>
                <line x1="50" y1="50" x2="50" y1="350" x2="350" stroke="black" stroke-width="1.5"/>
                
                <!-- Axes labels -->
                <text x="300" y="390" font-family="Arial" font-size="16" text-anchor="middle">Dataset Size (n)</text>
                <text x="20" y="200" font-family="Arial" font-size="16" text-anchor="middle" transform="rotate(-90, 20, 200)">Computation Time</text>
                
                <!-- Standard Gradient Descent -->
                <path d="M 50,350 Q 200,300 300,200 Q 400,100 550,50" fill="none" stroke="#3498db" stroke-width="3"/>
                <text x="450" y="80" font-family="Arial" font-size="14" fill="#3498db">Standard Gradient Descent</text>
                <text x="450" y="100" font-family="Arial" font-size="14" fill="#3498db">O(nd)</text>
                
                <!-- Optimized methods (preview) -->
                <path d="M 50,350 Q 200,330 300,300 Q 400,270 550,220" fill="none" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,3"/>
                <text x="450" y="240" font-family="Arial" font-size="14" fill="#e74c3c">Optimized Methods</text>
                
                <!-- Points on the axes -->
                <circle cx="50" cy="350" r="4" fill="#2c3e50"/>
                <text x="40" y="370" font-family="Arial" font-size="12" text-anchor="end">0</text>
                
                <circle cx="175" cy="350" r="4" fill="#2c3e50"/>
                <text x="175" y="370" font-family="Arial" font-size="12" text-anchor="middle">Small</text>
                
                <circle cx="300" cy="350" r="4" fill="#2c3e50"/>
                <text x="300" y="370" font-family="Arial" font-size="12" text-anchor="middle">Medium</text>
                
                <circle cx="425" cy="350" r="4" fill="#2c3e50"/>
                <text x="425" y="370" font-family="Arial" font-size="12" text-anchor="middle">Large</text>
                
                <circle cx="550" cy="350" r="4" fill="#2c3e50"/>
                <text x="550" y="370" font-family="Arial" font-size="12" text-anchor="middle">Very Large</text>
            </svg>
        </div>
        
        <div class="step">
            <p>To address this challenge, researchers have developed more efficient optimization strategies that reduce the computational cost per iteration. The two most common approaches are:</p>
            
            <div class="container">
                <div class="card">
                    <h3>Coordinate Descent</h3>
                    <p>Updates only one parameter at a time, keeping all others fixed.</p>
                    <p><strong>Benefit:</strong> Each iteration costs $O(n)$ instead of $O(nd)$.</p>
                </div>
                
                <div class="card">
                    <h3>Stochastic Gradient Descent</h3>
                    <p>Approximates the true gradient using only a single sample or mini-batch.</p>
                    <p><strong>Benefit:</strong> Each iteration costs $O(d)$ or $O(bd)$ where $b$ is the batch size.</p>
                </div>
            </div>
        </div>
        
        <div class="step">
            <p>Additionally, we'll explore the <strong>subgradient method</strong>, which extends gradient descent to non-differentiable convex functions, making optimization possible for a broader class of problems.</p>
        </div>
        
        <div class="note">
            <p>While these advanced techniques reduce the cost per iteration, they typically require more iterations to converge. The key insight is that in many machine learning problems, it's more efficient to perform many cheap iterations than fewer expensive ones.</p>
        </div>
    </section>

    <!-- Coordinate Descent Section -->
    <section id="coordinate-descent">
        <h2>2. Coordinate Descent</h2>
        
        <div class="concept-box">
            <p>Coordinate descent is an optimization technique that updates only one parameter at a time while keeping all others fixed. This approach simplifies the optimization problem by breaking it down into a series of one-dimensional subproblems.</p>
        </div>
        
        <div class="step">
            <p>In standard gradient descent, we update all parameters simultaneously in each iteration:</p>
            
            <div class="formula-box">
                $$\mathbf{w}^{(t+1)} = \mathbf{w}^{(t)} - \alpha \nabla f(\mathbf{w}^{(t)})$$
            </div>
            
            <p>where $\mathbf{w}^{(t)}$ represents all parameters at iteration $t$, $\alpha$ is the learning rate, and $\nabla f(\mathbf{w}^{(t)})$ is the gradient of the objective function $f$ at $\mathbf{w}^{(t)}$.</p>
        </div>
        
        <div class="step">
            <p>In contrast, coordinate descent selects a single parameter $j_k$ at iteration $k$ and updates only that parameter:</p>
            
            <div class="formula-box">
                $$w_{j_k}^{k+1} = w_{j_k}^k - \alpha_k \nabla_{j_k} f(\mathbf{w}^k)$$
            </div>
            
            <p>All other parameters remain unchanged:</p>
            
            <div class="formula-box">
                $$w_j^{k+1} = w_j^k \quad \text{for } j \neq j_k$$
            </div>
            
            <p>Here, $\nabla_{j_k} f(\mathbf{w}^k)$ represents the partial derivative of $f$ with respect to the $j_k$-th parameter.</p>
        </div>
        
        <div class="visualization">
            <svg width="650" height="450" viewBox="0 0 650 450">
                <!-- Contour plot background -->
                <defs>
                    <radialGradient id="contourGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                        <stop offset="0%" style="stop-color:#ffffff; stop-opacity:1" />
                        <stop offset="20%" style="stop-color:#e1f5fe; stop-opacity:1" />
                        <stop offset="40%" style="stop-color:#b3e5fc; stop-opacity:1" />
                        <stop offset="60%" style="stop-color:#81d4fa; stop-opacity:1" />
                        <stop offset="80%" style="stop-color:#4fc3f7; stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#29b6f6; stop-opacity:1" />
                    </radialGradient>
                </defs>
                
                <!-- Contour ellipses representing a convex function -->
                <ellipse cx="325" cy="225" rx="250" ry="150" fill="none" stroke="#b3e5fc" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="200" ry="120" fill="none" stroke="#81d4fa" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="150" ry="90" fill="none" stroke="#4fc3f7" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="100" ry="60" fill="none" stroke="#29b6f6" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="50" ry="30" fill="none" stroke="#0288d1" stroke-width="1" />
                
                <!-- Coordinate system -->
                <line x1="75" y1="225" x2="575" y1="225" x2="225" stroke="black" stroke-width="1.5" />
                <line x1="325" y1="75" x2="325" y1="375" x2="375" stroke="black" stroke-width="1.5" />
                
                <!-- Axis labels -->
                <text x="585" y="225" font-family="Arial" font-size="16">w₁</text>
                <text x="325" y="65" font-family="Arial" font-size="16">w₂</text>
                
                <!-- Standard Gradient Descent Path -->
                <path d="M 525,350 L 475,325 L 425,290 L 375,260 L 325,225" fill="none" stroke="#e74c3c" stroke-width="2" />
                <circle cx="525" cy="350" r="6" fill="#e74c3c" />
                <circle cx="475" cy="325" r="6" fill="#e74c3c" />
                <circle cx="425" cy="290" r="6" fill="#e74c3c" />
                <circle cx="375" cy="260" r="6" fill="#e74c3c" />
                <circle cx="325" cy="225" r="6" fill="#e74c3c" />
                
                <!-- Coordinate Descent Path -->
                <path d="M 525,350 L 525,290 L 425,290 L 425,225 L 325,225" fill="none" stroke="#2ecc71" stroke-width="2" stroke-dasharray="5,3" />
                <circle cx="525" cy="350" r="6" fill="#2ecc71" />
                <circle cx="525" cy="290" r="6" fill="#2ecc71" />
                <circle cx="425" cy="290" r="6" fill="#2ecc71" />
                <circle cx="425" cy="225" r="6" fill="#2ecc71" />
                <circle cx="325" cy="225" r="6" fill="#2ecc71" />
                
                <!-- Horizontal and vertical lines to indicate coordinate-wise movement -->
                <line x1="525" y1="350" x2="525" y1="290" stroke="#2ecc71" stroke-width="1" stroke-dasharray="2,2" />
                <line x1="525" y1="290" x2="425" y1="290" stroke="#2ecc71" stroke-width="1" stroke-dasharray="2,2" />
                <line x1="425" y1="290" x2="425" y1="225" stroke="#2ecc71" stroke-width="1" stroke-dasharray="2,2" />
                <line x1="425" y1="225" x2="325" y1="225" stroke="#2ecc71" stroke-width="1" stroke-dasharray="2,2" />
                
                <!-- Minimum point -->
                <circle cx="325" cy="225" r="8" fill="#3498db" />
                <text x="335" y="215" font-family="Arial" font-size="14">Minimum</text>
                
                <!-- Legend -->
                <rect x="450" y="100" width="180" height="80" fill="white" stroke="#95a5a6" stroke-width="1" />
                
                <line x1="460" y1="120" x2="490" y1="120" x2="120" stroke="#e74c3c" stroke-width="2" />
                <text x="500" y="125" font-family="Arial" font-size="14">Standard Gradient Descent</text>
                
                <line x1="460" y1="150" x2="490" y1="150" x2="150" stroke="#2ecc71" stroke-width="2" stroke-dasharray="5,3" />
                <text x="500" y="155" font-family="Arial" font-size="14">Coordinate Descent</text>
                
                <!-- Starting point label -->
                <text x="535" y="360" font-family="Arial" font-size="14">Start</text>
            </svg>
        </div>
        
        <div class="step">
            <p>The selection of which coordinate to update at each iteration can follow different strategies:</p>
            <ul>
                <li><strong>Cyclic:</strong> Parameters are updated in a fixed, predetermined order (e.g., 1, 2, ..., d, 1, 2, ...)</li>
                <li><strong>Random:</strong> A random parameter is selected at each iteration</li>
                <li><strong>Greedy:</strong> The parameter that yields the largest decrease in the objective function is selected</li>
            </ul>
        </div>
        
        <div class="algorithm">
            <h3>Coordinate Descent Algorithm</h3>
            <ol>
                <li>Initialize parameter vector $\mathbf{w}^0$</li>
                <li>For $k = 0, 1, 2, \ldots$ until convergence:
                    <ol>
                        <li>Select a coordinate $j_k \in \{1, 2, \ldots, d\}$</li>
                        <li>Compute the partial derivative $\nabla_{j_k} f(\mathbf{w}^k)$</li>
                        <li>Update $w_{j_k}^{k+1} = w_{j_k}^k - \alpha_k \nabla_{j_k} f(\mathbf{w}^k)$</li>
                        <li>Keep other coordinates unchanged: $w_j^{k+1} = w_j^k$ for $j \neq j_k$</li>
                    </ol>
                </li>
                <li>Return the final parameter vector $\mathbf{w}$</li>
            </ol>
        </div>
        
        <div class="pros-cons">
            <div class="pros">
                <h3>Advantages</h3>
                <ul>
                    <li>Each iteration costs $O(n)$ instead of $O(nd)$, making it much cheaper when $d$ is large</li>
                    <li>Simple to implement and understand</li>
                    <li>Works well for problems where computing partial derivatives is much cheaper than computing the full gradient</li>
                    <li>Particularly effective for problems with sparse features</li>
                </ul>
            </div>
            
            <div class="cons">
                <h3>Limitations</h3>
                <ul>
                    <li>Theoretically slower convergence rate than standard gradient descent</li>
                    <li>May perform poorly when parameters are highly correlated</li>
                    <li>Requires more iterations to converge</li>
                    <li>Not easily parallelizable (updates are inherently sequential)</li>
                </ul>
            </div>
        </div>
        
        <div class="note">
            <p>Despite its theoretical limitations, coordinate descent can be faster than standard gradient descent in practice if each iteration is at least $d$ times cheaper. This makes it particularly valuable for high-dimensional optimization problems where $d$ is large.</p>
        </div>
    </section>

    <!-- Stochastic Gradient Descent Section -->
    <section id="sgd">
        <h2>3. Stochastic Gradient Descent</h2>
        
        <div class="concept-box">
            <p>Stochastic Gradient Descent (SGD) is an optimization technique that approximates the true gradient using only a single sample or a small batch of samples at each iteration, making it particularly efficient for large datasets.</p>
        </div>
        
        <div class="step">
            <p>In machine learning, the objective function is typically computed as an average over all training samples:</p>
            
            <div class="formula-box">
                $$f(w) = \frac{1}{n}\sum_{i=1}^{n} f_i(w)$$
            </div>
            
            <p>where $f_i(w)$ represents the loss computed on the $i$-th sample, and $n$ is the total number of samples.</p>
        </div>
        
        <div class="step">
            <p>With this formulation, standard gradient descent performs the following update:</p>
            
            <div class="formula-box">
                $$w^{(t+1)} = w^{(t)} - \frac{\alpha}{n} \sum_{i=1}^{n}\nabla f_i(w^{(t)})$$
            </div>
            
            <p>This requires computing the gradient for all $n$ samples at each iteration, which is expensive for large datasets.</p>
        </div>
        
        <div class="step">
            <p>In stochastic gradient descent, we randomly select a single sample $i_k$ at each iteration and update using only its gradient:</p>
            
            <div class="formula-box">
                $$w^{(t+1)} = w^{(t)} - \alpha \nabla f_{i_k}(w^{(t)})$$
            </div>
            
            <p>where $i_k$ is randomly selected from $\{1, 2, \ldots, n\}$.</p>
        </div>
        
        <div class="visualization">
            <svg width="650" height="450" viewBox="0 0 650 450">
                <!-- Contour plot background -->
                <defs>
                    <radialGradient id="contourGradientSGD" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                        <stop offset="0%" style="stop-color:#ffffff; stop-opacity:1" />
                        <stop offset="20%" style="stop-color:#e1f5fe; stop-opacity:1" />
                        <stop offset="40%" style="stop-color:#b3e5fc; stop-opacity:1" />
                        <stop offset="60%" style="stop-color:#81d4fa; stop-opacity:1" />
                        <stop offset="80%" style="stop-color:#4fc3f7; stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#29b6f6; stop-opacity:1" />
                    </radialGradient>
                </defs>
                
                <!-- Contour ellipses representing a convex function -->
                <ellipse cx="325" cy="225" rx="250" ry="150" fill="none" stroke="#b3e5fc" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="200" ry="120" fill="none" stroke="#81d4fa" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="150" ry="90" fill="none" stroke="#4fc3f7" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="100" ry="60" fill="none" stroke="#29b6f6" stroke-width="1" />
                <ellipse cx="325" cy="225" rx="50" ry="30" fill="none" stroke="#0288d1" stroke-width="1" />
                
                <!-- Coordinate system -->
                <line x1="75" y1="225" x2="575" y1="225" x2="225" stroke="black" stroke-width="1.5" />
                <line x1="325" y1="75" x2="325" y1="375" x2="375" stroke="black" stroke-width="1.5" />
                
                <!-- Axis labels -->
                <text x="585" y="225" font-family="Arial" font-size="16">w₁</text>
                <text x="325" y="65" font-family="Arial" font-size="16">w₂</text>
                
                <!-- Standard Gradient Descent Path -->
                <path d="M 525,350 L 475,325 L 425,290 L 375,260 L 325,225" fill="none" stroke="#e74c3c" stroke-width="2" />
                <circle cx="525" cy="350" r="6" fill="#e74c3c" />
                <circle cx="475" cy="325" r="6" fill="#e74c3c" />
                <circle cx="425" cy="290" r="6" fill="#e74c3c" />
                <circle cx="375" cy="260" r="6" fill="#e74c3c" />
                <circle cx="325" cy="225" r="6" fill="#e74c3c" />
                
                <!-- SGD Path (more zigzag) -->
                <path d="M 525,350 L 490,300 L 510,260 L 440,270 L 410,220 L 370,260 L 330,200 L 360,180 L 325,225" fill="none" stroke="#f39c12" stroke-width="2" />
                <circle cx="525" cy="350" r="6" fill="#f39c12" />
                <circle cx="490" cy="300" r="6" fill="#f39c12" />
                <circle cx="510" cy="260" r="6" fill="#f39c12" />
                <circle cx="440" cy="270" r="6" fill="#f39c12" />
                <circle cx="410" cy="220" r="6" fill="#f39c12" />
                <circle cx="370" cy="260" r="6" fill="#f39c12" />
                <circle cx="330" cy="200" r="6" fill="#f39c12" />
                <circle cx="360" cy="180" r="6" fill="#f39c12" />
                <circle cx="325" cy="225" r="6" fill="#f39c12" />
                
                <!-- Minimum point -->
                <circle cx="325" cy="225" r="8" fill="#3498db" />
                <text x="335" y="215" font-family="Arial" font-size="14">Minimum</text>
                
                <!-- Legend -->
                <rect x="450" y="100" width="180" height="80" fill="white" stroke="#95a5a6" stroke-width="1" />
                
                <line x1="460" y1="120" x2="490" y1="120" x2="120" stroke="#e74c3c" stroke-width="2" />
                <text x="500" y="125" font-family="Arial" font-size="14">Standard Gradient Descent</text>
                
                <line x1="460" y1="150" x2="490" y1="150" x2="150" stroke="#f39c12" stroke-width="2" />
                <text x="500" y="155" font-family="Arial" font-size="14">Stochastic Gradient Descent</text>
                
                <!-- Starting point label -->
                <text x="535" y="360" font-family="Arial" font-size="14">Start</text>
            </svg>
        </div>
        
        <div class="step">
            <p>A common variant is <strong>Mini-batch Stochastic Gradient Descent</strong>, which uses a small batch of $b$ samples (where $b \ll n$) at each iteration:</p>
            
            <div class="formula-box">
                $$w^{(t+1)} = w^{(t)} - \frac{\alpha}{b} \sum_{i \in B_t}\nabla f_{i}(w^{(t)})$$
            </div>
            
            <p>where $B_t$ is a randomly selected batch of $b$ samples at iteration $t$.</p>
        </div>
        
        <div class="algorithm">
            <h3>Stochastic Gradient Descent Algorithm</h3>
            <ol>
                <li>Initialize parameter vector $w^{(0)}$</li>
                <li>For $t = 0, 1, 2, \ldots$ until convergence:
                    <ol>
                        <li>Randomly select a sample index $i_k \in \{1, 2, \ldots, n\}$</li>
                        <li>Compute the gradient $\nabla f_{i_k}(w^{(t)})$ for the selected sample</li>
                        <li>Update $w^{(t+1)} = w^{(t)} - \alpha_t \nabla f_{i_k}(w^{(t)})$</li>
                    </ol>
                </li>
                <li>Return the final parameter vector $w$</li>
            </ol>
        </div>
        
        <div class="algorithm">
            <h3>Mini-batch Stochastic Gradient Descent Algorithm</h3>
            <ol>
                <li>Initialize parameter vector $w^{(0)}$</li>
                <li>For $t = 0, 1, 2, \ldots$ until convergence:
                    <ol>
                        <li>Randomly select a mini-batch $B_t$ of $b$ samples</li>
                        <li>Compute the average gradient over the mini-batch: $\frac{1}{b} \sum_{i \in B_t}\nabla f_{i}(w^{(t)})$</li>
                        <li>Update $w^{(t+1)} = w^{(t)} - \alpha_t \frac{1}{b} \sum_{i \in B_t}\nabla f_{i}(w^{(t)})$</li>
                    </ol>
                </li>
                <li>Return the final parameter vector $w$</li>
            </ol>
        </div>
        
        <div class="step">
            <p>The learning rate $\alpha_t$ in SGD is typically decreased over time to ensure convergence. Common strategies include:</p>
            <ul>
                <li><strong>Constant:</strong> $\alpha_t = \alpha$ (simple but may not converge to the exact minimum)</li>
                <li><strong>Time-based decay:</strong> $\alpha_t = \frac{\alpha_0}{1 + kt}$ where $k$ is a hyperparameter</li>
                <li><strong>Step decay:</strong> $\alpha_t = \alpha_0 \times \gamma^{\lfloor t/s \rfloor}$ where $\gamma$ is the decay rate and $s$ is the step size</li>
                <li><strong>Exponential decay:</strong> $\alpha_t = \alpha_0 e^{-kt}$ where $k$ is the decay rate</li>
            </ul>
        </div>
        
        <div class="pros-cons">
            <div class="pros">
                <h3>Advantages</h3>
                <ul>
                    <li>Each iteration costs $O(d)$ instead of $O(nd)$, making it much cheaper for large datasets</li>
                    <li>Can escape local minima due to the noise in gradient estimation</li>
                    <li>Can start making progress before seeing the entire dataset</li>
                    <li>Often converges faster in terms of wall-clock time</li>
                    <li>Better suited for online learning where data arrives sequentially</li>
                </ul>
            </div>
            
            <div class="cons">
                <h3>Limitations</h3>
                <ul>
                    <li>Higher variance in parameter updates due to the noise in gradient estimation</li>
                    <li>May not converge to the exact minimum, but rather oscillate around it</li>
                    <li>Requires careful tuning of the learning rate schedule</li>
                    <li>Theoretically requires more iterations to converge than standard gradient descent</li>
                </ul>
            </div>
        </div>
        
        <div class="note">
            <p>Stochastic gradient descent has become the method of choice for training large neural networks and other machine learning models on big datasets. The mini-batch variant offers a good compromise between the efficiency of SGD and the stability of standard gradient descent.</p>
        </div>
    </section>

    <!-- Subgradient Method Section -->
    <section id="subgradient">
        <h2>4. Subgradient Method</h2>
        
        <div class="concept-box">
            <p>The subgradient method extends gradient descent to non-differentiable convex functions, allowing optimization for a broader class of problems that standard gradient descent cannot handle.</p>
        </div>
        
        <div class="step">
            <p>In many practical optimization problems, the objective function might not be differentiable at all points. For instance, functions like absolute value, maximum, or the L1 norm have points where the gradient is not defined.</p>
        </div>
        
        <div class="definition">
            <h3>Definition: Subderivative</h3>
            <p>A subderivative of a convex function $f: I \rightarrow \mathbb{R}$ at a point $x_0$ in the open interval $I$ is a real number $c$ such that:</p>
            
            <div class="formula-box">
                $$f(x) - f(x_0) \geq c(x - x_0)$$
            </div>
            
            <p>for all $x \in I$.</p>
            
            <p>The set of all subderivatives at a point is called the <strong>subdifferential</strong> at that point.</p>
        </div>
        
        <div class="step">
            <p>For differentiable functions, the only subgradient is the gradient itself. However, at non-differentiable points, multiple subgradients may exist.</p>
            
            <p>Consider the function $f(x) = |x|$ which is convex but not differentiable at $x = 0$:</p>
            <ul>
                <li>For $x_0 < 0$, the subdifferential is the singleton set $\{-1\}$</li>
                <li>For $x_0 > 0$, the subdifferential is the singleton set $\{1\}$</li>
                <li>For $x_0 = 0$, the subdifferential is the interval $[-1, 1]$</li>
            </ul>
        </div>
        
        <div class="visualization">
            <svg width="650" height="350" viewBox="0 0 650 350">
                <!-- Coordinate system -->
                <line x1="100" y1="175" x2="550" y1="175" stroke="black" stroke-width="1.5" />
                <line x1="325" y1="50" x2="325" y1="300" stroke="black" stroke-width="1.5" />
                
                <!-- Axis labels -->
                <text x="560" y="175" font-family="Arial" font-size="16">x</text>
                <text x="325" y="40" font-family="Arial" font-size="16">f(x)</text>
                
                <!-- |x| function -->
                <path d="M 175,275 L 325,175 L 475,275" fill="none" stroke="#3498db" stroke-width="3" />
                
                <!-- Point markers -->
                <circle cx="175" cy="275" r="4" fill="#2c3e50" />
                <text x="165" y="295" font-family="Arial" font-size="12">-3</text>
                
                <circle cx="225" cy="225" r="4" fill="#2c3e50" />
                <text x="215" y="245" font-family="Arial" font-size="12">-2</text>
                
                <circle cx="275" cy="175" r="4" fill="#2c3e50" />
                <text x="265" y="195" font-family="Arial" font-size="12">-1</text>
                
                <circle cx="325" cy="175" r="6" fill="#e74c3c" />
                <text x="325" y="195" font-family="Arial" font-size="12" text-anchor="middle">0</text>
                
                <circle cx="375" cy="175" r="4" fill="#2c3e50" />
                <text x="375" y="195" font-family="Arial" font-size="12" text-anchor="middle">1</text>
                
                <circle cx="425" cy="225" r="4" fill="#2c3e50" />
                <text x="425" y="245" font-family="Arial" font-size="12" text-anchor="middle">2</text>
                
                <circle cx="475" cy="275" r="4" fill="#2c3e50" />
                <text x="475" y="295" font-family="Arial" font-size="12" text-anchor="middle">3</text>
                
                <!-- Subdifferentials -->
                <!-- For x < 0 -->
                <line x1="225" y1="225" x2="265" y1="225" stroke="#2ecc71" stroke-width="2" />
                <text x="200" y="225" font-family="Arial" font-size="12" fill="#2ecc71">-1</text>
                
                <!-- For x > 0 -->
                <line x1="425" y1="225" x2="385" y1="225" stroke="#2ecc71" stroke-width="2" />
                <text x="450" y="225" font-family="Arial" font-size="12" fill="#2ecc71">+1</text>
                
                <!-- For x = 0 -->
                <path d="M 325,175 L 345,155 M 325,175 L 335,165 M 325,175 L 315,165 M 325,175 L 305,155" stroke="#f39c12" stroke-width="1.5" stroke-dasharray="3,2" />
                <path d="M 305,155 L 345,155" stroke="#f39c12" stroke-width="1.5" />
                <text x="325" y="145" font-family="Arial" font-size="12" fill="#f39c12" text-anchor="middle">[-1, 1]</text>
                
                <!-- Function label -->
                <text x="400" y="100" font-family="Arial" font-size="16">f(x) = |x|</text>
                
                <!-- Legend -->
                <rect x="450" y="70" width="180" height="60" fill="white" stroke="#95a5a6" stroke-width="1" />
                
                <line x1="460" y1="85" x2="480" y1="85" stroke="#3498db" stroke-width="3" />
                <text x="490" y="90" font-family="Arial" font-size="14">f(x) = |x|</text>
                
                <line x1="460" y1="115" x2="480" y1="115" stroke="#f39c12" stroke-width="1.5" stroke-dasharray="3,2" />
                <text x="490" y="120" font-family="Arial" font-size="14">Subdifferential</text>
            </svg>
        </div>
        
        <div class="algorithm">
            <h3>Subgradient Descent Algorithm</h3>
            <p>Let $f: \mathbb{R}^n \rightarrow \mathbb{R}$ be a convex function. The subgradient method iterates as follows:</p>
            
            <div class="formula-box">
                $$w^{(t+1)} = w^{(t)} - \alpha_t g^{(t)}$$
            </div>
            
            <p>where $g^{(t)}$ denotes any subgradient of $f$ at $w^{(t)}$. If $f$ is differentiable at $w^{(t)}$, then its only subgradient is the gradient vector $\nabla f(w^{(t)})$ itself.</p>
            
            <ol>
                <li>Initialize parameter vector $w^{(0)}$</li>
                <li>For $t = 0, 1, 2, \ldots$ until convergence:
                    <ol>
                        <li>Compute a subgradient $g^{(t)}$ of $f$ at $w^{(t)}$</li>
                        <li>Update $w^{(t+1)} = w^{(t)} - \alpha_t g^{(t)}$</li>
                    </ol>
                </li>
                <li>Return the final parameter vector $w$</li>
            </ol>
        </div>
        
        <div class="step">
            <p>Similar to standard gradient descent, the subgradient method can also be implemented in a stochastic manner for functions that are sums over many terms:</p>
            
            <div class="formula-box">
                $$w^{(t+1)} = w^{(t)} - \alpha_t g_{i_k}^{(t)}$$
            </div>
            
            <p>where $g_{i_k}^{(t)}$ denotes any subgradient of $f_{i_k}$ at $w^{(t)}$, and $i_k$ is randomly selected from $\{1, 2, \ldots, n\}$.</p>
        </div>
        
        <div class="note">
            <h3>Important Differences from Standard Gradient Descent</h3>
            <p>The subgradient method has some key differences compared to standard gradient descent:</p>
            <ul>
                <li>The objective function value may not decrease in every iteration</li>
                <li>A diminishing step size (learning rate) schedule is typically required for convergence</li>
                <li>The convergence rate is typically slower than that of gradient descent for differentiable functions</li>
                <li>Simple stopping criteria based on gradient magnitude don't work well, as subgradients may not approach zero</li>
            </ul>
        </div>
        
        <div class="pros-cons">
            <div class="pros">
                <h3>Advantages</h3>
                <ul>
                    <li>Can optimize non-differentiable convex functions</li>
                    <li>Extends the applicability of gradient-based methods to a broader class of problems</li>
                    <li>Simple to implement, similar to standard gradient descent</li>
                    <li>Useful for problems with L1 regularization, max functions, or other non-smooth objectives</li>
                </ul>
            </div>
            
            <div class="cons">
                <h3>Limitations</h3>
                <ul>
                    <li>Slower convergence compared to gradient descent for differentiable functions</li>
                    <li>Requires careful step size selection and may be more sensitive to learning rate</li>
                    <li>May not monotonically decrease the objective function</li>
                    <li>More difficult to detect convergence</li>
                </ul>
            </div>
        </div>
        
        <div class="step">
            <p>A common application of the subgradient method is in L1-regularized optimization problems such as LASSO regression:</p>
            
            <div class="formula-box">
                $$\min_{w} \frac{1}{2} \|Xw - y\|_2^2 + \lambda \|w\|_1$$
            </div>
            
            <p>The L1 norm $\|w\|_1 = \sum_{i=1}^{d} |w_i|$ is not differentiable at points where any component $w_i = 0$, making the subgradient method appropriate for this type of problem.</p>
        </div>
        
        <div class="note">
            <h3>Practical Considerations</h3>
            <p>In real-world applications, a combination of methods or more advanced variations are often used:</p>
            <ul>
                <li><strong>Adaptive Methods:</strong> AdaGrad, RMSProp, and Adam adjust learning rates for each parameter.</li>
                <li><strong>Hybrid Approaches:</strong> Begin with SGD for fast initial progress, then switch to standard gradient descent for fine-tuning.</li>
                <li><strong>Accelerated Methods:</strong> Nesterov's accelerated gradient or momentum methods can speed up convergence.</li>
                <li><strong>Second-Order Methods:</strong> For smaller problems, Newton or quasi-Newton methods (L-BFGS) can achieve faster convergence.</li>
            </ul>
        </div>
    </section>

    <!-- Comparison Section -->
    <section id="comparison">
        <h2>5. Comparison of Methods</h2>
        
        <div class="concept-box">
            <p>Let's compare the three optimization methods discussed in this tutorial: standard gradient descent, coordinate descent, and stochastic gradient descent, along with the subgradient method for non-differentiable functions.</p>
        </div>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Method</th>
                    <th>Cost per Iteration</th>
                    <th>Convergence Rate</th>
                    <th>When to Use</th>
                    <th>Key Considerations</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Standard Gradient Descent</td>
                    <td>$O(nd)$</td>
                    <td>Faster theoretical convergence</td>
                    <td>
                        <ul>
                            <li>Small to medium datasets</li>
                            <li>When high precision is required</li>
                            <li>Well-conditioned problems</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Expensive for large datasets</li>
                            <li>Parallelizable</li>
                            <li>Simple to implement</li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td>Coordinate Descent</td>
                    <td>$O(n)$</td>
                    <td>Slower theoretical convergence</td>
                    <td>
                        <ul>
                            <li>High-dimensional problems (large $d$)</li>
                            <li>When features are sparse</li>
                            <li>Problems with expensive full gradient computation</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Not easily parallelizable</li>
                            <li>Simple to implement</li>
                            <li>Good for sparse problems</li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td>Stochastic Gradient Descent</td>
                    <td>$O(d)$ or $O(bd)$</td>
                    <td>Slower and noisier convergence</td>
                    <td>
                        <ul>
                            <li>Very large datasets (large $n$)</li>
                            <li>Online learning scenarios</li>
                            <li>Deep learning and neural networks</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Requires learning rate scheduling</li>
                            <li>Higher variance in updates</li>
                            <li>May require more iterations</li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td>Subgradient Method</td>
                    <td>Same as respective base method</td>
                    <td>Slower than standard methods on differentiable functions</td>
                    <td>
                        <ul>
                            <li>Non-differentiable convex functions</li>
                            <li>L1 regularization problems</li>
                            <li>Constraints represented by max functions</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Requires diminishing step sizes</li>
                            <li>Progress may not be monotonic</li>
                            <li>Harder to determine convergence</li>
                        </ul>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="visualization">
            <svg width="700" height="450" viewBox="0 0 700 450">
                <!-- Axes -->
                <line x1="50" y1="400" x2="650" y1="400" stroke="black" stroke-width="1.5" />
                <line x1="50" y1="50" x2="50" y1="400" stroke="black" stroke-width="1.5" />
                
                <!-- Axis labels -->
                <text x="350" y="440" font-family="Arial" font-size="16" text-anchor="middle">Number of Iterations</text>
                <text x="20" y="225" font-family="Arial" font-size="16" text-anchor="middle" transform="rotate(-90, 20, 225)">Objective Value</text>
                
                <!-- Convergence paths -->
                <!-- Standard GD -->
                <path d="M 50,350 Q 150,300 250,200 Q 350,150 450,100 Q 550,75 650,65" fill="none" stroke="#e74c3c" stroke-width="3" />
                
                <!-- Coordinate Descent -->
                <path d="M 50,350 Q 150,325 250,275 Q 350,225 450,175 Q 550,125 650,100" fill="none" stroke="#2ecc71" stroke-width="3" stroke-dasharray="5,3" />
                
                <!-- SGD -->
                <path d="M 50,350 Q 100,325 150,300 Q 200,275 250,260 Q 300,245 350,230 Q 400,215 450,195 Q 500,175 550,145 Q 600,115 650,85" fill="none" stroke="#f39c12" stroke-width="3" stroke-dasharray="8,4" />
                <path d="M 100,325 Q 110,340 120,315" fill="none" stroke="#f39c12" stroke-width="1.5" />
                <path d="M 200,275 Q 210,290 220,265" fill="none" stroke="#f39c12" stroke-width="1.5" />
                <path d="M 300,245 Q 310,260 320,235" fill="none" stroke="#f39c12" stroke-width="1.5" />
                <path d="M 400,215 Q 410,230 420,205" fill="none" stroke="#f39c12" stroke-width="1.5" />
                <path d="M 500,175 Q 510,190 520,165" fill="none" stroke="#f39c12" stroke-width="1.5" />
                <path d="M 600,115 Q 610,130 620,105" fill="none" stroke="#f39c12" stroke-width="1.5" />
                
                <!-- Subgradient Method -->
                <path d="M 50,350 Q 150,335 250,290 Q 350,250 450,205 Q 550,165 650,140" fill="none" stroke="#3498db" stroke-width="2" stroke-dasharray="1,1" />
                
                <!-- Optimal value -->
                <line x1="50" y1="65" x2="650" y1="65" stroke="#95a5a6" stroke-width="1" stroke-dasharray="3,3" />
                <text x="100" y="60" font-family="Arial" font-size="12" fill="#95a5a6">Optimal Value</text>
                
                <!-- Legend -->
                <rect x="450" y="180" width="230" height="120" fill="white" stroke="#95a5a6" stroke-width="1" />
                
                <line x1="460" y1="200" x2="490" y1="200" stroke="#e74c3c" stroke-width="3" />
                <text x="500" y="205" font-family="Arial" font-size="14">Standard Gradient Descent</text>
                
                <line x1="460" y1="230" x2="490" y1="230" stroke="#2ecc71" stroke-width="3" stroke-dasharray="5,3" />
                <text x="500" y="235" font-family="Arial" font-size="14">Coordinate Descent</text>
                
                <line x1="460" y1="260" x2="490" y1="260" stroke="#f39c12" stroke-width="3" stroke-dasharray="8,4" />
                <text x="500" y="265" font-family="Arial" font-size="14">Stochastic Gradient Descent</text>
                
                <line x1="460" y1="290" x2="490" y1="290" stroke="#3498db" stroke-width="2" stroke-dasharray="1,1" />
                <text x="500" y="295" font-family="Arial" font-size="14">Subgradient Method</text>
            </svg>
        </div>
        
        <div class="step">
            <h3>Making the Right Choice</h3>
            <p>The choice of optimization method depends on several factors:</p>
            
            <ul>
                <li><strong>Dataset Size:</strong> For large datasets, SGD is often the most practical choice.</li>
                <li><strong>Problem Dimensionality:</strong> For high-dimensional problems, coordinate descent can be effective.</li>
                <li><strong>Function Differentiability:</strong> For non-differentiable functions, the subgradient method is necessary.</li>
                <li><strong>Required Precision:</strong> If high precision is needed, standard gradient descent may be worth the computational cost.</li>
                <li><strong>Hardware Constraints:</strong> For parallelization, standard gradient descent and mini-batch SGD are more suitable.</li>
            </ul>
        </div>
    </section>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
        <p>Advanced Gradient Descent Techniques Tutorial</p>
        <p>Based on the article "More Gradient Descent"</p>
    </footer>
</body>
</html> 