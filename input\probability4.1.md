4.1.0 Continuous Random Variables and their
 Distributions
 We have in fact already seen examples of continuous random variables before, e.g.,
 Example 1.14. Let us look at the same example with just a little bit different wording.
 Example 4.1
 
I choose a real number uniformly at random in the interval 
[a,b]
 , and call it X
 . By
 uniformly at random, we mean all intervals in 
[a,b]
 that have the same length must
 have the same probability. Find the CDF of X
 .
 Solution
 As we mentioned, this is almost exactly the same problem as Example 1.14, with the
 difference being, in that problem, we considered the interval from 
1
 to 
2
 . In that
 example, we saw that all individual points have probability 
0
 , i.e., P(X = x) = 0
 for all x
 . Also, the uniformity implies that the probability of an interval of length l
 in 
[a,b]
 must
 be proportional to its length:
 P(X ∈ [x1,x2]) ∝ (x2 −x1),
 Since P(X ∈ [a,b]) = 1
 , we conclude
 x2 −x1
 b −a
 where a ≤x1 ≤x2 ≤b.
 P(X ∈ [x1,x2]) = , where a≤x1 ≤x2 ≤b.
 Now, let us find the CDF. By definition FX(x) = P(X ≤ x)
 , thus we immediately have
 FX(x) = 0,
 FX(x) = 1,
 For a ≤ x ≤b
 , we have
 for x < a,
 for x ≥ b.
 FX(x) = P(X ≤x)
 =P(X∈[a,x])
 = .
 x−a
 b−a
Thus, to summarize 
⎧
 ⎪ 
FX(x) =
 ⎪
 ⎨
 ⎪ 
⎩
 ⎪
 0
 x−a
 b−a
 1
 for x < a
 for a ≤ x ≤b
 for x > b
 (4.1)
 Note that here it does not matter if we use "
 <
 " or "
 ≤
 ", as each individual point has
 probability zero, so for example P(X < 2) = P(X ≤ 2)
 . Figure 4.1 shows the CDF of X
 . As we expect the CDF starts at zero and ends at 
1
 . 
 
Fig.4.1 - CDF for a continuous random variable uniformly distributed over 
[a, b]
 .
 
One big difference that we notice here as opposed to discrete random variables is that
 the CDF is a continuous function, i.e., it does not have any jumps. Remember that
 jumps in the CDF correspond to points x
 for which P(X = x) > 0
 . Thus, the fact that
 the CDF does not have jumps is consistent with the fact that P(X = x) = 0
 for all x
 .
 Indeed, we have the following definition for continuous random variables.
Definition 4.1
 
A random variable X
 with CDF FX(x)
 is said to be continuous if FX(x)
 is a
 continuous function for all x ∈ R
 .
 We will also assume that the CDF of a continuous random variable is differentiable
 almost everywhere in R
 .
4.1.1 Probability Density Function (PDF)
 To determine the distribution of a discrete random variable we can either provide its
 PMF or CDF. For continuous random variables, the CDF is well-defined so we can
 provide the CDF. However, the PMF does not work for continuous random variables,
 because for a continuous random variable 
P(X = x) = 0
 for all 
x ∈ R
 . Instead, we can
 usually define the probability density function (PDF). The PDF is the density of
 probability rather than the probability mass. The concept is very similar to mass
 density in physics: its unit is probability per unit length. To get a feeling for PDF,
 consider a continuous random variable 
X
 and define the function 
fX(x)
 as follows
 (wherever the limit exists):
 fX(x) = lim
 Δ→0+
 P(x <X≤x+Δ)
 Δ
 .
 The function 
fX(x)
 gives us the probability density at point 
x
 . It is the limit of the
 probability of the interval 
(x,x + Δ]
 divided by the length of the interval as the length of
 the interval goes to 
0
 . Remember that
 P(x <X≤x+Δ)=FX(x+Δ)−FX(x).
 So, we conclude that
 dFX(x)
 dx
 fX(x) = lim
 Δ→0
 FX(x+Δ)−FX(x)
 Δ
 = =F′
 X(x), if FX(x) is differentiable at x.
 Thus, we have the following definition for the PDF of continuous random variables:
Definition 4.2
 
Consider a continuous random variable 
X
 with an absolutely
 continuous CDF 
FX(x)
 . The function 
fX(x)
 defined by
 dFX(x)
 dx
 fX(x) = =F′
 X(x), if FX(x) is differentiable at x
 is called the probability density function (PDF) of 
X
 .
 Let us find the PDF of the uniform random variable 
X
 discussed in 
Example 4.1. This
 random variable is said to have 
Uniform(a,b)
 distribution. The CDF of 
X
 is given in
 Equation 4.1. By taking the derivative, we obtain
 fX(x) =
 {
 1
 b−a
 0
 a <x<b
 x <a or x >b
 Note that the CDF is not differentiable at points 
a
 and 
b
 . Nevertheless, as we will
 discuss later on, this is not important. Figure 4.2 shows the PDF of 
X
 . As we see, the
 value of the PDF is constant in the interval from 
a
 to 
b
 . That is why we say 
X
 is
 uniformly distributed over 
[a,b]
 .
 Fig.4.2 - PDF for a continuous random variable uniformly distributed over 
[a, b]
 .
The uniform distribution is the simplest continuous random variable you can imagine.
 For other types of continuous random variables the PDF is non-uniform. Note that for
 small values of 
δ
 we can write
 P(x <X≤x+δ)≈fX(x)δ.
 Thus, if 
fX(x1) > fX(x2)
 , we can say 
P(x1 < X ≤ x1 +δ) > P(x2 < X ≤ x2 +δ)
 , i.e.,
 the value of 
X
 is more likely to be around 
x1
 than 
x2
 . This is another way of
 interpreting the PDF.
 Since the PDF is the derivative of the CDF, the CDF can be obtained from PDF by
 integration (assuming absolute continuity):
 x
 FX(x) =
 Also, we have
 ∫
 −∞
 fX(u)du.
 P(a <X≤b)=FX(b)−FX(a)=
 ∫
 b
 a
 fX(u)du.
 In particular, if we integrate over the entire real line, we must get 
1
 , i.e.,
 ∞
 ∫
 −∞
 fX(u)du = 1.
 That is, the area under the PDF curve must be equal to one. We can see that this
 holds for the uniform distribution since the area under the curve in Figure 4.2 is one.
 Note that 
fX(x)
 is density of probability, so it must be larger than or equal to zero, but it
 can be larger than 
1
 . Let us summarize the properties of the PDF.
 Consider a continuous random variable 
X
 with PDF 
fX(x)
 . We have
 1. 
fX(x) ≥ 0
 for all 
x ∈ R
 .
 2. 
∫ ∞
 −∞fX(u)du = 1
 .
 3. 
P(a < X ≤b) =FX(b)−FX(a) =∫b
 a fX(u)du
 .
 4. More generally, for a set 
A
 , 
P(X ∈ A) = ∫AfX(u)du
 .
 In the last item above, the set 
A
 must satisfy some mild conditions which are almost
 always satisfied in practice. An example of set 
A
 could be a union of some disjoint
intervals. For example, if you want to find 
P(X ∈ [0,1] ∪ [3,4])
 , you can write
 1
 P(X ∈ [0,1]∪ [3,4]) =
 ∫
 0
 Let us look at an example to practice the above concepts.
 4
 fX(u)du +
 ∫
 3
 fX(u)du.
 Example 4.2
 
Let 
X
 be a continuous random variable with the following PDF
 fX(x) =
 where 
c
 is a positive constant.
 a. Find 
c
 .
 b. Find the CDF of X, 
FX(x)
 .
 c. Find 
P(1 < X < 3)
 .
 Solution
 {ce−x
 0
 x ≥0
 otherwise
 a. To find 
c
 , we can use Property 2 above, in particular
 1 =∫∞
 −∞fX(u)du
 =∫∞
 0 ce−udu
 ∞
 =c
 [ −e−x
 ]
 0
 =c
 .
 Thus, we must have 
c = 1
 .
 b. To find the CDF of X, we use 
FX(x) = ∫ x
 −∞ fX(u)du
 , so for 
x < 0
 , we obtain 
FX(x) = 0
 . For 
x ≥ 0
 , we have
 FX(x) =
 Thus,
 FX(x) =
 ∫ 
x
 0
 e−u
 du = 1−e−x
 .
 {1−e−x
 0
 x ≥0
 otherwise
c. We can find 
P(1 < X < 3)
 using either the CDF or the PDF. If we use the CDF,
 we have
 P(1 <X<3)=FX(3)−FX(1)=[1−e−3
 ]−[1−e−1
 ] =e−1 −e−3.
 Equivalently, we can use the PDF. We have
 P(1 <X<3)=∫3
 1 fX(t)dt
 =∫3
 1 e−tdt
 =e−1 −e−3.
 Range
 
The range of a random variable 
X
 is the set of possible values of the random variable.
 If 
X
 is a continuous random variable, we can define the range of 
X
 as the set of real
 numbers 
x
 for which the PDF is larger than zero, i.e,
 RX ={x|fX(x) >0}.
 The set 
RX
 defined here might not exactly show all possible values of 
X
 , but the
 difference is practically unimportant.
4.1.2 Expected Value and Variance
 As we mentioned earlier, the theory of continuous random variables is very similar to
 the theory of discrete random variables. In particular, usually summations are replaced
 by integrals and PMFs are replaced by PDFs. The proofs and ideas are very
 analogous to the discrete case, so sometimes we state the results without
 mathematical derivations for the purpose of brevity.
 Remember that the expected value of a discrete random variable can be obtained as
 EX=
 ∑
 xk∈RX
 xkPX(xk).
 Now, by replacing the sum by an integral and PMF by PDF, we can write the definition
 of expected value of a continuous random variable as
 ∞
 EX=
 ∫
 xfX(x)dx
 −∞
 Example 4.3
 
Let 
X ∼ Uniform(a,b)
 . Find 
EX
 .
 Solution
 As we saw, the PDF of 
X
 is given by
 fX(x) =
 1
 b−a
 0
 { a<x<b
 so to find its expected value, we can write
 x <a or x >b
 EX=∫∞
 −∞xfX(x)dx
 =∫b
 a x( )dx
 1
 b−a
2
 b
 dx
 1
 =
 1
 b−a
 a+b
 2
 [ x2
 ]
 = .
 a
 This result is intuitively reasonable: since 
X
 is uniformly distributed over the interval
 [a, b]
 , we expect its mean to be the middle point, i.e., 
EX =
 .
 a+b
 2
 Example 4.4
 
Let 
X
 be a continuous random variable with PDF
 fX(x) =
 Find the expected value of 
X
 .
 Solution
 We have
 {2x
 0
 0 ≤x≤1
 otherwise
 EX=∫∞
 −∞xfX(x)dx
 =∫1
 0 x(2x)dx
 2
 =∫1
 0 2x2dx
 = .
 3
 Expected Value of a Function of a Continuous Random Variable
 
Remember the law of the unconscious statistician (LOTUS) for discrete random
 variables:
 E[g(X)] =
 ∑
 xk∈RX
 g(xk)PX(xk)
 (4.2)
 Now, by changing the sum to integral and changing the PMF to PDF we will obtain the
 similar formula for continuous random variables.
Law of the unconscious statistician (LOTUS) for continuous random variables:
 ∞
 E[g(X)] =
 ∫
 −∞
 g(x)fX(x)dx
 (4.3)
 As we have seen before, expectation is a linear operation, thus we always have
 E[aX+b] =aEX+b
 , for all 
a,b ∈ R
 , and
 E[X1 +X2+...+Xn] =EX1 +EX2+...+EXn
 , for any set of random variables 
X1,X2,...,Xn
 .
 Example 4.5
 
Let 
X
 be a continuous random variable with PDF
 1
 fX(x) =
 Find 
E(Xn)
 , where 
n ∈ N
 .
 Solution
 Using LOTUS we have
 E[Xn] = ∫ ∞
 2
 0
 {x+ 0≤x≤1
 otherwise
 −∞xnfX(x)dx
 =∫1
 0 xn(x+ )dx
 1
 2
 =
 1
 n+2
 1
 [ xn+2+ xn+1
 ]
 1
 2(n+1)
 3n+4
 2(n+1)(n+2)
 = .
 0
 Variance
 
Remember that the variance of any random variable is defined as
 Var(X) = E[(X−μX)2
 ] =EX2 −(EX)2.
 So for a continuous random variable, we can write
∞
 Var(X) = E[(X−μX)2
 ] =∫
 −∞
 (x −μX)2fX(x)dx
 ∞
 =EX2−(EX)2 =∫
 −∞
 Also remember that for 
a,b ∈ R
 , we always have
 Var(aX +b) =a2Var(X).
 Example 4.6
 
Let 
X
 be a continuous random variable with PDF
 fX(x) =
 3
 x4
 2
 x
 { x≥1
 0
 fX(x)dx −μ2
 X
 (4.4)
 otherwise
 Find the mean and variance of 
X
 .
 Solution
 E[X] =∫∞
 −∞xfX(x)dx
 Next, we find 
EX2
 using LOTUS,
 E[X2] = ∫ ∞
 =∫∞
 1 dx
 3
 x3
 3
 =
 2
 ∞
 [ − x−2
 ]
 1
 3
 = .
 2
 −∞x2fX(x)dx
 =∫∞
 1 dx
 3
 x2
 =
 ∞
 [ −3x−1
 ]
 1
 =3.
 Thus, we have
9
 Var(X) = EX2 −(EX)2 =3− = .
 4
 3
 4
4.1.3 Functions of Continuous Random Variables
 If 
X
 is a continuous random variable and 
Y = g(X)
 is a function of 
X
 , then 
Y
 itself is a
 random variable. Thus, we should be able to find the CDF and PDF of 
Y
 . It is usually
 more straightforward to start from the CDF and then to find the PDF by taking the
 derivative of the CDF. Note that before differentiating the CDF, we should check that
 the CDF is continuous. As we will see later, the function of a continuous random
 variable might be a non-continuous random variable. Let's look at an example.
 Example 4.7
 
Let 
X
 be a 
Uniform(0,1)
 random variable, and let 
Y = eX
 .
 a. Find the CDF of 
Y
 .
 b. Find the PDF of 
Y
 .
 c. Find 
EY
 .
 Solution
 First, note that we already know the CDF and PDF of 
X
 . In particular,
 FX(x) =
 0
 ⎧
 ⎪
 ⎨
 ⎩
 ⎪
 x
 1
 for x < 0
 for 0 ≤ x ≤1
 for x > 1
 It is a good idea to think about the range of 
Y
 before finding the distribution. Since 
ex
 is
 an increasing function of 
x
 and 
RX = [0,1]
 , we conclude that 
RY = [1,e]
 . So we
 immediately know that
 FY(y) = P(Y ≤y) =0,
 FY(y) = P(Y ≤y) =1,
 a. To find 
FY (y)
 for 
y ∈ [1,e]
 , we can write
 
FY(y) = P(Y ≤y)
 =P(eX ≤y)
 =P(X≤lny)
 for y < 1,
 for y ≥ e.
To summarize
 =FX(lny) = lny
 0
 FY(y) =
 ⎧
 ⎪
 ⎨
 ⎩
 ⎪
 ln y
 1
 for y < 1
 for 1 ≤ y <e
 for y ≥ e
 b. The above CDF is a continuous function, so we can obtain the PDF of 
Y
 by
 taking its derivative. We have
 1
 fY (y) = F′
 Y(y) =
 y
 0
 { for 1≤y≤e
 otherwise
 Note that the CDF is not technically differentiable at points 
1
 and 
e
 , but as we
 mentioned earlier we do not worry about this since this is a continuous random
 variable and changing the PDF at a finite number of points does not change
 probabilities.
 c. To find the 
EY
 , we can directly apply LOTUS,
 E[Y] = E[eX] = ∫ ∞
 −∞exfX(x)dx
 =∫1
 0 exdx
 =e−1.
 For this problem, we could also find 
EY
 using the PDF of 
Y
 ,
 E[Y] = ∫ ∞
 −∞yfY(y)dy
 1
 =∫e
 1 y dy
 =e−1.
 y
 Note that since we have already found the PDF of 
Y
 it did not matter which
 method we used to find 
E[Y]
 . However, if the problem only asked for 
E[Y]
 without asking for the PDF of 
Y
 , then using LOTUS would be much easier.
 Example 4.8
 
Let 
X ∼ Uniform(−1,1)
 and 
Y = X2
 . Find the CDF and PDF of 
Y
 .
 Solution
First, we note that 
RY = [0,1]
 . As usual, we start with the CDF. For 
y ∈ [0,1]
 , we have
 FY(y) = P(Y ≤y)
 =P(X2 ≤y)
 =P(−√
 y ≤X≤√
 y)
 √
 y−(−√
 =
 y)
 1−(−1)
 =√
 y.
 Thus, the CDF of 
Y
 is given by
 FY(y) =
 ⎧
 ⎪
 ⎨
 ⎩
 ⎪
 since X ∼ Uniform(−1,1)
 y
 0 for y<0
 √
 1
 for 0 ≤ y ≤1
 for y > 1
 Note that the CDF is a continuous function of 
Y
 , so 
Y
 is a continuous random variable.
 Thus, we can find the PDF of 
Y
 by differentiating 
FY (y)
 ,
 1
 fY (y) = F′
 Y(y) =
 2√
 y
 { for 0≤y≤1
 0
 otherwise
 The Method of Transformations
 
So far, we have discussed how we can find the distribution of a function of a
 continuous random variable starting from finding the CDF. If we are interested in
 finding the PDF of 
Y = g(X)
 , and the function 
g
 satisfies some properties, it might be
 easier to use a method called the method of transformations. Let's start with the case
 where 
g
 is a function satisfying the following properties:
 g(x)
 is differentiable;
 g(x)
 is a strictly increasing function, that is, if 
x1 < x2
 , then 
g(x1) < g(x2)
 .
 Now, let 
X
 be a continuous random variable and 
Y = g(X)
 . We will show that you can
 directly find the PDF of 
Y
 using the following formula.
 fY (y) =
 ⎧
 ⎨
 fX(x1)
 g
 ′(x1)
 0
 dx1
 dy
 ⎩ =fX(x1). where g(x1)=y
 if g(x) = y does not have a solution
 Note that since 
g
 is strictly increasing, its inverse function 
g−1
 is well defined. That is,
 for each 
y ∈ RY
 , there exists a unique 
x1
 such that 
g(x1) = y
 . We can write 
x1 =g−1(y)
 .
 FY(y) = P(Y ≤y)
=P(g(X) ≤y)
 =FX(g−1(y)).
 To find the PDF of 
Y
 , we differentiate
 fY (y) = FX(x1)
 d
 dy
 dx1
 dy
 = ⋅F′
 X(x1)
 =fX(x1)
 fX(x1)
 g
 dx1
 dy
 where g(x1) = y
 1
 = since = .
 ′(x1)
 dx
 dy
 dy
 dx
 We can repeat the same argument for the case where 
g
 is strictly decreasing. In that
 case, 
g′(x1)
 will be negative, so we need to use 
|g′(x1)|
 . Thus, we can state the
 following theorem for a strictly monotonic function. (A function 
g : R → R
 is called
 strictly monotonic if it is strictly increasing or strictly decreasing.)
 Theorem 4.1
 
Suppose that 
X
 is a continuous random variable and 
g : R → R
 is a strictly
 monotonic differentiable function. Let 
Y = g(X)
 . Then the PDF of 
Y
 is given by
 fY (y) =
 ⎧
 ⎨
 fX(x1)
 |g′(x1)|
 0
 dx1
 dy
 ⎩ =fX(x1).| | where g(x1)=y
 if g(x) = y does not have a solution
 To see how to use the formula, let's look at an example.
 Example 4.9
 
Let 
X
 be a continuous random variable with PDF
 fX(x) =
 1
 and let 
Y = 
. Find 
fY(y)
 .
 X
 Solution
 {4x3
 0
 0 <x≤1
 otherwise
 (4.5)
1
 First note that 
RY = [1,∞)
 . Also, note that 
g(x)
 is a strictly decreasing and
 differentiable function on 
(0,1]
 , so we may use Equation 4.5. We have 
g′(x) = − 
.
 For any 
y ∈ [1,∞)
 , 
x1 = g−1(y) = 
. So, for 
y ∈ [1,∞)
 fY (y) =
 1
 x2
 y
 Thus, we conclude
 fY (y) =
 =
 fX(x1)
 |g′(x1)|
 4x3
 1
 |− | 1
 x
 2
 1
 =4x5
 1
 = .
 4
 y5
 4
 y5
 { y≥1
 0
 otherwise
 Theorem 4.1 can be extended to a more general case. In particular, if 
g
 is not
 monotonic, we can usually divide it into a finite number of monotonic differentiable
 functions. Figure 4.3 shows a function 
g
 that has been divided into monotonic parts.
 We may state a more general form of Theorem 4.1.
 Fig.4.3 - Partitioning a function to monotone parts.
Theorem 4.2
 
Consider a continuous random variable 
X
 with domain 
RX
 , and let 
Y = g(X)
 .
 Suppose that we can partition 
RX
 into a finite number of intervals such that 
g(x)
 is
 strictly monotone and differentiable on each partition. Then the PDF of 
Y
 is given
 by
 n
 fY (y) =
 ∑
 i=1
 n
 fX(xi)
 |g′(xi)|
 =
 ∑
 i=1
 where 
x1,x2,...,xn
 are real solutions to 
g(x) = y
 .
 fX(xi). ∣ ∣ ∣
 ∣ ∣ ∣
 dxi
 dy
 Let us look at an example to see how we can use Theorem 4.2.
 Example 4.10
 
Let 
X
 be a continuous random variable with PDF
 x2
 2
 fX(x) = e− 
, for all x∈R
 1
 √
 2π
 and let 
Y = X2
 . Find 
fY (y)
 .
 Solution
 (4.6)
 We note that the function 
g(x) = x2
 is strictly decreasing on the interval 
(−∞,0)
 ,
 strictly increasing on the interval 
(0,∞)
 , and differentiable on both intervals, 
g′(x) = 2x
 .
 Thus, we can use Equation 4.6. First, note that 
RY = (0,∞)
 . Next, for any 
y ∈ (0,∞)
 we have two solutions for 
y = g(x)
 , in particular,
 x1 =√
 y, x2 =−√
 y
 Note that although 
0 ∈ RX
 it has not been included in our partition of 
RX
 . This is not a
 problem, since 
P(X = 0) = 0
 . Indeed, in the statement of Theorem 4.2, we could
 replace 
RX
 by 
RX −A
 , where 
A
 is any set for which 
P(X ∈ A) = 0
 . In particular, this is
 convenient when we exclude the endpoints of the intervals. Thus, we have
 fY (y) = +
 fX(x1)
 |g′(x1)|
 fX(√
 y)
 fX(x2)
 |g′(x2)|
 fX(−√
 y)
 = +
 |2√
 y|
 |−2√
 y|
y
 y
 1
 2√
 2
 = e− 
+ e−
 2πy
 1
 1
 2√
 2πy
 y
 2
 2
 = e− 
, for y∈(0,∞).
 √
 2πy
4.1.4 Solved Problems: 
 
Continuous Random Variables
 Problem 1
 
Let 
X
 be a random variable with PDF given by
 fX(x) =
 a. Find the constant 
c
 .
 b. Find 
EX
 and Var
 (X)
 .
 c. Find 
P(X ≥ )
 .
 1
 2
 Solution
 a. To find 
c
 , we can use 
∫ ∞
 {cx2
 0
 |x| ≤ 1
 otherwise
 −∞fX(u)du = 1
 :
 1 =∫∞
 −∞fX(u)du
 =∫1
 −1 cu2du
 = c.
 2
 3
 Thus, we must have 
c = 
.
 b. To find 
EX
 , we can write
 3
 2
 EX=∫1
 −1ufX(u)du
 3
 2
 = ∫1
 −1u3du
 =0.
 In fact, we could have guessed 
EX = 0
 because the PDF is symmetric around 
x =0
 . To find Var
 (X)
 , we have
 Var(X) = EX2 −(EX)2 =EX2
 =∫1
 −1 u2fX(u)du
 3
 2
 3
 = ∫1
 −1u4du
 = .
 5
 1
 c. To find 
P(X ≥ )
 , we can write
 2
 1
P(X≥ )= ∫
 1
 x2dx= .
 
Problem 2
 Let 
X
 be a continuous random variable with PDF given by
 fX(x)= e−|x|, for all x∈R.
 If 
Y=X2
 , find the CDF of 
Y
 .
 Solution
 First, we note that 
RY=[0,∞)
 . For 
y∈[0,∞)
 , we have
 FY(y)=P(Y≤y)
 =P(X2≤y)
 =P(−√y≤X≤√y)
 =∫√y
 −√y e−|x|dx
 =∫√y
 0 e−xdx
 =1−e−√y. 
Thus,
 FY(y)={1−e−√y y≥0
 0 otherwise
 
Problem 3
 Let 
X
 be a continuous random variable with PDF
 fX(x)={4x3 0<x≤1
 0 otherwise
 Find 
P(X≤ |X> )
 .
 Solution
 1
 2
 3
 2 1
 2
 7
 16
 1
 2
 1
 2
 2
 3
 1
 3
We have
 P(X≤ |X> )=
 =
 = . 
 
Problem 4
 Let 
X
 be a continuous random variable with PDF
 fX(x)=
 {x2
 ( 2x+ ) 0<x≤1
 0 otherwise
 If 
Y= +3
 , find Var
 (Y)
 .
 Solution
 First, note that
 Var(Y)=Var ( +3 )=4Var ( ) , using Equation 4.4
 Thus, it suffices to find Var
 ( )=E[ ]−(E[ ])2
 . Using LOTUS, we have
 E
 [ ] =∫
 1
 0
 x
 ( 2x+ ) dx=
 E
 [ ] =∫
 1
 0 ( 2x+ ) dx= .
 Thus, Var
 ( )=E[ ]−(E[ ])2=
 . So, we obtain
 Var(Y)=4Var ( )= .
 2
 3
 1
 3
 P( <X≤ ) 1
 3
 2
 3
 P(X> ) 1
 3
 ∫ 4x3dx
 2
 3
 1
 3
 ∫ 1
 4x3dx 1
 3
 3
 16
 3
 2
 2
 X
 2
 X
 1
 X
 1
 X
 1
 X2
 1
 X
 1
 X
 3
 2
 17
 12
 1
 X2
 3
 2
 5
 2
 1
 X
 1
 X2
 1
 X
 71
 144
 1
 X
 71
 36
Problem 5
 
Let 
X
 be a 
positive continuous random variable. Prove that 
EX = ∫ ∞
 0 P(X≥x)dx
 .
 Solution
 We have
 Thus, we need to show that
 ∞
 P(X ≥x)=
 ∫ 
∞
 ∞
 ∫
 x
 fX(t)dt.
 fX(t)dtdx = EX.
 0
 ∫
 x
 The left hand side is a double integral. In particular, it is the integral of 
fX(t)
 over the
 shaded region in Figure 4.4.
 Fig.4.4 - The shaded area shows the region of the double integral of
 Problem 5.
 We can take the integral with respect to 
x
 or 
t
 . Thus, we can write
 ∫ ∞
 0 ∫ ∞
 x fX(t)dtdx = ∫ ∞
 0 ∫t
 0 fX(t)dxdt
 =∫∞
 0 fX(t) (∫ t
 0 1dx ) dt
 
 
Problem 6
 Let 
X∼Uniform(− ,π)
 and 
Y=sin(X)
 . Find 
fY(y)
 .
 Solution
 Here 
Y=g(X)
 , where 
g
 is a differentiable function. Although 
g
 is not monotone, it can
 be divided to a finite number of regions in which it is monotone. Thus, we can use
 Equation 4.6. We note that since 
RX=[− ,π]
 , 
RY=[−1,1]
 . By looking at the plot of 
g(x)=sin(x)
 over 
[− ,π]
 , we notice that for 
y∈(0,1)
 there are two solutions to 
y=g(x)
 , while for 
y∈(−1,0)
 , there is only one solution. In particular, if 
y∈(0,1)
 , we
 have two solutions: 
x1=arcsin(y)
 , and 
x2=π−arcsin(y)
 . If 
y∈(−1,0)
 we have one
 solution, 
x1=arcsin(y)
 . Thus, for 
y∈(−1,0)
 , we have
 fY(y)=
 =
 = .
 
For 
y∈(0,1)
 , we have
 fY(y)= +
 = +
 = +
 = .
 
To summarize, we can write
 fY(y)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪⎩
 −1<y<0
 0<y<1
 0 otherwis