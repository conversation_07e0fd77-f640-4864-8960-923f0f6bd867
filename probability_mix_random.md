4.3.1 Mixed Random Variables
 Here, we will discuss mixed random variables. These are random variables that are
 neither discrete nor continuous, but are a mixture of both. In particular, a mixed
 random variable has a continuous part and a discrete part. Thus, we can use our tools
 from previous chapters to analyze them. In this section, we will provide some
 examples on how we can do this. Then in section 4.3.2, we will revisit the concept of
 mixed random variables using the delta "function."
 Example 4.14
 
Let 
X
 be a continuous random variable with the following PDF:
 fX(x) =
 Let also
 {2x
 0
 Y =g(X)=
 1
 2
 0 ≤x≤1
 otherwise
 {X 0≤X≤
 1
 X>
 Find the CDF of 
Y
 .
 Solution
 2
 1
 1
 2
 First we note that 
RX = [0,1]
 . For 
x ∈ [0,1]
 , 
0 ≤ g(x) ≤ 
. Thus, 
RY = [0, ]
 , and
 therefore
 2
 FY(y) = 0,
 for y < 0,
 1
 FY(y) = 1, for y > .
 2
 Now note that
 1
 P
 2
 1
 ( Y = )=P (X> )
 =∫ 1
 2xdx = .
 2
 1
 2
 3
 4
 1
 2
Also, for 
0 < y < 
,
 1
 2
 Thus, the CDF of 
Y
 is given by
 FY(y) = P(Y ≤y)
 =P(X≤y)
 =∫y
 0 2xdx
 =y2.
 1
 1 y≥
 FY(y) =
 ⎧
 ⎪ 
⎪
 ⎨
 ⎪ 
⎪⎩
 2
 y2 0≤y<
 0
 1
 2
 otherwise
 Figure 4.9 shows the CDF of 
Y
 . We note that the CDF is not continuous, so 
Y
 is not a
 continuous random variable. On the other hand, the CDF is not in the staircase form,
 so it is not a discrete random variable either. It is indeed a mixed random variable.
 1
 2
 1
 2
 1
 3
 There is a jump at 
y = 
, and the amount of jump is 
1 − = 
, which is the probability
 that 
Y = 
. The CDF is continuous at other points.
 4
 4
 Fig.4.9 - CDF of a Mixed random variable, Example 4.12.
 The CDF of 
Y
 has a continuous part and a discrete part. In particular, we can write
 FY(y) = C(y)+D(y),
 where 
C(y)
 is the continuous part of 
FY (y)
 , i.e.,
 1
 1
C(y)=⎧ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎩
 y≥
 y2 0≤y<
 0 y<0
 The discrete part of 
FY(y)
 is 
D(y)
 , given by
 D(y)=
 { y≥
 0 y<
 
In general, the CDF of a mixed random variable 
Y
 can be written as the sum of a
 continuous function and a staircase function:
 FY(y)=C(y)+D(y).
 We differentiate the continuous part of the CDF. In particular, let's define
 c(y)= , wherever C(y) is differentiable.
 Note that this is not a valid PDF as it does not integrate to one. Also, let 
{y1,y2,y3,...} be the set of jump points of 
D(y)
 , i.e., the points for which 
P(Y=yk)>0
 . We then
 have
 ∫
 ∞
 −∞
 c(y)dy+∑ yk
 P(Y=yk)=1.
 The expected value of 
Y
 can be obtained as
 EY=∫
 ∞
 −∞
 yc(y)dy+∑ yk
 ykP(Y=yk).
 
Example 4.15
 Let 
Y
 be the mixed random variable defined in Example 4.14.
 a. Find 
P( ≤Y≤ )
 .
 b. Find 
P(Y≥ )
 .
 c. Find 
EY
 .
 Solution
 1
 4
 1
 2
 1
 2
 3
 4
 1
 2
 1
 2
 dC(y)
 dy
 1
 4
 3
 8
 1
 4
Since we have the CDF of 
Y
 , we can find the probability that 
Y
 is in any given interval.
 We should pay special attention if the interval includes any jump points.
 a. Find 
P( ≤Y≤ )
 : We can write
 P ( ≤Y≤ )=FY
 ( )−FY
 ( )+P ( Y= )
 =( )2
 −( )2
 +0= .
 
b. Find 
P(Y≥ )
 : We have
 P ( Y≥ )=1−FY
 ( )+P ( Y= )
 =1−( )2
 = .
 
c. Find 
EY
 : Here, we can differentiate the continuous part of the CDF to obtain
 c(y)= =
 {2y 0≤y≤
 0 otherwise
 So, we can find 
EY
 as
 EY=∫0 y(2y)dy+ P ( Y= ) = + = . 
1
 4
 3
 8
 1
 4
 3
 8
 3
 8
 1
 4
 1
 4
 3
 8
 1
 4
 5
 64
 1
 4
 1
 4
 1
 4
 1
 4
 1
 4
 15
 16
 dC(y)
 dy
 1
 2
 1
 2 1
 2
 1
 2
 1
 12
 3
 8
 11
 24
4.3.2 Using the Delta Function
 In this section, we will use the Dirac delta function to analyze mixed random variables.
 Technically speaking, the Dirac delta function is not actually a function. It is what we
 may call a generalized function. Nevertheless, its definition is intuitive and it simplifies
 dealing with probability distributions.
 Remember that any random variable has a CDF. Thus, we can use the CDF to answer
 questions regarding discrete, continuous, and mixed random variables. On the other
 hand, the PDF is defined only for continuous random variables, while the PMF is
 defined only for discrete random variables. Using delta functions will allow us to define
 the PDF for discrete and mixed random variables. Thus, it allows us to unify the theory
 of discrete, continuous, and mixed random variables.
 Dirac Delta Function
 
Remember, we cannot define the PDF for a discrete random variable because its CDF
 has jumps. If we could somehow differentiate the CDF at jump points, we would be
 able to define the PDF for discrete random variables as well. This is the idea behind
 our effort in this section. Here, we will introduce the Dirac delta function and discuss its
 application to probability distributions. If you are less interested in the derivations, you
 may directly jump to Definition 4.3 and continue from there. Consider the unit step
 function 
u(x)
 defined by
 u(x) =
 {1 x≥0
 0
 otherwise
 (4.8)
 This function has a jump at 
x = 0
 . Let us remove the jump and define, for any 
α > 0
 ,
 the function 
uα
 as
 ⎧
 uα(x) =
 ⎪ 
⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪⎩
 α
 1 x>
 1
 α
 α
 2
 α
 2
 2
 α
 2
 α
 (x + ) − ≤x≤
 0 x<−
 The good thing about 
uα(x)
 is that it is a continuous function. Now let us define the
 function 
δα(x)
 as the derivative of 
uα(x)
 wherever it exists.
 2
 1
 α
 δα(x) = =
 duα(x)
 dx
 { |x|<
 0 |x|>
 α
 2
 α
 2
Figure 4.10 shows these functions.
 Fig.4.10 - Functions 
u(x),uα(x)
 , and 
δα(x)
 .
 We notice the following relations:
 d
 dx
 δα(x) = uα(x), u(x)= lim
 α→0
 uα(x)
 Now, we would like to define the delta "function", 
δ(x)
 , as
 δ(x) = lim
 α→0
 δα(x)
 (4.9)
 (4.10)
 Note that as 
α
 becomes smaller and smaller, the height of 
δα(x)
 becomes larger and
 larger and its width becomes smaller and smaller. Taking the limit, we obtain
 δ(x) =
 0
 {∞ x=0
 otherwise
 Combining Equations 4.9 and 4.10, we would like to symbolically write
 δ(x) =
 d
 dx
 u(x).
 Intuitively, when we are using the delta function, we have in mind 
δα(x)
 with extremely
 small 
α
 . In particular, we would like to have the following definitions. Let 
g : R ↦ R
 be
 a continuous function. We 
define
∫
 ∞
 −∞
 g(x)δ(x−x0)dx=lim α→0[∫
 ∞
 −∞
 g(x)δα(x−x0)dx
 ] (4.11)
 Then, we have the following lemma, which in fact is the most useful property of the
 delta function.
 Lemma 4.1
 Let 
g:R↦R
 be a continuous function. We have
 ∫
 ∞
 −∞
 g(x)δ(x−x0)dx=g(x0).
 Proof
 Let 
I
 be the value of the above integral. Then, we have
 I=limα→0
 [ ∫∞
 −∞g(x)δα(x−x0)dx
 ]
 =limα→0
 [ ∫
 x0+
 x0− dx
 ] .
 
By the mean value theorem in calculus, for any 
α>0
 , we have
 ∫
 x0+
 x0−
 dx=α =g(xα),
 for some 
xα∈(x0− ,x0+ ).
 Thus, we have
 I=lim α→0
 g(xα)=g(x0).
 The last equality holds because 
g(x)
 is a continuous function and 
limα→0xα=x0
 .
 For example, if we let 
g(x)=1
 for all 
x∈R
 , we obtain
 ∫
 ∞
 −∞
 δ(x)dx=1.
 It is worth noting that the Dirac 
δ
 function is not strictly speaking a valid function. The
 reason is that there is no function that can satisfy both of the conditions
 δ(x)=0( for x≠0) and ∫
 ∞
 −∞
 δ(x)dx=1.
 We can think of the delta function as a convenient notation for the integration condition
 4.11. The delta function can also be developed formally as a generalized function.
 Now, let us summarize properties of the delta function.
 α
 2
 α
 2
 g(x)
 α
 α
 2
 α
 2
 g(x)
 α
 g(xα)
 α
 α
 2
 α
 2
Definition 4.3: Properties of the delta function
 
We define the delta function 
δ(x)
 as an object with the following properties:
 1. 
δ(x) = {∞
 0
 d
 dx
 x =0
 otherwise
 2. 
δ(x) = u(x)
 , where 
u(x)
 is the unit step function (Equation 4.8);
 3. 
∫ ϵ
 −ϵ δ(x)dx = 1
 , for any 
ϵ > 0
 ;
 4. For any 
ϵ > 0
 and any function 
g(x)
 that is continuous over 
(x0 − ϵ,x0 + ϵ)
 ,
 we have
 ∞
 ∫
 −∞
 x0+ϵ
 g(x)δ(x −x0)dx =
 ∫
 x0−ϵ
 g(x)δ(x −x0)dx = g(x0).
 Figure 4.11 shows how we represent the delta function. The delta function, 
δ(x)
 , is
 shown by an arrow at 
x = 0
 . The height of the arrow is equal to 
1
 . If we want to
 represent 
2δ(x)
 , the height would be equal to 
2
 . In the figure, we also show the
 function 
δ(x − x0)
 , which is the shifted version of 
δ(x)
 .
 Fig.4.11 - Graphical representation of delta function.
 Using the Delta Function in PDFs of Discrete and Mixed Random Variables
 
In this section, we will use the delta function to extend the definition of the PDF to
 discrete and mixed random variables. Consider a discrete random variable 
X
 with
 range 
RX = {x1,x2,x3,...}
 and PMF 
PX(xk)
 . Note that the CDF for 
X
 can be written
 as
 FX(x) =
 ∑
 xk∈RX
 PX(xk)u(x −xk).
 Now that we have symbolically defined the derivative of the step function as the delta
 function, we can write a PDF for 
X
 by "differentiating" the CDF:
fX(x) =
 dFX(x)
 dx
 d
 dx
 =∑xk∈RX 
PX(xk) u(x−xk)
 =∑xk∈RX 
PX(xk)δ(x−xk).
 We call this the generalized PDF.
 For a discrete random variable 
X
 with range 
RX = {x1,x2,x3,...}
 and PMF 
PX(xk)
 , we define the (generalized) probability density function (PDF) as
 fX(x) =
 ∑
 xk∈RX
 PX(xk)δ(x −xk).
 Note that for any 
xk ∈ RX
 , the probability of 
X = xk
 is given by the coefficient of the
 corresponding 
δ
 function, 
δ(x − xk)
 .
 It is useful to use the generalized PDF because all random variables have a
 generalized PDF, so we can use the same formulas for discrete, continuous, and
 mixed random variables. If the (generalized) PDF of a random variable can be written
 as the sum of delta functions, then 
X
 is a discrete random variable. If the PDF does
 not include any delta functions, then 
X
 is a continuous random variable. Finally, if the
 PDF has both delta functions and non-delta functions, then 
X
 is a mixed random
 variable. Nevertheless, the formulas for probabilities, expectation and variance are the
 same for all kinds of random variables.
 To see how this works, we will consider the calculation of the expected value of a
 discrete random variable. Remember that the expected value of a continuous random
 variable is given by
 ∞
 EX=
 ∫
 −∞
 xfX(x)dx.
 Now suppose that I have a discrete random variable 
X
 . We can write
 EX=∫∞
 −∞xfX(x)dx
 =∫∞
 −∞x∑xk∈RX 
PX(xk)δ(x−xk)dx
 =∑xk∈RX 
PX(xk)∫ ∞
 −∞xδ(x−xk)dx
 =∑xk∈RX 
xkPX(xk)
 by the 4th property in Definition 4.3,
 which is the same as our original definition of expected value for discrete random
variables. Let us practice these concepts by looking at an example.
 Example 4.16
 
Let 
X
 be a random variable with the following CDF:
 ⎧
 FX(x) =
 ⎪ 
⎪
 ⎨
 ⎪ 
⎪⎩
 1
 2
 1
 4
 0
 1
 2
 + (1−e−x)
 1
 2
 + (1−e−x)
 x ≥1
 0 ≤x<1
 x <0
 a. What kind of random variable is 
X
 (discrete, continuous, or mixed)?
 b. Find the (generalized) PDF of 
X
 .
 c. Find 
P(X > 0.5)
 , both using the CDF and using the PDF.
 d. Find 
EX
 and Var
 (X)
 .
 Solution
 a. Let us plot 
FX(x)
 to better understand the problem. Figure 4.12 shows 
FX(x)
 . We
 see that the CDF has two jumps, at 
x = 0
 and 
x = 1
 . The CDF increases
 continuously from 
x = 0
 to 
x = 1
 and also after 
x = 1
 . Since the CDF is neither in
 the form of a staircase function, nor is it continuous, we conclude that 
X
 is a
 mixed random variable.
 Fig.4.11 - The CDF of 
X
 in Example 4.16.
 b. To find the PDF, we need to differentiate the CDF. We must be careful about the
 1
 4
 1
 1
 4
 points of discontinuity. In particular, we have two jumps: one at 
x = 0
 and one at 
x =1
 . The size of the jump for both points is equal to . Thus, the CDF has two
 delta functions: 
δ(x) + δ(x −1)
 . The continuous part of the CDF can be
 written as 
(1 −e−x)
 , for 
x > 0
 . Thus, we conclude
 2
 1
 1
 4
 1
 1
 fX(x) = δ(x)+ δ(x−1)+ e−x
 u(x).
 4
 4
 2
c. Using the CDF, we have
 P(X>0.5)=1−FX(0.5)
 =1− [ + (1−e−x) ] = + e−0.5
 =0.5533 
Using The PDF, we can write
 P(X>0.5)=∫∞
 0.5fX(x)dx
 =∫∞
 0.5( δ(x)+ δ(x−1)+ e−xu(x) ) dx
 =0+ + ∫∞
 0.5e−xdx (using Property 3 in Definition 4.3)
 = + e−0.5=0.5533 
d. We have
 EX=∫∞
 −∞xfX(x)dx
 =∫∞
 −∞( xδ(x)+ xδ(x−1)+ xe−xu(x) ) dx
 = ×0+ ×1+ ∫∞
 0 xe−xdx (using Property 4 in Definition 4.3)
 = + ×1= . 
Note that here 
∫∞
 0 xe−xdx
 is just the expected value of an 
Exponential(1) random variable, which we know is equal to 
1
 .
 EX2=∫∞
 −∞x2fX(x)dx
 =∫∞
 −∞( x2δ(x)+ x2δ(x−1)+ x2e−xu(x) ) dx
 = ×0+ ×1+ ∫∞
 0 x2e−xdx (using Property 4 in Definition 4.3)
 = + ×2=
 .
 
Again, note that 
∫∞
 0 x2e−xdx
 is just 
EX2
 for an 
Exponential(1)
 random variable,
 which we know is equal to 
2
 . Thus,
 Var(X)=EX2−(EX)2
 = −( )2
 =
 .
 
1
 4
 1
 2
 1
 4
 1
 2
 1
 4
 1
 4
 1
 2
 1
 4
 1
 2
 1
 4
 1
 2
 1
 4
 1
 4
 1
 2
 1
 4
 1
 4
 1
 2
 1
 4
 1
 2
 3
 4
 1
 4
 1
 4
 1
 2
 1
 4
 1
 4
 1
 2
 1
 4
 1
 2
 5
 4
 5
 4
 3
 4
 11
 16
In general, we can make the following statement:
 The (generalized) PDF of a mixed random variable can be written in the form
 fX(x) =
 ∑
 k
 akδ(x −xk)+g(x),
 where 
ak = P(X =xk)
 , and 
g(x) ≥ 0
 does not contain any delta functions.
 Furthermore, we have
 ∞
 ∫
 fX(x)dx =
 −∞
 ak +
 ∑
 k
 ∞
 ∫
 −∞
 g(x)dx = 1.
4.3.3 Solved Problems:
 
Mixed Random Variables
 Problem 1
 
Here is one way to think about a mixed random variable. Suppose that we have a
 discrete random variable 
Xd
 with (generalized) PDF and CDF 
fd(x)
 and 
Fd(x)
 , and a
 continuous random variable 
Xc
 with PDF and CDF 
fc(x)
 and 
Fc(x)
 . Now we create a
 new random variable 
X
 in the following way. We have a coin with 
P(H) = p
 . We toss
 the coin once. If it lands heads, then the value of 
X
 is determined according to the
 probability distribution of 
Xd
 . If the coin lands tails, the value of 
X
 is determined
 according to the probability distribution of 
Xc
 .
 a. Find the CDF of 
X,FX(x)
 .
 b. Find the PDF of 
X,fX(x)
 .
 c. Find 
EX
 .
 d. Find Var
 (X)
 .
 Solution
 a. Find the CDF of 
X,FX(x)
 : We can write
 FX(x) = P(X ≤x)
 =P(X≤x|H)P(H)+P(X ≤x|T)P(T)(law of total probability)
 =pP(Xd ≤x)+(1−p)P(Xc ≤x)
 =pFd(x)+(1−p)Fc(x)
 .
 b. Find the PDF of 
X,fX(x)
 : By differentiating 
FX(x)
 , we obtain
 dFX(x)
 dx
 fX(x) =
 =pfd(x)+(1−p)fc(x)
 .
 c. Find 
EX
 : We have
 EX=∫∞
 −∞xfX(x)dx
 =p∫∞
 −∞xfd(x)dx+(1−p)∫ ∞
 =pEXd +(1−p)EXc
 .
 −∞xfc(x)dx
d. Find Var
 (X)
 :
 EX2 =∫∞
 −∞x2fX(x)dx
 =p∫∞
 −∞x2fd(x)dx+(1−p)∫ ∞
 =pEX2
 d +(1−p)EX2
 c
 .
 −∞x2fc(x)dx
 Thus,
 Var(X) = EX2 −(EX)2
 =pEX2
 d +(1−p)EX2
 c −(pEXd +(1−p)EXc)2
 =pEX2
 d +(1−p)EX2
 c −p2(EXd)2 −(1−p)2(EXc)2 −2p(1−p)EXdEXc
 =p(EX2
 d −(EXd)2)+(1−p)(EX2
 c −(EXc)2)+p(1−p)(EXd −EXc)2
 =pVar(Xd)+(1−p)Var(Xc)+p(1−p)(EXd −EXc)2 .
 Problem 2
 
Let 
X
 be a random variable with CDF
 ⎧
 ⎪ 
FX(x) =
 ⎪
 ⎨
 ⎪ 
⎩
 ⎪
 1
 1
 2
 0
 x
 x ≥1
 + 0≤x<1
 2
 x <0
 a. What kind of random variable is X: discrete, continuous, or mixed?
 b. Find the PDF of X, 
fX(x)
 .
 c. Find 
E(eX)
 .
 d. Find 
P(X = 0|X ≤ 0.5)
 .
 Solution
 a. What kind of random variable is X: discrete, continuous, or mixed? We note that
 the CDF has a discontinuity at 
x = 0
 , and it is continuous at other points. Since 
FX(x)
 is not flat in other locations, we conclude 
X
 is a mixed random variable.
 Indeed, we can write
 1
 1
 FX(x) = u(x)+ FY(x),
 2
 2
 where 
Y
 is a 
Uniform(0,1)
 random variable. If we use the interpretation of
 Problem 1, we can say the following. We toss a fair coin. If it lands heads then 
X=0
 , otherwise 
X
 is obtained according the a 
Uniform(0,1)
 distribution.
b. Find the PDF of X, 
fX(x)
 : By differentiating the CDF, we obtain
 fX(x)= δ(x)+ fY(x),
 where 
fY(x)
 is the PDF of 
Uniform(0,1)
 , i.e.,
 fY(x)={1 0<x<1
 0 otherwise
 c. Find 
E(eX)
 : We can use LOTUS to write
 E(eX)=∫∞
 −∞exfX(x)dx
 = ∫∞
 −∞exδ(x)dx+ ∫∞
 −∞exfY(x)dx
 = e0+ ∫1
 0 exdx
 = + (e−1)
 = e
 .
 
Here is another way to think about this part: similar to part (c) of Problem 1, we
 can write
 E(eX)= ×e0+ E[eY]
 = + ∫1
 0 eydy
 = e
 .
 
d. Find 
P(X=0|X≤0.5)
 : We have
 P(X=0|X≤0.5)=
 =
 =
 = =
 .
 
 
Problem 3
 Let 
X
 be a 
Uniform(−2,2)
 continuous random variable. We define 
Y=g(X)
 , where
 the function 
g(x)
 is defined as
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 1
 2
 P(X=0,X≤0.5)
 P(X≤0.5)
 P(X=0)
 P(X≤0.5)
 0.5
 ∫0.5
 0 fX(x)dx
 0.5
 0.75
 2
 3
g(x) =
 ⎧
 1
 ⎪
 ⎨
 ⎩
 ⎪
 Find the CDF and PDF of 
Y
 .
 Solution
 Note that 
RY = [0,1]
 . Therefore,
 x
 0
 x >1
 0 ≤x≤1
 otherwise
 FY(y) = 0, for y <0,
 FY(y) = 1, for y ≥1.
 We also note that
 Also for 
0 < y < 1
 ,
 1
 P(Y =0)=P(X<0)= ,
 2
 1
 P(Y =1)=P(X>1)= .
 4
 FY(y) = P(Y ≤y) =P(X ≤y)=FX(y)= .
 y +2
 4
 Thus, the CDF of 
Y
 is given by
 FY(y) =
 ⎧
 ⎪ 
⎪
 ⎨
 ⎪ 
⎩
 ⎪
 1
 y+2
 4
 0
 y ≥1
 0 ≤y<1
 otherwise
 In particular, we note that there are two jumps in the CDF, one at 
y = 0
 and another at 
y =1
 . We can find the generalized PDF of 
Y
 by differentiating 
FY (y)
 :
 1
 1
 1
 fY (y) = δ(y)+ δ(y−1)+ (u(y)−u(y−1)).
 2
 4
 4
4.4 End of Chapter Problems
 Problem 1
 
Choose a real number uniformly at random in the interval 
[2,6]
 and call it 
X
 .
 a. Find the CDF of X, 
FX(x)
 .
 b. Find 
EX
 .
 Problem 2
 
Let 
X
 be a continuous random variable with the following PDF
 fX(x) =
 where 
c
 is a positive constant.
 a. Find c.
 b. Find the CDF of X, 
FX(x)
 .
 c. Find 
P(2 < X < 5)
 .
 d. Find 
EX
 .
 {ce−4x
 0
 x ≥0
 otherwise
 Problem 3
 
Let 
X
 be a continuous random variable with PDF
 2
 fX(x) =
 a. Find 
E(Xn)
 , for 
n = 1,2,3,⋯
 .
 b. Find the variance of 
X
 .
 3
 0
 {x2 + 0≤x≤1
 otherwise
 Problem 4
 
Let 
X
 be a 
uniform(0,1)
 random variable, and let 
Y = e−X
 .
 a. Find the CDF of 
Y
 .
b. Find the PDF of 
Y
 .
 c. Find 
EY
 .
 
Problem 5
 Let 
X
 be a continuous random variable with PDF
 fX(x)=
 { x4 0<x≤2
 0 otherwise
 and let 
Y=X2
 .
 a. Find the CDF of 
Y
 .
 b. Find the PDF of 
Y
 .
 c. Find 
EY
 .
 
Problem 6
 Let 
X∼Exponential(λ)
 , and 
Y=aX
 , where 
a
 is a positive real number. Show that
 Y∼Exponential
 ( ) .
 
Problem 7
 Let 
X∼Exponential(λ)
 . Show that
 a. 
EXn= EXn−1
 , for 
n=1,2,3,⋯
 ;
 b. 
EXn=
 , for 
n=1,2,3,⋯
 .
 
Problem 8
 Let 
X∼N(3,9)
 .
 a. Find 
P(X>0)
 .
 b. Find 
P(−3<X<8)
 .
 c. Find 
P(X>5|X>3)
 .
 
Problem 9
 
5
 32
 λ
 a
 n
 λ
 n!
 λn
Let 
X ∼ N(3,9)
 and 
Y = 5−X
 .
 a. Find 
P(X > 2)
 .
 b. Find 
P(−1 < Y < 3)
 .
 c. Find 
P(X > 4|Y < 2)
 .
 Problem 10
 
Let 
X
 be a continuous random variable with PDF
 x2
 2
 fX(x) = e−
 for all x∈R.
 1
 √
 2π
 and let 
Y = √
 |X|
 . Find 
fY (y)
 .
 Problem 11
 
Let 
X ∼ Exponential(2)
 and 
Y = 2 +3X
 .
 a. Find 
P(X > 2)
 .
 b. Find 
EY
 and 
Var(Y)
 .
 c. Find 
P(X > 2|Y < 11)
 .
 Problem 12
 
The median of a continuous random variable 
X
 can be defined as the unique real
 number m that satisfies
 1
 P(X ≥m)=P(X<m)= .
 2
 Find the median of the following random variables
 a. 
X ∼ Uniform(a,b)
 .
 b. 
Y ∼ Exponential(λ)
 .
 c. 
W ∼N(μ,σ2)
 .
 Problem 13
 
Let 
X
 be a random variable with the following CDF
FX(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0 forx<0
 x for0≤x<
 x+ for ≤x<
 1 forx≥
 a. Plot 
FX(x)
 and explain why 
X
 is a mixed random variable.
 b. Find 
P(X≤ )
 .
 c. Find 
P(X≥ )
 .
 d. Write CDF of 
X
 in the form of
 FX(x)=C(x)+D(x),
 where 
C(x)
 is a continuous function and 
D(x)
 is in the form of a staircase
 function, i.e.,
 D(x)=∑ k
 aku(x−xk).
 e. Find 
c(x)= C(x)
 .
 f. Find 
EX
 using 
EX=∫∞
 −∞xc(x)dx+∑kxkak
 
Problem 14
 Let 
X
 be a random variable with the following CDF
 FX(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0 forx<0
 x for0≤x<
 x+ for ≤x<
 1 forx≥
 a. Find the generalized PDF of 
X,fX(x)
 .
 b. Find 
EX
 using 
fX(x)
 .
 c. Find 
Var(X)
 using 
fX(x)
 .
 
Problem 15
 Let 
X
 be a mixed random variable with the following generalized PDF
 fX(x)= δ(x+2)+ δ(x−1)+ e−
 .
 1
 4
 1
 2
 1
 4
 1
 2
 1
 2
 1
 3
 1
 4
 d
 dx
 1
 4
 1
 2
 1
 4
 1
 2
 1
 2
 1
 3
 1
 6
 1
 2
 1
 √2π
 x2
 2
a. Find 
P(X = 1)
 and 
P(X = −2)
 .
 b. Find 
P(X ≥ 1)
 .
 c. Find 
P(X = 1|X ≥ 1)
 .
 d. Find 
EX
 and 
Var(X)
 .
 Problem 16
 
A company makes a certain device. We are interested in the lifetime of the device. It is
 estimated that around 2% of the devices are defective from the start so they have a
 lifetime of 0 years. If a device is not defective, then the lifetime of the device is
 exponentially distributed with a parameter 
λ = 2
 years. Let X be the lifetime of a
 randomly chosen device.
 a. Find the generalized PDF of 
X
 .
 b. Find 
P(X ≥ 1)
 .
 c. Find 
P(X > 2|X ≥ 1)
 .
 d. Find 
EX
 and 
Var(X)
 .
 Problem 17
 
A continuous random variable is said to have a 
Laplace(μ,b)
 distribution [
 is given by
 fX(x) = exp
 1
 2b
 1
 2b
 x−μ
 b
 ( −
 )
 exp( ) if x<μ
 =
 where 
μ ∈ R
 and 
b > 0
 .
 ⎧
 ⎪
 ⎨
 ⎪⎩
 1
 2b
 x−μ
 b
 exp(− ) if x≥μ
 14] if its PDF
 |x −μ|
 b
 a. If 
X ∼ Laplace(0,1)
 , find 
EX
 and 
Var(X)
 .
 b. If 
X ∼ Laplace(0,1)
 and 
Y = bX +μ
 , show that 
Y ∼ Laplace(μ,b)
 .
 c. Let 
Y ∼ Laplace(μ,b)
 , where 
μ ∈ R
 and 
b > 0
 . Find 
EY
 and 
Var(Y)
 .
 Problem 18
 
Let 
X ∼ Laplace(0,b)
 , i.e.,
( − 
) ,
 fX(x) = exp
 1
 2b
 |x|
 b
 where 
b > 0
 . Define 
Y = |X|
 . Show that 
Y ∼ Exponential ( )
 .
 1
 b
 Problem 19
 
A continuous random variable is said to have the standard Cauchy distribution if its
 PDF is given by
 fX(x) = .
 1
 π(1 +x2)
 If 
X
 has a standard Cauchy distribution, show that 
EX
 is not well-defined. Also, show 
EX2 =∞
 .
 Problem 20
 
A continuous random variable is said to have a Rayleigh distribution with parameter 
σ
 if its PDF is given by
 fX(x) = e−x2/2σ2
 u(x)
 x
 σ2
 =
 where 
σ > 0
 .
 x
 σ2
 { e−x2/2σ2
 0
 if x ≥ 0
 if x < 0
 a. If 
X ∼ Rayleigh(σ)
 , find 
EX
 .
 b. If 
X ∼ Rayleigh(σ)
 , find the CDF of 
X,FX(x)
 .
 c. If 
X ∼ Exponential(1)
 and 
Y = √
 2σ2X
 , show that 
Y ∼ Rayleigh(σ)
 .
 Problem 21
 
A continuous random variable is said to have a 
Pareto(xm,α)
 distribution [
 PDF is given by
 fX(x) =
 xα
 m
 xα+1
 {α forx≥xm
 0
 for x < xm
 15] if its
 where 
xm,α > 0
 . Let 
X ∼ Pareto(xm,α)
 .
a. Find the CDF of 
X
 , 
FX(x)
 .
 b. Find 
P(X > 3xm|X > 2xm)
 .
 c. If 
α > 2
 , find 
EX
 and 
Var(X)
 .
 Problem 22
 
Let 
Z ∼ N(0,1)
 . If we define 
X = eσZ+μ
 , then we say that 
X
 has a log-normal
 distribution with parameters 
μ
 and 
σ
 , and we write 
X ∼ LogNormal(μ,σ)
 .
 a. If 
X ∼ LogNormal(μ,σ)
 , find the CDF of 
X
 in terms of the 
Φ
 function.
 b. Find 
EX
 and 
Var(X)
 .
 Problem 23
 
Let 
X1
 , 
X2
 , 
⋯
 , 
Xn
 be independent random variables with 
Xi ∼ Exponential(λ)
 .
 Define
 Y =X1+X2+⋯+Xn.
 As we will see later, 
Y
 has a Gamma distribution with parameters 
n
 and 
λ
 , i.e.,
 Y ∼Gamma(n,λ)
 . Using this, show that if 
Y ∼ Gamma(n,λ)
 , then 
EY =
 and 
Var(Y) = 
.
 n
 