<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linear Models Tutorial: From <PERSON><PERSON>'s Law to Statistical Inference</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        nav {
            background: #34495e;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #3498db;
        }
        
        main {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            border-left: 5px solid #3498db;
        }
        
        h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .formula-box {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }
        
        .step-box {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        .visualization {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            font-weight: 600;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .advantage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .advantage-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .advantage-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .code-box {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .warning-box {
            background: #e74c3c;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .info-box {
            background: #3498db;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            header {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            main {
                padding: 1rem;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Linear Models Tutorial</h1>
            <p class="subtitle">From Hubble's Law to Statistical Inference</p>
            <p>A comprehensive guide to understanding linear regression and its mathematical foundations</p>
        </header>
        
        <nav>
            <div class="nav-links">
                <a href="#introduction">Introduction</a>
                <a href="#simple-model">Simple Linear Model</a>
                <a href="#matrix-form">Matrix Formulation</a>
                <a href="#theory">Theory</a>
                <a href="#inference">Statistical Inference</a>
                <a href="#advanced">Advanced Topics</a>
            </div>
        </nav>
        
        <main>
            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>1. Introduction: How Old is the Universe?</h2>
                
                <div class="highlight-box">
                    <h3>🌌 The Cosmic Question</h3>
                    <p>The Big Bang model suggests that the universe expands uniformly according to <strong>Hubble's Law</strong>:</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Hubble's Law:</strong></p>
                    $$y = \beta x$$
                    <p>where:</p>
                    <ul style="text-align: left; margin-top: 1rem;">
                        <li><strong>y</strong>: relative velocity of galaxies</li>
                        <li><strong>x</strong>: distance between galaxies</li>
                        <li><strong>β</strong>: Hubble's constant (unknown parameter)</li>
                    </ul>
                </div>
                
                <p><strong>Key Insight:</strong> β⁻¹ gives the approximate age of the universe, but β must be estimated from observational data!</p>
                
                <div class="visualization">
                    <h4>Hubble Space Telescope Data Visualization</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
                            </linearGradient>
                        </defs>
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Axes -->
                        <line x1="80" y1="350" x2="720" y2="350" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="80" y1="50" x2="80" y2="350" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Data points (representing Hubble's observations) -->
                        <circle cx="120" cy="320" r="4" fill="#e74c3c"/>
                        <circle cx="160" cy="280" r="4" fill="#e74c3c"/>
                        <circle cx="200" cy="260" r="4" fill="#e74c3c"/>
                        <circle cx="240" cy="220" r="4" fill="#e74c3c"/>
                        <circle cx="280" cy="200" r="4" fill="#e74c3c"/>
                        <circle cx="320" cy="180" r="4" fill="#e74c3c"/>
                        <circle cx="360" cy="160" r="4" fill="#e74c3c"/>
                        <circle cx="400" cy="140" r="4" fill="#e74c3c"/>
                        <circle cx="440" cy="120" r="4" fill="#e74c3c"/>
                        <circle cx="480" cy="100" r="4" fill="#e74c3c"/>
                        <circle cx="520" cy="90" r="4" fill="#e74c3c"/>
                        <circle cx="560" cy="80" r="4" fill="#e74c3c"/>
                        
                        <!-- Fitted line -->
                        <line x1="80" y1="340" x2="680" y2="70" stroke="#3498db" stroke-width="3"/>
                        
                        <!-- Some scattered points showing measurement error -->
                        <circle cx="180" cy="300" r="4" fill="#e74c3c"/>
                        <circle cx="220" cy="200" r="4" fill="#e74c3c"/>
                        <circle cx="340" cy="140" r="4" fill="#e74c3c"/>
                        <circle cx="460" cy="110" r="4" fill="#e74c3c"/>
                        
                        <!-- Labels -->
                        <text x="400" y="380" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Distance (Mpc)</text>
                        <text x="30" y="200" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold" transform="rotate(-90 30 200)">Velocity (km/s)</text>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">Hubble Diagram: Galaxy Velocity vs Distance</text>
                        
                        <!-- Legend -->
                        <circle cx="600" cy="300" r="4" fill="#e74c3c"/>
                        <text x="620" y="305" font-size="12" fill="#2c3e50">Observed Data</text>
                        <line x1="590" y1="320" x2="610" y2="320" stroke="#3498db" stroke-width="3"/>
                        <text x="620" y="325" font-size="12" fill="#2c3e50">Fitted Line</text>
                    </svg>
                </div>
                
                <h3>The Problem with Direct Application</h3>
                <p>The observed data don't follow Hubble's law exactly due to:</p>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>🔬 Measurement Errors</h4>
                        <p>Distance measurement relies on Cepheid variable stars and their period-luminosity relationship</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📡 Velocity Errors</h4>
                        <p>Doppler shift measurements require correction for local velocity components</p>
                    </div>
                    <div class="advantage-card">
                        <h4>🌌 Natural Variation</h4>
                        <p>Cosmic phenomena introduce inherent variability in observations</p>
                    </div>
                </div>
                
                <div class="step-box">
                    <span class="step-number">1</span>
                    <strong>Statistical Questions We Need to Answer:</strong>
                    <ol style="margin-left: 3rem; margin-top: 0.5rem;">
                        <li>What value of β is most consistent with the data?</li>
                        <li>What range of β values is consistent with the data?</li>
                        <li>Are specific theoretical values of β consistent with our observations?</li>
                    </ol>
                </div>
            </section>
            
            <!-- Simple Linear Model Section -->
            <section id="simple-model" class="section">
                <h2>2. The Simple Linear Model</h2>
                
                <h3>2.1 Statistical Model Formulation</h3>
                <p>Instead of assuming perfect adherence to Hubble's law, we model the observed velocity as:</p>
                
                <div class="formula-box">
                    <p><strong>Statistical Model:</strong></p>
                    $$y_i = \beta x_i + \epsilon_i, \quad i = 1, 2, \ldots, n$$
                    <p>where:</p>
                    <ul style="text-align: left; margin-top: 1rem;">
                        <li><strong>y_i</strong>: observed velocity for galaxy i</li>
                        <li><strong>x_i</strong>: observed distance for galaxy i</li>
                        <li><strong>β</strong>: unknown Hubble constant</li>
                        <li><strong>ε_i</strong>: random error term with E(ε_i) = 0, Var(ε_i) = σ²</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <h4>🎯 Model Assumptions</h4>
                    <ul>
                        <li><strong>Independence:</strong> Error terms ε_i are mutually independent</li>
                        <li><strong>Zero Mean:</strong> E(ε_i) = 0 for all i</li>
                        <li><strong>Constant Variance:</strong> Var(ε_i) = σ² for all i</li>
                    </ul>
                </div>
                
                <h3>2.2 General Form</h3>
                <p>More formally, consider n observations (x_i, y_i) where y_i is an observation on random variable Y_i:</p>
                
                <div class="formula-box">
                    $$Y_i = \mu_i + \epsilon_i \quad \text{where} \quad \mu_i = x_i\beta$$
                    <p>Here μ_i ≡ E(Y_i) is the expected value of Y_i</p>
                </div>
                
                <div class="visualization">
                    <h4>Simple Linear Model Illustration</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Axes -->
                        <line x1="100" y1="350" x2="700" y2="350" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="100" y1="50" x2="100" y2="350" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- True regression line -->
                        <line x1="100" y1="300" x2="650" y2="100" stroke="#3498db" stroke-width="3"/>
                        
                        <!-- Data points and error visualization -->
                        <circle cx="200" cy="240" r="5" fill="#e74c3c"/>
                        <circle cx="300" cy="180" r="5" fill="#e74c3c"/>
                        <circle cx="400" cy="140" r="5" fill="#e74c3c"/>
                        <circle cx="500" cy="120" r="5" fill="#e74c3c"/>
                        <circle cx="600" cy="110" r="5" fill="#e74c3c"/>
                        
                        <!-- Expected values on the line -->
                        <circle cx="200" cy="255" r="3" fill="#3498db"/>
                        <circle cx="300" cy="210" r="3" fill="#3498db"/>
                        <circle cx="400" cy="165" r="3" fill="#3498db"/>
                        <circle cx="500" cy="120" r="3" fill="#3498db"/>
                        <circle cx="600" cy="125" r="3" fill="#3498db"/>
                        
                        <!-- Error arrows -->
                        <line x1="200" y1="240" x2="200" y2="255" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="300" y1="180" x2="300" y2="210" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="400" y1="140" x2="400" y2="165" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="600" y1="110" x2="600" y2="125" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <!-- Labels -->
                        <text x="220" y="235" font-size="12" fill="#e74c3c" font-weight="bold">εᵢ</text>
                        <text x="320" y="175" font-size="12" fill="#e74c3c" font-weight="bold">εᵢ</text>
                        <text x="420" y="135" font-size="12" fill="#e74c3c" font-weight="bold">εᵢ</text>
                        
                        <text x="180" y="270" font-size="12" fill="#e74c3c">yᵢ</text>
                        <text x="180" y="250" font-size="12" fill="#3498db">μᵢ</text>
                        
                        <!-- Axes labels -->
                        <text x="400" y="380" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Predictor Variable (x)</text>
                        <text x="50" y="200" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold" transform="rotate(-90 50 200)">Response Variable (Y)</text>
                        
                        <!-- Legend -->
                        <text x="450" y="80" font-size="14" fill="#3498db" font-weight="bold">E(Y) = μ = βx</text>
                        <text x="450" y="100" font-size="14" fill="#e74c3c" font-weight="bold">Y = μ + ε</text>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">Simple Linear Model Structure</text>
                    </svg>
                </div>
                
                <h3>2.3 Least Squares Estimation</h3>
                <p>How do we estimate β? We choose the value that makes the model fit closest to the data by minimizing the <strong>residual sum of squares</strong>:</p>
                
                <div class="formula-box">
                    <p><strong>Objective Function:</strong></p>
                    $$S = \sum_{i=1}^n (y_i - \mu_i)^2 = \sum_{i=1}^n (y_i - x_i\beta)^2$$
                </div>
                
                <div class="step-box">
                    <span class="step-number">2</span>
                    <strong>Deriving the Least Squares Estimator:</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        Differentiate S with respect to β and set to zero:
                    </p>
                </div>
                
                <div class="formula-box">
                    $$\frac{\partial S}{\partial \beta} = -2\sum_{i=1}^n x_i(y_i - x_i\beta) = 0$$
                    
                    <p>Solving for β̂:</p>
                    $$\hat{\beta} = \frac{\sum_{i=1}^n x_i y_i}{\sum_{i=1}^n x_i^2}$$
                </div>
                
                <h3>2.4 Properties of the Least Squares Estimator</h3>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>🎯 Unbiasedness</h4>
                        <p>E(β̂) = β</p>
                        <p>The estimator gets it right on average</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📊 Variance</h4>
                        <p>Var(β̂) = σ²/Σx²ᵢ</p>
                        <p>Precision depends on error variance and predictor spread</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📈 Efficiency</h4>
                        <p>Minimum variance among all linear unbiased estimators</p>
                        <p>Gauss-Markov theorem</p>
                    </div>
                </div>
            </section>
            
            <!-- Matrix Formulation Section -->
            <section id="matrix-form" class="section">
                <h2>3. Matrix Formulation of Linear Models</h2>
                
                <h3>3.1 Why Matrix Form?</h3>
                <p>Linear models become much more powerful when we can handle multiple predictors and complex relationships. The matrix formulation provides:</p>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>🔧 Generality</h4>
                        <p>Handle any number of predictors</p>
                    </div>
                    <div class="advantage-card">
                        <h4>⚡ Computational Efficiency</h4>
                        <p>Fast algorithms for large datasets</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📐 Mathematical Elegance</h4>
                        <p>Clean theoretical results</p>
                    </div>
                </div>
                
                <h3>3.2 General Linear Model Form</h3>
                <div class="formula-box">
                    <p><strong>Matrix Form:</strong></p>
                    $$\boldsymbol{\mu} = \mathbf{X}\boldsymbol{\beta}$$
                    <p>where:</p>
                    <ul style="text-align: left; margin-top: 1rem;">
                        <li><strong>μ</strong>: n×1 vector of expected values</li>
                        <li><strong>X</strong>: n×p model (design) matrix</li>
                        <li><strong>β</strong>: p×1 parameter vector</li>
                    </ul>
                </div>
                
                <h3>3.3 Examples of Model Matrices</h3>
                
                <div class="step-box">
                    <span class="step-number">3</span>
                    <strong>Example 1: Straight Line Model</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        μᵢ = β₀ + β₁xᵢ
                    </p>
                </div>
                
                <div class="formula-box">
                    $$\begin{bmatrix} \mu_1 \\ \mu_2 \\ \vdots \\ \mu_n \end{bmatrix} = \begin{bmatrix} 1 & x_1 \\ 1 & x_2 \\ \vdots & \vdots \\ 1 & x_n \end{bmatrix} \begin{bmatrix} \beta_0 \\ \beta_1 \end{bmatrix}$$
                </div>
                
                <div class="step-box">
                    <span class="step-number">4</span>
                    <strong>Example 2: Cubic Model</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        μᵢ = β₀ + β₁xᵢ + β₂xᵢ² + β₃xᵢ³
                    </p>
                </div>
                
                <div class="formula-box">
                    $$\mathbf{X} = \begin{bmatrix} 1 & x_1 & x_1^2 & x_1^3 \\ 1 & x_2 & x_2^2 & x_2^3 \\ \vdots & \vdots & \vdots & \vdots \\ 1 & x_n & x_n^2 & x_n^3 \end{bmatrix}$$
                </div>
                
                <div class="visualization">
                    <h4>Model Matrix Structure</h4>
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Background -->
                        <rect width="800" height="300" fill="url(#bg)" />
                        
                        <!-- Matrix X visualization -->
                        <rect x="100" y="50" width="200" height="150" stroke="#3498db" stroke-width="2" fill="none"/>
                        <text x="200" y="40" text-anchor="middle" font-size="14" fill="#3498db" font-weight="bold">Model Matrix X</text>
                        
                        <!-- Matrix elements -->
                        <text x="130" y="80" font-size="12" fill="#2c3e50">1</text>
                        <text x="160" y="80" font-size="12" fill="#2c3e50">x₁</text>
                        <text x="190" y="80" font-size="12" fill="#2c3e50">x₁²</text>
                        <text x="230" y="80" font-size="12" fill="#2c3e50">x₁³</text>
                        
                        <text x="130" y="100" font-size="12" fill="#2c3e50">1</text>
                        <text x="160" y="100" font-size="12" fill="#2c3e50">x₂</text>
                        <text x="190" y="100" font-size="12" fill="#2c3e50">x₂²</text>
                        <text x="230" y="100" font-size="12" fill="#2c3e50">x₂³</text>
                        
                        <text x="130" y="140" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="160" y="140" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="190" y="140" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="230" y="140" font-size="12" fill="#2c3e50">⋮</text>
                        
                        <text x="130" y="180" font-size="12" fill="#2c3e50">1</text>
                        <text x="160" y="180" font-size="12" fill="#2c3e50">xₙ</text>
                        <text x="190" y="180" font-size="12" fill="#2c3e50">xₙ²</text>
                        <text x="230" y="180" font-size="12" fill="#2c3e50">xₙ³</text>
                        
                        <!-- Multiplication symbol -->
                        <text x="350" y="130" font-size="20" fill="#2c3e50">×</text>
                        
                        <!-- Parameter vector -->
                        <rect x="400" y="80" width="50" height="100" stroke="#e74c3c" stroke-width="2" fill="none"/>
                        <text x="425" y="70" text-anchor="middle" font-size="14" fill="#e74c3c" font-weight="bold">β</text>
                        
                        <text x="425" y="100" text-anchor="middle" font-size="12" fill="#2c3e50">β₀</text>
                        <text x="425" y="120" text-anchor="middle" font-size="12" fill="#2c3e50">β₁</text>
                        <text x="425" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">β₂</text>
                        <text x="425" y="160" text-anchor="middle" font-size="12" fill="#2c3e50">β₃</text>
                        
                        <!-- Equals sign -->
                        <text x="500" y="130" font-size="20" fill="#2c3e50">=</text>
                        
                        <!-- Result vector -->
                        <rect x="550" y="80" width="50" height="100" stroke="#27ae60" stroke-width="2" fill="none"/>
                        <text x="575" y="70" text-anchor="middle" font-size="14" fill="#27ae60" font-weight="bold">μ</text>
                        
                        <text x="575" y="100" text-anchor="middle" font-size="12" fill="#2c3e50">μ₁</text>
                        <text x="575" y="120" text-anchor="middle" font-size="12" fill="#2c3e50">μ₂</text>
                        <text x="575" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="575" y="160" text-anchor="middle" font-size="12" fill="#2c3e50">μₙ</text>
                        
                        <!-- Dimensions -->
                        <text x="200" y="220" text-anchor="middle" font-size="12" fill="#3498db">n × p</text>
                        <text x="425" y="200" text-anchor="middle" font-size="12" fill="#e74c3c">p × 1</text>
                        <text x="575" y="200" text-anchor="middle" font-size="12" fill="#27ae60">n × 1</text>
                    </svg>
                </div>
                
                <h3>3.4 Factor Variables and Dummy Coding</h3>
                <p>Categorical predictors are handled using indicator (dummy) variables:</p>
                
                <div class="step-box">
                    <span class="step-number">5</span>
                    <strong>Example: Three Groups Model</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        μᵢ = βⱼ if observation i is in group j
                    </p>
                </div>
                
                <div class="formula-box">
                    $$\begin{bmatrix} \mu_1 \\ \mu_2 \\ \mu_3 \\ \mu_4 \\ \mu_5 \\ \mu_6 \end{bmatrix} = \begin{bmatrix} 1 & 0 & 0 \\ 1 & 0 & 0 \\ 0 & 1 & 0 \\ 0 & 1 & 0 \\ 0 & 0 & 1 \\ 0 & 0 & 1 \end{bmatrix} \begin{bmatrix} \beta_1 \\ \beta_2 \\ \beta_3 \end{bmatrix}$$
                    <p><em>2 observations per group, 3 groups total</em></p>
                </div>
            </section>
            
            <!-- Theory Section -->
            <section id="theory" class="section">
                <h2>4. Theory of Linear Models</h2>
                
                <h3>4.1 The Full Model</h3>
                <div class="formula-box">
                    <p><strong>Complete Linear Model:</strong></p>
                    $$\boldsymbol{\mu} = \mathbf{X}\boldsymbol{\beta}, \quad \mathbf{y} \sim N(\boldsymbol{\mu}, \mathbf{I}_n\sigma^2)$$
                    <p>where X is an n×p full rank matrix</p>
                </div>
                
                <h3>4.2 QR Decomposition Method</h3>
                <p>The key to efficient and stable computation is the QR decomposition:</p>
                
                <div class="formula-box">
                    $$\mathbf{X} = \mathbf{Q} \begin{bmatrix} \mathbf{R} \\ \mathbf{0} \end{bmatrix} = \mathbf{Q}_f \mathbf{R}$$
                    <p>where:</p>
                    <ul style="text-align: left; margin-top: 1rem;">
                        <li><strong>Q</strong>: n×n orthogonal matrix (Q^T Q = I)</li>
                        <li><strong>Q_f</strong>: first p columns of Q</li>
                        <li><strong>R</strong>: p×p upper triangular matrix</li>
                    </ul>
                </div>
                
                <div class="visualization">
                    <h4>QR Decomposition Visualization</h4>
                    <svg width="800" height="200" viewBox="0 0 800 200">
                        <!-- Background -->
                        <rect width="800" height="200" fill="url(#bg)" />
                        
                        <!-- Matrix X -->
                        <rect x="50" y="50" width="80" height="100" stroke="#3498db" stroke-width="2" fill="#3498db" opacity="0.3"/>
                        <text x="90" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">X</text>
                        <text x="90" y="170" text-anchor="middle" font-size="12" fill="#3498db">n × p</text>
                        
                        <!-- Equals -->
                        <text x="160" y="105" font-size="20" fill="#2c3e50">=</text>
                        
                        <!-- Matrix Q -->
                        <rect x="200" y="50" width="100" height="100" stroke="#e74c3c" stroke-width="2" fill="#e74c3c" opacity="0.3"/>
                        <text x="250" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Q</text>
                        <text x="250" y="170" text-anchor="middle" font-size="12" fill="#e74c3c">n × n</text>
                        
                        <!-- Multiplication -->
                        <text x="330" y="105" font-size="20" fill="#2c3e50">×</text>
                        
                        <!-- Matrix with R and 0 -->
                        <rect x="370" y="50" width="80" height="60" stroke="#27ae60" stroke-width="2" fill="#27ae60" opacity="0.3"/>
                        <rect x="370" y="110" width="80" height="40" stroke="#95a5a6" stroke-width="2" fill="#95a5a6" opacity="0.3"/>
                        <text x="410" y="85" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">R</text>
                        <text x="410" y="135" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">0</text>
                        <text x="410" y="170" text-anchor="middle" font-size="12" fill="#27ae60">p × p</text>
                        
                        <!-- Properties -->
                        <text x="550" y="80" font-size="12" fill="#2c3e50">Q^T Q = I (orthogonal)</text>
                        <text x="550" y="100" font-size="12" fill="#2c3e50">R upper triangular</text>
                        <text x="550" y="120" font-size="12" fill="#2c3e50">Numerically stable</text>
                    </svg>
                </div>
                
                <h3>4.3 Least Squares Solution</h3>
                <p>Using the QR decomposition, the least squares estimator becomes:</p>
                
                <div class="formula-box">
                    $$||\mathbf{y} - \mathbf{X}\boldsymbol{\beta}||^2 = ||\mathbf{Q}^T\mathbf{y} - \begin{bmatrix} \mathbf{R} \\ \mathbf{0} \end{bmatrix}\boldsymbol{\beta}||^2 = ||\mathbf{f} - \mathbf{R}\boldsymbol{\beta}||^2 + ||\mathbf{r}||^2$$
                    
                    <p>where Q^T y = [f^T, r^T]^T</p>
                    
                    <p><strong>Solution:</strong></p>
                    $$\hat{\boldsymbol{\beta}} = \mathbf{R}^{-1}\mathbf{f}$$
                </div>
                
                <h3>4.4 Distribution of β̂</h3>
                <div class="formula-box">
                    <p><strong>Exact Distribution:</strong></p>
                    $$\hat{\boldsymbol{\beta}} \sim N(\boldsymbol{\beta}, \mathbf{R}^{-1}\mathbf{R}^{-T}\sigma^2)$$
                    
                    <p><strong>Alternative form:</strong></p>
                    $$\hat{\boldsymbol{\beta}} \sim N(\boldsymbol{\beta}, (\mathbf{X}^T\mathbf{X})^{-1}\sigma^2)$$
                </div>
                
                <h3>4.5 Variance Estimation</h3>
                <div class="formula-box">
                    <p><strong>Unbiased estimator:</strong></p>
                    $$\hat{\sigma}^2 = \frac{||\mathbf{r}||^2}{n-p} = \frac{||\mathbf{y} - \mathbf{X}\hat{\boldsymbol{\beta}}||^2}{n-p}$$
                    
                    <p>where n-p are the residual degrees of freedom</p>
                </div>
            </section>
            
            <!-- Statistical Inference Section -->
            <section id="inference" class="section">
                <h2>5. Statistical Inference</h2>
                
                <h3>5.1 t-Distribution Results</h3>
                <p>For individual parameters, we have the fundamental result:</p>
                
                <div class="formula-box">
                    $$\frac{\hat{\beta}_i - \beta_i}{\hat{\sigma}_{\hat{\beta}_i}} \sim t_{n-p}$$
                    <p>where σ̂_β̂ᵢ is the standard error of β̂ᵢ</p>
                </div>
                
                <h3>5.2 Confidence Intervals</h3>
                <div class="step-box">
                    <span class="step-number">6</span>
                    <strong>95% Confidence Interval for βᵢ:</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        β̂ᵢ ± t₀.₀₂₅,ₙ₋ₚ × σ̂_β̂ᵢ
                    </p>
                </div>
                
                <div class="info-box">
                    <h4>💡 Interpretation</h4>
                    <p>If we repeated the experiment many times, 95% of such intervals would contain the true parameter value.</p>
                </div>
                
                <h3>5.3 Hypothesis Testing</h3>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>Individual Parameters</h4>
                        <p><strong>H₀:</strong> βᵢ = 0</p>
                        <p><strong>Test statistic:</strong> t = β̂ᵢ/σ̂_β̂ᵢ</p>
                        <p><strong>Distribution:</strong> t_{n-p} under H₀</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Multiple Parameters</h4>
                        <p><strong>H₀:</strong> Cβ = d</p>
                        <p><strong>Test statistic:</strong> F-ratio</p>
                        <p><strong>Distribution:</strong> F_{q,n-p} under H₀</p>
                    </div>
                </div>
                
                <h3>5.4 F-ratio Tests</h3>
                <p>For testing H₀: β₁ = 0 (subset of parameters), we use:</p>
                
                <div class="formula-box">
                    $$F = \frac{(\text{RSS}_0 - \text{RSS}_1)/q}{\text{RSS}_1/(n-p)} \sim F_{q,n-p}$$
                    <p>where:</p>
                    <ul style="text-align: left; margin-top: 1rem;">
                        <li>RSS₀: Residual sum of squares for null model</li>
                        <li>RSS₁: Residual sum of squares for full model</li>
                        <li>q: Number of parameters being tested</li>
                    </ul>
                </div>
                
                <div class="code-box">
# R code for Hubble constant estimation
library(gamair)
data(hubble)

# Fit the model
hub.mod <- lm(y ~ x - 1, data=hubble)
summary(hub.mod)

# Check residuals
plot(fitted(hub.mod), residuals(hub.mod))

# Remove outliers and refit
hub.mod1 <- lm(y ~ x - 1, data=hubble[-c(3,15),])

# Calculate age of universe
hubble.const <- coef(hub.mod1)/3.09e19
age <- 1/hubble.const
age/(60^2*24*365)  # Age in years
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Important Note</h4>
                    <p>Don't automatically remove outliers! Always investigate why they occur and whether they provide valuable information about the model's adequacy.</p>
                </div>
            </section>
            
            <!-- Advanced Topics Section -->
            <section id="advanced" class="section">
                <h2>6. Advanced Topics</h2>
                
                <h3>6.1 The Influence (Hat) Matrix</h3>
                <p>The influence matrix A transforms observations into fitted values:</p>
                
                <div class="formula-box">
                    $$\hat{\boldsymbol{\mu}} = \mathbf{A}\mathbf{y} \quad \text{where} \quad \mathbf{A} = \mathbf{Q}_f\mathbf{Q}_f^T = \mathbf{X}(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T$$
                </div>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>🎯 Key Properties</h4>
                        <p><strong>Idempotent:</strong> AA = A</p>
                        <p><strong>Trace:</strong> tr(A) = p</p>
                        <p><strong>Symmetric:</strong> A = A^T</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📊 Practical Use</h4>
                        <p>Diagonal elements measure leverage</p>
                        <p>Used in diagnostics</p>
                        <p>Helps identify influential points</p>
                    </div>
                </div>
                
                <h3>6.2 Residuals and Fitted Values</h3>
                <div class="formula-box">
                    <p><strong>Fitted Values:</strong></p>
                    $$E(\hat{\boldsymbol{\mu}}) = \boldsymbol{\mu}, \quad \text{Var}(\hat{\boldsymbol{\mu}}) = \mathbf{A}\sigma^2$$
                    
                    <p><strong>Residuals:</strong></p>
                    $$\hat{\boldsymbol{\epsilon}} = (\mathbf{I} - \mathbf{A})\mathbf{y}, \quad E(\hat{\boldsymbol{\epsilon}}) = \mathbf{0}, \quad \text{Var}(\hat{\boldsymbol{\epsilon}}) = (\mathbf{I} - \mathbf{A})\sigma^2$$
                </div>
                
                <h3>6.3 Gauss-Markov Theorem</h3>
                <div class="highlight-box">
                    <h4>🏆 Why Least Squares is Optimal</h4>
                    <p><strong>Theorem:</strong> Among all linear unbiased estimators, least squares has minimum variance.</p>
                    <p><strong>Conditions:</strong> E(ε) = 0, Var(ε) = σ²I (no normality required!)</p>
                </div>
                
                <div class="visualization">
                    <h4>Geometry of Least Squares</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- 3D coordinate system -->
                        <line x1="100" y1="300" x2="250" y2="300" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="100" y1="300" x2="100" y2="150" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="100" y1="300" x2="180" y2="350" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Model subspace (plane) -->
                        <path d="M 100 280 L 250 260 L 200 180 L 120 200 Z" fill="#3498db" opacity="0.3" stroke="#3498db"/>
                        
                        <!-- Data point -->
                        <circle cx="180" cy="120" r="6" fill="#e74c3c"/>
                        <text x="190" y="115" font-size="12" fill="#e74c3c" font-weight="bold">y (observed)</text>
                        
                        <!-- Fitted point (projection) -->
                        <circle cx="160" cy="200" r="6" fill="#27ae60"/>
                        <text x="170" y="195" font-size="12" fill="#27ae60" font-weight="bold">ŷ (fitted)</text>
                        
                        <!-- Residual vector -->
                        <line x1="180" y1="120" x2="160" y2="200" stroke="#e74c3c" stroke-width="3"/>
                        <text x="150" y="160" font-size="12" fill="#e74c3c" font-weight="bold">residual</text>
                        
                        <!-- Column space directions -->
                        <line x1="100" y1="300" x2="200" y2="250" stroke="#8e44ad" stroke-width="3"/>
                        <line x1="100" y1="300" x2="150" y2="220" stroke="#f39c12" stroke-width="3" stroke-dasharray="5,5"/>
                        
                        <text x="500" y="80" font-size="14" fill="#2c3e50" font-weight="bold">Geometric Interpretation:</text>
                        <text x="500" y="110" font-size="12" fill="#2c3e50">• 3D space represents all possible responses</text>
                        <text x="500" y="130" font-size="12" fill="#2c3e50">• Model subspace contains all E(y) = Xβ</text>
                        <text x="500" y="150" font-size="12" fill="#2c3e50">• Least squares finds closest point in subspace</text>
                        <text x="500" y="170" font-size="12" fill="#2c3e50">• Residual vector is perpendicular to subspace</text>
                        
                        <!-- Axes labels -->
                        <text x="260" y="310" font-size="12" fill="#2c3e50">Y₁</text>
                        <text x="90" y="140" font-size="12" fill="#2c3e50">Y₂</text>
                        <text x="190" y="365" font-size="12" fill="#2c3e50">Y₃</text>
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">Geometric View of Least Squares Fitting</text>
                    </svg>
                </div>
                
                <h3>6.4 Model Diagnostics</h3>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>📈 Residual Plots</h4>
                        <p>Plot residuals vs fitted values</p>
                        <p>Check for patterns, heteroscedasticity</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📊 Q-Q Plots</h4>
                        <p>Check normality assumption</p>
                        <p>Identify heavy tails, skewness</p>
                    </div>
                    <div class="advantage-card">
                        <h4>🎯 Leverage & Influence</h4>
                        <p>Identify influential observations</p>
                        <p>Use hat matrix diagonal elements</p>
                    </div>
                    <div class="advantage-card">
                        <h4>🔍 Outlier Detection</h4>
                        <p>Standardized residuals</p>
                        <p>Cook's distance</p>
                    </div>
                </div>
                
                <div class="highlight-box">
                    <h3>🌟 Key Takeaways</h3>
                    <ul>
                        <li><strong>Linear models</strong> provide the foundation for most statistical analysis</li>
                        <li><strong>Matrix formulation</strong> enables efficient computation and elegant theory</li>
                        <li><strong>QR decomposition</strong> is numerically stable and computationally efficient</li>
                        <li><strong>Least squares</strong> is optimal under Gauss-Markov conditions</li>
                        <li><strong>Diagnostics</strong> are essential for validating model assumptions</li>
                        <li><strong>The universe is approximately 13 billion years old!</strong> 🌌</li>
                    </ul>
                </div>
            </section>
        </main>
        
        <footer>
            <p>&copy; 2024 Linear Models Tutorial. Based on GAM Chapter 1. Created for educational purposes.</p>
            <p>From Hubble's observations to modern statistical theory - the beauty of mathematical modeling</p>
        </footer>
    </div>
</body>
</html> 