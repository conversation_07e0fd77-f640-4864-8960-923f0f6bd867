<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GAMLSS Tutorial: Generalized Additive Models for Location, Scale and Shape</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        header {
            background: linear-gradient(135deg, #2c3e50 0%, #4a6cf7 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        nav {
            background: #34495e;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #4a6cf7;
        }
        
        main {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            border-left: 5px solid #4a6cf7;
        }
        
        h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #4a6cf7;
            padding-bottom: 0.5rem;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .formula-box {
            background: #fff;
            border: 2px solid #4a6cf7;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .comparison-table th {
            background: #4a6cf7;
            color: white;
            font-weight: 600;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .advantage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .advantage-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #4a6cf7;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .advantage-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .visualization {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .step-box {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            header {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            main {
                padding: 1rem;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>GAMLSS Tutorial</h1>
            <p class="subtitle">Generalized Additive Models for Location, Scale and Shape</p>
            <p>A comprehensive guide to understanding and applying GAMLSS models</p>
        </header>
        
        <nav>
            <div class="nav-links">
                <a href="#introduction">Introduction</a>
                <a href="#mathematical-foundation">Mathematical Foundation</a>
                <a href="#model-components">Model Components</a>
                <a href="#distributions">Distributions</a>
                <a href="#estimation">Estimation</a>
                <a href="#applications">Applications</a>
            </div>
        </nav>
        
        <main>
            <!-- Introduction Section -->
            <section id="introduction" class="section">
                <h2>1. Introduction to GAMLSS</h2>
                
                <div class="highlight-box">
                    <h3>What is GAMLSS?</h3>
                    <p><strong>GAMLSS</strong> (Generalized Additive Models for Location, Scale and Shape) is a highly flexible regression framework that extends traditional generalized linear models (GLMs) and generalized additive models (GAMs) by allowing modeling of <em>all parameters</em> of the response distribution, not just the mean.</p>
                </div>
                
                <h3>Key Innovation</h3>
                <p>Unlike GLMs and GAMs that focus primarily on modeling the mean (location parameter), GAMLSS allows you to model:</p>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>🎯 Location (μ)</h4>
                        <p>The central tendency or mean of the distribution</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📏 Scale (σ)</h4>
                        <p>The variability or spread of the distribution</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📊 Shape (ν, τ)</h4>
                        <p>Skewness and kurtosis parameters that control the distribution's shape</p>
                    </div>
                </div>
                
                <h3>Comparison with Traditional Models</h3>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Model Type</th>
                            <th>Parameters Modeled</th>
                            <th>Distribution Family</th>
                            <th>Flexibility</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>GLM</strong></td>
                            <td>Mean only</td>
                            <td>Exponential family</td>
                            <td>Limited</td>
                        </tr>
                        <tr>
                            <td><strong>GAM</strong></td>
                            <td>Mean only</td>
                            <td>Exponential family</td>
                            <td>Moderate</td>
                        </tr>
                        <tr>
                            <td><strong>GAMLSS</strong></td>
                            <td>All parameters (μ, σ, ν, τ)</td>
                            <td>Very general</td>
                            <td>Very High</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="visualization">
                    <h4>GAMLSS Framework Visualization</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
                            </linearGradient>
                        </defs>
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">GAMLSS Model Structure</text>
                        
                        <!-- Input Variables -->
                        <rect x="50" y="80" width="120" height="60" rx="10" fill="#4a6cf7" opacity="0.8"/>
                        <text x="110" y="100" text-anchor="middle" fill="white" font-weight="bold">Explanatory</text>
                        <text x="110" y="120" text-anchor="middle" fill="white" font-weight="bold">Variables</text>
                        
                        <!-- GAMLSS Box -->
                        <rect x="250" y="60" width="300" height="280" rx="15" fill="none" stroke="#2c3e50" stroke-width="3"/>
                        <text x="400" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">GAMLSS Framework</text>
                        
                        <!-- Parameter boxes -->
                        <rect x="270" y="100" width="80" height="40" rx="5" fill="#e74c3c" opacity="0.8"/>
                        <text x="310" y="125" text-anchor="middle" fill="white" font-weight="bold">μ (Location)</text>
                        
                        <rect x="370" y="100" width="80" height="40" rx="5" fill="#f39c12" opacity="0.8"/>
                        <text x="410" y="125" text-anchor="middle" fill="white" font-weight="bold">σ (Scale)</text>
                        
                        <rect x="270" y="160" width="80" height="40" rx="5" fill="#27ae60" opacity="0.8"/>
                        <text x="310" y="185" text-anchor="middle" fill="white" font-weight="bold">ν (Shape1)</text>
                        
                        <rect x="370" y="160" width="80" height="40" rx="5" fill="#8e44ad" opacity="0.8"/>
                        <text x="410" y="185" text-anchor="middle" fill="white" font-weight="bold">τ (Shape2)</text>
                        
                        <!-- Modeling components -->
                        <text x="400" y="230" text-anchor="middle" font-size="14" fill="#2c3e50">Each parameter can include:</text>
                        <text x="400" y="250" text-anchor="middle" font-size="12" fill="#34495e">• Parametric terms</text>
                        <text x="400" y="265" text-anchor="middle" font-size="12" fill="#34495e">• Smoothing terms</text>
                        <text x="400" y="280" text-anchor="middle" font-size="12" fill="#34495e">• Random effects</text>
                        <text x="400" y="295" text-anchor="middle" font-size="12" fill="#34495e">• Spatial terms</text>
                        
                        <!-- Output -->
                        <rect x="630" y="80" width="120" height="60" rx="10" fill="#2ecc71" opacity="0.8"/>
                        <text x="690" y="100" text-anchor="middle" fill="white" font-weight="bold">Response</text>
                        <text x="690" y="120" text-anchor="middle" fill="white" font-weight="bold">Distribution</text>
                        
                        <!-- Arrows -->
                        <path d="M 170 110 L 240 110" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <path d="M 560 120 L 620 110" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                            </marker>
                        </defs>
                    </svg>
                </div>
            </section>
            
            <!-- Mathematical Foundation Section -->
            <section id="mathematical-foundation" class="section">
                <h2>2. Mathematical Foundation</h2>
                
                <h3>Core Model Definition</h3>
                <p>GAMLSS models the parameters θ = (θ₁, θ₂, ..., θₚ) of a probability density function f(y|θ) using additive models:</p>
                
                <div class="formula-box">
                    <p><strong>General GAMLSS Model:</strong></p>
                    $$g_k(\theta_k) = \eta_k = X_k \beta_k + \sum_{j=1}^{J_k} Z_{jk} \gamma_{jk}$$
                    <p>for k = 1, 2, ..., p</p>
                </div>
                
                <div class="step-box">
                    <span class="step-number">1</span>
                    <strong>Understanding the Components:</strong>
                    <ul style="margin-left: 3rem; margin-top: 0.5rem;">
                        <li><strong>g_k(·)</strong>: Link function for parameter k</li>
                        <li><strong>θ_k</strong>: k-th parameter vector (length n)</li>
                        <li><strong>η_k</strong>: Linear predictor for parameter k</li>
                        <li><strong>X_k β_k</strong>: Parametric component</li>
                        <li><strong>Z_{jk} γ_{jk}</strong>: Additive components (smooth terms, random effects)</li>
                    </ul>
                </div>
                
                <h3>Four-Parameter Model</h3>
                <p>For most practical applications, a four-parameter model suffices:</p>
                
                <div class="formula-box">
                    $$\begin{align}
                    g_1(\mu) &= \eta_1 = X_1 \beta_1 + \sum_{j=1}^{J_1} Z_{j1} \gamma_{j1} \quad \text{(Location)}\\
                    g_2(\sigma) &= \eta_2 = X_2 \beta_2 + \sum_{j=1}^{J_2} Z_{j2} \gamma_{j2} \quad \text{(Scale)}\\
                    g_3(\nu) &= \eta_3 = X_3 \beta_3 + \sum_{j=1}^{J_3} Z_{j3} \gamma_{j3} \quad \text{(Shape 1)}\\
                    g_4(\tau) &= \eta_4 = X_4 \beta_4 + \sum_{j=1}^{J_4} Z_{j4} \gamma_{j4} \quad \text{(Shape 2)}
                    \end{align}$$
                </div>
                
                <h3>Key Assumptions</h3>
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>Independence</h4>
                        <p>Observations y_i are independent conditional on θ_i</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Distributional Flexibility</h4>
                        <p>No restriction to exponential family distributions</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Random Effects</h4>
                        <p>γ_{jk} ~ N(0, G_{jk}^{-1}) with independence between different j,k</p>
                    </div>
                </div>
                
                <div class="visualization">
                    <h4>Parameter Relationships Visualization</h4>
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Background -->
                        <rect width="800" height="300" fill="url(#bg)" />
                        
                        <!-- Distribution curve -->
                        <path d="M 100 200 Q 200 50 300 80 Q 400 110 500 150 Q 600 190 700 200" 
                              stroke="#e74c3c" stroke-width="3" fill="none"/>
                        
                        <!-- Parameter effects -->
                        <circle cx="200" cy="70" r="8" fill="#e74c3c"/>
                        <text x="220" y="75" font-size="12" fill="#e74c3c" font-weight="bold">μ (Location)</text>
                        <line x1="200" y1="70" x2="200" y2="200" stroke="#e74c3c" stroke-dasharray="5,5"/>
                        
                        <circle cx="400" cy="130" r="8" fill="#f39c12"/>
                        <text x="420" y="135" font-size="12" fill="#f39c12" font-weight="bold">σ (Scale)</text>
                        <line x1="300" y1="200" x2="500" y2="200" stroke="#f39c12" stroke-width="2"/>
                        <line x1="300" y1="195" x2="300" y2="205" stroke="#f39c12" stroke-width="2"/>
                        <line x1="500" y1="195" x2="500" y2="205" stroke="#f39c12" stroke-width="2"/>
                        
                        <!-- Shape indicators -->
                        <path d="M 600 150 Q 650 120 700 160" stroke="#27ae60" stroke-width="2" fill="none"/>
                        <text x="620" y="110" font-size="12" fill="#27ae60" font-weight="bold">ν (Skewness)</text>
                        
                        <ellipse cx="400" cy="150" rx="150" ry="30" stroke="#8e44ad" stroke-width="2" fill="none" opacity="0.5"/>
                        <text x="420" y="250" font-size="12" fill="#8e44ad" font-weight="bold">τ (Kurtosis)</text>
                        
                        <!-- Axes -->
                        <line x1="80" y1="200" x2="720" y2="200" stroke="#2c3e50" stroke-width="1"/>
                        <line x1="100" y1="50" x2="100" y2="220" stroke="#2c3e50" stroke-width="1"/>
                        
                        <text x="400" y="280" text-anchor="middle" font-size="14" fill="#2c3e50">Response Variable (y)</text>
                        <text x="50" y="135" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 50 135)">Density</text>
                    </svg>
                </div>
            </section>
            
            <!-- Model Components Section -->
            <section id="model-components" class="section">
                <h2>3. Model Components</h2>
                
                <h3>3.1 Parametric Terms</h3>
                <p>The parametric component X_k β_k can include various types of terms:</p>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>Linear Terms</h4>
                        <p>Direct relationships: β₀ + β₁x₁ + β₂x₂</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Interaction Terms</h4>
                        <p>Product effects: β₃x₁x₂</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Polynomial Terms</h4>
                        <p>Non-linear relationships: β₄x² + β₅x³</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Fractional Polynomials</h4>
                        <p>Flexible curves: β₆x^p + β₇x^q</p>
                    </div>
                </div>
                
                <h3>3.2 Additive Terms (Smoothing)</h3>
                
                <div class="step-box">
                    <span class="step-number">2</span>
                    <strong>Cubic Smoothing Splines:</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        These model unknown smooth functions h(t) by minimizing:
                    </p>
                </div>
                
                <div class="formula-box">
                    $$\text{Penalized likelihood} = l - \lambda \int_{-\infty}^{\infty} h''(t)^2 dt$$
                    <p>where λ controls the smoothness</p>
                </div>
                
                <div class="visualization">
                    <h4>Smoothing Parameter Effect</h4>
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Background -->
                        <rect width="800" height="300" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Effect of Smoothing Parameter λ</text>
                        
                        <!-- Data points -->
                        <circle cx="100" cy="200" r="3" fill="#e74c3c"/>
                        <circle cx="150" cy="120" r="3" fill="#e74c3c"/>
                        <circle cx="200" cy="180" r="3" fill="#e74c3c"/>
                        <circle cx="250" cy="100" r="3" fill="#e74c3c"/>
                        <circle cx="300" cy="160" r="3" fill="#e74c3c"/>
                        <circle cx="350" cy="90" r="3" fill="#e74c3c"/>
                        <circle cx="400" cy="140" r="3" fill="#e74c3c"/>
                        <circle cx="450" cy="110" r="3" fill="#e74c3c"/>
                        <circle cx="500" cy="170" r="3" fill="#e74c3c"/>
                        <circle cx="550" cy="130" r="3" fill="#e74c3c"/>
                        <circle cx="600" cy="190" r="3" fill="#e74c3c"/>
                        <circle cx="650" cy="150" r="3" fill="#e74c3c"/>
                        <circle cx="700" cy="200" r="3" fill="#e74c3c"/>
                        
                        <!-- Low lambda (wiggly) -->
                        <path d="M 100 200 L 150 120 L 200 180 L 250 100 L 300 160 L 350 90 L 400 140 L 450 110 L 500 170 L 550 130 L 600 190 L 650 150 L 700 200" 
                              stroke="#27ae60" stroke-width="2" fill="none"/>
                        <text x="150" y="270" font-size="12" fill="#27ae60" font-weight="bold">λ small (wiggly fit)</text>
                        
                        <!-- High lambda (smooth) -->
                        <path d="M 100 180 Q 300 120 500 140 Q 600 150 700 180" 
                              stroke="#3498db" stroke-width="2" fill="none"/>
                        <text x="450" y="270" font-size="12" fill="#3498db" font-weight="bold">λ large (smooth fit)</text>
                        
                        <!-- Axes -->
                        <line x1="80" y1="220" x2="720" y2="220" stroke="#2c3e50" stroke-width="1"/>
                        <line x1="100" y1="80" x2="100" y2="240" stroke="#2c3e50" stroke-width="1"/>
                        
                        <text x="400" y="290" text-anchor="middle" font-size="12" fill="#2c3e50">Explanatory Variable</text>
                    </svg>
                </div>
                
                <h3>3.3 Random Effects Terms</h3>
                <p>GAMLSS can incorporate various types of random effects:</p>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>Overdispersion</h4>
                        <p>γᵢ ~ N(0, λ⁻¹) for each observation</p>
                    </div>
                    <div class="advantage-card">
                        <h4>One-factor Random Effects</h4>
                        <p>γⱼ ~ N(0, λ⁻¹) for groups</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Longitudinal Effects</h4>
                        <p>Correlated measurements over time</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Spatial Effects</h4>
                        <p>Geographically structured random effects</p>
                    </div>
                </div>
                
                <div class="step-box">
                    <span class="step-number">3</span>
                    <strong>Random Walk Terms:</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        Useful for time series modeling:
                    </p>
                    <ul style="margin-left: 5rem; margin-top: 0.5rem;">
                        <li><strong>RW(1):</strong> h[t] = h[t-1] + εₜ</li>
                        <li><strong>RW(2):</strong> h[t] = 2h[t-1] - h[t-2] + εₜ</li>
                    </ul>
                </div>
            </section>
            
            <!-- Distributions Section -->
            <section id="distributions" class="section">
                <h2>4. Distribution Families</h2>
                
                <div class="highlight-box">
                    <h3>Distribution Flexibility</h3>
                    <p>Unlike GLMs which are restricted to exponential family distributions, GAMLSS can use any distribution for which the density function and its derivatives can be computed.</p>
                </div>
                
                <h3>Available Distribution Categories</h3>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Parameters</th>
                            <th>Examples</th>
                            <th>Use Cases</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Discrete (1 param)</strong></td>
                            <td>1</td>
                            <td>Poisson, Binomial, Geometric</td>
                            <td>Count data, proportions</td>
                        </tr>
                        <tr>
                            <td><strong>Discrete (2+ param)</strong></td>
                            <td>2-3</td>
                            <td>Negative Binomial, Beta-Binomial</td>
                            <td>Overdispersed counts</td>
                        </tr>
                        <tr>
                            <td><strong>Continuous (2 param)</strong></td>
                            <td>2</td>
                            <td>Normal, Gamma, Weibull</td>
                            <td>Standard continuous data</td>
                        </tr>
                        <tr>
                            <td><strong>Continuous (3+ param)</strong></td>
                            <td>3-4</td>
                            <td>Box-Cox Normal, t-family</td>
                            <td>Skewed, heavy-tailed data</td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>Key Distribution Examples</h3>
                
                <div class="step-box">
                    <span class="step-number">4</span>
                    <strong>Box-Cox Normal Distribution:</strong>
                    <p style="margin-left: 3rem; margin-top: 0.5rem;">
                        For positive data y > 0, transforms to standard normal:
                    </p>
                </div>
                
                <div class="formula-box">
                    $$z = \begin{cases}
                    \frac{1}{\sigma\nu}\left[\left(\frac{y}{\mu}\right)^\nu - 1\right] & \text{if } \nu \neq 0 \\
                    \frac{1}{\sigma}\log\left(\frac{y}{\mu}\right) & \text{if } \nu = 0
                    \end{cases}$$
                    <p>where z ~ N(0,1)</p>
                </div>
                
                <div class="visualization">
                    <h4>Distribution Shape Flexibility</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Distribution Shapes in GAMLSS</text>
                        
                        <!-- Normal distribution -->
                        <path d="M 50 300 Q 150 150 250 300" stroke="#3498db" stroke-width="3" fill="none"/>
                        <text x="150" y="340" text-anchor="middle" font-size="12" fill="#3498db" font-weight="bold">Normal</text>
                        <text x="150" y="355" text-anchor="middle" font-size="10" fill="#3498db">μ, σ</text>
                        
                        <!-- Skewed distribution -->
                        <path d="M 300 300 Q 350 100 450 180 Q 500 220 550 300" stroke="#e74c3c" stroke-width="3" fill="none"/>
                        <text x="425" y="340" text-anchor="middle" font-size="12" fill="#e74c3c" font-weight="bold">Skewed</text>
                        <text x="425" y="355" text-anchor="middle" font-size="10" fill="#e74c3c">μ, σ, ν</text>
                        
                        <!-- Heavy-tailed distribution -->
                        <path d="M 580 300 Q 630 80 680 120 Q 720 160 750 300" stroke="#27ae60" stroke-width="3" fill="none"/>
                        <text x="665" y="340" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">Heavy-tailed</text>
                        <text x="665" y="355" text-anchor="middle" font-size="10" fill="#27ae60">μ, σ, ν, τ</text>
                        
                        <!-- Parameter effects -->
                        <text x="400" y="80" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Parameter Effects:</text>
                        <text x="400" y="110" text-anchor="middle" font-size="12" fill="#e74c3c">μ: Location (shifts left/right)</text>
                        <text x="400" y="130" text-anchor="middle" font-size="12" fill="#f39c12">σ: Scale (widens/narrows)</text>
                        <text x="400" y="150" text-anchor="middle" font-size="12" fill="#27ae60">ν: Skewness (asymmetry)</text>
                        <text x="400" y="170" text-anchor="middle" font-size="12" fill="#8e44ad">τ: Kurtosis (tail heaviness)</text>
                        
                        <!-- Baseline -->
                        <line x1="30" y1="300" x2="770" y2="300" stroke="#2c3e50" stroke-width="1" opacity="0.3"/>
                    </svg>
                </div>
                
                <h3>Model Notation</h3>
                <p>GAMLSS models are specified using the notation:</p>
                
                <div class="formula-box">
                    <p><strong>Model Specification:</strong></p>
                    $$y \sim D\{g_1(\theta_1) = t_1, g_2(\theta_2) = t_2, \ldots, g_p(\theta_p) = t_p\}$$
                    <p>Example: y ~ TF{μ = cs(x,3), log(σ) = x, log(ν) = 1}</p>
                    <p><em>t-distribution with cubic spline in μ, linear σ, constant ν</em></p>
                </div>
            </section>
            
            <!-- Estimation Section -->
            <section id="estimation" class="section">
                <h2>5. Estimation Methods</h2>
                
                <h3>5.1 Penalized Likelihood</h3>
                <p>GAMLSS uses penalized maximum likelihood estimation:</p>
                
                <div class="formula-box">
                    $$l_p = l - \frac{1}{2}\sum_{k=1}^p \sum_{j=1}^{J_k} \gamma_{jk}^T G_{jk} \gamma_{jk}$$
                    <p>where l is the log-likelihood and the penalty controls smoothness</p>
                </div>
                
                <div class="step-box">
                    <span class="step-number">5</span>
                    <strong>Two Main Algorithms:</strong>
                    <ul style="margin-left: 3rem; margin-top: 0.5rem;">
                        <li><strong>RS Algorithm:</strong> Based on Rigby-Stasinopoulos approach</li>
                        <li><strong>CG Algorithm:</strong> Based on Cole-Green method</li>
                    </ul>
                </div>
                
                <h3>5.2 Backfitting Algorithm</h3>
                <p>The additive terms are fitted using a backfitting procedure:</p>
                
                <div class="visualization">
                    <h4>Backfitting Process</h4>
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Background -->
                        <rect width="800" height="300" fill="url(#bg)" />
                        
                        <!-- Process flow -->
                        <rect x="50" y="80" width="120" height="40" rx="5" fill="#3498db" opacity="0.8"/>
                        <text x="110" y="105" text-anchor="middle" fill="white" font-weight="bold">Initialize</text>
                        
                        <rect x="220" y="80" width="120" height="40" rx="5" fill="#e74c3c" opacity="0.8"/>
                        <text x="280" y="105" text-anchor="middle" fill="white" font-weight="bold">Update μ</text>
                        
                        <rect x="390" y="80" width="120" height="40" rx="5" fill="#f39c12" opacity="0.8"/>
                        <text x="450" y="105" text-anchor="middle" fill="white" font-weight="bold">Update σ</text>
                        
                        <rect x="560" y="80" width="120" height="40" rx="5" fill="#27ae60" opacity="0.8"/>
                        <text x="620" y="105" text-anchor="middle" fill="white" font-weight="bold">Update ν, τ</text>
                        
                        <rect x="340" y="180" width="120" height="40" rx="5" fill="#8e44ad" opacity="0.8"/>
                        <text x="400" y="200" text-anchor="middle" fill="white" font-weight="bold">Check</text>
                        <text x="400" y="215" text-anchor="middle" fill="white" font-weight="bold">Convergence</text>
                        
                        <!-- Arrows -->
                        <path d="M 170 100 L 210 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <path d="M 340 100 L 380 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <path d="M 510 100 L 550 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <path d="M 620 120 L 620 140 L 400 140 L 400 170" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <path d="M 340 200 L 280 200 L 280 140 L 280 120" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        
                        <text x="650" y="260" font-size="12" fill="#2c3e50">Iterate until convergence</text>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>5.3 Smoothing Matrix</h3>
                <p>The key to the algorithm is the smoothing matrix:</p>
                
                <div class="formula-box">
                    $$S_{jk} = Z_{jk}(Z_{jk}^T W_{kk} Z_{jk} + G_{jk})^{-1} Z_{jk}^T W_{kk}$$
                    <p>Applied to partial residuals to update estimates</p>
                </div>
            </section>
            
            <!-- Applications Section -->
            <section id="applications" class="section">
                <h2>6. Practical Applications</h2>
                
                <h3>6.1 When to Use GAMLSS</h3>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>🔄 Heteroscedasticity</h4>
                        <p>When variance changes with predictors</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📈 Skewed Data</h4>
                        <p>When data shows asymmetry that varies</p>
                    </div>
                    <div class="advantage-card">
                        <h4>📊 Heavy Tails</h4>
                        <p>When kurtosis varies across conditions</p>
                    </div>
                    <div class="advantage-card">
                        <h4>🎯 Centile Modeling</h4>
                        <p>Growth charts, reference curves</p>
                    </div>
                </div>
                
                <h3>6.2 Model Selection Process</h3>
                
                <div class="step-box">
                    <span class="step-number">6</span>
                    <strong>Step-by-Step Approach:</strong>
                    <ol style="margin-left: 3rem; margin-top: 0.5rem;">
                        <li>Start with simple parametric model for μ</li>
                        <li>Add smoothing terms if needed</li>
                        <li>Model σ if residual plots show heteroscedasticity</li>
                        <li>Add shape parameters (ν, τ) if skewness/kurtosis present</li>
                        <li>Use AIC/BIC for model comparison</li>
                    </ol>
                </div>
                
                <h3>6.3 Model Diagnostics</h3>
                
                <div class="visualization">
                    <h4>Diagnostic Tools</h4>
                    <svg width="800" height="200" viewBox="0 0 800 200">
                        <!-- Background -->
                        <rect width="800" height="200" fill="url(#bg)" />
                        
                        <!-- Diagnostic boxes -->
                        <rect x="50" y="50" width="150" height="100" rx="10" fill="#3498db" opacity="0.8"/>
                        <text x="125" y="80" text-anchor="middle" fill="white" font-weight="bold">Quantile</text>
                        <text x="125" y="100" text-anchor="middle" fill="white" font-weight="bold">Residuals</text>
                        <text x="125" y="120" text-anchor="middle" fill="white" font-size="10">Should be U(0,1)</text>
                        
                        <rect x="250" y="50" width="150" height="100" rx="10" fill="#e74c3c" opacity="0.8"/>
                        <text x="325" y="80" text-anchor="middle" fill="white" font-weight="bold">Worm Plots</text>
                        <text x="325" y="100" text-anchor="middle" fill="white" font-size="10">Detrended Q-Q</text>
                        <text x="325" y="120" text-anchor="middle" fill="white" font-size="10">Should be flat</text>
                        
                        <rect x="450" y="50" width="150" height="100" rx="10" fill="#27ae60" opacity="0.8"/>
                        <text x="525" y="80" text-anchor="middle" fill="white" font-weight="bold">Global</text>
                        <text x="525" y="100" text-anchor="middle" fill="white" font-weight="bold">Deviance</text>
                        <text x="525" y="120" text-anchor="middle" fill="white" font-size="10">Model fit measure</text>
                        
                        <rect x="650" y="50" width="120" height="100" rx="10" fill="#8e44ad" opacity="0.8"/>
                        <text x="710" y="85" text-anchor="middle" fill="white" font-weight="bold">AIC/BIC</text>
                        <text x="710" y="115" text-anchor="middle" fill="white" font-size="10">Model selection</text>
                    </svg>
                </div>
                
                <h3>6.4 Real-world Applications</h3>
                
                <div class="advantage-grid">
                    <div class="advantage-card">
                        <h4>Growth Charts</h4>
                        <p>WHO child growth standards using Box-Cox normal distribution</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Environmental Data</h4>
                        <p>Rainfall, pollution modeling with skewed distributions</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Financial Risk</h4>
                        <p>Heavy-tailed distributions for extreme value modeling</p>
                    </div>
                    <div class="advantage-card">
                        <h4>Medical Studies</h4>
                        <p>Biomarker distributions varying with patient characteristics</p>
                    </div>
                </div>
                
                <div class="highlight-box">
                    <h3>Key Takeaways</h3>
                    <ul>
                        <li><strong>Flexibility:</strong> Model all distribution parameters, not just the mean</li>
                        <li><strong>Distributions:</strong> Wide range beyond exponential family</li>
                        <li><strong>Smoothing:</strong> Incorporate non-linear relationships easily</li>
                        <li><strong>Random Effects:</strong> Handle complex data structures</li>
                        <li><strong>Diagnostics:</strong> Comprehensive tools for model checking</li>
                    </ul>
                </div>
            </section>
        </main>
        
        <footer>
            <p>&copy; 2024 GAMLSS Tutorial. Based on Rigby & Stasinopoulos (2005). Created for educational purposes.</p>
            <p>For more information, visit the <a href="https://www.gamlss.com/" style="color: #4a6cf7;">official GAMLSS website</a></p>
        </footer>
    </div>
</body>
</html> 