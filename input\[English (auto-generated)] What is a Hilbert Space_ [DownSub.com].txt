Hilbert spaces. If you're not sure what
they are, you're in good company. As the
legend goes, the great mathematician
<PERSON> once entered a lecture
hall. The speaker that day was fellow
math giant <PERSON>. The topic,
Hilbert spaces. At the end of the
lecture, a confused <PERSON><PERSON> raised his
hand to ask just one question. What is a
Hilbert space? I love this tale. It
would be like if <PERSON><PERSON><PERSON><PERSON><PERSON> asked about
the Pythagorean theorem. While the story
is likely just a legend, it amusingly
captures the importance that both of
these figures played in the development
of Hilbert spaces with Hi<PERSON> laying
much of the groundwork and <PERSON><PERSON><PERSON>
finishing it off by giving the first
complete definition of them. These
spaces then quickly became useful in
areas like quantum mechanics, the
physics of heat transfer, and even
modeling sound waves. Admittedly, when I
first encountered Hilbert spaces, they
were a bit intimidating, but here you
will see that they are really just a
natural generalization of the spaces we
encounter in high school
geometry. We begin with a humble vector,
an arrow in the two-dimensional plane.
By choosing a specific coordinate
system, we can specify the location of
this vector with just two numbers. how
many steps we need to take in the x
direction plus how many steps in the y
direction. Although this is generally
the standard coordinate system we use,
there's nothing that prevents us from
using another one. We can just as easily
use any other two vectors to form our
coordinate system as long as they have a
90° angle between them. In the language
of linear algebra, we would say that
these two vectors are orthogonal to each
other. And since any other vector in the
plane can be expressed as some
combination of these two, then they form
an orthogonal basis. If these basis
vectors also both had a length of one,
then they would be an orthonormal basis.
Something we can also do with vectors in
the 2D plane is to form a dotproduct,
which involves multiplying the
components of each vector and then
adding them up.
We can do the exact same thing in three
dimensions by just taking another
component into account. Frequently, it's
x, y, and z. But again, we can use any
three perpendicular vectors as a
basis. In fact, not only do these
concepts naturally carry over to
dimension 3, but they can easily be
applied to any finite dimension.
What a hbert space is then is the
extension of these concepts to an
infinite number of
dimensions. And instead of just ordinary
vectors that are arrows in space, the
vectors in a Hilbert space will also
include abstract mathematical vectors.
So they are things that live in a vector
space, which is just a well-defined
collection of objects that satisfy
certain rules. The main ones being that
you can scale vectors and add them
together without leaving the space. So
the set of all row vectors form a vector
space as does the set of all column
vectors. More interestingly, the
collection of all n byn matrices and
even of all functions form a vector
space. Now before moving on, as things
begin to get a bit more abstract, I'd
like to emphasize that you should really
always be keeping the two-dimensional
case of arrows in space in the back of
your mind. That's the intuitive picture.
Everything else will just be a way to
codify the same type of behavior in a
more abstract general
[Music]
setting. In essence, then a Hilbert
space is a vector space with two extra
requirements. The first we'll cover is
that a Hilbert space is complete. In
order to understand what this means, I
think it's very helpful to consider
polomial functions. That is any function
that can be written in the following
way.
The space of all functions that can be
written like this itself forms a vector
space where a very natural basis to
choose is the set of all powers of x. So
any function living in the space can be
written as some linear combination of
these powers. Let's now consider one
very specific linear
combination. As we continue adding
terms, the function should start to look
more and more familiar to you. And in
the limit of an infinite number of
terms, this approaches the tailaylor
series for sin of
x. We can take another linear
combination to arrive at cosine of
x and yet another to arrive at the
exponential
function. But here's the thing, none of
these are actually polomials.
Any polomial has only a finite number of
powers. It can be really large but it
must have some cutoff. By letting n go
to infinity in all three of these cases
we have ended up with something outside
of our vector
space. How did this happen? We were just
combining a bunch of polomials together
and then all of a sudden a whole
different type of function appeared.
Well, something similar happens in a
setting you might find more familiar.
Consider the following sequence of
numbers. Each particular number is
rational, but as the limit goes to
infinity, the sequence converges to pi,
an irrational number. In fact, this
phenomenon occurs in all sorts of places
in math. And mathematicians have found a
way to deal with this general problem.
What's needed is to realize that in all
these cases, the space that we're
working in is actually part of a larger
space. One that in a sense completes the
smaller one. So in this case, the real
numbers are the completion of the
rationals. In the case of polomials, its
completion is a space of all continuous
functions. The technical term that's
used is to call these types of sequences
Koshy sequences.
And if there exists a Koshy sequence
converging to something that is beyond
the space, then the space is incomplete.
But if you extend your space to include
these limits and now every Koshy
sequence has a limit that exists inside
of it, you get a complete space. So a
Hbert space is a vector space that is
complete. This naturally brings us to
the second requirement for a Hilbert
space, namely that it needs to have
something called an inner product
defined on it. A basic vector space has
enough of a structure that allows for
adding vectors and scaling
them. But there's no notion of distance
nor angles between vectors. This is a
problem since in order for the Hilbert
space to be complete, we need to find
Koshy sequences and determine where they
converge to a process which requires
having some concept of angles and
distance at hand.
In order to be able to measure these
quantities, we need to add some
additional structure to the vector
space. An inner product, which is a
function that satisfies these criteria
for any vectors U, V, W in the vector
space and numbers A and B that you're
scaling vectors with. Essentially, this
is just a generalized dotproduct. It is
a way to capture the lengths of vectors
as well as the angles between them for a
general vector space. In fact, in the
case of the familiar 2D plane or 3D
space, the regular dot productduct just
is an inner product. So, a hilbert space
is a vector space that is complete and
has an inner product. Or to put it even
more concisely, it is an inner product
space that is
complete. What are some examples of
Hilbert
spaces? One example is just the
straightforward generalization of
ukitian space I mentioned earlier.
It's the set of all sequences where this
infinite sum
converges. The inner product between any
two vectors is just the dotproduct but
with an infinite number of
terms. And from an inequality that's
valid on all inner product spaces. This
guarantees inner products between any
two vectors will converge.
Another very important example is the
set of all functions defined on the
interval 0 to 2 pi where this integral
is finite. This requirement guarantees
that the following inner product is well
defined for any two vectors in this
space. And if you focus on these trick
functions, the inner product between any
two different s and cosine functions is
zero, which means they are all
orthogonal to each other. Amazingly they
form an orthonormal basis for the entire
space. So they can act as a sort of
coordinate system here and any function
in the space can be written as a linear
combination of them leading directly to
the forier approximation of
functions. Yet another extremely
important example is Schroinger's wave
function in quantum mechanics. The wave
function is what describes the state of
a physical system and it lives in a
related Hilbert space this time defined
over all real
numbers. Interestingly, if this is the
run-of-the-mill remon integral that most
of us are more familiar with, then there
will be sequences that converge to
something outside of the space. In order
for this space to be complete and thus
actually be a Hilbert space, the
integral here needs to be the more
versatile LEG integral.
A huge thank you goes out to this video
sponsor,
brilliant.org. I've been studying math
and physics for years. Along the way,
I've learned that the best way to
solidify my understanding is by doing
tons of practice problems. With
Brilliant, you'll never struggle to find
engaging problems to work through again.
Packed with thousands of lessons spread
over hundreds of courses, each lesson
provides opportunities to interactively
play with problems, allowing you to test
your knowledge as you go. Not only will
you be able to finally develop an
intuitive grasp for calculus concepts,
but Brilliant gives you the opportunity
to explore topics like quantum
mechanics, machine learning, and even
mathematical logic. If you'd like to try
everything Brilliant has to offer for
free for a full 30 days, visit
brilliant.org/abidebyreason.
org/abidebyreason or click on the link
in the description.