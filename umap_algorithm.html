<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding UMAP: Uniform Manifold Approximation and Projection</title>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        h1 {
            font-size: 2.5em;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            font-size: 1.8em;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        h3 {
            font-size: 1.4em;
            color: #2980b9;
        }
        h4 {
            font-size: 1.2em;
            color: #3498db;
        }
        p {
            margin-bottom: 1.2em;
            text-align: justify;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }
        pre {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', Courier, monospace;
        }
        .note {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 10px 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #fcf3cf;
            border-left: 4px solid #f1c40f;
            padding: 10px 15px;
            margin: 20px 0;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .toc h3 {
            margin-top: 0;
        }
        .toc ul {
            padding-left: 20px;
        }
        .algorithm {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', Courier, monospace;
        }
        .equation {
            text-align: center;
            margin: 20px 0;
        }
        .figure {
            text-align: center;
            margin: 20px 0;
        }
        .figure img {
            max-width: 80%;
        }
        .figure-caption {
            font-style: italic;
            text-align: center;
            margin-top: 10px;
        }
        .highlight {
            background-color: #ffffcc;
            padding: 2px;
        }
    </style>
</head>
<body>
    <h1>Understanding UMAP: Uniform Manifold Approximation and Projection</h1>
    
    <div class="note">
        <strong>Note:</strong> This document provides a comprehensive step-by-step explanation of UMAP (Uniform Manifold Approximation and Projection), a dimension reduction technique developed by McInnes, Healy, and Melville. UMAP combines strong theoretical foundations with practical computational efficiency to create visualizations that preserve both local and global structure.
    </div>

    <div class="toc">
        <h3>Table of Contents</h3>
        <ul>
            <li><a href="#introduction">1. Introduction to Dimension Reduction</a></li>
            <li><a href="#foundations">2. Mathematical Foundations of UMAP</a>
                <ul>
                    <li><a href="#manifold">2.1 Manifold Theory and Uniform Data Distribution</a></li>
                    <li><a href="#riemannian">2.2 Riemannian Metrics and Local Distance</a></li>
                    <li><a href="#topology">2.3 Topological Representations</a></li>
                    <li><a href="#fuzzy">2.4 Fuzzy Simplicial Sets</a></li>
                </ul>
            </li>
            <li><a href="#algorithm">3. The UMAP Algorithm</a>
                <ul>
                    <li><a href="#graph-construction">3.1 Graph Construction Phase</a></li>
                    <li><a href="#layout">3.2 Graph Layout Phase</a></li>
                    <li><a href="#optimization">3.3 Optimization Process</a></li>
                </ul>
            </li>
            <li><a href="#implementation">4. Implementation Details</a>
                <ul>
                    <li><a href="#parameters">4.1 Hyperparameters and Their Effects</a></li>
                    <li><a href="#computational">4.2 Computational Optimizations</a></li>
                </ul>
            </li>
            <li><a href="#comparison">5. Comparison with Other Methods</a>
                <ul>
                    <li><a href="#tsne">5.1 UMAP vs. t-SNE</a></li>
                    <li><a href="#largevis">5.2 UMAP vs. LargeVis</a></li>
                    <li><a href="#pca">5.3 UMAP vs. PCA and Laplacian Eigenmaps</a></li>
                </ul>
            </li>
            <li><a href="#practical">6. Practical Considerations</a>
                <ul>
                    <li><a href="#strengths">6.1 Strengths of UMAP</a></li>
                    <li><a href="#weaknesses">6.2 Limitations and Weaknesses</a></li>
                    <li><a href="#when">6.3 When to Use UMAP</a></li>
                </ul>
            </li>
            <li><a href="#conclusion">7. Conclusion and Future Directions</a></li>
        </ul>
    </div>

    <section id="introduction">
        <h2>1. Introduction to Dimension Reduction</h2>
        <p>Dimension reduction plays a crucial role in data science, serving as a fundamental technique for both visualization and preprocessing in machine learning workflows. As datasets grow in size and complexity, the need for algorithms that can effectively handle massive, high-dimensional data becomes increasingly important.</p>
        
        <p>Dimension reduction techniques typically fall into two categories:</p>
        <ul>
            <li><strong>Global structure preservation methods</strong> - These aim to preserve pairwise distance structures among all data samples (e.g., PCA, MDS, Sammon mapping)</li>
            <li><strong>Local structure preservation methods</strong> - These prioritize preserving local distances over global ones (e.g., t-SNE, Isomap, LargeVis, Laplacian eigenmaps)</li>
        </ul>
        
        <p>UMAP (Uniform Manifold Approximation and Projection) represents a significant advancement in dimension reduction, offering a technique that:</p>
        <ul>
            <li>Has strong mathematical foundations in Riemannian geometry and algebraic topology</li>
            <li>Preserves both local and global structure better than many existing methods</li>
            <li>Scales efficiently to large datasets</li>
            <li>Has no computational restrictions on embedding dimension</li>
        </ul>
        
        <p>Since its introduction, UMAP has found widespread use across various fields including bioinformatics, materials science, and machine learning. This document will explore the theoretical foundations, algorithm implementation, and practical applications of UMAP in a step-by-step manner.</p>
    </section>


<section id="foundations">
        <h2>2. Mathematical Foundations of UMAP</h2>
        <p>UMAP's effectiveness stems from its strong mathematical foundations in manifold theory, Riemannian geometry, and topological data analysis. This section explores these foundations step by step to provide insight into why UMAP works so well.</p>

        <section id="manifold">
            <h3>2.1 Manifold Theory and Uniform Data Distribution</h3>
            <p>The core assumption underlying UMAP (and many other dimension reduction techniques) is that high-dimensional data lies on or near a lower-dimensional manifold embedded in the high-dimensional space. This is often referred to as the <strong>manifold hypothesis</strong>.</p>
            
            <div class="figure">
                <svg width="500" height="300" viewBox="0 0 500 300">
                    <!-- Background -->
                    <rect width="500" height="300" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- High-dimensional space representation -->
                    <rect x="50" y="50" width="400" height="200" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1" stroke-dasharray="5,5"/>
                    <text x="250" y="30" text-anchor="middle" font-size="14">High-dimensional Space</text>
                    
                    <!-- Manifold -->
                    <path d="M100,150 C150,100 200,200 250,150 C300,100 350,200 400,150" stroke="#e74c3c" stroke-width="3" fill="none"/>
                    <text x="250" y="270" text-anchor="middle" font-size="14">Lower-dimensional Manifold</text>
                    
                    <!-- Data points -->
                    <circle cx="110" cy="145" r="4" fill="#3498db"/>
                    <circle cx="150" cy="120" r="4" fill="#3498db"/>
                    <circle cx="190" cy="170" r="4" fill="#3498db"/>
                    <circle cx="230" cy="155" r="4" fill="#3498db"/>
                    <circle cx="270" cy="140" r="4" fill="#3498db"/>
                    <circle cx="310" cy="130" r="4" fill="#3498db"/>
                    <circle cx="350" cy="170" r="4" fill="#3498db"/>
                    <circle cx="390" cy="150" r="4" fill="#3498db"/>
                </svg>
                <div class="figure-caption">Figure 1: Illustration of the manifold hypothesis - high-dimensional data points (blue) lie on or near a lower-dimensional manifold (red curve).</div>
            </div>
            
            <p>UMAP introduces an important refinement to this assumption: it assumes that data is <strong>uniformly distributed</strong> on the manifold. This is a key insight that allows UMAP to better capture both local and global structure.</p>
            
            <p>However, real-world data is rarely uniformly distributed on a manifold. To address this, UMAP employs a clever approach by assuming that the manifold has a <strong>Riemannian metric</strong> that makes the data appear uniform with respect to that metric.</p>
            
            <div class="note">
                <p>The assumption of uniform distribution on the manifold is crucial because it allows UMAP to construct a consistent representation of the data's topology. By working with a Riemannian metric that makes the data appear uniform, UMAP can effectively capture the intrinsic structure of the data.</p>
            </div>
        </section>

        <section id="riemannian">
            <h3>2.2 Riemannian Metrics and Local Distance</h3>
            <p>A central challenge in manifold learning is how to approximate geodesic distances on the manifold. UMAP addresses this through a key lemma:</p>
            
            <div class="equation">
                <p>\[ \text{Lemma 1. Let } (M, g) \text{ be a Riemannian manifold in an ambient } \mathbb{R}^n, \text{ and let } p \in M \text{ be a point. If } g \text{ is locally constant about } p \text{ in an open neighborhood } U \text{ such that } g \text{ is a constant diagonal matrix in ambient coordinates, then in a ball } B \subseteq U \text{ centered at } p \text{ with volume } \frac{\pi^{n/2}}{\Gamma(n/2 + 1)} \text{ with respect to } g, \text{ the geodesic distance from } p \text{ to any point } q \in B \text{ is } \frac{1}{r} d_{\mathbb{R}^n}(p,q), \text{ where } r \text{ is the radius of the ball in the ambient space and } d_{\mathbb{R}^n} \text{ is the existing metric on the ambient space.} \]</p>
            </div>
            
            <p>This lemma suggests that we can approximate geodesic distances from a point to its neighbors by normalizing distances with respect to the distance to the k<sup>th</sup> nearest neighbor of that point.</p>
            
            <p>In practical terms, this means:</p>
            <ol>
                <li>For each data point \(x_i\), find its k nearest neighbors</li>
                <li>Compute the distance \(\rho_i\) to the nearest neighbor</li>
                <li>Find a normalization factor \(\sigma_i\) that ensures a consistent distribution of neighbors</li>
                <li>Use these to define a local metric that approximates geodesic distance</li>
            </ol>
            
            <p>This approach creates a family of local metrics, one for each data point. These metrics may not be compatible with each other, which leads to the need for a topological representation that can combine them.</p>
        </section>

        <section id="topology">
            <h3>2.3 Topological Representations</h3>
            <p>To handle the family of incompatible local metrics, UMAP turns to topological data analysis, specifically using the language of <strong>simplicial sets</strong>.</p>
            
            <h4>Simplices and Simplicial Sets</h4>
            <p>A simplex is a generalization of the notion of a triangle or tetrahedron to arbitrary dimensions:</p>
            <ul>
                <li>A 0-simplex is a point</li>
                <li>A 1-simplex is a line segment</li>
                <li>A 2-simplex is a triangle</li>
                <li>A 3-simplex is a tetrahedron</li>
                <li>And so on for higher dimensions</li>
            </ul>
            
            <div class="figure">
                <svg width="600" height="200" viewBox="0 0 600 200">
                    <!-- 0-simplex -->
                    <circle cx="100" cy="100" r="5" fill="#3498db"/>
                    <text x="100" y="130" text-anchor="middle" font-size="14">0-simplex</text>
                    <text x="100" y="150" text-anchor="middle" font-size="12">(Point)</text>
                    
                    <!-- 1-simplex -->
                    <line x1="200" y1="120" x2="250" y2="80" stroke="#3498db" stroke-width="2"/>
                    <circle cx="200" cy="120" r="5" fill="#3498db"/>
                    <circle cx="250" cy="80" r="5" fill="#3498db"/>
                    <text x="225" y="130" text-anchor="middle" font-size="14">1-simplex</text>
                    <text x="225" y="150" text-anchor="middle" font-size="12">(Line Segment)</text>
                    
                    <!-- 2-simplex -->
                    <polygon points="320,120 370,120 345,70" fill="#3498db" fill-opacity="0.2" stroke="#3498db" stroke-width="2"/>
                    <circle cx="320" cy="120" r="5" fill="#3498db"/>
                    <circle cx="370" cy="120" r="5" fill="#3498db"/>
                    <circle cx="345" cy="70" r="5" fill="#3498db"/>
                    <text x="345" y="130" text-anchor="middle" font-size="14">2-simplex</text>
                    <text x="345" y="150" text-anchor="middle" font-size="12">(Triangle)</text>
                    
                    <!-- 3-simplex -->
                    <polygon points="470,120 440,120 455,70 490,90" fill="#3498db" fill-opacity="0.2" stroke="#3498db" stroke-width="2"/>
                    <polygon points="470,120 455,70 490,90" fill="#3498db" fill-opacity="0.4" stroke="#3498db" stroke-width="2"/>
                    <circle cx="440" cy="120" r="5" fill="#3498db"/>
                    <circle cx="470" cy="120" r="5" fill="#3498db"/>
                    <circle cx="455" cy="70" r="5" fill="#3498db"/>
                    <circle cx="490" cy="90" r="5" fill="#3498db"/>
                    <text x="465" y="130" text-anchor="middle" font-size="14">3-simplex</text>
                    <text x="465" y="150" text-anchor="middle" font-size="12">(Tetrahedron)</text>
                </svg>
                <div class="figure-caption">Figure 2: Simplices of different dimensions.</div>
            </div>
            
            <p>A <strong>simplicial set</strong> is a way to represent topological spaces using simplices. Formally, it's defined as a contravariant functor from the simplex category to the category of sets.</p>
            
            <p>UMAP uses simplicial sets to represent the topological structure of the data. For each local neighborhood of a data point, it constructs a simplicial set that captures the local structure. These local simplicial sets are then combined to form a global representation.</p>
            
            <div class="note">
                <p>The key insight here is that while combining incompatible metric spaces is challenging, combining simplicial sets (which capture topological structure) is much more straightforward. This allows UMAP to create a unified representation of the data's topology.</p>
            </div>
        </section>

        <section id="fuzzy">
            <h3>2.4 Fuzzy Simplicial Sets</h3>
            <p>To handle real-world data with noise and uncertainty, UMAP extends the concept of simplicial sets to <strong>fuzzy simplicial sets</strong>. In a fuzzy simplicial set, each simplex has a membership strength between 0 and 1, indicating the degree to which it belongs to the set.</p>
            
            <p>The fuzzy simplicial set is constructed as follows:</p>
            <ol>
                <li>For each data point, compute the local metric based on its k nearest neighbors</li>
                <li>Convert this local metric space into a fuzzy simplicial set using the fuzzy singular set functor</li>
                <li>Combine all the local fuzzy simplicial sets using a fuzzy union operation</li>
            </ol>
            
            <p>Mathematically, the membership strength of a 1-simplex (edge) between points \(x_i\) and \(x_j\) is given by:</p>
            
            <div class="equation">
                <p>\[ v_{j|i} = \exp\left(-\frac{\max(0, d(x_i, x_j) - \rho_i)}{\sigma_i}\right) \]</p>
            </div>
            
            <p>Where:</p>
            <ul>
                <li>\(d(x_i, x_j)\) is the distance between \(x_i\) and \(x_j\)</li>
                <li>\(\rho_i\) is the distance to the nearest neighbor of \(x_i\)</li>
                <li>\(\sigma_i\) is a normalization factor chosen to ensure a consistent distribution of neighbors</li>
            </ul>
            
            <p>The fuzzy union of these local representations is computed using the probabilistic t-conorm:</p>
            
            <div class="equation">
                <p>\[ v_{ij} = v_{j|i} + v_{i|j} - v_{j|i}v_{i|j} \]</p>
            </div>
            
            <p>This creates a single fuzzy simplicial set that represents the global topological structure of the data.</p>
            
            <div class="note">
                <p>The use of fuzzy simplicial sets allows UMAP to handle noise and uncertainty in the data, making it more robust than methods that use hard thresholds or binary relationships.</p>
            </div>
        </section>
    </section> 
        <!-- This is just the outline. The rest of the sections will be filled in incrementally. -->
        <section id="algorithm">
            <h2>3. The UMAP Algorithm</h2>
            <p>With the mathematical foundations established, we can now describe the UMAP algorithm in a more computational manner. UMAP operates in two main phases: graph construction and graph layout optimization.</p>
    
            <section id="graph-construction">
                <h3>3.1 Graph Construction Phase</h3>
                <p>The first phase of UMAP involves constructing a weighted k-nearest neighbor graph that represents the fuzzy topological structure of the data.</p>
                
                <div class="algorithm">
                    <h4>Algorithm 1: UMAP Graph Construction</h4>
                    <pre>
    Input: Dataset X = {x₁, x₂, ..., xₙ}, number of neighbors k
    Output: Weighted graph G representing the fuzzy topological structure
    
    1. For each point xᵢ in X:
       a. Find the k nearest neighbors of xᵢ: {xᵢ₁, xᵢ₂, ..., xᵢₖ}
       b. Compute ρᵢ = min{d(xᵢ, xᵢⱼ) | 1 ≤ j ≤ k, d(xᵢ, xᵢⱼ) > 0}
       c. Find σᵢ such that Σⱼ₌₁ᵏ exp(-(max(0, d(xᵢ, xᵢⱼ) - ρᵢ))/σᵢ) = log₂(k)
       d. For each neighbor xᵢⱼ:
          i. Set vⱼ|ᵢ = exp(-(max(0, d(xᵢ, xᵢⱼ) - ρᵢ))/σᵢ)
    
    2. Create a directed graph with:
       a. Vertices = X
       b. Edges = {(xᵢ, xᵢⱼ) | 1 ≤ j ≤ k, 1 ≤ i ≤ n}
       c. Edge weights w((xᵢ, xᵢⱼ)) = vⱼ|ᵢ
    
    3. Convert to an undirected graph by symmetrizing the edge weights:
       a. For each pair of vertices (xᵢ, xⱼ) with at least one directed edge:
          i. Set vᵢⱼ = vⱼ|ᵢ + vᵢ|ⱼ - vⱼ|ᵢ·vᵢ|ⱼ
    
    4. Return the undirected weighted graph G
                    </pre>
                </div>
                
                <p>Let's break down the key steps:</p>
                
                <h4>Finding Nearest Neighbors</h4>
                <p>For each data point, UMAP finds its k nearest neighbors using a distance metric (typically Euclidean distance, but any valid metric can be used). This can be done efficiently using approximate nearest neighbor algorithms like Nearest-Neighbor-Descent.</p>
                
                <h4>Computing Local Metrics</h4>
                <p>For each point \(x_i\), UMAP computes:</p>
                <ul>
                    <li>\(\rho_i\): The distance to the nearest neighbor (ensuring local connectivity)</li>
                    <li>\(\sigma_i\): A normalization factor that ensures the sum of connection weights to the k nearest neighbors equals \(\log_2(k)\)</li>
                </ul>
                
                <p>These parameters define the local metric around each point, approximating the geodesic distance on the manifold.</p>
                
                <h4>Creating the Fuzzy Topological Representation</h4>
                <p>The membership strength of an edge between \(x_i\) and \(x_j\) is computed as:</p>
                
                <div class="equation">
                    <p>\[ v_{j|i} = \exp\left(-\frac{\max(0, d(x_i, x_j) - \rho_i)}{\sigma_i}\right) \]</p>
                </div>
                
                <p>This creates a directed graph where the weight of an edge represents the membership strength of the corresponding 1-simplex in the fuzzy simplicial set.</p>
                
                <h4>Symmetrizing the Graph</h4>
                <p>The directed graph is converted to an undirected graph using the fuzzy set union operation (probabilistic t-conorm):</p>
                
                <div class="equation">
                    <p>\[ v_{ij} = v_{j|i} + v_{i|j} - v_{j|i}v_{i|j} \]</p>
                </div>
                
                <p>This creates a single undirected weighted graph that represents the fuzzy topological structure of the data.</p>
                
                <div class="figure">
                    <svg width="600" height="400" viewBox="0 0 600 400">
                        <!-- Background -->
                        <rect width="600" height="400" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                        
                        <!-- Data points -->
                        <circle cx="100" cy="100" r="6" fill="#e74c3c"/>
                        <circle cx="150" cy="80" r="6" fill="#3498db"/>
                        <circle cx="180" cy="130" r="6" fill="#3498db"/>
                        <circle cx="220" cy="90" r="6" fill="#3498db"/>
                        <circle cx="70" cy="150" r="6" fill="#3498db"/>
                        <circle cx="130" cy="170" r="6" fill="#3498db"/>
                        
                        <!-- Directed edges -->
                        <line x1="100" y1="100" x2="150" y2="80" stroke="#e74c3c" stroke-width="2" stroke-opacity="0.7"/>
                        <line x1="100" y1="100" x2="180" y2="130" stroke="#e74c3c" stroke-width="1.5" stroke-opacity="0.5"/>
                        <line x1="100" y1="100" x2="220" y2="90" stroke="#e74c3c" stroke-width="1" stroke-opacity="0.3"/>
                        <line x1="100" y1="100" x2="70" y2="150" stroke="#e74c3c" stroke-width="1.8" stroke-opacity="0.6"/>
                        <line x1="100" y1="100" x2="130" y2="170" stroke="#e74c3c" stroke-width="0.8" stroke-opacity="0.2"/>
                        
                        <!-- Labels -->
                        <text x="100" y="85" text-anchor="middle" font-size="12">xᵢ</text>
                        <text x="150" y="65" text-anchor="middle" font-size="12">xᵢ₁</text>
                        <text x="180" y="115" text-anchor="middle" font-size="12">xᵢ₂</text>
                        <text x="220" y="75" text-anchor="middle" font-size="12">xᵢ₃</text>
                        <text x="70" y="135" text-anchor="middle" font-size="12">xᵢ₄</text>
                        <text x="130" y="155" text-anchor="middle" font-size="12">xᵢ₅</text>
                        
                        <!-- Weights -->
                        <text x="125" y="80" text-anchor="middle" font-size="10" fill="#e74c3c">0.9</text>
                        <text x="140" y="115" text-anchor="middle" font-size="10" fill="#e74c3c">0.7</text>
                        <text x="160" y="90" text-anchor="middle" font-size="10" fill="#e74c3c">0.4</text>
                        <text x="85" y="125" text-anchor="middle" font-size="10" fill="#e74c3c">0.8</text>
                        <text x="115" y="135" text-anchor="middle" font-size="10" fill="#e74c3c">0.3</text>
                        
                        <!-- Symmetrized graph -->
                        <circle cx="400" cy="100" r="6" fill="#e74c3c"/>
                        <circle cx="450" cy="80" r="6" fill="#3498db"/>
                        <circle cx="480" cy="130" r="6" fill="#3498db"/>
                        <circle cx="520" cy="90" r="6" fill="#3498db"/>
                        <circle cx="370" cy="150" r="6" fill="#3498db"/>
                        <circle cx="430" cy="170" r="6" fill="#3498db"/>
                        
                        <!-- Undirected edges -->
                        <line x1="400" y1="100" x2="450" y2="80" stroke="#2c3e50" stroke-width="3" stroke-opacity="0.9"/>
                        <line x1="400" y1="100" x2="480" y2="130" stroke="#2c3e50" stroke-width="2" stroke-opacity="0.7"/>
                        <line x1="400" y1="100" x2="520" y2="90" stroke="#2c3e50" stroke-width="1" stroke-opacity="0.4"/>
                        <line x1="400" y1="100" x2="370" y2="150" stroke="#2c3e50" stroke-width="2.5" stroke-opacity="0.8"/>
                        <line x1="400" y1="100" x2="430" y2="170" stroke="#2c3e50" stroke-width="0.8" stroke-opacity="0.3"/>
                        
                        <!-- Labels -->
                        <text x="400" y="85" text-anchor="middle" font-size="12">xᵢ</text>
                        <text x="450" y="65" text-anchor="middle" font-size="12">xᵢ₁</text>
                        <text x="480" y="115" text-anchor="middle" font-size="12">xᵢ₂</text>
                        <text x="520" y="75" text-anchor="middle" font-size="12">xᵢ₃</text>
                        <text x="370" y="135" text-anchor="middle" font-size="12">xᵢ₄</text>
                        <text x="430" y="155" text-anchor="middle" font-size="12">xᵢ₅</text>
                        
                        <!-- Weights -->
                        <text x="425" y="80" text-anchor="middle" font-size="10">0.95</text>
                        <text x="440" y="115" text-anchor="middle" font-size="10">0.8</text>
                        <text x="460" y="90" text-anchor="middle" font-size="10">0.5</text>
                        <text x="385" y="125" text-anchor="middle" font-size="10">0.9</text>
                        <text x="415" y="135" text-anchor="middle" font-size="10">0.4</text>
                        
                        <!-- Labels -->
                        <text x="150" y="220" text-anchor="middle" font-size="14">Directed Graph</text>
                        <text x="450" y="220" text-anchor="middle" font-size="14">Symmetrized Graph</text>
                        
                        <!-- Arrow -->
                        <line x1="250" y1="130" x2="320" y2="130" stroke="#2c3e50" stroke-width="2"/>
                        <polygon points="320,130 310,125 310,135" fill="#2c3e50"/>
                        <text x="285" y="120" text-anchor="middle" font-size="12">Symmetrize</text>
                    </svg>
                    <div class="figure-caption">Figure 3: Illustration of the graph construction phase. Left: Directed graph where edges represent the membership strength of 1-simplices from the perspective of the central point. Right: Symmetrized undirected graph representing the fuzzy topological structure.</div>
                </div>
            </section>
    
            <section id="layout">
                <h3>3.2 Graph Layout Phase</h3>
                <p>The second phase of UMAP involves finding a low-dimensional representation of the data that preserves the fuzzy topological structure captured by the graph.</p>
                
                <div class="algorithm">
                    <h4>Algorithm 2: UMAP Layout Optimization</h4>
                    <pre>
    Input: Weighted graph G, target dimension d, minimum distance min_dist
    Output: Low-dimensional embedding Y = {y₁, y₂, ..., yₙ} ⊂ ℝᵈ
    
    1. Initialize Y using spectral embedding:
       a. Compute the normalized Laplacian L of G
       b. Find the d+1 smallest eigenvectors of L
       c. Use the 2nd to (d+1)th eigenvectors as the initial embedding
    
    2. Define the attractive and repulsive forces:
       a. Fit parameters a and b such that:
          i. Φ(x, y) = (1 + a‖x - y‖²ᵇ)⁻¹
          ii. Φ approximates Ψ(x, y) = {
                 1                        if ‖x - y‖ ≤ min_dist
                 exp(-(‖x - y‖ - min_dist)) otherwise
    
    3. For a fixed number of epochs:
       a. For each edge (xᵢ, xⱼ) with weight vᵢⱼ:
          i. Apply attractive force with probability vᵢⱼ:
             yᵢ ← yᵢ + η · ∇(log(Φ))(yᵢ, yⱼ)
          ii. Apply repulsive force by sampling negative examples:
             yᵢ ← yᵢ + η · ∇(log(1 - Φ))(yᵢ, yₖ) for randomly sampled k
       b. Decrease the learning rate η
    
    4. Return the optimized embedding Y
                    </pre>
                </div>
                
                <p>Let's examine the key components of the layout optimization:</p>
                
                <h4>Initialization with Spectral Embedding</h4>
                <p>UMAP initializes the low-dimensional representation using spectral embedding, which provides a good starting point for the optimization process. This is done by:</p>
                <ol>
                    <li>Computing the normalized Laplacian of the graph</li>
                    <li>Finding the eigenvectors corresponding to the smallest eigenvalues</li>
                    <li>Using these eigenvectors as the initial embedding</li>
                </ol>
                
                <div class="note">
                    <p>The normalized Laplacian of the graph is a discrete approximation of the Laplace-Beltrami operator of the manifold, making spectral embedding a natural choice for initialization.</p>
                </div>
                
                <h4>Defining the Low-Dimensional Similarity</h4>
                <p>UMAP defines a similarity measure in the low-dimensional space:</p>
                
                <div class="equation">
                    <p>\[ w_{ij} = \left(1 + a\|y_i - y_j\|_2^{2b}\right)^{-1} \]</p>
                </div>
                
                <p>Where \(a\) and \(b\) are parameters chosen to approximate the curve:</p>
                
                <div class="equation">
                    <p>\[ \Psi(y_i, y_j) = \begin{cases} 
                    1 & \text{if } \|y_i - y_j\|_2 \leq \text{min\_dist} \\
                    \exp(-(\|y_i - y_j\|_2 - \text{min\_dist})) & \text{otherwise}
                    \end{cases} \]</p>
                </div>
                
                <p>The parameter <code>min_dist</code> controls how tightly points are packed together in the embedding.</p>
            </section>
    
            <section id="optimization">
                <h3>3.3 Optimization Process</h3>
                <p>UMAP optimizes the layout by minimizing the cross-entropy between the fuzzy topological representations of the high-dimensional data and the low-dimensional embedding:</p>
                
                <div class="equation">
                    <p>\[ C(V, W) = \sum_{(i,j)} v_{ij} \log\left(\frac{v_{ij}}{w_{ij}}\right) + (1 - v_{ij}) \log\left(\frac{1 - v_{ij}}{1 - w_{ij}}\right) \]</p>
                </div>
                
                <p>Where:</p>
                <ul>
                    <li>\(v_{ij}\) is the edge weight in the high-dimensional graph</li>
                    <li>\(w_{ij}\) is the similarity in the low-dimensional space</li>
                </ul>
                
                <p>This optimization is performed using stochastic gradient descent with the following forces:</p>
                
                <h4>Attractive Forces</h4>
                <p>For each edge \((x_i, x_j)\) with weight \(v_{ij}\), apply an attractive force with probability \(v_{ij}\):</p>
                
                <div class="equation">
                    <p>\[ F_{\text{attractive}}(y_i, y_j) = -2ab\|y_i - y_j\|_2^{2(b-1)} \cdot \frac{1}{1 + \|y_i - y_j\|_2^{2}} \cdot v_{ij} \cdot (y_i - y_j) \]</p>
                </div>
                
                <h4>Repulsive Forces</h4>
                <p>For each point \(y_i\), sample random points \(y_k\) and apply repulsive forces:</p>
                
                <div class="equation">
                    <p>\[ F_{\text{repulsive}}(y_i, y_k) = \frac{2b}{\epsilon + \|y_i - y_k\|_2^2} \cdot \frac{1}{1 + a\|y_i - y_k\|_2^{2b}} \cdot (1 - v_{ik}) \cdot (y_i - y_k) \]</p>
                </div>
                
                <p>Where \(\epsilon\) is a small constant to prevent division by zero.</p>
                
                <div class="figure">
                    <svg width="600" height="400" viewBox="0 0 600 400">
                        <!-- Background -->
                        <rect width="600" height="400" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                        
                        <!-- High-dimensional space -->
                        <rect x="50" y="50" width="200" height="300" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                        <text x="150" y="30" text-anchor="middle" font-size="14">High-dimensional Space</text>
                        
                        <!-- Low-dimensional space -->
                        <rect x="350" y="50" width="200" height="300" fill="#f9e8e8" rx="5" ry="5" stroke="#e74c3c" stroke-width="1"/>
                        <text x="450" y="30" text-anchor="middle" font-size="14">Low-dimensional Space</text>
                        
                        <!-- High-dimensional points -->
                        <circle cx="100" cy="100" r="6" fill="#3498db"/>
                        <circle cx="150" cy="120" r="6" fill="#3498db"/>
                        <circle cx="120" cy="180" r="6" fill="#3498db"/>
                        <circle cx="180" cy="200" r="6" fill="#3498db"/>
                        <circle cx="90" cy="250" r="6" fill="#3498db"/>
                        
                        <!-- High-dimensional edges -->
                        <line x1="100" y1="100" x2="150" y2="120" stroke="#3498db" stroke-width="3" stroke-opacity="0.8"/>
                        <line x1="100" y1="100" x2="120" y2="180" stroke="#3498db" stroke-width="2" stroke-opacity="0.6"/>
                        <line x1="150" y1="120" x2="180" y2="200" stroke="#3498db" stroke-width="1.5" stroke-opacity="0.5"/>
                        <line x1="120" y1="180" x2="90" y2="250" stroke="#3498db" stroke-width="2.5" stroke-opacity="0.7"/>
                        
                        <!-- Low-dimensional points -->
                        <circle cx="380" cy="150" r="6" fill="#e74c3c"/>
                        <circle cx="430" cy="170" r="6" fill="#e74c3c"/>
                        <circle cx="400" cy="230" r="6" fill="#e74c3c"/>
                        <circle cx="460" cy="250" r="6" fill="#e74c3c"/>
                        <circle cx="370" cy="300" r="6" fill="#e74c3c"/>
                        
                        <!-- Attractive forces -->
                        <line x1="380" y1="150" x2="430" y2="170" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,3"/>
                        <line x1="380" y1="150" x2="400" y2="230" stroke="#27ae60" stroke-width="1.5" stroke-dasharray="5,3"/>
                        <line x1="430" y1="170" x2="460" y2="250" stroke="#27ae60" stroke-width="1" stroke-dasharray="5,3"/>
                        <line x1="400" y1="230" x2="370" y2="300" stroke="#27ae60" stroke-width="1.8" stroke-dasharray="5,3"/>
                        
                        <!-- Repulsive forces -->
                        <line x1="380" y1="150" x2="460" y2="250" stroke="#f39c12" stroke-width="1" stroke-dasharray="2,2"/>
                        <line x1="430" y1="170" x2="370" y2="300" stroke="#f39c12" stroke-width="1" stroke-dasharray="2,2"/>
                        
                        <!-- Labels -->
                        <text x="405" y="160" text-anchor="middle" font-size="10" fill="#27ae60">Attractive</text>
                        <text x="420" y="200" text-anchor="middle" font-size="10" fill="#f39c12">Repulsive</text>
                        
                        <!-- Arrow -->
                        <line x1="250" y1="200" x2="320" y2="200" stroke="#2c3e50" stroke-width="2"/>
                        <polygon points="320,200 310,195 310,205" fill="#2c3e50"/>
                        <text x="285" y="190" text-anchor="middle" font-size="12">Optimize</text>
                    </svg>
                    <div class="figure-caption">Figure 4: Illustration of the layout optimization process. The high-dimensional graph (left) is preserved in the low-dimensional space (right) through attractive forces (green) between connected points and repulsive forces (orange) between unconnected points.</div>
                </div>
                
                <h4>Stochastic Gradient Descent</h4>
                <p>The optimization is performed using stochastic gradient descent with negative sampling:</p>
                <ol>
                    <li>For each edge \((x_i, x_j)\) with weight \(v_{ij}\), sample it with probability \(v_{ij}\)</li>
                    <li>Apply the attractive force to update \(y_i\)</li>
                    <li>Sample random points and apply repulsive forces</li>
                    <li>Gradually decrease the learning rate</li>
                </ol>
                
                <p>This approach is computationally efficient and scales well to large datasets.</p>
                
                <div class="note">
                    <p>The combination of spectral initialization and stochastic gradient descent allows UMAP to efficiently find a low-dimensional representation that preserves the topological structure of the data.</p>
                </div>
            </section>
        </section>
        
    <section id="implementation">
        <h2>4. Implementation Details</h2>
        <p>While the theoretical foundations and algorithmic descriptions provide a solid understanding of UMAP, there are several important implementation details that affect its performance and behavior in practice.</p>

        <section id="parameters">
            <h3>4.1 Hyperparameters and Their Effects</h3>
            <p>UMAP has several hyperparameters that control its behavior. Understanding these parameters is crucial for effectively using UMAP in practice.</p>
            
            <h4>Number of Neighbors (n_neighbors)</h4>
            <p>This parameter controls the size of the local neighborhood used for manifold approximation. It has a significant effect on the resulting embedding:</p>
            <ul>
                <li><strong>Small values</strong> (e.g., 5-15): Focus on preserving very local structure, potentially at the expense of global structure. This can lead to more fragmented embeddings with well-preserved local neighborhoods.</li>
                <li><strong>Large values</strong> (e.g., 50-200): Capture more global structure but may lose fine details of local structure. This tends to result in more continuous embeddings.</li>
            </ul>
            
            <p>The choice of n_neighbors represents a trade-off between preserving local versus global structure:</p>
            
            <div class="figure">
                <svg width="600" height="250" viewBox="0 0 600 250">
                    <!-- Background -->
                    <rect width="600" height="250" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Small n_neighbors -->
                    <rect x="50" y="50" width="200" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="150" y="30" text-anchor="middle" font-size="14">Small n_neighbors</text>
                    
                    <!-- Small n_neighbors visualization -->
                    <circle cx="90" cy="80" r="4" fill="#3498db"/>
                    <circle cx="100" cy="85" r="4" fill="#3498db"/>
                    <circle cx="85" cy="95" r="4" fill="#3498db"/>
                    <circle cx="110" cy="100" r="4" fill="#3498db"/>
                    <circle cx="95" cy="110" r="4" fill="#3498db"/>
                    <path d="M90,80 Q95,85 100,85 Q95,90 85,95 Q95,100 110,100 Q100,105 95,110" stroke="#3498db" stroke-width="1" fill="none"/>
                    
                    <circle cx="150" cy="120" r="4" fill="#e74c3c"/>
                    <circle cx="160" cy="125" r="4" fill="#e74c3c"/>
                    <circle cx="145" cy="135" r="4" fill="#e74c3c"/>
                    <circle cx="170" cy="140" r="4" fill="#e74c3c"/>
                    <circle cx="155" cy="150" r="4" fill="#e74c3c"/>
                    <path d="M150,120 Q155,125 160,125 Q155,130 145,135 Q155,140 170,140 Q160,145 155,150" stroke="#e74c3c" stroke-width="1" fill="none"/>
                    
                    <circle cx="210" cy="80" r="4" fill="#2ecc71"/>
                    <circle cx="220" cy="85" r="4" fill="#2ecc71"/>
                    <circle cx="205" cy="95" r="4" fill="#2ecc71"/>
                    <circle cx="230" cy="100" r="4" fill="#2ecc71"/>
                    <circle cx="215" cy="110" r="4" fill="#2ecc71"/>
                    <path d="M210,80 Q215,85 220,85 Q215,90 205,95 Q215,100 230,100 Q220,105 215,110" stroke="#2ecc71" stroke-width="1" fill="none"/>
                    
                    <!-- Large n_neighbors -->
                    <rect x="350" y="50" width="200" height="150" fill="#f9e8e8" rx="5" ry="5" stroke="#e74c3c" stroke-width="1"/>
                    <text x="450" y="30" text-anchor="middle" font-size="14">Large n_neighbors</text>
                    
                    <!-- Large n_neighbors visualization -->
                    <circle cx="390" cy="80" r="4" fill="#3498db"/>
                    <circle cx="400" cy="85" r="4" fill="#3498db"/>
                    <circle cx="385" cy="95" r="4" fill="#3498db"/>
                    <circle cx="410" cy="100" r="4" fill="#3498db"/>
                    <circle cx="395" cy="110" r="4" fill="#3498db"/>
                    
                    <circle cx="450" cy="120" r="4" fill="#e74c3c"/>
                    <circle cx="460" cy="125" r="4" fill="#e74c3c"/>
                    <circle cx="445" cy="135" r="4" fill="#e74c3c"/>
                    <circle cx="470" cy="140" r="4" fill="#e74c3c"/>
                    <circle cx="455" cy="150" r="4" fill="#e74c3c"/>
                    
                    <circle cx="510" cy="80" r="4" fill="#2ecc71"/>
                    <circle cx="520" cy="85" r="4" fill="#2ecc71"/>
                    <circle cx="505" cy="95" r="4" fill="#2ecc71"/>
                    <circle cx="530" cy="100" r="4" fill="#2ecc71"/>
                    <circle cx="515" cy="110" r="4" fill="#2ecc71"/>
                    
                    <!-- Global structure path -->
                    <path d="M390,80 Q420,100 450,120 Q480,110 510,80" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,3" fill="none"/>
                    <text x="450" y="180" text-anchor="middle" font-size="12">Global structure preserved</text>
                    
                    <!-- Labels -->
                    <text x="150" y="220" text-anchor="middle" font-size="12">Local clusters well-preserved</text>
                    <text x="150" y="235" text-anchor="middle" font-size="12">Global structure may be lost</text>
                </svg>
                <div class="figure-caption">Figure 5: Effect of the n_neighbors parameter. Small values preserve local structure but may fragment the embedding (left), while large values preserve global structure but may lose fine local details (right).</div>
            </div>
            
            <h4>Minimum Distance (min_dist)</h4>
            <p>This parameter controls how tightly points are packed together in the embedding space. It affects the balance between preserving local and global structure:</p>
            <ul>
                <li><strong>Small values</strong> (e.g., 0.0-0.1): Result in tighter, more clustered embeddings. This preserves more local structure but can lead to overcrowding.</li>
                <li><strong>Large values</strong> (e.g., 0.5-1.0): Create more evenly dispersed embeddings. This makes global structure more visible but may lose some local details.</li>
            </ul>
            
            <div class="figure">
                <svg width="600" height="250" viewBox="0 0 600 250">
                    <!-- Background -->
                    <rect width="600" height="250" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Small min_dist -->
                    <rect x="50" y="50" width="200" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="150" y="30" text-anchor="middle" font-size="14">Small min_dist</text>
                    
                    <!-- Cluster 1 -->
                    <circle cx="90" cy="90" r="3" fill="#3498db"/>
                    <circle cx="95" cy="88" r="3" fill="#3498db"/>
                    <circle cx="93" cy="94" r="3" fill="#3498db"/>
                    <circle cx="88" cy="96" r="3" fill="#3498db"/>
                    <circle cx="85" cy="91" r="3" fill="#3498db"/>
                    
                    <!-- Cluster 2 -->
                    <circle cx="130" cy="90" r="3" fill="#e74c3c"/>
                    <circle cx="135" cy="88" r="3" fill="#e74c3c"/>
                    <circle cx="133" cy="94" r="3" fill="#e74c3c"/>
                    <circle cx="128" cy="96" r="3" fill="#e74c3c"/>
                    <circle cx="125" cy="91" r="3" fill="#e74c3c"/>
                    
                    <!-- Cluster 3 -->
                    <circle cx="170" cy="90" r="3" fill="#2ecc71"/>
                    <circle cx="175" cy="88" r="3" fill="#2ecc71"/>
                    <circle cx="173" cy="94" r="3" fill="#2ecc71"/>
                    <circle cx="168" cy="96" r="3" fill="#2ecc71"/>
                    <circle cx="165" cy="91" r="3" fill="#2ecc71"/>
                    
                    <!-- Cluster 4 -->
                    <circle cx="90" cy="130" r="3" fill="#f39c12"/>
                    <circle cx="95" cy="128" r="3" fill="#f39c12"/>
                    <circle cx="93" cy="134" r="3" fill="#f39c12"/>
                    <circle cx="88" cy="136" r="3" fill="#f39c12"/>
                    <circle cx="85" cy="131" r="3" fill="#f39c12"/>
                    
                    <!-- Cluster 5 -->
                    <circle cx="130" cy="130" r="3" fill="#9b59b6"/>
                    <circle cx="135" cy="128" r="3" fill="#9b59b6"/>
                    <circle cx="133" cy="134" r="3" fill="#9b59b6"/>
                    <circle cx="128" cy="136" r="3" fill="#9b59b6"/>
                    <circle cx="125" cy="131" r="3" fill="#9b59b6"/>
                    
                    <!-- Cluster 6 -->
                    <circle cx="170" cy="130" r="3" fill="#1abc9c"/>
                    <circle cx="175" cy="128" r="3" fill="#1abc9c"/>
                    <circle cx="173" cy="134" r="3" fill="#1abc9c"/>
                    <circle cx="168" cy="136" r="3" fill="#1abc9c"/>
                    <circle cx="165" cy="131" r="3" fill="#1abc9c"/>
                    
                    <!-- Large min_dist -->
                    <rect x="350" y="50" width="200" height="150" fill="#f9e8e8" rx="5" ry="5" stroke="#e74c3c" stroke-width="1"/>
                    <text x="450" y="30" text-anchor="middle" font-size="14">Large min_dist</text>
                    
                    <!-- Cluster 1 -->
                    <circle cx="380" cy="80" r="3" fill="#3498db"/>
                    <circle cx="390" cy="75" r="3" fill="#3498db"/>
                    <circle cx="385" cy="90" r="3" fill="#3498db"/>
                    <circle cx="375" cy="95" r="3" fill="#3498db"/>
                    <circle cx="370" cy="85" r="3" fill="#3498db"/>
                    
                    <!-- Cluster 2 -->
                    <circle cx="440" cy="80" r="3" fill="#e74c3c"/>
                    <circle cx="450" cy="75" r="3" fill="#e74c3c"/>
                    <circle cx="445" cy="90" r="3" fill="#e74c3c"/>
                    <circle cx="435" cy="95" r="3" fill="#e74c3c"/>
                    <circle cx="430" cy="85" r="3" fill="#e74c3c"/>
                    
                    <!-- Cluster 3 -->
                    <circle cx="500" cy="80" r="3" fill="#2ecc71"/>
                    <circle cx="510" cy="75" r="3" fill="#2ecc71"/>
                    <circle cx="505" cy="90" r="3" fill="#2ecc71"/>
                    <circle cx="495" cy="95" r="3" fill="#2ecc71"/>
                    <circle cx="490" cy="85" r="3" fill="#2ecc71"/>
                    
                    <!-- Cluster 4 -->
                    <circle cx="380" cy="140" r="3" fill="#f39c12"/>
                    <circle cx="390" cy="135" r="3" fill="#f39c12"/>
                    <circle cx="385" cy="150" r="3" fill="#f39c12"/>
                    <circle cx="375" cy="155" r="3" fill="#f39c12"/>
                    <circle cx="370" cy="145" r="3" fill="#f39c12"/>
                    
                    <!-- Cluster 5 -->
                    <circle cx="440" cy="140" r="3" fill="#9b59b6"/>
                    <circle cx="450" cy="135" r="3" fill="#9b59b6"/>
                    <circle cx="445" cy="150" r="3" fill="#9b59b6"/>
                    <circle cx="435" cy="155" r="3" fill="#9b59b6"/>
                    <circle cx="430" cy="145" r="3" fill="#9b59b6"/>
                    
                    <!-- Cluster 6 -->
                    <circle cx="500" cy="140" r="3" fill="#1abc9c"/>
                    <circle cx="510" cy="135" r="3" fill="#1abc9c"/>
                    <circle cx="505" cy="150" r="3" fill="#1abc9c"/>
                    <circle cx="495" cy="155" r="3" fill="#1abc9c"/>
                    <circle cx="490" cy="145" r="3" fill="#1abc9c"/>
                    
                    <!-- Labels -->
                    <text x="150" y="220" text-anchor="middle" font-size="12">Tight clusters, clear separation</text>
                    <text x="450" y="220" text-anchor="middle" font-size="12">Dispersed clusters, more spacing</text>
                </svg>
                <div class="figure-caption">Figure 6: Effect of the min_dist parameter. Small values create tightly packed clusters (left), while large values disperse points more evenly (right).</div>
            </div>
            
            <h4>Number of Epochs (n_epochs)</h4>
            <p>This parameter controls how many iterations of optimization are performed. More epochs generally lead to better embeddings but increase computation time:</p>
            <ul>
                <li>The default is typically set to 200 for smaller datasets and 500 for larger ones</li>
                <li>For large datasets, fewer epochs may be sufficient due to more gradient updates per epoch</li>
                <li>For small datasets or when fine details are important, increasing n_epochs can improve results</li>
            </ul>
            
            <h4>Metric</h4>
            <p>UMAP can work with any valid distance metric, not just Euclidean distance. Common choices include:</p>
            <ul>
                <li><strong>Euclidean</strong>: Standard for continuous data</li>
                <li><strong>Manhattan</strong>: Less sensitive to outliers than Euclidean</li>
                <li><strong>Cosine</strong>: Good for text data and other high-dimensional sparse data</li>
                <li><strong>Correlation</strong>: Useful for gene expression and other biological data</li>
                <li><strong>Hamming</strong>: For binary data</li>
                <li><strong>Jaccard</strong>: For sets and binary data</li>
            </ul>
            
            <div class="note">
                <p>The choice of metric should reflect the nature of the data and the types of relationships you want to preserve in the embedding.</p>
            </div>
            
            <h4>Target Embedding Dimension</h4>
            <p>Unlike t-SNE, which is primarily designed for 2D or 3D visualization, UMAP has no computational restrictions on the embedding dimension. This makes it useful for:</p>
            <ul>
                <li>Visualization (2D or 3D)</li>
                <li>Dimension reduction as preprocessing for machine learning (any dimension)</li>
                <li>Feature extraction (typically 10-100 dimensions)</li>
            </ul>
        </section>

        <section id="computational">
            <h3>4.2 Computational Optimizations</h3>
            <p>Several optimizations make UMAP efficient and scalable to large datasets:</p>
            
            <h4>Approximate Nearest Neighbor Search</h4>
            <p>UMAP uses the Nearest-Neighbor-Descent algorithm for efficient approximate nearest neighbor search. This algorithm has empirical complexity of approximately O(N<sup>1.14</sup>), making it much faster than exact nearest neighbor algorithms for large datasets.</p>
            
            <div class="figure">
                <svg width="500" height="300" viewBox="0 0 500 300">
                    <!-- Background -->
                    <rect width="500" height="300" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Axes -->
                    <line x1="50" y1="250" x2="450" y2="250" stroke="#333" stroke-width="2"/>
                    <line x1="50" y1="50" x2="50" y2="250" stroke="#333" stroke-width="2"/>
                    
                    <!-- Axis labels -->
                    <text x="250" y="280" text-anchor="middle" font-size="14">Dataset Size (N)</text>
                    <text x="20" y="150" text-anchor="middle" font-size="14" transform="rotate(-90,20,150)">Time</text>
                    
                    <!-- Curves -->
                    <path d="M50,250 Q150,230 250,150 T450,50" stroke="#e74c3c" stroke-width="2" fill="none"/>
                    <path d="M50,250 Q150,240 250,200 T450,120" stroke="#3498db" stroke-width="2" fill="none"/>
                    <path d="M50,250 Q150,245 250,230 T450,180" stroke="#2ecc71" stroke-width="2" fill="none"/>
                    
                    <!-- Legend -->
                    <rect x="320" y="70" width="120" height="70" fill="white" stroke="#ddd" stroke-width="1" rx="5" ry="5"/>
                    <line x1="330" y1="85" x2="350" y2="85" stroke="#e74c3c" stroke-width="2"/>
                    <text x="355" y="90" font-size="12">Exact NN (O(N²))</text>
                    <line x1="330" y1="105" x2="350" y2="105" stroke="#3498db" stroke-width="2"/>
                    <text x="355" y="110" font-size="12">KD-Tree (O(N log N))</text>
                    <line x1="330" y1="125" x2="350" y2="125" stroke="#2ecc71" stroke-width="2"/>
                    <text x="355" y="130" font-size="12">NN-Descent (O(N¹·¹⁴))</text>
                </svg>
                <div class="figure-caption">Figure 7: Computational complexity comparison of nearest neighbor search algorithms. NN-Descent used by UMAP scales better than exact methods for large datasets.</div>
            </div>
            
            <h4>Stochastic Gradient Descent with Negative Sampling</h4>
            <p>UMAP optimizes the embedding using stochastic gradient descent with negative sampling, similar to techniques used in word2vec. This approach:</p>
            <ul>
                <li>Avoids the need for global normalization (unlike t-SNE)</li>
                <li>Allows for efficient optimization without computing all pairwise distances</li>
                <li>Scales linearly with the number of edges in the graph (O(kN) where k is the number of neighbors)</li>
            </ul>
            
            <h4>Spectral Initialization</h4>
            <p>UMAP initializes the embedding using spectral embedding (eigenvectors of the normalized Laplacian), which provides several benefits:</p>
            <ul>
                <li>Faster convergence compared to random initialization</li>
                <li>More stable results across multiple runs</li>
                <li>Better preservation of global structure</li>
            </ul>
            
            <h4>Scalability Comparison</h4>
            <p>UMAP's computational optimizations result in superior scaling compared to other dimension reduction techniques:</p>
            
            <table>
                <thead>
                    <tr>
                        <th>Algorithm</th>
                        <th>Time Complexity</th>
                        <th>Space Complexity</th>
                        <th>Scales to Large Datasets</th>
                        <th>Scales to High Dimensions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>UMAP</td>
                        <td>O(N<sup>1.14</sup>)</td>
                        <td>O(N)</td>
                        <td>Yes</td>
                        <td>Yes</td>
                    </tr>
                    <tr>
                        <td>t-SNE</td>
                        <td>O(N log N)</td>
                        <td>O(N)</td>
                        <td>Limited</td>
                        <td>No</td>
                    </tr>
                    <tr>
                        <td>LargeVis</td>
                        <td>O(N)</td>
                        <td>O(N)</td>
                        <td>Yes</td>
                        <td>Limited</td>
                    </tr>
                    <tr>
                        <td>PCA</td>
                        <td>O(ND<sup>2</sup>)</td>
                        <td>O(ND)</td>
                        <td>Yes</td>
                        <td>Limited</td>
                    </tr>
                    <tr>
                        <td>Isomap</td>
                        <td>O(N<sup>3</sup>)</td>
                        <td>O(N<sup>2</sup>)</td>
                        <td>No</td>
                        <td>No</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="note">
                <p>UMAP's computational efficiency allows it to handle datasets with millions of samples and thousands of dimensions, making it suitable for a wide range of applications.</p>
            </div>
        </section>
    </section>

    <section id="comparison">
        <h2>5. Comparison with Other Methods</h2>
        <p>To fully appreciate UMAP's strengths and understand its place among dimension reduction techniques, it's helpful to compare it with other popular methods, particularly t-SNE, LargeVis, PCA, and Laplacian Eigenmaps.</p>

        <section id="tsne">
            <h3>5.1 UMAP vs. t-SNE</h3>
            <p>t-SNE (t-Distributed Stochastic Neighbor Embedding) has been the gold standard for visualization of high-dimensional data since its introduction in 2008. UMAP and t-SNE share several similarities but also have important differences:</p>
            
            <h4>Similarities</h4>
            <ul>
                <li>Both focus on preserving local structure in the data</li>
                <li>Both create visualizations where similar points are placed close together</li>
                <li>Both use stochastic approaches for optimization</li>
                <li>Both can reveal clusters and patterns in high-dimensional data</li>
            </ul>
            
            <h4>Key Differences</h4>
            <table>
                <thead>
                    <tr>
                        <th>Aspect</th>
                        <th>UMAP</th>
                        <th>t-SNE</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Theoretical Foundation</td>
                        <td>Riemannian geometry and algebraic topology</td>
                        <td>Stochastic neighbor embedding with t-distribution</td>
                    </tr>
                    <tr>
                        <td>Global Structure Preservation</td>
                        <td>Better at preserving global structure</td>
                        <td>Focuses primarily on local structure</td>
                    </tr>
                    <tr>
                        <td>Computational Efficiency</td>
                        <td>More efficient, especially for large datasets</td>
                        <td>Less efficient, particularly for large datasets</td>
                    </tr>
                    <tr>
                        <td>Embedding Dimension</td>
                        <td>No restrictions on embedding dimension</td>
                        <td>Primarily designed for 2D or 3D visualization</td>
                    </tr>
                    <tr>
                        <td>Scaling to High Dimensions</td>
                        <td>Scales well to high-dimensional data</td>
                        <td>Often requires dimensionality reduction as preprocessing</td>
                    </tr>
                    <tr>
                        <td>Objective Function</td>
                        <td>Cross-entropy between fuzzy topological representations</td>
                        <td>Kullback-Leibler divergence between probability distributions</td>
                    </tr>
                    <tr>
                        <td>Initialization</td>
                        <td>Spectral embedding (eigenvectors of Laplacian)</td>
                        <td>Random or PCA initialization</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="figure">
                <svg width="700" height="350" viewBox="0 0 700 350">
                    <!-- Background -->
                    <rect width="700" height="350" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- t-SNE visualization -->
                    <rect x="50" y="50" width="250" height="250" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="175" y="30" text-anchor="middle" font-size="16">t-SNE</text>
                    
                    <!-- UMAP visualization -->
                    <rect x="400" y="50" width="250" height="250" fill="#f9e8e8" rx="5" ry="5" stroke="#e74c3c" stroke-width="1"/>
                    <text x="525" y="30" text-anchor="middle" font-size="16">UMAP</text>
                    
                    <!-- t-SNE clusters -->
                    <!-- Cluster 1 -->
                    <circle cx="100" cy="100" r="5" fill="#3498db"/>
                    <circle cx="110" cy="95" r="5" fill="#3498db"/>
                    <circle cx="105" cy="110" r="5" fill="#3498db"/>
                    <circle cx="90" cy="105" r="5" fill="#3498db"/>
                    <circle cx="95" cy="90" r="5" fill="#3498db"/>
                    
                    <!-- Cluster 2 -->
                    <circle cx="200" cy="100" r="5" fill="#e74c3c"/>
                    <circle cx="210" cy="95" r="5" fill="#e74c3c"/>
                    <circle cx="205" cy="110" r="5" fill="#e74c3c"/>
                    <circle cx="190" cy="105" r="5" fill="#e74c3c"/>
                    <circle cx="195" cy="90" r="5" fill="#e74c3c"/>
                    
                    <!-- Cluster 3 -->
                    <circle cx="100" cy="200" r="5" fill="#2ecc71"/>
                    <circle cx="110" cy="195" r="5" fill="#2ecc71"/>
                    <circle cx="105" cy="210" r="5" fill="#2ecc71"/>
                    <circle cx="90" cy="205" r="5" fill="#2ecc71"/>
                    <circle cx="95" cy="190" r="5" fill="#2ecc71"/>
                    
                    <!-- Cluster 4 -->
                    <circle cx="200" cy="200" r="5" fill="#f39c12"/>
                    <circle cx="210" cy="195" r="5" fill="#f39c12"/>
                    <circle cx="205" cy="210" r="5" fill="#f39c12"/>
                    <circle cx="190" cy="205" r="5" fill="#f39c12"/>
                    <circle cx="195" cy="190" r="5" fill="#f39c12"/>
                    
                    <!-- UMAP clusters with preserved global structure -->
                    <!-- Cluster 1 -->
                    <circle cx="450" cy="100" r="5" fill="#3498db"/>
                    <circle cx="460" cy="95" r="5" fill="#3498db"/>
                    <circle cx="455" cy="110" r="5" fill="#3498db"/>
                    <circle cx="440" cy="105" r="5" fill="#3498db"/>
                    <circle cx="445" cy="90" r="5" fill="#3498db"/>
                    
                    <!-- Cluster 2 -->
                    <circle cx="550" cy="100" r="5" fill="#e74c3c"/>
                    <circle cx="560" cy="95" r="5" fill="#e74c3c"/>
                    <circle cx="555" cy="110" r="5" fill="#e74c3c"/>
                    <circle cx="540" cy="105" r="5" fill="#e74c3c"/>
                    <circle cx="545" cy="90" r="5" fill="#e74c3c"/>
                    
                    <!-- Cluster 3 -->
                    <circle cx="450" cy="200" r="5" fill="#2ecc71"/>
                    <circle cx="460" cy="195" r="5" fill="#2ecc71"/>
                    <circle cx="455" cy="210" r="5" fill="#2ecc71"/>
                    <circle cx="440" cy="205" r="5" fill="#2ecc71"/>
                    <circle cx="445" cy="190" r="5" fill="#2ecc71"/>
                    
                    <!-- Cluster 4 -->
                    <circle cx="550" cy="200" r="5" fill="#f39c12"/>
                    <circle cx="560" cy="195" r="5" fill="#f39c12"/>
                    <circle cx="555" cy="210" r="5" fill="#f39c12"/>
                    <circle cx="540" cy="205" r="5" fill="#f39c12"/>
                    <circle cx="545" cy="190" r="5" fill="#f39c12"/>
                    
                    <!-- Global structure in UMAP -->
                    <path d="M450,100 L550,100 L550,200 L450,200 Z" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,3" fill="none"/>
                    
                    <!-- Labels -->
                    <text x="175" y="320" text-anchor="middle" font-size="14">Local structure preserved</text>
                    <text x="175" y="340" text-anchor="middle" font-size="14">Global structure may be distorted</text>
                    
                    <text x="525" y="320" text-anchor="middle" font-size="14">Local structure preserved</text>
                    <text x="525" y="340" text-anchor="middle" font-size="14">Global structure better preserved</text>
                </svg>
                <div class="figure-caption">Figure 8: Comparison of t-SNE and UMAP visualizations. Both preserve local structure (clusters), but UMAP tends to better preserve global relationships between clusters (purple dashed line).</div>
            </div>
            
            <div class="note">
                <p>While t-SNE often creates more visually appealing clusters for visualization purposes, UMAP tends to preserve more of the global structure while still maintaining clear cluster separation. This makes UMAP more suitable for tasks where understanding the relationships between clusters is important.</p>
            </div>
        </section>

        <section id="largevis">
            <h3>5.2 UMAP vs. LargeVis</h3>
            <p>LargeVis is another dimension reduction technique designed to address some of t-SNE's limitations, particularly for large datasets. UMAP and LargeVis share several algorithmic similarities:</p>
            
            <h4>Similarities</h4>
            <ul>
                <li>Both use approximate nearest neighbor search for efficiency</li>
                <li>Both employ stochastic gradient descent with negative sampling</li>
                <li>Both scale better to large datasets than t-SNE</li>
                <li>Both avoid global normalization, unlike t-SNE</li>
            </ul>
            
            <h4>Key Differences</h4>
            <ul>
                <li><strong>Theoretical Foundation</strong>: UMAP has stronger mathematical foundations in topology and geometry, while LargeVis is more heuristically motivated</li>
                <li><strong>Graph Construction</strong>: UMAP uses fuzzy simplicial sets with local connectivity constraints, while LargeVis uses a probabilistic approach similar to t-SNE</li>
                <li><strong>Objective Function</strong>: UMAP minimizes cross-entropy between fuzzy topological representations, while LargeVis maximizes a likelihood function</li>
                <li><strong>Initialization</strong>: UMAP uses spectral embedding, while LargeVis typically uses random initialization</li>
            </ul>
            
            <p>In terms of performance, UMAP and LargeVis often produce similar results, but UMAP tends to be more computationally efficient and preserves more global structure.</p>
        </section>

        <section id="pca">
            <h3>5.3 UMAP vs. PCA and Laplacian Eigenmaps</h3>
            <p>Principal Component Analysis (PCA) and Laplacian Eigenmaps represent two different approaches to dimension reduction:</p>
            
            <h4>UMAP vs. PCA</h4>
            <p>PCA is a linear dimension reduction technique that projects data along the directions of maximum variance:</p>
            <ul>
                <li><strong>Linearity</strong>: PCA is linear and cannot capture nonlinear structures in the data, while UMAP is nonlinear</li>
                <li><strong>Interpretability</strong>: PCA components have clear interpretations as directions of maximum variance, while UMAP dimensions lack direct interpretability</li>
                <li><strong>Global Structure</strong>: PCA focuses on preserving global variance, while UMAP prioritizes local structure</li>
                <li><strong>Scalability</strong>: PCA scales well with the number of samples but poorly with the number of dimensions, while UMAP scales well with both</li>
            </ul>
            
            <h4>UMAP vs. Laplacian Eigenmaps</h4>
            <p>Laplacian Eigenmaps is a nonlinear dimension reduction technique that preserves local neighborhoods:</p>
            <ul>
                <li><strong>Graph Construction</strong>: Both use graph-based representations, but UMAP uses fuzzy simplicial sets while Laplacian Eigenmaps uses a binary adjacency graph</li>
                <li><strong>Optimization</strong>: Laplacian Eigenmaps solves an eigenvalue problem, while UMAP uses stochastic gradient descent</li>
                <li><strong>Local Structure</strong>: Both preserve local structure, but UMAP's fuzzy approach handles noise better</li>
                <li><strong>Scalability</strong>: UMAP scales better to large datasets than Laplacian Eigenmaps</li>
            </ul>
            
            <div class="figure">
                <svg width="700" height="500" viewBox="0 0 700 500">
                    <!-- Background -->
                    <rect width="700" height="500" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- PCA visualization -->
                    <rect x="50" y="50" width="250" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="175" y="30" text-anchor="middle" font-size="16">PCA</text>
                    
                    <!-- Laplacian Eigenmaps visualization -->
                    <rect x="400" y="50" width="250" height="150" fill="#eafaf1" rx="5" ry="5" stroke="#2ecc71" stroke-width="1"/>
                    <text x="525" y="30" text-anchor="middle" font-size="16">Laplacian Eigenmaps</text>
                    
                    <!-- UMAP visualization -->
                    <rect x="225" y="250" width="250" height="150" fill="#f9e8e8" rx="5" ry="5" stroke="#e74c3c" stroke-width="1"/>
                    <text x="350" y="230" text-anchor="middle" font-size="16">UMAP</text>
                    
                    <!-- PCA projection -->
                    <line x1="80" y1="125" x2="270" y2="125" stroke="#3498db" stroke-width="2" stroke-dasharray="5,3"/>
                    <line x1="175" y1="70" x2="175" y2="180" stroke="#3498db" stroke-width="1" stroke-dasharray="5,3"/>
                    <text x="270" y="140" font-size="12" fill="#3498db">PC1</text>
                    <text x="190" y="70" font-size="12" fill="#3498db">PC2</text>
                    
                    <!-- PCA points -->
                    <circle cx="100" cy="80" r="4" fill="#3498db"/>
                    <circle cx="120" cy="90" r="4" fill="#3498db"/>
                    <circle cx="90" cy="100" r="4" fill="#3498db"/>
                    <circle cx="110" cy="110" r="4" fill="#3498db"/>
                    <circle cx="130" cy="120" r="4" fill="#3498db"/>
                    
                    <circle cx="200" cy="130" r="4" fill="#e74c3c"/>
                    <circle cx="220" cy="140" r="4" fill="#e74c3c"/>
                    <circle cx="190" cy="150" r="4" fill="#e74c3c"/>
                    <circle cx="210" cy="160" r="4" fill="#e74c3c"/>
                    <circle cx="230" cy="170" r="4" fill="#e74c3c"/>
                    
                    <!-- Laplacian Eigenmaps points -->
                    <circle cx="430" cy="80" r="4" fill="#3498db"/>
                    <circle cx="440" cy="90" r="4" fill="#3498db"/>
                    <circle cx="420" cy="100" r="4" fill="#3498db"/>
                    <circle cx="450" cy="85" r="4" fill="#3498db"/>
                    <circle cx="425" cy="95" r="4" fill="#3498db"/>
                    
                    <circle cx="530" cy="120" r="4" fill="#e74c3c"/>
                    <circle cx="540" cy="130" r="4" fill="#e74c3c"/>
                    <circle cx="520" cy="140" r="4" fill="#e74c3c"/>
                    <circle cx="550" cy="125" r="4" fill="#e74c3c"/>
                    <circle cx="525" cy="135" r="4" fill="#e74c3c"/>
                    
                    <!-- Laplacian Eigenmaps edges -->
                    <line x1="430" y1="80" x2="440" y2="90" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="440" y1="90" x2="420" y2="100" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="420" y1="100" x2="450" y2="85" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="450" y1="85" x2="425" y2="95" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="425" y1="95" x2="430" y2="80" stroke="#2ecc71" stroke-width="1"/>
                    
                    <line x1="530" y1="120" x2="540" y2="130" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="540" y1="130" x2="520" y2="140" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="520" y1="140" x2="550" y2="125" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="550" y1="125" x2="525" y2="135" stroke="#2ecc71" stroke-width="1"/>
                    <line x1="525" y1="135" x2="530" y2="120" stroke="#2ecc71" stroke-width="1"/>
                    
                    <!-- UMAP points -->
                    <circle cx="280" cy="280" r="4" fill="#3498db"/>
                    <circle cx="290" cy="290" r="4" fill="#3498db"/>
                    <circle cx="270" cy="300" r="4" fill="#3498db"/>
                    <circle cx="300" cy="285" r="4" fill="#3498db"/>
                    <circle cx="275" cy="295" r="4" fill="#3498db"/>
                    
                    <circle cx="380" cy="320" r="4" fill="#e74c3c"/>
                    <circle cx="390" cy="330" r="4" fill="#e74c3c"/>
                    <circle cx="370" cy="340" r="4" fill="#e74c3c"/>
                    <circle cx="400" cy="325" r="4" fill="#e74c3c"/>
                    <circle cx="375" cy="335" r="4" fill="#e74c3c"/>
                    
                    <!-- UMAP edges with varying weights -->
                    <line x1="280" y1="280" x2="290" y2="290" stroke="#e74c3c" stroke-width="3" stroke-opacity="0.8"/>
                    <line x1="290" y1="290" x2="270" y2="300" stroke="#e74c3c" stroke-width="2" stroke-opacity="0.6"/>
                    <line x1="270" y1="300" x2="300" y2="285" stroke="#e74c3c" stroke-width="2.5" stroke-opacity="0.7"/>
                    <line x1="300" y1="285" x2="275" y2="295" stroke="#e74c3c" stroke-width="1.5" stroke-opacity="0.5"/>
                    <line x1="275" y1="295" x2="280" y2="280" stroke="#e74c3c" stroke-width="2" stroke-opacity="0.6"/>
                    
                    <line x1="380" y1="320" x2="390" y2="330" stroke="#e74c3c" stroke-width="3" stroke-opacity="0.8"/>
                    <line x1="390" y1="330" x2="370" y2="340" stroke="#e74c3c" stroke-width="2" stroke-opacity="0.6"/>
                    <line x1="370" y1="340" x2="400" y2="325" stroke="#e74c3c" stroke-width="2.5" stroke-opacity="0.7"/>
                    <line x1="400" y1="325" x2="375" y2="335" stroke="#e74c3c" stroke-width="1.5" stroke-opacity="0.5"/>
                    <line x1="375" y1="335" x2="380" y2="320" stroke="#e74c3c" stroke-width="2" stroke-opacity="0.6"/>
                    
                    <!-- Labels -->
                    <text x="175" y="200" text-anchor="middle" font-size="12">Linear projection</text>
                    <text x="175" y="215" text-anchor="middle" font-size="12">Preserves global variance</text>
                    
                    <text x="525" y="200" text-anchor="middle" font-size="12">Nonlinear, preserves local structure</text>
                    <text x="525" y="215" text-anchor="middle" font-size="12">Binary neighborhood graph</text>
                    
                    <text x="350" y="400" text-anchor="middle" font-size="12">Nonlinear, preserves local structure</text>
                    <text x="350" y="415" text-anchor="middle" font-size="12">Fuzzy weighted graph with better noise handling</text>
                    <text x="350" y="430" text-anchor="middle" font-size="12">Balances local and global structure</text>
                </svg>
                <div class="figure-caption">Figure 9: Comparison of PCA, Laplacian Eigenmaps, and UMAP. PCA performs linear projection along principal components, Laplacian Eigenmaps preserves local structure using binary neighborhood graphs, and UMAP uses fuzzy weighted graphs to balance local and global structure preservation.</div>
            </div>
            
            <div class="note">
                <p>Each method has its strengths: PCA excels at interpretability and simplicity, Laplacian Eigenmaps provides a theoretical foundation for manifold learning, and UMAP combines the strengths of both while addressing their limitations.</p>
            </div>
            
            <h4>When to Use Each Method</h4>
            <table>
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Best For</th>
                        <th>Limitations</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>UMAP</td>
                        <td>
                            <ul>
                                <li>Visualization of high-dimensional data</li>
                                <li>Preserving both local and global structure</li>
                                <li>Large datasets with complex structure</li>
                                <li>Dimension reduction for ML preprocessing</li>
                            </ul>
                        </td>
                        <td>
                            <ul>
                                <li>Less interpretable dimensions</li>
                                <li>May find structure in noise with small datasets</li>
                                <li>Parameter tuning can be challenging</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td>t-SNE</td>
                        <td>
                            <ul>
                                <li>Visualization focused on cluster separation</li>
                                <li>When local structure is the primary concern</li>
                                <li>When clear visual separation is needed</li>
                            </ul>
                        </td>
                        <td>
                            <ul>
                                <li>Poor preservation of global structure</li>
                                <li>Computationally expensive for large datasets</li>
                                <li>Limited to low-dimensional embeddings</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td>PCA</td>
                        <td>
                            <ul>
                                <li>When interpretability is crucial</li>
                                <li>As a first step in exploratory data analysis</li>
                                <li>When data has linear structure</li>
                                <li>Feature extraction with interpretable components</li>
                            </ul>
                        </td>
                        <td>
                            <ul>
                                <li>Cannot capture nonlinear structure</li>
                                <li>May miss important local patterns</li>
                                <li>Limited by linearity assumptions</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td>Laplacian Eigenmaps</td>
                        <td>
                            <ul>
                                <li>When theoretical guarantees are important</li>
                                <li>For understanding manifold structure</li>
                                <li>As initialization for other methods</li>
                            </ul>
                        </td>
                        <td>
                            <ul>
                                <li>Less scalable to large datasets</li>
                                <li>Binary neighborhood relationships</li>
                                <li>Less robust to noise</li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>
    </section>

    <section id="practical">
        <h2>6. Practical Considerations</h2>
        <p>Beyond the theoretical foundations and algorithmic details, there are several practical considerations to keep in mind when using UMAP for real-world applications.</p>

        <section id="strengths">
            <h3>6.1 Strengths of UMAP</h3>
            <p>UMAP offers several advantages that make it a powerful tool for dimension reduction and visualization:</p>
            
            <h4>Better Preservation of Global Structure</h4>
            <p>UMAP generally preserves more of the global structure of the data compared to t-SNE. This means that relationships between clusters and overall data topology are better maintained in the low-dimensional representation.</p>
            
            <div class="figure">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- High-dimensional space -->
                    <rect x="50" y="50" width="200" height="200" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="150" y="30" text-anchor="middle" font-size="14">High-dimensional Space</text>
                    
                    <!-- UMAP embedding -->
                    <rect x="350" y="50" width="200" height="200" fill="#f9e8e8" rx="5" ry="5" stroke="#e74c3c" stroke-width="1"/>
                    <text x="450" y="30" text-anchor="middle" font-size="14">UMAP Embedding</text>
                    
                    <!-- High-dimensional structure -->
                    <!-- Cluster 1 -->
                    <circle cx="80" cy="80" r="4" fill="#3498db"/>
                    <circle cx="90" cy="85" r="4" fill="#3498db"/>
                    <circle cx="85" cy="75" r="4" fill="#3498db"/>
                    <circle cx="95" cy="80" r="4" fill="#3498db"/>
                    
                    <!-- Cluster 2 -->
                    <circle cx="80" cy="180" r="4" fill="#e74c3c"/>
                    <circle cx="90" cy="185" r="4" fill="#e74c3c"/>
                    <circle cx="85" cy="175" r="4" fill="#e74c3c"/>
                    <circle cx="95" cy="180" r="4" fill="#e74c3c"/>
                    
                    <!-- Cluster 3 -->
                    <circle cx="180" cy="80" r="4" fill="#2ecc71"/>
                    <circle cx="190" cy="85" r="4" fill="#2ecc71"/>
                    <circle cx="185" cy="75" r="4" fill="#2ecc71"/>
                    <circle cx="195" cy="80" r="4" fill="#2ecc71"/>
                    
                    <!-- Cluster 4 -->
                    <circle cx="180" cy="180" r="4" fill="#f39c12"/>
                    <circle cx="190" cy="185" r="4" fill="#f39c12"/>
                    <circle cx="185" cy="175" r="4" fill="#f39c12"/>
                    <circle cx="195" cy="180" r="4" fill="#f39c12"/>
                    
                    <!-- Global structure -->
                    <path d="M100,100 L100,160 L160,160 L160,100 Z" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,3" fill="none"/>
                    <text x="130" y="130" text-anchor="middle" font-size="12" fill="#9b59b6">Global</text>
                    <text x="130" y="145" text-anchor="middle" font-size="12" fill="#9b59b6">Structure</text>
                    
                    <!-- UMAP structure -->
                    <!-- Cluster 1 -->
                    <circle cx="380" cy="80" r="4" fill="#3498db"/>
                    <circle cx="390" cy="85" r="4" fill="#3498db"/>
                    <circle cx="385" cy="75" r="4" fill="#3498db"/>
                    <circle cx="395" cy="80" r="4" fill="#3498db"/>
                    
                    <!-- Cluster 2 -->
                    <circle cx="380" cy="180" r="4" fill="#e74c3c"/>
                    <circle cx="390" cy="185" r="4" fill="#e74c3c"/>
                    <circle cx="385" cy="175" r="4" fill="#e74c3c"/>
                    <circle cx="395" cy="180" r="4" fill="#e74c3c"/>
                    
                    <!-- Cluster 3 -->
                    <circle cx="480" cy="80" r="4" fill="#2ecc71"/>
                    <circle cx="490" cy="85" r="4" fill="#2ecc71"/>
                    <circle cx="485" cy="75" r="4" fill="#2ecc71"/>
                    <circle cx="495" cy="80" r="4" fill="#2ecc71"/>
                    
                    <!-- Cluster 4 -->
                    <circle cx="480" cy="180" r="4" fill="#f39c12"/>
                    <circle cx="490" cy="185" r="4" fill="#f39c12"/>
                    <circle cx="485" cy="175" r="4" fill="#f39c12"/>
                    <circle cx="495" cy="180" r="4" fill="#f39c12"/>
                    
                    <!-- Preserved global structure -->
                    <path d="M400,100 L400,160 L460,160 L460,100 Z" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,3" fill="none"/>
                    
                    <!-- Arrow -->
                    <line x1="250" y1="150" x2="320" y2="150" stroke="#2c3e50" stroke-width="2"/>
                    <polygon points="320,150 310,145 310,155" fill="#2c3e50"/>
                    <text x="285" y="140" text-anchor="middle" font-size="12">UMAP</text>
                    
                    <!-- Labels -->
                    <text x="150" y="270" text-anchor="middle" font-size="12">High-dimensional data with</text>
                    <text x="150" y="285" text-anchor="middle" font-size="12">global structure</text>
                    
                    <text x="450" y="270" text-anchor="middle" font-size="12">UMAP preserves both local clusters</text>
                    <text x="450" y="285" text-anchor="middle" font-size="12">and global relationships</text>
                </svg>
                <div class="figure-caption">Figure 10: UMAP preserves both local cluster structure and global relationships between clusters.</div>
            </div>
            
            <h4>Computational Efficiency</h4>
            <p>UMAP is significantly faster than t-SNE, especially for large datasets. This efficiency comes from:</p>
            <ul>
                <li>Approximate nearest neighbor search</li>
                <li>Stochastic gradient descent with negative sampling</li>
                <li>No need for global normalization</li>
                <li>Efficient spectral initialization</li>
            </ul>
            
            <h4>Flexibility in Embedding Dimension</h4>
            <p>Unlike t-SNE, which is primarily designed for 2D or 3D visualization, UMAP has no computational restrictions on the embedding dimension. This makes it useful for:</p>
            <ul>
                <li>Visualization (2D or 3D)</li>
                <li>Dimension reduction as preprocessing for machine learning (any dimension)</li>
                <li>Feature extraction (typically 10-100 dimensions)</li>
            </ul>
            
            <h4>Scalability to High Dimensions</h4>
            <p>UMAP can work directly with high-dimensional data without requiring dimensionality reduction as preprocessing. This is particularly important for tasks where the original features contain important information that might be lost in preprocessing.</p>
            
            <h4>Theoretical Foundations</h4>
            <p>UMAP has strong mathematical foundations in Riemannian geometry and algebraic topology. This provides a solid theoretical understanding of how and why the algorithm works, which can guide parameter selection and interpretation of results.</p>
        </section>

        <section id="weaknesses">
            <h3>6.2 Limitations and Weaknesses</h3>
            <p>Despite its strengths, UMAP has several limitations that users should be aware of:</p>
            
            <h4>Limited Interpretability</h4>
            <p>Like most non-linear dimension reduction techniques, UMAP does not provide easily interpretable dimensions. Unlike PCA, where each dimension corresponds to a direction of maximum variance, UMAP dimensions do not have specific meanings.</p>
            
            <div class="warning">
                <p>If strong interpretability of the reduced dimensions is critical for your application, consider using linear techniques like PCA or NMF instead of UMAP.</p>
            </div>
            
            <h4>Sensitivity to Hyperparameters</h4>
            <p>UMAP's results can be sensitive to the choice of hyperparameters, particularly the number of neighbors and minimum distance. Different parameter settings can lead to different embeddings, which may emphasize different aspects of the data.</p>
            
            <div class="figure">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Parameter combinations -->
                    <rect x="50" y="50" width="150" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="125" y="30" text-anchor="middle" font-size="12">Small n_neighbors, Small min_dist</text>
                    
                    <rect x="225" y="50" width="150" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="300" y="30" text-anchor="middle" font-size="12">Small n_neighbors, Large min_dist</text>
                    
                    <rect x="400" y="50" width="150" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="475" y="30" text-anchor="middle" font-size="12">Large n_neighbors, Small min_dist</text>
                    
                    <rect x="225" y="225" width="150" height="150" fill="#e8f4f8" rx="5" ry="5" stroke="#3498db" stroke-width="1"/>
                    <text x="300" y="390" text-anchor="middle" font-size="12">Large n_neighbors, Large min_dist</text>
                    
                    <!-- Embeddings -->
                    <!-- Small n, Small min_dist -->
                    <circle cx="75" cy="75" r="3" fill="#3498db"/>
                    <circle cx="80" cy="73" r="3" fill="#3498db"/>
                    <circle cx="78" cy="78" r="3" fill="#3498db"/>
                    <circle cx="73" cy="80" r="3" fill="#3498db"/>
                    <circle cx="70" cy="75" r="3" fill="#3498db"/>
                    
                    <circle cx="125" cy="75" r="3" fill="#e74c3c"/>
                    <circle cx="130" cy="73" r="3" fill="#e74c3c"/>
                    <circle cx="128" cy="78" r="3" fill="#e74c3c"/>
                    <circle cx="123" cy="80" r="3" fill="#e74c3c"/>
                    <circle cx="120" cy="75" r="3" fill="#e74c3c"/>
                    
                    <circle cx="75" cy="125" r="3" fill="#2ecc71"/>
                    <circle cx="80" cy="123" r="3" fill="#2ecc71"/>
                    <circle cx="78" cy="128" r="3" fill="#2ecc71"/>
                    <circle cx="73" cy="130" r="3" fill="#2ecc71"/>
                    <circle cx="70" cy="125" r="3" fill="#2ecc71"/>
                    
                    <circle cx="125" cy="125" r="3" fill="#f39c12"/>
                    <circle cx="130" cy="123" r="3" fill="#f39c12"/>
                    <circle cx="128" cy="128" r="3" fill="#f39c12"/>
                    <circle cx="123" cy="130" r="3" fill="#f39c12"/>
                    <circle cx="120" cy="125" r="3" fill="#f39c12"/>
                    
                    <!-- Small n, Large min_dist -->
                    <circle cx="250" cy="75" r="3" fill="#3498db"/>
                    <circle cx="260" cy="70" r="3" fill="#3498db"/>
                    <circle cx="255" cy="85" r="3" fill="#3498db"/>
                    <circle cx="245" cy="90" r="3" fill="#3498db"/>
                    <circle cx="240" cy="80" r="3" fill="#3498db"/>
                    
                    <circle cx="300" cy="75" r="3" fill="#e74c3c"/>
                    <circle cx="310" cy="70" r="3" fill="#e74c3c"/>
                    <circle cx="305" cy="85" r="3" fill="#e74c3c"/>
                    <circle cx="295" cy="90" r="3" fill="#e74c3c"/>
                    <circle cx="290" cy="80" r="3" fill="#e74c3c"/>
                    
                    <circle cx="250" cy="125" r="3" fill="#2ecc71"/>
                    <circle cx="260" cy="120" r="3" fill="#2ecc71"/>
                    <circle cx="255" cy="135" r="3" fill="#2ecc71"/>
                    <circle cx="245" cy="140" r="3" fill="#2ecc71"/>
                    <circle cx="240" cy="130" r="3" fill="#2ecc71"/>
                    
                    <circle cx="300" cy="125" r="3" fill="#f39c12"/>
                    <circle cx="310" cy="120" r="3" fill="#f39c12"/>
                    <circle cx="305" cy="135" r="3" fill="#f39c12"/>
                    <circle cx="295" cy="140" r="3" fill="#f39c12"/>
                    <circle cx="290" cy="130" r="3" fill="#f39c12"/>
                    
                    <!-- Large n, Small min_dist -->
                    <circle cx="425" cy="75" r="3" fill="#3498db"/>
                    <circle cx="430" cy="73" r="3" fill="#3498db"/>
                    <circle cx="428" cy="78" r="3" fill="#3498db"/>
                    <circle cx="423" cy="80" r="3" fill="#3498db"/>
                    <circle cx="420" cy="75" r="3" fill="#3498db"/>
                    
                    <circle cx="475" cy="75" r="3" fill="#e74c3c"/>
                    <circle cx="480" cy="73" r="3" fill="#e74c3c"/>
                    <circle cx="478" cy="78" r="3" fill="#e74c3c"/>
                    <circle cx="473" cy="80" r="3" fill="#e74c3c"/>
                    <circle cx="470" cy="75" r="3" fill="#e74c3c"/>
                    
                    <circle cx="425" cy="125" r="3" fill="#2ecc71"/>
                    <circle cx="430" cy="123" r="3" fill="#2ecc71"/>
                    <circle cx="428" cy="128" r="3" fill="#2ecc71"/>
                    <circle cx="423" cy="130" r="3" fill="#2ecc71"/>
                    <circle cx="420" cy="125" r="3" fill="#2ecc71"/>
                    
                    <circle cx="475" cy="125" r="3" fill="#f39c12"/>
                    <circle cx="480" cy="123" r="3" fill="#f39c12"/>
                    <circle cx="478" cy="128" r="3" fill="#f39c12"/>
                    <circle cx="473" cy="130" r="3" fill="#f39c12"/>
                    <circle cx="470" cy="125" r="3" fill="#f39c12"/>
                    
                    <!-- Global structure -->
                    <path d="M425,75 L475,75 L475,125 L425,125 Z" stroke="#9b59b6" stroke-width="1" stroke-dasharray="3,2" fill="none"/>
                    
                    <!-- Large n, Large min_dist -->
                    <circle cx="250" cy="250" r="3" fill="#3498db"/>
                    <circle cx="260" cy="245" r="3" fill="#3498db"/>
                    <circle cx="255" cy="260" r="3" fill="#3498db"/>
                    <circle cx="245" cy="265" r="3" fill="#3498db"/>
                    <circle cx="240" cy="255" r="3" fill="#3498db"/>
                    
                    <circle cx="300" cy="250" r="3" fill="#e74c3c"/>
                    <circle cx="310" cy="245" r="3" fill="#e74c3c"/>
                    <circle cx="305" cy="260" r="3" fill="#e74c3c"/>
                    <circle cx="295" cy="265" r="3" fill="#e74c3c"/>
                    <circle cx="290" cy="255" r="3" fill="#e74c3c"/>
                    
                    <circle cx="250" cy="300" r="3" fill="#2ecc71"/>
                    <circle cx="260" cy="295" r="3" fill="#2ecc71"/>
                    <circle cx="255" cy="310" r="3" fill="#2ecc71"/>
                    <circle cx="245" cy="315" r="3" fill="#2ecc71"/>
                    <circle cx="240" cy="305" r="3" fill="#2ecc71"/>
                    
                    <circle cx="300" cy="300" r="3" fill="#f39c12"/>
                    <circle cx="310" cy="295" r="3" fill="#f39c12"/>
                    <circle cx="305" cy="310" r="3" fill="#f39c12"/>
                    <circle cx="295" cy="315" r="3" fill="#f39c12"/>
                    <circle cx="290" cy="305" r="3" fill="#f39c12"/>
                    
                    <!-- Global structure -->
                    <path d="M250,250 L300,250 L300,300 L250,300 Z" stroke="#9b59b6" stroke-width="1" stroke-dasharray="3,2" fill="none"/>
                    
                    <!-- Labels -->
                    <text x="125" y="170" text-anchor="middle" font-size="10">Tight clusters, fragmented</text>
                    <text x="125" y="185" text-anchor="middle" font-size="10">global structure</text>
                    
                    <text x="300" y="170" text-anchor="middle" font-size="10">Dispersed clusters, fragmented</text>
                    <text x="300" y="185" text-anchor="middle" font-size="10">global structure</text>
                    
                    <text x="475" y="170" text-anchor="middle" font-size="10">Tight clusters, preserved</text>
                    <text x="475" y="185" text-anchor="middle" font-size="10">global structure</text>
                    
                    <text x="300" y="345" text-anchor="middle" font-size="10">Dispersed clusters, preserved</text>
                    <text x="300" y="360" text-anchor="middle" font-size="10">global structure</text>
                </svg>
                <div class="figure-caption">Figure 11: Different parameter combinations can lead to different embeddings, emphasizing different aspects of the data structure.</div>
            </div>
            
            <h4>Finding Structure in Noise</h4>
            <p>UMAP can sometimes find structure in random noise, especially with small datasets and small values of n_neighbors. This is sometimes called the "constellation effect" because it resembles how humans find patterns (constellations) in random arrangements of stars.</p>
            
            <div class="note">
                <p>When working with small datasets, be cautious about interpreting apparent structure in UMAP embeddings. Consider using larger values of n_neighbors and validating findings with other methods.</p>
            </div>
            
            <h4>Stochastic Nature</h4>
            <p>UMAP is a stochastic algorithm, meaning that different runs can produce different results. While UMAP is generally more stable than t-SNE, particularly when using spectral initialization, the exact layout can still vary between runs.</p>
        </section>

        <section id="when">
            <h3>6.3 When to Use UMAP</h3>
            <p>UMAP is particularly well-suited for the following scenarios:</p>
            
            <h4>Visualization of High-Dimensional Data</h4>
            <p>UMAP excels at creating 2D or 3D visualizations of high-dimensional data that preserve both local and global structure. This makes it ideal for exploratory data analysis and understanding complex datasets.</p>
            
            <h4>Preprocessing for Machine Learning</h4>
            <p>UMAP can be used as a preprocessing step for machine learning algorithms, reducing the dimensionality of the data while preserving important structure. This can improve both the efficiency and performance of downstream algorithms.</p>
            
            <div class="figure">
                <svg width="700" height="200" viewBox="0 0 700 200">
                    <!-- Background -->
                    <rect width="700" height="200" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Pipeline -->
                    <rect x="50" y="70" width="120" height="60" rx="5" ry="5" fill="#e8f4f8" stroke="#3498db" stroke-width="2"/>
                    <text x="110" y="105" text-anchor="middle" font-size="14">High-dimensional Data</text>
                    
                    <polygon points="180,100 190,95 190,105" fill="#3498db"/>
                    <line x1="170" y1="100" x2="190" y2="100" stroke="#3498db" stroke-width="2"/>
                    
                    <rect x="200" y="70" width="120" height="60" rx="5" ry="5" fill="#f9e8e8" stroke="#e74c3c" stroke-width="2"/>
                    <text x="260" y="105" text-anchor="middle" font-size="14">UMAP</text>
                    
                    <polygon points="330,100 340,95 340,105" fill="#e74c3c"/>
                    <line x1="320" y1="100" x2="340" y2="100" stroke="#e74c3c" stroke-width="2"/>
                    
                    <rect x="350" y="70" width="120" height="60" rx="5" ry="5" fill="#eafaf1" stroke="#2ecc71" stroke-width="2"/>
                    <text x="410" y="105" text-anchor="middle" font-size="14">Machine Learning Algorithm</text>
                    
                    <polygon points="480,100 490,95 490,105" fill="#2ecc71"/>
                    <line x1="470" y1="100" x2="490" y2="100" stroke="#2ecc71" stroke-width="2"/>
                    
                    <rect x="500" y="70" width="120" height="60" rx="5" ry="5" fill="#fcf3cf" stroke="#f1c40f" stroke-width="2"/>
                    <text x="560" y="105" text-anchor="middle" font-size="14">Results</text>
                    
                    <!-- Labels -->
                    <text x="110" y="150" text-anchor="middle" font-size="12">High-dimensional</text>
                    <text x="110" y="165" text-anchor="middle" font-size="12">feature space</text>
                    
                    <text x="260" y="150" text-anchor="middle" font-size="12">Dimension</text>
                    <text x="260" y="165" text-anchor="middle" font-size="12">reduction</text>
                    
                    <text x="410" y="150" text-anchor="middle" font-size="12">Classification,</text>
                    <text x="410" y="165" text-anchor="middle" font-size="12">clustering, etc.</text>
                    
                    <text x="560" y="150" text-anchor="middle" font-size="12">Improved efficiency</text>
                    <text x="560" y="165" text-anchor="middle" font-size="12">and performance</text>
                </svg>
                <div class="figure-caption">Figure 12: UMAP as a preprocessing step in a machine learning pipeline.</div>
            </div>
            
            <h4>Large Datasets</h4>
            <p>UMAP's computational efficiency makes it suitable for large datasets that would be impractical to analyze with other dimension reduction techniques like t-SNE or Isomap.</p>
            
            <h4>High-Dimensional Data</h4>
            <p>Unlike many other dimension reduction techniques, UMAP can work directly with high-dimensional data without requiring dimensionality reduction as preprocessing.</p>
            
            <h4>When Not to Use UMAP</h4>
            <p>UMAP may not be the best choice in the following scenarios:</p>
            <ul>
                <li><strong>When interpretability is crucial</strong>: If you need to understand the meaning of each dimension in the reduced space, consider using linear techniques like PCA or NMF.</li>
                <li><strong>For very small datasets</strong>: With small datasets, UMAP may find structure in noise. Consider using simpler techniques or increasing the n_neighbors parameter.</li>
                <li><strong>When exact distances matter</strong>: UMAP focuses on preserving topology rather than exact distances. If preserving all pairwise distances is important, consider using MDS or similar techniques.</li>
            </ul>
            
            <div class="note">
                <p>In many cases, it's beneficial to use multiple dimension reduction techniques together. For example, you might use PCA for initial exploration and interpretation, followed by UMAP for more detailed visualization and structure discovery.</p>
            </div>
        </section>
    </section>
</body>
</html>