<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Integration Applications - Comprehensive Tutorial</title>
    
    <!-- MathJax 3 Configuration with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true,
                tags: 'ams'
            },
            svg: {
                fontCache: 'global',
                displayAlign: 'center',
                displayIndent: '0'
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            margin: 40px 0;
            padding: 30px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #007bff;
        }

        .section h2 {
            color: #007bff;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .section h3 {
            color: #495057;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .learning-objectives {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border-left: 5px solid #f39c12;
        }

        .learning-objectives h2 {
            color: white;
            border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        }

        .objectives-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .objective-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }

        .theorem-box {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: 2px solid #2980b9;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .theorem-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }

        .formula-box {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            border: 2px solid #8e44ad;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .formula-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }

        .example-box {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border: 2px solid #e67e22;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .example-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .application-box {
            background: linear-gradient(135deg, #16a085, #138d75);
            border: 2px solid #138d75;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .application-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .warning-box {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 2px solid #c0392b;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            border-left: 6px solid #f39c12;
        }

        .info-box {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            border-left: 6px solid #f1c40f;
        }

        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .tutorial-nav {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .tutorial-nav h3 {
            margin-bottom: 15px;
            text-align: center;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .nav-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .formula-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .formula-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-radius: 10px;
            padding: 20px;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>Integration Applications</h1>
            <p>From Basic Formulas to Real-World Problem Solving</p>
        </div>

        <!-- Tutorial Navigation -->
        <div class="tutorial-nav">
            <h3>Tutorial Outline</h3>
            <div class="nav-grid">
                <div class="nav-item">
                    <strong>Section 1:</strong><br>Introduction & Overview
                </div>
                <div class="nav-item">
                    <strong>Section 2:</strong><br>Basic Integration Formulas
                </div>
                <div class="nav-item">
                    <strong>Section 3:</strong><br>The Net Change Theorem
                </div>
                <div class="nav-item">
                    <strong>Section 4:</strong><br>Motion Analysis
                </div>
                <div class="nav-item">
                    <strong>Section 5:</strong><br>Applied Problems
                </div>
                <div class="nav-item">
                    <strong>Section 6:</strong><br>Even & Odd Functions
                </div>
                <div class="nav-item">
                    <strong>Section 7:</strong><br>Practice & Assessment
                </div>
            </div>
        </div>

        <!-- Learning Objectives -->
        <div class="section learning-objectives">
            <h2>Learning Objectives</h2>
            <div class="objectives-grid">
                <div class="objective-item">
                    <strong>5.4.1</strong> Apply the basic integration formulas to solve practical problems
                </div>
                <div class="objective-item">
                    <strong>5.4.2</strong> Explain the significance of the net change theorem in real-world contexts
                </div>
                <div class="objective-item">
                    <strong>5.4.3</strong> Use the net change theorem to solve applied problems in physics and engineering
                </div>
                <div class="objective-item">
                    <strong>5.4.4</strong> Apply the special properties of even and odd function integration
                </div>
            </div>
        </div>

        <!-- Section 1: Introduction & Overview -->
        <div class="section">
            <h2>Section 1: Introduction & Overview</h2>
            
            <p>You've mastered the theoretical foundations of integration—from Riemann sums to the Fundamental Theorem of Calculus. Now it's time to see integration in action! This section focuses on the practical applications of integration, showing how these mathematical tools solve real-world problems in physics, engineering, economics, and beyond.</p>

            <h3>The Power of Applied Integration</h3>
            <p>Integration is more than just finding antiderivatives—it's the key to understanding:</p>
            <ul style="margin: 15px 0 15px 30px;">
                <li><strong>Net Change:</strong> How quantities accumulate over time</li>
                <li><strong>Motion Analysis:</strong> Distance traveled, displacement, and velocity relationships</li>
                <li><strong>Resource Management:</strong> Consumption rates, flow rates, and optimization</li>
                <li><strong>Geometric Applications:</strong> Areas, volumes, and surface areas</li>
                <li><strong>Special Properties:</strong> Computational shortcuts using symmetry</li>
            </ul>

            <div class="svg-container">
                <svg width="800" height="450" viewBox="0 0 800 450">
                    <!-- Background -->
                    <rect width="800" height="450" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Integration Applications in the Real World</text>
                    
                    <!-- Physics section -->
                    <rect x="50" y="70" width="140" height="120" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="10"/>
                    <text x="120" y="95" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Physics</text>
                    <text x="120" y="115" text-anchor="middle" font-size="11" fill="white">• Velocity → Position</text>
                    <text x="120" y="130" text-anchor="middle" font-size="11" fill="white">• Acceleration → Velocity</text>
                    <text x="120" y="145" text-anchor="middle" font-size="11" fill="white">• Force → Work</text>
                    <text x="120" y="160" text-anchor="middle" font-size="11" fill="white">• Power → Energy</text>
                    <text x="120" y="175" text-anchor="middle" font-size="11" fill="white">• Displacement</text>
                    
                    <!-- Engineering section -->
                    <rect x="210" y="70" width="140" height="120" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="10"/>
                    <text x="280" y="95" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Engineering</text>
                    <text x="280" y="115" text-anchor="middle" font-size="11" fill="white">• Fluid Flow</text>
                    <text x="280" y="130" text-anchor="middle" font-size="11" fill="white">• Heat Transfer</text>
                    <text x="280" y="145" text-anchor="middle" font-size="11" fill="white">• Material Stress</text>
                    <text x="280" y="160" text-anchor="middle" font-size="11" fill="white">• Signal Processing</text>
                    <text x="280" y="175" text-anchor="middle" font-size="11" fill="white">• Volume Analysis</text>
                    
                    <!-- Economics section -->
                    <rect x="370" y="70" width="140" height="120" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="10"/>
                    <text x="440" y="95" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Economics</text>
                    <text x="440" y="115" text-anchor="middle" font-size="11" fill="white">• Consumer Surplus</text>
                    <text x="440" y="130" text-anchor="middle" font-size="11" fill="white">• Producer Surplus</text>
                    <text x="440" y="145" text-anchor="middle" font-size="11" fill="white">• Total Revenue</text>
                    <text x="440" y="160" text-anchor="middle" font-size="11" fill="white">• Cost Analysis</text>
                    <text x="440" y="175" text-anchor="middle" font-size="11" fill="white">• Profit Optimization</text>
                    
                    <!-- Biology section -->
                    <rect x="530" y="70" width="140" height="120" fill="#27ae60" stroke="#229954" stroke-width="2" rx="10"/>
                    <text x="600" y="95" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Biology</text>
                    <text x="600" y="115" text-anchor="middle" font-size="11" fill="white">• Population Growth</text>
                    <text x="600" y="130" text-anchor="middle" font-size="11" fill="white">• Drug Concentration</text>
                    <text x="600" y="145" text-anchor="middle" font-size="11" fill="white">• Blood Flow</text>
                    <text x="600" y="160" text-anchor="middle" font-size="11" fill="white">• Enzyme Kinetics</text>
                    <text x="600" y="175" text-anchor="middle" font-size="11" fill="white">• Biomass Calculation</text>
                    
                    <!-- Central integration symbol -->
                    <circle cx="400" cy="280" r="60" fill="#9b59b6" stroke="#8e44ad" stroke-width="3"/>
                    <text x="400" y="275" text-anchor="middle" font-size="24" font-weight="bold" fill="white">∫</text>
                    <text x="400" y="295" text-anchor="middle" font-size="12" fill="white">Integration</text>
                    
                    <!-- Arrows pointing to integration -->
                    <path d="M 190 150 Q 300 200 340 250" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 350 150 Q 380 200 380 220" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 510 150 Q 450 200 420 220" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    <path d="M 610 150 Q 500 200 460 250" stroke="#7f8c8d" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
                        </marker>
                    </defs>
                    
                    <!-- Bottom applications -->
                    <rect x="150" y="350" width="120" height="80" fill="#16a085" stroke="#138d75" stroke-width="2" rx="10"/>
                    <text x="210" y="375" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Geometry</text>
                    <text x="210" y="390" text-anchor="middle" font-size="10" fill="white">Areas & Volumes</text>
                    <text x="210" y="405" text-anchor="middle" font-size="10" fill="white">Surface Areas</text>
                    <text x="210" y="420" text-anchor="middle" font-size="10" fill="white">Arc Lengths</text>
                    
                    <rect x="290" y="350" width="120" height="80" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="10"/>
                    <text x="350" y="375" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Statistics</text>
                    <text x="350" y="390" text-anchor="middle" font-size="10" fill="white">Probability Density</text>
                    <text x="350" y="405" text-anchor="middle" font-size="10" fill="white">Expected Values</text>
                    <text x="350" y="420" text-anchor="middle" font-size="10" fill="white">Normal Distribution</text>
                    
                    <rect x="430" y="350" width="120" height="80" fill="#8e44ad" stroke="#7d3c98" stroke-width="2" rx="10"/>
                    <text x="490" y="375" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Technology</text>
                    <text x="490" y="390" text-anchor="middle" font-size="10" fill="white">Computer Graphics</text>
                    <text x="490" y="405" text-anchor="middle" font-size="10" fill="white">Signal Analysis</text>
                    <text x="490" y="420" text-anchor="middle" font-size="10" fill="white">Data Processing</text>
                    
                    <!-- Arrows from integration to bottom applications -->
                    <line x1="360" y1="330" x2="210" y2="350" stroke="#7f8c8d" stroke-width="2"/>
                    <line x1="400" y1="340" x2="350" y2="350" stroke="#7f8c8d" stroke-width="2"/>
                    <line x1="440" y1="330" x2="490" y2="350" stroke="#7f8c8d" stroke-width="2"/>
                </svg>
            </div>

            <h3>Definite vs. Indefinite Integrals: Choosing the Right Tool</h3>
            <p>As you dive into applications, it's crucial to understand when to use definite versus indefinite integrals:</p>

            <div class="warning-box">
                <strong>Key Distinction:</strong>
                <p><strong>Definite Integrals:</strong> Give you a specific number (net change, total accumulation)</p>
                <p><strong>Indefinite Integrals:</strong> Give you a family of functions (general antiderivatives)</p>
                <p>Choose based on what your problem is asking for!</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Background -->
                    <rect width="700" height="300" fill="white"/>
                    
                    <!-- Definite integral side -->
                    <rect x="50" y="50" width="280" height="200" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                    <text x="190" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Definite Integrals</text>
                    <text x="190" y="105" text-anchor="middle" font-size="14" fill="#2c3e50">∫ₐᵇ f(x)dx = Number</text>
                    
                    <text x="70" y="130" font-size="12" fill="#2c3e50" font-weight="bold">Use When You Need:</text>
                    <text x="70" y="150" font-size="11" fill="#2c3e50">• Total distance traveled</text>
                    <text x="70" y="165" font-size="11" fill="#2c3e50">• Net displacement</text>
                    <text x="70" y="180" font-size="11" fill="#2c3e50">• Area under curve</text>
                    <text x="70" y="195" font-size="11" fill="#2c3e50">• Volume of solid</text>
                    <text x="70" y="210" font-size="11" fill="#2c3e50">• Total consumption</text>
                    <text x="70" y="225" font-size="11" fill="#2c3e50">• Work done by force</text>
                    
                    <!-- Indefinite integral side -->
                    <rect x="370" y="50" width="280" height="200" fill="#fff4e6" stroke="#f39c12" stroke-width="2" rx="10"/>
                    <text x="510" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Indefinite Integrals</text>
                    <text x="510" y="105" text-anchor="middle" font-size="14" fill="#2c3e50">∫ f(x)dx = F(x) + C</text>
                    
                    <text x="390" y="130" font-size="12" fill="#2c3e50" font-weight="bold">Use When You Need:</text>
                    <text x="390" y="150" font-size="11" fill="#2c3e50">• Position function from velocity</text>
                    <text x="390" y="165" font-size="11" fill="#2c3e50">• Velocity function from acceleration</text>
                    <text x="390" y="180" font-size="11" fill="#2c3e50">• General antiderivative</text>
                    <text x="390" y="195" font-size="11" fill="#2c3e50">• Family of solutions</text>
                    <text x="390" y="210" font-size="11" fill="#2c3e50">• Function with unknown constant</text>
                    <text x="390" y="225" font-size="11" fill="#2c3e50">• Preliminary step in analysis</text>
                    
                    <!-- Central separator -->
                    <line x1="350" y1="50" x2="350" y2="250" stroke="#bdc3c7" stroke-width="2"/>
                    <text x="350" y="30" text-anchor="middle" font-size="14" font-weight="bold" fill="#7f8c8d">vs.</text>
                </svg>
            </div>

            <h3>What Makes This Section Special</h3>
            <p>This tutorial goes beyond basic computation to show you:</p>
            
            <div class="info-box">
                <strong>Practical Skills You'll Develop:</strong>
                <br>• How to set up integrals for real-world problems
                <br>• When and how to apply the Net Change Theorem
                <br>• Techniques for analyzing motion and displacement
                <br>• Shortcuts using even and odd function properties
                <br>• Problem-solving strategies for complex applications
                <br>• Connections between mathematical theory and practical solutions
            </div>

            <h3>Real-World Problem Preview</h3>
            <p>Here's a taste of the problems you'll be able to solve by the end of this tutorial:</p>

            <div class="application-box">
                <h4>Sample Applications</h4>
                <p><strong>Iceboat Racing:</strong> Calculate how far a racer travels when wind speed varies according to $v(t) = 20t + 5$ for the first half hour, then remains constant at 15 mph.</p>
                
                <p><strong>Gasoline Consumption:</strong> Determine total fuel used when a motorboat consumes gasoline at rate $5 - 0.1t^3$ gallons per hour.</p>
                
                <p><strong>Projectile Motion:</strong> Find the height and velocity of a ball thrown upward with initial speed 40 m/sec from 1.5 m height.</p>
                
                <p><strong>Symmetry Shortcuts:</strong> Evaluate $\int_{-2}^2 (3x^8 - 2)dx$ instantly using even function properties!</p>
            </div>

            <p>Ready to see integration transform from abstract mathematics into a powerful problem-solving toolkit? Let's begin with a review of the essential integration formulas you'll need.</p>
        </div>

        <!-- Section 2: Basic Integration Formulas -->
        <div class="section">
            <h2>Section 2: Basic Integration Formulas</h2>
            
            <p>Before diving into complex applications, let's review the fundamental integration formulas that will be your building blocks for solving real-world problems. These formulas are the computational foundation for all the exciting applications we'll explore.</p>

            <h3>Essential Integration Formula Reference</h3>
            
            <div class="formula-grid">
                <div class="formula-card">
                    <h4>Power Rule</h4>
                    <p>$$\int x^n dx = \frac{x^{n+1}}{n+1} + C$$</p>
                    <p style="font-size: 0.9em; margin-top: 10px;">where $n \neq -1$</p>
                </div>
                
                <div class="formula-card">
                    <h4>Logarithmic</h4>
                    <p>$$\int \frac{1}{x} dx = \ln|x| + C$$</p>
                    <p style="font-size: 0.9em; margin-top: 10px;">$x \neq 0$</p>
                </div>
                
                <div class="formula-card">
                    <h4>Exponential</h4>
                    <p>$$\int e^x dx = e^x + C$$</p>
                    <p>$$\int a^x dx = \frac{a^x}{\ln a} + C$$</p>
                </div>
                
                <div class="formula-card">
                    <h4>Trigonometric</h4>
                    <p>$$\int \sin x dx = -\cos x + C$$</p>
                    <p>$$\int \cos x dx = \sin x + C$$</p>
                </div>
            </div>

            <h3>Properties of Definite Integrals</h3>
            <p>These properties are crucial for solving application problems efficiently:</p>

            <div class="theorem-box">
                <h4>Key Properties for Applications</h4>
                <p><strong>Linearity:</strong> $\int_a^b [f(x) + g(x)]dx = \int_a^b f(x)dx + \int_a^b g(x)dx$</p>
                <p><strong>Scalar Multiplication:</strong> $\int_a^b kf(x)dx = k\int_a^b f(x)dx$</p>
                <p><strong>Additivity:</strong> $\int_a^c f(x)dx = \int_a^b f(x)dx + \int_b^c f(x)dx$</p>
                <p><strong>Sign Reversal:</strong> $\int_a^b f(x)dx = -\int_b^a f(x)dx$</p>
            </div>

            <h3>Worked Example: Power Rule Application</h3>
            <p>Let's work through a practical example that demonstrates how basic formulas lead to real-world solutions:</p>

            <div class="example-box">
                <h4>Example: Integrating Using Power Rule</h4>
                <p><strong>Problem:</strong> Evaluate $\int_1^4 t\sqrt{1+t} dt$</p>
                
                <p><strong>Solution Strategy:</strong></p>
                <p>First, we need to rewrite this in a form where we can apply the power rule. Let's use substitution:</p>
                
                <p><strong>Step 1:</strong> Let $u = 1 + t$, so $du = dt$ and $t = u - 1$</p>
                <p>When $t = 1$: $u = 2$</p>
                <p>When $t = 4$: $u = 5$</p>
                
                <p><strong>Step 2:</strong> Substitute:</p>
                <p>$$\int_1^4 t\sqrt{1+t} dt = \int_2^5 (u-1)\sqrt{u} du = \int_2^5 (u-1)u^{1/2} du$$</p>
                
                <p><strong>Step 3:</strong> Expand and apply power rule:</p>
                <p>$$= \int_2^5 (u^{3/2} - u^{1/2}) du$$</p>
                <p>$$= \left[\frac{u^{5/2}}{5/2} - \frac{u^{3/2}}{3/2}\right]_2^5$$</p>
                <p>$$= \left[\frac{2u^{5/2}}{5} - \frac{2u^{3/2}}{3}\right]_2^5$$</p>
                
                <p><strong>Step 4:</strong> Evaluate:</p>
                <p>$$= \left(\frac{2(5)^{5/2}}{5} - \frac{2(5)^{3/2}}{3}\right) - \left(\frac{2(2)^{5/2}}{5} - \frac{2(2)^{3/2}}{3}\right)$$</p>
                <p>$$= \left(\frac{2 \cdot 5^2\sqrt{5}}{5} - \frac{2 \cdot 5\sqrt{5}}{3}\right) - \left(\frac{2 \cdot 4\sqrt{2}}{5} - \frac{2 \cdot 2\sqrt{2}}{3}\right)$$</p>
                <p>$$= 10\sqrt{5} - \frac{10\sqrt{5}}{3} - \frac{8\sqrt{2}}{5} + \frac{4\sqrt{2}}{3}$$</p>
                
                <p><strong>Answer:</strong> $\frac{20\sqrt{5}}{3} - \frac{8\sqrt{2}}{15} = \frac{100\sqrt{5} - 8\sqrt{2}}{15}$</p>
            </div>

            <h3>Practice Problem</h3>
            <p>Try this problem using the techniques we just demonstrated:</p>

            <div class="application-box">
                <h4>Checkpoint Problem</h4>
                <p><strong>Find:</strong> $\int_1^3 (x^2 - 3x) dx$</p>
                
                <p><strong>Step-by-Step Solution:</strong></p>
                <p><strong>Step 1:</strong> Apply linearity property:</p>
                <p>$$\int_1^3 (x^2 - 3x) dx = \int_1^3 x^2 dx - 3\int_1^3 x dx$$</p>
                
                <p><strong>Step 2:</strong> Apply power rule to each term:</p>
                <p>$$= \left[\frac{x^3}{3}\right]_1^3 - 3\left[\frac{x^2}{2}\right]_1^3$$</p>
                
                <p><strong>Step 3:</strong> Evaluate using Fundamental Theorem:</p>
                <p>$$= \left(\frac{27}{3} - \frac{1}{3}\right) - 3\left(\frac{9}{2} - \frac{1}{2}\right)$$</p>
                <p>$$= \left(9 - \frac{1}{3}\right) - 3\left(\frac{8}{2}\right)$$</p>
                <p>$$= \frac{26}{3} - 12 = \frac{26-36}{3} = -\frac{10}{3}$$</p>
                
                <p><strong>Interpretation:</strong> The negative result indicates that the area below the x-axis is greater than the area above it in the interval [1,3].</p>
            </div>

            <h3>Visual Understanding: Area and Net Area</h3>
            <p>Understanding what definite integrals represent geometrically is crucial for applications:</p>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Geometric Interpretation: f(x) = x² - 3x</text>
                    
                    <!-- Coordinate system -->
                    <g transform="translate(350, 200)">
                        <!-- Axes -->
                        <line x1="-300" y1="0" x2="300" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-150" x2="0" y2="150" stroke="#34495e" stroke-width="2"/>
                        
                        <!-- Grid lines -->
                        <defs>
                            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                                <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#ecf0f1" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect x="-300" y="-150" width="600" height="300" fill="url(#grid)"/>
                        
                        <!-- Axis labels -->
                        <text x="280" y="-10" font-size="14" fill="#2c3e50">x</text>
                        <text x="10" y="-130" font-size="14" fill="#2c3e50">y</text>
                        
                        <!-- Tick marks and labels -->
                        <g stroke="#34495e" stroke-width="1">
                            <line x1="-200" y1="-5" x2="-200" y2="5"/>
                            <text x="-200" y="20" text-anchor="middle" font-size="12" fill="#2c3e50">-2</text>
                            
                            <line x1="-100" y1="-5" x2="-100" y2="5"/>
                            <text x="-100" y="20" text-anchor="middle" font-size="12" fill="#2c3e50">-1</text>
                            
                            <line x1="100" y1="-5" x2="100" y2="5"/>
                            <text x="100" y="20" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                            
                            <line x1="200" y1="-5" x2="200" y2="5"/>
                            <text x="200" y="20" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                            
                            <line x1="300" y1="-5" x2="300" y2="5"/>
                            <text x="300" y="20" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                            
                            <line x1="-5" y1="-100" x2="5" y2="-100"/>
                            <text x="-20" y="-95" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                            
                            <line x1="-5" y1="100" x2="5" y2="100"/>
                            <text x="-20" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">-2</text>
                        </g>
                        
                        <!-- Parabola y = x² - 3x -->
                        <path d="M -250 700 Q -150 250 -50 50 Q 0 -37.5 50 0 Q 100 12.5 150 50 Q 200 112.5 250 200 Q 275 243.75 300 300" 
                              stroke="#e74c3c" stroke-width="3" fill="none"/>
                        
                        <!-- Area under curve from x=1 to x=3 (negative area) -->
                        <path d="M 100 0 Q 125 12.5 150 50 Q 175 87.5 200 112.5 Q 225 137.5 250 200 Q 275 243.75 300 300 L 300 0 Z" 
                              fill="rgba(231, 76, 60, 0.3)" stroke="#e74c3c" stroke-width="2"/>
                        
                        <!-- Integration bounds -->
                        <line x1="100" y1="-120" x2="100" y2="120" stroke="#f39c12" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="300" y1="-120" x2="300" y2="120" stroke="#f39c12" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <!-- Labels -->
                        <text x="100" y="-130" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">x = 1</text>
                        <text x="300" y="-130" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">x = 3</text>
                        
                        <!-- Function label -->
                        <text x="-200" y="-120" font-size="14" font-weight="bold" fill="#e74c3c">f(x) = x² - 3x</text>
                        
                        <!-- Area label -->
                        <text x="200" y="70" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Area = -10/3</text>
                        <text x="200" y="85" text-anchor="middle" font-size="10" fill="#2c3e50">(Negative because below x-axis)</text>
                    </g>
                </svg>
            </div>

            <div class="info-box">
                <strong>Key Insight:</strong> When a function is negative over an interval, the definite integral gives the negative of the geometric area. This "signed area" concept is fundamental to understanding applications like net displacement versus total distance.
            </div>

            <h3>Building Toward Applications</h3>
            <p>These basic formulas and concepts form the foundation for everything we'll do next. Notice how:</p>
            <ul style="margin: 15px 0 15px 30px;">
                <li>The <strong>power rule</strong> handles polynomial rates (like velocity, acceleration)</li>
                <li>The <strong>exponential formulas</strong> model growth and decay processes</li>
                <li>The <strong>trigonometric formulas</strong> appear in oscillatory motion</li>
                <li>The <strong>properties</strong> let us break complex problems into manageable pieces</li>
                <li>The <strong>geometric interpretation</strong> helps us understand what our answers mean physically</li>
            </ul>

            <p>Now that we have our computational tools ready, let's explore the powerful Net Change Theorem—the bridge between mathematical integration and real-world problem solving!</p>
        </div>

        <!-- Section 3: The Net Change Theorem -->
        <div class="section">
            <h2>Section 3: The Net Change Theorem</h2>
            
            <p>The Net Change Theorem is perhaps the most practically important result in all of calculus. It directly connects rates of change to total accumulation, making it the foundation for solving countless real-world problems. This theorem transforms integration from an abstract mathematical operation into a powerful tool for understanding how quantities change over time.</p>

            <h3>Understanding the Concept</h3>
            <p>Imagine you're tracking the water level in a tank. If you know the rate at which water flows in and out (the rate of change), the Net Change Theorem tells you exactly how much the water level changes over any time period. This is the essence of why integration is so powerful!</p>

            <div class="theorem-box">
                <h4>The Net Change Theorem</h4>
                <p>The net change in a quantity equals the integral of its rate of change:</p>
                <p>$$F(b) = F(a) + \int_a^b F'(x)dx$$</p>
                <p><strong>Equivalently:</strong></p>
                <p>$$\int_a^b F'(x)dx = F(b) - F(a)$$</p>
                <p><strong>In words:</strong> The new value equals the initial value plus the integral of the rate of change.</p>
            </div>

            <h3>Why This Theorem Is Revolutionary</h3>
            <p>Before this theorem, calculating total change required knowing the exact function. Now, if you only know the rate of change, you can still find the total change! This opens the door to solving problems where:</p>

            <div class="svg-container">
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="white"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Net Change Theorem: From Rate to Total Change</text>
                    
                    <!-- Left side: Rate function -->
                    <g transform="translate(100, 80)">
                        <rect x="0" y="0" width="200" height="240" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Given: Rate Function</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">F'(x) = rate of change</text>
                        
                        <!-- Rate function graph -->
                        <g transform="translate(20, 60)">
                            <!-- Axes -->
                            <line x1="0" y1="80" x2="160" y2="80" stroke="#34495e" stroke-width="1"/>
                            <line x1="20" y1="0" x2="20" y2="160" stroke="#34495e" stroke-width="1"/>
                            
                            <!-- Rate curve -->
                            <path d="M 20 80 Q 50 60 80 70 Q 110 80 140 65" stroke="#e74c3c" stroke-width="3" fill="none"/>
                            
                            <!-- Labels -->
                            <text x="150" y="85" font-size="10" fill="#2c3e50">x</text>
                            <text x="5" y="15" font-size="10" fill="#2c3e50">F'(x)</text>
                            <text x="80" y="50" text-anchor="middle" font-size="10" fill="#e74c3c">Rate of Change</text>
                        </g>
                        
                        <text x="100" y="220" text-anchor="middle" font-size="11" fill="#2c3e50">Examples: velocity, flow rate,</text>
                        <text x="100" y="235" text-anchor="middle" font-size="11" fill="#2c3e50">consumption rate, growth rate</text>
                    </g>
                    
                    <!-- Arrow -->
                    <g transform="translate(320, 200)">
                        <path d="M 0 0 L 120 0" stroke="#f39c12" stroke-width="4" marker-end="url(#arrow)"/>
                        <text x="60" y="-10" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Integrate</text>
                        <text x="60" y="20" text-anchor="middle" font-size="14" fill="#2c3e50">∫ₐᵇ F'(x)dx</text>
                    </g>
                    
                    <!-- Arrow marker -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
                        </marker>
                    </defs>
                    
                    <!-- Right side: Net change -->
                    <g transform="translate(500, 80)">
                        <rect x="0" y="0" width="200" height="240" fill="#e8f8f5" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Result: Net Change</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">F(b) - F(a) = total change</text>
                        
                        <!-- Net change visualization -->
                        <g transform="translate(20, 60)">
                            <!-- Before and after bars -->
                            <rect x="30" y="40" width="40" height="100" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                            <rect x="90" y="20" width="40" height="120" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                            
                            <!-- Labels -->
                            <text x="50" y="155" text-anchor="middle" font-size="10" fill="#2c3e50">Initial</text>
                            <text x="50" y="170" text-anchor="middle" font-size="10" fill="#2c3e50">F(a)</text>
                            
                            <text x="110" y="155" text-anchor="middle" font-size="10" fill="#2c3e50">Final</text>
                            <text x="110" y="170" text-anchor="middle" font-size="10" fill="#2c3e50">F(b)</text>
                            
                            <!-- Net change arrow -->
                            <path d="M 75 100 L 85 80" stroke="#e74c3c" stroke-width="3" marker-end="url(#smallarrow)"/>
                            <text x="80" y="70" text-anchor="middle" font-size="10" font-weight="bold" fill="#e74c3c">Net Change</text>
                        </g>
                        
                        <text x="100" y="220" text-anchor="middle" font-size="11" fill="#2c3e50">Examples: displacement, total</text>
                        <text x="100" y="235" text-anchor="middle" font-size="11" fill="#2c3e50">consumption, volume change</text>
                    </g>
                    
                    <!-- Small arrow marker -->
                    <defs>
                        <marker id="smallarrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Classic Application: Velocity and Displacement</h3>
            <p>Let's start with the most intuitive application—motion. When you integrate velocity over time, you get displacement (net change in position).</p>

            <div class="example-box">
                <h4>Example: Car Motion Analysis</h4>
                <p><strong>Scenario:</strong> A car travels north at 40 mph from 2 PM to 4 PM, then travels south at 30 mph from 4 PM to 5 PM.</p>
                
                <p><strong>Setting Up the Problem:</strong></p>
                <p>Let's define our velocity function $v(t)$ where $t$ is hours after 2 PM:</p>
                <p>$$v(t) = \begin{cases}
                40 & \text{if } 0 \leq t \leq 2 \\
                -30 & \text{if } 2 < t \leq 3
                \end{cases}$$</p>
                
                <p><strong>Finding Net Displacement:</strong></p>
                <p>Using the Net Change Theorem, the net displacement is:</p>
                <p>$$\text{Net Displacement} = \int_0^3 v(t)dt = \int_0^2 40dt + \int_2^3 (-30)dt$$</p>
                <p>$$= 40(2-0) + (-30)(3-2) = 80 - 30 = 50 \text{ miles north}$$</p>
                
                <p><strong>Finding Total Distance:</strong></p>
                <p>For total distance, we integrate the absolute value:</p>
                <p>$$\text{Total Distance} = \int_0^3 |v(t)|dt = \int_0^2 40dt + \int_2^3 30dt$$</p>
                <p>$$= 40(2) + 30(1) = 80 + 30 = 110 \text{ miles}$$</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Background -->
                    <rect width="700" height="300" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Velocity vs. Time: Net Displacement vs. Total Distance</text>
                    
                    <!-- Coordinate system -->
                    <g transform="translate(350, 180)">
                        <!-- Axes -->
                        <line x1="-300" y1="0" x2="300" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-100" x2="0" y2="100" stroke="#34495e" stroke-width="2"/>
                        
                        <!-- Grid -->
                        <defs>
                            <pattern id="grid2" width="60" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 60 0 L 0 0 0 20" fill="none" stroke="#ecf0f1" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect x="-300" y="-100" width="600" height="200" fill="url(#grid2)"/>
                        
                        <!-- Axis labels -->
                        <text x="280" y="-10" font-size="12" fill="#2c3e50">t (hours)</text>
                        <text x="10" y="-80" font-size="12" fill="#2c3e50">v (mph)</text>
                        
                        <!-- Time labels -->
                        <text x="0" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">2 PM</text>
                        <text x="120" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">4 PM</text>
                        <text x="180" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">5 PM</text>
                        
                        <!-- Velocity function -->
                        <!-- Positive velocity segment -->
                        <rect x="0" y="-80" width="120" height="80" fill="rgba(39, 174, 96, 0.3)" stroke="#27ae60" stroke-width="2"/>
                        <line x1="0" y1="-80" x2="120" y2="-80" stroke="#27ae60" stroke-width="4"/>
                        <text x="60" y="-85" text-anchor="middle" font-size="11" font-weight="bold" fill="#27ae60">40 mph North</text>
                        
                        <!-- Negative velocity segment -->
                        <rect x="120" y="0" width="60" height="60" fill="rgba(231, 76, 60, 0.3)" stroke="#e74c3c" stroke-width="2"/>
                        <line x1="120" y1="60" x2="180" y2="60" stroke="#e74c3c" stroke-width="4"/>
                        <text x="150" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="#e74c3c">30 mph South</text>
                        
                        <!-- Time markers -->
                        <line x1="0" y1="-90" x2="0" y2="90" stroke="#7f8c8d" stroke-width="1" stroke-dasharray="3,3"/>
                        <line x1="120" y1="-90" x2="120" y2="90" stroke="#7f8c8d" stroke-width="1" stroke-dasharray="3,3"/>
                        <line x1="180" y1="-90" x2="180" y2="90" stroke="#7f8c8d" stroke-width="1" stroke-dasharray="3,3"/>
                        
                        <!-- Net displacement calculation -->
                        <text x="-250" y="-70" font-size="12" font-weight="bold" fill="#2c3e50">Net Displacement:</text>
                        <text x="-250" y="-55" font-size="11" fill="#27ae60">Positive area: 80 miles</text>
                        <text x="-250" y="-40" font-size="11" fill="#e74c3c">Negative area: -30 miles</text>
                        <text x="-250" y="-25" font-size="11" font-weight="bold" fill="#2c3e50">Net: 50 miles north</text>
                        
                        <!-- Total distance calculation -->
                        <text x="-250" y="30" font-size="12" font-weight="bold" fill="#2c3e50">Total Distance:</text>
                        <text x="-250" y="45" font-size="11" fill="#27ae60">|80| + |-30| = 110 miles</text>
                    </g>
                </svg>
            </div>

            <h3>Key Distinctions: Net Change vs. Total Change</h3>
            <p>The car example perfectly illustrates a crucial concept that appears throughout physics and engineering:</p>

            <div class="warning-box">
                <strong>Critical Distinction:</strong>
                <ul style="margin: 10px 0 0 20px;">
                    <li><strong>Net Change:</strong> $\int_a^b f(t)dt$ - accounts for direction (positive and negative values)</li>
                    <li><strong>Total Change:</strong> $\int_a^b |f(t)|dt$ - always positive, measures total magnitude</li>
                </ul>
                <p style="margin-top: 10px;">Choose based on what the problem is asking for!</p>
            </div>

            <h3>Practice with the Net Change Theorem</h3>
            <p>Let's work through a problem that demonstrates the power of this theorem:</p>

            <div class="application-box">
                <h4>Practice Problem: Particle Motion</h4>
                <p><strong>Given:</strong> A particle moves with velocity $v(t) = 3t - 5$ meters per second for $0 \leq t \leq 3$.</p>
                
                <p><strong>Find the net displacement:</strong></p>
                <p>$$\text{Net Displacement} = \int_0^3 v(t)dt = \int_0^3 (3t - 5)dt$$</p>
                <p>$$= \left[\frac{3t^2}{2} - 5t\right]_0^3$$</p>
                <p>$$= \left(\frac{3(9)}{2} - 5(3)\right) - \left(\frac{3(0)}{2} - 5(0)\right)$$</p>
                <p>$$= \frac{27}{2} - 15 = 13.5 - 15 = -1.5 \text{ meters}$$</p>
                
                <p><strong>Interpretation:</strong> The particle ends up 1.5 meters to the left of its starting position.</p>
                
                <p><strong>Find the total distance traveled:</strong></p>
                <p>First, find when $v(t) = 0$: $3t - 5 = 0 \Rightarrow t = \frac{5}{3}$</p>
                <p>The velocity changes sign at $t = \frac{5}{3}$, so we split the integral:</p>
                <p>$$\text{Total Distance} = \int_0^{5/3} |3t - 5|dt + \int_{5/3}^3 |3t - 5|dt$$</p>
                <p>$$= \int_0^{5/3} (5 - 3t)dt + \int_{5/3}^3 (3t - 5)dt$$</p>
                <p>$$= \left[5t - \frac{3t^2}{2}\right]_0^{5/3} + \left[\frac{3t^2}{2} - 5t\right]_{5/3}^3$$</p>
                <p>$$= \frac{25}{6} + \frac{8}{3} = \frac{25 + 16}{6} = \frac{41}{6} \approx 6.83 \text{ meters}$$</p>
            </div>

            <h3>The Broader Significance</h3>
            <p>The Net Change Theorem is the mathematical foundation for understanding:</p>
            
            <div class="info-box">
                <strong>Applications Across All Fields:</strong>
                <br>• <strong>Physics:</strong> Velocity → Displacement, Force → Work, Power → Energy
                <br>• <strong>Economics:</strong> Marginal cost → Total cost, Rate of revenue → Revenue
                <br>• <strong>Biology:</strong> Growth rate → Population change, Drug concentration rates
                <br>• <strong>Engineering:</strong> Flow rates → Volume, Heat transfer rates → Temperature change
                <br>• <strong>Environmental Science:</strong> Pollution rates → Total contamination
            </div>

            <p>The beauty of this theorem is that it provides a unified approach to all these seemingly different problems. Once you master the concept of "rate of change integrated equals net change," you have a powerful tool for solving problems across all scientific and engineering disciplines.</p>

            <p>Next, we'll dive deeper into motion analysis, exploring more sophisticated applications of the Net Change Theorem to real-world physics problems!</p>
        </div>

        <!-- Section 4: Motion Analysis -->
        <div class="section">
            <h2>Section 4: Motion Analysis</h2>
            
            <p>Motion analysis represents one of the most natural and important applications of integration. From projectiles to vehicles to particles, understanding how position, velocity, and acceleration relate through integration is fundamental to physics, engineering, and countless real-world applications.</p>

            <h3>The Calculus of Motion: Building Connections</h3>
            <p>Motion analysis beautifully demonstrates the inverse relationship between differentiation and integration:</p>

            <div class="svg-container">
                <svg width="600" height="250" viewBox="0 0 600 250">
                    <!-- Background -->
                    <rect width="600" height="250" fill="white"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">The Hierarchy of Motion</text>
                    
                    <!-- Position box -->
                    <rect x="50" y="60" width="140" height="80" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="10"/>
                    <text x="120" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Position</text>
                    <text x="120" y="105" text-anchor="middle" font-size="12" fill="white">s(t) or x(t)</text>
                    <text x="120" y="125" text-anchor="middle" font-size="11" fill="white">"Where am I?"</text>
                    
                    <!-- Velocity box -->
                    <rect x="230" y="60" width="140" height="80" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="10"/>
                    <text x="300" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Velocity</text>
                    <text x="300" y="105" text-anchor="middle" font-size="12" fill="white">v(t) = s'(t)</text>
                    <text x="300" y="125" text-anchor="middle" font-size="11" fill="white">"How fast am I going?"</text>
                    
                    <!-- Acceleration box -->
                    <rect x="410" y="60" width="140" height="80" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="10"/>
                    <text x="480" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Acceleration</text>
                    <text x="480" y="105" text-anchor="middle" font-size="12" fill="white">a(t) = v'(t)</text>
                    <text x="480" y="125" text-anchor="middle" font-size="11" fill="white">"How is my speed changing?"</text>
                    
                    <!-- Integration arrows -->
                    <path d="M 190 90 L 220 90" stroke="#27ae60" stroke-width="3" marker-end="url(#greenarrow)"/>
                    <text x="205" y="85" text-anchor="middle" font-size="10" font-weight="bold" fill="#27ae60">∫</text>
                    
                    <path d="M 370 90 L 400 90" stroke="#27ae60" stroke-width="3" marker-end="url(#greenarrow)"/>
                    <text x="385" y="85" text-anchor="middle" font-size="10" font-weight="bold" fill="#27ae60">∫</text>
                    
                    <!-- Differentiation arrows -->
                    <path d="M 220 110 L 190 110" stroke="#8e44ad" stroke-width="3" marker-end="url(#purplearrow)"/>
                    <text x="205" y="125" text-anchor="middle" font-size="10" font-weight="bold" fill="#8e44ad">d/dt</text>
                    
                    <path d="M 400 110 L 370 110" stroke="#8e44ad" stroke-width="3" marker-end="url(#purplearrow)"/>
                    <text x="385" y="125" text-anchor="middle" font-size="10" font-weight="bold" fill="#8e44ad">d/dt</text>
                    
                    <!-- Arrow definitions -->
                    <defs>
                        <marker id="greenarrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#27ae60"/>
                        </marker>
                        <marker id="purplearrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#8e44ad"/>
                        </marker>
                    </defs>
                    
                    <!-- Labels -->
                    <text x="120" y="170" text-anchor="middle" font-size="11" fill="#27ae60" font-weight="bold">Integrate to get position</text>
                    <text x="120" y="185" text-anchor="middle" font-size="11" fill="#8e44ad" font-weight="bold">Differentiate to get velocity</text>
                    
                    <text x="480" y="170" text-anchor="middle" font-size="11" fill="#27ae60" font-weight="bold">Integrate to get velocity</text>
                    <text x="480" y="185" text-anchor="middle" font-size="11" fill="#8e44ad" font-weight="bold">Differentiate to get jerk</text>
                </svg>
            </div>

            <h3>Complex Motion Example: Exponential Rates</h3>
            <p>Let's explore a more sophisticated example that showcases the power of the Net Change Theorem:</p>

            <div class="example-box">
                <h4>Example: Exponential Velocity Function</h4>
                <p><strong>Problem:</strong> Find the net displacement and total distance traveled given the velocity function $f(t) = \frac{1}{2}e^t - 2$ over the interval $[0, 2]$.</p>
                
                <p><strong>Step 1: Find Net Displacement</strong></p>
                <p>Using the Net Change Theorem:</p>
                <p>$$\text{Net Displacement} = \int_0^2 \left(\frac{1}{2}e^t - 2\right) dt$$</p>
                <p>$$= \left[\frac{1}{2}e^t - 2t\right]_0^2$$</p>
                <p>$$= \left(\frac{1}{2}e^2 - 4\right) - \left(\frac{1}{2}e^0 - 0\right)$$</p>
                <p>$$= \frac{1}{2}e^2 - 4 - \frac{1}{2} = \frac{1}{2}(e^2 - 1) - 4$$</p>
                <p>$$\approx \frac{1}{2}(7.389 - 1) - 4 = 3.194 - 4 = -0.806 \text{ meters}$$</p>
                
                <p><strong>Step 2: Find When Velocity Changes Sign</strong></p>
                <p>Set $f(t) = 0$: $\frac{1}{2}e^t - 2 = 0$</p>
                <p>$e^t = 4 \Rightarrow t = \ln(4) \approx 1.386$</p>
                
                <p><strong>Step 3: Calculate Total Distance</strong></p>
                <p>Since velocity changes sign at $t = \ln(4)$:</p>
                <p>$$\text{Total Distance} = \int_0^{\ln(4)} \left|f(t)\right| dt + \int_{\ln(4)}^2 \left|f(t)\right| dt$$</p>
                <p>$$= \int_0^{\ln(4)} \left(2 - \frac{1}{2}e^t\right) dt + \int_{\ln(4)}^2 \left(\frac{1}{2}e^t - 2\right) dt$$</p>
                <p>After calculation: Total Distance $\approx 1.614$ meters</p>
            </div>

            <h3>Real-World Application: Projectile Motion</h3>
            <p>Let's apply our integration skills to a classic physics problem:</p>

            <div class="application-box">
                <h4>Example: Ball Thrown Upward</h4>
                <p><strong>Problem:</strong> A ball is thrown upward from a height of 1.5 m with initial speed 40 m/sec. Acceleration due to gravity is $-9.8$ m/sec². Find the velocity and height functions, then determine when the ball hits the ground.</p>
                
                <p><strong>Given Information:</strong></p>
                <ul style="margin: 10px 0 10px 30px; color: white;">
                    <li>Initial height: $h_0 = 1.5$ m</li>
                    <li>Initial velocity: $v_0 = 40$ m/sec</li>
                    <li>Acceleration: $a(t) = -9.8$ m/sec²</li>
                </ul>
                
                <p><strong>Step 1: Find Velocity Function</strong></p>
                <p>Since $a(t) = v'(t) = -9.8$, we integrate to get velocity:</p>
                <p>$$v(t) = \int a(t) dt = \int (-9.8) dt = -9.8t + C$$</p>
                <p>Using initial condition $v(0) = 40$:</p>
                <p>$$40 = -9.8(0) + C \Rightarrow C = 40$$</p>
                <p>$$v(t) = 40 - 9.8t$$</p>
                
                <p><strong>Step 2: Find Position Function</strong></p>
                <p>Since $v(t) = h'(t)$, we integrate velocity to get height:</p>
                <p>$$h(t) = \int v(t) dt = \int (40 - 9.8t) dt = 40t - 4.9t^2 + C$$</p>
                <p>Using initial condition $h(0) = 1.5$:</p>
                <p>$$1.5 = 40(0) - 4.9(0)^2 + C \Rightarrow C = 1.5$$</p>
                <p>$$h(t) = 1.5 + 40t - 4.9t^2$$</p>
                
                <p><strong>Step 3: When Does the Ball Hit the Ground?</strong></p>
                <p>Set $h(t) = 0$:</p>
                <p>$$1.5 + 40t - 4.9t^2 = 0$$</p>
                <p>$$4.9t^2 - 40t - 1.5 = 0$$</p>
                <p>Using the quadratic formula: $t \approx 8.20$ seconds</p>
            </div>

            <h3>Visual Analysis: Complete Motion Profile</h3>
            <p>Let's visualize the complete motion of our projectile:</p>

            <div class="svg-container">
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="white"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Projectile Motion: Position, Velocity, and Acceleration</text>
                    
                    <!-- Position graph -->
                    <g transform="translate(100, 80)">
                        <rect x="0" y="0" width="180" height="140" fill="#f8f9fa" stroke="#3498db" stroke-width="1"/>
                        <text x="90" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Position h(t)</text>
                        
                        <!-- Axes -->
                        <line x1="20" y1="120" x2="160" y2="120" stroke="#34495e" stroke-width="1"/>
                        <line x1="20" y1="30" x2="20" y2="120" stroke="#34495e" stroke-width="1"/>
                        
                        <!-- Parabola -->
                        <path d="M 20 115 Q 50 60 80 30 Q 110 20 140 35 Q 150 45 160 80" stroke="#3498db" stroke-width="3" fill="none"/>
                        
                        <!-- Labels -->
                        <text x="90" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">t (sec)</text>
                        <text x="10" y="80" text-anchor="middle" font-size="10" fill="#2c3e50">h</text>
                        <text x="25" y="45" font-size="8" fill="#3498db">Max height</text>
                        <text x="155" y="95" font-size="8" fill="#3498db">Ground</text>
                    </g>
                    
                    <!-- Velocity graph -->
                    <g transform="translate(310, 80)">
                        <rect x="0" y="0" width="180" height="140" fill="#f8f9fa" stroke="#e74c3c" stroke-width="1"/>
                        <text x="90" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Velocity v(t)</text>
                        
                        <!-- Axes -->
                        <line x1="20" y1="120" x2="160" y2="120" stroke="#34495e" stroke-width="1"/>
                        <line x1="20" y1="30" x2="20" y2="120" stroke="#34495e" stroke-width="1"/>
                        <line x1="20" y1="75" x2="160" y2="75" stroke="#bdc3c7" stroke-width="1" stroke-dasharray="2,2"/>
                        
                        <!-- Linear function -->
                        <line x1="20" y1="40" x2="160" y2="110" stroke="#e74c3c" stroke-width="3"/>
                        
                        <!-- Labels -->
                        <text x="90" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">t (sec)</text>
                        <text x="10" y="80" text-anchor="middle" font-size="10" fill="#2c3e50">v</text>
                        <text x="25" y="50" font-size="8" fill="#e74c3c">v₀ = 40</text>
                        <text x="100" y="65" font-size="8" fill="#e74c3c">v = 0</text>
                        <text x="145" y="105" font-size="8" fill="#e74c3c">Negative</text>
                    </g>
                    
                    <!-- Acceleration graph -->
                    <g transform="translate(520, 80)">
                        <rect x="0" y="0" width="180" height="140" fill="#f8f9fa" stroke="#f39c12" stroke-width="1"/>
                        <text x="90" y="15" text-anchor="middle" font-size="12" font-weight="bold" fill="#f39c12">Acceleration a(t)</text>
                        
                        <!-- Axes -->
                        <line x1="20" y1="120" x2="160" y2="120" stroke="#34495e" stroke-width="1"/>
                        <line x1="20" y1="30" x2="20" y2="120" stroke="#34495e" stroke-width="1"/>
                        
                        <!-- Constant acceleration -->
                        <line x1="20" y1="90" x2="160" y2="90" stroke="#f39c12" stroke-width="4"/>
                        
                        <!-- Labels -->
                        <text x="90" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">t (sec)</text>
                        <text x="10" y="80" text-anchor="middle" font-size="10" fill="#2c3e50">a</text>
                        <text x="90" y="105" text-anchor="middle" font-size="8" fill="#f39c12">a = -9.8 m/s²</text>
                    </g>
                    
                    <!-- Analysis summary -->
                    <g transform="translate(50, 250)">
                        <rect x="0" y="0" width="700" height="120" fill="#ecf0f1" stroke="#95a5a6" stroke-width="1" rx="5"/>
                        <text x="350" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Motion Analysis Summary</text>
                        
                        <text x="20" y="45" font-size="12" fill="#2c3e50"><tspan font-weight="bold">Key Insights:</tspan></text>
                        <text x="20" y="65" font-size="11" fill="#2c3e50">• Position function: h(t) = 1.5 + 40t - 4.9t² (parabolic due to constant acceleration)</text>
                        <text x="20" y="80" font-size="11" fill="#2c3e50">• Velocity function: v(t) = 40 - 9.8t (linear due to constant acceleration)</text>
                        <text x="20" y="95" font-size="11" fill="#2c3e50">• Acceleration function: a(t) = -9.8 (constant due to gravity)</text>
                        <text x="20" y="110" font-size="11" fill="#2c3e50">• Maximum height reached when v(t) = 0, ball hits ground when h(t) = 0</text>
                    </g>
                </svg>
            </div>

            <h3>Advanced Motion: Variable Acceleration</h3>
            <p>Not all motion involves constant acceleration. Let's examine a case with variable acceleration:</p>

            <div class="application-box">
                <h4>Example: Variable Acceleration</h4>
                <p><strong>Problem:</strong> A particle moves with acceleration $a(t) = t - 3$ m/sec² for $0 \leq t \leq 6$. If $v(0) = 3$ m/sec and $s(0) = 0$, find the velocity and position functions, and determine the total distance traveled.</p>
                
                <p><strong>Step 1: Find Velocity Function</strong></p>
                <p>$$v(t) = \int a(t) dt = \int (t - 3) dt = \frac{t^2}{2} - 3t + C$$</p>
                <p>Using $v(0) = 3$: $C = 3$</p>
                <p>$$v(t) = \frac{t^2}{2} - 3t + 3$$</p>
                
                <p><strong>Step 2: Find Position Function</strong></p>
                <p>$$s(t) = \int v(t) dt = \int \left(\frac{t^2}{2} - 3t + 3\right) dt = \frac{t^3}{6} - \frac{3t^2}{2} + 3t + C$$</p>
                <p>Using $s(0) = 0$: $C = 0$</p>
                <p>$$s(t) = \frac{t^3}{6} - \frac{3t^2}{2} + 3t$$</p>
                
                <p><strong>Step 3: Find When Velocity Changes Sign</strong></p>
                <p>$v(t) = 0$ when $\frac{t^2}{2} - 3t + 3 = 0$</p>
                <p>$t^2 - 6t + 6 = 0$</p>
                <p>$t = 3 \pm \sqrt{3} \approx 1.27, 4.73$</p>
                
                <p><strong>Step 4: Calculate Total Distance</strong></p>
                <p>The particle changes direction at $t \approx 1.27$ and $t \approx 4.73$, so we need to evaluate the position function at these points and take absolute differences.</p>
            </div>

            <h3>Problem-Solving Strategy for Motion</h3>
            <p>When tackling motion problems, follow this systematic approach:</p>

            <div class="info-box">
                <strong>Motion Analysis Checklist:</strong>
                <br>1. <strong>Identify Given Information:</strong> What functions are provided? Initial conditions?
                <br>2. <strong>Determine What's Needed:</strong> Position, velocity, acceleration, distance, displacement?
                <br>3. <strong>Apply Integration/Differentiation:</strong> Use the motion hierarchy relationships
                <br>4. <strong>Use Initial Conditions:</strong> Find constants of integration
                <br>5. <strong>Check Sign Changes:</strong> For total distance calculations
                <br>6. <strong>Interpret Results:</strong> What do the answers mean physically?
            </div>

            <p>Motion analysis demonstrates the practical power of integration in solving real-world physics problems. From simple projectiles to complex orbital mechanics, these techniques form the foundation of classical mechanics. Next, we'll expand our scope to explore how integration solves problems involving fluid flow, consumption rates, and other applied scenarios!</p>
        </div>

        <!-- Section 5: Applied Problems -->
        <div class="section">
            <h2>Section 5: Applied Problems</h2>
            
            <p>The Net Change Theorem extends far beyond motion problems. From gasoline consumption to fluid flow to geometric applications, integration provides a unified approach to solving diverse real-world problems.</p>

            <h3>Fluid Consumption and Flow</h3>
            
            <div class="example-box">
                <h4>Example: Motorboat Gasoline Consumption</h4>
                <p><strong>Problem:</strong> A motorboat consumes gasoline at the rate of $5 - 0.1t^3$ gallons per hour. How much gasoline is used in the first 2 hours?</p>
                
                <p><strong>Solution:</strong> Using the Net Change Theorem:</p>
                <p>$$\text{Total Consumption} = \int_0^2 (5 - 0.1t^3) dt$$</p>
                <p>$$= \left[5t - 0.1 \cdot \frac{t^4}{4}\right]_0^2 = \left[5t - 0.025t^4\right]_0^2$$</p>
                <p>$$= (10 - 0.025 \cdot 16) - (0) = 10 - 0.4 = 9.6 \text{ gallons}$$</p>
            </div>

            <h3>The Iceboat Problem</h3>
            <p>This classic problem demonstrates how piecewise functions arise in real applications:</p>

            <div class="application-box">
                <h4>Example: Iceboat Racing with Variable Wind</h4>
                <p><strong>Problem:</strong> Andrew's iceboat travels at twice the wind speed. The wind speed is:</p>
                <p>$$v_{\text{wind}}(t) = \begin{cases}
                20t + 5 & \text{for } 0 \leq t \leq 0.5 \\
                15 & \text{for } 0.5 < t \leq 1
                \end{cases}$$</p>
                
                <p><strong>Andrew's velocity:</strong> $v_{\text{boat}}(t) = 2 \cdot v_{\text{wind}}(t)$</p>
                
                <p><strong>Distance calculation:</strong></p>
                <p>$$\text{Distance} = \int_0^{0.5} 2(20t + 5) dt + \int_{0.5}^1 2(15) dt$$</p>
                <p>$$= \int_0^{0.5} (40t + 10) dt + \int_{0.5}^1 30 dt$$</p>
                <p>$$= \left[20t^2 + 10t\right]_0^{0.5} + [30t]_{0.5}^1$$</p>
                <p>$$= (5 + 5) + (30 - 15) = 10 + 15 = 25 \text{ miles}$$</p>
            </div>

            <h3>Geometric Applications</h3>
            
            <div class="svg-container">
                <svg width="700" height="350" viewBox="0 0 700 350">
                    <!-- Background -->
                    <rect width="700" height="350" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Geometric Applications of Integration</text>
                    
                    <!-- Volume applications -->
                    <g transform="translate(50, 60)">
                        <rect x="0" y="0" width="150" height="100" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Volume Changes</text>
                        <text x="75" y="45" text-anchor="middle" font-size="11" fill="white">Cube: s → 2s</text>
                        <text x="75" y="60" text-anchor="middle" font-size="11" fill="white">∫₁² 3s² ds = 7s³ units</text>
                        <text x="75" y="85" text-anchor="middle" font-size="10" fill="white">Volume increases by 7 times</text>
                    </g>
                    
                    <!-- Surface area -->
                    <g transform="translate(220, 60)">
                        <rect x="0" y="0" width="150" height="100" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Surface Area</text>
                        <text x="75" y="45" text-anchor="middle" font-size="11" fill="white">Sphere: R → 2R</text>
                        <text x="75" y="60" text-anchor="middle" font-size="11" fill="white">∫ᴿ²ᴿ 8πr dr = 12πR²</text>
                        <text x="75" y="85" text-anchor="middle" font-size="10" fill="white">Surface increases by 3×</text>
                    </g>
                    
                    <!-- Pentagon area -->
                    <g transform="translate(390, 60)">
                        <rect x="0" y="0" width="150" height="100" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Pentagon Area</text>
                        <text x="75" y="45" text-anchor="middle" font-size="11" fill="white">DC Pentagon</text>
                        <text x="75" y="60" text-anchor="middle" font-size="11" fill="white">∫₃₆₀⁹²⁰ p·2a da</text>
                        <text x="75" y="85" text-anchor="middle" font-size="10" fill="white">Roof area calculation</text>
                    </g>
                    
                    <!-- Fluid flow tank -->
                    <g transform="translate(135, 180)">
                        <rect x="0" y="0" width="200" height="120" fill="#16a085" stroke="#138d75" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Conical Tank Flow</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="white">Volume = πx³/3</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="white">Flow rate = 1 m³/min</text>
                        <text x="100" y="80" text-anchor="middle" font-size="11" fill="white">Height after 5 min?</text>
                        <text x="100" y="100" text-anchor="middle" font-size="10" fill="white">∫₀⁵ 1 dt = 5 = πh³/3</text>
                    </g>
                    
                    <!-- Cylindrical tank -->
                    <g transform="translate(365, 180)">
                        <rect x="0" y="0" width="200" height="120" fill="#8e44ad" stroke="#7d3c98" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Cylindrical Tank</text>
                        <text x="100" y="45" text-anchor="middle" font-size="11" fill="white">A(x) = 4(6x - x²)</text>
                        <text x="100" y="60" text-anchor="middle" font-size="11" fill="white">Volume 2m to 3m:</text>
                        <text x="100" y="80" text-anchor="middle" font-size="11" fill="white">∫₂³ 4(6x - x²) dx</text>
                        <text x="100" y="100" text-anchor="middle" font-size="10" fill="white">= 32/3 m³</text>
                    </g>
                </svg>
            </div>
        </div>

        <!-- Section 6: Even and Odd Functions -->
        <div class="section">
            <h2>Section 6: Even and Odd Function Integration</h2>
            
            <p>Even and odd functions have special symmetry properties that lead to powerful computational shortcuts when integrating over symmetric intervals.</p>

            <h3>Symmetry Properties</h3>
            
            <div class="theorem-box">
                <h4>Integration Rules for Symmetric Functions</h4>
                <p><strong>Even Functions:</strong> $f(-x) = f(x)$</p>
                <p>$$\int_{-a}^a f(x)dx = 2\int_0^a f(x)dx$$</p>
                
                <p><strong>Odd Functions:</strong> $f(-x) = -f(x)$</p>
                <p>$$\int_{-a}^a f(x)dx = 0$$</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Background -->
                    <rect width="700" height="300" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Even vs. Odd Function Symmetry</text>
                    
                    <!-- Even function graph -->
                    <g transform="translate(100, 60)">
                        <rect x="0" y="0" width="200" height="200" fill="#f8f9fa" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#3498db">Even Function</text>
                        <text x="100" y="35" text-anchor="middle" font-size="11" fill="#2c3e50">f(-x) = f(x)</text>
                        
                        <!-- Coordinate system -->
                        <g transform="translate(100, 100)">
                            <line x1="-80" y1="0" x2="80" y2="0" stroke="#34495e" stroke-width="1"/>
                            <line x1="0" y1="-60" x2="0" y2="60" stroke="#34495e" stroke-width="1"/>
                            
                            <!-- Even function curve (parabola) -->
                            <path d="M -80 60 Q -40 -40 0 -50 Q 40 -40 80 60" stroke="#3498db" stroke-width="3" fill="none"/>
                            
                            <!-- Shaded areas -->
                            <path d="M -80 60 Q -40 -40 0 -50 L 0 0 L -80 0 Z" fill="rgba(52, 152, 219, 0.3)"/>
                            <path d="M 0 -50 Q 40 -40 80 60 L 80 0 L 0 0 Z" fill="rgba(52, 152, 219, 0.3)"/>
                            
                            <text x="0" y="80" text-anchor="middle" font-size="10" fill="#3498db">Equal areas</text>
                        </g>
                    </g>
                    
                    <!-- Odd function graph -->
                    <g transform="translate(400, 60)">
                        <rect x="0" y="0" width="200" height="200" fill="#f8f9fa" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e74c3c">Odd Function</text>
                        <text x="100" y="35" text-anchor="middle" font-size="11" fill="#2c3e50">f(-x) = -f(x)</text>
                        
                        <!-- Coordinate system -->
                        <g transform="translate(100, 100)">
                            <line x1="-80" y1="0" x2="80" y2="0" stroke="#34495e" stroke-width="1"/>
                            <line x1="0" y1="-60" x2="0" y2="60" stroke="#34495e" stroke-width="1"/>
                            
                            <!-- Odd function curve -->
                            <path d="M -80 -50 Q -40 -20 0 0 Q 40 20 80 50" stroke="#e74c3c" stroke-width="3" fill="none"/>
                            
                            <!-- Shaded areas -->
                            <path d="M -80 -50 Q -40 -20 0 0 L -80 0 Z" fill="rgba(231, 76, 60, 0.3)"/>
                            <path d="M 0 0 Q 40 20 80 50 L 80 0 Z" fill="rgba(46, 204, 113, 0.3)"/>
                            
                            <text x="-40" y="-30" text-anchor="middle" font-size="10" fill="#e74c3c">Negative</text>
                            <text x="40" y="30" text-anchor="middle" font-size="10" fill="#27ae60">Positive</text>
                            <text x="0" y="80" text-anchor="middle" font-size="10" fill="#2c3e50">Areas cancel</text>
                        </g>
                    </g>
                </svg>
            </div>

            <div class="example-box">
                <h4>Example: Even Function Integration</h4>
                <p><strong>Problem:</strong> Evaluate $\int_{-2}^2 (3x^8 - 2) dx$</p>
                
                <p><strong>Step 1:</strong> Recognize that $f(x) = 3x^8 - 2$ is even because:</p>
                <p>$f(-x) = 3(-x)^8 - 2 = 3x^8 - 2 = f(x)$</p>
                
                <p><strong>Step 2:</strong> Apply the even function rule:</p>
                <p>$$\int_{-2}^2 (3x^8 - 2) dx = 2\int_0^2 (3x^8 - 2) dx$$</p>
                
                <p><strong>Step 3:</strong> Evaluate:</p>
                <p>$$= 2\left[\frac{3x^9}{9} - 2x\right]_0^2 = 2\left[\frac{x^9}{3} - 2x\right]_0^2$$</p>
                <p>$$= 2\left(\frac{512}{3} - 4\right) = 2 \cdot \frac{500}{3} = \frac{1000}{3}$$</p>
            </div>

            <div class="application-box">
                <h4>Example: Odd Function Integration</h4>
                <p><strong>Problem:</strong> Evaluate $\int_{-\pi}^\pi (-5\sin x) dx$</p>
                
                <p><strong>Solution:</strong> Since $-5\sin x$ is an odd function:</p>
                <p>$$\int_{-\pi}^\pi (-5\sin x) dx = 0$$</p>
                
                <p><strong>Verification:</strong> $f(-x) = -5\sin(-x) = -5(-\sin x) = 5\sin x = -f(x)$ ✓</p>
            </div>
        </div>

        <!-- Section 7: Practice & Assessment -->
        <div class="section">
            <h2>Section 7: Practice & Assessment</h2>
            
            <p>Test your mastery of integration applications with these comprehensive problems that bring together all the concepts we've covered.</p>

            <h3>🎯 Mastery Checklist</h3>
            
            <div class="info-box">
                <strong>By now, you should be able to:</strong>
                <br>✅ Apply basic integration formulas to complex problems
                <br>✅ Use the Net Change Theorem for various applications
                <br>✅ Distinguish between net change and total change
                <br>✅ Solve motion problems involving position, velocity, and acceleration
                <br>✅ Apply integration to fluid flow and consumption problems
                <br>✅ Use even/odd function properties for computational shortcuts
                <br>✅ Set up and solve real-world application problems
            </div>

            <h3>Comprehensive Problem Set</h3>

            <div class="application-box">
                <h4>Challenge Problem 1: Multi-Phase Motion</h4>
                <p><strong>Problem:</strong> A particle moves with velocity:</p>
                <p>$$v(t) = \begin{cases}
                t^2 - 4t + 3 & \text{if } 0 \leq t \leq 3 \\
                6 - t & \text{if } 3 < t \leq 6
                \end{cases}$$</p>
                
                <p><strong>Tasks:</strong></p>
                <p>a) Find when the particle changes direction</p>
                <p>b) Calculate the total distance traveled over [0,6]</p>
                <p>c) Determine the particle's position if it starts at the origin</p>
            </div>

            <div class="application-box">
                <h4>Challenge Problem 2: Tank Design</h4>
                <p><strong>Problem:</strong> Water flows into a conical tank at 2 m³/min. The tank has volume $V = \frac{\pi h^3}{12}$ up to height $h$.</p>
                
                <p><strong>Tasks:</strong></p>
                <p>a) How high is the water after 10 minutes?</p>
                <p>b) At what rate is the height changing when $h = 4$ meters?</p>
                <p>c) How long to fill the tank to 6 meters?</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Background -->
                    <rect width="700" height="300" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Integration Applications: Master Achievement</text>
                    
                    <!-- Achievement badges -->
                    <g transform="translate(100, 60)">
                        <circle cx="60" cy="60" r="50" fill="#3498db" stroke="#2980b9" stroke-width="3"/>
                        <text x="60" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Basic</text>
                        <text x="60" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Formulas</text>
                        <text x="60" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">✓</text>
                    </g>
                    
                    <g transform="translate(230, 60)">
                        <circle cx="60" cy="60" r="50" fill="#e74c3c" stroke="#c0392b" stroke-width="3"/>
                        <text x="60" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Net Change</text>
                        <text x="60" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Theorem</text>
                        <text x="60" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">✓</text>
                    </g>
                    
                    <g transform="translate(360, 60)">
                        <circle cx="60" cy="60" r="50" fill="#f39c12" stroke="#e67e22" stroke-width="3"/>
                        <text x="60" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Motion</text>
                        <text x="60" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Analysis</text>
                        <text x="60" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">✓</text>
                    </g>
                    
                    <g transform="translate(490, 60)">
                        <circle cx="60" cy="60" r="50" fill="#27ae60" stroke="#229954" stroke-width="3"/>
                        <text x="60" y="45" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Applied</text>
                        <text x="60" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Problems</text>
                        <text x="60" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="white">✓</text>
                    </g>
                    
                    <!-- Master level -->
                    <g transform="translate(295, 180)">
                        <circle cx="60" cy="60" r="60" fill="#8e44ad" stroke="#7d3c98" stroke-width="4"/>
                        <text x="60" y="45" text-anchor="middle" font-size="14" font-weight="bold" fill="white">INTEGRATION</text>
                        <text x="60" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="white">MASTER</text>
                        <text x="60" y="80" text-anchor="middle" font-size="20" font-weight="bold" fill="white">★</text>
                    </g>
                </svg>
            </div>

            <h3>🎓 Congratulations!</h3>
            <p>You've completed a comprehensive journey through integration applications! You now have the tools to:</p>
            
            <ul style="margin: 15px 0 15px 30px;">
                <li>Apply integration to solve real-world problems across multiple disciplines</li>
                <li>Use the Net Change Theorem as a bridge between rates and totals</li>
                <li>Analyze complex motion scenarios with confidence</li>
                <li>Leverage symmetry properties for computational efficiency</li>
                <li>Approach unfamiliar application problems with systematic methods</li>
            </ul>

            <div class="info-box">
                <strong>Looking Forward:</strong> These integration applications form the foundation for advanced topics including differential equations, vector calculus, and multivariable calculus. The problem-solving strategies you've developed here will serve you well in physics, engineering, economics, and any quantitative field where change and accumulation play important roles.
            </div>

            <p style="text-align: center; font-style: italic; margin-top: 30px; color: #7f8c8d;">
                "Integration is not just about finding areas under curves—it's about understanding how our world changes and accumulates over time."
            </p>
        </div>
    </div>
</body>
</html> 