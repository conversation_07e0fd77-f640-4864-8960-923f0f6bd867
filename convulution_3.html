<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Convolutional Networks: Structured Outputs, Data Types, and Efficient Algorithms</title>
    
    <!-- MathJax Configuration -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
            --text-color: #333;
            --light-text: #f8f9fa;
            --border-radius: 8px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #fff;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--secondary-color);
            color: var(--light-text);
            padding: 30px 20px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 2rem;
            color: var(--secondary-color);
            margin: 30px 0 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
        }
        
        h3 {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin: 25px 0 15px;
        }
        
        p {
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: var(--light-bg);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background-color: white;
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .visualization {
            margin: 30px auto;
            text-align: center;
            max-width: 100%;
        }
        
        .note {
            background-color: #fef9e7;
            border-left: 4px solid #f1c40f;
            padding: 15px;
            margin: 20px 0;
        }
        
        .important {
            background-color: #fadbd8;
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 20px 0;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        
        th {
            background-color: var(--secondary-color);
            color: white;
        }
        
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .code-block {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            h2 {
                font-size: 1.6rem;
            }
            
            h3 {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Advanced Convolutional Networks</h1>
        <p>Structured Outputs, Data Types, and Efficient Algorithms</p>
    </header>
    
    <div class="outline">
        <h2>Tutorial Outline</h2>
        <ol>
            <li>Introduction</li>
            <li>Structured Outputs
                <ul>
                    <li>High-dimensional structured outputs in CNNs</li>
                    <li>Pixel-wise labeling and segmentation</li>
                    <li>Dealing with size differences between input and output</li>
                    <li>Recurrent convolutional networks for refinement</li>
                    <li>Post-processing with graphical models</li>
                </ul>
            </li>
            <li>Data Types for Convolutional Networks
                <ul>
                    <li>Overview of data dimensionality and channels</li>
                    <li>Single-channel data examples (1D, 2D, 3D)</li>
                    <li>Multi-channel data examples</li>
                    <li>Handling variable-sized inputs</li>
                    <li>When convolution makes sense for variable inputs</li>
                </ul>
            </li>
            <li>Efficient Convolution Algorithms
                <ul>
                    <li>Computational challenges in modern CNNs</li>
                    <li>Frequency domain convolution with Fourier transforms</li>
                    <li>Separable kernels for efficient computation</li>
                    <li>Runtime and storage complexity analysis</li>
                    <li>Research directions in efficient convolution</li>
                </ul>
            </li>
            <li>Summary and Conclusions</li>
        </ol>
    </div>
    
    <!-- Content sections will be added here -->
    
    <div class="section" id="introduction">
        <h2>1. Introduction</h2>
        <p>
            Convolutional Neural Networks (CNNs) have revolutionized the field of computer vision and beyond. While basic CNN concepts like convolution layers, pooling, and activation functions are foundational, understanding advanced concepts is essential for applying these networks to complex real-world problems.
        </p>
        <p>
            In this tutorial, we'll explore three advanced topics in convolutional networks:
        </p>
        <ul>
            <li><strong>Structured Outputs:</strong> How CNNs can produce rich, high-dimensional outputs like pixel-wise segmentation masks rather than simple classification labels.</li>
            <li><strong>Data Types:</strong> The various types of data that can be processed using convolutional architectures, from 1D audio signals to 3D medical imaging data.</li>
            <li><strong>Efficient Convolution Algorithms:</strong> Techniques to optimize the computational efficiency of convolution operations, which is crucial for deploying large-scale models.</li>
        </ul>
        
        <div class="visualization">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <!-- Background -->
                <rect width="700" height="300" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Input image representation -->
                <rect x="50" y="100" width="120" height="120" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="110" y="80" text-anchor="middle" font-weight="bold">Input Data</text>
                <text x="110" y="170" text-anchor="middle" fill="white">Various Types</text>
                
                <!-- CNN Architecture -->
                <rect x="230" y="80" width="240" height="160" fill="#2c3e50" stroke="#2c3e50" stroke-width="2" rx="10" ry="10" />
                <text x="350" y="60" text-anchor="middle" font-weight="bold">Advanced CNN Architecture</text>
                
                <!-- Conv layers representation -->
                <rect x="250" y="110" width="40" height="100" fill="#3498db" stroke="#f8f9fa" stroke-width="1" rx="5" ry="5" />
                <rect x="300" y="110" width="40" height="100" fill="#e74c3c" stroke="#f8f9fa" stroke-width="1" rx="5" ry="5" />
                <rect x="350" y="110" width="40" height="100" fill="#2ecc71" stroke="#f8f9fa" stroke-width="1" rx="5" ry="5" />
                <rect x="400" y="110" width="40" height="100" fill="#f1c40f" stroke="#f8f9fa" stroke-width="1" rx="5" ry="5" />
                
                <!-- Efficient algorithms indicator -->
                <text x="350" y="230" text-anchor="middle" fill="white" font-size="12">Efficient Convolution Algorithms</text>
                
                <!-- Output representation -->
                <rect x="530" y="100" width="120" height="120" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="590" y="80" text-anchor="middle" font-weight="bold">Structured Output</text>
                <text x="590" y="170" text-anchor="middle" fill="white">High-Dimensional</text>
                
                <!-- Connecting arrows -->
                <path d="M 170 160 L 230 160" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 470 160 L 530 160" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 1: Overview of advanced convolutional network concepts covered in this tutorial.</figcaption>
        </div>
        
        <p>
            By mastering these concepts, you'll be able to design more flexible, efficient, and powerful convolutional networks that can tackle a wider range of problems beyond basic image classification.
        </p>
    </div>
    
    <div class="section" id="structured-outputs">
        <h2>2. Structured Outputs</h2>
        <p>
            Convolutional networks are not limited to outputting simple class labels or regression values. They can produce high-dimensional, structured outputs that maintain spatial information from the input. This capability makes CNNs powerful tools for tasks like semantic segmentation, where each pixel in an image needs to be classified.
        </p>
        
        <h3>2.1 High-dimensional Structured Outputs</h3>
        <p>
            A typical structured output from a CNN is a tensor $S$ where $S_{i,j,k}$ represents the probability that pixel $(j,k)$ of the input belongs to class $i$. This allows the model to generate precise masks that follow object outlines, enabling applications like:
        </p>
        <ul>
            <li>Pixel-wise semantic segmentation</li>
            <li>Instance segmentation</li>
            <li>Boundary detection</li>
            <li>Depth estimation</li>
        </ul>
        
        <div class="formula-box">
            <p>
                Formally, if an input image $X$ has dimensions $H \times W \times C$ (height, width, channels), a structured output tensor for a semantic segmentation task with $N$ classes would have dimensions $H' \times W' \times N$, where each point $(h', w')$ contains a probability distribution over the $N$ classes.
            </p>
        </div>
        
        <h3>2.2 Dealing with Size Differences</h3>
        <p>
            A common challenge in structured output prediction is that the output dimensions may not match the input dimensions due to pooling layers with stride greater than 1. Several strategies exist to address this issue:
        </p>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect width="700" height="400" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Strategies for Handling Output Size Issues</text>
                
                <!-- Strategy 1: Avoid Pooling -->
                <rect x="50" y="60" width="180" height="140" fill="white" stroke="#3498db" stroke-width="2" rx="5" ry="5" />
                <text x="140" y="85" text-anchor="middle" font-weight="bold" fill="#3498db">Avoid Pooling</text>
                
                <!-- Input-output visualization for Strategy 1 -->
                <rect x="70" y="100" width="60" height="60" fill="#3498db" stroke="#2c3e50" stroke-width="1" />
                <rect x="150" y="100" width="60" height="60" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="100" y="175" text-anchor="middle" font-size="12">Input</text>
                <text x="180" y="175" text-anchor="middle" font-size="12">Output</text>
                <text x="140" y="190" text-anchor="middle" font-size="10">(Jain et al. 2007)</text>
                
                <!-- Strategy 2: Lower Resolution -->
                <rect x="260" y="60" width="180" height="140" fill="white" stroke="#e74c3c" stroke-width="2" rx="5" ry="5" />
                <text x="350" y="85" text-anchor="middle" font-weight="bold" fill="#e74c3c">Lower Resolution</text>
                
                <!-- Input-output visualization for Strategy 2 -->
                <rect x="280" y="100" width="60" height="60" fill="#3498db" stroke="#2c3e50" stroke-width="1" />
                <rect x="360" y="100" width="40" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="310" y="175" text-anchor="middle" font-size="12">Input</text>
                <text x="380" y="175" text-anchor="middle" font-size="12">Output</text>
                <text x="350" y="190" text-anchor="middle" font-size="10">(Pinheiro & Collobert 2014, 2015)</text>
                
                <!-- Strategy 3: Unit Stride Pooling -->
                <rect x="470" y="60" width="180" height="140" fill="white" stroke="#2ecc71" stroke-width="2" rx="5" ry="5" />
                <text x="560" y="85" text-anchor="middle" font-weight="bold" fill="#2ecc71">Unit Stride Pooling</text>
                
                <!-- Input-output visualization for Strategy 3 -->
                <rect x="490" y="100" width="60" height="60" fill="#3498db" stroke="#2c3e50" stroke-width="1" />
                <rect x="570" y="100" width="60" height="60" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="520" y="175" text-anchor="middle" font-size="12">Input</text>
                <text x="600" y="175" text-anchor="middle" font-size="12">Output</text>
                
                <!-- Recurrent Refinement -->
                <rect x="160" y="220" width="380" height="160" fill="white" stroke="#9b59b6" stroke-width="2" rx="5" ry="5" />
                <text x="350" y="245" text-anchor="middle" font-weight="bold" fill="#9b59b6">Recurrent Refinement Strategy</text>
                
                <!-- Recurrent network visualization -->
                <!-- Initial prediction -->
                <rect x="190" y="270" width="40" height="40" fill="#f1c40f" stroke="#2c3e50" stroke-width="1" />
                <text x="210" y="325" text-anchor="middle" font-size="10">Initial Prediction</text>
                
                <!-- Refinement stages -->
                <rect x="280" y="270" width="40" height="40" fill="#e67e22" stroke="#2c3e50" stroke-width="1" />
                <text x="300" y="325" text-anchor="middle" font-size="10">Refinement 1</text>
                
                <rect x="370" y="270" width="40" height="40" fill="#d35400" stroke="#2c3e50" stroke-width="1" />
                <text x="390" y="325" text-anchor="middle" font-size="10">Refinement 2</text>
                
                <rect x="460" y="270" width="40" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="480" y="325" text-anchor="middle" font-size="10">Final Output</text>
                
                <!-- Connecting arrows -->
                <path d="M 230 290 L 280 290" stroke="#2c3e50" stroke-width="1" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 320 290 L 370 290" stroke="#2c3e50" stroke-width="1" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 410 290 L 460 290" stroke="#2c3e50" stroke-width="1" fill="none" marker-end="url(#arrowhead)" />
                
                <text x="350" y="350" text-anchor="middle" font-size="10">(Jain et al. 2007; Pinheiro & Collobert 2014, 2015)</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 2: Strategies for handling size differences between input and output in structured prediction tasks.</figcaption>
        </div>
        
        <ul>
            <li><strong>Avoid Pooling:</strong> Design the network architecture to avoid pooling operations altogether, preserving the spatial dimensions.</li>
            <li><strong>Lower Resolution Grid:</strong> Accept a lower-resolution output grid than the input.</li>
            <li><strong>Unit Stride Pooling:</strong> Use pooling operators with unit stride to maintain spatial dimensions.</li>
        </ul>
        
        <h3>2.3 Recurrent Convolutional Networks for Refinement</h3>
        <p>
            One effective approach for pixel-wise labeling is to use a recurrent convolutional network that iteratively refines its predictions:
        </p>
        
        <ol>
            <li>Produce an initial guess of image labels</li>
            <li>Refine this guess using information about neighboring pixels</li>
            <li>Repeat the refinement step several times, sharing weights across iterations</li>
        </ol>
        
        <div class="visualization">
            <svg width="700" height="500" viewBox="0 0 700 500">
                <!-- Background -->
                <rect width="700" height="500" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Recurrent Convolutional Network for Pixel Labeling</text>
                
                <!-- Input image X -->
                <rect x="50" y="150" width="80" height="80" fill="#3498db" stroke="#2c3e50" stroke-width="2" />
                <text x="90" y="135" text-anchor="middle" font-weight="bold">Input X</text>
                <text x="90" y="245" text-anchor="middle" font-size="10">Image Tensor</text>
                <text x="90" y="260" text-anchor="middle" font-size="10">(rows × cols × channels)</text>
                
                <!-- Step 1 -->
                <rect x="200" y="80" width="100" height="50" fill="#2c3e50" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="250" y="110" text-anchor="middle" fill="white">Hidden H(1)</text>
                
                <rect x="200" y="250" width="100" height="50" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="250" y="280" text-anchor="middle" fill="white">Output Ŷ(1)</text>
                
                <!-- Step 2 -->
                <rect x="370" y="80" width="100" height="50" fill="#2c3e50" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="420" y="110" text-anchor="middle" fill="white">Hidden H(2)</text>
                
                <rect x="370" y="250" width="100" height="50" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="420" y="280" text-anchor="middle" fill="white">Output Ŷ(2)</text>
                
                <!-- Step 3 -->
                <rect x="540" y="80" width="100" height="50" fill="#2c3e50" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="590" y="110" text-anchor="middle" fill="white">Hidden H(3)</text>
                
                <rect x="540" y="250" width="100" height="50" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="590" y="280" text-anchor="middle" fill="white">Output Ŷ(3)</text>
                
                <!-- Connections -->
                <!-- Step 1 connections -->
                <path d="M 130 190 L 200 105" stroke="#3498db" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="160" y="120" text-anchor="middle" fill="#3498db" font-weight="bold">U</text>
                
                <path d="M 250 130 L 250 250" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="265" y="190" text-anchor="middle" fill="#e74c3c" font-weight="bold">V</text>
                
                <!-- Step 2 connections -->
                <path d="M 130 190 L 370 105" stroke="#3498db" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="250" y="120" text-anchor="middle" fill="#3498db" font-weight="bold">U</text>
                
                <path d="M 420 130 L 420 250" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="435" y="190" text-anchor="middle" fill="#e74c3c" font-weight="bold">V</text>
                
                <path d="M 300 275 L 370 105" stroke="#9b59b6" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="330" y="190" text-anchor="middle" fill="#9b59b6" font-weight="bold">W</text>
                
                <!-- Step 3 connections -->
                <path d="M 130 190 L 540 105" stroke="#3498db" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="370" y="120" text-anchor="middle" fill="#3498db" font-weight="bold">U</text>
                
                <path d="M 590 130 L 590 250" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="605" y="190" text-anchor="middle" fill="#e74c3c" font-weight="bold">V</text>
                
                <path d="M 470 275 L 540 105" stroke="#9b59b6" stroke-width="3" fill="none" marker-end="url(#arrowhead)" />
                <text x="500" y="190" text-anchor="middle" fill="#9b59b6" font-weight="bold">W</text>
                
                <!-- Parameter sharing note -->
                <rect x="250" y="330" width="200" height="60" fill="#fef9e7" stroke="#f1c40f" stroke-width="2" rx="5" ry="5" />
                <text x="350" y="355" text-anchor="middle">Parameter Sharing:</text>
                <text x="350" y="375" text-anchor="middle">Same U, V, W weights used</text>
                <text x="350" y="390" text-anchor="middle">across all time steps</text>
                
                <!-- Legend -->
                <rect x="500" y="330" width="180" height="150" fill="white" stroke="#2c3e50" stroke-width="1" rx="5" ry="5" />
                <text x="590" y="350" text-anchor="middle" font-weight="bold">Legend</text>
                
                <line x1="520" y1="370" x2="550" y2="370" stroke="#3498db" stroke-width="3" />
                <text x="590" y="375" text-anchor="start">U: Image to Hidden</text>
                
                <line x1="520" y1="400" x2="550" y2="400" stroke="#e74c3c" stroke-width="3" />
                <text x="590" y="405" text-anchor="start">V: Hidden to Output</text>
                
                <line x1="520" y1="430" x2="550" y2="430" stroke="#9b59b6" stroke-width="3" />
                <text x="590" y="435" text-anchor="start">W: Output to Hidden</text>
                
                <text x="590" y="465" text-anchor="middle" font-size="10">(Based on Pinheiro & Collobert 2014, 2015)</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 3: Recurrent convolutional network for pixel labeling. The network iteratively refines its predictions using the same parameters across iterations.</figcaption>
        </div>
        
        <div class="note">
            <p>
                In the recurrent architecture shown above:
            </p>
            <ul>
                <li>$X$ is the input image tensor with dimensions for rows, columns, and channels</li>
                <li>$\hat{Y}$ is the output tensor with a probability distribution over labels for each pixel</li>
                <li>$H$ is the hidden layer representation</li>
                <li>$U$ are convolution kernels applied to the input image</li>
                <li>$V$ are kernels that produce label estimates from hidden values</li>
                <li>$W$ are kernels applied to previous label estimates (except on the first iteration)</li>
                <li>The same parameters are used on each step, making this a recurrent network</li>
            </ul>
        </div>
        
        <h3>2.4 Post-processing with Graphical Models</h3>
        <p>
            After obtaining pixel-wise predictions, various methods can be used to further process these predictions for better segmentation:
        </p>
        
        <ul>
            <li>Using graphical models to capture the probabilistic relationships between neighboring pixels</li>
            <li>Training the convolutional network to directly maximize an approximation of the graphical model objective (Ning et al. 2005; Thompson et al. 2014)</li>
            <li>Applying conditional random fields (CRFs) as a post-processing step</li>
        </ul>
        
        <p>
            The general principle is to leverage the fact that large groups of contiguous pixels tend to belong to the same class, promoting spatial consistency in the final segmentation.
        </p>
    </div>
    
    <div class="section" id="data-types">
        <h2>3. Data Types for Convolutional Networks</h2>
        <p>
            Convolutional networks are versatile models that can process various types of data with different dimensionalities and channel structures. The design of these networks can be adapted to accommodate the specific properties of the data being processed.
        </p>
        
        <h3>3.1 Overview of Data Dimensionality and Channels</h3>
        <p>
            The data used with convolutional networks typically consists of several channels, each channel being the observation of a different quantity at some point in space or time. The number of dimensions and channels affects the architecture design:
        </p>
        
        <div class="visualization">
            <svg width="700" height="350" viewBox="0 0 700 350">
                <!-- Background -->
                <rect width="700" height="350" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Data Dimensionality in Convolutional Networks</text>
                
                <!-- 1D Data Representation -->
                <rect x="50" y="60" width="180" height="120" fill="white" stroke="#3498db" stroke-width="2" rx="5" ry="5" />
                <text x="140" y="80" text-anchor="middle" font-weight="bold" fill="#3498db">1D Data</text>
                
                <!-- 1D waveform -->
                <path d="M 70 120 Q 80 90 90 120 Q 100 150 110 120 Q 120 90 130 120 Q 140 150 150 120 Q 160 90 170 120 Q 180 150 190 120 Q 200 90 210 120" 
                      stroke="#3498db" stroke-width="2" fill="none" />
                <text x="140" y="170" text-anchor="middle" font-size="12">Audio Waveform</text>
                
                <!-- 2D Data Representation -->
                <rect x="260" y="60" width="180" height="120" fill="white" stroke="#e74c3c" stroke-width="2" rx="5" ry="5" />
                <text x="350" y="80" text-anchor="middle" font-weight="bold" fill="#e74c3c">2D Data</text>
                
                <!-- 2D grid -->
                <rect x="300" y="100" width="100" height="60" fill="#f8f9fa" stroke="#e74c3c" stroke-width="1" />
                <!-- Grid lines -->
                <line x1="320" y1="100" x2="320" y2="160" stroke="#e74c3c" stroke-width="1" />
                <line x1="340" y1="100" x2="340" y2="160" stroke="#e74c3c" stroke-width="1" />
                <line x1="360" y1="100" x2="360" y2="160" stroke="#e74c3c" stroke-width="1" />
                <line x1="380" y1="100" x2="380" y2="160" stroke="#e74c3c" stroke-width="1" />
                <line x1="300" y1="120" x2="400" y2="120" stroke="#e74c3c" stroke-width="1" />
                <line x1="300" y1="140" x2="400" y2="140" stroke="#e74c3c" stroke-width="1" />
                <text x="350" y="170" text-anchor="middle" font-size="12">Image or Spectrogram</text>
                
                <!-- 3D Data Representation -->
                <rect x="470" y="60" width="180" height="120" fill="white" stroke="#2ecc71" stroke-width="2" rx="5" ry="5" />
                <text x="560" y="80" text-anchor="middle" font-weight="bold" fill="#2ecc71">3D Data</text>
                
                <!-- 3D cube -->
                <path d="M 520 120 L 580 100 L 580 140 L 520 160 Z" fill="#f8f9fa" stroke="#2ecc71" stroke-width="1" />
                <path d="M 520 120 L 550 100 L 610 100 L 580 120 Z" fill="#f8f9fa" stroke="#2ecc71" stroke-width="1" />
                <path d="M 580 120 L 610 100 L 610 140 L 580 160 Z" fill="#f8f9fa" stroke="#2ecc71" stroke-width="1" />
                <text x="560" y="170" text-anchor="middle" font-size="12">Volumetric Data (CT Scan)</text>
                
                <!-- Channels Representation -->
                <rect x="160" y="210" width="380" height="120" fill="white" stroke="#9b59b6" stroke-width="2" rx="5" ry="5" />
                <text x="350" y="230" text-anchor="middle" font-weight="bold" fill="#9b59b6">Multi-channel Examples</text>
                
                <!-- RGB channels -->
                <rect x="180" y="250" width="50" height="50" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" opacity="0.7" />
                <rect x="190" y="260" width="50" height="50" fill="#3498db" stroke="#2c3e50" stroke-width="1" opacity="0.7" />
                <rect x="200" y="270" width="50" height="50" fill="#2ecc71" stroke="#2c3e50" stroke-width="1" opacity="0.7" />
                <text x="215" y="340" text-anchor="middle" font-size="12">RGB Image</text>
                
                <!-- Time series -->
                <path d="M 320 280 L 330 270 L 340 290 L 350 260 L 360 280 L 370 250 L 380 275 L 390 265" 
                      stroke="#3498db" stroke-width="2" fill="none" />
                <path d="M 320 290 L 330 280 L 340 295 L 350 270 L 360 285 L 370 265 L 380 290 L 390 270" 
                      stroke="#e74c3c" stroke-width="2" fill="none" />
                <path d="M 320 270 L 330 290 L 340 275 L 350 295 L 360 265 L 370 290 L 380 270 L 390 285" 
                      stroke="#2ecc71" stroke-width="2" fill="none" />
                <text x="350" y="340" text-anchor="middle" font-size="12">Multi-channel Time Series</text>
                
                <!-- Video -->
                <rect x="450" y="250" width="30" height="30" fill="#f8f9fa" stroke="#2c3e50" stroke-width="1" />
                <rect x="460" y="260" width="30" height="30" fill="#f8f9fa" stroke="#2c3e50" stroke-width="1" />
                <rect x="470" y="270" width="30" height="30" fill="#f8f9fa" stroke="#2c3e50" stroke-width="1" />
                <text x="465" y="340" text-anchor="middle" font-size="12">Video Frames</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 4: Various data types and their dimensionality in convolutional networks.</figcaption>
        </div>
        
        <h3>3.2 Examples of Different Data Types</h3>
        
        <p>The table below provides examples of different formats of data that can be used with convolutional networks, categorized by dimensionality and number of channels:</p>
        
        <table>
            <thead>
                <tr>
                    <th>Dimension</th>
                    <th>Single Channel</th>
                    <th>Multi-Channel</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>1D</strong></td>
                    <td>
                        <strong>Audio Waveform:</strong> The axis we convolve over corresponds to time. We discretize time and measure the amplitude of the waveform once per time step.
                    </td>
                    <td>
                        <strong>Skeleton Animation Data:</strong> Animations of 3D computer-rendered characters are generated by altering the pose of a "skeleton" over time. At each point in time, the pose of the character is described by a specification of the angles of each of the joints in the character's skeleton. Each channel in the data we feed to the convolutional model represents the angle about one axis of one joint.
                    </td>
                </tr>
                <tr>
                    <td><strong>2D</strong></td>
                    <td>
                        <strong>Audio with Fourier Transform:</strong> We can transform the audio waveform into a 2D tensor with different rows corresponding to different frequencies and different columns corresponding to different points in time. Using convolution in time makes the model equivariant to shifts in time. Using convolution across the frequency axis makes the model equivariant to frequency, so that the same melody played in a different octave produces the same representation but at a different height in the network's output.
                    </td>
                    <td>
                        <strong>Color Image Data:</strong> One channel contains the red pixels, one the green pixels, and one the blue pixels. The convolution kernel moves over both the horizontal and vertical axes of the image, conferring translation equivariance in both directions.
                    </td>
                </tr>
                <tr>
                    <td><strong>3D</strong></td>
                    <td>
                        <strong>Volumetric Data:</strong> A common source of this kind of data is medical imaging technology, such as CT scans.
                    </td>
                    <td>
                        <strong>Color Video Data:</strong> One axis corresponds to time, one to the height of the video frame, and one to the width of the video frame.
                    </td>
                </tr>
            </tbody>
        </table>
        
        <h3>3.3 Handling Variable-sized Inputs</h3>
        <p>
            One significant advantage of convolutional networks is their ability to process inputs with varying spatial dimensions. This feature makes them particularly valuable even when computational cost and overfitting are not major concerns.
        </p>
        
        <div class="visualization">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <!-- Background -->
                <rect width="700" height="300" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Handling Variable-sized Inputs</text>
                
                <!-- Variable Input Example -->
                <rect x="50" y="70" width="150" height="80" fill="white" stroke="#3498db" stroke-width="2" rx="5" ry="5" />
                <rect x="50" y="170" width="100" height="60" fill="white" stroke="#3498db" stroke-width="2" rx="5" ry="5" />
                <text x="125" y="115" text-anchor="middle" font-size="12">Larger Input</text>
                <text x="100" y="200" text-anchor="middle" font-size="12">Smaller Input</text>
                
                <!-- CNN Processing -->
                <rect x="250" y="120" width="120" height="60" fill="#2c3e50" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="310" y="155" text-anchor="middle" fill="white">CNN</text>
                
                <!-- Variable Output Example - Variable Size -->
                <rect x="420" y="70" width="150" height="80" fill="white" stroke="#e74c3c" stroke-width="2" rx="5" ry="5" />
                <text x="495" y="115" text-anchor="middle" font-size="12">Larger Output</text>
                
                <rect x="445" y="170" width="100" height="60" fill="white" stroke="#e74c3c" stroke-width="2" rx="5" ry="5" />
                <text x="495" y="200" text-anchor="middle" font-size="12">Smaller Output</text>
                
                <!-- Fixed Output Example - Fixed Size -->
                <rect x="600" y="120" width="50" height="60" fill="white" stroke="#e74c3c" stroke-width="2" rx="5" ry="5" />
                <text x="625" y="155" text-anchor="middle" font-size="12">Fixed</text>
                <text x="625" y="170" text-anchor="middle" font-size="12">Size</text>
                
                <!-- Connecting arrows -->
                <path d="M 200 110 L 250 150" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 150 200 L 250 150" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                
                <path d="M 370 150 L 420 110" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 370 150 L 445 200" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 370 150 L 600 150" stroke="#2c3e50" stroke-width="2" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
                
                <text x="470" y="60" text-anchor="middle" font-size="14" font-weight="bold">Variable Size Output</text>
                <text x="625" y="105" text-anchor="middle" font-size="14" font-weight="bold">Fixed Size</text>
                <text x="625" y="190" text-anchor="middle" font-size="10">Additional</text>
                <text x="625" y="205" text-anchor="middle" font-size="10">pooling</text>
                <text x="625" y="220" text-anchor="middle" font-size="10">required</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 5: Processing variable-sized inputs with convolutional networks.</figcaption>
        </div>
        
        <p>
            For example, when processing images of different dimensions:
        </p>
        
        <ul>
            <li><strong>Variable to Variable:</strong> The same convolution kernel is applied a different number of times depending on input size, and the output dimensions scale accordingly. This is suitable for tasks like semantic segmentation where we want a label for each pixel.</li>
            <li><strong>Variable to Fixed:</strong> When a fixed-size output is required (e.g., for classification), additional processing is needed, such as a pooling layer with regions that scale proportionally to input size.</li>
        </ul>
        
        <div class="formula-box">
            <p>
                Mathematically, the same convolution kernel induces a different size of doubly block circulant matrix for each size of input, allowing the network to naturally handle inputs of varying dimensions.
            </p>
        </div>
        
        <h3>3.4 When Convolution Makes Sense for Variable Inputs</h3>
        <p>
            It's important to note that the use of convolution for variable-sized inputs is only appropriate in specific contexts:
        </p>
        
        <div class="important">
            <p>
                <strong>Appropriate Use:</strong> When inputs have variable size because they contain varying amounts of the same kind of observation (e.g., different lengths of time recordings, different widths of spatial observations).
            </p>
            <p>
                <strong>Inappropriate Use:</strong> When inputs have variable size because they include different types of observations. For example, if processing college applications where some applicants took standardized tests and others didn't, it wouldn't make sense to convolve the same weights over both grades and test scores.
            </p>
        </div>
        
        <p>
            This distinction is crucial for designing proper architectures that leverage the strength of convolutional networks while avoiding misapplications that could lead to poor performance.
        </p>
    </div>
    
    <div class="section" id="efficient-convolution-algorithms">
        <h2>4. Efficient Convolution Algorithms</h2>
        <p>
            Modern convolutional networks often involve networks containing more than one million units, making computational efficiency a critical consideration. While powerful hardware implementations leveraging parallel computation are essential, algorithmic optimizations can also significantly improve performance.
        </p>
        
        <h3>4.1 Computational Challenges in Modern CNNs</h3>
        <p>
            The convolution operation is computationally intensive, especially for:
        </p>
        <ul>
            <li>Deep networks with many layers</li>
            <li>Networks processing high-resolution inputs</li>
            <li>Models with large numbers of channels</li>
            <li>Real-time inference applications</li>
        </ul>
        
        <div class="visualization">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <!-- Background -->
                <rect width="700" height="300" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Computational Complexity of Convolution</text>
                
                <!-- Naive Convolution -->
                <rect x="50" y="70" width="280" height="80" fill="white" stroke="#3498db" stroke-width="2" rx="5" ry="5" />
                <text x="190" y="90" text-anchor="middle" font-weight="bold" fill="#3498db">Standard/Naive Convolution</text>
                
                <!-- Input dimensions -->
                <rect x="70" y="110" width="80" height="25" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="110" y="127" text-anchor="middle" font-size="12" fill="white">Input Size</text>
                
                <!-- Kernel dimensions -->
                <rect x="160" y="110" width="40" height="25" fill="#2ecc71" stroke="#2c3e50" stroke-width="1" />
                <text x="180" y="127" text-anchor="middle" font-size="12" fill="white">Kernel</text>
                
                <!-- Complexity -->
                <text x="250" y="127" text-anchor="middle" font-weight="bold">O(n²·k²)</text>
                
                <!-- Frequency Domain Convolution -->
                <rect x="50" y="170" width="280" height="80" fill="white" stroke="#9b59b6" stroke-width="2" rx="5" ry="5" />
                <text x="190" y="190" text-anchor="middle" font-weight="bold" fill="#9b59b6">Frequency Domain Convolution</text>
                
                <!-- FFT Transform -->
                <rect x="70" y="210" width="60" height="25" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="100" y="227" text-anchor="middle" font-size="12" fill="white">FFT</text>
                
                <!-- Multiply -->
                <rect x="140" y="210" width="60" height="25" fill="#f1c40f" stroke="#2c3e50" stroke-width="1" />
                <text x="170" y="227" text-anchor="middle" font-size="12" fill="white">Multiply</text>
                
                <!-- Inverse FFT -->
                <rect x="210" y="210" width="60" height="25" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="240" y="227" text-anchor="middle" font-size="12" fill="white">IFFT</text>
                
                <!-- Complexity -->
                <text x="300" y="227" text-anchor="middle" font-weight="bold">O(n²·log n)</text>
                
                <!-- Separable Convolution -->
                <rect x="370" y="70" width="280" height="180" fill="white" stroke="#2ecc71" stroke-width="2" rx="5" ry="5" />
                <text x="510" y="90" text-anchor="middle" font-weight="bold" fill="#2ecc71">Separable Convolution</text>
                
                <!-- Standard kernel visualization -->
                <rect x="390" y="110" width="100" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <text x="440" y="132" text-anchor="middle" font-weight="bold" fill="white">Standard 2D Kernel</text>
                <text x="440" y="150" text-anchor="middle" fill="white">O(w²)</text>
                
                <!-- Separable kernel visualization -->
                <rect x="530" y="110" width="100" height="20" fill="#3498db" stroke="#2c3e50" stroke-width="1" />
                <rect x="530" y="140" width="20" height="60" fill="#3498db" stroke="#2c3e50" stroke-width="1" />
                <text x="580" y="125" text-anchor="middle" font-size="12" fill="white">Row Vector</text>
                <text x="540" y="170" text-anchor="middle" font-size="12" fill="white">Col Vector</text>
                <text x="580" y="190" text-anchor="middle" fill="white">O(2w)</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 6: Comparison of different convolution algorithm complexities. For a d-dimensional kernel of width w in each dimension, standard convolution requires O(w^d) operations, while separable convolution requires only O(w × d).</figcaption>
        </div>
        
        <h3>4.2 Frequency Domain Convolution</h3>
        <p>
            One approach to speed up convolution is to leverage the convolution theorem from signal processing, which states that convolution in the spatial domain is equivalent to multiplication in the frequency domain.
        </p>
        
        <div class="formula-box">
            <p>The steps for frequency domain convolution are:</p>
            <ol>
                <li>Convert both the input and kernel to the frequency domain using a Fourier transform</li>
                <li>Perform point-wise multiplication of the two signals</li>
                <li>Convert the result back to the spatial domain using an inverse Fourier transform</li>
            </ol>
            <p>Mathematically: $f * g = \mathcal{F}^{-1}(\mathcal{F}(f) \cdot \mathcal{F}(g))$</p>
            <p>Where $\mathcal{F}$ is the Fourier transform, $\mathcal{F}^{-1}$ is the inverse Fourier transform, and $*$ represents convolution.</p>
        </div>
        
        <p>
            This approach is particularly efficient for large convolution kernel sizes, as the Fast Fourier Transform (FFT) algorithm has a complexity of $O(n \log n)$, making the overall frequency domain convolution more efficient than the standard spatial domain convolution for certain problem sizes.
        </p>
        
        <div class="visualization">
            <svg width="700" height="280" viewBox="0 0 700 280">
                <!-- Background -->
                <rect width="700" height="280" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Frequency Domain Convolution</text>
                
                <!-- Input signal -->
                <rect x="50" y="70" width="100" height="60" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="100" y="105" text-anchor="middle" fill="white">Input</text>
                
                <!-- Kernel -->
                <rect x="50" y="170" width="100" height="60" fill="#2ecc71" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="100" y="205" text-anchor="middle" fill="white">Kernel</text>
                
                <!-- FFT of Input -->
                <rect x="220" y="70" width="100" height="60" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" opacity="0.7" />
                <text x="270" y="105" text-anchor="middle" fill="white">FFT(Input)</text>
                
                <!-- FFT of Kernel -->
                <rect x="220" y="170" width="100" height="60" fill="#2ecc71" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" opacity="0.7" />
                <text x="270" y="205" text-anchor="middle" fill="white">FFT(Kernel)</text>
                
                <!-- Pointwise Multiplication -->
                <rect x="390" y="120" width="100" height="60" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="440" y="155" text-anchor="middle" fill="white" font-size="12">Point-wise</text>
                <text x="440" y="170" text-anchor="middle" fill="white" font-size="12">Multiplication</text>
                
                <!-- IFFT Result -->
                <rect x="560" y="120" width="100" height="60" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="610" y="155" text-anchor="middle" fill="white">Output</text>
                
                <!-- Arrows -->
                <path d="M 150 100 L 220 100" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <text x="185" y="90" text-anchor="middle" font-size="12" fill="#2c3e50">FFT</text>
                
                <path d="M 150 200 L 220 200" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <text x="185" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">FFT</text>
                
                <path d="M 320 100 L 390 140" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 320 200 L 390 160" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                
                <path d="M 490 150 L 560 150" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <text x="525" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">IFFT</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 7: Process of frequency domain convolution using Fourier transforms.</figcaption>
        </div>
        
        <h3>4.3 Separable Kernels for Efficient Computation</h3>
        <p>
            A particularly efficient approach is to use separable kernels. A d-dimensional kernel is separable when it can be expressed as the outer product of d vectors, one vector per dimension.
        </p>
        
        <div class="formula-box">
            <p>For a 2D separable kernel:</p>
            <p>$K(i, j) = \mathbf{u}(i) \times \mathbf{v}(j)$</p>
            <p>Where $\mathbf{u}$ and $\mathbf{v}$ are 1D vectors.</p>
        </div>
        
        <p>
            With separable kernels, instead of performing one d-dimensional convolution with the full kernel, we can perform a sequence of one-dimensional convolutions with each of the component vectors.
        </p>
        
        <div class="visualization">
            <svg width="700" height="350" viewBox="0 0 700 350">
                <!-- Background -->
                <rect width="700" height="350" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-weight="bold" font-size="16">Separable Convolution</text>
                
                <!-- Input image -->
                <rect x="50" y="70" width="120" height="120" fill="#3498db" stroke="#2c3e50" stroke-width="2" />
                <text x="110" y="135" text-anchor="middle" fill="white">Input</text>
                
                <!-- Standard 2D convolution -->
                <rect x="250" y="70" width="120" height="120" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" />
                <text x="310" y="135" text-anchor="middle" fill="white">2D Convolution</text>
                <text x="310" y="155" text-anchor="middle" fill="white">O(w²)</text>
                
                <!-- Horizontal convolution -->
                <rect x="250" y="220" width="120" height="120" fill="#2ecc71" stroke="#2c3e50" stroke-width="2" />
                <text x="310" y="250" text-anchor="middle" fill="white" font-size="12">Horizontal Conv</text>
                <text x="310" y="270" text-anchor="middle" fill="white" font-size="12">O(w)</text>
                
                <!-- Vertical convolution -->
                <rect x="450" y="220" width="120" height="120" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" />
                <text x="510" y="250" text-anchor="middle" fill="white" font-size="12">Vertical Conv</text>
                <text x="510" y="270" text-anchor="middle" fill="white" font-size="12">O(w)</text>
                
                <!-- Final output -->
                <rect x="450" y="70" width="120" height="120" fill="#f1c40f" stroke="#2c3e50" stroke-width="2" />
                <text x="510" y="135" text-anchor="middle" fill="white">Output</text>
                
                <!-- Standard kernel -->
                <g transform="translate(190, 100)">
                    <rect width="40" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                    <text x="20" y="25" text-anchor="middle" font-size="10" fill="white">K</text>
                </g>
                
                <!-- Horizontal kernel -->
                <g transform="translate(190, 250)">
                    <rect width="40" height="10" fill="#2ecc71" stroke="#2c3e50" stroke-width="1" />
                    <text x="20" y="8" text-anchor="middle" font-size="8" fill="white">u</text>
                </g>
                
                <!-- Vertical kernel -->
                <g transform="translate(390, 250)">
                    <rect width="10" height="40" fill="#9b59b6" stroke="#2c3e50" stroke-width="1" />
                    <text x="5" y="25" text-anchor="middle" font-size="8" fill="white">v</text>
                </g>
                
                <!-- Arrows -->
                <path d="M 170 130 L 230 130" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 370 130 L 430 130" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                
                <path d="M 110 190 L 110 250 L 230 250" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 370 280 L 430 280" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                <path d="M 510 220 L 510 190" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 8: Comparison of standard 2D convolution versus separable convolution. Separable convolution decomposes a single 2D operation into sequential 1D operations along different axes.</figcaption>
        </div>
        
        <h3>4.4 Runtime and Storage Complexity Analysis</h3>
        <p>
            The computational benefits of separable convolutions are significant:
        </p>
        
        <div class="important">
            <p>
                For a d-dimensional kernel with width w in each dimension:
            </p>
            <ul>
                <li><strong>Standard convolution:</strong> O(w<sup>d</sup>) runtime and parameter storage space</li>
                <li><strong>Separable convolution:</strong> O(w × d) runtime and parameter storage space</li>
            </ul>
            <p>
                For example, with a 5×5 2D kernel, standard convolution requires 25 parameters and operations, while separable convolution requires just 5+5=10 parameters and operations.
            </p>
        </div>
        
        <p>
            It's important to note that not every convolution can be represented in a separable form. However, when applicable, the computational savings make separable convolutions highly attractive for efficient implementations.
        </p>
        
        <h3>4.5 Research Directions in Efficient Convolution</h3>
        <p>
            Developing more efficient convolution algorithms remains an active area of research. Some current directions include:
        </p>
        
        <ul>
            <li><strong>Winograd convolution:</strong> Reduces multiplication operations at the cost of additional additions and increased complexity</li>
            <li><strong>Quantized networks:</strong> Using lower precision arithmetic to improve computation speed</li>
            <li><strong>Sparse convolution:</strong> Exploiting the sparsity pattern in kernels or activations</li>
            <li><strong>Network pruning:</strong> Removing redundant or less important connections</li>
            <li><strong>Neural architecture search:</strong> Automatically discovering efficient architectural patterns</li>
        </ul>
        
        <div class="note">
            <p>
                Even techniques that improve only forward propagation efficiency (without optimizing backward propagation) are valuable, particularly in deployment scenarios. In commercial applications, it's typical to devote more resources to deployment of a network than to its training.
            </p>
        </div>
    </div>
    
    <div class="section" id="summary">
        <h2>5. Summary and Conclusions</h2>
        <p>
            In this tutorial, we explored advanced concepts in convolutional neural networks that extend beyond basic classification tasks. These concepts enable more powerful, flexible, and efficient networks for a wide range of applications.
        </p>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect width="700" height="400" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="40" text-anchor="middle" font-weight="bold" font-size="18">Advanced CNN Concepts Summary</text>
                
                <!-- Central CNN representation -->
                <rect x="250" y="150" width="200" height="100" fill="#2c3e50" stroke="#2c3e50" stroke-width="2" rx="10" ry="10" />
                <text x="350" y="195" text-anchor="middle" fill="white" font-size="16">Advanced CNN</text>
                <text x="350" y="215" text-anchor="middle" fill="white" font-size="12">Capabilities</text>
                
                <!-- Structured Outputs Node -->
                <rect x="100" y="80" width="150" height="80" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="10" ry="10" />
                <text x="175" y="110" text-anchor="middle" fill="white" font-weight="bold">Structured Outputs</text>
                <text x="175" y="130" text-anchor="middle" fill="white" font-size="11">Pixel-wise labeling</text>
                <text x="175" y="145" text-anchor="middle" fill="white" font-size="11">Semantic segmentation</text>
                
                <!-- Data Types Node -->
                <rect x="100" y="240" width="150" height="80" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="10" ry="10" />
                <text x="175" y="270" text-anchor="middle" fill="white" font-weight="bold">Data Types</text>
                <text x="175" y="290" text-anchor="middle" fill="white" font-size="11">1D/2D/3D data</text>
                <text x="175" y="305" text-anchor="middle" fill="white" font-size="11">Variable-sized inputs</text>
                
                <!-- Efficient Algorithms Node -->
                <rect x="450" y="160" width="150" height="80" fill="#2ecc71" stroke="#2c3e50" stroke-width="2" rx="10" ry="10" />
                <text x="525" y="190" text-anchor="middle" fill="white" font-weight="bold">Efficient Algorithms</text>
                <text x="525" y="210" text-anchor="middle" fill="white" font-size="11">Frequency domain</text>
                <text x="525" y="225" text-anchor="middle" fill="white" font-size="11">Separable kernels</text>
                
                <!-- Applications Node -->
                <rect x="275" y="300" width="150" height="80" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="10" ry="10" />
                <text x="350" y="330" text-anchor="middle" fill="white" font-weight="bold">Applications</text>
                <text x="350" y="350" text-anchor="middle" fill="white" font-size="11">Medical imaging</text>
                <text x="350" y="365" text-anchor="middle" fill="white" font-size="11">Video analysis</text>
                
                <!-- Connections -->
                <path d="M 250 120 L 225 120" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead_rev)" />
                <path d="M 250 180 L 225 240" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead_rev)" />
                <path d="M 450 200 L 405 200" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead_rev)" />
                <path d="M 350 300 L 350 250" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead_rev)" />
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead_rev" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="10 0, 0 3.5, 10 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 9: Summary of advanced convolutional neural network concepts covered in this tutorial.</figcaption>
        </div>
        
        <h3>5.1 Key Takeaways</h3>
        
        <div class="important">
            <h4>Structured Outputs</h4>
            <ul>
                <li>CNNs can generate complex, high-dimensional outputs like pixel-wise segmentation masks</li>
                <li>Strategies exist to address input-output size differences (avoiding pooling, lower resolution outputs, unit stride pooling)</li>
                <li>Recurrent convolutional networks can iteratively refine predictions</li>
                <li>Post-processing with graphical models can improve segmentation coherence</li>
            </ul>
        </div>
        
        <div class="important">
            <h4>Data Types for Convolutional Networks</h4>
            <ul>
                <li>CNNs can process data of various dimensionalities (1D, 2D, 3D) and channel configurations</li>
                <li>The same network architecture can handle variable-sized inputs naturally</li>
                <li>Convolution is appropriate for variable sizes only when they represent varying amounts of the same kind of observation</li>
                <li>Special pooling strategies may be needed when fixed-sized outputs are required</li>
            </ul>
        </div>
        
        <div class="important">
            <h4>Efficient Convolution Algorithms</h4>
            <ul>
                <li>Frequency domain convolution can improve efficiency for certain kernel sizes</li>
                <li>Separable kernels dramatically reduce computational and storage requirements</li>
                <li>Research continues in quantized networks, sparse convolutions, and neural architecture search</li>
                <li>Deployment often benefits from optimization even when training does not</li>
            </ul>
        </div>
        
        <h3>5.2 Future Directions</h3>
        <p>
            The field of convolutional networks continues to evolve rapidly. Some promising directions include:
        </p>
        <ul>
            <li>More efficient network architectures that require less computational resources</li>
            <li>Hardware accelerators specifically designed for convolutional operations</li>
            <li>Novel applications in domains beyond computer vision, such as natural language processing and audio analysis</li>
            <li>Integration with other deep learning techniques like attention mechanisms and transformers</li>
            <li>Self-supervised and few-shot learning techniques to reduce the need for large labeled datasets</li>
        </ul>
        
        <p>
            By understanding these advanced concepts, you're now better equipped to design and implement convolutional networks that can handle a wider range of tasks with greater efficiency and flexibility.
        </p>
    </div>
</body>
</html> 