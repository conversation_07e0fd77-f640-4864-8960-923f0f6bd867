36-705: Intermediate Statistics Fall 2019
Lecture 2: August 28
Lecturer: <PERSON><PERSON> in the last class we discussed that we would like to understand the behaviour of
the average of independent random variables. Towards that goal let us begin by trying to
understand the tail behaviour of a random variable.
2.1 Markov Inequality
The most elementary tail bound is <PERSON><PERSON>’s inequality, which asserts that for a positive
random variable X ≥ 0,
P(X ≥ t) ≤
E[X]
t
.
Intuitively, if the mean of a (positive) random variable is small then it is unlikely to be too
large too often, i.e. the probability that it is large is small. While Markov on its own is
fairly crude it will form the basis for much more refined tail bounds.
Proof: Fix an arbitrary t > 0. Define the indicator:
I(t) = (
1 if X ≥ t
0 if X < t.
We have that,
tI(t) ≤ X,
so that
E[X] ≥ E[tI(t)] = tE[I(t)] = tP(X ≥ t).
2.2 Chebyshev Inequality
Ch<PERSON><PERSON>he<PERSON>’s inequality states that for a random variable X, with Var(X) = σ
2
:
P(|X − E[X]| ≥ kσ) ≤
1
k
2
∀k ≥ 0.
2-1
2-2 Lecture 2: August 28
Before we prove this lets look at a simple application. In the last lecture we saw that if we
average i.i.d. random variables with mean µ and variance σ
2
, we have that the average:
µbn =
1
n
Xn
i=1
Xi
,
has mean µ and variance σ
2/n. So applying Chebyshev’s inequality to µbn we obtain that,
P

|µbn − µ| ≥ kσ
√
n

≤
1
k
2
.
Alternatively, with probability at least 0.99 (for instance) the average is within 10σ/√
n from
the its expectation. This is pretty neat and almost directly gives us something called the
Weak Law of Large Numbers (but we will return to this).
We will study refinements of this inequality today, but in some sense it already has the
correct “1/
√
n” behaviour. The refinements will mainly be to show that in many cases we
can dramatically improve the constant 10.
Proof: Chebyshev’s inequality is an immediate consequence of Markov’s inequality.
P(|X − E[X]| ≥ kσ) = P(|X − E[X]|
2 ≥ k
2σ
2
)
≤
E(|X − E[X]|
2
)
k
2σ
2
=
1
k
2
.
2.3 Chernoff Method
There are several refinements to the Chebyshev inequality. One simple one that is sometimes
useful is to observe that if the random variable X has a finite k-th central moment then we
have that,
P(|X − E[X]| ≥ t) ≤
E|X − E[X]|
k
t
k
.
For many random variables (we will see some examples today), the moment generating
function will exist in a neighborhood around 0, i.e the mgf is finite for all |t| ≤ b where b > 0
is some constant. In these cases, we can use the mgf to produce a tail bound.
Define, µ = E[X]. For any t > 0, we have that,
P((X − µ) ≥ u) = P(exp(t(X − µ)) ≥ exp(tu)) ≤
E[exp(t(X − µ))]
exp(tu)
.
Now t is a parameter we can choose to get a tight upper bound, i.e. we can write this bound
as:
P((X − µ) ≥ u) ≤ inf
0≤t≤b
exp(−t(u + µ))E[exp(tX)].
This bound is known as Chernoff’s bound.
Lecture 2: August 28 2-3
2.3.1 Gaussian Tail Bounds via Chernoff
Suppose that, X ∼ N(µ, σ2
), then a simple calculation (see HW2) gives that the mgf of X
is:
MX(t) = E[exp(tX)] = exp(tµ + t
2σ
2
/2).
The mgf is defined for all t. To apply the Chernoff bound we then need to compute:
inf
t≥0
exp(−t(u + µ)) exp(tµ + t
2σ
2
/2) = inf
t≥0
exp(−tu + t
2σ
2
/2),
which is minimized when t = u/σ2 which in turn yields the tail bound,
P(X − µ ≥ u) ≤ exp(−u
2
/(2σ
2
)).
This is often referred to as a one-sided or upper tail bound. We can use the fact that if X has
distribution N(µ, σ2
) then −X has distribution N(−µ, σ2
) and repeat the above calculation
to obtain the analogous lower tail bound,
P(−X + µ ≤ u) ≤ exp(−u
2
/(2σ
2
)).
Putting these two pieces together, we have the two-sided Gaussian tail bound:
P(|X − µ| ≥ u) ≤ 2 exp(−u
2
/(2σ
2
)).
The main thing to observe is that this inequality is much sharper than Chebyshev’s inequality.
In particular, suppose we consider the average of i.i.d Gaussian random variables, i.e. we
have X1, . . . , Xn ∼ N(µ, σ2
) and we construct the estimate:
µb =
1
n
Xn
i=1
Xi
.
Using the fact that the average of Gaussian RVs is Gaussian we obtain that µb has a
N(µ, σ2/n) distribution. In this case, using the Gaussian tail bound we derived we obtain
that,
P(|µb − µ| ≥ kσ/√
n) ≤ 2 exp(−k
2
/2).
This is an example of an exponential tail inequality. Comparing with Chebyshev’s inequality
we should observe two things:
1. Both inequalities say roughly that the deviation of the average from the expected value
goes down as 1/
√
n.
2-4 Lecture 2: August 28
2. However, the Gaussian tail bound says if the random variables are actually Gaussian
then the chance that the deviation is much bigger than σ/√
n goes down exponentially
fast. Let us look at a concrete example, we say previously that Chebyshev told us the
average is within 10σ/√
n with probability at least 0.99.
On the other hand the exponential tail bound says that with probability 0.99 the
average is within,
p
2 ln(1/0.005)σ/√
n ≈ 3.25σ/√
n.
More generally, Chebyshev tells us that with probability at least 1 − δ,
|µb − µ| ≤ σ
√
nδ
while the exponential tail bound tells us that,
|µb − µ| ≤ σ
r
2 ln(2/δ)
n
.
The first goes up polynomially as δ → 0, while the second more refined bound goes up
only logarithmically.
2.3.2 Sub-Gaussian Random Variables
It turns out that the Gaussian tail inequality from the previous section is much more broadly
applicable to a class of random variables called sub-Gaussian random variables. Roughly
these are random variables whose tails decay faster than a Gaussian. Formally, a random
variable X with mean µ is sub-Gaussian if there exists a positive number σ such that,
E[exp(t(X − µ))] ≤ exp(σ
2
t
2
/2),
for all t ∈ R. Gaussian random variables with variance σ
2
satisfy the above condition with
equality, so a σ-sub-Gaussian random variable basically just has an mgf that is dominated
by a Gaussian with variance σ.
It is straightforward to go through the above Chernoff bound to conclude that for a subGaussian random variable we have the same two-sided exponential tail bound,
P(|X − µ| ≥ u) ≤ 2 exp(−u
2
/(2σ
2
)).
Suppose we have n i.i.d random variables σ sub-Gaussian RVs X1, X2, . . . , Xn, then by
Lecture 2: August 28 2-5
independence we obtain that,
E[exp(t(µb − µ))] = E[exp(t/nXn
i=1
(Xi − µ)]
=
Yn
i=1
E[exp(t(Xi − µ)/n)]
≤ exp(t
2σ
2
/(2n)).
Alternatively, the average of n independent σ-sub Gaussian RVs is σ/√
n-sub Gaussian. This
yields the tail bound for the average of sub Gaussian RVs:
P(|µb − µ| ≥ kσ/√
n) ≤ 2 exp(−k
2
/2).
2.3.3 Bounded Random Variables - Hoeffding’s bound
We claimed in the previous section that many classes of RVs are sub-Gaussian. In this
section, we show this for an important special case: bounded random variables.
Example 1: Let us first consider a simple case, of Rademacher random variables, i.e.
random variables that take the values {+1, −1} equiprobably. In this case we can see that,
E[exp(tX)] = 1
2
[exp(t) + exp(−t)]
=
1
2
"X∞
k=0
(−t)
k
k!
+
X∞
k=0
t
k
k!
#
=
X∞
k=0
t
2k
(2k)! ≤
X∞
k=0
t
2k
2
kk!
= exp(t
2
/2).
This shows that Rademacher random variables are 1-sub Gaussian.
Detour: Jensen’s inequality: Jensen’s inequality states that for a convex function
g : R 7→ R we have that,
E[g(X)] ≥ g(E[X]).
If g is concave then the reverse inequality holds.
Proof: Let µ = E[X] and let Lµ(x) = a + bx be the tangent line to the function g at µ,
i.e. we have that Lµ(µ) = g(µ). By convexity we know that g(x) ≥ Lµ(x) for every point x.
Thus we have that,
E[g(X)] ≥ E[Lµ(X)] = E[a + bX]
= a + bµ = Lµ(µ) = g(µ).
2-6 Lecture 2: August 28
Example 2: Bounded Random Variables. Let X be a random variable with zero
mean and with support on some bounded interval [a, b].
You should convince yourself that the zero mean assumption does not matter in general (you
can always subtract the mean, i.e. define a new random variable Y = X − E[X] and use Y
in the calculation below).
Let X0 denote an independent copy of X then we have that,
EX[exp(tX)] = EX[exp(t(X − E[X
0
])] ≤ EX,X0[exp(t(X − X
0
)],
using Jensen’s inequality, and the convexity of the function g(x) = exp(x).
Now, let  be a Rademacher random variable. Then note that the distribution of X − X0
is
identical to the distribution of X0 − X and more importantly of (X − X0
). So we obtain
that,
EX,X0[exp(t(X − X
0
)] = EX,X0[E
[exp(t(X − X
0
)]]
≤ EX,X0[exp(t
2
(X − X
0
)
2
/2],
where we just use the result from Example 1, with (X, X0
) fixed by conditioning. Now
(X − X0
) using boundedness is at most (b − a) so we obtain that,
EX[exp(tX)] ≤ exp(t
2
(b − a)
2
/2),
which in turn shows that bounded random variables are (b − a)-sub Gaussian.
This in turn yields Hoeffding’s bound. Suppose that, X1, . . . , Xn are independent identically
distribution bounded random variables, with a ≤ Xi ≤ b for all i then,
P
 




1
n
Xn
i=1
Xi − µ





≥ t
!
≤ 2 exp 
−
nt2
2(b − a)
2

.
This is a two-sided exponential tail inequality for the averages of bounded random variables.
With some effort you can derive a slightly tighter bound on the MGF to obtain the stronger
bound that:
P
 




1
n
Xn
i=1
Xi − µ





≥ t
!
≤ 2 exp 
−
2nt2
(b − a)
2

.
2.3.4 A simple generalization
It is worth noting that none of the exponential tail inequalities we proved required the random
variables to be identically distributed. More generally, suppose that we have X1, . . . , Xn
Lecture 2: August 28 2-7
which are each σ1, . . . , σn sub Gaussian. Then using just independence you can verify that
their average µb is σ-sub Gaussian, where,
σ =
1
n
vuutXn
i=1
σ
2
i
This in turn yields the exponential tail inequality,
P
 




1
n
Xn
i=1
(Xi − E[Xi
])





≥ t
!
≤ exp(−t
2
/(2σ
2
)).
Note that the random variables still need to be independent but no longer need to be identically distributed (i.e. they can for instance have different means and sub-Gaussian parameters).