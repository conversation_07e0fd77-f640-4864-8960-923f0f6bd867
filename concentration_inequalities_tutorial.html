<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concentration Inequalities and Tail Bounds - Interactive Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            border-radius: 15px;
            margin: -20px -20px 40px -20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .course-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .course-info h3 {
            margin-bottom: 5px;
        }

        .navigation {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }

        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: #007bff;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            transition: background 0.3s;
        }

        .nav-link:hover {
            background: #0056b3;
        }

        .section {
            margin: 40px 0;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #1e3c72;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #2a5298;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
            padding-left: 15px;
            border-left: 4px solid #4facfe;
        }

        .theorem-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0fff0 100%);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .theorem-box::before {
            content: "THEOREM";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 5px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .proof-box {
            background: linear-gradient(135deg, #fff3e0 0%, #fffbf0 100%);
            border: 2px solid #ff9800;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .proof-box::before {
            content: "PROOF";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #ff9800;
            color: white;
            padding: 4px 12px;
            border-radius: 5px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .example-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3f9ff 100%);
            border: 2px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .example-box::before {
            content: "EXAMPLE";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #2196f3;
            color: white;
            padding: 4px 12px;
            border-radius: 5px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .insight-box {
            background: linear-gradient(135deg, #f3e5f5 0%, #faf2fc 100%);
            border: 2px solid #9c27b0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .insight-box::before {
            content: "KEY INSIGHT";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #9c27b0;
            color: white;
            padding: 4px 12px;
            border-radius: 5px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .comparison-box {
            background: linear-gradient(135deg, #ffe8e8 0%, #fff5f5 100%);
            border: 2px solid #f44336;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .comparison-box::before {
            content: "COMPARISON";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #f44336;
            color: white;
            padding: 4px 12px;
            border-radius: 5px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .summary-box {
            background: linear-gradient(135deg, #e1f5fe 0%, #f0f9ff 100%);
            border: 2px solid #0277bd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .summary-box::before {
            content: "SUMMARY";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #0277bd;
            color: white;
            padding: 4px 12px;
            border-radius: 5px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .formula-highlight {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
            font-size: 1.1em;
        }

        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #fafafa;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .step-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .step-list h4 {
            color: #495057;
            margin-bottom: 15px;
        }

        .step-list ol {
            padding-left: 20px;
        }

        .step-list li {
            margin: 10px 0;
            line-height: 1.6;
        }

        .progress-indicator {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Concentration Inequalities and Tail Bounds</h1>
            <p>A Step-by-Step Interactive Tutorial</p>
        </div>

        <div class="course-info">
            <h3>36-705: Intermediate Statistics Fall 2019</h3>
            <p>Lecture 2: August 28 | Lecturer: Siva Balakrishnan</p>
        </div>

        <div class="navigation">
            <h4>Tutorial Navigation</h4>
            <div class="nav-links">
                <a href="#introduction" class="nav-link">Introduction</a>
                <a href="#markov" class="nav-link">Markov Inequality</a>
                <a href="#chebyshev" class="nav-link">Chebyshev Inequality</a>
                <a href="#chernoff" class="nav-link">Chernoff Method</a>
                <a href="#gaussian" class="nav-link">Gaussian Tail Bounds</a>
                <a href="#subgaussian" class="nav-link">Sub-Gaussian Variables</a>
                <a href="#hoeffding" class="nav-link">Hoeffding's Bound</a>
                <a href="#generalization" class="nav-link">Generalization</a>
            </div>
        </div>

        <div class="progress-indicator">
            <div class="progress-bar" style="width: 90%"></div>
        </div>

        <!-- Introduction Section -->
        <section id="introduction" class="section">
            <h2>Introduction: Why Study Concentration Inequalities?</h2>
            
            <p>In probability theory and statistics, one of the most fundamental questions is: <strong>How does the average of independent random variables behave?</strong> This tutorial explores powerful mathematical tools called concentration inequalities that help us understand the tail behavior of random variables and provide bounds on how far they deviate from their expected values.</p>

            <div class="insight-box">
                <h4>The Central Question</h4>
                <p>Given independent random variables $X_1, X_2, \ldots, X_n$, what can we say about their average $\bar{\mu}_n = \frac{1}{n}\sum_{i=1}^n X_i$? How likely is it to deviate significantly from its expected value?</p>
            </div>

            <h3>Tutorial Outline and Learning Path</h3>
            
            <div class="step-list">
                <h4>Our Journey Through Concentration Inequalities</h4>
                <ol>
                    <li><strong>Markov Inequality</strong> - The most basic tail bound for positive random variables</li>
                    <li><strong>Chebyshev Inequality</strong> - Improving bounds using variance information</li>
                    <li><strong>Chernoff Method</strong> - Using moment generating functions for exponential bounds</li>
                    <li><strong>Gaussian Tail Bounds</strong> - Exponential concentration for normal distributions</li>
                    <li><strong>Sub-Gaussian Variables</strong> - Generalizing beyond Gaussian distributions</li>
                    <li><strong>Hoeffding's Bound</strong> - Powerful results for bounded random variables</li>
                    <li><strong>Advanced Generalizations</strong> - Extensions to non-identical distributions</li>
                </ol>
            </div>

            <div class="svg-container">
                <h4>Concentration Inequalities Hierarchy</h4>
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="800" height="400" fill="url(#bgGradient)" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#1e3c72">
                        From Basic to Advanced: The Concentration Hierarchy
                    </text>
                    
                    <!-- Markov Level -->
                    <rect x="50" y="60" width="150" height="60" fill="#ff9800" rx="8" opacity="0.8"/>
                    <text x="125" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Markov</text>
                    <text x="125" y="105" text-anchor="middle" font-size="12" fill="white">Basic bound</text>
                    
                    <!-- Chebyshev Level -->
                    <rect x="250" y="60" width="150" height="60" fill="#2196f3" rx="8" opacity="0.8"/>
                    <text x="325" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Chebyshev</text>
                    <text x="325" y="105" text-anchor="middle" font-size="12" fill="white">Uses variance</text>
                    
                    <!-- Chernoff Level -->
                    <rect x="450" y="60" width="150" height="60" fill="#9c27b0" rx="8" opacity="0.8"/>
                    <text x="525" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Chernoff</text>
                    <text x="525" y="105" text-anchor="middle" font-size="12" fill="white">Uses MGF</text>
                    
                    <!-- Advanced Level -->
                    <rect x="600" y="60" width="150" height="60" fill="#4caf50" rx="8" opacity="0.8"/>
                    <text x="675" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Advanced</text>
                    <text x="675" y="105" text-anchor="middle" font-size="12" fill="white">Exponential</text>
                    
                    <!-- Arrows showing progression -->
                    <path d="M 210 90 L 240 90" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 410 90 L 440 90" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 610 90 L 590 90" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                    
                    <!-- Quality indicators -->
                    <text x="125" y="150" text-anchor="middle" font-size="12" fill="#666">Crude but general</text>
                    <text x="325" y="150" text-anchor="middle" font-size="12" fill="#666">Better convergence</text>
                    <text x="525" y="150" text-anchor="middle" font-size="12" fill="#666">Exponential decay</text>
                    <text x="675" y="150" text-anchor="middle" font-size="12" fill="#666">Sharp bounds</text>
                    
                    <!-- Convergence rates -->
                    <text x="400" y="200" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Convergence Rate Comparison
                    </text>
                    
                    <!-- Rate boxes -->
                    <rect x="100" y="220" width="120" height="40" fill="#ffebee" stroke="#f44336" rx="5"/>
                    <text x="160" y="235" text-anchor="middle" font-size="10" fill="#d32f2f">Markov</text>
                    <text x="160" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#d32f2f">O(1/t)</text>
                    
                    <rect x="250" y="220" width="120" height="40" fill="#e3f2fd" stroke="#2196f3" rx="5"/>
                    <text x="310" y="235" text-anchor="middle" font-size="10" fill="#1976d2">Chebyshev</text>
                    <text x="310" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">O(1/t²)</text>
                    
                    <rect x="400" y="220" width="120" height="40" fill="#f3e5f5" stroke="#9c27b0" rx="5"/>
                    <text x="460" y="235" text-anchor="middle" font-size="10" fill="#7b1fa2">Chernoff</text>
                    <text x="460" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#7b1fa2">exp(-ct²)</text>
                    
                    <rect x="550" y="220" width="120" height="40" fill="#e8f5e8" stroke="#4caf50" rx="5"/>
                    <text x="610" y="235" text-anchor="middle" font-size="10" fill="#388e3c">Hoeffding</text>
                    <text x="610" y="250" text-anchor="middle" font-size="12" font-weight="bold" fill="#388e3c">exp(-2nt²)</text>
                    
                    <!-- Applications -->
                    <text x="400" y="300" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Key Applications
                    </text>
                    
                    <text x="200" y="330" text-anchor="middle" font-size="12" fill="#666">• Law of Large Numbers</text>
                    <text x="200" y="350" text-anchor="middle" font-size="12" fill="#666">• Central Limit Theorem</text>
                    
                    <text x="600" y="330" text-anchor="middle" font-size="12" fill="#666">• Machine Learning Theory</text>
                    <text x="600" y="350" text-anchor="middle" font-size="12" fill="#666">• Statistical Learning</text>
                    
                </svg>
            </div>

            <div class="comparison-box">
                <h4>What Makes These Inequalities Powerful?</h4>
                <div class="two-column">
                    <div>
                        <h5>Progressive Refinement</h5>
                        <p>Each inequality improves upon the previous one by using more information about the random variables (variance, moment generating function, boundedness).</p>
                    </div>
                    <div>
                        <h5>Practical Impact</h5>
                        <p>Better bounds mean we can make stronger statistical guarantees with smaller sample sizes, which is crucial for machine learning and data science applications.</p>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <h4>Exponential vs Polynomial Decay Comparison</h4>
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        The Power of Exponential Concentration
                    </text>
                    
                    <!-- Axes -->
                    <line x1="80" y1="300" x2="720" y2="300" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="300" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Axis labels -->
                    <text x="400" y="330" text-anchor="middle" font-size="12" fill="#333">Deviation u (in units of σ)</text>
                    <text x="30" y="175" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90, 30, 175)">P(|X-μ| ≥ u)</text>
                    
                    <!-- Chebyshev bound (1/u²) -->
                    <path d="M 120 80 Q 200 120, 300 180 Q 500 240, 700 285" 
                          stroke="#f44336" stroke-width="3" fill="none" stroke-dasharray="8,4"/>
                    
                    <!-- Gaussian exponential bound -->
                    <path d="M 120 85 Q 200 150, 300 220 Q 500 280, 700 298" 
                          stroke="#4caf50" stroke-width="3" fill="none"/>
                    
                    <!-- True Gaussian probability -->
                    <path d="M 120 90 Q 200 160, 300 230 Q 500 285, 700 299" 
                          stroke="#2196f3" stroke-width="2" fill="none" stroke-dasharray="2,2"/>
                    
                    <!-- Legend -->
                    <rect x="500" y="60" width="250" height="120" fill="white" stroke="#ccc" rx="5"/>
                    
                    <line x1="510" y1="80" x2="540" y2="80" stroke="#f44336" stroke-width="3" stroke-dasharray="8,4"/>
                    <text x="550" y="85" font-size="12" fill="#f44336">Chebyshev: O(1/u²)</text>
                    
                    <line x1="510" y1="100" x2="540" y2="100" stroke="#4caf50" stroke-width="3"/>
                    <text x="550" y="105" font-size="12" fill="#4caf50">Chernoff: O(exp(-u²))</text>
                    
                    <line x1="510" y1="120" x2="540" y2="120" stroke="#2196f3" stroke-width="2" stroke-dasharray="2,2"/>
                    <text x="550" y="125" font-size="12" fill="#2196f3">True Gaussian</text>
                    
                    <text x="625" y="145" font-size="10" fill="#666" text-anchor="middle">Exponential decay is</text>
                    <text x="625" y="157" font-size="10" fill="#666" text-anchor="middle">dramatically better!</text>
                    <text x="625" y="169" font-size="10" fill="#666" text-anchor="middle">And nearly tight!</text>
                    
                    <!-- Specific points -->
                    <circle cx="300" cy="180" r="4" fill="#f44336"/>
                    <circle cx="300" cy="220" r="4" fill="#4caf50"/>
                    <text x="320" y="185" font-size="10" fill="#f44336">0.11</text>
                    <text x="320" y="225" font-size="10" fill="#4caf50">0.0013</text>
                    <text x="320" y="205" font-size="10" fill="#666">At u=3σ:</text>
                    
                </svg>
            </div>

            <h3>Why This Matters: Sample Complexity</h3>

            <div class="insight-box">
                <h4>Revolutionary Impact on Sample Complexity</h4>
                <p>The exponential concentration has profound implications for statistical learning:</p>
                
                <div class="two-column">
                    <div>
                        <h5>Chebyshev Sample Complexity</h5>
                        <p>To achieve $\epsilon$-accuracy with probability $1-\delta$:</p>
                        <div class="formula-highlight">
                            $$n = O\left(\frac{\sigma^2}{\delta \epsilon^2}\right)$$
                        </div>
                        <p>Polynomial dependence on $1/\delta$</p>
                    </div>
                    <div>
                        <h5>Gaussian Bound Sample Complexity</h5>
                        <p>To achieve $\epsilon$-accuracy with probability $1-\delta$:</p>
                        <div class="formula-highlight">
                            $$n = O\left(\frac{\sigma^2 \log(1/\delta)}{\epsilon^2}\right)$$
                        </div>
                        <p>Logarithmic dependence on $1/\delta$!</p>
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h4>Numerical Example: The Logarithmic Advantage</h4>
                <p>Consider $\sigma = 1$, $\epsilon = 0.1$, and varying confidence levels:</p>
                
                <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #dee2e6; padding: 8px;">Confidence</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">$\delta$</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">Chebyshev $n$</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">Gaussian $n$</th>
                        <th style="border: 1px solid #dee2e6; padding: 8px;">Improvement</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">90%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">0.1</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">1,000</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">46</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>22×</strong></td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td style="border: 1px solid #dee2e6; padding: 8px;">99%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">0.01</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">10,000</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">92</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>109×</strong></td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">99.9%</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">0.001</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">100,000</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">138</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>725×</strong></td>
                    </tr>
                </table>
                
                <p>The improvement grows dramatically as we demand higher confidence!</p>
            </div>

            <div class="step-list">
                <h4>Moving Forward</h4>
                <p>The Chernoff method gives us exponential bounds for Gaussian random variables, but what about other distributions? Next, we'll explore sub-Gaussian random variables, which extend these powerful concentration results to a much broader class of distributions.</p>
            </div>

        </section>

        <!-- Summary Section -->
        <section id="summary" class="section">
            <h2>Summary: The Journey So Far</h2>
            
            <p>We've seen how concentration inequalities progressively refine our understanding of random variable behavior, moving from crude polynomial bounds to sharp exponential concentration.</p>

            <div class="comparison-box">
                <h4>Complete Comparison Table</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                    <tr style="background: #1e3c72; color: white;">
                        <th style="border: 1px solid #dee2e6; padding: 12px;">Method</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">Requirements</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">Bound Type</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">Decay Rate</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px;">99% Confidence</th>
                    </tr>
                    <tr style="background: #fff3e0;">
                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>Markov</strong></td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$E[X]$ only</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">One-sided</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$O(1/t)$</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$100\sigma$</td>
                    </tr>
                    <tr style="background: #e3f2fd;">
                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>Chebyshev</strong></td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$E[X], \text{Var}(X)$</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">Two-sided</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$O(1/t^2)$</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$10\sigma$</td>
                    </tr>
                    <tr style="background: #e8f5e8;">
                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>Chernoff (Gaussian)</strong></td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">MGF or Gaussianity</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">Two-sided</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$\exp(-t^2)$</td>
                        <td style="border: 1px solid #dee2e6; padding: 8px;">$3.25\sigma$</td>
                    </tr>
                </table>
            </div>

            <div class="insight-box">
                <h4>Key Takeaways</h4>
                <ul>
                    <li><strong>Progressive Refinement:</strong> Each method uses more information to get tighter bounds</li>
                    <li><strong>Exponential Power:</strong> Chernoff-type bounds provide dramatically better constants</li>
                    <li><strong>Sample Complexity:</strong> Better concentration means fewer samples needed for statistical guarantees</li>
                    <li><strong>Broad Applicability:</strong> These techniques form the foundation of modern statistical learning theory</li>
                </ul>
            </div>

            <div class="step-list">
                <h4>What's Next in the Full Theory?</h4>
                <p>This tutorial covered the foundational concepts. The lecture continues with:</p>
                <ol>
                    <li><strong>Sub-Gaussian Random Variables</strong> - Extending exponential concentration beyond Gaussians</li>
                    <li><strong>Hoeffding's Bound</strong> - Powerful results for bounded random variables</li>
                    <li><strong>Advanced Generalizations</strong> - Non-identical distributions and more</li>
                </ol>
            </div>

                </section>

        <!-- Sub-Gaussian Random Variables Section -->
        <section id="subgaussian" class="section">
            <h2>4. Sub-Gaussian Random Variables: Beyond Normal Distributions</h2>
            
            <p>The exponential tail bounds we derived for Gaussian random variables are remarkably powerful, but they apply to a much broader class of random variables called <em>sub-Gaussian</em> random variables. This generalization is one of the most important concepts in modern concentration theory.</p>

            <div class="insight-box">
                <h4>The Big Picture</h4>
                <p>Sub-Gaussian random variables are those whose tails decay at least as fast as a Gaussian distribution. This includes bounded random variables, many common distributions, and even some with infinite support!</p>
            </div>

            <h3>Definition and Intuition</h3>

            <div class="theorem-box">
                <h4>Sub-Gaussian Random Variables</h4>
                <p>A random variable $X$ with mean $\mu$ is called <strong>$\sigma$-sub-Gaussian</strong> if its moment generating function satisfies:</p>
                <div class="formula-highlight">
                    $$E[e^{t(X-\mu)}] \leq e^{\frac{\sigma^2 t^2}{2}} \quad \text{for all } t \in \mathbb{R}$$
                </div>
                <p>Equivalently, the MGF of $X-\mu$ is dominated by the MGF of a Gaussian with variance $\sigma^2$.</p>
            </div>

            <div class="example-box">
                <h4>Understanding the Definition</h4>
                <div class="step-list">
                    <ul>
                        <li><strong>Gaussian case:</strong> If $X \sim N(\mu, \sigma^2)$, then equality holds, so Gaussian variables are exactly $\sigma$-sub-Gaussian</li>
                        <li><strong>Bounded case:</strong> If $X$ is bounded in $[a,b]$, then $X$ is $(b-a)$-sub-Gaussian (we'll prove this!)</li>
                        <li><strong>Heavy tails:</strong> Variables with heavier tails than Gaussian (like Cauchy) are not sub-Gaussian</li>
                        <li><strong>Light tails:</strong> Variables with lighter tails (like bounded) are sub-Gaussian with smaller parameters</li>
                    </ul>
                </div>
            </div>

            <div class="svg-container">
                <h4>Sub-Gaussian Concept Visualization</h4>
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Sub-Gaussian Random Variables: MGF Domination
                    </text>
                    
                    <!-- Coordinate system -->
                    <line x1="100" y1="400" x2="700" y2="400" stroke="#333" stroke-width="2"/>
                    <line x1="100" y1="400" x2="100" y2="100" stroke="#333" stroke-width="2"/>
                    
                    <!-- Axis labels -->
                    <text x="400" y="430" text-anchor="middle" font-size="14" fill="#333">t</text>
                    <text x="70" y="250" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90, 70, 250)">MGF</text>
                    
                    <!-- Gaussian MGF (reference) -->
                    <path d="M 100 380 Q 200 360, 300 320 Q 400 280, 500 240 Q 600 200, 700 150" 
                          stroke="#4caf50" stroke-width="3" fill="none"/>
                    <text x="720" y="155" font-size="12" fill="#4caf50" font-weight="bold">e^(σ²t²/2)</text>
                    
                    <!-- Sub-Gaussian MGF (below) -->
                    <path d="M 100 385 Q 200 370, 300 340 Q 400 310, 500 280 Q 600 250, 700 200" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    <text x="720" y="205" font-size="12" fill="#2196f3" font-weight="bold">E[e^(t(X-μ))]</text>
                    
                    <!-- Super-Gaussian MGF (above) -->
                    <path d="M 100 375 Q 200 350, 300 300 Q 400 250, 500 180 Q 600 120, 700 80" 
                          stroke="#f44336" stroke-width="3" fill="none" stroke-dasharray="5,5"/>
                    <text x="720" y="85" font-size="12" fill="#f44336">Heavy-tailed</text>
                    
                    <!-- Shaded region showing domination -->
                    <path d="M 100 380 Q 200 360, 300 320 Q 400 280, 500 240 Q 600 200, 700 150 
                             L 700 400 L 100 400 Z" 
                          fill="#4caf50" opacity="0.1"/>
                    
                    <!-- Annotations -->
                    <text x="400" y="60" text-anchor="middle" font-size="14" fill="#2e7d32" font-weight="bold">
                        Sub-Gaussian Region
                    </text>
                    <text x="400" y="80" text-anchor="middle" font-size="12" fill="#2e7d32">
                        MGF ≤ Gaussian MGF
                    </text>
                    
                    <!-- Arrow pointing to region -->
                    <path d="M 400 90 L 350 200" stroke="#2e7d32" stroke-width="2" marker-end="url(#arrowhead3)"/>
                    
                    <!-- Examples boxes -->
                    <rect x="50" y="450" width="200" height="40" fill="#e3f2fd" stroke="#2196f3" rx="5"/>
                    <text x="150" y="470" text-anchor="middle" font-size="12" fill="#1976d2" font-weight="bold">
                        Examples: Bounded, Rademacher
                    </text>
                    
                    <rect x="300" y="450" width="200" height="40" fill="#e8f5e8" stroke="#4caf50" rx="5"/>
                    <text x="400" y="470" text-anchor="middle" font-size="12" fill="#2e7d32" font-weight="bold">
                        Gaussian (Boundary Case)
                    </text>
                    
                    <rect x="550" y="450" width="200" height="40" fill="#ffebee" stroke="#f44336" rx="5"/>
                    <text x="650" y="470" text-anchor="middle" font-size="12" fill="#c62828" font-weight="bold">
                        Heavy-tailed (Not Sub-Gaussian)
                    </text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#2e7d32"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Tail Bounds for Sub-Gaussian Variables</h3>

            <div class="theorem-box">
                <h4>Sub-Gaussian Tail Bounds</h4>
                <p>If $X$ is $\sigma$-sub-Gaussian with mean $\mu$, then:</p>
                <div class="formula-highlight">
                    $$P(|X - \mu| \geq u) \leq 2e^{-\frac{u^2}{2\sigma^2}}$$
                </div>
                <p>This is exactly the same bound as for Gaussian random variables!</p>
            </div>

            <div class="proof-box">
                <h4>Proof Sketch</h4>
                <p>The proof follows immediately from the Chernoff method:</p>
                <div class="step-list">
                    <ol>
                        <li>Apply Chernoff bound using the sub-Gaussian condition</li>
                        <li>The optimization yields the same optimal $t^* = \frac{u}{\sigma^2}$</li>
                        <li>The bound becomes identical to the Gaussian case</li>
                    </ol>
                </div>
            </div>

            <h3>Averages of Sub-Gaussian Variables</h3>

            <div class="theorem-box">
                <h4>Sub-Gaussian Averages</h4>
                <p>If $X_1, \ldots, X_n$ are independent $\sigma$-sub-Gaussian random variables with means $\mu_1, \ldots, \mu_n$, then their average:</p>
                <div class="formula-highlight">
                    $$\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i$$
                </div>
                <p>is $\frac{\sigma}{\sqrt{n}}$-sub-Gaussian. This gives us:</p>
                <div class="formula-highlight">
                    $$P\left(\left|\bar{X}_n - \frac{1}{n}\sum_{i=1}^n \mu_i\right| \geq \frac{k\sigma}{\sqrt{n}}\right) \leq 2e^{-\frac{k^2}{2}}$$
                </div>
            </div>

            <div class="example-box">
                <h4>The Power of Sub-Gaussian Theory</h4>
                <p>This result is incredibly powerful because:</p>
                <div class="step-list">
                    <ul>
                        <li><strong>Universality:</strong> The same exponential bound applies to any sub-Gaussian variables</li>
                        <li><strong>Rate:</strong> The $1/\sqrt{n}$ convergence rate is optimal</li>
                        <li><strong>Constants:</strong> The exponential constant is often sharp</li>
                        <li><strong>Generality:</strong> Works for bounded, Gaussian, and many other distributions</li>
                    </ul>
                </div>
            </div>

        </section>

        <!-- Bounded Random Variables - Hoeffding's Bound Section -->
        <section id="hoeffding" class="section">
            <h2>5. Bounded Random Variables: Hoeffding's Bound</h2>
            
            <p>One of the most important classes of sub-Gaussian random variables consists of bounded random variables. Hoeffding's bound provides the fundamental concentration inequality for such variables and has countless applications in statistics, machine learning, and computer science.</p>

            <h3>Rademacher Random Variables: A Simple Start</h3>

            <div class="example-box">
                <h4>Example 1: Rademacher Variables</h4>
                <p>Let's start with the simplest case: <strong>Rademacher random variables</strong> that take values $\{+1, -1\}$ with equal probability.</p>
                
                <div class="step-list">
                    <h5>Computing the MGF:</h5>
                    <div class="formula-highlight">
                        $$E[e^{tX}] = \frac{1}{2}[e^t + e^{-t}] = \frac{1}{2}\left[\sum_{k=0}^{\infty} \frac{t^k}{k!} + \sum_{k=0}^{\infty} \frac{(-t)^k}{k!}\right]$$
                    </div>
                    
                    <p>Notice that odd powers cancel, leaving only even powers:</p>
                    <div class="formula-highlight">
                        $$E[e^{tX}] = \sum_{k=0}^{\infty} \frac{t^{2k}}{(2k)!} \leq \sum_{k=0}^{\infty} \frac{t^{2k}}{2^k k!} = e^{t^2/2}$$
                    </div>
                    
                    <p>Therefore, Rademacher variables are <strong>1-sub-Gaussian</strong>.</p>
                </div>
            </div>

            <h3>Jensen's Inequality: A Crucial Tool</h3>

            <div class="theorem-box">
                <h4>Jensen's Inequality</h4>
                <p>For a convex function $g: \mathbb{R} \to \mathbb{R}$ and any random variable $X$:</p>
                <div class="formula-highlight">
                    $$E[g(X)] \geq g(E[X])$$
                </div>
                <p>If $g$ is concave, the inequality reverses.</p>
            </div>

            <div class="proof-box">
                <h4>Geometric Intuition</h4>
                <p>Jensen's inequality essentially says that the expected value of a convex function is at least the function evaluated at the expected value. This is because convex functions lie above their tangent lines.</p>
            </div>

            <div class="svg-container">
                <h4>Jensen's Inequality Visualization</h4>
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Jensen's Inequality for Convex Functions
                    </text>
                    
                    <!-- Coordinate system -->
                    <line x1="80" y1="350" x2="520" y2="350" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="350" x2="80" y2="80" stroke="#333" stroke-width="2"/>
                    
                    <!-- Convex function -->
                    <path d="M 100 320 Q 200 200, 300 150 Q 400 120, 500 140" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    <text x="520" y="145" font-size="12" fill="#2196f3" font-weight="bold">g(x)</text>
                    
                    <!-- Random variable values -->
                    <circle cx="180" cy="350" r="3" fill="#f44336"/>
                    <circle cx="220" cy="350" r="3" fill="#f44336"/>
                    <circle cx="280" cy="350" r="3" fill="#f44336"/>
                    <circle cx="320" cy="350" r="3" fill="#f44336"/>
                    <circle cx="380" cy="350" r="3" fill="#f44336"/>
                    
                    <!-- Expected value -->
                    <circle cx="270" cy="350" r="5" fill="#4caf50"/>
                    <text x="275" y="370" font-size="12" fill="#4caf50" font-weight="bold">E[X]</text>
                    
                    <!-- Function values at random points -->
                    <line x1="180" y1="350" x2="180" y2="250" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    <line x1="220" y1="350" x2="220" y2="210" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    <line x1="280" y1="350" x2="280" y2="160" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    <line x1="320" y1="350" x2="320" y2="140" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    <line x1="380" y1="350" x2="380" y2="130" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    
                    <!-- g(E[X]) -->
                    <line x1="270" y1="350" x2="270" y2="155" stroke="#4caf50" stroke-width="3"/>
                    <text x="275" y="150" font-size="12" fill="#4caf50" font-weight="bold">g(E[X])</text>
                    
                    <!-- E[g(X)] line -->
                    <line x1="60" y1="180" x2="400" y2="180" stroke="#ff9800" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="410" y="185" font-size="12" fill="#ff9800" font-weight="bold">E[g(X)]</text>
                    
                    <!-- Arrow showing inequality -->
                    <path d="M 300 165 L 300 175" stroke="#e91e63" stroke-width="3" marker-end="url(#arrowhead4)"/>
                    <text x="310" y="170" font-size="12" fill="#e91e63" font-weight="bold">≥</text>
                    
                    <!-- Annotation -->
                    <rect x="420" y="250" width="150" height="80" fill="#e8f5e8" stroke="#4caf50" rx="5"/>
                    <text x="495" y="275" text-anchor="middle" font-size="12" fill="#2e7d32" font-weight="bold">
                        Jensen's Inequality:
                    </text>
                    <text x="495" y="295" text-anchor="middle" font-size="11" fill="#2e7d32">
                        E[g(X)] ≥ g(E[X])
                    </text>
                    <text x="495" y="315" text-anchor="middle" font-size="10" fill="#2e7d32">
                        for convex g
                    </text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead4" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#e91e63"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>General Bounded Random Variables</h3>

            <div class="example-box">
                <h4>Example 2: Bounded Random Variables</h4>
                <p>Let $X$ be a random variable with zero mean and support on $[a, b]$. We will prove that $X$ is $(b-a)$-sub-Gaussian using a beautiful technique called <em>symmetrization</em>.</p>
                
                <div class="proof-box">
                    <h5>Detailed Proof that $X$ is $(b-a)$-sub-Gaussian:</h5>
                    
                    <div class="step-list">
                        <h6><strong>Step 1: Independence and Jensen's Inequality</strong></h6>
                        <p><strong>Setup:</strong> Let $X'$ be an independent copy of $X$ (same distribution, independent of $X$).</p>
                        
                        <p><strong>Key insight:</strong> Since $X$ has zero mean, we have $E[X'] = E[X] = 0$, so:</p>
                        <div class="formula-highlight">
                            $$E[e^{tX}] = E[e^{t(X - 0)}] = E[e^{t(X - E[X'])}]$$
                        </div>
                        
                        <p><strong>Apply Jensen's inequality:</strong> The exponential function $f(u) = e^{tu}$ is convex. By Jensen's inequality:</p>
                        <div class="formula-highlight">
                            $$E[e^{t(X - E[X'])}] \leq E[e^{t(X - X')}]$$
                        </div>
                        
                        <p><strong>Why this works:</strong> We're replacing the constant $E[X']$ with the random variable $X'$. Since the exponential is convex, taking expectation over the random $X'$ can only increase the value.</p>
                        
                        <p><strong>Technical detail:</strong> More precisely, by the tower property of expectation:</p>
                        <div class="formula-highlight">
                            $$E[e^{t(X - E[X'])}] = E_X[e^{t(X - E[X'])}] \leq E_X[E_{X'}[e^{t(X - X')}]] = E_{X,X'}[e^{t(X-X')}]$$
                        </div>
                    </div>
                    
                    <div class="step-list">
                        <h6><strong>Step 2: Symmetrization Technique</strong></h6>
                        <p><strong>Key observation:</strong> Let $\epsilon$ be a Rademacher random variable (takes values $\{+1, -1\}$ with equal probability), independent of $X$ and $X'$.</p>
                        
                        <p><strong>Claim:</strong> The random variables $(X-X')$ and $\epsilon(X-X')$ have the same distribution.</p>
                        
                        <p><strong>Proof of claim:</strong> For any measurable set $A$:</p>
                        <div class="formula-highlight">
                            \begin{align}
                            P(\epsilon(X-X') \in A) &= P(\epsilon = 1) P(X-X' \in A) + P(\epsilon = -1) P(-(X-X') \in A)\\
                            &= \frac{1}{2} P(X-X' \in A) + \frac{1}{2} P(X'-X \in A)
                            \end{align}
                        </div>
                        
                        <p><strong>Why they're equal:</strong> Since $X$ and $X'$ are independent and identically distributed, $(X-X')$ and $(X'-X)$ have the same distribution. Therefore:</p>
                        <div class="formula-highlight">
                            $$P(\epsilon(X-X') \in A) = \frac{1}{2} P(X-X' \in A) + \frac{1}{2} P(X-X' \in A) = P(X-X' \in A)$$
                        </div>
                        
                        <p><strong>Consequence:</strong> We can replace $(X-X')$ with $\epsilon(X-X')$ in expectation:</p>
                        <div class="formula-highlight">
                            $$E_{X,X'}[e^{t(X-X')}] = E_{X,X',\epsilon}[e^{t\epsilon(X-X')}]$$
                        </div>
                    </div>
                    
                    <div class="step-list">
                        <h6><strong>Step 3: Apply Rademacher Sub-Gaussian Bound</strong></h6>
                        <p><strong>Conditioning strategy:</strong> We condition on the values of $X$ and $X'$, treating $(X-X')$ as a fixed constant.</p>
                        
                        <p><strong>Apply Rademacher result:</strong> From our earlier analysis, we know that for any constant $c$, if $\epsilon$ is Rademacher:</p>
                        <div class="formula-highlight">
                            $$E_\epsilon[e^{t\epsilon c}] \leq e^{t^2c^2/2}$$
                        </div>
                        
                        <p><strong>Conditioning on $(X,X')$:</strong> Treating $(X-X')$ as the constant $c$:</p>
                        <div class="formula-highlight">
                            $$E_\epsilon[e^{t\epsilon(X-X')} \mid X,X'] \leq e^{t^2(X-X')^2/2}$$
                        </div>
                        
                        <p><strong>Take expectation over $(X,X')$:</strong></p>
                        <div class="formula-highlight">
                            $$E_{X,X',\epsilon}[e^{t\epsilon(X-X')}] = E_{X,X'}[E_\epsilon[e^{t\epsilon(X-X')} \mid X,X']] \leq E_{X,X'}[e^{t^2(X-X')^2/2}]$$
                        </div>
                    </div>
                    
                    <div class="step-list">
                        <h6><strong>Step 4: Use Boundedness to Complete the Proof</strong></h6>
                        <p><strong>Boundedness constraint:</strong> Since $X \in [a,b]$ and $X' \in [a,b]$:</p>
                        <div class="formula-highlight">
                            $$|X - X'| \leq \max_{x,x' \in [a,b]} |x - x'| = b - a$$
                        </div>
                        
                        <p><strong>Apply the bound:</strong> Since $(X-X')^2 \leq (b-a)^2$:</p>
                        <div class="formula-highlight">
                            $$E_{X,X'}[e^{t^2(X-X')^2/2}] \leq E_{X,X'}[e^{t^2(b-a)^2/2}] = e^{t^2(b-a)^2/2}$$
                        </div>
                        
                        <p><strong>Chain the inequalities:</strong> Combining all steps:</p>
                        <div class="formula-highlight">
                            \begin{align}
                            E[e^{tX}] &\leq E_{X,X'}[e^{t(X-X')}] \quad \text{(Step 1)}\\
                            &= E_{X,X',\epsilon}[e^{t\epsilon(X-X')}] \quad \text{(Step 2)}\\
                            &\leq E_{X,X'}[e^{t^2(X-X')^2/2}] \quad \text{(Step 3)}\\
                            &\leq e^{t^2(b-a)^2/2} \quad \text{(Step 4)}
                            \end{align}
                        </div>
                        
                        <p><strong>Conclusion:</strong> This shows that $E[e^{tX}] \leq e^{t^2(b-a)^2/2}$ for all $t \in \mathbb{R}$, which is exactly the definition of being $(b-a)$-sub-Gaussian!</p>
                    </div>
                    
                    <div class="insight-box">
                        <h5>Why This Proof is Beautiful</h5>
                        <p>The symmetrization technique is one of the most elegant tools in probability theory:</p>
                        <ul>
                            <li><strong>Universality:</strong> Works for any bounded random variable, regardless of its specific distribution</li>
                            <li><strong>Reduction:</strong> Reduces the problem to understanding Rademacher variables</li>
                            <li><strong>Optimality:</strong> The $(b-a)$ bound is actually tight in many cases</li>
                            <li><strong>Generality:</strong> The technique extends to much more general settings</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="theorem-box">
                <h4>Hoeffding's Bound</h4>
                <p>Let $X_1, \ldots, X_n$ be independent random variables with $a_i \leq X_i \leq b_i$ for all $i$. Then:</p>
                <div class="formula-highlight">
                    $$P\left(\left|\frac{1}{n}\sum_{i=1}^n X_i - \frac{1}{n}\sum_{i=1}^n E[X_i]\right| \geq t\right) \leq 2\exp\left(-\frac{2n^2t^2}{\sum_{i=1}^n (b_i-a_i)^2}\right)$$
                </div>
                <p>For identically distributed variables with $a \leq X_i \leq b$:</p>
                <div class="formula-highlight">
                    $$P\left(\left|\bar{X}_n - \mu\right| \geq t\right) \leq 2\exp\left(-\frac{2nt^2}{(b-a)^2}\right)$$
                </div>
            </div>

            <div class="comparison-box">
                <h4>Hoeffding vs Other Bounds</h4>
                <div class="two-column">
                    <div>
                        <h5>Advantages of Hoeffding's Bound</h5>
                        <ul>
                            <li>Only requires boundedness (very general)</li>
                            <li>Exponential concentration</li>
                            <li>Sharp constants in many cases</li>
                            <li>No distributional assumptions needed</li>
                        </ul>
                    </div>
                    <div>
                        <h5>Applications</h5>
                        <ul>
                            <li>Empirical risk minimization</li>
                            <li>Survey sampling</li>
                            <li>Monte Carlo methods</li>
                            <li>Online learning algorithms</li>
                        </ul>
                    </div>
                </div>
            </div>

        </section>

        <!-- Simple Generalization Section -->
        <section id="generalization" class="section">
            <h2>6. A Simple Generalization: Non-Identical Distributions</h2>
            
            <p>One of the most powerful aspects of concentration inequalities is that they extend naturally to cases where the random variables are not identically distributed. This generalization is crucial for many practical applications.</p>

            <div class="insight-box">
                <h4>Why This Matters</h4>
                <p>In real applications, we often deal with heterogeneous data where different observations might have different variances or come from related but distinct distributions. The generalization shows that concentration still occurs!</p>
            </div>

            <h3>The General Result</h3>

            <div class="theorem-box">
                <h4>Concentration for Non-Identical Sub-Gaussian Variables</h4>
                <p>Suppose $X_1, \ldots, X_n$ are independent random variables where $X_i$ is $\sigma_i$-sub-Gaussian. Then their average:</p>
                <div class="formula-highlight">
                    $$\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i$$
                </div>
                <p>is $\sigma$-sub-Gaussian, where:</p>
                <div class="formula-highlight">
                    $$\sigma = \frac{1}{n}\sqrt{\sum_{i=1}^n \sigma_i^2}$$
                </div>
                <p>This yields the concentration inequality:</p>
                <div class="formula-highlight">
                    $$P\left(\left|\bar{X}_n - \frac{1}{n}\sum_{i=1}^n E[X_i]\right| \geq t\right) \leq 2\exp\left(-\frac{n^2t^2}{2\sum_{i=1}^n \sigma_i^2}\right)$$
                </div>
            </div>

            <div class="proof-box">
                <h4>Proof Sketch</h4>
                <div class="step-list">
                    <ol>
                        <li><strong>Independence:</strong> For independent sub-Gaussian variables:
                            $$E[e^{t(\bar{X}_n - E[\bar{X}_n])}] = \prod_{i=1}^n E[e^{t(X_i - E[X_i])/n}]$$
                        </li>
                        <li><strong>Sub-Gaussian property:</strong> Each factor is bounded:
                            $$E[e^{t(X_i - E[X_i])/n}] \leq e^{\sigma_i^2 t^2/(2n^2)}$$
                        </li>
                        <li><strong>Combine:</strong> Taking the product:
                            $$E[e^{t(\bar{X}_n - E[\bar{X}_n])}] \leq \exp\left(\frac{t^2}{2n^2}\sum_{i=1}^n \sigma_i^2\right)$$
                        </li>
                    </ol>
                </div>
            </div>

            <h3>Special Cases and Examples</h3>

            <div class="example-box">
                <h4>Example 1: Mixed Bounded Variables</h4>
                <p>Suppose we have:</p>
                <ul>
                    <li>$X_1, \ldots, X_{n/2}$ bounded in $[-1, 1]$ (so $\sigma_i = 2$)</li>
                    <li>$X_{n/2+1}, \ldots, X_n$ bounded in $[-2, 2]$ (so $\sigma_i = 4$)</li>
                </ul>
                
                <p>Then the effective sub-Gaussian parameter is:</p>
                <div class="formula-highlight">
                    $$\sigma = \frac{1}{n}\sqrt{\frac{n}{2} \cdot 4 + \frac{n}{2} \cdot 16} = \frac{1}{n}\sqrt{10n} = \sqrt{\frac{10}{n}}$$
                </div>
                
                <p>The concentration bound becomes:</p>
                <div class="formula-highlight">
                    $$P(|\bar{X}_n - E[\bar{X}_n]| \geq t) \leq 2\exp\left(-\frac{nt^2}{20}\right)$$
                </div>
            </div>

            <div class="example-box">
                <h4>Example 2: Varying Gaussian Variables</h4>
                <p>Consider $X_i \sim N(\mu_i, \sigma_i^2)$ with different variances. The average concentrates as:</p>
                <div class="formula-highlight">
                    $$P\left(\left|\bar{X}_n - \frac{1}{n}\sum_{i=1}^n \mu_i\right| \geq t\right) \leq 2\exp\left(-\frac{n^2t^2}{2\sum_{i=1}^n \sigma_i^2}\right)$$
                </div>
                
                <p>Notice that variables with larger variances contribute more to the denominator, making concentration slower.</p>
            </div>

            <div class="svg-container">
                <h4>Heterogeneous Concentration Visualization</h4>
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Concentration with Heterogeneous Variables
                    </text>
                    
                    <!-- Three different distributions -->
                    <text x="150" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="#2196f3">
                        Tight (σ₁ = 1)
                    </text>
                    <ellipse cx="150" cy="120" rx="30" ry="15" fill="#2196f3" opacity="0.3"/>
                    <path d="M 120 120 Q 135 110, 150 115 Q 165 110, 180 120" stroke="#2196f3" stroke-width="2"/>
                    
                    <text x="350" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="#ff9800">
                        Medium (σ₂ = 2)
                    </text>
                    <ellipse cx="350" cy="120" rx="60" ry="25" fill="#ff9800" opacity="0.3"/>
                    <path d="M 290 120 Q 320 100, 350 110 Q 380 100, 410 120" stroke="#ff9800" stroke-width="2"/>
                    
                    <text x="550" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="#f44336">
                        Wide (σ₃ = 3)
                    </text>
                    <ellipse cx="550" cy="120" rx="90" ry="35" fill="#f44336" opacity="0.3"/>
                    <path d="M 460 120 Q 505 90, 550 105 Q 595 90, 640 120" stroke="#f44336" stroke-width="2"/>
                    
                    <!-- Arrows pointing down -->
                    <path d="M 150 140 L 150 180" stroke="#666" stroke-width="2" marker-end="url(#arrowhead5)"/>
                    <path d="M 350 140 L 350 180" stroke="#666" stroke-width="2" marker-end="url(#arrowhead5)"/>
                    <path d="M 550 140 L 550 180" stroke="#666" stroke-width="2" marker-end="url(#arrowhead5)"/>
                    
                    <text x="400" y="170" text-anchor="middle" font-size="12" fill="#666">
                        Average
                    </text>
                    
                    <!-- Combined distribution -->
                    <text x="400" y="220" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">
                        Combined: σ = √(σ₁² + σ₂² + σ₃²)/n
                    </text>
                    
                    <ellipse cx="400" cy="280" rx="80" ry="30" fill="#4caf50" opacity="0.3"/>
                    <path d="M 320 280 Q 360 260, 400 270 Q 440 260, 480 280" stroke="#4caf50" stroke-width="3"/>
                    
                    <!-- Formula box -->
                    <rect x="200" y="320" width="400" height="60" fill="#e8f5e8" stroke="#4caf50" rx="10"/>
                    <text x="400" y="340" text-anchor="middle" font-size="12" fill="#2e7d32" font-weight="bold">
                        Effective Sub-Gaussian Parameter:
                    </text>
                    <text x="400" y="360" text-anchor="middle" font-size="14" fill="#2e7d32">
                        σ = (1/n)√(1² + 2² + 3²) = √14/n
                    </text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead5" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Key Insights and Applications</h3>

            <div class="insight-box">
                <h4>Important Observations</h4>
                <div class="step-list">
                    <ul>
                        <li><strong>Weighted average:</strong> The effective variance is a weighted average of individual variances</li>
                        <li><strong>Bottleneck effect:</strong> A few variables with large $\sigma_i$ can dominate the bound</li>
                        <li><strong>Still exponential:</strong> We maintain exponential concentration even with heterogeneity</li>
                        <li><strong>Practical relevance:</strong> This is the setting for most real-world applications</li>
                    </ul>
                </div>
            </div>

            <div class="example-box">
                <h4>Real-World Applications</h4>
                <div class="two-column">
                    <div>
                        <h5>Machine Learning</h5>
                        <ul>
                            <li>Different features may have different scales</li>
                            <li>Training examples from different sources</li>
                            <li>Heterogeneous sensor data</li>
                        </ul>
                    </div>
                    <div>
                        <h5>Statistics</h5>
                        <ul>
                            <li>Survey data with varying response rates</li>
                            <li>Medical trials with different patient groups</li>
                            <li>Economic data across different regions</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="summary-box">
                <h4>Summary: The Power of Concentration Inequalities</h4>
                <p>We've journeyed from basic Markov and Chebyshev inequalities to sophisticated exponential bounds. The key insights are:</p>
                <div class="step-list">
                    <ol>
                        <li><strong>Universal patterns:</strong> The $1/\sqrt{n}$ rate appears everywhere</li>
                        <li><strong>Exponential bounds:</strong> Sub-Gaussian variables give exponential concentration</li>
                        <li><strong>Practical tools:</strong> Hoeffding's bound works for any bounded variables</li>
                        <li><strong>Robustness:</strong> Results extend to heterogeneous settings</li>
                    </ol>
                </div>
                <p>These tools form the foundation of modern statistical learning theory, providing the theoretical guarantees that make machine learning algorithms reliable and predictable.</p>
            </div>

        </section>

    </div>
</body>
</html>

        <!-- Markov Inequality Section -->
        <section id="markov" class="section">
            <h2>1. Markov Inequality: The Foundation</h2>
            
            <p>Markov's inequality is the most elementary tail bound and serves as the foundation for all other concentration inequalities. Despite its simplicity, it provides fundamental insights into the behavior of random variables.</p>

            <div class="theorem-box">
                <h4>Markov's Inequality</h4>
                <p>For a positive random variable $X \geq 0$ and any $t > 0$:</p>
                <div class="formula-highlight">
                    $$P(X \geq t) \leq \frac{E[X]}{t}$$
                </div>
            </div>

            <div class="insight-box">
                <h4>Intuitive Understanding</h4>
                <p>If the mean of a positive random variable is small, then it is unlikely to be large too often. The probability that it takes large values must be small.</p>
            </div>

            <h3>Step-by-Step Proof</h3>

            <div class="proof-box">
                <h4>Proof of Markov's Inequality</h4>
                <div class="step-list">
                    <ol>
                        <li><strong>Define an indicator function:</strong> For arbitrary $t > 0$, define:
                            $$I(t) = \begin{cases} 
                            1 & \text{if } X \geq t \\
                            0 & \text{if } X < t 
                            \end{cases}$$
                        </li>
                        <li><strong>Key observation:</strong> Since $X \geq 0$ and we're interested in $X \geq t$:
                            $$tI(t) \leq X$$
                        </li>
                        <li><strong>Take expectations:</strong> 
                            $$E[tI(t)] \leq E[X]$$
                        </li>
                        <li><strong>Simplify the left side:</strong>
                            $$tE[I(t)] = tP(X \geq t) \leq E[X]$$
                        </li>
                        <li><strong>Divide by $t$:</strong>
                            $$P(X \geq t) \leq \frac{E[X]}{t}$$
                        </li>
                    </ol>
                </div>
            </div>

            <div class="svg-container">
                <h4>Visual Representation of Markov's Inequality</h4>
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#fafafa" rx="10"/>
                    
                    <!-- Axes -->
                    <line x1="60" y1="350" x2="740" y2="350" stroke="#333" stroke-width="2"/>
                    <line x1="60" y1="350" x2="60" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Axis labels -->
                    <text x="400" y="380" text-anchor="middle" font-size="14" fill="#333">x</text>
                    <text x="30" y="200" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90, 30, 200)">Probability Density</text>
                    
                    <!-- Exponential distribution curve -->
                    <path d="M 60 100 Q 120 120, 180 150 Q 280 200, 400 250 Q 520 300, 650 340 Q 700 350, 740 350" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    
                    <!-- Threshold line -->
                    <line x1="400" y1="50" x2="400" y2="350" stroke="#f44336" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="405" y="40" font-size="12" fill="#f44336" font-weight="bold">t</text>
                    
                    <!-- Shaded area representing P(X ≥ t) -->
                    <path d="M 400 250 Q 520 300, 650 340 Q 700 350, 740 350 L 740 350 L 400 350 Z" 
                          fill="#f44336" opacity="0.3"/>
                    
                    <!-- Labels -->
                    <text x="550" y="320" font-size="14" fill="#f44336" font-weight="bold">P(X ≥ t)</text>
                    <text x="200" y="180" font-size="14" fill="#2196f3" font-weight="bold">f(x)</text>
                    
                    <!-- Mean indicator -->
                    <line x1="200" y1="50" x2="200" y2="350" stroke="#4caf50" stroke-width="2" stroke-dasharray="3,3"/>
                    <text x="205" y="40" font-size="12" fill="#4caf50" font-weight="bold">E[X]</text>
                    
                    <!-- Inequality illustration -->
                    <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Markov's Inequality: P(X ≥ t) ≤ E[X]/t
                    </text>
                    
                    <!-- Rectangle showing the bound -->
                    <rect x="500" y="80" width="200" height="60" fill="#ff9800" opacity="0.2" stroke="#ff9800" stroke-width="2"/>
                    <text x="600" y="105" text-anchor="middle" font-size="12" fill="#e65100">Upper Bound</text>
                    <text x="600" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#e65100">E[X]/t</text>
                </svg>
            </div>

            <h3>Practical Example</h3>

            <div class="example-box">
                <h4>Example: Exponential Random Variable</h4>
                <p>Let $X \sim \text{Exp}(\lambda)$ with $E[X] = 1/\lambda$. Using Markov's inequality:</p>
                <div class="formula-highlight">
                    $$P(X \geq t) \leq \frac{1/\lambda}{t} = \frac{1}{\lambda t}$$
                </div>
                <p><strong>Comparison with exact probability:</strong> For an exponential distribution, the exact probability is $P(X \geq t) = e^{-\lambda t}$.</p>
                
                <div class="two-column">
                    <div>
                        <h5>Markov Bound</h5>
                        <p>$P(X \geq t) \leq \frac{1}{\lambda t}$</p>
                        <p>Decreases as $O(1/t)$</p>
                    </div>
                    <div>
                        <h5>Exact Probability</h5>
                        <p>$P(X \geq t) = e^{-\lambda t}$</p>
                        <p>Decreases exponentially</p>
                    </div>
                </div>
                
                <p><strong>Note:</strong> Markov's bound is quite loose but universally applicable!</p>
            </div>

            <h3>Limitations and Insights</h3>

            <div class="comparison-box">
                <h4>Strengths and Weaknesses of Markov's Inequality</h4>
                <div class="two-column">
                    <div>
                        <h5>✅ Strengths</h5>
                        <ul>
                            <li>Extremely general - works for any positive random variable</li>
                            <li>Only requires knowledge of the mean</li>
                            <li>Simple to apply and understand</li>
                            <li>Foundation for more sophisticated bounds</li>
                        </ul>
                    </div>
                    <div>
                        <h5>❌ Limitations</h5>
                        <ul>
                            <li>Often quite loose (not tight)</li>
                            <li>Only provides one-sided bounds</li>
                            <li>Doesn't use information about variance</li>
                            <li>Polynomial decay rate</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <h4>Markov Bound Quality Visualization</h4>
                <svg width="800" height="300" viewBox="0 0 800 300">
                    <!-- Background -->
                    <rect width="800" height="300" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Comparing Markov Bound with True Probabilities
                    </text>
                    
                    <!-- Axes -->
                    <line x1="80" y1="250" x2="720" y2="250" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="250" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Axis labels -->
                    <text x="400" y="280" text-anchor="middle" font-size="12" fill="#333">Threshold t</text>
                    <text x="30" y="150" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90, 30, 150)">Probability</text>
                    
                    <!-- Markov bound (1/t curve) -->
                    <path d="M 100 60 Q 200 80, 300 120 Q 500 180, 700 240" 
                          stroke="#f44336" stroke-width="3" fill="none" stroke-dasharray="5,5"/>
                    
                    <!-- True exponential curve -->
                    <path d="M 100 80 Q 200 140, 300 190 Q 500 230, 700 248" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    
                    <!-- Legend -->
                    <rect x="520" y="60" width="200" height="80" fill="white" stroke="#ccc" rx="5"/>
                    <line x1="530" y1="80" x2="560" y2="80" stroke="#f44336" stroke-width="3" stroke-dasharray="5,5"/>
                    <text x="570" y="85" font-size="12" fill="#f44336">Markov Bound</text>
                    
                    <line x1="530" y1="100" x2="560" y2="100" stroke="#2196f3" stroke-width="3"/>
                    <text x="570" y="105" font-size="12" fill="#2196f3">True Probability</text>
                    
                    <text x="570" y="125" font-size="10" fill="#666">(Exponential distribution)</text>
                    
                    <!-- Gap indication -->
                    <text x="300" y="100" font-size="12" fill="#666" text-anchor="middle">Gap shows</text>
                    <text x="300" y="115" font-size="12" fill="#666" text-anchor="middle">looseness</text>
                    
                </svg>
            </div>

        </section>

        <!-- Chebyshev Inequality Section -->
        <section id="chebyshev" class="section">
            <h2>2. Chebyshev Inequality: Using Variance Information</h2>
            
            <p>Chebyshev's inequality is our first major improvement over Markov's inequality. By utilizing information about the variance of a random variable, we can obtain much tighter bounds, especially for deviations around the mean.</p>

            <div class="theorem-box">
                <h4>Chebyshev's Inequality</h4>
                <p>For a random variable $X$ with mean $\mu = E[X]$ and variance $\sigma^2 = \text{Var}(X)$, and for any $k \geq 0$:</p>
                <div class="formula-highlight">
                    $$P(|X - \mu| \geq k\sigma) \leq \frac{1}{k^2}$$
                </div>
                <p>Equivalently, for any $t > 0$:</p>
                <div class="formula-highlight">
                    $$P(|X - \mu| \geq t) \leq \frac{\sigma^2}{t^2}$$
                </div>
            </div>

            <div class="insight-box">
                <h4>Key Improvement Over Markov</h4>
                <p>Chebyshev provides a <strong>two-sided bound</strong> around the mean and uses variance information to give quadratically better decay: $O(1/t^2)$ instead of $O(1/t)$.</p>
            </div>

            <h3>Proof Strategy</h3>

            <div class="proof-box">
                <h4>Proof of Chebyshev's Inequality</h4>
                <p>The elegant proof shows how Chebyshev follows directly from Markov's inequality:</p>
                <div class="step-list">
                    <ol>
                        <li><strong>Rewrite the event:</strong> 
                            $$P(|X - \mu| \geq k\sigma) = P(|X - \mu|^2 \geq k^2\sigma^2)$$
                        </li>
                        <li><strong>Apply Markov to the squared deviation:</strong> Since $(X - \mu)^2 \geq 0$:
                            $$P(|X - \mu|^2 \geq k^2\sigma^2) \leq \frac{E[|X - \mu|^2]}{k^2\sigma^2}$$
                        </li>
                        <li><strong>Recognize the variance:</strong>
                            $$E[|X - \mu|^2] = E[(X - \mu)^2] = \text{Var}(X) = \sigma^2$$
                        </li>
                        <li><strong>Substitute back:</strong>
                            $$P(|X - \mu| \geq k\sigma) \leq \frac{\sigma^2}{k^2\sigma^2} = \frac{1}{k^2}$$
                        </li>
                    </ol>
                </div>
            </div>

            <div class="svg-container">
                <h4>Chebyshev's Inequality Visualization</h4>
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Chebyshev Bound: Two-sided Concentration Around the Mean
                    </text>
                    
                    <!-- Axes -->
                    <line x1="60" y1="350" x2="740" y2="350" stroke="#333" stroke-width="2"/>
                    <line x1="60" y1="350" x2="60" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Normal distribution curve -->
                    <path d="M 100 340 Q 200 200, 300 120 Q 400 80, 500 120 Q 600 200, 700 340" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    
                    <!-- Mean line -->
                    <line x1="400" y1="50" x2="400" y2="350" stroke="#4caf50" stroke-width="2"/>
                    <text x="405" y="40" font-size="12" fill="#4caf50" font-weight="bold">μ</text>
                    
                    <!-- Standard deviation markers -->
                    <line x1="300" y1="50" x2="300" y2="350" stroke="#ff9800" stroke-width="2" stroke-dasharray="3,3"/>
                    <line x1="500" y1="50" x2="500" y2="350" stroke="#ff9800" stroke-width="2" stroke-dasharray="3,3"/>
                    <text x="305" y="40" font-size="12" fill="#ff9800">μ-kσ</text>
                    <text x="505" y="40" font-size="12" fill="#ff9800">μ+kσ</text>
                    
                    <!-- Tail areas -->
                    <path d="M 100 340 Q 200 200, 300 120 L 300 350 L 100 350 Z" 
                          fill="#f44336" opacity="0.3"/>
                    <path d="M 500 120 Q 600 200, 700 340 L 700 350 L 500 350 Z" 
                          fill="#f44336" opacity="0.3"/>
                    
                    <!-- Labels -->
                    <text x="200" y="250" font-size="12" fill="#f44336" text-anchor="middle">≤ 1/(2k²)</text>
                    <text x="600" y="250" font-size="12" fill="#f44336" text-anchor="middle">≤ 1/(2k²)</text>
                    <text x="400" y="300" font-size="14" fill="#4caf50" text-anchor="middle" font-weight="bold">
                        P(|X-μ| ≥ kσ) ≤ 1/k²
                    </text>
                    
                    <!-- Distance indicators -->
                    <path d="M 300 370 L 500 370" stroke="#ff9800" stroke-width="2"/>
                    <path d="M 300 370 L 305 365" stroke="#ff9800" stroke-width="2"/>
                    <path d="M 500 370 L 495 365" stroke="#ff9800" stroke-width="2"/>
                    <text x="400" y="385" text-anchor="middle" font-size="12" fill="#ff9800">2kσ</text>
                </svg>
            </div>

            <h3>Application to Sample Averages</h3>

            <div class="example-box">
                <h4>The Power of Chebyshev for Sample Means</h4>
                <p>Consider i.i.d. random variables $X_1, X_2, \ldots, X_n$ with mean $\mu$ and variance $\sigma^2$. The sample average is:</p>
                <div class="formula-highlight">
                    $$\bar{\mu}_n = \frac{1}{n}\sum_{i=1}^n X_i$$
                </div>
                <p>This average has mean $\mu$ and variance $\sigma^2/n$. Applying Chebyshev:</p>
                <div class="formula-highlight">
                    $$P\left(\left|\bar{\mu}_n - \mu\right| \geq \frac{k\sigma}{\sqrt{n}}\right) \leq \frac{1}{k^2}$$
                </div>
                
                <div class="insight-box">
                    <h5>The $1/\sqrt{n}$ Convergence Rate</h5>
                    <p>With probability at least $0.99$, the sample average is within $10\sigma/\sqrt{n}$ of the true mean. This gives us the famous $1/\sqrt{n}$ convergence rate!</p>
                </div>
            </div>

            <h3>Concrete Example and Comparison</h3>

            <div class="example-box">
                <h4>Example: Confidence Intervals</h4>
                <p>Suppose we want the sample average to be within $\epsilon$ of the true mean with probability at least $1-\delta$. How many samples do we need?</p>
                
                <div class="two-column">
                    <div>
                        <h5>Using Chebyshev</h5>
                        <p>We need: $P(|\bar{\mu}_n - \mu| \geq \epsilon) \leq \delta$</p>
                        <p>Setting $\frac{\sigma^2}{n\epsilon^2} = \delta$, we get:</p>
                        <div class="formula-highlight">
                            $$n \geq \frac{\sigma^2}{\delta \epsilon^2}$$
                        </div>
                    </div>
                    <div>
                        <h5>Numerical Example</h5>
                        <p>For $\delta = 0.01$, $\epsilon = 0.1$, $\sigma = 1$:</p>
                        <p>$n \geq \frac{1}{0.01 \times 0.01} = 10,000$ samples</p>
                        <p>This shows the polynomial dependence on the confidence level.</p>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <h4>Chebyshev vs Markov Comparison</h4>
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Bound Quality: Chebyshev vs Markov for |X - μ|
                    </text>
                    
                    <!-- Axes -->
                    <line x1="80" y1="300" x2="720" y2="300" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="300" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Axis labels -->
                    <text x="400" y="330" text-anchor="middle" font-size="12" fill="#333">Deviation t</text>
                    <text x="30" y="175" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90, 30, 175)">Upper Bound</text>
                    
                    <!-- Markov bound (linear decay) -->
                    <path d="M 120 80 L 200 120 L 300 180 L 500 240 L 700 290" 
                          stroke="#f44336" stroke-width="3" fill="none" stroke-dasharray="8,4"/>
                    
                    <!-- Chebyshev bound (quadratic decay) -->
                    <path d="M 120 60 Q 200 80, 300 120 Q 500 200, 700 280" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    
                    <!-- True probability (exponential decay) -->
                    <path d="M 120 70 Q 200 100, 300 160 Q 500 250, 700 295" 
                          stroke="#4caf50" stroke-width="3" fill="none" stroke-dasharray="2,2"/>
                    
                    <!-- Legend -->
                    <rect x="520" y="60" width="220" height="100" fill="white" stroke="#ccc" rx="5"/>
                    
                    <line x1="530" y1="80" x2="560" y2="80" stroke="#f44336" stroke-width="3" stroke-dasharray="8,4"/>
                    <text x="570" y="85" font-size="12" fill="#f44336">Markov: O(1/t)</text>
                    
                    <line x1="530" y1="100" x2="560" y2="100" stroke="#2196f3" stroke-width="3"/>
                    <text x="570" y="105" font-size="12" fill="#2196f3">Chebyshev: O(1/t²)</text>
                    
                    <line x1="530" y1="120" x2="560" y2="120" stroke="#4caf50" stroke-width="3" stroke-dasharray="2,2"/>
                    <text x="570" y="125" font-size="12" fill="#4caf50">True (e.g., Gaussian)</text>
                    
                    <text x="630" y="145" font-size="10" fill="#666" text-anchor="middle">Chebyshev is much</text>
                    <text x="630" y="157" font-size="10" fill="#666" text-anchor="middle">tighter than Markov!</text>
                    
                    <!-- Annotations -->
                    <text x="200" y="200" font-size="11" fill="#666" text-anchor="middle" transform="rotate(-20, 200, 200)">
                        Quadratic improvement
                    </text>
                    
                </svg>
            </div>

            <h3>Connection to the Weak Law of Large Numbers</h3>

            <div class="insight-box">
                <h4>Almost the Weak Law of Large Numbers</h4>
                <p>Chebyshev's inequality almost directly gives us the Weak Law of Large Numbers. For any $\epsilon > 0$:</p>
                <div class="formula-highlight">
                    $$P(|\bar{\mu}_n - \mu| \geq \epsilon) \leq \frac{\sigma^2}{n\epsilon^2} \to 0 \text{ as } n \to \infty$$
                </div>
                <p>This shows that the sample average converges in probability to the true mean!</p>
            </div>

            <div class="comparison-box">
                <h4>Chebyshev's Strengths and Limitations</h4>
                <div class="two-column">
                    <div>
                        <h5>✅ Improvements Over Markov</h5>
                        <ul>
                            <li>Two-sided bounds around the mean</li>
                            <li>Quadratic improvement: $O(1/t^2)$ vs $O(1/t)$</li>
                            <li>Uses variance information effectively</li>
                            <li>Establishes the $1/\sqrt{n}$ convergence rate</li>
                            <li>Leads to Weak Law of Large Numbers</li>
                        </ul>
                    </div>
                    <div>
                        <h5>❌ Still Has Limitations</h5>
                        <ul>
                            <li>Still polynomial decay, not exponential</li>
                            <li>Conservative constants (factor of 10 for 99% confidence)</li>
                            <li>Doesn't utilize full distributional information</li>
                            <li>Can be loose for specific distributions</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="step-list">
                <h4>What's Next?</h4>
                <p>While Chebyshev is a major improvement, we can do even better using moment generating functions. This leads us to the Chernoff method, which can provide exponential concentration bounds for many important classes of random variables.</p>
            </div>

        </section>

        <!-- Chernoff Method Section -->
        <section id="chernoff" class="section">
            <h2>3. The Chernoff Method: Using Moment Generating Functions</h2>
            
            <p>The Chernoff method represents a major leap forward in concentration inequalities. By utilizing moment generating functions (MGFs), we can often achieve exponential concentration bounds, a dramatic improvement over the polynomial bounds of Markov and Chebyshev.</p>

            <div class="insight-box">
                <h4>The Key Insight</h4>
                <p>Instead of applying Markov directly to the random variable, we apply it to an exponential transformation of the variable, then optimize over the exponential parameter.</p>
            </div>

            <h3>The General Chernoff Bound</h3>

            <div class="theorem-box">
                <h4>Chernoff's Method</h4>
                <p>For a random variable $X$ with mean $\mu$ and moment generating function $M_X(t) = E[e^{tX}]$ defined in a neighborhood of 0, we have:</p>
                <div class="formula-highlight">
                    $$P(X - \mu \geq u) \leq \inf_{t > 0} \frac{E[e^{t(X-\mu)}]}{e^{tu}} = \inf_{t > 0} e^{-tu} M_{X-\mu}(t)$$
                </div>
                <p>This is known as <strong>Chernoff's bound</strong>.</p>
            </div>

            <h3>Step-by-Step Derivation</h3>

            <div class="proof-box">
                <h4>Derivation of the Chernoff Bound</h4>
                <div class="step-list">
                    <ol>
                        <li><strong>Exponential transformation:</strong> For any $t > 0$:
                            $$P(X - \mu \geq u) = P(e^{t(X-\mu)} \geq e^{tu})$$
                        </li>
                        <li><strong>Apply Markov's inequality:</strong> Since $e^{t(X-\mu)} \geq 0$:
                            $$P(e^{t(X-\mu)} \geq e^{tu}) \leq \frac{E[e^{t(X-\mu)}]}{e^{tu}}$$
                        </li>
                        <li><strong>Optimize over $t$:</strong> Since this holds for any $t > 0$:
                            $$P(X - \mu \geq u) \leq \inf_{t > 0} \frac{E[e^{t(X-\mu)}]}{e^{tu}}$$
                        </li>
                        <li><strong>The magic:</strong> The infimum often yields exponential bounds!</li>
                    </ol>
                </div>
            </div>

            <div class="svg-container">
                <h4>Chernoff Method Visualization</h4>
                <svg width="800" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#fafafa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1e3c72">
                        Chernoff Method: Exponential Transformation and Optimization
                    </text>
                    
                    <!-- Original distribution -->
                    <text x="200" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#2196f3">
                        Original: X - μ
                    </text>
                    <path d="M 120 100 Q 180 80, 220 90 Q 260 120, 280 140" 
                          stroke="#2196f3" stroke-width="3" fill="none"/>
                    
                    <!-- Threshold -->
                    <line x1="240" y1="60" x2="240" y2="150" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    <text x="245" y="55" font-size="12" fill="#f44336">u</text>
                    
                    <!-- Exponential transformation -->
                    <text x="200" y="200" text-anchor="middle" font-size="14" font-weight="bold" fill="#9c27b0">
                        Transformed: e^(t(X-μ))
                    </text>
                    <path d="M 120 240 Q 160 220, 200 210 Q 240 200, 280 190" 
                          stroke="#9c27b0" stroke-width="3" fill="none"/>
                    
                    <!-- Exponential threshold -->
                    <line x1="240" y1="200" x2="240" y2="250" stroke="#f44336" stroke-width="2" stroke-dasharray="3,3"/>
                    <text x="245" y="195" font-size="12" fill="#f44336">e^(tu)</text>
                    
                    <!-- Arrow showing transformation -->
                    <path d="M 200 160 Q 220 170, 200 185" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>
                    <text x="210" y="175" font-size="12" fill="#666">e^(t·)</text>
                    
                    <!-- Optimization process -->
                    <text x="600" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#4caf50">
                        Optimization Over t
                    </text>
                    
                    <!-- Multiple curves for different t values -->
                    <path d="M 520 100 Q 580 90, 620 95 Q 660 110, 680 130" 
                          stroke="#ff9800" stroke-width="2" fill="none" opacity="0.6"/>
                    <text x="680" y="125" font-size="10" fill="#ff9800">t₁</text>
                    
                    <path d="M 520 120 Q 580 110, 620 115 Q 660 130, 680 150" 
                          stroke="#ff5722" stroke-width="2" fill="none" opacity="0.6"/>
                    <text x="680" y="145" font-size="10" fill="#ff5722">t₂</text>
                    
                    <path d="M 520 110 Q 580 100, 620 105 Q 660 120, 680 140" 
                          stroke="#4caf50" stroke-width="3" fill="none"/>
                    <text x="680" y="135" font-size="10" fill="#4caf50">t*</text>
                    
                    <!-- Result -->
                    <rect x="450" y="280" width="300" height="80" fill="#e8f5e8" stroke="#4caf50" rx="10"/>
                    <text x="600" y="305" text-anchor="middle" font-size="14" font-weight="bold" fill="#2e7d32">
                        Result: Exponential Bound
                    </text>
                    <text x="600" y="325" text-anchor="middle" font-size="12" fill="#2e7d32">
                        P(X - μ ≥ u) ≤ e^(-f(u))
                    </text>
                    <text x="600" y="345" text-anchor="middle" font-size="12" fill="#2e7d32">
                        where f(u) grows like u²
                    </text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Application to Gaussian Random Variables</h3>

            <div class="example-box">
                <h4>Gaussian Tail Bounds via Chernoff</h4>
                <p>Let $X \sim N(\mu, \sigma^2)$. The moment generating function is:</p>
                <div class="formula-highlight">
                    $$M_X(t) = E[e^{tX}] = e^{t\mu + \frac{t^2\sigma^2}{2}}$$
                </div>
                
                <div class="step-list">
                    <h5>Applying Chernoff Method:</h5>
                    <ol>
                        <li><strong>Set up the bound:</strong>
                            $$P(X - \mu \geq u) \leq \inf_{t \geq 0} e^{-tu} \cdot e^{\frac{t^2\sigma^2}{2}} = \inf_{t \geq 0} e^{-tu + \frac{t^2\sigma^2}{2}}$$
                        </li>
                        <li><strong>Optimize:</strong> Taking the derivative and setting to zero:
                            $$\frac{d}{dt}(-tu + \frac{t^2\sigma^2}{2}) = -u + t\sigma^2 = 0$$
                        </li>
                        <li><strong>Optimal choice:</strong> $t^* = \frac{u}{\sigma^2}$</li>
                        <li><strong>Final bound:</strong>
                            $$P(X - \mu \geq u) \leq e^{-\frac{u^2}{2\sigma^2}}$$
                        </li>
                    </ol>
                </div>
            </div>

            <div class="theorem-box">
                <h4>Gaussian Tail Bounds</h4>
                <p>For $X \sim N(\mu, \sigma^2)$:</p>
                <div class="formula-highlight">
                    $$P(X - \mu \geq u) \leq e^{-\frac{u^2}{2\sigma^2}} \quad \text{(upper tail)}$$
                </div>
                <div class="formula-highlight">
                    $$P(X - \mu \leq -u) \leq e^{-\frac{u^2}{2\sigma^2}} \quad \text{(lower tail)}$$
                </div>
                <div class="formula-highlight">
                    $$P(|X - \mu| \geq u) \leq 2e^{-\frac{u^2}{2\sigma^2}} \quad \text{(two-sided)}$$
                </div>
            </div>

            <h3>Dramatic Improvement Over Chebyshev</h3>

            <div class="comparison-box">
                <h4>Exponential vs Polynomial: A Concrete Comparison</h4>
                <p>For a Gaussian random variable, let's compare confidence intervals:</p>
                
                <div class="two-column">
                    <div>
                        <h5>Chebyshev (Polynomial)</h5>
                        <p>For 99% confidence ($\delta = 0.01$):</p>
                        <div class="formula-highlight">
                            $$|X - \mu| \leq \frac{\sigma}{\sqrt{\delta}} = 10\sigma$$
                        </div>
                        <p>Needs to be within <strong>10 standard deviations</strong>!</p>
                    </div>
                    <div>
                        <h5>Gaussian Tail Bound (Exponential)</h5>
                        <p>For 99% confidence ($\delta = 0.01$):</p>
                        <div class="formula-highlight">
                            $$|X - \mu| \leq \sigma\sqrt{2\ln(2/\delta)} \approx 3.25\sigma$$
                        </div>
                        <p>Only needs <strong>3.25 standard deviations</strong>!</p>
                    </div>
                </div>
            </div>

        </section> 