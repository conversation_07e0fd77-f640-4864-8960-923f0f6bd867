9.4 Convolution and Pooling as an Infinitely Strong
 Prior
 Recall the concept of a prior probability distribution from section . This is
 5.2
 a probability distribution over the parameters of a model that encodes our beliefs
 about what models are reasonable, before we have seen any data.
 Priors can be considered weak or strong depending on how concentrated the
 probability density in the prior is. A weak prior is a prior distribution with high
 entropy, such as a Gaussian distribution with high variance. Such a prior allows
 the data to move the parameters more or less freely. A strong prior has very low
 entropy, such as a Gaussian distribution with low variance. Such a prior plays a
 more active role in determining where the parameters end up.
 An infinitely strong prior places zero probability on some parameters and says
 that these parameter values are completely forbidden, regardless of how much
 support the data gives to those values.
 We can imagine a convolutional net as being similar to a fully connected net,
 but with an infinitely strong prior over its weights. This infinitely strong prior
 says that the weights for one hidden unit must be identical to the weights of its
 neighbor, but shifted in space. The prior also says that the weights must be zero,
 except for in the small, spatially contiguous receptive field assigned to that hidden
 unit. Overall, we can think of the use of convolution as introducing an infinitely
 strong prior probability distribution over the parameters of a layer. This prior
 345
CHAPTER 9. CONVOLUTIONALNETWORKS
 Output of softmax: 
1,000 class 
probabilities
 Output of matrix 
multiply: 1,000 units
 Output of reshape to 
vector:
 16,384 units
 Output of pooling 
with stride 4: 
16x16x64
 Output of 
convolution + 
ReLU: 64x64x64
 Output of pooling 
with stride 4: 
64x64x64
 Output of 
convolution + 
ReLU: 256x256x64
 Input image: 
256x256x3
 Output of softmax: 
1,000 class 
probabilities
 Output of matrix 
multiply: 1,000 units
 Output of reshape to 
vector:
 576 units
 Output of pooling to 
3x3 grid: 3x3x64
 Output of 
convolution + 
ReLU: 64x64x64
 Output of pooling 
with stride 4: 
64x64x64
 Output of 
convolution + 
ReLU: 256x256x64
 Input image: 
256x256x3
 Output of softmax: 
1,000 class 
probabilities
 Output of average 
pooling: 1x1x1,000
 Output of 
convolution:
 16x16x1,000
 Output of pooling 
with stride 4: 
16x16x64
 Output of 
convolution + 
ReLU: 64x64x64
 Output of pooling 
with stride 4: 
64x64x64
 Output of 
convolution + 
ReLU: 256x256x64
 Input image: 
256x256x3
 Figure 9.11: Examples of architectures for classification with convolutional networks. The
 specific strides and depths used in this figure are not advisable for real use; they are
 designed to be very shallow in order to fit onto the page. Real convolutional networks
 also often involve significant amounts of branching, unlike the chain structures used
 here for simplicity. (Left)A convolutional network that processes a fixed image size.
 After alternating between convolution and pooling for a few layers, the tensor for the
 convolutional feature map is reshaped to flatten out the spatial dimensions. The rest
 of the network is an ordinary feedforward network classifier, as described in chapter .
 6
 (Center)A convolutional network that processes a variable-sized image, but still maintains
 a fully connected section. This network uses a pooling operation with variably-sized pools
 but a fixed number of pools, in order to provide a fixed-size vector of 576 units to the
 fully connected portion of the network. 
(Right)
 A convolutional network that does not
 have any fully connected weight layer. Instead, the last convolutional layer outputs one
 feature map per class. The model presumably learns a map of how likely each class is to
 occur at each spatial location. Averaging a feature map down to a single value provides
 the argument to the softmax classifier at the top.
 346
CHAPTER 9. CONVOLUTIONALNETWORKS
 says that the function the layer should learn contains only local interactions and is
 equivariant to translation. Likewise, the use of pooling is an infinitely strong prior
 that each unit should be invariant to small translations.
 Of course, implementing a convolutional net as a fully connected net with an
 infinitely strong prior would be extremely computationally wasteful. But thinking
 of a convolutional net as a fully connected net with an infinitely strong prior can
 give us some insights into how convolutional nets work.
 One key insight is that convolution and pooling can cause underfitting. Like
 any prior, convolution and pooling are only useful when the assumptions made
 by the prior are reasonably accurate. If a task relies on preserving precise spatial
 information, then using pooling on all features can increase the training error.
 Some convolutional network architectures (
 Szegedy et al. 2014a
 ,
 ) are designed to
 use pooling on some channels but not on other channels, in order to get both
 highly invariant features and features that will not underfit when the translation
 invariance prior is incorrect. When a task involves incorporating information from
 very distant locations in the input, then the prior imposed by convolution may be
 inappropriate.
 Another key insight from this view is that we should only compare convolu
tional models to other convolutional models in benchmarks of statistical learning
 performance. Models that do not use convolution would be able to learn even
 if we permuted all of the pixels in the image. For many image datasets, there
 are separate benchmarks for models that are permutation invariant and must
 discover the concept of topology via learning, and models that have the knowledge
 of spatial relationships hard-coded into them by their designer.
 9.5 Variants of the Basic Convolution Function
 When discussing convolution in the context of neural networks, we usually do
 not refer exactly to the standard discrete convolution operation as it is usually
 understood in the mathematical literature. The functions used in practice differ
 slightly. Here we describe these differences in detail, and highlight some useful
 properties of the functions used in neural networks.
 First, when we refer to convolution in the context of neural networks, we usually
 actually mean an operation that consists of many applications of convolution in
 parallel. This is because convolution with a single kernel can only extract one kind
 of feature, albeit at many spatial locations. Usually we want each layer of our
 network to extract many kinds of features, at many locations.
 347
CHAPTER 9. CONVOLUTIONALNETWORKS
 Additionally, the input is usually not just a grid of real values. Rather, it is a
 grid of vector-valued observations. For example, a color image has a red, green
 and blue intensity at each pixel. In a multilayer convolutional network, the input
 to the second layer is the output of the first layer, which usually has the output
 of many different convolutions at each position. When working with images, we
 usually think of the input and output of the convolution as being 3-D tensors, with
 one index into the different channels and two indices into the spatial coordinates
 of each channel. Software implementations usually work in batch mode, so they
 will actually use 4-D tensors, with the fourth axis indexing different examples in
 the batch, but we will omit the batch axis in our description here for simplicity.
 Because convolutional networks usually use multi-channel convolution, the
 linear operations they are based on are not guaranteed to be commutative, even if
 kernel-flipping is used. These multi-channel operations are only commutative if
 each operation has the same number of output channels as input channels.
 Assume we have a 4-D kernel tensorK with element Ki,j,k,l giving the connection
 strength between a unit in channel i of the output and a unit in channel j of the
 input, with an offset of k rows and l columns between the output unit and the
 input unit. Assume our input consists of observed data V with element Vi,j,k giving
 the value of the input unit within channel i at row j and column k. Assume our
 output consists of Z with the same format as V. If Z is produced by convolving K
 across
 V
 without flipping , then
 K
 Zi,j,k = 

 l,m,n
 Vl,j m ,k n
 + −1 + −1Ki,l,m,n
 (9.7)
 where the summation over l, m and nis over all values for which the tensor indexing
 operations inside the summation is valid. In linear algebra notation, we index into
 1
 arrays using a for the first entry. This necessitates the
 −1 in the above formula.
 Programming languages such as C and Python index starting from , rendering
 0
 the above expression even simpler.
 We may want to skip over some positions of the kernel in order to reduce the
 computational cost (at the expense of not extracting our features as finely). We
 can think of this as downsampling the output of the full convolution function. If
 we want to sample only every s pixels in each direction in the output, then we can
 define a downsampled convolution function such that
 c
 
 Zi,j,k = (
 c K V , ,s i,j,k =
 )
 l,m,n
 
 Vl, j
 ( − ×
 1) + ( − × 1) + Ki,l,m,n
  .
 s m, k
 s n
 (9.8)
 We refer to s as the stride of this downsampled convolution. It is also possible
 348
CHAPTER 9. CONVOLUTIONALNETWORKS
 9.12
 for an
 to define a separate stride for each direction of motion. See figure
 illustration.
 One essential feature of any convolutional network implementation is the ability
 to implicitly zero-pad the input V in order to make it wider. Without this feature,
 the width of the representation shrinks by one pixel less than the kernel width
 at each layer. Zero padding the input allows us to control the kernel width and
 the size of the output independently. Without zero padding, we are forced to
 9.13
 choose between shrinking the spatial extent of the network rapidly and using small
 kernels—both scenarios that significantly limit the expressive power of the network.
 See figure
 for an example.
 Three special cases of the zero-padding setting are worth mentioning. One is
 the extreme case in which no zero-padding is used whatsoever, and the convolution
 kernel is only allowed to visit positions where the entire kernel is contained entirely
 within the image. In MATLAB terminology, this is called valid convolution. In
 this case, all pixels in the output are a function of the same number of pixels in
 the input, so the behavior of an output pixel is somewhat more regular. However,
 the size of the output shrinks at each layer. If the input image has width m and
 the kernel has width k, the output will be of width m k
 − +1. Therate of this
 shrinkage can be dramatic if the kernels used are large. Since the shrinkage is
 greater than 0, it limits the number of convolutional layers that can be included
 in the network. As layers are added, the spatial dimension of the network will
 eventually drop to 1 × 1, at which point additional layers cannot meaningfully
 be considered convolutional. Another special case of the zero-padding setting is
 when just enough zero-padding is added to keep the size of the output equal to
 the size of the input. MATLAB calls this same convolution. In this case, the
 network can contain as many convolutional layers as the available hardware can
 support, since the operation of convolution does not modify the architectural
 possibilities available to the next layer. However, the input pixels near the border
 influence fewer output pixels than the input pixels near the center. This can make
 the border pixels somewhat underrepresented in the model. This motivates the
 other extreme case, which MATLAB refers to as full convolution, in which enough
 zeroes are added for every pixel to be visited k times in each direction, resulting
 in an output image of width m+ k−1. In this case, the output pixels near the
 border are a function of fewer pixels than the output pixels near the center. This
 can make it difficult to learn a single kernel that performs well at all positions in
 the convolutional feature map. Usually the optimal amount of zero padding (in
 terms of test set classification accuracy) lies somewhere between “valid” and “same”
 convolution.
 349
CHAPTER 9. CONVOLUTIONALNETWORKS
 s1
 s1
 Strided
 convolution
 x1
 x1
 s1
 s1
 Downsampling
 Convolution
 z1
 z1
 x1
 x1
 s2
 s2
 x2
 x2
 z2
 z2
 x2
 x2
 x3
 x3
 s2
 s2
 z3
 z3
 x3
 x3
 s3
 s3
 x4
 x4
 z4
 z4
 x4
 x4
 x5
 x5
 s3
 s3
 z5
 z5
 x5
 x5
 Figure 9.12: Convolution with a stride. In this example, we use a stride of two.
 (Top)Convolution with a stride length of two implemented in a single operation. (Bot
tom)Convolution with a stride greater than one pixel is mathematically equivalent to
 convolution with unit stride followed by downsampling. Obviously, the two-step approach
 involving downsampling is computationally wasteful, because it computes many values
 that are then discarded.
 350
CHAPTER 9. CONVOLUTIONALNETWORKS
 ...
 ...
 ...
 ...
 ...
 ...
 ...
 ...
 ...
 Figure 9.13: The effect of zero padding on network size: Consider a convolutional network
 with a kernel of width six at every layer. In this example, we do not use any pooling, so
 only the convolution operation itself shrinks the network size. (Top)In this convolutional
 network, we do not use any implicit zero padding. This causes the representation to
 shrink by five pixels at each layer. Starting from an input of sixteen pixels, we are only
 able to have three convolutional layers, and the last layer does not ever move the kernel,
 so arguably only two of the layers are truly convolutional. The rate of shrinking can
 be mitigated by using smaller kernels, but smaller kernels are less expressive and some
 shrinking is inevitable in this kind of architecture.
 (Bottom)
 By adding five implicit zeroes
 to each layer, we prevent the representation from shrinking with depth. This allows us to
 make an arbitrarily deep convolutional network.
 351
CHAPTER 9. CONVOLUTIONALNETWORKS
 In some cases, we do not actually want to use convolution, but rather locally
 connected layers (
 ,
 LeCun 1986 1989
 ,
 ). In this case, the adjacency matrix in the
 graph of our MLP is the same, but every connection has its own weight, specified
 by a 6-D tensor W. The indices into W are respectively: i, the output channel,
 j, the output row, k, the output column, l, the input channel, m, the row offset
 within the input, and n, the column offset within the input. The linear part of a
 locally connected layer is then given by
 
 Zi,j,k =
 l,m,n
 [Vl,j m ,k n
 + −1 + −1wi,j,k,l,m,n].
 (9.9)
 This is sometimes also called unshared convolution, because it is a similar oper
ation to discrete convolution with a small kernel, but without sharing parameters
 across locations. Figure
 connections.
 9.14
 compares local connections, convolution, and full
 Locally connected layers are useful when we know that each feature should be
 a function of a small part of space, but there is no reason to think that the same
 feature should occur across all of space. For example, if we want to tell if an image
 is a picture of a face, we only need to look for the mouth in the bottom half of the
 image.
 It can also be useful to make versions of convolution or locally connected layers
 in which the connectivity is further restricted, for example to constrain each output
 channel i to be a function of only a subset of the input channels l. A common
 way to do this is to make the first m output channels connect to only the first
 n input channels, the second m output channels connect to only the second n
 input channels, and so on. See figure
 9.15
 for an example. Modeling interactions
 between few channels allows the network to have fewer parameters in order to
 reduce memory consumption and increase statistical efficiency, and also reduces
 the amount of computation needed to perform forward and back-propagation. It
 accomplishes these goals without reducing the number of hidden units.
 Tiled convolution (
 ,
 Gregor and LeCun 2010a Le et al. 2010
 ;
 ,
 ) offers a com
promise between a convolutional layer and a locally connected layer. Rather than
 learning a separate set of weights at
 every
 spatial location, we learn a set of kernels
 that we rotate through as we move through space. This means that immediately
 neighboring locations will have different filters, like in a locally connected layer,
 but the memory requirements for storing the parameters will increase only by a
 factor of the size of this set of kernels, rather than the size of the entire output
 feature map. See figure
 9.16
 for a comparison of locally connected layers, tiled
 convolution, and standard convolution.
 352
CHAPTER9. CONVOLUTIONALNETWORKS
 x1 x1 x2 x2 x3 x3
 s2 s2 s1 s1 s3 s3
 x4 x4
 s4 s4
 x5 x5
 s5 s5
 x1 x1 x2 x2
 s1 s1 s3 s3
 x5 x5
 s5 s5
 x1 x1 x2 x2 x3 x3
 s2 s2 s1 s1 s3 s3
 x4 x4
 s4 s4
 x5 x5
 s5 s5
 a        b a        b a        b a        b a     
a        b c      d e     f g      h i  
x4 x4 x3 x3
 s4 s4 s2 s2
 Figure9.14:Comparisonof localconnections,convolution,andfullconnections.
 (Top)Alocallyconnectedlayerwithapatchsizeoftwopixels.Eachedgeislabeledwith
 auniquelettertoshowthateachedgeisassociatedwithitsownweightparameter.
 (Center)Aconvolutional layerwithakernelwidthoftwopixels.Thismodelhasexactly
 thesameconnectivityasthelocallyconnectedlayer.Thedifferenceliesnot inwhichunits
 interactwitheachother,butinhowtheparametersareshared.Thelocallyconnectedlayer
 hasnoparametersharing.Theconvolutional layerusesthesametwoweightsrepeatedly
 acrosstheentireinput,as indicatedbytherepetitionoftheletters labelingeachedge.
 (Bottom)Afullyconnectedlayerresemblesalocallyconnectedlayer inthesensethateach
 edgehas itsownparameter(therearetoomanytolabelexplicitlywithletters inthis
 diagram).However, itdoesnothavetherestrictedconnectivityofthelocallyconnected
 layer.
 353
CHAPTER 9. CONVOLUTIONALNETWORKS
 Output Tensor
 Input Tensor
 Channel coordinates
 Spatial coordinates
 Figure 9.15: A convolutional network with the first two output channels connected to
 only the first two input channels, and the second two output channels connected to only
 the second two input channels.
 354
CHAPTER9. CONVOLUTIONALNETWORKS
 x1 x1 x2 x2 x3 x3
 s2 s2 s1 s1 s3 s3
 x4 x4
 s4 s4
 x5 x5
 s5 s5
 x1 x1 x2 x2 x3 x3
 s2 s2 s1 s1 s3 s3
 x4 x4
 s4 s4
 x5 x5
 s5 s5
 a        b a        b a        b a        b a    
a        b c      d e     f g      h i  
x1 x1 x2 x2 x3 x3
 s2 s2 s1 s1 s3 s3
 x4 x4
 s4 s4
 x5 x5
 s5 s5
 a        b c        d a        b c        d a     
Figure9.16:Acomparisonof locallyconnectedlayers,tiledconvolution,andstandard
 convolution.All threehavethesamesetsofconnectionsbetweenunits,whenthesame
 sizeofkernel isused.Thisdiagramillustratestheuseofakernel that istwopixelswide.
 Thedifferencesbetweenthemethodslies inhowtheyshareparameters. (Top)Alocally
 connectedlayerhasnosharingatall.Weindicatethateachconnectionhasitsownweight
 bylabelingeachconnectionwithauniqueletter. Tiledconvolutionhasasetof (Center)
 tdifferentkernels.Hereweillustratethecaseoft= 2. Oneofthesekernelshasedges
 labeled“a” and“b,”whiletheotherhasedges labeled“c” and“d.” Eachtimewemoveone
 pixel totheright intheoutput,wemoveontousingadifferentkernel.Thismeansthat,
 likethelocallyconnectedlayer,neighboringunits intheoutputhavedifferentparameters.
 Unlike the locallyconnected layer,afterwehavegone throughallt availablekernels,
 wecyclebacktothefirstkernel. If twooutputunitsareseparatedbyamultipleoft
 steps, thentheyshareparameters. Traditionalconvolutionisequivalenttotiled (Bottom)
 convolutionwitht= 1.Thereisonlyonekernelanditisappliedeverywhere,as indicated
 inthediagrambyusingthekernelwithweights labeled“a” and“b” everywhere.
 355
CHAPTER 9. CONVOLUTIONALNETWORKS
 To define tiled convolution algebraically, let k be a 6-D tensor, where two of
 the dimensions correspond to different locations in the output map. Rather than
 having a separate index for each location in the output map, output locations cycle
 through a set of t different choices of kernel stack in each direction. If t is equal to
 the output width, this is the same as a locally connected layer.
 
 Zi,j,k =
 where
 %
 l,m,n
 Vl,j m ,k n
 + −1 + −1Ki,l,m,n,j t ,k t
 % +1 %+1,
 is the modulo operation, with
 t%t = 0 (
 ,
 (9.10)
 t + 1)%t = 1, etc. It is
 straightforward to generalize this equation to use a different tiling range for each
 dimension.
 Both locally connected layers and tiled convolutional layers have an interesting
 interaction with max-pooling: the detector units of these layers are driven by
 different filters. If these filters learn to detect different transformed versions of
 the same underlying features, then the max-pooled units become invariant to the
 learned transformation (see figure
 9.9
 invariant specifically to translation.
 ). Convolutional layers are hard-coded to be
 Other operations besides convolution are usually necessary to implement a
 convolutional network. To perform learning, one must be able to compute the
 gradient with respect to the kernel, given the gradient with respect to the outputs.
 In some simple cases, this operation can be performed using the convolution
 operation, but many cases of interest, including the case of stride greater than 1,
 do not have this property.
 Recall that convolution is a linear operation and can thus be described as a
 matrix multiplication (if we first reshape the input tensor into a flat vector). The
 matrix involved is a function of the convolution kernel. The matrix is sparse and
 each element of the kernel is copied to several elements of the matrix. This view
 helps us to derive some of the other operations needed to implement a convolutional
 network.
 Multiplication by the transpose of the matrix defined by convolution is one
 such operation. This is the operation needed to back-propagate error derivatives
 through a convolutional layer, so it is needed to train convolutional networks
 that have more than one hidden layer. This same operation is also needed if we
 wish to reconstruct the visible units from the hidden units (
 Simard et al. 1992
 ,
 ).
 Reconstructing the visible units is an operation commonly used in the models
 described in part
 III
 of this book, such as autoencoders, RBMs, and sparse coding.
 Transpose convolution is necessary to construct convolutional versions of those
 models. Like the kernel gradient operation, this input gradient operation can be
 356
CHAPTER 9. CONVOLUTIONALNETWORKS
 implemented using a convolution in some cases, but in the general case requires
 a third operation to be implemented. Care must be taken to coordinate this
 transpose operation with the forward propagation. The size of the output that the
 transpose operation should return depends on the zero padding policy and stride of
 the forward propagation operation, as well as the size of the forward propagation’s
 output map. In some cases, multiple sizes of input to forward propagation can
 result in the same size of output map, so the transpose operation must be explicitly
 told what the size of the original input was.
 These three operations—convolution, backprop from output to weights, and
 backprop from output to inputs—are sufficient to compute all of the gradients
 needed to train any depth of feedforward convolutional network, as well as to train
 convolutional networks with reconstruction functions based on the transpose of
 convolution. See
 Goodfellow 2010
 (
 ) for a full derivation of the equations in the
 fully general multi-dimensional, multi-example case. To give a sense of how these
 equations work, we present the two dimensional, single example version here.
 Suppose we want to train a convolutional network that incorporates strided
 convolution of kernel stack K applied to multi-channel image V with stride s as
 defined byc(K V
 ,
 function J(V K
 ,
 , s) as in equation
 9.8
 . Suppose we want to minimize some loss
 ). During forward propagation, we will need to use c itself to
 output Z, which is then propagated through the rest of the network and used to
 compute the cost function J. During back-propagation, we will receive a tensor G
 such that Gi,j,k = ∂
 ∂Zi,j,k 
J
 ,
 (V K)
 .
 To train the network, we need to compute the derivatives with respect to the
 weights in the kernel. To do so, we can use a function
 
 g , ,s
 (G V )i,j,k,l =
 ∂
 ∂Ki,j,k,l 
J
 ,
 (V K) =
 m,n
 Gi,m,nVj, m
 ( − × 1) + ( − ×
 s k, n
 s l
 1) +. (9.11)
 If this layer is not the bottom layer of the network, we will need to compute
 the gradient with respect to V in order to back-propagate the error farther down.
 To do so, we can use a function
 h , ,s
 (K G )i,j,k = ∂
 ∂Vi,j,k
 J
 =
 ,
 (V K)
 
 l,m
 s.t.
 ( 1) + =
 l− ×s m j
 (
 n− ×s p k
 (9.12)
 
 n,p
 s.t.
 1) + =
 
 q
 Kq,i,m,pGq,l,n.
 (9.13)
 Autoencoder networks, described in chapter , are feedforward networks
 14
 trained to copy their input to their output. A simple example is the PCA algorithm,
 357
CHAPTER 9. CONVOLUTIONALNETWORKS
 that copies its input x to an approximate reconstruction r using the function
 WWx. It is common for more general autoencoders to use multiplication
 by the transpose of the weight matrix just as PCA does. To make such models
 convolutional, we can use the function h to perform the transpose of the convolution
 operation. Suppose we have hidden units H in the same format as Z and we define
 a reconstruction
 R
 = ( h , ,s . )
 K H
 (9.14)
 In order to train the autoencoder, we will receive the gradient with respect
 to R as a tensor E. To train the decoder, we need to obtain the gradient with
 respect to K. This is given by g(H E
 , ,s). To train the encoder, we need to obtain
 the gradient with respect to H. This is given by c(K E
 , ,s). It is also possible to
 differentiate through g using c and h, but these operations are not needed for the
 back-propagation algorithm on any standard network architectures.
 Generally, we do not use only a linear operation in order to transform from
 the inputs to the outputs in a convolutional layer. We generally also add some
 bias term to each output before applying the nonlinearity. This raises the question
 of how to share parameters among the biases. For locally connected layers it is
 natural to give each unit its own bias, and for tiled convolution, it is natural to
 share the biases with the same tiling pattern as the kernels. For convolutional
 layers, it is typical to have one bias per channel of the output and share it across
 all locations within each convolution map. However, if the input is of known, fixed
 size, it is also possible to learn a separate bias at each location of the output map.
 Separating the biases may slightly reduce the statistical efficiency of the model, but
 also allows the model to correct for differences in the image statistics at different
 locations. For example, when using implicit zero padding, detector units at the
 edge of the image receive less total input and may need larger biases.