Given a vector v
 and some other vector u
 not in span{v}
, we can construct a new vector: v⊥:=v−u⋅vu⋅uu.

151166977515379.jpg

This new vector v⊥
 is orthogonal to u
 because

﻿
u⋅v⊥=u⋅v−u⋅vu⋅uu⋅u=0.(14.4.1)

Hence, {u,v⊥}
 is an orthogonal basis for span{u,v}
. When v
 is not parallel to u
, v⊥≠0
, and normalizing these vectors we obtain {u|u|,v⊥|v⊥|}
, an orthonormal basis for the vector space span{u,v}
.

Sometimes we write v=v⊥+v∥
 where:

v⊥v∥==v−u⋅vu⋅uuu⋅vu⋅uu.

This is called an orthogonal decomposition
 because we have decomposed v
 into a sum of orthogonal vectors. This decomposition depends on u
; if we change the direction of u
 we change v⊥
 and v∥
.

If u
, v
 are linearly independent vectors in R3
, then the set {u,v⊥,u×v⊥}
 would be an orthogonal basis for R3
. This set could then be normalized by dividing each vector by its length to obtain an orthonormal basis.

However, it often occurs that we are interested in vector spaces with dimension greater than 3
, and must resort to craftier means than cross products to obtain an orthogonal basis.﻿

Given a third vector w
, we should first check that w
 does not lie in the span of u
 and v
, i.e.
, check that u,v
 and w
 are linearly independent. If it does not, we then can define:

w⊥=w−u⋅wu⋅uu−v⊥⋅wv⊥⋅v⊥v⊥.(14.4.2)

We can check that u⋅w⊥
 and v⊥⋅w⊥
 are both zero:

u⋅w⊥=u⋅(w−u⋅wu⋅uu−v⊥⋅wv⊥⋅v⊥v⊥)=u⋅w−u⋅wu⋅uu⋅u−v⊥⋅wv⊥⋅v⊥u⋅v⊥=u⋅w−u⋅w−v⊥⋅wv⊥⋅v⊥u⋅v⊥ = 0

since u
 is orthogonal to v⊥
, and

v⊥⋅w⊥=v⊥⋅(w−u⋅wu⋅uu−v⊥⋅wv⊥⋅v⊥v⊥)=v⊥⋅w−u⋅wu⋅uv⊥⋅u−v⊥⋅wv⊥⋅v⊥v⊥⋅v⊥=v⊥⋅w−u⋅wu⋅uv⊥⋅u−v⊥⋅w = 0

because u
 is orthogonal to v⊥
. Since w⊥
 is orthogonal to both u
 and v⊥
, we have that {u,v⊥,w⊥}
 is an orthogonal basis for span{u,v,w}
.

The Gram-Schmidt Procedure
In fact, given a set {v1,v2,…}
 of linearly independent vectors, we can define an orthogonal basis for span{v1,v2,…}
 consisting of the following vectors:

v⊥1v⊥2v⊥3v⊥i:=:=:=⋮=:=⋮v1v2−v⊥1⋅v2v⊥1⋅v⊥1v⊥1v3−v⊥1⋅v3v⊥1⋅v⊥1v⊥1−v⊥2⋅v3v⊥2⋅v⊥2v⊥2vi−∑j<iv⊥j⋅viv⊥j⋅v⊥jv⊥jvi−v⊥1⋅viv⊥1⋅v⊥1v⊥1−v⊥2⋅viv⊥2⋅v⊥2v⊥2−⋯−v⊥i−1⋅viv⊥i−1⋅v⊥i−1v⊥i−1

Notice that each v⊥i
 here depends on v⊥j
 for every j<i
. This allows us to inductively/algorithmically build up a linearly independent, orthogonal set of vectors {v⊥1,v⊥2,…}
 such that span{v⊥1,v⊥2,…}=span{v1,v2,…}
. That is, an orthogonal basis for the latter vector space. This algorithm is called the Gram--Schmidt orthogonalization procedure
--Gram worked at a Danish insurance company over one hundred years ago, Schmidt was a student of Hilbert (the famous German mathmatician).

Example14.4.1
:

We'll obtain an orthogonal basis for R3
 by appling Gram-Schmidt to the linearly independent set {v1=⎛⎝⎜110⎞⎠⎟,v2=⎛⎝⎜111⎞⎠⎟,v3=⎛⎝⎜311⎞⎠⎟}
. First, we set v⊥1:=v1
. Then:

v⊥2v⊥3==⎛⎝⎜111⎞⎠⎟−22⎛⎝⎜110⎞⎠⎟=⎛⎝⎜001⎞⎠⎟⎛⎝⎜311⎞⎠⎟−42⎛⎝⎜110⎞⎠⎟−11⎛⎝⎜001⎞⎠⎟=⎛⎝⎜1−10⎞⎠⎟.

Then the set

⎧⎩⎨⎪⎪⎛⎝⎜110⎞⎠⎟,⎛⎝⎜001⎞⎠⎟,⎛⎝⎜1−10⎞⎠⎟⎫⎭⎬⎪⎪(14.4.3)

is an orthogonal basis for R3
. To obtain an orthonormal basis, as always we simply divide each of these vectors by its length, yielding:

⎧⎩⎨⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎛⎝⎜⎜⎜⎜⎜12–√12–√0⎞⎠⎟⎟⎟⎟⎟,⎛⎝⎜001⎞⎠⎟,⎛⎝⎜⎜⎜⎜⎜12–√−12–√0⎞⎠⎟⎟⎟⎟⎟⎫⎭⎬⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪⎪.(14.4.4)

Contributor
David Cherney, Tom Denton, and Andrew Waldron (UC Davis)

In chapter 7, section 7.7 we learned how to solve linear systems by decomposing a matrix M
 into a product of lower and upper triangular matrices

M=LU.(14.5.1)

The Gram-Schmidt procedure suggests another matrix decomposition,

M=QR,(14.5.2)

where Q
 is an orthogonal matrix and R
 is an upper triangular matrix. So-called QR-decompositions are useful for solving linear systems, eigenvalue problems and least squares approximations. You can easily get the idea behind the QR
 decomposition by working through a simple example.

Example14.5.1
:

Find the QR
 decomposition of

M=⎛⎝⎜210−1311−2−2⎞⎠⎟.(14.5.3)

What we will do is to think of the columns of M
 as three 3-vectors and use Gram-Schmidt to build an orthonormal basis from these that will become the columns of the orthogonal matrix Q
. We will use the matrix R
 to record the steps of the Gram-Schmidt procedure in such a way that the product QR
 equals M
.

To begin with we write

M=⎛⎝⎜⎜210−7514511−2−2⎞⎠⎟⎟⎛⎝⎜1001510001⎞⎠⎟.(14.5.4)

In the first matrix the first two columns are orthogonal because we simply replaced the second column of M
 by the vector that the Gram-Schmidt procedure produces from the first two columns of M
, namely

⎛⎝⎜⎜−751451⎞⎠⎟⎟=⎛⎝⎜−131⎞⎠⎟−15⎛⎝⎜210⎞⎠⎟.(14.5.5)

The matrix on the right is almost the identity matrix, save the +15
 in the second entry of the first row, whose effect upon multiplying the two matrices precisely undoes what we we did to the second column of the first matrix. For the third column of M
 we use Gram--Schmidt to deduce the third orthogonal vector

⎛⎝⎜⎜−1613−76⎞⎠⎟⎟=⎛⎝⎜1−2−2⎞⎠⎟−0⎛⎝⎜210⎞⎠⎟−−9545⎛⎝⎜⎜−751451⎞⎠⎟⎟(14.5.6)

and therefore, using exactly the same procedure write

M=⎛⎝⎜⎜210−751451−1613−76⎞⎠⎟⎟⎛⎝⎜⎜10015100−561⎞⎠⎟⎟.(14.5.7)

This is not quite the answer because the first matrix is now made of mutually orthogonal column vectors, but a bona fide
 orthogonal matrix is comprised of orthonormal vectors. To achieve that we divide each column of the first matrix by its length and multiply the corresponding row of the second matrix by the same amount:

M=⎛⎝⎜⎜⎜⎜25√55√50−730√90730√4530√18−6√186√9−76√18⎞⎠⎟⎟⎟⎟⎛⎝⎜⎜⎜5–√005√5330√500−30√26√2⎞⎠⎟⎟⎟=QR.(14.5.8)

A nice check of this result is to verify that entry (i,j)
 of the matrix R
 equals the dot product of the i
-th column of Q
 with the j
-th column of M
. (Some people memorize this fact and use it as a recipe for computing QR
 deompositions.) A good test of your own understanding is to work out why this is true!

Contributor
David Cherney, Tom Denton, and Andrew Waldron (UC Davis)