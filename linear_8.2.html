<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linear Algebra 8.2: Orthogonal Diagonalization Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async 
            src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .outline {
            background: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }

        .outline h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .outline-list {
            list-style: none;
            counter-reset: section-counter;
        }

        .outline-list li {
            counter-increment: section-counter;
            margin: 15px 0;
            padding: 15px;
            background: linear-gradient(90deg, #f8f9ff 0%, #ffffff 100%);
            border-radius: 10px;
            border-left: 4px solid #667eea;
            position: relative;
        }

        .outline-list li::before {
            content: "Section " counter(section-counter) ": ";
            font-weight: bold;
            color: #667eea;
        }

        .section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .section h2 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #764ba2;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
        }

        .theorem {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
        }

        .theorem::before {
            content: "Theorem";
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            position: absolute;
            top: -15px;
            left: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .definition {
            background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
            border: 2px solid #ff7043;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
        }

        .definition::before {
            content: "Definition";
            background: #ff7043;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            position: absolute;
            top: -15px;
            left: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .example {
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            border: 2px solid #4caf50;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
        }

        .example::before {
            content: "Example";
            background: #4caf50;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            position: absolute;
            top: -15px;
            left: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .proof {
            background: linear-gradient(135deg, #f9f9f9 0%, #eeeeee 100%);
            border-left: 4px solid #9e9e9e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 12px 12px 0;
            font-style: italic;
        }

        .proof::before {
            content: "Proof: ";
            font-weight: bold;
            color: #666;
            font-style: normal;
        }

        .info-box {
            background: linear-gradient(135deg, #e1f5fe 0%, #f3e5f5 100%);
            border: 1px solid #0288d1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #0288d1;
        }

        .warning-box {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            border: 1px solid #ffa000;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffa000;
        }

        .note {
            background: linear-gradient(135deg, #f3e5f5 0%, #e8eaf6 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #7b1fa2;
        }

        .matrix {
            text-align: center;
            margin: 20px 0;
        }

        .step-by-step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .step {
            background: white;
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .step-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }

        svg {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Linear Algebra 8.2</h1>
            <p>Orthogonal Diagonalization</p>
        </div>

        <div class="outline">
            <h2>📚 Tutorial Outline</h2>
            <ol class="outline-list">
                <li><strong>Introduction & Prerequisites</strong><br>
                    Review of matrix diagonalization, orthogonal vs orthonormal vectors, motivation for orthogonal diagonalization</li>
                <li><strong>Orthogonal Matrices</strong><br>
                    Definition, key properties, examples, and important characteristics</li>
                <li><strong>The Principal Axes Theorem</strong><br>
                    The main result connecting symmetric matrices and orthogonal diagonalizability</li>
                <li><strong>Properties of Symmetric Matrices</strong><br>
                    Key theorems about symmetric matrices and their eigenvectors</li>
                <li><strong>Worked Examples</strong><br>
                    Step-by-step orthogonal diagonalization with detailed calculations</li>
                <li><strong>Applications to Quadratic Forms</strong><br>
                    Principal axes for quadratic forms and geometric interpretations</li>
                <li><strong>Extensions and Advanced Topics</strong><br>
                    Triangulation theorem and related results</li>
            </ol>
        </div>

        <!-- Section 1: Introduction & Prerequisites -->
        <div class="section">
            <h2>1. Introduction & Prerequisites</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Understand the relationship between orthogonal and orthonormal vectors</li>
                    <li>Recall the concept of matrix diagonalization</li>
                    <li>Appreciate the advantages of orthogonal diagonalization</li>
                </ul>
            </div>

            <h3>Review: Matrix Diagonalization</h3>
            <p>Recall from Theorem 5.5.3 that an $n \times n$ matrix $A$ is <span class="highlight">diagonalizable</span> if and only if it has $n$ linearly independent eigenvectors. When this occurs, we can construct a matrix $P$ with these eigenvectors as columns, and we have:</p>
            
            <div class="matrix">
                $$P^{-1}AP = D$$
            </div>
            
            <p>where $D$ is a diagonal matrix containing the eigenvalues of $A$.</p>

            <!-- SVG: Diagonalization Process -->
            <svg width="800" height="300" viewBox="0 0 800 300">
                <defs>
                    <linearGradient id="matrixGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="arrowGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Matrix A -->
                <rect x="50" y="100" width="80" height="80" fill="url(#matrixGrad)" stroke="#667eea" stroke-width="2" rx="5"/>
                <text x="90" y="145" text-anchor="middle" font-family="serif" font-size="24" font-weight="bold">A</text>
                <text x="90" y="200" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Original Matrix</text>
                
                <!-- Arrow 1 -->
                <path d="M 140 140 L 180 140" stroke="url(#arrowGrad)" stroke-width="3" marker-end="url(#arrowhead)"/>
                <text x="160" y="130" text-anchor="middle" font-family="Arial" font-size="10" fill="#667eea">Find eigenvectors</text>
                
                <!-- Matrix P -->
                <rect x="190" y="80" width="80" height="120" fill="url(#matrixGrad)" stroke="#667eea" stroke-width="2" rx="5"/>
                <text x="230" y="145" text-anchor="middle" font-family="serif" font-size="20" font-weight="bold">P</text>
                <text x="230" y="215" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Eigenvector Matrix</text>
                
                <!-- Arrow 2 -->
                <path d="M 280 140 L 320 140" stroke="url(#arrowGrad)" stroke-width="3" marker-end="url(#arrowhead)"/>
                <text x="300" y="130" text-anchor="middle" font-family="Arial" font-size="10" fill="#667eea">P⁻¹AP</text>
                
                <!-- Matrix D -->
                <rect x="330" y="100" width="80" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                <text x="370" y="145" text-anchor="middle" font-family="serif" font-size="24" font-weight="bold">D</text>
                <text x="370" y="200" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Diagonal Matrix</text>
                
                <!-- Diagonal elements visualization -->
                <circle cx="350" cy="120" r="8" fill="#4caf50"/>
                <circle cx="370" cy="140" r="8" fill="#4caf50"/>
                <circle cx="390" cy="160" r="8" fill="#4caf50"/>
                <text x="370" y="175" text-anchor="middle" font-family="Arial" font-size="8" fill="#666">λ₁, λ₂, λ₃</text>
                
                <!-- Question mark -->
                <text x="500" y="150" font-family="Arial" font-size="40" fill="#ff7043">?</text>
                <text x="500" y="180" text-anchor="middle" font-family="Arial" font-size="14" fill="#ff7043">Can we make P</text>
                <text x="500" y="195" text-anchor="middle" font-family="Arial" font-size="14" fill="#ff7043">orthogonal?</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="url(#arrowGrad)" />
                    </marker>
                </defs>
            </svg>

            <h3>Orthogonal vs Orthonormal Vectors</h3>
            <p>Before proceeding, let's clarify the important distinction:</p>
            
            <div class="definition">
                <p><strong>Orthogonal Vectors:</strong> Vectors $\mathbf{v}_1, \mathbf{v}_2, \ldots, \mathbf{v}_k$ are orthogonal if $\mathbf{v}_i \cdot \mathbf{v}_j = 0$ for all $i \neq j$.</p>
                <p><strong>Orthonormal Vectors:</strong> An orthogonal set is orthonormal if additionally $\|\mathbf{v}_i\| = 1$ for each vector.</p>
            </div>

            <div class="note">
                <p><strong>🔑 Key Insight:</strong> Any orthogonal set $\{\mathbf{v}_1, \mathbf{v}_2, \ldots, \mathbf{v}_k\}$ can be normalized to create an orthonormal set:</p>
                $$\left\{\frac{\mathbf{v}_1}{\|\mathbf{v}_1\|}, \frac{\mathbf{v}_2}{\|\mathbf{v}_2\|}, \ldots, \frac{\mathbf{v}_k}{\|\mathbf{v}_k\|}\right\}$$
            </div>

            <!-- SVG: Orthogonal vs Orthonormal -->
            <svg width="700" height="200" viewBox="0 0 700 200">
                <!-- Orthogonal vectors -->
                <g transform="translate(100, 100)">
                    <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">Orthogonal Vectors</text>
                    <!-- Grid -->
                    <defs>
                        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="-60" y="-60" width="120" height="120" fill="url(#grid)"/>
                    
                    <!-- Axes -->
                    <line x1="-60" y1="0" x2="60" y2="0" stroke="#ccc" stroke-width="1"/>
                    <line x1="0" y1="-60" x2="0" y2="60" stroke="#ccc" stroke-width="1"/>
                    
                    <!-- Vector v1 -->
                    <line x1="0" y1="0" x2="40" y2="0" stroke="#ff5722" stroke-width="3" marker-end="url(#arrowRed)"/>
                    <text x="45" y="5" font-family="Arial" font-size="12" fill="#ff5722">v₁</text>
                    <text x="20" y="-10" font-family="Arial" font-size="10" fill="#ff5722">||v₁|| = 2</text>
                    
                    <!-- Vector v2 -->
                    <line x1="0" y1="0" x2="0" y2="-30" stroke="#2196f3" stroke-width="3" marker-end="url(#arrowBlue)"/>
                    <text x="5" y="-35" font-family="Arial" font-size="12" fill="#2196f3">v₂</text>
                    <text x="10" y="-15" font-family="Arial" font-size="10" fill="#2196f3">||v₂|| = 1.5</text>
                    
                    <!-- Dot product -->
                    <text x="0" y="80" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">v₁ · v₂ = 0</text>
                </g>
                
                <!-- Orthonormal vectors -->
                <g transform="translate(450, 100)">
                    <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">Orthonormal Vectors</text>
                    <!-- Grid -->
                    <rect x="-60" y="-60" width="120" height="120" fill="url(#grid)"/>
                    
                    <!-- Axes -->
                    <line x1="-60" y1="0" x2="60" y2="0" stroke="#ccc" stroke-width="1"/>
                    <line x1="0" y1="-60" x2="0" y2="60" stroke="#ccc" stroke-width="1"/>
                    
                    <!-- Unit circle -->
                    <circle cx="0" cy="0" r="20" fill="none" stroke="#4caf50" stroke-width="1" stroke-dasharray="3,3"/>
                    
                    <!-- Vector u1 -->
                    <line x1="0" y1="0" x2="20" y2="0" stroke="#ff5722" stroke-width="3" marker-end="url(#arrowRed)"/>
                    <text x="25" y="5" font-family="Arial" font-size="12" fill="#ff5722">u₁</text>
                    <text x="10" y="-10" font-family="Arial" font-size="10" fill="#ff5722">||u₁|| = 1</text>
                    
                    <!-- Vector u2 -->
                    <line x1="0" y1="0" x2="0" y2="-20" stroke="#2196f3" stroke-width="3" marker-end="url(#arrowBlue)"/>
                    <text x="5" y="-25" font-family="Arial" font-size="12" fill="#2196f3">u₂</text>
                    <text x="10" y="-35" font-family="Arial" font-size="10" fill="#2196f3">||u₂|| = 1</text>
                    
                    <!-- Dot product -->
                    <text x="0" y="80" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">u₁ · u₂ = 0, ||uᵢ|| = 1</text>
                </g>
                
                <!-- Arrow markers -->
                <defs>
                    <marker id="arrowRed" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#ff5722" />
                    </marker>
                    <marker id="arrowBlue" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#2196f3" />
                    </marker>
                </defs>
            </svg>

            <h3>The Power of Orthogonal Bases</h3>
            <p>Orthogonal bases are <span class="highlight">"really nice"</span> for several reasons:</p>
            <ul>
                <li><strong>Easy projections:</strong> Computing projections onto orthogonal subspaces is straightforward</li>
                <li><strong>Simple calculations:</strong> Many linear algebra computations simplify dramatically</li>
                <li><strong>Geometric insight:</strong> Orthogonal bases provide clear geometric interpretations</li>
                <li><strong>Numerical stability:</strong> Computations with orthogonal matrices are numerically stable</li>
            </ul>

            <div class="warning-box">
                <p><strong>⚡ The Central Question:</strong> Which $n \times n$ matrices have an orthogonal basis of eigenvectors?</p>
                <p>This question leads us to the beautiful theory of <strong>orthogonal diagonalization</strong>!</p>
            </div>
        </div>

        <!-- Section 2: Orthogonal Matrices -->
        <div class="section">
            <h2>2. Orthogonal Matrices</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Understand the definition and equivalent characterizations of orthogonal matrices</li>
                    <li>Learn key properties and examples of orthogonal matrices</li>
                    <li>Appreciate why orthogonal matrices are "easy to invert"</li>
                </ul>
            </div>

            <h3>The Fundamental Theorem for Orthogonal Matrices</h3>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.1:</strong> The following conditions are equivalent for an $n \times n$ matrix $P$:</p>
                <ol>
                    <li>$P$ is invertible and $P^{-1} = P^T$</li>
                    <li>The rows of $P$ are orthonormal</li>
                    <li>The columns of $P$ are orthonormal</li>
                </ol>
            </div>

            <div class="proof">
                <p>Condition (1) is equivalent to $PP^T = I$ by properties of matrix inverses.</p>
                <p>Let $\mathbf{x}_1, \mathbf{x}_2, \ldots, \mathbf{x}_n$ denote the rows of $P$. Then $\mathbf{x}_j^T$ is the $j$-th column of $P^T$, so the $(i,j)$-entry of $PP^T$ is $\mathbf{x}_i \cdot \mathbf{x}_j$.</p>
                <p>Thus $PP^T = I$ means:</p>
                <ul>
                    <li>$\mathbf{x}_i \cdot \mathbf{x}_j = 0$ if $i \neq j$ (orthogonal rows)</li>
                    <li>$\mathbf{x}_i \cdot \mathbf{x}_i = 1$ for all $i$ (unit length rows)</li>
                </ul>
                <p>This proves $(1) \Leftrightarrow (2)$. The proof of $(1) \Leftrightarrow (3)$ is similar.</p>
            </div>

            <div class="definition">
                <p><strong>Orthogonal Matrix:</strong> An $n \times n$ matrix $P$ is called an <span class="highlight">orthogonal matrix</span> if it satisfies one (and hence all) of the conditions in Theorem 8.2.1.</p>
                <p><strong>Note:</strong> The name "orthonormal matrix" might be more accurate, but "orthogonal matrix" is standard terminology.</p>
            </div>

            <!-- SVG: Orthogonal Matrix Properties -->
            <svg width="800" height="400" viewBox="0 0 800 400">
                <defs>
                    <linearGradient id="orthogGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    Properties of Orthogonal Matrix P
                </text>
                
                <!-- Property 1: Easy inverse -->
                <g transform="translate(150, 80)">
                    <rect x="-100" y="-30" width="200" height="60" fill="url(#orthogGrad)" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="0" y="-10" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#2e7d32">Easy to Invert</text>
                    <text x="0" y="10" text-anchor="middle" font-family="serif" font-size="16" fill="#2e7d32">P⁻¹ = Pᵀ</text>
                </g>
                
                <!-- Property 2: Orthonormal columns -->
                <g transform="translate(650, 80)">
                    <rect x="-100" y="-30" width="200" height="60" fill="url(#orthogGrad)" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="0" y="-10" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#2e7d32">Orthonormal Columns</text>
                    <text x="0" y="10" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">‖pᵢ‖ = 1, pᵢ · pⱼ = 0</text>
                </g>
                
                <!-- Matrix visualization -->
                <g transform="translate(400, 200)">
                    <!-- Matrix P -->
                    <rect x="-60" y="-60" width="120" height="120" fill="none" stroke="#667eea" stroke-width="3" rx="5"/>
                    <text x="0" y="5" text-anchor="middle" font-family="serif" font-size="24" font-weight="bold" fill="#667eea">P</text>
                    
                    <!-- Column vectors -->
                    <line x1="-40" y1="-40" x2="-40" y2="40" stroke="#ff5722" stroke-width="3"/>
                    <line x1="-20" y1="-40" x2="-20" y2="40" stroke="#2196f3" stroke-width="3"/>
                    <line x1="0" y1="-40" x2="0" y2="40" stroke="#4caf50" stroke-width="3"/>
                    <line x1="20" y1="-40" x2="20" y2="40" stroke="#ff9800" stroke-width="3"/>
                    <line x1="40" y1="-40" x2="40" y2="40" stroke="#9c27b0" stroke-width="3"/>
                    
                    <!-- Column labels -->
                    <text x="-40" y="-50" text-anchor="middle" font-family="Arial" font-size="10" fill="#ff5722">p₁</text>
                    <text x="-20" y="-50" text-anchor="middle" font-family="Arial" font-size="10" fill="#2196f3">p₂</text>
                    <text x="0" y="-50" text-anchor="middle" font-family="Arial" font-size="10" fill="#4caf50">p₃</text>
                    <text x="20" y="-50" text-anchor="middle" font-family="Arial" font-size="10" fill="#ff9800">p₄</text>
                    <text x="40" y="-50" text-anchor="middle" font-family="Arial" font-size="10" fill="#9c27b0">p₅</text>
                </g>
                
                <!-- Property 3: Orthonormal rows -->
                <g transform="translate(150, 320)">
                    <rect x="-100" y="-30" width="200" height="60" fill="url(#orthogGrad)" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="0" y="-10" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#2e7d32">Orthonormal Rows</text>
                    <text x="0" y="10" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">‖rᵢ‖ = 1, rᵢ · rⱼ = 0</text>
                </g>
                
                <!-- Property 4: Preserves lengths -->
                <g transform="translate(650, 320)">
                    <rect x="-100" y="-30" width="200" height="60" fill="url(#orthogGrad)" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="0" y="-10" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#2e7d32">Preserves Lengths</text>
                    <text x="0" y="10" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">‖Px‖ = ‖x‖</text>
                </g>
            </svg>

            <h3>Examples of Orthogonal Matrices</h3>
            
            <div class="example">
                <p><strong>Example 8.2.1:</strong> The rotation matrix 
                $$R_\theta = \begin{pmatrix} \cos\theta & -\sin\theta \\ \sin\theta & \cos\theta \end{pmatrix}$$
                is orthogonal for any angle $\theta$.</p>
                
                <p><strong>Verification:</strong> We need to check that $R_\theta R_\theta^T = I$:</p>
                $$R_\theta R_\theta^T = \begin{pmatrix} \cos\theta & -\sin\theta \\ \sin\theta & \cos\theta \end{pmatrix} \begin{pmatrix} \cos\theta & \sin\theta \\ -\sin\theta & \cos\theta \end{pmatrix}$$
                $$= \begin{pmatrix} \cos^2\theta + \sin^2\theta & 0 \\ 0 & \sin^2\theta + \cos^2\theta \end{pmatrix} = \begin{pmatrix} 1 & 0 \\ 0 & 1 \end{pmatrix} = I$$
            </div>

            <!-- SVG: Rotation Matrix Visualization -->
            <svg width="600" height="300" viewBox="0 0 600 300">
                <defs>
                    <marker id="vectorArrow" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#667eea" />
                    </marker>
                </defs>
                
                <g transform="translate(150, 150)">
                    <text x="0" y="-120" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">
                        Rotation Matrix: Length-Preserving Transformation
                    </text>
                    
                    <!-- Coordinate axes -->
                    <line x1="-100" y1="0" x2="100" y2="0" stroke="#ddd" stroke-width="1"/>
                    <line x1="0" y1="-100" x2="0" y2="100" stroke="#ddd" stroke-width="1"/>
                    
                    <!-- Unit circle -->
                    <circle cx="0" cy="0" r="60" fill="none" stroke="#e0e0e0" stroke-width="1" stroke-dasharray="5,5"/>
                    
                    <!-- Original vector -->
                    <line x1="0" y1="0" x2="60" y2="0" stroke="#ff5722" stroke-width="3" marker-end="url(#vectorArrow)"/>
                    <text x="70" y="5" font-family="Arial" font-size="12" fill="#ff5722">x</text>
                    
                    <!-- Rotated vector -->
                    <line x1="0" y1="0" x2="42.4" y2="-42.4" stroke="#2196f3" stroke-width="3" marker-end="url(#vectorArrow)"/>
                    <text x="50" y="-50" font-family="Arial" font-size="12" fill="#2196f3">R₄₅°x</text>
                    
                    <!-- Angle arc -->
                    <path d="M 30 0 A 30 30 0 0 0 21.2 -21.2" fill="none" stroke="#4caf50" stroke-width="2"/>
                    <text x="25" y="-15" font-family="Arial" font-size="10" fill="#4caf50">45°</text>
                    
                    <!-- Length preservation note -->
                    <text x="0" y="120" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        ||x|| = ||R₄₅°x|| = 1 (length preserved)
                    </text>
                </g>
                
                <!-- Matrix representation -->
                <g transform="translate(450, 150)">
                    <rect x="-80" y="-40" width="160" height="80" fill="#f5f5f5" stroke="#667eea" stroke-width="2" rx="10"/>
                    <text x="0" y="-15" text-anchor="middle" font-family="serif" font-size="14" fill="#667eea">R₄₅° =</text>
                    <text x="0" y="5" text-anchor="middle" font-family="serif" font-size="12" fill="#667eea">⎛ √2/2  -√2/2 ⎞</text>
                    <text x="0" y="20" text-anchor="middle" font-family="serif" font-size="12" fill="#667eea">⎝ √2/2   √2/2 ⎠</text>
                </g>
            </svg>

            <div class="example">
                <p><strong>Example 8.2.2:</strong> Consider the matrix 
                $$A = \begin{pmatrix} 2 & 1 & 1 \\ -1 & 1 & 1 \\ 0 & -1 & 1 \end{pmatrix}$$</p>
                
                <p>This matrix has orthogonal rows, but the columns are <strong>not</strong> orthogonal, so $A$ is not orthogonal.</p>
                
                <p>However, if we normalize the rows, we get:</p>
                $$P = \begin{pmatrix} 
                \frac{\sqrt{2}}{\sqrt{6}} & \frac{1}{\sqrt{6}} & \frac{1}{\sqrt{6}} \\
                \frac{-1}{\sqrt{3}} & \frac{1}{\sqrt{3}} & \frac{1}{\sqrt{3}} \\
                0 & \frac{-1}{\sqrt{2}} & \frac{1}{\sqrt{2}}
                \end{pmatrix}$$
                
                <p>Now $P$ is orthogonal (both rows and columns are orthonormal).</p>
            </div>

            <h3>Properties of Orthogonal Matrices</h3>
            
            <div class="theorem">
                <p><strong>Important Properties:</strong> If $P$ and $Q$ are orthogonal matrices, then:</p>
                <ol>
                    <li>$PQ$ is also orthogonal</li>
                    <li>$P^{-1} = P^T$ is also orthogonal</li>
                    <li>$\det(P) = \pm 1$</li>
                    <li>Orthogonal matrices preserve lengths: $\|P\mathbf{x}\| = \|\mathbf{x}\|$</li>
                    <li>Orthogonal matrices preserve angles and distances</li>
                </ol>
            </div>

            <div class="proof">
                <p><strong>For property 1:</strong> Since $P$ and $Q$ are invertible, $PQ$ is invertible and:
                $$(PQ)^{-1} = Q^{-1}P^{-1} = Q^T P^T = (PQ)^T$$
                Hence $PQ$ is orthogonal.</p>
                
                <p><strong>For property 2:</strong> We have $(P^{-1})^{-1} = P = (P^T)^T = (P^{-1})^T$, so $P^{-1}$ is orthogonal.</p>
            </div>

            <div class="note">
                <p><strong>🔑 Geometric Interpretation:</strong> Orthogonal matrices represent combinations of rotations and reflections. They preserve the geometry of space - distances, angles, and orientations (up to reflection) remain unchanged.</p>
            </div>

            <div class="warning-box">
                <p><strong>⚠️ Important:</strong> For a matrix to be orthogonal, it's not sufficient that the rows are merely orthogonal - they must be orthonormal (orthogonal with unit length). The same applies to columns.</p>
            </div>
        </div>

        <!-- Section 3: The Principal Axes Theorem -->
        <div class="section">
            <h2>3. The Principal Axes Theorem</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Understand the connection between symmetric matrices and orthogonal diagonalizability</li>
                    <li>Learn the equivalence of three key conditions</li>
                    <li>Appreciate the power of this fundamental theorem</li>
                </ul>
            </div>

            <h3>Orthogonally Diagonalizable Matrices</h3>
            
            <div class="definition">
                <p><strong>Orthogonally Diagonalizable:</strong> An $n \times n$ matrix $A$ is said to be <span class="highlight">orthogonally diagonalizable</span> when an orthogonal matrix $P$ can be found such that:</p>
                $$P^{-1}AP = P^TAP \text{ is diagonal}$$
            </div>

            <p>This is a stronger condition than regular diagonalizability - we require that the diagonalizing matrix $P$ be orthogonal.</p>

            <!-- SVG: Orthogonal vs Regular Diagonalization -->
            <svg width="800" height="350" viewBox="0 0 800 350">
                <defs>
                    <linearGradient id="regularGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffecb3;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#fff8e1;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="orthogonalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    Regular vs Orthogonal Diagonalization
                </text>
                
                <!-- Regular Diagonalization -->
                <g transform="translate(200, 150)">
                    <rect x="-150" y="-80" width="300" height="160" fill="url(#regularGrad)" stroke="#ff9800" stroke-width="2" rx="15"/>
                    <text x="0" y="-50" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#e65100">Regular Diagonalization</text>
                    
                    <text x="0" y="-20" text-anchor="middle" font-family="serif" font-size="14" fill="#e65100">P⁻¹AP = D</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">P has linearly independent</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">columns (eigenvectors)</text>
                    <text x="0" y="35" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">P⁻¹ can be complicated to compute</text>
                    <text x="0" y="55" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#e65100">Works for many matrices</text>
                </g>
                
                <!-- Orthogonal Diagonalization -->
                <g transform="translate(600, 150)">
                    <rect x="-150" y="-80" width="300" height="160" fill="url(#orthogonalGrad)" stroke="#4caf50" stroke-width="2" rx="15"/>
                    <text x="0" y="-50" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#2e7d32">Orthogonal Diagonalization</text>
                    
                    <text x="0" y="-20" text-anchor="middle" font-family="serif" font-size="14" fill="#2e7d32">PᵀAP = D</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">P has orthonormal</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">columns (eigenvectors)</text>
                    <text x="0" y="35" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">P⁻¹ = Pᵀ (easy to compute!)</text>
                    <text x="0" y="55" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#2e7d32">Only for symmetric matrices</text>
                </g>
                
                <!-- Question: Which matrices? -->
                <text x="400" y="280" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#ff5722">
                    Which matrices can be orthogonally diagonalized?
                </text>
                <text x="400" y="300" text-anchor="middle" font-family="Arial" font-size="14" fill="#ff5722">
                    The answer is beautifully simple...
                </text>
            </svg>

            <h3>The Main Theorem</h3>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.2 (Principal Axes Theorem):</strong> The following conditions are equivalent for an $n \times n$ matrix $A$:</p>
                <ol>
                    <li>$A$ has an orthonormal set of $n$ eigenvectors</li>
                    <li>$A$ is orthogonally diagonalizable</li>
                    <li>$A$ is symmetric</li>
                </ol>
            </div>

            <div class="note">
                <p><strong>🌟 Remarkable Result:</strong> This theorem tells us that a matrix can be orthogonally diagonalized if and only if it is symmetric! This provides a complete characterization.</p>
            </div>

            <!-- SVG: Principal Axes Theorem Visualization -->
            <svg width="700" height="400" viewBox="0 0 700 400">
                <defs>
                    <linearGradient id="condition1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="condition2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="condition3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ffe0b2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    The Equivalence Triangle
                </text>
                
                <!-- Condition 1: Orthonormal eigenvectors -->
                <g transform="translate(350, 100)">
                    <rect x="-120" y="-40" width="240" height="80" fill="url(#condition1)" stroke="#2196f3" stroke-width="3" rx="15"/>
                    <text x="0" y="-15" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#1565c0">Condition 1</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#1565c0">A has orthonormal set</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="12" fill="#1565c0">of n eigenvectors</text>
                </g>
                
                <!-- Condition 2: Orthogonally diagonalizable -->
                <g transform="translate(150, 280)">
                    <rect x="-120" y="-40" width="240" height="80" fill="url(#condition2)" stroke="#4caf50" stroke-width="3" rx="15"/>
                    <text x="0" y="-15" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#2e7d32">Condition 2</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">A is orthogonally</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">diagonalizable</text>
                </g>
                
                <!-- Condition 3: Symmetric -->
                <g transform="translate(550, 280)">
                    <rect x="-120" y="-40" width="240" height="80" fill="url(#condition3)" stroke="#ff9800" stroke-width="3" rx="15"/>
                    <text x="0" y="-15" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#e65100">Condition 3</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">A is symmetric</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">(A = Aᵀ)</text>
                </g>
                
                <!-- Arrows showing equivalence -->
                <defs>
                    <marker id="equivArrow" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#667eea" />
                    </marker>
                </defs>
                
                <!-- 1 ↔ 2 -->
                <line x1="270" y1="160" x2="200" y2="220" stroke="#667eea" stroke-width="3" marker-end="url(#equivArrow)"/>
                <line x1="200" y1="220" x2="270" y2="160" stroke="#667eea" stroke-width="3" marker-end="url(#equivArrow)"/>
                <text x="230" y="185" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#667eea">⟺</text>
                
                <!-- 2 ↔ 3 -->
                <line x1="270" y1="280" x2="430" y2="280" stroke="#667eea" stroke-width="3" marker-end="url(#equivArrow)"/>
                <line x1="430" y1="280" x2="270" y2="280" stroke="#667eea" stroke-width="3" marker-end="url(#equivArrow)"/>
                <text x="350" y="295" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#667eea">⟺</text>
                
                <!-- 1 ↔ 3 -->
                <line x1="430" y1="160" x2="500" y2="220" stroke="#667eea" stroke-width="3" marker-end="url(#equivArrow)"/>
                <line x1="500" y1="220" x2="430" y2="160" stroke="#667eea" stroke-width="3" marker-end="url(#equivArrow)"/>
                <text x="470" y="185" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold" fill="#667eea">⟺</text>
                
                <!-- Center text -->
                <text x="350" y="350" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#667eea">
                    All three conditions are equivalent!
                </text>
            </svg>

            <h3>Proof Outline</h3>
            
            <div class="proof">
                <p><strong>$(1) \Leftrightarrow (2)$:</strong> If $A$ has orthonormal eigenvectors $\mathbf{x}_1, \mathbf{x}_2, \ldots, \mathbf{x}_n$, then $P = [\mathbf{x}_1 \; \mathbf{x}_2 \; \cdots \; \mathbf{x}_n]$ is orthogonal, and $P^{-1}AP$ is diagonal.</p>
                
                <p>Conversely, if $P^{-1}AP$ is diagonal where $P$ is orthogonal, then the columns of $P$ form an orthonormal set of eigenvectors.</p>
                
                <p><strong>$(2) \Rightarrow (3)$:</strong> If $P^TAP = D$ is diagonal where $P^{-1} = P^T$, then:
                $$A = PDP^T$$
                Since $D^T = D$, we have:
                $$A^T = (PDP^T)^T = PD^TP^T = PDP^T = A$$
                So $A$ is symmetric.</p>
                
                <p><strong>$(3) \Rightarrow (2)$:</strong> This is the most involved direction, proved by induction on $n$. The key insight is that symmetric matrices have real eigenvalues, and eigenvectors corresponding to distinct eigenvalues are orthogonal.</p>
            </div>

            <h3>Why "Principal Axes"?</h3>
            
            <p>The name "Principal Axes Theorem" comes from geometry. A set of orthonormal eigenvectors of a symmetric matrix $A$ is called a set of <span class="highlight">principal axes</span> for $A$.</p>

            <div class="note">
                <p><strong>🔑 Key Terminology:</strong></p>
                <ul>
                    <li><strong>Principal Axes:</strong> Orthonormal eigenvectors of a symmetric matrix</li>
                    <li><strong>Real Spectral Theorem:</strong> Another name for the Principal Axes Theorem</li>
                    <li><strong>Spectrum:</strong> The set of distinct eigenvalues of a matrix</li>
                </ul>
            </div>

            <div class="info-box">
                <p><strong>🎯 Practical Significance:</strong> This theorem tells us exactly when we can find an orthogonal diagonalization:</p>
                <ol>
                    <li><strong>Check if the matrix is symmetric</strong> - if yes, it can be orthogonally diagonalized</li>
                    <li><strong>Find eigenvalues and eigenvectors</strong> - all eigenvalues will be real</li>
                    <li><strong>Orthogonalize eigenvectors within each eigenspace</strong> (if needed)</li>
                    <li><strong>Normalize to get orthonormal eigenvectors</strong></li>
                    <li><strong>Form orthogonal matrix $P$</strong> with these as columns</li>
                </ol>
            </div>

            <div class="warning-box">
                <p><strong>⚡ Remember:</strong> Only symmetric matrices can be orthogonally diagonalized. However, many important matrices in applications (like covariance matrices, moment of inertia tensors, etc.) are symmetric!</p>
            </div>
        </div>

        <!-- Section 4: Properties of Symmetric Matrices -->
        <div class="section">
            <h2>4. Properties of Symmetric Matrices</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Learn the key property that makes symmetric matrices special</li>
                    <li>Understand why eigenvectors of symmetric matrices are orthogonal</li>
                    <li>See how these properties enable orthogonal diagonalization</li>
                </ul>
            </div>

            <h3>The Fundamental Property</h3>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.3:</strong> If $A$ is an $n \times n$ symmetric matrix, then</p>
                $$(A\mathbf{x}) \cdot \mathbf{y} = \mathbf{x} \cdot (A\mathbf{y})$$
                <p>for all columns $\mathbf{x}$ and $\mathbf{y}$ in $\mathbb{R}^n$.</p>
            </div>

            <div class="proof">
                <p>Recall that $\mathbf{x} \cdot \mathbf{y} = \mathbf{x}^T \mathbf{y}$ for all columns $\mathbf{x}$ and $\mathbf{y}$. Since $A^T = A$, we get:
                $$(A\mathbf{x}) \cdot \mathbf{y} = (A\mathbf{x})^T \mathbf{y} = \mathbf{x}^T A^T \mathbf{y} = \mathbf{x}^T A \mathbf{y} = \mathbf{x} \cdot (A\mathbf{y})$$
                </p>
            </div>

            <div class="note">
                <p><strong>🔑 Geometric Interpretation:</strong> This property means that the linear transformation represented by a symmetric matrix preserves the "bilinear relationship" between vectors. This is a fundamental symmetry property.</p>
            </div>

            <!-- SVG: Symmetric Property Visualization -->
            <svg width="700" height="300" viewBox="0 0 700 300">
                <defs>
                    <linearGradient id="symGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    Symmetric Matrix Property: (Ax)·y = x·(Ay)
                </text>
                
                <!-- Left side: (Ax)·y -->
                <g transform="translate(150, 150)">
                    <rect x="-120" y="-80" width="240" height="160" fill="url(#symGrad)" stroke="#4caf50" stroke-width="2" rx="15"/>
                    <text x="0" y="-50" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#2e7d32">(Ax) · y</text>
                    
                    <!-- Vector visualization -->
                    <g transform="translate(0, -20)">
                        <!-- x vector -->
                        <line x1="-40" y1="20" x2="-20" y2="0" stroke="#2196f3" stroke-width="3"/>
                        <text x="-45" y="25" font-family="Arial" font-size="12" fill="#2196f3">x</text>
                        
                        <!-- A transformation -->
                        <rect x="-10" y="5" width="20" height="20" fill="#667eea" stroke="#667eea"/>
                        <text x="0" y="18" text-anchor="middle" font-family="serif" font-size="12" fill="white">A</text>
                        
                        <!-- Ax vector -->
                        <line x1="20" y1="10" x2="50" y2="-10" stroke="#ff5722" stroke-width="3"/>
                        <text x="55" y="-5" font-family="Arial" font-size="12" fill="#ff5722">Ax</text>
                        
                        <!-- y vector -->
                        <line x1="-10" y1="35" x2="10" y2="45" stroke="#4caf50" stroke-width="3"/>
                        <text x="15" y="50" font-family="Arial" font-size="12" fill="#4caf50">y</text>
                        
                        <!-- Dot product indication -->
                        <circle cx="30" cy="25" r="5" fill="#ff9800"/>
                        <text x="0" y="65" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Take dot product</text>
                    </g>
                </g>
                
                <!-- Equals sign -->
                <text x="350" y="150" text-anchor="middle" font-family="Arial" font-size="30" font-weight="bold" fill="#667eea">=</text>
                
                <!-- Right side: x·(Ay) -->
                <g transform="translate(550, 150)">
                    <rect x="-120" y="-80" width="240" height="160" fill="url(#symGrad)" stroke="#4caf50" stroke-width="2" rx="15"/>
                    <text x="0" y="-50" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#2e7d32">x · (Ay)</text>
                    
                    <!-- Vector visualization -->
                    <g transform="translate(0, -20)">
                        <!-- y vector -->
                        <line x1="-50" y1="35" x2="-30" y2="45" stroke="#4caf50" stroke-width="3"/>
                        <text x="-55" y="40" font-family="Arial" font-size="12" fill="#4caf50">y</text>
                        
                        <!-- A transformation -->
                        <rect x="-10" y="35" width="20" height="20" fill="#667eea" stroke="#667eea"/>
                        <text x="0" y="48" text-anchor="middle" font-family="serif" font-size="12" fill="white">A</text>
                        
                        <!-- Ay vector -->
                        <line x1="20" y1="40" x2="50" y2="50" stroke="#9c27b0" stroke-width="3"/>
                        <text x="55" y="55" font-family="Arial" font-size="12" fill="#9c27b0">Ay</text>
                        
                        <!-- x vector -->
                        <line x1="-40" y1="10" x2="-20" y2="-10" stroke="#2196f3" stroke-width="3"/>
                        <text x="-45" y="5" font-family="Arial" font-size="12" fill="#2196f3">x</text>
                        
                        <!-- Dot product indication -->
                        <circle cx="5" cy="15" r="5" fill="#ff9800"/>
                        <text x="0" y="65" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">Take dot product</text>
                    </g>
                </g>
                
                <!-- Note -->
                <text x="350" y="270" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
                    This symmetry property is unique to symmetric matrices!
                </text>
            </svg>

            <h3>Orthogonality of Eigenvectors</h3>
            
            <p>The symmetric property leads to a crucial result about eigenvectors:</p>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.4:</strong> If $A$ is a symmetric matrix, then eigenvectors of $A$ corresponding to distinct eigenvalues are orthogonal.</p>
            </div>

            <div class="proof">
                <p>Let $A\mathbf{x} = \lambda\mathbf{x}$ and $A\mathbf{y} = \mu\mathbf{y}$, where $\lambda \neq \mu$. Using Theorem 8.2.3, we compute:</p>
                $$\lambda(\mathbf{x} \cdot \mathbf{y}) = (\lambda\mathbf{x}) \cdot \mathbf{y} = (A\mathbf{x}) \cdot \mathbf{y} = \mathbf{x} \cdot (A\mathbf{y}) = \mathbf{x} \cdot (\mu\mathbf{y}) = \mu(\mathbf{x} \cdot \mathbf{y})$$
                <p>Hence $(\lambda - \mu)(\mathbf{x} \cdot \mathbf{y}) = 0$, and so $\mathbf{x} \cdot \mathbf{y} = 0$ because $\lambda \neq \mu$.</p>
            </div>

            <!-- SVG: Orthogonal Eigenvectors -->
            <svg width="600" height="400" viewBox="0 0 600 400">
                <defs>
                    <marker id="eigenArrow" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#ff5722" />
                    </marker>
                    <marker id="eigenArrow2" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#2196f3" />
                    </marker>
                </defs>
                
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">
                    Eigenvectors of Symmetric Matrix are Orthogonal
                </text>
                
                <g transform="translate(300, 200)">
                    <!-- Coordinate grid -->
                    <defs>
                        <pattern id="eigenGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="-150" y="-120" width="300" height="240" fill="url(#eigenGrid)"/>
                    
                    <!-- Axes -->
                    <line x1="-150" y1="0" x2="150" y2="0" stroke="#ddd" stroke-width="2"/>
                    <line x1="0" y1="-120" x2="0" y2="120" stroke="#ddd" stroke-width="2"/>
                    
                    <!-- Eigenvector 1 -->
                    <line x1="0" y1="0" x2="80" y2="0" stroke="#ff5722" stroke-width="4" marker-end="url(#eigenArrow)"/>
                    <text x="90" y="5" font-family="Arial" font-size="14" fill="#ff5722">v₁</text>
                    <text x="90" y="20" font-family="Arial" font-size="12" fill="#ff5722">λ₁ = 3</text>
                    
                    <!-- Eigenvector 2 -->
                    <line x1="0" y1="0" x2="0" y2="-60" stroke="#2196f3" stroke-width="4" marker-end="url(#eigenArrow2)"/>
                    <text x="10" y="-65" font-family="Arial" font-size="14" fill="#2196f3">v₂</text>
                    <text x="10" y="-50" font-family="Arial" font-size="12" fill="#2196f3">λ₂ = 1</text>
                    
                    <!-- Right angle indicator -->
                    <path d="M 15 0 L 15 -15 L 0 -15" fill="none" stroke="#4caf50" stroke-width="2"/>
                    <text x="20" y="-20" font-family="Arial" font-size="12" fill="#4caf50">90°</text>
                    
                    <!-- Orthogonality note -->
                    <text x="0" y="100" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
                        v₁ · v₂ = 0 (since λ₁ ≠ λ₂)
                    </text>
                    
                    <!-- Matrix example -->
                    <g transform="translate(-100, -100)">
                        <rect x="-50" y="-30" width="100" height="60" fill="#f5f5f5" stroke="#667eea" stroke-width="1" rx="5"/>
                        <text x="0" y="-10" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Example: A =</text>
                        <text x="0" y="5" text-anchor="middle" font-family="serif" font-size="11" fill="#667eea">⎛ 3  0 ⎞</text>
                        <text x="0" y="18" text-anchor="middle" font-family="serif" font-size="11" fill="#667eea">⎝ 0  1 ⎠</text>
                    </g>
                </g>
                
                <!-- Key insight -->
                <text x="300" y="360" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#4caf50">
                    This orthogonality is automatic for symmetric matrices!
                </text>
            </svg>

            <h3>The Complete Procedure</h3>
            
            <p>Now we can outline the complete procedure for orthogonally diagonalizing a symmetric $n \times n$ matrix:</p>

            <div class="step-by-step">
                <h4>📋 Orthogonal Diagonalization Algorithm</h4>
                
                <div class="step">
                    <span class="step-number">1</span>
                    <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                        <strong>Find the eigenvalues</strong><br>
                        Solve the characteristic equation $\det(A - \lambda I) = 0$. All eigenvalues will be real.
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                        <strong>Find eigenvectors for each eigenvalue</strong><br>
                        For each eigenvalue $\lambda_i$, find a basis for the eigenspace $E_{\lambda_i}(A)$.
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                        <strong>Orthogonalize within each eigenspace</strong><br>
                        If an eigenspace has dimension > 1, use the Gram-Schmidt process to get orthogonal vectors within that eigenspace.
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">4</span>
                    <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                        <strong>Normalize all eigenvectors</strong><br>
                        Convert the orthogonal eigenvectors to orthonormal by dividing each by its length.
                    </div>
                </div>
                
                <div class="step">
                    <span class="step-number">5</span>
                    <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                        <strong>Form the orthogonal matrix P</strong><br>
                        Use the orthonormal eigenvectors as columns of $P$. Then $P^TAP$ is diagonal.
                    </div>
                </div>
            </div>

            <div class="note">
                <p><strong>🔑 Why This Works:</strong></p>
                <ul>
                    <li><strong>Step 1:</strong> Symmetric matrices always have real eigenvalues (Theorem 5.5.7)</li>
                    <li><strong>Step 2:</strong> Symmetric matrices are always diagonalizable</li>
                    <li><strong>Step 3:</strong> Eigenvectors from different eigenspaces are automatically orthogonal (Theorem 8.2.4)</li>
                    <li><strong>Step 4:</strong> Normalization preserves orthogonality</li>
                    <li><strong>Step 5:</strong> The resulting matrix $P$ is orthogonal by construction</li>
                </ul>
            </div>

            <div class="warning-box">
                <p><strong>⚠️ Important Note:</strong> The Gram-Schmidt process in Step 3 is only needed if some eigenvalue has multiplicity greater than 1. For simple eigenvalues, the eigenvectors are automatically orthogonal to all others.</p>
            </div>

            <div class="theorem">
                <p><strong>Converse (Exercise 8.2.15):</strong> If $(A\mathbf{x}) \cdot \mathbf{y} = \mathbf{x} \cdot (A\mathbf{y})$ for all $n$-columns $\mathbf{x}$ and $\mathbf{y}$, then $A$ is symmetric.</p>
            </div>

            <p>This shows that the property in Theorem 8.2.3 completely characterizes symmetric matrices.</p>
        </div>

        <!-- Section 5: Worked Examples -->
        <div class="section">
            <h2>5. Worked Examples</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Apply the orthogonal diagonalization algorithm to concrete examples</li>
                    <li>Practice finding eigenvalues, eigenvectors, and orthonormal bases</li>
                    <li>See how the theory translates to computational practice</li>
                </ul>
            </div>

            <h3>Example 5.1: Simple 2×2 Case</h3>
            
            <div class="example">
                <p><strong>Problem:</strong> Orthogonally diagonalize the matrix 
                $$A = \begin{pmatrix} 3 & 1 \\ 1 & 3 \end{pmatrix}$$</p>
                
                <div class="step-by-step">
                    <div class="step">
                        <span class="step-number">1</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find the eigenvalues</strong><br>
                            The characteristic polynomial is:
                            $$\det(A - \lambda I) = \det\begin{pmatrix} 3-\lambda & 1 \\ 1 & 3-\lambda \end{pmatrix} = (3-\lambda)^2 - 1 = \lambda^2 - 6\lambda + 8$$
                            $$= (\lambda - 2)(\lambda - 4)$$
                            So the eigenvalues are $\lambda_1 = 4$ and $\lambda_2 = 2$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">2</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find eigenvectors for λ₁ = 4</strong><br>
                            Solve $(A - 4I)\mathbf{x} = \mathbf{0}$:
                            $$\begin{pmatrix} -1 & 1 \\ 1 & -1 \end{pmatrix}\begin{pmatrix} x_1 \\ x_2 \end{pmatrix} = \begin{pmatrix} 0 \\ 0 \end{pmatrix}$$
                            This gives $-x_1 + x_2 = 0$, so $x_2 = x_1$. A basis eigenvector is $\mathbf{v}_1 = \begin{pmatrix} 1 \\ 1 \end{pmatrix}$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">3</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find eigenvectors for λ₂ = 2</strong><br>
                            Solve $(A - 2I)\mathbf{x} = \mathbf{0}$:
                            $$\begin{pmatrix} 1 & 1 \\ 1 & 1 \end{pmatrix}\begin{pmatrix} x_1 \\ x_2 \end{pmatrix} = \begin{pmatrix} 0 \\ 0 \end{pmatrix}$$
                            This gives $x_1 + x_2 = 0$, so $x_2 = -x_1$. A basis eigenvector is $\mathbf{v}_2 = \begin{pmatrix} 1 \\ -1 \end{pmatrix}$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">4</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Check orthogonality and normalize</strong><br>
                            We verify: $\mathbf{v}_1 \cdot \mathbf{v}_2 = (1)(1) + (1)(-1) = 0$ ✓<br>
                            Now normalize:
                            $$\mathbf{u}_1 = \frac{\mathbf{v}_1}{\|\mathbf{v}_1\|} = \frac{1}{\sqrt{2}}\begin{pmatrix} 1 \\ 1 \end{pmatrix} = \begin{pmatrix} \frac{1}{\sqrt{2}} \\ \frac{1}{\sqrt{2}} \end{pmatrix}$$
                            $$\mathbf{u}_2 = \frac{\mathbf{v}_2}{\|\mathbf{v}_2\|} = \frac{1}{\sqrt{2}}\begin{pmatrix} 1 \\ -1 \end{pmatrix} = \begin{pmatrix} \frac{1}{\sqrt{2}} \\ -\frac{1}{\sqrt{2}} \end{pmatrix}$$
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">5</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Form the orthogonal matrix P</strong><br>
                            $$P = [\mathbf{u}_1 \; \mathbf{u}_2] = \begin{pmatrix} \frac{1}{\sqrt{2}} & \frac{1}{\sqrt{2}} \\ \frac{1}{\sqrt{2}} & -\frac{1}{\sqrt{2}} \end{pmatrix}$$
                            
                            Then $P^TAP = \begin{pmatrix} 4 & 0 \\ 0 & 2 \end{pmatrix}$.
                        </div>
                    </div>
                </div>
            </div>

            <!-- SVG: 2x2 Example Visualization -->
            <svg width="800" height="400" viewBox="0 0 800 400">
                <defs>
                    <linearGradient id="eigenspace1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ffcdd2;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="eigenspace2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                    <marker id="eigenvecArrow" markerWidth="8" markerHeight="6" 
                            refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#667eea" />
                    </marker>
                </defs>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    Example 5.1: Orthogonal Eigenvectors
                </text>
                
                <g transform="translate(400, 200)">
                    <!-- Grid -->
                    <defs>
                        <pattern id="exampleGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f5f5f5" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="-200" y="-150" width="400" height="300" fill="url(#exampleGrid)"/>
                    
                    <!-- Axes -->
                    <line x1="-200" y1="0" x2="200" y2="0" stroke="#ddd" stroke-width="2"/>
                    <line x1="0" y1="-150" x2="0" y2="150" stroke="#ddd" stroke-width="2"/>
                    
                    <!-- Eigenspace for λ₁ = 4 -->
                    <line x1="-150" y1="-150" x2="150" y2="150" stroke="#ff5722" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="120" y="125" font-family="Arial" font-size="12" fill="#ff5722">Eigenspace for λ₁ = 4</text>
                    
                    <!-- Eigenspace for λ₂ = 2 -->
                    <line x1="-150" y1="150" x2="150" y2="-150" stroke="#2196f3" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="120" y="-115" font-family="Arial" font-size="12" fill="#2196f3">Eigenspace for λ₂ = 2</text>
                    
                    <!-- Eigenvector v₁ -->
                    <line x1="0" y1="0" x2="60" y2="60" stroke="#ff5722" stroke-width="4" marker-end="url(#eigenvecArrow)"/>
                    <text x="70" y="70" font-family="Arial" font-size="14" fill="#ff5722">v₁ = (1,1)</text>
                    
                    <!-- Eigenvector v₂ -->
                    <line x1="0" y1="0" x2="60" y2="-60" stroke="#2196f3" stroke-width="4" marker-end="url(#eigenvecArrow)"/>
                    <text x="70" y="-70" font-family="Arial" font-size="14" fill="#2196f3">v₂ = (1,-1)</text>
                    
                    <!-- Orthogonal indicator -->
                    <path d="M 20 20 L 20 0 L 20 -20" fill="none" stroke="#4caf50" stroke-width="3"/>
                    <text x="25" y="0" font-family="Arial" font-size="12" fill="#4caf50">90°</text>
                    
                    <!-- Matrix visualization -->
                    <g transform="translate(-150, -120)">
                        <rect x="-50" y="-30" width="100" height="60" fill="#f5f5f5" stroke="#667eea" stroke-width="2" rx="5"/>
                        <text x="0" y="-10" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">A =</text>
                        <text x="0" y="5" text-anchor="middle" font-family="serif" font-size="12" fill="#667eea">⎛ 3  1 ⎞</text>
                        <text x="0" y="18" text-anchor="middle" font-family="serif" font-size="12" fill="#667eea">⎝ 1  3 ⎠</text>
                    </g>
                </g>
                
                <!-- Result -->
                <text x="400" y="360" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
                    Result: P = (1/√2)⎛1  1⎞   and   P^TAP = ⎛4  0⎞
                </text>
                <text x="400" y="375" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
                    ⎝1 -1⎠              ⎝0  2⎠
                </text>
            </svg>

            <h3>Example 5.2: 3×3 Matrix with Repeated Eigenvalue</h3>
            
            <div class="example">
                <p><strong>Problem:</strong> Orthogonally diagonalize the matrix 
                $$A = \begin{pmatrix} 1 & 0 & 1 \\ 0 & 1 & 0 \\ 1 & 0 & 1 \end{pmatrix}$$</p>
                
                <div class="step-by-step">
                    <div class="step">
                        <span class="step-number">1</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find the eigenvalues</strong><br>
                            $$\det(A - \lambda I) = \det\begin{pmatrix} 1-\lambda & 0 & 1 \\ 0 & 1-\lambda & 0 \\ 1 & 0 & 1-\lambda \end{pmatrix}$$
                            $$= (1-\lambda)[(1-\lambda)^2 - 1] = (1-\lambda)(\lambda^2 - 2\lambda) = (1-\lambda)\lambda(\lambda-2)$$
                            So eigenvalues are: $\lambda_1 = 2$, $\lambda_2 = 1$, $\lambda_3 = 0$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">2</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find eigenvector for λ₁ = 2</strong><br>
                            Solve $(A - 2I)\mathbf{x} = \mathbf{0}$:
                            $$\begin{pmatrix} -1 & 0 & 1 \\ 0 & -1 & 0 \\ 1 & 0 & -1 \end{pmatrix}\begin{pmatrix} x_1 \\ x_2 \\ x_3 \end{pmatrix} = \begin{pmatrix} 0 \\ 0 \\ 0 \end{pmatrix}$$
                            This gives $x_3 = x_1$ and $x_2 = 0$. So $\mathbf{v}_1 = \begin{pmatrix} 1 \\ 0 \\ 1 \end{pmatrix}$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">3</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find eigenvector for λ₂ = 1</strong><br>
                            Solve $(A - I)\mathbf{x} = \mathbf{0}$:
                            $$\begin{pmatrix} 0 & 0 & 1 \\ 0 & 0 & 0 \\ 1 & 0 & 0 \end{pmatrix}\begin{pmatrix} x_1 \\ x_2 \\ x_3 \end{pmatrix} = \begin{pmatrix} 0 \\ 0 \\ 0 \end{pmatrix}$$
                            This gives $x_3 = 0$ and $x_1 = 0$, so $x_2$ is free. So $\mathbf{v}_2 = \begin{pmatrix} 0 \\ 1 \\ 0 \end{pmatrix}$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">4</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Find eigenvector for λ₃ = 0</strong><br>
                            Solve $A\mathbf{x} = \mathbf{0}$:
                            $$\begin{pmatrix} 1 & 0 & 1 \\ 0 & 1 & 0 \\ 1 & 0 & 1 \end{pmatrix}\begin{pmatrix} x_1 \\ x_2 \\ x_3 \end{pmatrix} = \begin{pmatrix} 0 \\ 0 \\ 0 \end{pmatrix}$$
                            This gives $x_1 + x_3 = 0$ and $x_2 = 0$, so $x_3 = -x_1$. So $\mathbf{v}_3 = \begin{pmatrix} 1 \\ 0 \\ -1 \end{pmatrix}$.
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">5</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Verify orthogonality and normalize</strong><br>
                            Check: $\mathbf{v}_1 \cdot \mathbf{v}_2 = 0$, $\mathbf{v}_1 \cdot \mathbf{v}_3 = 1 + 0 + (-1) = 0$, $\mathbf{v}_2 \cdot \mathbf{v}_3 = 0$ ✓<br>
                            Normalize:
                            $$\mathbf{u}_1 = \frac{1}{\sqrt{2}}\begin{pmatrix} 1 \\ 0 \\ 1 \end{pmatrix}, \quad \mathbf{u}_2 = \begin{pmatrix} 0 \\ 1 \\ 0 \end{pmatrix}, \quad \mathbf{u}_3 = \frac{1}{\sqrt{2}}\begin{pmatrix} 1 \\ 0 \\ -1 \end{pmatrix}$$
                        </div>
                    </div>
                    
                    <div class="step">
                        <span class="step-number">6</span>
                        <div style="display: inline-block; vertical-align: top; width: calc(100% - 50px);">
                            <strong>Form the result</strong><br>
                            $$P = \begin{pmatrix} \frac{1}{\sqrt{2}} & 0 & \frac{1}{\sqrt{2}} \\ 0 & 1 & 0 \\ \frac{1}{\sqrt{2}} & 0 & -\frac{1}{\sqrt{2}} \end{pmatrix}$$
                            
                            And $P^TAP = \begin{pmatrix} 2 & 0 & 0 \\ 0 & 1 & 0 \\ 0 & 0 & 0 \end{pmatrix}$.
                        </div>
                    </div>
                </div>
            </div>

                         <div class="note">
                 <p><strong>🔑 Key Observations:</strong></p>
                 <ul>
                     <li>The eigenvalues are automatically real (since $A$ is symmetric)</li>
                     <li>Eigenvectors from different eigenspaces are automatically orthogonal</li>
                     <li>Each eigenspace has dimension 1, so no Gram-Schmidt process was needed</li>
                     <li>The final orthogonal matrix $P$ has orthonormal columns</li>
                 </ul>
             </div>
        </div>

        <!-- Section 6: Applications to Quadratic Forms -->
        <div class="section">
            <h2>6. Applications to Quadratic Forms</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Understand the connection between symmetric matrices and quadratic forms</li>
                    <li>Learn about principal axes of quadratic forms</li>
                    <li>See geometric applications including conic sections and optimization</li>
                </ul>
            </div>

            <h3>Quadratic Forms</h3>
            
            <div class="definition">
                <p><strong>Quadratic Form:</strong> A quadratic form on $\mathbb{R}^n$ is a function $q: \mathbb{R}^n \to \mathbb{R}$ of the form:</p>
                $$q(\mathbf{x}) = \mathbf{x}^T A \mathbf{x}$$
                <p>where $A$ is an $n \times n$ symmetric matrix called the <span class="highlight">matrix of the quadratic form</span>.</p>
            </div>

            <div class="example">
                <p><strong>Example 6.1:</strong> Consider the quadratic form $q(x_1, x_2) = 3x_1^2 + 2x_1x_2 + 3x_2^2$.</p>
                
                <p>This can be written as $q(\mathbf{x}) = \mathbf{x}^T A \mathbf{x}$ where:</p>
                $$A = \begin{pmatrix} 3 & 1 \\ 1 & 3 \end{pmatrix}, \quad \mathbf{x} = \begin{pmatrix} x_1 \\ x_2 \end{pmatrix}$$
                
                <p>Let's verify:
                $$\mathbf{x}^T A \mathbf{x} = \begin{pmatrix} x_1 & x_2 \end{pmatrix} \begin{pmatrix} 3 & 1 \\ 1 & 3 \end{pmatrix} \begin{pmatrix} x_1 \\ x_2 \end{pmatrix} = \begin{pmatrix} x_1 & x_2 \end{pmatrix} \begin{pmatrix} 3x_1 + x_2 \\ x_1 + 3x_2 \end{pmatrix}$$
                $$= x_1(3x_1 + x_2) + x_2(x_1 + 3x_2) = 3x_1^2 + x_1x_2 + x_1x_2 + 3x_2^2 = 3x_1^2 + 2x_1x_2 + 3x_2^2$$</p>
            </div>

            <!-- SVG: Quadratic Form Visualization -->
            <svg width="800" height="400" viewBox="0 0 800 400">
                <defs>
                    <linearGradient id="quadGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffebee;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#ffcdd2;stop-opacity:0.8" />
                    </linearGradient>
                    <linearGradient id="quadGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:0.8" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    Quadratic Form: q(x₁,x₂) = 3x₁² + 2x₁x₂ + 3x₂²
                </text>
                
                <!-- Original coordinates -->
                <g transform="translate(200, 200)">
                    <text x="0" y="-140" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#667eea">Original Coordinates</text>
                    
                    <!-- Grid -->
                    <defs>
                        <pattern id="quadGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="-120" y="-120" width="240" height="240" fill="url(#quadGrid)"/>
                    
                    <!-- Axes -->
                    <line x1="-120" y1="0" x2="120" y2="0" stroke="#ddd" stroke-width="2"/>
                    <line x1="0" y1="-120" x2="0" y2="120" stroke="#ddd" stroke-width="2"/>
                    <text x="125" y="5" font-family="Arial" font-size="12" fill="#666">x₁</text>
                    <text x="5" y="-125" font-family="Arial" font-size="12" fill="#666">x₂</text>
                    
                    <!-- Level curves (tilted ellipses) -->
                    <ellipse cx="0" cy="0" rx="30" ry="20" fill="none" stroke="#ff5722" stroke-width="2" 
                             transform="rotate(45 0 0)" opacity="0.7"/>
                    <ellipse cx="0" cy="0" rx="50" ry="33" fill="none" stroke="#ff5722" stroke-width="2" 
                             transform="rotate(45 0 0)" opacity="0.7"/>
                    <ellipse cx="0" cy="0" rx="70" ry="47" fill="none" stroke="#ff5722" stroke-width="2" 
                             transform="rotate(45 0 0)" opacity="0.7"/>
                    
                    <!-- Quadratic form equation -->
                    <text x="0" y="140" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        Level curves: 3x₁² + 2x₁x₂ + 3x₂² = constant
                    </text>
                </g>
                
                <!-- Principal axes coordinates -->
                <g transform="translate(600, 200)">
                    <text x="0" y="-140" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#667eea">Principal Axes Coordinates</text>
                    
                    <!-- Grid -->
                    <rect x="-120" y="-120" width="240" height="240" fill="url(#quadGrid)"/>
                    
                    <!-- Principal axes -->
                    <line x1="-120" y1="0" x2="120" y2="0" stroke="#4caf50" stroke-width="3"/>
                    <line x1="0" y1="-120" x2="0" y2="120" stroke="#4caf50" stroke-width="3"/>
                    <text x="125" y="5" font-family="Arial" font-size="12" fill="#4caf50">y₁</text>
                    <text x="5" y="-125" font-family="Arial" font-size="12" fill="#4caf50">y₂</text>
                    
                    <!-- Level curves (aligned ellipses) -->
                    <ellipse cx="0" cy="0" rx="25" ry="35" fill="none" stroke="#2196f3" stroke-width="2" opacity="0.7"/>
                    <ellipse cx="0" cy="0" rx="42" ry="60" fill="none" stroke="#2196f3" stroke-width="2" opacity="0.7"/>
                    <ellipse cx="0" cy="0" rx="60" ry="85" fill="none" stroke="#2196f3" stroke-width="2" opacity="0.7"/>
                    
                    <!-- Simplified equation -->
                    <text x="0" y="140" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        Level curves: 4y₁² + 2y₂² = constant
                    </text>
                </g>
                
                <!-- Transformation arrow -->
                <path d="M 330 200 L 470 200" stroke="#667eea" stroke-width="4" marker-end="url(#transformArrow)"/>
                <text x="400" y="190" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Orthogonal</text>
                <text x="400" y="205" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Transformation</text>
                
                <defs>
                    <marker id="transformArrow" markerWidth="12" markerHeight="8" 
                            refX="11" refY="4" orient="auto">
                        <polygon points="0 0, 12 4, 0 8" fill="#667eea" />
                    </marker>
                </defs>
            </svg>

            <h3>Principal Axes Theorem for Quadratic Forms</h3>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.5 (Principal Axes Theorem for Quadratic Forms):</strong> Let $q(\mathbf{x}) = \mathbf{x}^T A \mathbf{x}$ be a quadratic form where $A$ is symmetric. Then there exists an orthogonal change of variables that transforms $q$ into a quadratic form with no cross terms:</p>
                $$q(\mathbf{x}) = \lambda_1 y_1^2 + \lambda_2 y_2^2 + \cdots + \lambda_n y_n^2$$
                <p>where $\lambda_1, \lambda_2, \ldots, \lambda_n$ are the eigenvalues of $A$, and the new variables $y_1, y_2, \ldots, y_n$ are coordinates with respect to the principal axes (orthonormal eigenvectors of $A$).</p>
            </div>

            <div class="proof">
                <p>Since $A$ is symmetric, by the Principal Axes Theorem (8.2.2), there exists an orthogonal matrix $P$ such that $P^TAP = D$ where $D = \text{diag}(\lambda_1, \lambda_2, \ldots, \lambda_n)$.</p>
                
                <p>Let $\mathbf{y} = P^T\mathbf{x}$ (orthogonal change of variables). Then $\mathbf{x} = P\mathbf{y}$ and:</p>
                $$q(\mathbf{x}) = \mathbf{x}^T A \mathbf{x} = (P\mathbf{y})^T A (P\mathbf{y}) = \mathbf{y}^T P^T A P \mathbf{y} = \mathbf{y}^T D \mathbf{y}$$
                $$= \lambda_1 y_1^2 + \lambda_2 y_2^2 + \cdots + \lambda_n y_n^2$$
            </div>

            <h3>Geometric Applications</h3>
            
            <div class="example">
                <p><strong>Example 6.2: Conic Section Analysis</strong></p>
                
                <p>Consider the conic section defined by $3x_1^2 + 2x_1x_2 + 3x_2^2 = 1$.</p>
                
                <p>From Example 5.1, we know that for $A = \begin{pmatrix} 3 & 1 \\ 1 & 3 \end{pmatrix}$, we have:</p>
                <ul>
                    <li>Eigenvalues: $\lambda_1 = 4$, $\lambda_2 = 2$</li>
                    <li>Orthogonal matrix: $P = \frac{1}{\sqrt{2}}\begin{pmatrix} 1 & 1 \\ 1 & -1 \end{pmatrix}$</li>
                </ul>
                
                <p>The change of variables $\mathbf{y} = P^T\mathbf{x}$ gives:</p>
                $$\begin{pmatrix} y_1 \\ y_2 \end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix} 1 & 1 \\ 1 & -1 \end{pmatrix}\begin{pmatrix} x_1 \\ x_2 \end{pmatrix} = \frac{1}{\sqrt{2}}\begin{pmatrix} x_1 + x_2 \\ x_1 - x_2 \end{pmatrix}$$
                
                <p>In the new coordinates, the conic becomes:</p>
                $$4y_1^2 + 2y_2^2 = 1 \quad \text{or} \quad \frac{y_1^2}{1/4} + \frac{y_2^2}{1/2} = 1$$
                
                <p>This is an <span class="highlight">ellipse</span> with semi-axes of lengths $\frac{1}{2}$ and $\frac{1}{\sqrt{2}}$.</p>
            </div>

            <!-- SVG: Conic Section Analysis -->
            <svg width="700" height="350" viewBox="0 0 700 350">
                <defs>
                    <linearGradient id="ellipseGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:0.3" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="350" y="25" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">
                    Conic Section: 3x₁² + 2x₁x₂ + 3x₂² = 1
                </text>
                
                <g transform="translate(350, 175)">
                    <!-- Grid -->
                    <defs>
                        <pattern id="conicGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f5f5f5" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="-250" y="-150" width="500" height="300" fill="url(#conicGrid)"/>
                    
                    <!-- Original coordinate axes -->
                    <line x1="-250" y1="0" x2="250" y2="0" stroke="#ddd" stroke-width="1"/>
                    <line x1="0" y1="-150" x2="0" y2="150" stroke="#ddd" stroke-width="1"/>
                    <text x="255" y="5" font-family="Arial" font-size="12" fill="#999">x₁</text>
                    <text x="5" y="-155" font-family="Arial" font-size="12" fill="#999">x₂</text>
                    
                    <!-- Principal axes (rotated 45°) -->
                    <line x1="-177" y1="-177" x2="177" y2="177" stroke="#4caf50" stroke-width="3" stroke-dasharray="8,4"/>
                    <line x1="-177" y1="177" x2="177" y2="-177" stroke="#4caf50" stroke-width="3" stroke-dasharray="8,4"/>
                    <text x="185" y="185" font-family="Arial" font-size="12" fill="#4caf50">y₁-axis</text>
                    <text x="185" y="-175" font-family="Arial" font-size="12" fill="#4caf50">y₂-axis</text>
                    
                    <!-- Ellipse (rotated) -->
                    <ellipse cx="0" cy="0" rx="50" ry="71" fill="url(#ellipseGrad)" stroke="#2196f3" stroke-width="3" 
                             transform="rotate(45 0 0)"/>
                    
                    <!-- Semi-axis labels -->
                    <g transform="rotate(45 0 0)">
                        <line x1="0" y1="0" x2="50" y2="0" stroke="#ff5722" stroke-width="2"/>
                        <line x1="0" y1="0" x2="0" y2="-71" stroke="#ff9800" stroke-width="2"/>
                        <text x="25" y="-8" text-anchor="middle" font-family="Arial" font-size="10" fill="#ff5722">1/2</text>
                        <text x="8" y="-35" font-family="Arial" font-size="10" fill="#ff9800">1/√2</text>
                    </g>
                    
                    <!-- Equation in new coordinates -->
                    <text x="0" y="130" text-anchor="middle" font-family="Arial" font-size="14" fill="#2196f3">
                        In principal axes: 4y₁² + 2y₂² = 1
                    </text>
                    
                    <!-- Eigenvalue annotations -->
                    <g transform="translate(-200, -120)">
                        <rect x="-60" y="-25" width="120" height="50" fill="#fff" stroke="#667eea" stroke-width="1" rx="5"/>
                        <text x="0" y="-5" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Eigenvalues:</text>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial" font-size="11" fill="#667eea">λ₁ = 4, λ₂ = 2</text>
                    </g>
                </g>
            </svg>

            <h3>Classification of Quadratic Forms</h3>
            
            <div class="definition">
                <p><strong>Classification by Eigenvalues:</strong> A quadratic form $q(\mathbf{x}) = \mathbf{x}^T A \mathbf{x}$ is:</p>
                <ul>
                    <li><strong>Positive definite</strong> if all eigenvalues of $A$ are positive</li>
                    <li><strong>Negative definite</strong> if all eigenvalues of $A$ are negative</li>
                    <li><strong>Indefinite</strong> if $A$ has both positive and negative eigenvalues</li>
                    <li><strong>Positive semidefinite</strong> if all eigenvalues are non-negative</li>
                    <li><strong>Negative semidefinite</strong> if all eigenvalues are non-positive</li>
                </ul>
            </div>

            <!-- SVG: Quadratic Form Classification -->
            <svg width="800" height="300" viewBox="0 0 800 300">
                <defs>
                    <linearGradient id="posDefGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="negDefGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ffcdd2;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="indefiniteGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ffe0b2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">
                    Classification of Quadratic Forms
                </text>
                
                <!-- Positive Definite -->
                <g transform="translate(150, 150)">
                    <rect x="-120" y="-60" width="240" height="120" fill="url(#posDefGrad)" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="0" y="-35" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#2e7d32">Positive Definite</text>
                    <text x="0" y="-15" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">All λᵢ > 0</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#2e7d32">Ellipsoid/Ellipse</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="11" fill="#2e7d32">q(x) ≥ 0, q(x) = 0 ⟺ x = 0</text>
                    <text x="0" y="35" text-anchor="middle" font-family="Arial" font-size="11" fill="#2e7d32">Example: x₁² + x₂²</text>
                </g>
                
                <!-- Negative Definite -->
                <g transform="translate(400, 150)">
                    <rect x="-120" y="-60" width="240" height="120" fill="url(#negDefGrad)" stroke="#f44336" stroke-width="2" rx="10"/>
                    <text x="0" y="-35" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#c62828">Negative Definite</text>
                    <text x="0" y="-15" text-anchor="middle" font-family="Arial" font-size="12" fill="#c62828">All λᵢ < 0</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#c62828">Ellipsoid/Ellipse</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="11" fill="#c62828">q(x) ≤ 0, q(x) = 0 ⟺ x = 0</text>
                    <text x="0" y="35" text-anchor="middle" font-family="Arial" font-size="11" fill="#c62828">Example: -x₁² - x₂²</text>
                </g>
                
                <!-- Indefinite -->
                <g transform="translate(650, 150)">
                    <rect x="-120" y="-60" width="240" height="120" fill="url(#indefiniteGrad)" stroke="#ff9800" stroke-width="2" rx="10"/>
                    <text x="0" y="-35" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#e65100">Indefinite</text>
                    <text x="0" y="-15" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">Mixed signs of λᵢ</text>
                    <text x="0" y="0" text-anchor="middle" font-family="Arial" font-size="12" fill="#e65100">Hyperbola/Hyperboloid</text>
                    <text x="0" y="15" text-anchor="middle" font-family="Arial" font-size="11" fill="#e65100">q(x) can be + or -</text>
                    <text x="0" y="35" text-anchor="middle" font-family="Arial" font-size="11" fill="#e65100">Example: x₁² - x₂²</text>
                </g>
            </svg>

            <h3>Optimization Applications</h3>
            
            <div class="note">
                <p><strong>🔑 Connection to Optimization:</strong> The principal axes theorem is crucial for understanding optimization problems involving quadratic functions:</p>
                <ul>
                    <li><strong>Constrained optimization:</strong> Finding extrema of quadratic forms subject to constraints</li>
                    <li><strong>Principal component analysis (PCA):</strong> Finding principal directions of variance in data</li>
                    <li><strong>Least squares:</strong> Understanding the geometry of the normal equations</li>
                    <li><strong>Stability analysis:</strong> Classifying equilibrium points in dynamical systems</li>
                </ul>
            </div>

                         <div class="warning-box">
                 <p><strong>⚡ Practical Note:</strong> When working with quadratic forms in applications:</p>
                 <ol>
                     <li>Always ensure the matrix is symmetric (if not, use $\frac{1}{2}(A + A^T)$)</li>
                     <li>The eigenvalues determine the shape and nature of level sets</li>
                     <li>Principal axes give the "natural" coordinate system for the problem</li>
                     <li>Numerical methods for large matrices use iterative algorithms to find principal components</li>
                 </ol>
             </div>
        </div>

        <!-- Section 7: Extensions and Advanced Topics -->
        <div class="section">
            <h2>7. Extensions and Advanced Topics</h2>
            
            <div class="info-box">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Learn about the Real Spectral Theorem and its generalizations</li>
                    <li>Understand the Triangulation Theorem for general matrices</li>
                    <li>See connections to functional analysis and infinite-dimensional spaces</li>
                </ul>
            </div>

            <h3>The Real Spectral Theorem</h3>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.6 (Real Spectral Theorem):</strong> Let $A$ be an $n \times n$ real symmetric matrix. Then:</p>
                <ol>
                    <li>All eigenvalues of $A$ are real</li>
                    <li>The dimension of each eigenspace equals the multiplicity of the eigenvalue</li>
                    <li>Eigenspaces corresponding to distinct eigenvalues are orthogonal</li>
                    <li>$\mathbb{R}^n$ has an orthonormal basis consisting of eigenvectors of $A$</li>
                </ol>
            </div>

            <div class="note">
                <p><strong>🔑 Historical Note:</strong> The spectral theorem is one of the most fundamental results in linear algebra and has far-reaching applications:</p>
                <ul>
                    <li><strong>Physics:</strong> Quantum mechanics (observables are represented by symmetric/Hermitian operators)</li>
                    <li><strong>Statistics:</strong> Principal Component Analysis and covariance matrices</li>
                    <li><strong>Engineering:</strong> Vibration analysis and signal processing</li>
                    <li><strong>Machine Learning:</strong> Dimensionality reduction and kernel methods</li>
                </ul>
            </div>

            <h3>Triangulation Theorem</h3>
            
            <p>While not every matrix can be orthogonally diagonalized, every matrix can be "triangulated" using orthogonal transformations:</p>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.7 (Real Triangulation Theorem):</strong> If $A$ is an $n \times n$ real matrix with $n$ real eigenvalues (counting multiplicities), then there exists an orthogonal matrix $P$ such that $P^TAP$ is upper triangular.</p>
            </div>

            <div class="proof">
                <p><strong>Proof outline:</strong> The proof proceeds by induction on $n$:</p>
                <ol>
                    <li><strong>Base case:</strong> For $n = 1$, any $1 \times 1$ matrix is already triangular</li>
                    <li><strong>Inductive step:</strong> Assume the result holds for $(n-1) \times (n-1)$ matrices</li>
                    <li>Let $\lambda$ be an eigenvalue of $A$ with unit eigenvector $\mathbf{u}_1$</li>
                    <li>Extend $\{\mathbf{u}_1\}$ to an orthonormal basis $\{\mathbf{u}_1, \mathbf{u}_2, \ldots, \mathbf{u}_n\}$</li>
                    <li>Form $Q = [\mathbf{u}_1 \; \mathbf{u}_2 \; \cdots \; \mathbf{u}_n]$, then $Q^TAQ$ has the form:
                    $$Q^TAQ = \begin{pmatrix} \lambda & \mathbf{b}^T \\ \mathbf{0} & A_1 \end{pmatrix}$$</li>
                    <li>Apply the inductive hypothesis to $A_1$ to complete the triangulation</li>
                </ol>
            </div>

            <!-- SVG: Triangulation Process -->
            <svg width="800" height="350" viewBox="0 0 800 350">
                <defs>
                    <linearGradient id="triangGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="upperTriGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    Triangulation Theorem: Any Matrix Can Be Triangulated
                </text>
                
                <!-- Original Matrix -->
                <g transform="translate(150, 175)">
                    <text x="0" y="-80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#667eea">Original Matrix A</text>
                    
                    <!-- Matrix visualization -->
                    <rect x="-60" y="-60" width="120" height="120" fill="url(#triangGrad)" stroke="#667eea" stroke-width="3" rx="5"/>
                    
                    <!-- Matrix elements (filled) -->
                    <g fill="#1976d2">
                        <circle cx="-30" cy="-30" r="6"/>
                        <circle cx="0" cy="-30" r="6"/>
                        <circle cx="30" cy="-30" r="6"/>
                        <circle cx="-30" cy="0" r="6"/>
                        <circle cx="0" cy="0" r="6"/>
                        <circle cx="30" cy="0" r="6"/>
                        <circle cx="-30" cy="30" r="6"/>
                        <circle cx="0" cy="30" r="6"/>
                        <circle cx="30" cy="30" r="6"/>
                    </g>
                    
                    <text x="0" y="90" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        General matrix with
                    </text>
                    <text x="0" y="105" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        real eigenvalues
                    </text>
                </g>
                
                <!-- Transformation arrow -->
                <path d="M 220 175 L 380 175" stroke="#667eea" stroke-width="4" marker-end="url(#triangArrow)"/>
                <text x="300" y="165" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Orthogonal</text>
                <text x="300" y="180" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Similarity</text>
                <text x="300" y="195" text-anchor="middle" font-family="serif" font-size="12" fill="#667eea">P^TAP</text>
                
                <!-- Upper Triangular Matrix -->
                <g transform="translate(450, 175)">
                    <text x="0" y="-80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#4caf50">Upper Triangular</text>
                    
                    <!-- Matrix visualization -->
                    <rect x="-60" y="-60" width="120" height="120" fill="url(#upperTriGrad)" stroke="#4caf50" stroke-width="3" rx="5"/>
                    
                    <!-- Upper triangular elements (filled) -->
                    <g fill="#2e7d32">
                        <circle cx="-30" cy="-30" r="6"/>
                        <circle cx="0" cy="-30" r="6"/>
                        <circle cx="30" cy="-30" r="6"/>
                        <circle cx="0" cy="0" r="6"/>
                        <circle cx="30" cy="0" r="6"/>
                        <circle cx="30" cy="30" r="6"/>
                    </g>
                    
                    <!-- Lower triangular elements (zeros) -->
                    <g fill="#c8e6c9">
                        <circle cx="-30" cy="0" r="4"/>
                        <circle cx="-30" cy="30" r="4"/>
                        <circle cx="0" cy="30" r="4"/>
                    </g>
                    
                    <!-- Zero labels -->
                    <text x="-30" y="5" text-anchor="middle" font-family="Arial" font-size="8" fill="#666">0</text>
                    <text x="-30" y="35" text-anchor="middle" font-family="Arial" font-size="8" fill="#666">0</text>
                    <text x="0" y="35" text-anchor="middle" font-family="Arial" font-size="8" fill="#666">0</text>
                    
                    <text x="0" y="90" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        Upper triangular
                    </text>
                    <text x="0" y="105" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        form
                    </text>
                </g>
                
                <!-- Orthogonal Matrix P -->
                <g transform="translate(650, 175)">
                    <text x="0" y="-80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#ff9800">Orthogonal Matrix P</text>
                    
                    <!-- Matrix visualization -->
                    <rect x="-50" y="-50" width="100" height="100" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                    
                    <!-- Orthogonal property indicators -->
                    <g stroke="#ff9800" stroke-width="2" fill="none">
                        <line x1="-30" y1="-30" x2="-30" y2="30"/>
                        <line x1="0" y1="-30" x2="0" y2="30"/>
                        <line x1="30" y1="-30" x2="30" y2="30"/>
                    </g>
                    
                    <text x="0" y="0" text-anchor="middle" font-family="serif" font-size="14" fill="#ff9800">P</text>
                    <text x="0" y="75" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                        P^T P = I
                    </text>
                </g>
                
                <defs>
                    <marker id="triangArrow" markerWidth="12" markerHeight="8" 
                            refX="11" refY="4" orient="auto">
                        <polygon points="0 0, 12 4, 0 8" fill="#667eea" />
                    </marker>
                </defs>
                
                <!-- Key insight -->
                <text x="400" y="310" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#667eea">
                    Key: Every matrix with real eigenvalues can be triangulated orthogonally
                </text>
            </svg>

            <h3>Complex Spectral Theorem</h3>
            
            <div class="theorem">
                <p><strong>Theorem 8.2.8 (Complex Spectral Theorem):</strong> Let $A$ be an $n \times n$ complex matrix. Then $A$ is unitarily diagonalizable (i.e., there exists a unitary matrix $U$ such that $U^*AU$ is diagonal) if and only if $A$ is normal (i.e., $AA^* = A^*A$).</p>
            </div>

            <div class="note">
                <p><strong>🔑 Generalizations:</strong></p>
                <ul>
                    <li><strong>Hermitian matrices:</strong> Complex analog of symmetric matrices ($A^* = A$)</li>
                    <li><strong>Unitary matrices:</strong> Complex analog of orthogonal matrices ($U^*U = I$)</li>
                    <li><strong>Normal matrices:</strong> Matrices that commute with their conjugate transpose</li>
                    <li><strong>Skew-Hermitian matrices:</strong> Satisfy $A^* = -A$</li>
                </ul>
            </div>

            <h3>Infinite-Dimensional Extensions</h3>
            
            <p>The spectral theorem extends beautifully to infinite-dimensional spaces:</p>
            
            <div class="theorem">
                <p><strong>Functional Analysis Spectral Theorem:</strong> Let $H$ be a Hilbert space and $T: H \to H$ be a compact self-adjoint operator. Then there exists an orthonormal basis of $H$ consisting of eigenvectors of $T$.</p>
            </div>

            <div class="example">
                <p><strong>Example 7.1: Fourier Series</strong></p>
                
                <p>Consider the space $L^2[0, 2\pi]$ of square-integrable functions on $[0, 2\pi]$. The functions:</p>
                $$\left\{\frac{1}{\sqrt{2\pi}}, \frac{\cos(nx)}{\sqrt{\pi}}, \frac{\sin(nx)}{\sqrt{\pi}} : n = 1, 2, 3, \ldots\right\}$$
                
                <p>form an orthonormal basis for this space. This is the infinite-dimensional analog of having an orthonormal basis of eigenvectors!</p>
            </div>

            <!-- SVG: Infinite-Dimensional Visualization -->
            <svg width="700" height="300" viewBox="0 0 700 300">
                <defs>
                    <linearGradient id="fourierGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e8eaf6;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#c5cae9;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Title -->
                <text x="350" y="25" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#667eea">
                    Infinite-Dimensional Spectral Theory: Fourier Basis
                </text>
                
                <g transform="translate(350, 150)">
                    <!-- Background -->
                    <rect x="-300" y="-100" width="600" height="200" fill="url(#fourierGrad)" stroke="#667eea" stroke-width="2" rx="10"/>
                    
                    <!-- Fourier basis functions -->
                    <g transform="translate(-250, 0)">
                        <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Constant</text>
                        <!-- Constant function -->
                        <line x1="-40" y1="0" x2="40" y2="0" stroke="#ff5722" stroke-width="3"/>
                        <text x="0" y="20" text-anchor="middle" font-family="serif" font-size="10" fill="#666">1/√(2π)</text>
                    </g>
                    
                    <g transform="translate(-100, 0)">
                        <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">cos(x)</text>
                        <!-- Cosine function -->
                        <path d="M -40 0 Q -20 -30 0 0 Q 20 30 40 0" stroke="#2196f3" stroke-width="3" fill="none"/>
                        <text x="0" y="20" text-anchor="middle" font-family="serif" font-size="10" fill="#666">cos(x)/√π</text>
                    </g>
                    
                    <g transform="translate(50, 0)">
                        <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">sin(x)</text>
                        <!-- Sine function -->
                        <path d="M -40 0 Q -20 30 0 0 Q 20 -30 40 0" stroke="#4caf50" stroke-width="3" fill="none"/>
                        <text x="0" y="20" text-anchor="middle" font-family="serif" font-size="10" fill="#666">sin(x)/√π</text>
                    </g>
                    
                    <g transform="translate(200, 0)">
                        <text x="0" y="-70" text-anchor="middle" font-family="Arial" font-size="12" fill="#667eea">Higher modes</text>
                        <!-- Higher frequency functions -->
                        <path d="M -40 0 Q -30 -20 -20 0 Q -10 20 0 0 Q 10 -20 20 0 Q 30 20 40 0" stroke="#9c27b0" stroke-width="3" fill="none"/>
                        <text x="0" y="20" text-anchor="middle" font-family="serif" font-size="10" fill="#666">cos(nx), sin(nx)</text>
                    </g>
                    
                    <!-- Orthogonality indication -->
                    <text x="0" y="60" text-anchor="middle" font-family="Arial" font-size="14" fill="#667eea">
                        Orthonormal Basis: ∫₀²π f(x)g(x)dx = δ_{fg}
                    </text>
                    
                    <!-- Infinite extension -->
                    <text x="250" y="0" text-anchor="middle" font-family="Arial" font-size="20" fill="#667eea">...</text>
                    <text x="270" y="20" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">∞</text>
                </g>
                
                <!-- Connection to finite case -->
                <text x="350" y="270" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">
                    Just like finite-dimensional eigenvector bases, but with infinitely many basis functions!
                </text>
            </svg>

            <h3>Modern Applications and Extensions</h3>
            
            <div class="note">
                <p><strong>🚀 Contemporary Developments:</strong></p>
                <ul>
                    <li><strong>Random Matrix Theory:</strong> Studying eigenvalue distributions of large random matrices</li>
                    <li><strong>Tensor Decompositions:</strong> Generalizing eigendecomposition to higher-order tensors</li>
                    <li><strong>Matrix Manifolds:</strong> Studying matrices as points on geometric manifolds</li>
                    <li><strong>Quantum Information:</strong> Using spectral theory for quantum state analysis</li>
                    <li><strong>Machine Learning:</strong> Spectral clustering, kernel PCA, and graph neural networks</li>
                </ul>
            </div>

            <div class="info-box">
                <p><strong>🎓 Further Study:</strong> The orthogonal diagonalization concepts you've learned form the foundation for advanced topics in:</p>
                <ul>
                    <li><strong>Functional Analysis:</strong> Spectral theory of operators on infinite-dimensional spaces</li>
                    <li><strong>Differential Geometry:</strong> Spectral geometry and eigenvalue problems on manifolds</li>
                    <li><strong>Numerical Analysis:</strong> Iterative methods for large-scale eigenvalue problems</li>
                    <li><strong>Physics:</strong> Quantum mechanics and statistical mechanics</li>
                    <li><strong>Data Science:</strong> Dimensionality reduction and manifold learning</li>
                </ul>
            </div>

            <div class="warning-box">
                <p><strong>🎯 Key Takeaways:</strong></p>
                <ol>
                    <li><strong>Symmetric matrices are special:</strong> They're exactly the matrices that can be orthogonally diagonalized</li>
                    <li><strong>Principal axes provide insight:</strong> Natural coordinate systems reveal the underlying structure</li>
                    <li><strong>Applications are everywhere:</strong> From conic sections to quantum mechanics to machine learning</li>
                    <li><strong>The theory extends beautifully:</strong> From finite to infinite dimensions, from real to complex numbers</li>
                </ol>
            </div>

            <!-- Final Summary SVG -->
            <svg width="800" height="200" viewBox="0 0 800 200">
                <defs>
                    <linearGradient id="summaryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9ff;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e8eaf6;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Background -->
                <rect x="50" y="20" width="700" height="160" fill="url(#summaryGrad)" stroke="#667eea" stroke-width="3" rx="15"/>
                
                <!-- Title -->
                <text x="400" y="50" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#667eea">
                    The Beauty of Orthogonal Diagonalization
                </text>
                
                <!-- Central concept -->
                <circle cx="400" cy="100" r="40" fill="#667eea" opacity="0.2"/>
                <text x="400" y="105" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#667eea">
                    Symmetric Matrix
                </text>
                
                <!-- Connected concepts -->
                <g fill="#4caf50" font-family="Arial" font-size="12">
                    <circle cx="200" cy="80" r="5"/>
                    <text x="210" y="85">Real Eigenvalues</text>
                    
                    <circle cx="200" cy="120" r="5"/>
                    <text x="210" y="125">Orthogonal Eigenvectors</text>
                    
                    <circle cx="600" cy="80" r="5"/>
                    <text x="520" y="85">Quadratic Forms</text>
                    
                    <circle cx="600" cy="120" r="5"/>
                    <text x="520" y="125">Principal Axes</text>
                </g>
                
                <!-- Connecting lines -->
                <g stroke="#667eea" stroke-width="2" opacity="0.5">
                    <line x1="360" y1="85" x2="220" y2="80"/>
                    <line x1="360" y1="115" x2="220" y2="120"/>
                    <line x1="440" y1="85" x2="580" y2="80"/>
                    <line x1="440" y1="115" x2="580" y2="120"/>
                </g>
                
                <!-- Bottom message -->
                <text x="400" y="160" text-anchor="middle" font-family="Arial" font-size="14" font-style="italic" fill="#667eea">
                    "The most beautiful mathematics is often the most useful"
                </text>
            </svg>
        </div>
    </div>
</body>
</html> 