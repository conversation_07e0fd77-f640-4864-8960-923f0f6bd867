Skip to main content
Library homepage

chrome_reader_mode
Enter Reader Mode
LibreOne Launchpad
Search 
How can we help you?
 Search
Sign in
Contents Home   Campus Bookshelves   Monroe Community College   MTH 211 Calculus II   Chapter 5: Integration   5.1: Approximating Areas Expand/collapse global location
5.1: Approximating Areas
CCBYNCSA

picture_as_pdf
Downloads
Buy Print Copy
Submit Adoption Report
Peer Review
Donate
 
OpenStax
OpenStax
Learning Objectives
Use sigma (summation) notation to calculate sums and powers of integers.
Use the sum of rectangular areas to approximate the area under a curve.
Use R<PERSON>mann sums to approximate area.
<PERSON><PERSON><PERSON> was fascinated with calculating the areas of various shapes—in other words, the amount of space enclosed by the shape. He used a process that has come to be known as the method of exhaustion, which used smaller and smaller shapes, the areas of which could be calculated exactly, to fill an irregular region and thereby obtain closer and closer approximations to the total area. In this process, an area bounded by curves is filled with rectangles, triangles, and shapes with exact area formulas. These areas are then summed to approximate the area of the curved region.

In this section, we develop techniques to approximate the area between a curve, defined by a function f(x),
 and the x-axis on a closed interval [a,b].
 Like <PERSON><PERSON><PERSON>, we first approximate the area under the curve using shapes of known area (namely, rectangles). By using smaller and smaller rectangles, we get closer and closer approximations to the area. Taking a limit allows us to calculate the exact area under the curve.

Let’s start by introducing some notation to make the calculations easier. We then consider the case when f(x)
 is continuous and nonnegative. Later in the chapter, we relax some of these restrictions and develop techniques that apply in more general cases.

Sigma (Summation) Notation
As mentioned, we will use shapes of known area to approximate the area of an irregular region bounded by curves. This process often requires adding up long strings of numbers. To make it easier to write down these lengthy sums, we look at some new notation here, called sigma notation (also known as summation notation). The Greek capital letter Σ
, sigma, is used to express long sums of values in a compact form. For example, if we want to add all the integers from 1 to 20 without sigma notation, we have to write

1+2+3+4+5+6+7+8+9+10+11+12+13+14+15+16+17+18+19+20.

We could probably skip writing a couple of terms and write

1+2+3+4+⋯+19+20,

which is better, but still cumbersome. With sigma notation, we write this sum as

∑i=120i

which is much more compact. Typically, sigma notation is presented in the form

∑i=1nai

where ai
 describes the terms to be added, and the i
 is called the index
. Each term is evaluated, then we sum all the values, beginning with the value when i=1
 and ending with the value when i=n.
 For example, an expression like ∑i=27si
 is interpreted as s2+s3+s4+s5+s6+s7
. Note that the index is used only to keep track of the terms to be added; it does not factor into the calculation of the sum itself. The index is therefore called a dummy variable. We can use any letter we like for the index. Typically, mathematicians use i,j,k,m
, and n
 for indices.

Let’s try a couple of examples of using sigma notation.

Example 5.1.1
: Using Sigma Notation
Write in sigma notation and evaluate the sum of terms 3i
 for i=1,2,3,4,5.
Write the sum in sigma notation:
1+14+19+116+125.

Solution
Write
∑i=153i=3+32+33+34+35=363.
The denominator of each term is a perfect square. Using sigma notation, this sum can be written as ∑i=151i2
.
Exercise 5.1.1
Write in sigma notation and evaluate the sum of terms 2i
 for i=3,4,5,6.

Hint
Answer
The properties associated with the summation process are given in the following rule.

Rule: Properties of Sigma Notation
Let a1,a2,…,an
 and b1,b2,…,bn
 represent two sequences of terms and let c
 be a constant. The following properties hold for all positive integers n
 and for integers m
, with 1≤m≤n.

∑i=1nc=nc
∑i=1ncai=c∑i=1nai
∑i=1n(ai+bi)=∑i=1nai+∑i=1nbi
∑i=1n(ai−bi)=∑i=1nai−∑i=1nbi
∑i=1nai=∑i=1mai+∑i=m+1nai
Proof
We prove properties (ii.) and (iii.) here, and leave proof of the other properties to the Exercises.

(ii.) We have

∑i=1ncai=ca1+ca2+ca3+⋯+can=c(a1+a2+a3+⋯+an)=c∑i=1nai.

(iii.) We have

∑i=1n(ai+bi)=(a1+b1)+(a2+b2)+(a3+b3)+⋯+(an+bn)=(a1+a2+a3+⋯+an)+(b1+b2+b3+⋯+bn)=∑i=1nai+∑i=1nbi.(5.1.1)(5.1.2)(5.1.3)

□

A few more formulas for frequently found functions simplify the summation process further. These are shown in the next rule, for sums and powers of integers, and we use them in the next set of examples.

Rule: Sums and Powers of Integers
1. The sum of n
 integers is given by

∑i=1ni=1+2+⋯+n=n(n+1)2.(5.1.4)

2. The sum of consecutive integers squared is given by

∑i=1ni2=12+22+⋯+n2=n(n+1)(2n+1)6.(5.1.5)

3. The sum of consecutive integers cubed is given by

∑i=1ni3=13+23+⋯+n3=n2(n+1)24.(5.1.6)

Example 5.1.2
: Evaluation Using Sigma Notation
Write using sigma notation and evaluate:

The sum of the terms (i−3)2
 for i=1,2,…,200.
The sum of the terms (i3−i2)
 for i=1,2,3,4,5,6
Solution
a. Multiplying out (i−3)2
, we can break the expression into three terms.

∑i=1200(i−3)2=∑i=1200(i2−6i+9)=∑i=1200i2−∑i=12006i+∑i=12009=∑i=1200i2−6∑i=1200i+∑i=12009=200(200+1)(400+1)6−6[200(200+1)2]+9(200)=2,686,700−120,600+1800=2,567,900

b. Use sigma notation property iv. and the rules for the sum of squared terms and the sum of cubed terms.

∑i=16(i3−i2)=∑i=16i3−∑i=16i2=62(6+1)24−6(6+1)(2(6)+1)6=17644−5466=350

Exercise 5.1.2
Find the sum of the values of 4+3i
 for i=1,2,…,100.

Hint
Answer
Example 5.1.3
: Finding the Sum of the Function Values
Find the sum of the values of f(x)=x3
 over the integers 1,2,3,…,10.

Solution
Using Equation 5.1.6
, we have

∑i=010i3=(10)2(10+1)24=100(121)4=3025

Exercise 5.1.3
Evaluate the sum indicated by the notation ∑k=120(2k+1)
.

Hint
Answer
Approximating Area
Now that we have the necessary notation, we return to the problem at hand: approximating the area under a curve. Let f(x)
 be a continuous, nonnegative function defined on the closed interval [a,b]
. We want to approximate the area A
 bounded by f(x)
 above, the x
-axis below, the line x=a
 on the left, and the line x=b
 on the right (Figure 5.1.1
).

A graph in quadrant one of an area bounded by a generic curve f(x) at the top, the x-axis at the bottom, the line x = a to the left, and the line x = b to the right. About midway through, the concavity switches from concave down to concave up, and the function starts to increases shortly before the line x = b.
Figure 5.1.1
: An area (shaded region) bounded by the curve f(x)
 at top, the x
-axis at bottom, the line x=a
 to the left, and the line x=b
 at right.
How do we approximate the area under this curve? The approach is a geometric one. By dividing a region into many small shapes that have known area formulas, we can sum these areas and obtain a reasonable estimate of the true area. We begin by dividing the interval [a,b]
 into n
 subintervals of equal width, b−an
. We do this by selecting equally spaced points x0,x1,x2,…,xn
 with x0=a,xn=b,
 and

xi−xi−1=b−an

for i=1,2,3,…,n.

We denote the width of each subinterval with the notation Δx,
 so Δx=b−an
 and

xi=x0+iΔx

for i=1,2,3,…,n.
 This notion of dividing an interval [a,b]
 into subintervals by selecting points from within the interval is used quite often in approximating the area under a curve, so let’s define some relevant terminology.

Definition: Partitions
A set of points P=xi
 for i=0,1,2,…,n
 with a=x0<x1<x2<...<xn=b
, which divides the interval [a,b]
 into subintervals of the form [x0,x1],[x1,x2],...,[xn−1,xn]
 is called a partition of [a,b]
. If the subintervals all have the same width, the set of points forms a regular partition (or uniform partition) of the interval [a,b].

We can use this regular partition as the basis of a method for estimating the area under the curve. We next examine two methods: the left-endpoint approximation and the right-endpoint approximation.

Rule: Left-Endpoint Approximation
On each subinterval [xi−1,xi]
 (for i=1,2,3,…,n
), construct a rectangle with width Δx
 and height equal to f(xi−1)
, which is the function value at the left endpoint of the subinterval. Then the area of this rectangle is f(xi−1)Δx
. Adding the areas of all these rectangles, we get an approximate value for A
 (Figure 5.1.2
). We use the notation Ln
 to denote that this is a left-endpoint approximation of A
 using n
 subintervals.

A≈Ln=f(x0)Δx+f(x1)Δx+⋯+f(xn−1)Δx=∑i=1nf(xi−1)Δx

A diagram showing the left-endpoint approximation of area under a curve. Under a parabola with vertex on the y axis and above the x axis, rectangles are drawn between a=x0 on the origin and b = xn. The rectangles have endpoints at a=x0, x1, x2…x(n-1), and b = xn, spaced equally. The height of each rectangle is determined by the value of the given function at the left endpoint of the rectangle.
Figure 5.1.2
: In the left-endpoint approximation of area under a curve, the height of each rectangle is determined by the function value at the left of each subinterval.
The second method for approximating area under a curve is the right-endpoint approximation. It is almost the same as the left-endpoint approximation, but now the heights of the rectangles are determined by the function values at the right of each subinterval.

Rule: Right-Endpoint Approximation
Construct a rectangle on each subinterval [xi−1,xi]
, only this time the height of the rectangle is determined by the function value f(xi)
 at the right endpoint of the subinterval. Then, the area of each rectangle is f(xi)Δx
 and the approximation for A
 is given by

A≈Rn=f(x1)Δx+f(x2)Δx+⋯+f(xn)Δx=∑i=1nf(xi)Δx.

The notation Rn
 indicates this is a right-endpoint approximation for A
 (Figure 5.1.3
).

A diagram showing the right-endpoint approximation of area under a curve. Under a parabola with vertex on the y axis and above the x axis, rectangles are drawn between a=x0 on the origin and b = xn. The rectangles have endpoints at a=x0, x1, x2…x(n-1), and b = xn, spaced equally. The height of each rectangle is determined by the value of the given function at the right endpoint of the rectangle.
Figure 5.1.3
: In the right-endpoint approximation of area under a curve, the height of each rectangle is determined by the function value at the right of each subinterval. Note that the right-endpoint approximation differs from the left-endpoint approximation in Figure 5.1.2
.
The graphs in Figure 5.1.4
 represent the curve f(x)=x22
. In Figure 5.1.4b
 we divide the region represented by the interval [0,3]
 into six subintervals, each of width 0.5
. Thus, Δx=0.5
. We then form six rectangles by drawing vertical lines perpendicular to xi−1
, the left endpoint of each subinterval. We determine the height of each rectangle by calculating f(xi−1)
 for i=1,2,3,4,5,6.
 The intervals are [0,0.5],[0.5,1],[1,1.5],[1.5,2],[2,2.5],[2.5,3]
. We find the area of each rectangle by multiplying the height by the width. Then, the sum of the rectangular areas approximates the area between f(x)
 and the x
-axis. When the left endpoints are used to calculate height, we have a left-endpoint approximation. Thus,

A≈L6=∑i=16f(xi−1)Δx=f(x0)Δx+f(x1)Δx+f(x2)Δx+f(x3)Δx+f(x4)Δx+f(x5)Δx=f(0)0.5+f(0.5)0.5+f(1)0.5+f(1.5)0.5+f(2)0.5+f(2.5)0.5=(0)0.5+(0.125)0.5+(0.5)0.5+(1.125)0.5+(2)0.5+(3.125)0.5=0+0.0625+0.25+0.5625+1+1.5625=3.4375units2

Diagrams side by side, showing the differences in approximating the area under a parabolic curve with vertex at the origin between the left endpoints method (the first diagram) and the right endpoints method (the second diagram). In the first diagram, rectangles are drawn at even intervals (delta x) under the curve with heights determined by the value of the function at the left endpoints. In the second diagram, the rectangles are drawn in the same fashion, but with heights determined by the value of the function at the right endpoints. The endpoints in both are spaced equally from the origin to (3, 0), labeled x0 to x6.
Figure 5.1.4
: Methods of approximating the area under a curve by using (a) the left endpoints and (b) the right endpoints.
In Figure 5.1.4b
, we draw vertical lines perpendicular to xi
 such that xi
 is the right endpoint of each subinterval, and calculate f(xi)
 for i=1,2,3,4,5,6
. We multiply each f(xi)
 by Δx
 to find the rectangular areas, and then add them. This is a right-endpoint approximation of the area under f(x)
. Thus,

A≈R6=∑i=16f(xi)Δx=f(x1)Δx+f(x2)Δx+f(x3)Δx+f(x4)Δx+f(x5)Δx+f(x6)Δx=f(0.5)0.5+f(1)0.5+f(1.5)0.5+f(2)0.5+f(2.5)0.5+f(3)0.5=(0.125)0.5+(0.5)0.5+(1.125)0.5+(2)0.5+(3.125)0.5+(4.5)0.5=0.0625+0.25+0.5625+1+1.5625+2.25=5.6875units2.

Example 5.1.4
: Approximating the Area Under a Curve
Use both left-endpoint and right-endpoint approximations to approximate the area under the curve of f(x)=x2
 on the interval [0,2]
; use n=4
.

Solution
First, divide the interval [0,2]
 into n
 equal subintervals. Using n=4,Δx=(2−0)4=0.5
. This is the width of each rectangle. The intervals [0,0.5],[0.5,1],[1,1.5],[1.5,2]
 are shown in Figure 5.1.5
. Using a left-endpoint approximation, the heights are f(0)=0,f(0.5)=0.25,f(1)=1,
 and f(1.5)=2.25.
 Then,

L4=f(x0)Δx+f(x1)Δx+f(x2)Δx+f(x3)Δx=0(0.5)+0.25(0.5)+1(0.5)+2.25(0.5)=1.75units2

A graph of the left-endpoint approximation of the area under the curve f(x) = x^2 from 0 to 2 with endpoints spaced .5 units apart. The heights of the rectangle are determined by the values of the function at their left endpoints.
Figure 5.1.5
: The graph shows the left-endpoint approximation of the area under f(x)=x2
 from 0
 to 2
.
The right-endpoint approximation is shown in Figure 5.1.6
. The intervals are the same, Δx=0.5,
 but now use the right endpoint to calculate the height of the rectangles. We have

R4=f(x1)Δx+f(x2)Δx+f(x3)Δx+f(x4)Δx=0.25(0.5)+1(0.5)+2.25(0.5)+4(0.5)=3.75units2

A graph of the right-endpoint approximation method of the area under the curve f(x) = x^2 from 0 to 2 with endpoints spaced .5 units apart. The heights of the rectangles are determined by the values of the function at the right endpoints.
Figure 5.1.6
: The graph shows the right-endpoint approximation of the area under f(x)=x2
 from 0
 to 2
.
The left-endpoint approximation is 1.75units2
; the right-endpoint approximation is 3.75units2
.

Exercise 5.1.4
Sketch left-endpoint and right-endpoint approximations for f(x)=1x
 on [1,2]
; use n=4
. Approximate the area using both methods.

Hint
Answer
Looking at Figure 5.1.4
 and the graphs in Example 5.1.4
, we can see that when we use a small number of intervals, neither the left-endpoint approximation nor the right-endpoint approximation is a particularly accurate estimate of the area under the curve. However, it seems logical that if we increase the number of points in our partition, our estimate of A
 will improve. We will have more rectangles, but each rectangle will be thinner, so we will be able to fit the rectangles to the curve more precisely.

We can demonstrate the improved approximation obtained through smaller intervals with an example. Let’s explore the idea of increasing n
, first in a left-endpoint approximation with four rectangles, then eight rectangles, and finally 32
 rectangles. Then, let’s do the same thing in a right-endpoint approximation, using the same sets of intervals, of the same curved region. Figure 5.1.7
 shows the area of the region under the curve f(x)=(x−1)3+4
 on the interval [0,2]
 using a left-endpoint approximation where n=4.
 The width of each rectangle is

Δx=2−04=12.

The area is approximated by the summed areas of the rectangles, or

L4=f(0)(0.5)+f(0.5)(0.5)+f(1)(0.5)+f(1.5)0.5=7.5units2

A graph of the left-endpoint approximation of the area under the given curve from a = x0 to b=x4. The heights of the rectangles are determined by the values of the function at the left endpoints.
Figure 5.1.7
: With a left-endpoint approximation and dividing the region from a
 to b
 into four equal intervals, the area under the curve is approximately equal to the sum of the areas of the rectangles.
Figure 5.1.8
 shows the same curve divided into eight subintervals. Comparing the graph with four rectangles in Figure 5.1.7
 with this graph with eight rectangles, we can see there appears to be less white space under the curve when n=8.
 This white space is area under the curve we are unable to include using our approximation. The area of the rectangles is

L8=f(0)(0.25)+f(0.25)(0.25)+f(0.5)(0.25)+f(0.75)(0.25)+f(1)(0.25)+f(1.25)(0.25)+f(1.5)(0.25)+f(1.75)(0.25)=7.75units2

A graph showing the left-endpoint approximation for the area under the given curve from a=x0 to b = x8. The heights of the rectangles are determined by the values of the function at the left endpoints.
Figure 5.1.8
: The region under the curve is divided into n=8
 rectangular areas of equal width for a left-endpoint approximation.
The graph in Figure 5.1.9
 shows the same function with 32
 rectangles inscribed under the curve. There appears to be little white space left. The area occupied by the rectangles is

L32=f(0)(0.0625)+f(0.0625)(0.0625)+f(0.125)(0.0625)+⋯+f(1.9375)(0.0625)=7.9375units2.

A graph of the left-endpoint approximation of the area under the given curve from a = x0 to b = x32. The heights of the rectangles are determined by the values of the function at the left endpoints.
Figure 5.1.9
: Here, 32
 rectangles are inscribed under the curve for a left-endpoint approximation.
We can carry out a similar process for the right-endpoint approximation method. A right-endpoint approximation of the same curve, using four rectangles (Figure 5.1.10
), yields an area

R4=f(0.5)(0.5)+f(1)(0.5)+f(1.5)(0.5)+f(2)(0.5)=8.5units2.

A graph of the right-endpoint approximation for the area under the given curve from x0 to x4. The heights of the rectangles are determined by the values of the function at the right endpoints.
Figure 5.1.10
: Now we divide the area under the curve into four equal subintervals for a right-endpoint approximation.
Dividing the region over the interval [0,2]
 into eight rectangles results in Δx=2−08=0.25.
 The graph is shown in Figure 5.1.11
. The area is

R8=f(0.25)(0.25)+f(0.5)(0.25)+f(0.75)(0.25)+f(1)(0.25)+f(1.25)(0.25)+f(1.5)(0.25)+f(1.75)(0.25)+f(2)(0.25)=8.25units2

A graph of the right-endpoint approximation for the area under the given curve from a=x0 to b=x8.The heights of the rectangles are determined by the values of the function at the right endpoints.
Figure 5.1.11
: Here we use right-endpoint approximation for a region divided into eight equal subintervals.
Last, the right-endpoint approximation with n=32
 is close to the actual area (Figure 5.1.12
). The area is approximately

R32=f(0.0625)(0.0625)+f(0.125)(0.0625)+f(0.1875)(0.0625)+⋯+f(2)(0.0625)=8.0625units2

A graph of the right-endpoint approximation for the area under the given curve from a=x0 to b=x32. The heights of the rectangles are determined by the values of the function at the right endpoints.
Figure 5.1.12
: The region is divided into 32
 equal subintervals for a right-endpoint approximation.
Based on these figures and calculations, it appears we are on the right track; the rectangles appear to approximate the area under the curve better as n
 gets larger. Furthermore, as n
 increases, both the left-endpoint and right-endpoint approximations appear to approach an area of 8
 square units. Table 5.1.15
 shows a numerical comparison of the left- and right-endpoint methods. The idea that the approximations of the area under the curve get better and better as n
 gets larger and larger is very important, and we now explore this idea in more detail.

Table 5.1.15
: Converging Values of Left- and Right-Endpoint Approximations as n
 Increases
Value of n
Approximate Area Ln
Approximate Area Rn
n=4
7.5
8.5
n=8
7.75
8.25
n=32
7.94
8.06
Forming Riemann Sums
So far we have been using rectangles to approximate the area under a curve. The heights of these rectangles have been determined by evaluating the function at either the right or left endpoints of the subinterval [xi−1,xi]
. In reality, there is no reason to restrict evaluation of the function to one of these two points only. We could evaluate the function at any point x∗i
 in the subinterval [xi−1,xi]
, and use f(x∗i)
 as the height of our rectangle. This gives us an estimate for the area of the form

A≈∑i=1nf(x∗i)Δx.

A sum of this form is called a Riemann sum, named for the 19th-century mathematician Bernhard Riemann, who developed the idea.

Definition: Riemann sum
Let f(x)
 be defined on a closed interval [a,b]
 and let P
 be any partition of [a,b]
. Let Δxi
 be the width of each subinterval [xi−1,xi]
 and for each i
, let x∗i
 be any point in [xi−1,xi]
. A Riemann sum is defined for f(x)
 as

∑i=1nf(x∗i)Δxi.

At this point, we'll choose a regular partition P
, as we have in our examples above. This forces all Δxi
 to be equal to Δx=b−an
 for any natural number of intervals n
.

Recall that with the left- and right-endpoint approximations, the estimates seem to get better and better as n
 get larger and larger. The same thing happens with Riemann sums. Riemann sums give better approximations for larger values of n
. We are now ready to define the area under a curve in terms of Riemann sums.

Definition: Area Under the Curve
Let f(x)
 be a continuous, nonnegative function on an interval [a,b]
, and let ∑i=1nf(x∗i)Δx
 be a Riemann sum for f(x)
 with a regular partition P
. Then, the area under the curve y=f(x)
 on [a,b]
 is given by

A=limn→∞∑i=1nf(x∗i)Δx.

See a graphical demonstration of the construction of a Riemann sum.

Some subtleties here are worth discussing. First, note that taking the limit of a sum is a little different from taking the limit of a function f(x)
 as x
 goes to infinity. Limits of sums are discussed in detail in the chapter on Sequences and Series; however, for now we can assume that the computational techniques we used to compute limits of functions can also be used to calculate limits of sums.

Second, we must consider what to do if the expression converges to different limits for different choices of x∗i.
 Fortunately, this does not happen. Although the proof is beyond the scope of this text, it can be shown that if f(x)
 is continuous on the closed interval [a,b]
, then limn→∞∑i=1nf(x∗i)Δx
 exists and is unique (in other words, it does not depend on the choice of x∗i
).

We look at some examples shortly. But, before we do, let’s take a moment and talk about some specific choices for x∗i
. Although any choice for x∗i
 gives us an estimate of the area under the curve, we don’t necessarily know whether that estimate is too high (overestimate) or too low (underestimate). If it is important to know whether our estimate is high or low, we can select our value for x∗i
 to guarantee one result or the other.

If we want an overestimate, for example, we can choose x∗i
 such that for i=1,2,3,…,n,
 f(x∗i)≥f(x)
 for all x∈[xi−1,xi]
. In other words, we choose x∗i
 so that for i=1,2,3,…,n,
 f(x∗i)
 is the maximum function value on the interval [xi−1,xi]
. If we select x∗i
 in this way, then the Riemann sum ∑i=1nf(x∗i)Δx
 is called an upper sum. Similarly, if we want an underestimate, we can choose x∗i
 so that for i=1,2,3,…,n,
 f(x∗i)
 is the minimum function value on the interval [xi−1,xi]
. In this case, the associated Riemann sum is called a lower sum. Note that if f(x)
 is either increasing or decreasing throughout the interval [a,b]
, then the maximum and minimum values of the function occur at the endpoints of the subintervals, so the upper and lower sums are just the same as the left- and right-endpoint approximations.

Example 5.1.5
: Finding Lower and Upper Sums
Find a lower sum for f(x)=10−x2
 on [1,2]
; let n=4
 subintervals.

Solution
With n=4
 over the interval [1,2],Δx=14
. We can list the intervals as [1,1.25],[1.25,1.5],[1.5,1.75],
 and [1.75,2]
. Because the function is decreasing over the interval [1,2],
 Figure shows that a lower sum is obtained by using the right endpoints.

The graph of f(x) = 10 − x^2 from 0 to 2. It is set up for a right-end approximation of the area bounded by the curve and the x axis on [1, 2], labeled a=x0 to x4. It shows a lower sum.
Figure 5.1.13
: The graph of f(x)=10−x2
 is set up for a right-endpoint approximation of the area bounded by the curve and the x
-axis on [1,2]
, and it shows a lower sum.
The Riemann sum is

∑k=14(10−x2)(0.25)=0.25[10−(1.25)2+10−(1.5)2+10−(1.75)2+10−(2)2]=0.25[8.4375+7.75+6.9375+6]=7.28units2.

The area of 7.28
 units2
 is a lower sum and an underestimate.

Exercise 5.1.5
Find an upper sum for f(x)=10−x2
 on [1,2]
; let n=4.
Sketch the approximation.
Hint
Answer
Example 5.1.6
: Finding Lower and Upper Sums for f(x)=sinx
Find a lower sum for f(x)=sinx
 over the interval [a,b]=[0,π2]
; let n=6.

Solution
Let’s first look at the graph in Figure 5.1.14
 to get a better idea of the area of interest.

A graph of the function y = sin(x) from 0 to pi. It is set up for a left endpoint approximation from 0 to pi/2 and n=6. It is a lower sum.
Figure 5.1.14
: The graph of y=sinx
 is divided into six regions: Δx=π/26=π12
.
The intervals are [0,π12],[π12,π6],[π6,π4],[π4,π3],[π3,5π12]
, and [5π12,π2]
. Note that f(x)=sinx
 is increasing on the interval [0,π2]
, so a left-endpoint approximation gives us the lower sum. A left-endpoint approximation is the Riemann sum ∑5i=0sinxi(π12)
.We have

A≈sin(0)(π12)+sin(π12)(π12)+sin(π6)(π12)+sin(π4)(π12)+sin(π3)(π12)+sin(5π12)(π12)≈0.863units2.

Exercise 5.1.6
Using the function f(x)=sinx
 over the interval [0,π2],
 find an upper sum; let n=6.

Hint
Answer
Key Concepts
The use of sigma (summation) notation of the form ∑i=1nai
 is useful for expressing long sums of values in compact form.
For a continuous function defined over an interval [a,b],
 the process of dividing the interval into n
 equal parts, extending a rectangle to the graph of the function, calculating the areas of the series of rectangles, and then summing the areas yields an approximation of the area of that region.
When using a regular partition, the width of each rectangle is Δx=b−an
.
Riemann sums are expressions of the form ∑i=1nf(x∗i)Δx,
 and can be used to estimate the area under the curve y=f(x).
 Left- and right-endpoint approximations are special kinds of Riemann sums where the values of x∗i
 are chosen to be the left or right endpoints of the subintervals, respectively.
Riemann sums allow for much flexibility in choosing the set of points x∗i
 at which the function is evaluated, often with an eye to obtaining a lower sum or an upper sum.
Key Equations
Properties of Sigma Notation
∑i=1nc∑i=1ncai∑i=1n(ai+bi)∑i=1n(ai−bi)∑i=1nai=nc=c∑i=1nai=∑i=1nai+∑i=1nbi=∑i=1nai−∑i=1nbi=∑i=1mai+∑i=m+1nai

Sums and Powers of Integers
∑i=1ni=1+2+⋯+n=n(n+1)2

∑i=1ni2=12+22+⋯+n2=n(n+1)(2n+1)6

∑i=0ni3=13+23+⋯+n3=n2(n+1)24
 

Left-Endpoint Approximation
A≈Ln=f(x0)Δx+f(x1)Δx+⋯+f(xn−1)Δx=∑i=1nf(xi−1)Δx

Right-Endpoint Approximation
A≈Rn=f(x1)Δx+f(x2)Δx+⋯+f(xn)Δx=∑i=1nf(xi)Δx

Glossary
left-endpoint approximation
lower sum
partition
regular partition
riemann sum
right-endpoint approximation
sigma notation
upper sum
This page titled 5.1: Approximating Areas is shared under a CC BY-NC-SA 4.0 license and was authored, remixed, and/or curated by OpenStax.

5.1: Approximating Areas by Edwin “Jed” Herman, Gilbert Strang is licensed CC BY-NC-SA 4.0. Original source: https://openstax.org/details/books/calculus-volume-1.
Toggle block-level attributions
Back to top
Was this article helpful?YesNo
?
The LibreTexts libraries are Powered by NICE CXone Expert and are supported by the Department of Education Open Textbook Pilot Project, the UC Davis Office of the Provost, the UC Davis Library, the California State University Affordable Learning Solutions Program, and Merlot. We also acknowledge previous National Science Foundation support under grant numbers 1246120, 1525057, and 1413739. Privacy Policy. Terms & Conditions. Accessibility Statement. For more information contact <NAME_EMAIL>.

Complete your gift to make an impact