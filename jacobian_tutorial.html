<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jacobian Matrix and Determinant - A Step-by-Step Tutorial</title>
    
    <!-- MathJax for LaTeX rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\\\(', '\\\\)']],
                displayMath: [['$$', '$$'], ['\\\\[', '\\\\]']],
                processEscapes: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --background-color: #f9f9f9;
            --text-color: #333;
            --light-gray: #ecf0f1;
            --code-bg: #f5f5f5;
            --note-bg: #e8f4f8;
            --note-border: #3498db;
            --step-bg: #f8f5e8;
            --step-border: #f39c12;
            --theorem-bg: #e8f8f5;
            --theorem-border: #1abc9c;
            --definition-bg: #e6f7e6;
            --definition-border: #27ae60;
            --example-bg: #f8e8e8;
            --example-border: #e74c3c;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1, h2, h3, h4 {
            color: var(--primary-color);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        h2 {
            font-size: 1.8rem;
            margin-top: 2.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }
        
        h3 {
            font-size: 1.4rem;
            margin-top: 2rem;
        }
        
        p {
            margin-bottom: 1.5rem;
        }
        
        .visualization {
            margin: 30px 0;
            text-align: center;
        }
        
        .theorem {
            background-color: var(--theorem-bg);
            border-left: 4px solid var(--theorem-border);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .definition {
            background-color: var(--definition-bg);
            border-left: 4px solid var(--definition-border);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .example {
            background-color: var(--example-bg);
            border-left: 4px solid var(--example-border);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .note {
            background-color: var(--note-bg);
            border-left: 4px solid var(--note-border);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .step {
            background-color: var(--step-bg);
            border-left: 4px solid var(--step-border);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        code {
            font-family: 'Courier New', Courier, monospace;
            background-color: var(--code-bg);
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        pre {
            background-color: var(--code-bg);
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--light-gray);
        }
        
        th {
            background-color: var(--light-gray);
            color: var(--primary-color);
        }
        
        tr:hover {
            background-color: var(--light-gray);
        }
        
        .svg-container {
            width: 100%;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>Jacobian Matrix and Determinant</h1>
        <p>A comprehensive tutorial on multivariable calculus transformations</p>
    </header>
    
    <section id="introduction">
        <h2>1. Introduction</h2>
        
        <p>The Jacobian matrix and its determinant are fundamental concepts in multivariable calculus, playing a crucial role in various areas of mathematics, physics, computer science, and engineering. Named after the German mathematician Carl Gustav Jacob Jacobi (1804-1851), these tools help us understand how functions transform space and how to perform changes of variables in multiple dimensions.</p>
        
        <p>In this tutorial, we'll explore:</p>
        <ul>
            <li>What the Jacobian matrix represents and how to calculate it</li>
            <li>The meaning and applications of the Jacobian determinant</li>
            <li>How to use the Jacobian for coordinate transformations</li>
            <li>Practical examples and step-by-step calculations</li>
            <li>Applications in different fields</li>
        </ul>
        
        <div class="note">
            <p>Prerequisites: To fully understand this tutorial, you should be familiar with basic calculus concepts including partial derivatives, matrix operations, and multivariable functions.</p>
        </div>
        
        <h3>1.1 Motivation: Why Do We Need the Jacobian?</h3>
        
        <p>Consider these common scenarios where the Jacobian becomes essential:</p>
        
        <ul>
            <li><strong>Coordinate Transformations</strong>: When switching between coordinate systems (e.g., Cartesian to polar coordinates), the Jacobian helps us transform integrals correctly.</li>
            <li><strong>Change of Variables in Integration</strong>: The Jacobian determinant gives us the correct scaling factor when changing variables in multiple integrals.</li>
            <li><strong>Vector Field Transformations</strong>: Understanding how vector fields transform under mappings.</li>
            <li><strong>Linearization of Functions</strong>: The Jacobian matrix gives the best linear approximation of a function near a point.</li>
            <li><strong>Sensitivity Analysis</strong>: Determining how sensitive a system of equations is to small changes in variables.</li>
        </ul>
        
        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Jacobian as a Transformation</text>
                
                <!-- Original coordinates -->
                <rect x="100" y="100" width="150" height="150" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <text x="175" y="90" text-anchor="middle" font-size="16" fill="#3498db">Original Space</text>
                
                <!-- Arrow -->
                <line x1="280" y1="175" x2="320" y2="175" stroke="#e74c3c" stroke-width="3" />
                <polygon points="320,175 310,170 310,180" fill="#e74c3c" />
                <text x="300" y="160" text-anchor="middle" font-size="14" fill="#e74c3c">Transformation</text>
                
                <!-- Transformed coordinates -->
                <path d="M 350,100 L 500,120 L 480,250 L 370,230 Z" fill="#e6f7e6" stroke="#27ae60" stroke-width="2" />
                <text x="425" y="90" text-anchor="middle" font-size="16" fill="#27ae60">Transformed Space</text>
                
                <!-- Jacobian label -->
                <text x="300" y="200" text-anchor="middle" font-size="16" fill="#2c3e50">Jacobian describes this mapping</text>
                <text x="300" y="225" text-anchor="middle" font-size="14" fill="#2c3e50">How areas/volumes change</text>
                <text x="300" y="250" text-anchor="middle" font-size="14" fill="#2c3e50">How directions transform</text>
            </svg>
        </div>
    </section>
    
    <section id="definition">
        <h2>2. Definition and Basic Properties</h2>
        
        <p>Before diving into the Jacobian matrix, let's establish some context by understanding vector-valued functions.</p>
        
        <h3>2.1 Vector-Valued Functions</h3>
        
        <div class="definition">
            <h4>Vector-Valued Function</h4>
            <p>A vector-valued function $\mathbf{F}: \mathbb{R}^n \rightarrow \mathbb{R}^m$ maps an $n$-dimensional input vector to an $m$-dimensional output vector:</p>
            <p>$$\mathbf{F}(\mathbf{x}) = \mathbf{F}(x_1, x_2, \ldots, x_n) = \begin{pmatrix} f_1(x_1, x_2, \ldots, x_n) \\ f_2(x_1, x_2, \ldots, x_n) \\ \vdots \\ f_m(x_1, x_2, \ldots, x_n) \end{pmatrix}$$</p>
            <p>where each $f_i$ is a scalar-valued function that represents one component of the output vector.</p>
        </div>
        
        <p>Examples of vector-valued functions include:</p>
        <ul>
            <li>Coordinate transformations (e.g., Cartesian to polar)</li>
            <li>Velocity fields in fluid dynamics</li>
            <li>Displacement functions in mechanics</li>
            <li>Systems of equations where each equation gives one output component</li>
        </ul>
        
        <h3>2.2 The Jacobian Matrix</h3>
        
        <div class="definition">
            <h4>Jacobian Matrix</h4>
            <p>For a vector-valued function $\mathbf{F}: \mathbb{R}^n \rightarrow \mathbb{R}^m$, the Jacobian matrix at a point $\mathbf{x}$ is an $m \times n$ matrix of all first-order partial derivatives:</p>
            <p>$$J_\mathbf{F}(\mathbf{x}) = \begin{pmatrix} 
                \frac{\partial f_1}{\partial x_1} & \frac{\partial f_1}{\partial x_2} & \cdots & \frac{\partial f_1}{\partial x_n} \\
                \frac{\partial f_2}{\partial x_1} & \frac{\partial f_2}{\partial x_2} & \cdots & \frac{\partial f_2}{\partial x_n} \\
                \vdots & \vdots & \ddots & \vdots \\
                \frac{\partial f_m}{\partial x_1} & \frac{\partial f_m}{\partial x_2} & \cdots & \frac{\partial f_m}{\partial x_n}
            \end{pmatrix}$$</p>
        </div>
        
        <p>In other words, the entry in the $i$-th row and $j$-th column of the Jacobian matrix is $\frac{\partial f_i}{\partial x_j}$, the partial derivative of the $i$-th component function with respect to the $j$-th input variable.</p>
        
        <div class="note">
            <p>The Jacobian matrix is sometimes denoted as $\frac{\partial \mathbf{F}}{\partial \mathbf{x}}$, $D\mathbf{F}(\mathbf{x})$, or simply $\mathbf{J}$ when the context is clear.</p>
        </div>
        
        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Structure of the Jacobian Matrix</text>
                
                <!-- Jacobian matrix representation -->
                <rect x="150" y="70" width="300" height="180" fill="#f5f5f5" stroke="#3498db" stroke-width="2" />
                
                <!-- Matrix entries -->
                <text x="170" y="110" font-size="16" fill="#e74c3c">∂f₁/∂x₁</text>
                <text x="250" y="110" font-size="16" fill="#e74c3c">∂f₁/∂x₂</text>
                <text x="350" y="110" font-size="16" fill="#e74c3c">⋯</text>
                <text x="420" y="110" font-size="16" fill="#e74c3c">∂f₁/∂xₙ</text>
                
                <text x="170" y="150" font-size="16" fill="#e74c3c">∂f₂/∂x₁</text>
                <text x="250" y="150" font-size="16" fill="#e74c3c">∂f₂/∂x₂</text>
                <text x="350" y="150" font-size="16" fill="#e74c3c">⋯</text>
                <text x="420" y="150" font-size="16" fill="#e74c3c">∂f₂/∂xₙ</text>
                
                <text x="170" y="190" font-size="16" fill="#e74c3c">⋮</text>
                <text x="250" y="190" font-size="16" fill="#e74c3c">⋮</text>
                <text x="350" y="190" font-size="16" fill="#e74c3c">⋱</text>
                <text x="420" y="190" font-size="16" fill="#e74c3c">⋮</text>
                
                <text x="170" y="230" font-size="16" fill="#e74c3c">∂fₘ/∂x₁</text>
                <text x="250" y="230" font-size="16" fill="#e74c3c">∂fₘ/∂x₂</text>
                <text x="350" y="230" font-size="16" fill="#e74c3c">⋯</text>
                <text x="420" y="230" font-size="16" fill="#e74c3c">∂fₘ/∂xₙ</text>
                
                <!-- Labels -->
                <text x="125" y="110" text-anchor="end" font-size="16" fill="#2c3e50">Row 1:</text>
                <text x="125" y="150" text-anchor="end" font-size="16" fill="#2c3e50">Row 2:</text>
                <text x="125" y="230" text-anchor="end" font-size="16" fill="#2c3e50">Row m:</text>
                
                <text x="170" y="270" text-anchor="middle" font-size="16" fill="#2c3e50">Col 1</text>
                <text x="250" y="270" text-anchor="middle" font-size="16" fill="#2c3e50">Col 2</text>
                <text x="420" y="270" text-anchor="middle" font-size="16" fill="#2c3e50">Col n</text>
            </svg>
        </div>
        
        <h3>2.3 Special Cases</h3>
        
        <p>The Jacobian matrix takes different forms depending on the dimensions of the input and output spaces:</p>
        
        <div class="example">
            <h4>Case 1: $\mathbf{F}: \mathbb{R} \rightarrow \mathbb{R}^m$ (Single Input, Multiple Outputs)</h4>
            <p>The Jacobian is an $m \times 1$ column vector (often called the derivative or gradient):</p>
            <p>$$J_\mathbf{F}(x) = \begin{pmatrix} 
                \frac{d f_1}{d x} \\
                \frac{d f_2}{d x} \\
                \vdots \\
                \frac{d f_m}{d x}
            \end{pmatrix}$$</p>
        </div>
        
        <div class="example">
            <h4>Case 2: $\mathbf{F}: \mathbb{R}^n \rightarrow \mathbb{R}$ (Multiple Inputs, Single Output)</h4>
            <p>The Jacobian is a $1 \times n$ row vector, which is the transpose of the gradient:</p>
            <p>$$J_\mathbf{F}(\mathbf{x}) = \begin{pmatrix} 
                \frac{\partial f}{\partial x_1} & \frac{\partial f}{\partial x_2} & \cdots & \frac{\partial f}{\partial x_n}
            \end{pmatrix}$$</p>
        </div>
        
        <div class="example">
            <h4>Case 3: $\mathbf{F}: \mathbb{R}^n \rightarrow \mathbb{R}^n$ (Equal Dimensions)</h4>
            <p>The Jacobian is an $n \times n$ square matrix. This is particularly important for coordinate transformations and for calculating the Jacobian determinant.</p>
        </div>
        
        <h3>2.4 Basic Properties</h3>
        
        <p>The Jacobian matrix has several important properties:</p>
        
        <div class="theorem">
            <h4>Properties of the Jacobian Matrix</h4>
            <ol>
                <li><strong>Linearity</strong>: If $\mathbf{F} = a\mathbf{G} + b\mathbf{H}$ where $a$ and $b$ are constants, then $J_\mathbf{F} = aJ_\mathbf{G} + bJ_\mathbf{H}$.</li>
                <li><strong>Chain Rule</strong>: If $\mathbf{H} = \mathbf{F} \circ \mathbf{G}$ (composition of functions), then $J_\mathbf{H}(\mathbf{x}) = J_\mathbf{F}(\mathbf{G}(\mathbf{x})) \cdot J_\mathbf{G}(\mathbf{x})$.</li>
                <li><strong>Inverse Function Theorem</strong>: If $\mathbf{F}: \mathbb{R}^n \rightarrow \mathbb{R}^n$ is invertible near a point $\mathbf{x}$ and $J_\mathbf{F}(\mathbf{x})$ is invertible, then the Jacobian of the inverse function $\mathbf{F}^{-1}$ at $\mathbf{F}(\mathbf{x})$ is the inverse of the Jacobian: $J_{\mathbf{F}^{-1}}(\mathbf{F}(\mathbf{x})) = [J_\mathbf{F}(\mathbf{x})]^{-1}$.</li>
                <li><strong>Linear Approximation</strong>: The Jacobian provides the best linear approximation of $\mathbf{F}$ near a point $\mathbf{x}_0$: $\mathbf{F}(\mathbf{x}) \approx \mathbf{F}(\mathbf{x}_0) + J_\mathbf{F}(\mathbf{x}_0)(\mathbf{x} - \mathbf{x}_0)$.</li>
            </ol>
        </div>
    </section>
    
    <section id="jacobian-determinant">
        <h2>3. The Jacobian Determinant</h2>
        
        <p>When working with square Jacobian matrices (where the input and output dimensions are equal), the determinant of the Jacobian becomes a powerful tool with important geometric and analytical interpretations.</p>
        
        <h3>3.1 Definition</h3>
        
        <div class="definition">
            <h4>Jacobian Determinant</h4>
            <p>For a function $\mathbf{F}: \mathbb{R}^n \rightarrow \mathbb{R}^n$, the Jacobian determinant is the determinant of the Jacobian matrix:</p>
            <p>$$\det(J_\mathbf{F}(\mathbf{x})) = \det\left(\frac{\partial(f_1, f_2, \ldots, f_n)}{\partial(x_1, x_2, \ldots, x_n)}\right)$$</p>
            <p>This is sometimes denoted as $|\frac{\partial \mathbf{F}}{\partial \mathbf{x}}|$ or simply $|J|$.</p>
        </div>
        
        <div class="example">
            <h4>For a 2D Transformation</h4>
            <p>If $\mathbf{F}(x, y) = (f_1(x,y), f_2(x,y))$, then:</p>
            <p>$$\det(J_\mathbf{F}(x,y)) = \begin{vmatrix} 
                \frac{\partial f_1}{\partial x} & \frac{\partial f_1}{\partial y} \\
                \frac{\partial f_2}{\partial x} & \frac{\partial f_2}{\partial y}
            \end{vmatrix} = \frac{\partial f_1}{\partial x}\frac{\partial f_2}{\partial y} - \frac{\partial f_1}{\partial y}\frac{\partial f_2}{\partial x}$$</p>
        </div>
        
        <h3>3.2 Geometric Interpretation</h3>
        
        <p>The Jacobian determinant has a profound geometric meaning:</p>
        
        <div class="theorem">
            <h4>Geometric Meaning of the Jacobian Determinant</h4>
            <p>The absolute value of the Jacobian determinant $|\det(J_\mathbf{F}(\mathbf{x}))|$ at a point $\mathbf{x}$ represents the factor by which the function $\mathbf{F}$ scales volumes near $\mathbf{x}$.</p>
            <ul>
                <li>If $|\det(J_\mathbf{F}(\mathbf{x}))| > 1$, volumes are expanded by the transformation.</li>
                <li>If $|\det(J_\mathbf{F}(\mathbf{x}))| < 1$, volumes are contracted.</li>
                <li>If $\det(J_\mathbf{F}(\mathbf{x})) = 0$, the transformation is not invertible at $\mathbf{x}$ (dimension is reduced).</li>
                <li>If $\det(J_\mathbf{F}(\mathbf{x})) < 0$, the transformation reverses orientation.</li>
            </ul>
        </div>
        
        <div class="svg-container">
            <svg width="600" height="320" viewBox="0 0 600 320">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Geometric Interpretation of Jacobian Determinant</text>
                
                <!-- Original square -->
                <rect x="80" y="100" width="100" height="100" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <text x="130" y="90" text-anchor="middle" font-size="16" fill="#3498db">Original Area</text>
                <text x="130" y="210" text-anchor="middle" font-size="14" fill="#3498db">Area = 1</text>
                
                <!-- Arrows -->
                <line x1="200" y1="150" x2="240" y2="150" stroke="#e74c3c" stroke-width="3" />
                <polygon points="240,150 230,145 230,155" fill="#e74c3c" />
                <text x="220" y="135" text-anchor="middle" font-size="14" fill="#e74c3c">F</text>
                
                <!-- Expansion transformation -->
                <path d="M 270,100 L 400,80 L 420,200 L 290,220 Z" fill="#e6f7e6" stroke="#27ae60" stroke-width="2" />
                <text x="345" y="90" text-anchor="middle" font-size="16" fill="#27ae60">Transformed Area</text>
                <text x="345" y="220" text-anchor="middle" font-size="14" fill="#27ae60">Area = |det(J)|</text>
                
                <!-- Jacobian calculation -->
                <rect x="170" y="240" width="260" height="60" rx="10" fill="white" stroke="#2c3e50" stroke-width="1" />
                <text x="300" y="265" text-anchor="middle" font-size="16" fill="#2c3e50">|det(J)| = Ratio of areas</text>
                <text x="300" y="285" text-anchor="middle" font-size="14" fill="#2c3e50">= Transformed area / Original area</text>
            </svg>
        </div>
        
        <p>In 2D, the Jacobian determinant represents the ratio of areas. In 3D, it represents the ratio of volumes.</p>
        
        <div class="example">
            <h4>Sign of the Determinant and Orientation</h4>
            <p>Consider a 2D transformation:</p>
            <ul>
                <li>If $\det(J) > 0$, the transformation preserves orientation (e.g., right-handed coordinates remain right-handed).</li>
                <li>If $\det(J) < 0$, the transformation reverses orientation (e.g., right-handed coordinates become left-handed).</li>
            </ul>
            <p>For example, reflecting across the x-axis (mapping $(x,y)$ to $(x,-y)$) reverses orientation and has a negative Jacobian determinant.</p>
        </div>
        
        <h3>3.3 Critical Points and the Jacobian</h3>
        
        <div class="theorem">
            <h4>Critical Points</h4>
            <p>Points where $\det(J_\mathbf{F}(\mathbf{x})) = 0$ are called critical points of the transformation. At these points:</p>
            <ul>
                <li>The transformation is not locally invertible</li>
                <li>The transformation compresses at least one dimension to zero</li>
                <li>The linear approximation is not a one-to-one mapping</li>
            </ul>
        </div>
        
        <p>Critical points are important in optimization problems, where they can represent local extrema, saddle points, or other special features of a function.</p>
        
        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Critical Points Where det(J) = 0</text>
                
                <!-- Regular mapping -->
                <rect x="100" y="100" width="60" height="60" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <path d="M 250,80 L 310,100 L 290,160 L 230,140 Z" fill="#e6f7e6" stroke="#27ae60" stroke-width="2" />
                <line x1="160" y1="130" x2="230" y2="130" stroke="#2c3e50" stroke-width="1.5" />
                <polygon points="230,130 220,125 220,135" fill="#2c3e50" />
                <text x="195" y="120" text-anchor="middle" font-size="14" fill="#2c3e50">det(J) ≠ 0</text>
                
                <!-- Singular mapping (line) -->
                <rect x="100" y="200" width="60" height="60" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <line x1="250" y1="230" x2="310" y2="230" stroke="#e74c3c" stroke-width="3" />
                <line x1="160" y1="230" x2="230" y2="230" stroke="#2c3e50" stroke-width="1.5" />
                <polygon points="230,230 220,225 220,235" fill="#2c3e50" />
                <text x="195" y="220" text-anchor="middle" font-size="14" fill="#2c3e50">det(J) = 0</text>
                
                <!-- Fold mapping -->
                <rect x="380" y="100" width="60" height="60" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <path d="M 500,130 Q 530,100 560,130" fill="none" stroke="#e74c3c" stroke-width="3" />
                <line x1="440" y1="130" x2="480" y2="130" stroke="#2c3e50" stroke-width="1.5" />
                <polygon points="480,130 470,125 470,135" fill="#2c3e50" />
                <text x="460" y="120" text-anchor="middle" font-size="14" fill="#2c3e50">det(J) = 0 at fold</text>
                
                <!-- Explanation -->
                <text x="380" y="220" text-anchor="start" font-size="14" fill="#2c3e50">When det(J) = 0, the mapping:</text>
                <text x="400" y="240" text-anchor="start" font-size="14" fill="#2c3e50">• Collapses dimensions</text>
                <text x="400" y="260" text-anchor="start" font-size="14" fill="#2c3e50">• Is not invertible</text>
                <text x="400" y="280" text-anchor="start" font-size="14" fill="#2c3e50">• Creates singularities</text>
            </svg>
        </div>
        
        <h3>3.4 Invariance of the Jacobian Determinant</h3>
        
        <div class="theorem">
            <h4>Composition of Functions</h4>
            <p>For composed functions $\mathbf{H} = \mathbf{F} \circ \mathbf{G}$, the determinant of the Jacobian satisfies:</p>
            <p>$$\det(J_\mathbf{H}(\mathbf{x})) = \det(J_\mathbf{F}(\mathbf{G}(\mathbf{x}))) \cdot \det(J_\mathbf{G}(\mathbf{x}))$$</p>
            <p>This is the multivariable generalization of the chain rule for derivatives.</p>
        </div>
        
        <p>This property is particularly useful when dealing with multiple coordinate transformations, as it allows us to compute the overall scaling factor by multiplying the individual scaling factors.</p>
    </section>
    
    <section id="change-of-variables">
        <h2>4. Change of Variables in Integration</h2>
        
        <p>One of the most important applications of the Jacobian determinant is in performing changes of variables in multiple integrals. This generalizes the substitution rule from single-variable calculus.</p>
        
        <h3>4.1 The Change of Variables Formula</h3>
        
        <div class="theorem">
            <h4>Change of Variables Theorem</h4>
            <p>Let $\mathbf{F}: U \subset \mathbb{R}^n \rightarrow V \subset \mathbb{R}^n$ be a continuously differentiable bijection with a non-zero Jacobian determinant in $U$. Then, for any integrable function $g: V \rightarrow \mathbb{R}$:</p>
            <p>$$\int_V g(\mathbf{y}) \, d\mathbf{y} = \int_U g(\mathbf{F}(\mathbf{x})) \, |\det(J_\mathbf{F}(\mathbf{x}))| \, d\mathbf{x}$$</p>
        </div>
        
        <p>In more intuitive terms, when you change variables in a multiple integral, you must:</p>
        <ol>
            <li>Substitute the new variables into the integrand</li>
            <li>Multiply by the absolute value of the Jacobian determinant</li>
            <li>Change the limits of integration to correspond to the new variables</li>
        </ol>
        
        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Change of Variables in Integration</text>
                
                <!-- Original region -->
                <path d="M 100,100 L 220,80 L 240,180 L 120,200 Z" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <text x="170" y="140" text-anchor="middle" font-size="14" fill="#3498db">V</text>
                <text x="120" y="90" text-anchor="start" font-size="14" fill="#3498db">y-coordinates</text>
                
                <!-- Integral in original coordinates -->
                <text x="170" y="230" text-anchor="middle" font-size="16" fill="#2c3e50">$\int_V g(\mathbf{y}) \, d\mathbf{y}$</text>
                
                <!-- Arrow -->
                <line x1="280" y1="150" x2="320" y2="150" stroke="#e74c3c" stroke-width="3" />
                <polygon points="320,150 310,145 310,155" fill="#e74c3c" />
                <text x="300" y="135" text-anchor="middle" font-size="14" fill="#e74c3c">F⁻¹</text>
                
                <!-- Transformed region -->
                <rect x="380" y="100" width="120" height="100" fill="#e6f7e6" stroke="#27ae60" stroke-width="2" />
                <text x="440" y="150" text-anchor="middle" font-size="14" fill="#27ae60">U</text>
                <text x="400" y="90" text-anchor="start" font-size="14" fill="#27ae60">x-coordinates</text>
                
                <!-- Integral in new coordinates -->
                <text x="440" y="230" text-anchor="middle" font-size="16" fill="#2c3e50">$\int_U g(\mathbf{F}(\mathbf{x})) \, |\det(J_\mathbf{F}(\mathbf{x}))| \, d\mathbf{x}$</text>
                
                <!-- Explanation -->
                <rect x="170" y="260" width="260" height="30" rx="5" fill="white" stroke="#2c3e50" stroke-width="1" opacity="0.8" />
                <text x="300" y="280" text-anchor="middle" font-size="14" fill="#2c3e50">|det(J)| = Volume scale factor</text>
            </svg>
        </div>
        
        <h3>4.2 Example: Polar Coordinates</h3>
        
        <div class="example">
            <h4>Change to Polar Coordinates</h4>
            <p>Consider the transformation from polar to Cartesian coordinates:</p>
            <p>$\mathbf{F}(r, \theta) = (r\cos\theta, r\sin\theta) = (x, y)$</p>
            <p>The Jacobian matrix is:</p>
            <p>$$J_\mathbf{F}(r, \theta) = \begin{pmatrix} 
                \frac{\partial x}{\partial r} & \frac{\partial x}{\partial \theta} \\
                \frac{\partial y}{\partial r} & \frac{\partial y}{\partial \theta}
            \end{pmatrix} = \begin{pmatrix} 
                \cos\theta & -r\sin\theta \\
                \sin\theta & r\cos\theta
            \end{pmatrix}$$</p>
            <p>Computing the determinant:</p>
            <p>$$\det(J_\mathbf{F}(r, \theta)) = \cos\theta \cdot r\cos\theta - (-r\sin\theta) \cdot \sin\theta = r\cos^2\theta + r\sin^2\theta = r$$</p>
            <p>Therefore, when changing from Cartesian to polar coordinates in a double integral, we multiply by $|r|$, which is simply $r$ since $r > 0$ in polar coordinates:</p>
            <p>$$\int\int_R f(x,y) \, dx \, dy = \int\int_S f(r\cos\theta, r\sin\theta) \, r \, dr \, d\theta$$</p>
            <p>where $R$ is a region in the $xy$-plane and $S$ is the corresponding region in the $r\theta$-plane.</p>
        </div>
        
        <div class="step">
            <h4>Step-by-Step Procedure for Change of Variables</h4>
            <ol>
                <li>Identify the transformation $\mathbf{F}$ between the coordinate systems</li>
                <li>Compute the Jacobian matrix $J_\mathbf{F}$ by finding all partial derivatives</li>
                <li>Calculate the Jacobian determinant $\det(J_\mathbf{F})$</li>
                <li>Replace the original integrand $g(\mathbf{y})$ with $g(\mathbf{F}(\mathbf{x}))$</li>
                <li>Multiply by the absolute value of the Jacobian determinant $|\det(J_\mathbf{F})|$</li>
                <li>Transform the region of integration and the differential element</li>
                <li>Evaluate the new integral</li>
            </ol>
        </div>
        
        <h3>4.3 Example: Spherical Coordinates</h3>
        
        <div class="example">
            <h4>Change to Spherical Coordinates</h4>
            <p>The transformation from spherical to Cartesian coordinates is:</p>
            <p>$$\mathbf{F}(\rho, \theta, \phi) = (\rho\sin\phi\cos\theta, \rho\sin\phi\sin\theta, \rho\cos\phi) = (x, y, z)$$</p>
            <p>where $\rho \geq 0$ is the radial distance, $\theta \in [0, 2\pi]$ is the azimuthal angle, and $\phi \in [0, \pi]$ is the polar angle.</p>
            <p>Computing the Jacobian determinant (which involves a more complex calculation):</p>
            <p>$$\det(J_\mathbf{F}(\rho, \theta, \phi)) = \rho^2 \sin\phi$$</p>
            <p>Therefore, when changing from Cartesian to spherical coordinates in a triple integral:</p>
            <p>$$\int\int\int_E f(x,y,z) \, dx \, dy \, dz = \int\int\int_D f(\rho\sin\phi\cos\theta, \rho\sin\phi\sin\theta, \rho\cos\phi) \, \rho^2 \sin\phi \, d\rho \, d\theta \, d\phi$$</p>
            <p>where $E$ is a region in $xyz$-space and $D$ is the corresponding region in $\rho\theta\phi$-space.</p>
        </div>
        
        <div class="note">
            <p>The Jacobian determinant in coordinate transformations always represents the "scale factor" that relates differential volume elements between the two coordinate systems. For common coordinate systems, these determinants are well-known and can be used directly without recalculation.</p>
        </div>
    </section>
    
    <section id="examples">
        <h2>5. Worked Examples</h2>
        
        <p>Let's work through several examples to reinforce our understanding of how to calculate and apply the Jacobian matrix and determinant in various scenarios.</p>
        
        <h3>5.1 Example: Linear Transformation</h3>
        
        <div class="example">
            <h4>Linear Transformation in 2D</h4>
            <p>Consider the linear transformation $\mathbf{F}(x, y) = (2x + 3y, 4x - y)$.</p>
            
            <p><strong>Step 1:</strong> Identify the component functions.</p>
            <p>$f_1(x, y) = 2x + 3y$ and $f_2(x, y) = 4x - y$</p>
            
            <p><strong>Step 2:</strong> Compute the partial derivatives.</p>
            <p>$\frac{\partial f_1}{\partial x} = 2$, $\frac{\partial f_1}{\partial y} = 3$</p>
            <p>$\frac{\partial f_2}{\partial x} = 4$, $\frac{\partial f_2}{\partial y} = -1$</p>
            
            <p><strong>Step 3:</strong> Form the Jacobian matrix.</p>
            <p>$$J_\mathbf{F}(x, y) = \begin{pmatrix} 
                \frac{\partial f_1}{\partial x} & \frac{\partial f_1}{\partial y} \\
                \frac{\partial f_2}{\partial x} & \frac{\partial f_2}{\partial y}
            \end{pmatrix} = \begin{pmatrix} 
                2 & 3 \\
                4 & -1
            \end{pmatrix}$$</p>
            
            <p><strong>Step 4:</strong> Calculate the Jacobian determinant.</p>
            <p>$$\det(J_\mathbf{F}(x, y)) = \begin{vmatrix} 2 & 3 \\ 4 & -1 \end{vmatrix} = 2 \cdot (-1) - 3 \cdot 4 = -2 - 12 = -14$$</p>
            
            <p><strong>Step 5:</strong> Interpret the results.</p>
            <p>Since $\det(J_\mathbf{F}) = -14 < 0$, this transformation:</p>
            <ul>
                <li>Scales areas by a factor of 14</li>
                <li>Reverses orientation (flips the coordinate system)</li>
                <li>Is invertible (because the determinant is non-zero)</li>
            </ul>
        </div>
        
        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Linear Transformation Example</text>
                
                <!-- Original square -->
                <rect x="100" y="100" width="100" height="100" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <text x="150" y="90" text-anchor="middle" font-size="16" fill="#3498db">Original Square</text>
                <text x="85" y="150" text-anchor="end" font-size="14" fill="#3498db">(0,0)</text>
                <text x="185" y="150" text-anchor="start" font-size="14" fill="#3498db">(1,0)</text>
                <text x="150" y="210" text-anchor="middle" font-size="14" fill="#3498db">(0,1)</text>
                
                <!-- Arrows -->
                <line x1="220" y1="150" x2="260" y2="150" stroke="#e74c3c" stroke-width="3" />
                <polygon points="260,150 250,145 250,155" fill="#e74c3c" />
                <text x="240" y="135" text-anchor="middle" font-size="14" fill="#e74c3c">F(x,y)=(2x+3y,4x-y)</text>
                
                <!-- Transformed parallelogram -->
                <path d="M 300,150 L 400,130 L 370,80 L 270,100 Z" fill="#e6f7e6" stroke="#27ae60" stroke-width="2" />
                <text x="335" y="70" text-anchor="middle" font-size="16" fill="#27ae60">Transformed Shape</text>
                <text x="290" y="150" text-anchor="end" font-size="14" fill="#27ae60">(0,0)</text>
                <text x="410" y="130" text-anchor="start" font-size="14" fill="#27ae60">(2,4)</text>
                <text x="370" y="70" text-anchor="middle" font-size="14" fill="#27ae60">(3,-1)</text>
                
                <!-- Determinant value -->
                <rect x="200" y="230" width="200" height="40" rx="5" fill="white" stroke="#2c3e50" stroke-width="1" />
                <text x="300" y="255" text-anchor="middle" font-size="16" fill="#e74c3c">det(J) = -14</text>
            </svg>
        </div>
        
        <h3>5.2 Example: Nonlinear Transformation</h3>
        
        <div class="example">
            <h4>Polar to Cartesian Coordinates (Revisited)</h4>
            <p>Let's revisit the polar coordinates transformation in more detail: $\mathbf{F}(r, \theta) = (r\cos\theta, r\sin\theta) = (x, y)$</p>
            
            <p><strong>Step 1:</strong> Identify the component functions.</p>
            <p>$f_1(r, \theta) = r\cos\theta$ and $f_2(r, \theta) = r\sin\theta$</p>
            
            <p><strong>Step 2:</strong> Compute the partial derivatives.</p>
            <p>$\frac{\partial f_1}{\partial r} = \cos\theta$, $\frac{\partial f_1}{\partial \theta} = -r\sin\theta$</p>
            <p>$\frac{\partial f_2}{\partial r} = \sin\theta$, $\frac{\partial f_2}{\partial \theta} = r\cos\theta$</p>
            
            <p><strong>Step 3:</strong> Form the Jacobian matrix.</p>
            <p>$$J_\mathbf{F}(r, \theta) = \begin{pmatrix} 
                \cos\theta & -r\sin\theta \\
                \sin\theta & r\cos\theta
            \end{pmatrix}$$</p>
            
            <p><strong>Step 4:</strong> Calculate the Jacobian determinant.</p>
            <p>$$\det(J_\mathbf{F}(r, \theta)) = \cos\theta \cdot r\cos\theta - (-r\sin\theta) \cdot \sin\theta$$</p>
            <p>$$= r\cos^2\theta + r\sin^2\theta = r(\cos^2\theta + \sin^2\theta) = r$$</p>
            
            <p><strong>Step 5:</strong> Use the result for a change of variables.</p>
            <p>To transform an integral from Cartesian to polar coordinates:</p>
            <p>$$\int\int_R f(x,y) \, dx \, dy = \int\int_S f(r\cos\theta, r\sin\theta) \, r \, dr \, d\theta$$</p>
            <p>where the additional $r$ factor comes from the Jacobian determinant.</p>
        </div>
        
        <h3>5.3 Example: 3D Transformation</h3>
        
        <div class="example">
            <h4>Cylindrical to Cartesian Coordinates</h4>
            <p>Consider the transformation from cylindrical to Cartesian coordinates: $\mathbf{F}(r, \theta, z) = (r\cos\theta, r\sin\theta, z) = (x, y, z)$</p>
            
            <p><strong>Step 1:</strong> Identify the component functions.</p>
            <p>$f_1(r, \theta, z) = r\cos\theta$, $f_2(r, \theta, z) = r\sin\theta$, and $f_3(r, \theta, z) = z$</p>
            
            <p><strong>Step 2:</strong> Compute the partial derivatives.</p>
            <p>$\frac{\partial f_1}{\partial r} = \cos\theta$, $\frac{\partial f_1}{\partial \theta} = -r\sin\theta$, $\frac{\partial f_1}{\partial z} = 0$</p>
            <p>$\frac{\partial f_2}{\partial r} = \sin\theta$, $\frac{\partial f_2}{\partial \theta} = r\cos\theta$, $\frac{\partial f_2}{\partial z} = 0$</p>
            <p>$\frac{\partial f_3}{\partial r} = 0$, $\frac{\partial f_3}{\partial \theta} = 0$, $\frac{\partial f_3}{\partial z} = 1$</p>
            
            <p><strong>Step 3:</strong> Form the Jacobian matrix.</p>
            <p>$$J_\mathbf{F}(r, \theta, z) = \begin{pmatrix} 
                \cos\theta & -r\sin\theta & 0 \\
                \sin\theta & r\cos\theta & 0 \\
                0 & 0 & 1
            \end{pmatrix}$$</p>
            
            <p><strong>Step 4:</strong> Calculate the Jacobian determinant.</p>
            <p>Using the cofactor expansion along the third row:</p>
            <p>$$\det(J_\mathbf{F}(r, \theta, z)) = 1 \cdot \begin{vmatrix} \cos\theta & -r\sin\theta \\ \sin\theta & r\cos\theta \end{vmatrix}$$</p>
            <p>$$= 1 \cdot (r\cos^2\theta + r\sin^2\theta) = r$$</p>
            
            <p><strong>Step 5:</strong> Use the result for a change of variables.</p>
            <p>For a triple integral in cylindrical coordinates:</p>
            <p>$$\int\int\int_E f(x,y,z) \, dx \, dy \, dz = \int\int\int_D f(r\cos\theta, r\sin\theta, z) \, r \, dr \, d\theta \, dz$$</p>
            <p>The factor $r$ in the integrand comes from the Jacobian determinant.</p>
        </div>
        
        <h3>5.4 Example: Finding Critical Points</h3>
        
        <div class="example">
            <h4>Critical Points of a Transformation</h4>
            <p>Consider the transformation $\mathbf{F}(x, y) = (x^2 - y^2, 2xy)$, which maps $\mathbb{R}^2$ to $\mathbb{R}^2$.</p>
            
            <p><strong>Step 1:</strong> Compute the Jacobian matrix.</p>
            <p>$\frac{\partial f_1}{\partial x} = 2x$, $\frac{\partial f_1}{\partial y} = -2y$</p>
            <p>$\frac{\partial f_2}{\partial x} = 2y$, $\frac{\partial f_2}{\partial y} = 2x$</p>
            <p>$$J_\mathbf{F}(x, y) = \begin{pmatrix} 
                2x & -2y \\
                2y & 2x
            \end{pmatrix}$$</p>
            
            <p><strong>Step 2:</strong> Find the Jacobian determinant.</p>
            <p>$$\det(J_\mathbf{F}(x, y)) = 2x \cdot 2x - (-2y) \cdot 2y = 4x^2 + 4y^2 = 4(x^2 + y^2)$$</p>
            
            <p><strong>Step 3:</strong> Find the critical points (where the determinant is zero).</p>
            <p>$\det(J_\mathbf{F}(x, y)) = 0$ when $4(x^2 + y^2) = 0$, which happens only when $x = 0$ and $y = 0$.</p>
            <p>So the only critical point is at the origin $(0, 0)$.</p>
            
            <p><strong>Step 4:</strong> Interpret the result.</p>
            <p>The transformation $\mathbf{F}(x, y) = (x^2 - y^2, 2xy)$ is not invertible at the origin, but is invertible everywhere else. This particular transformation is actually the complex squaring function $z \mapsto z^2$ if we identify $(x, y)$ with the complex number $z = x + iy$.</p>
        </div>
        
        <div class="note">
            <p>In the complex squaring example, we see that the Jacobian determinant is $4(x^2 + y^2) = 4|z|^2$, which vanishes only at $z = 0$. This aligns with the fact that the complex squaring function is not invertible at the origin because both $z$ and $-z$ map to the same value $z^2$.</p>
        </div>
    </section>
    
    <section id="applications">
        <h2>6. Applications in Various Fields</h2>
        
        <p>The Jacobian matrix and determinant have wide-ranging applications across mathematics, science, and engineering. Here are some important applications in different fields:</p>
        
        <h3>6.1 Physics and Engineering</h3>
        
        <div class="example">
            <h4>Fluid Dynamics</h4>
            <p>In fluid dynamics, the Jacobian matrix appears in:</p>
            <ul>
                <li>The strain rate tensor, which describes how a fluid element deforms</li>
                <li>Conservation laws when changing between different coordinate systems</li>
                <li>Numerical simulations where coordinate transformations are needed to model complex geometries</li>
            </ul>
        </div>
        
        <div class="example">
            <h4>Electromagnetism</h4>
            <p>Maxwell's equations can be expressed in different coordinate systems using the Jacobian determinant to transform differential operators like gradient, divergence, and curl.</p>
        </div>
        
        <div class="example">
            <h4>Continuum Mechanics</h4>
            <p>The Jacobian determinant represents the local volume change ratio in material deformations. It's crucial in:</p>
            <ul>
                <li>Finite element analysis</li>
                <li>Structural mechanics</li>
                <li>Modeling of elastic and plastic deformations</li>
                <li>Computing strains and stresses in materials</li>
            </ul>
        </div>
        
        <h3>6.2 Computer Graphics and Computer Vision</h3>
        
        <div class="example">
            <h4>3D Rendering and Geometric Transformations</h4>
            <p>The Jacobian matrix is used to:</p>
            <ul>
                <li>Transform surface normals when rendering 3D objects</li>
                <li>Compute texture mapping transformations</li>
                <li>Calculate how light interacts with curved surfaces</li>
            </ul>
        </div>
        
        <div class="example">
            <h4>Image Processing</h4>
            <p>In image warping and morphing, the Jacobian determinant helps ensure that transformations preserve or properly scale image features.</p>
        </div>
        
        <div class="example">
            <h4>Computer Vision</h4>
            <p>The Jacobian matrix appears in:</p>
            <ul>
                <li>Camera calibration and 3D reconstruction</li>
                <li>Optical flow calculations</li>
                <li>Feature tracking algorithms</li>
                <li>Structure from motion</li>
            </ul>
        </div>
        
        <h3>6.3 Mathematics and Optimization</h3>
        
        <div class="example">
            <h4>Multivariable Calculus</h4>
            <p>Beyond the change of variables in integration, the Jacobian is used in:</p>
            <ul>
                <li>The implicit function theorem</li>
                <li>Inverse function theorem</li>
                <li>Identifying critical points of functions</li>
            </ul>
        </div>
        
        <div class="example">
            <h4>Optimization Algorithms</h4>
            <p>In numerical optimization, the Jacobian matrix appears in:</p>
            <ul>
                <li>Newton's method for systems of equations</li>
                <li>The Gauss-Newton algorithm for nonlinear least squares</li>
                <li>Sensitivity analysis of optimization problems</li>
                <li>Constrained optimization techniques</li>
            </ul>
        </div>
        
        <div class="example">
            <h4>Differential Geometry</h4>
            <p>The Jacobian determinant is used to:</p>
            <ul>
                <li>Define the volume form on a manifold</li>
                <li>Study the properties of mappings between spaces</li>
                <li>Analyze the curvature of surfaces</li>
            </ul>
        </div>
        
        <h3>6.4 Data Science and Machine Learning</h3>
        
        <div class="example">
            <h4>Neural Networks</h4>
            <p>The Jacobian matrix is used in:</p>
            <ul>
                <li>Backpropagation algorithms for training neural networks</li>
                <li>Analysis of network sensitivity to input perturbations</li>
                <li>Adversarial example generation</li>
            </ul>
        </div>
        
        <div class="example">
            <h4>Dimensionality Reduction</h4>
            <p>Techniques like locally linear embedding (LLE) and t-SNE use the Jacobian to preserve local structure when mapping high-dimensional data to lower dimensions.</p>
        </div>
        
        <div class="example">
            <h4>Normalizing Flows</h4>
            <p>In generative modeling, normalizing flows use invertible transformations with tractable Jacobian determinants to transform between simple distributions and complex data distributions.</p>
        </div>
        
        <div class="svg-container">
            <svg width="600" height="350" viewBox="0 0 600 350">
                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Applications of the Jacobian</text>
                
                <!-- Background rectangles -->
                <rect x="50" y="60" width="240" height="120" rx="10" fill="#e8f4f8" stroke="#3498db" stroke-width="2" />
                <rect x="310" y="60" width="240" height="120" rx="10" fill="#e6f7e6" stroke="#27ae60" stroke-width="2" />
                <rect x="50" y="200" width="240" height="120" rx="10" fill="#f8e8e8" stroke="#e74c3c" stroke-width="2" />
                <rect x="310" y="200" width="240" height="120" rx="10" fill="#f8f5e8" stroke="#f39c12" stroke-width="2" />
                
                <!-- Headers -->
                <text x="170" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Physics & Engineering</text>
                <text x="430" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">Computer Graphics</text>
                <text x="170" y="220" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">Mathematics</text>
                <text x="430" y="220" text-anchor="middle" font-size="16" font-weight="bold" fill="#f39c12">Data Science</text>
                
                <!-- Content -->
                <text x="70" y="100" font-size="14" fill="#2c3e50">• Fluid dynamics</text>
                <text x="70" y="120" font-size="14" fill="#2c3e50">• Electromagnetism</text>
                <text x="70" y="140" font-size="14" fill="#2c3e50">• Continuum mechanics</text>
                <text x="70" y="160" font-size="14" fill="#2c3e50">• Finite element analysis</text>
                
                <text x="330" y="100" font-size="14" fill="#2c3e50">• 3D rendering</text>
                <text x="330" y="120" font-size="14" fill="#2c3e50">• Texture mapping</text>
                <text x="330" y="140" font-size="14" fill="#2c3e50">• Image warping</text>
                <text x="330" y="160" font-size="14" fill="#2c3e50">• Computer vision</text>
                
                <text x="70" y="240" font-size="14" fill="#2c3e50">• Coordinate transforms</text>
                <text x="70" y="260" font-size="14" fill="#2c3e50">• Optimization algorithms</text>
                <text x="70" y="280" font-size="14" fill="#2c3e50">• Differential geometry</text>
                <text x="70" y="300" font-size="14" fill="#2c3e50">• Multivariable calculus</text>
                
                <text x="330" y="240" font-size="14" fill="#2c3e50">• Neural networks</text>
                <text x="330" y="260" font-size="14" fill="#2c3e50">• Dimensionality reduction</text>
                <text x="330" y="280" font-size="14" fill="#2c3e50">• Normalizing flows</text>
                <text x="330" y="300" font-size="14" fill="#2c3e50">• Sensitivity analysis</text>
                
                <!-- Center icon -->
                <circle cx="300" cy="175" r="25" fill="#2c3e50" />
                <text x="300" y="182" text-anchor="middle" font-size="18" font-weight="bold" fill="white">J</text>
            </svg>
        </div>
    </section>
    
    <section id="conclusion">
        <h2>7. Conclusion and Summary</h2>
        
        <p>Throughout this tutorial, we've explored the Jacobian matrix and determinant from their definitions to their applications in various fields. Let's summarize the key points:</p>
        
        <div class="theorem">
            <h4>Key Concepts</h4>
            <ul>
                <li><strong>Jacobian Matrix</strong>: The matrix of all first-order partial derivatives of a vector-valued function, representing the best local linear approximation of the function.</li>
                <li><strong>Jacobian Determinant</strong>: Represents the factor by which volumes are scaled under the transformation, with the sign indicating whether orientation is preserved.</li>
                <li><strong>Critical Points</strong>: Points where the Jacobian determinant is zero, indicating that the transformation is not locally invertible.</li>
                <li><strong>Change of Variables</strong>: When changing variables in multiple integrals, we multiply by the absolute value of the Jacobian determinant to account for the distortion of volume elements.</li>
            </ul>
        </div>
        
        <h3>7.1 Step-by-Step Procedure for Working with Jacobians</h3>
        
        <div class="step">
            <h4>Calculating the Jacobian Matrix and Determinant</h4>
            <ol>
                <li>Identify the component functions of your vector-valued function</li>
                <li>Compute all partial derivatives of each component with respect to each input variable</li>
                <li>Arrange these derivatives into the Jacobian matrix</li>
                <li>Calculate the determinant of this matrix (for square Jacobians)</li>
                <li>Interpret the result based on your application context</li>
            </ol>
        </div>
        
        <h3>7.2 Common Patterns and Formulas</h3>
        
        <div class="note">
            <h4>Common Jacobian Determinants for Coordinate Transformations</h4>
            <ul>
                <li><strong>Polar Coordinates</strong>: $|\det(J)| = r$ for the mapping $(r, \theta) \mapsto (r\cos\theta, r\sin\theta)$</li>
                <li><strong>Cylindrical Coordinates</strong>: $|\det(J)| = r$ for the mapping $(r, \theta, z) \mapsto (r\cos\theta, r\sin\theta, z)$</li>
                <li><strong>Spherical Coordinates</strong>: $|\det(J)| = \rho^2 \sin\phi$ for the mapping $(\rho, \theta, \phi) \mapsto (\rho\sin\phi\cos\theta, \rho\sin\phi\sin\theta, \rho\cos\phi)$</li>
                <li><strong>Linear Transformation</strong>: $|\det(J)| = |\det(A)|$ for the mapping $\mathbf{x} \mapsto A\mathbf{x}$ where $A$ is a constant matrix</li>
            </ul>
        </div>
        
        <h3>7.3 Further Learning</h3>
        
        <p>If you're interested in exploring the Jacobian matrix and determinant further, consider these advanced topics:</p>
        
        <ul>
            <li><strong>Differential Forms</strong>: A more general framework for understanding integration on manifolds</li>
            <li><strong>Multivariate Chain Rule</strong>: Generalizations of the chain rule for more complex compositions</li>
            <li><strong>Riemannian Geometry</strong>: Using the Jacobian to understand geometric properties of curved spaces</li>
            <li><strong>Lie Groups and Lie Algebras</strong>: Studying continuous transformations and their infinitesimal generators</li>
            <li><strong>Computational Aspects</strong>: Efficient algorithms for computing Jacobians, particularly for large systems</li>
        </ul>
        
        <p>The Jacobian matrix and determinant are fundamental tools in multivariable calculus that bridge linear and nonlinear analyses. They provide a powerful framework for understanding how functions transform space, making them essential in both theoretical and applied mathematics.</p>
    </section>
</body>
</html> 