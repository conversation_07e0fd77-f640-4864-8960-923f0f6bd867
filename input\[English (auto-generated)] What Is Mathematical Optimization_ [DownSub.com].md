in 1987 the new york times front page
read
breakthrough in problem solving the
breakthrough here
refers to the invention of the interior
points method
an efficient algorithm for convex
optimization
and here you might wonder what is convex
optimization
and what is so important about it that
the headline refers to it as
problem solving and what makes the
discovery of this new algorithm
for solving convex optimization problems
so special
that it's made the headline of a journal
like the new york times
if you are asking yourselves these
questions you are in luck because this
is exactly
the topic of this video series
this series is broken into three parts
part one is a short introduction to the
active field of mathematics
that is optimization and some of its
applications
in part two we will dive deeper into the
topic of convex optimization
and we will introduce a fascinating
related concept
that is the principle of duality
the third and last part has a more
algorithmic component
there we will finally have all the
necessary ingredients
to overview the interior points method
for convex optimization
[Music]
as a bonus if you stick around until the
end
you will know what this ocean animation
represents and how it relates to the
topic of optimization
this video series is intended for anyone
who is curious about mathematics
and its applications it should be
accessible to a
wide range of audiences since it does
not require any specific background
beyond basic linear algebra and basic
calculus
it is suited for math and computer
science students
who want to know more about optimization
[Music]
but also researchers or engineers who
might have heard of optimization before
and want to know what they can gain from
it or how they can use it
in their own work or research projects
let me point out here that these videos
represent my
personal take on the topic of convex
optimization
so it might be a little different from
more traditional expositions that you
might find elsewhere
and it is by no means exhaustive but my
goal here is
to give a self-contained presentation
that is crisp
visual and focuses on building intuition
without getting bogged down with too
much mathematical details
which you can always check later if
needed
and with that being said it's time to
grab some coffee because we're about to
get started
before we talk about convex optimization
let's take a step back and
first define optimization what do we
mean when we
refer to some problem as an optimization
problem
well you have an optimization problem
whenever you are faced with a bunch of
options that you can choose from
where each option has a cost associated
with it
and your job is to make the optimal
choice or in other words
you need to pick the option with the
smallest possible cost
note that sometimes in optimization
there are situations where
we want to pick the option which
maximizes some given metric
often referred to as a reward rather
than minimize the cost
without cause of generality we can
simply multiply the reward
by -1 and convert a maximization problem
to a minimization one so for the rest of
this video
i'm going to follow a cost minimization
convention
note also that in this toy example there
are only 6
options in general however the number of
options could be huge
or even infinite a typical example where
the number of options is infinite is
when you try to pick a real number x
from the continuum of all real numbers
that minimizes some given function f
say differently you try to pick a scalar
x
that we refer to as a decision variable
such that
f of x is as small as possible
here x is a one-dimensional scale but it
could also be multi-dimensional
for example x could be a two-dimensional
vector x1 x2
a three-dimensional vector x1 x2 x3 etc
more formally
an optimization problem is given by
three ingredients
the first ingredient is a set where our
decision variable lives
often this is simply rn the
second ingredient is a cost function f
that we want to minimize
this function maps the set where our
decision variable lives
to r this function is sometimes known as
the objective function
the third and final ingredients are
constraints on the decision variable
constraints come in the form of equality
constraints
and inequality constraints
the equality and inequality constraints
together define what is known as the
feasible set or the set of possibilities
where you can pick
the decision variable x from
to recap optimization problems are often
written in this form
let us right away see some examples when
the cost function
f is a linear function and when the
functions
h i and gj that define the constraints
are
linear functions as well then we have
what is known as a
linear program linear programs are among
the problems that we understand the best
and as we will see later on it is
essential to understand what happens in
the simpler
and maybe idealistic linear word before
we move on
to more complicated nonlinear problems
[Music]
so in the next three minutes or so let
us try to build some geometric intuition
about linear programs to visualize
linear programs geometrically
we need to first understand how to
visualize linear functions
there are essentially two ways we could
go about visualizing
a linear function like this one either
we represent it with a hyperplane with
equation f of x equals zero
or with the vector c that is normal to
this hyperplane
the hyperplane representation is useful
for visualizing where the linear
function f
takes some given value like maybe f of x
equals
0 1 or 2. on the other hand
the vector representation is useful for
understanding the direction
that increases or decreases this linear
function
[Music]
for linear cost functions we prefer the
vector representation
the reason is that in the absence of
constraints and
in order to minimize a cost function f
what you need to do is simply move along
the vector minus c
as much as possible easy enough
so now let us see how to visualize
linear constraints
for linear functions that define linear
constraints
we prefer the hyperplane representation
a hyperplane divides the space in three
regions
a positive half space a negative valve
space
and a null region or the hyperplane
itself
now if you think of the whole space
where your decision variable lives as
this cube
then a linear constraint for example an
inequality constraint
will cut off a part of the cube and as
you add
more and more linear constraints more
and more parts of the cube
are going to be cut off and the region
of the space that we are left with
is our feasible region
moving on an optimization problem that
might be familiar to you
is linear regression or the least
squares problem
here we try to model the relationship
between
inputs a and outputs b and more
specifically
we try to fit a linear model so
our decision variable is a vector of
weight our objective function
measures the error in our linear model
and note that the objective function is
not a linear function but rather
a quadratic function and our constraints
are
well there aren't any constraints in
this example
there exists quite few variations of
this problem
for example we could replace the linear
model with a
more complicated function like a neural
network
in that case the regression problem
becomes a considerably harder problem
and this should already give you a hint
that some optimization problems
are easier to handle than others a third
example of
optimization problems i want to show you
is portfolio optimization
from the field of finance in portfolio
optimization
you are presented with a list of assets
like stocks
and your job is to pick which assets and
how much of each asset
you should buy here your objective is to
maximize returns and your constraints
are
that maybe you have a fixed budget that
you cannot exceed
and maybe you have a cap on the maximum
volatility
allowed for your portfolio
more generally any problem that involves
taking the best decision under
constraints
is an optimization problem engineering
advertising games are just some examples
in conclusion to this first part of this
series
i would like to point out that so far we
have focused on
taking a problem and formulating it as
an optimization problem
we have not yet discussed how you can
actually solve these problems
in fact optimization problems that have
very similar formulations
can require a completely different set
of techniques to solve
and we often need to design specialized
machinery
for each problem separately in the last
couple of decades however
something magical has happened we have
discovered that a
large family of optimization problems
namely
convex optimization problems can be
solved efficiently in a unified manner
once you recognize a given optimization
problem as convex
then you can apply an already
established and mature technology that
people have developed for convex
problems
almost as a black box in the next video
we will discuss convexity in more
details and see what makes this property
so attractive from an optimization point
of view
and in the third and last video we will
peek inside this black box
and uncover some fascinating ideas
behind it
and finally i would like to thank you
personally for making it this far in
this video and
see you next time

in the previous video we gave the  definition of optimization  and even formulated a few problems as  optimization problems  what we have not done however is discuss  how you go about solving  such problems what if i told you that  each one of these three optimization  problems can be solved easily with a few  lines of code  and note that the code is almost  identical for these three problems  even though the problems themselves look  very different  we won't go into the details of the code  or the algorithms that's for a later  video  but what we will do today is present the  common property  that allows us to solve all of these  problems in a unified fashion  and this property is called as you might  have guessed convexit  so what is convicted  if you open in a textbook you will find  the definition of complexity in  three different contexts the context of  sets  functions and optimization problems  let's go through them one by one the  first definition relates to sets  a set is convex if for any pair of  points  inside the set the segment between the  two points  falls entirely inside the set it's easy  to visualize how a set can fail to be  convex  so for example this set with a hole in  it  fails to become vex because the middle  point falls  outside of the stand for similar reasons  this set here also fails to be convex  examples of convex sets include planes  polyhedra aka intersection of half  spaces  bowls potato shapes etc you get the idea  the second definition relates to  functions  a function f is convex if it's epigraph  that is the region above the graph of  the function  is a convex set  intuitively this means that the function  is ball shaped  or curved upward if you will  convex functions include the function x  or any linear function for that matter  the function x squared the exponential  function  etc non-convex functions  include the function x cubed the sine  function  etc and i should mention here that a  nice way to construct new complex  functions from existing ones  is to take a function that is known to  be convex and  scale it with a positive constant or  take two convex functions  and add them together the third context  where one encounters convexity  is that of optimization problems an  optimization problem is said to be  convex if  the objective function f is convex the  functions gi  that give the inequality constraints are  convex  and the functions h i are linear you  might wonder why we require the  functions h i to be linear and not just  convex the reason for that is simple we  can replace  an equality h equals zero with two  inequalities  h smaller than zero and minus h smaller  than zero  and the only functions for which h and  minus h  are both convex are the linear ones  now that we get the formalities out of  the way what is convexity really  and why do we care about it i would  argue that the crucial thing that  convexity allows us to do  is apply the principle of duality  the principle of duality is so  fundamental and so beautiful  that if there was only one thing you  should remember from this video  it should be this one duality is the  ability to view a mathematical concept  from two different perspectives a primal  one and a dual one  let's be more concrete and see what  duality means  in the three context where convexity  appears sets  functions and optimization problems  let's start with convex sets the  definition i gave before was internal  i took points inside the set and i  required that the segment between them  be inside the set too there is a  dual external definition too now instead  of taking a point inside my set  i take a hyper plane that supports my  set  this simply means that i take a  hyperplane in such a way that my set  falls entirely  in the positive side of this hyperplane  with the definition of supporting  hyperplanes in mind the  dual definition of convex sets is as  follows  a set is convex if when you consider the  intersection  of the positive regions of all of its  supporting hyperplanes  you recover the set itself  let that sink in for a moment and let's  just take some time  to appreciate the ramifications of this  definition to optimization  according to this dual definition convex  sets  are the ones defined with possibly  infinitely many linear inequalities  so somehow convex sets through the lens  of the duality principle  are polyhedra with possibly infinitely  many faces  and most of the things that we love  about the linear world  that makes things simpler extend to the  convex world with little effort  this actually reminds me of the quote of  rockefeller  one of the leading figures in  optimization theory who said  in fact the great watershed in  optimization  isn't between linearity and  non-linearity but  between convexity and non-convexity  what he means by that is that for a long  time we thought that linear problems are  easy  and nonlinear problems are hard but as  it turns  out and this is a more and more commonly  held belief  what actually makes optimization  problems easy is convexity  which generalizes the notion of just  linearity  back to duality the duality principle  can be more clearly seen in the context  of complex functions  as it lets you extrapolate the local  behavior of a function  to some global property of this function  and this statement will make more sense  in a second  if we want to talk about local behavior  of a function  we have to mention taylor's  approximation theorem  in its simplest form this theorem states  that  if you zoom in on a function around the  point x  then it would look a lot like a linear  function or in other words  the line or in higher dimension the  hyperplane given by this equation  is a good approximation of the graph of  f around the point x  and this hyperplane is usually called a  tangent hyperplane  the dual definition of convex functions  is as follows  f is convex if and only if its graph  is always above its tangent hyperplanes  the ramifications of this new definition  are huge  let's say you find the point x where the  gradient of f  is equal to zero then the tangent  hyperplane at that point is  horizontal and since convex functions  will always be above their tangent  planes  this mean that the point x is actually a  global minimizer of f  and this already gives us a way to solve  and constrain minimization problems  take the function x squared plus one as  an example this function is convex  and its gradient or derivative is two  times x  so when x equals zero the derivative is  equal to zero  so x equals zero is where this function  attains its minimum  another example of an constraint  optimization problem  is the least squares problem we saw in  the last video  to find its minimum we simply set the  gradient to 0  and then we solve for x  the third and last example i wanted to  show you is that of a non-constant  linear function like this one the  gradient of this function is a constant  given by a non-zero constant vector  and since this gradient can never vanish  we can conclude that this linear  function  doesn't have a minimizer and indeed it's  not hard to see that  non-constant linear functions are  unbounded  [Music]  notice how in all of the examples we  went from an  information about the gradient which is  local  to a global property of the function  that is that  x is a global minimizer of the function  and all of that  thanks to convexity a word of warning  here  in general not all convex optimization  problems  are as easy to solve are these three  for one the objective function can be  more complicated  so much so that it would not be possible  to solve  the gradient equation directly and in  that case  several methods have been developed to  solve the gradient equation  chief amongst them is newton's method  and  i have already made a video about that  if you're interested  a more serious problem is that  optimization problems often  will have constraints in the next video  we will explore the topic of  duality in convex optimizations further  and derive the  kkd conditions that generalize  the condition that grad of f equals zero  to optimization problems with cancer  friends  and we will see a very nice solution  technique to solve for these conditions  that is known as the interior  appointment  and that will be the end of this second  video thank you very much for watching  and  see you next time  [Music]  so

in the previous video we have seen what
duality means for convex sets
and what duality means for convex
functions
in this video we will see what duality
means for convex optimization problems
and we will see how it can be helpful
for designing efficient algorithms
for solving these problems
[Music]
let's work with an example this time
imagine you are the captain of a ship
in the middle of the ocean using some
sophisticated equipment
you know that the rain is coming from
some direction c for example
c equals the vector 1 1. so you decide
to steer the ship
in the opposite direction as much as
possible
you also remember that the sea is
infested with sharks
except for a safe circular region
of radius one unit this now becomes an
optimization problem
our decision variable is the
two-dimensional vector x
that represents the position of the ship
our objective is
c transpose x or in other words x one
plus x
and we have a single constraint which is
that we need to pick x
from the unit disk
note that this is a convex optimization
problem because the objective function
is linear
and in particular convex and the
constraint is given by
a convex function one thing we would
love to do
is somehow get rid of the constraint
indeed
if this constraint did not exist then we
would just have an unconstrained
convex optimization problem and we have
discussed those in detail
in the previous video and the nice way
to visualize what is going on
in that case is to imagine that the
level of the c
is tilted according to the objective
function
in such a way that low sea levels
correspond to
low objective values and high sea levels
to high objective values
it becomes visually clear that the
answer would simply be
to steer the ship in the direction of
minus c
which matches our intuition so keep this
analogy in mind
for later because it will be useful
but life is unfair and you often need to
deal with constraints
otherwise you become a snack for some
hungry sharks
so one way to get rid of a constraint is
to move it to the objective function via
what is called a penalty function
so what is a penalty function you ask
well
it is simply a function that penalizes
you for not satisfying a constraint
and a natural choice for this penalty
function
is a function that takes the value of 0
when its argument is negative
and the value plus infinity when its
argument is positive
let's call this particular penalty
function the zero infinity penalty for
later references now if you pick an x
that satisfies the constraint
then the penalty term will evaluate to
zero it is as if the penalty term
simply was not there but if you pick an
x that does not satisfy the constraint
then the penalty term will evaluate to
plus infinity
which is really the worst possible
outcome you can hope for
as far as minimization problems are
concerned visually
this penalty function raises the sea
level outside of our disk of safety
to plus infinity so it makes it no
longer
desirable to steer the shape outside of
this circle
even if the original objective value was
smaller there
and this penalty term is almost perfect
we generally get rid of the constraint
without affecting the optimal value of
the minimization problem
the price we pay however is that our
objective function now
can take the value plus infinity and
while we can just replace it with some
really large value like
a million we would still have the issue
that our objective function
has become discontinuous and it would no
longer make sense to take the gradient
of this function for example
one thing we could try here is
approximate this penalty function
with another function that is smooth and
does not take infinite values
the simplest thing we could try here is
to pick a linear function as our penalty
function
something like u times y where the slope
u
is a non-negative number visually
you can see how this penalty is a smooth
approximation
of the zero infinity penalty function
how does this linear penalty work
well the idea is simple if you pick an x
that does not satisfy the constraint
this penalty term will evaluate not to
infinity this time
but to a quantity that is proportional
to how much
you violated the constraint and if you
pick an x that satisfies the constraint
believe it or not you actually get
rewarded
and as much as we like it when we get
rewarded for doing the right thing
and penalize for violating constraints
it is less than ideal in this scenario
because it affects the optimal value of
our minimization problem
and the amount by which it affects the
optimal value
depends on the value of the slope you
clearly for any value of u this linear
approximation is
rather poor but notice that if you
consider
all of the u's and take the maximum
of the corresponding linear penalties
you can check by looking at the plot
that you recover the original zero
infinity penalty function
which is pretty neat right so let's try
to use this maximum
as our penalty function and note that
now
we can take the maximum outside since
x1 plus x2 does not depend on you
what we have now is what is called a min
max problem
and a nice interpretation of what's
going on here is
to view the min max problem as a two
persons game
we have an x player and a u player
the x player picks an x to try to make
this quantity small
while the u player will try to penalize
us if we don't satisfy the constraints
and pku that makes this term big
[Music]
and in games like these it is important
to know who plays first
indeed if given the choice you will
always want to go second
because the second player gets the
benefit of knowing what the first player
did
so here naturally the x player might
protest that it is not fair that he
always goes first
and wonders what happens if the other
player you
goes first for a change well if you goes
first
then the x player's job becomes much
simpler
he just needs to minimize this quantity
and i emphasize here that this quantity
is a convex function
without any constraints so x can just
take the gradient of this quantity
set it to 0 and solve for x
and now that we know what the response
of x should be
we can plug that back in
maximize with respect to u
[Music]
and get the solution x 1 equals x 2
equals
minus square root of 2 over 2. it is
important to note here that
what we have just done is not solve the
original problem
but a variation of it the problem that
we solved is actually called the
dual problem while the original problem
is called the primal problem
and there are a lot of benefits to
considering this dual problem
but before we talk about that let's just
take a step back from this example for a
bit
and see what the dual problem is for
convex optimization problems in general
let us now consider a general convex
optimization problem
and inspired by the example we just saw
let's try to derive its dual
the first step is to get rid of the
constraints
for that multiply each function in the
constraints
with a scalar that represents the slope
of our penalty function
and move these functions from the
constraints to the objective function
and similar to what we did in the
example we are going to take the maximum
over the scalars uis and vi's
for inequality constraints like the ones
we had in the example
we really want the cost to be positive
while for equality constraints
where we want to penalize positive and
negative values alike
we will allow the slope to be positive
or negative
and now to get the dual problem we pull
the maximum outside
and we switch the order of the min and
the max
this quantity here is known in the
literature
as the lagrangian of the optimization
problem
and the dual problem is referred to as
the lagrangian du
and we will go back to the lagrangian in
a second
keywords are in order at this point
first
the dual problem has one variable for
each constraint
of the primer these can be interpreted
as the cost
or price for violating that constraint
second the dual problem gives us a lower
bound
on the optimal value of the primal
problem this is because in the dual
problem
and unlike in the primal problem the x
player
which tries to make our objective
function small goes second
and therefore has the advantage and
finally
and what is more interesting is that
under some mild assumptions
strong duality holds this means that the
optimal value of the primal and the dual
problem
actually coincide so you can solve
whichever you find
easiest and you will find the right
answer
the assumptions i refer to are a bit
technical so i won't list them here
just know that they are usually
satisfied and they are satisfied for our
toe example for instance
anyway we won't worry too much about
that and we're just going to assume from
now on
that strong duality holds
and now that we have derived the dual
problem let's see how it can be useful
for solving convex optimization problems
and this will lead us to the next theme
of this video which is the
celebrated kkt conditions let's consider
our friend the lagrangian again remember
that the x player
tries to minimize this quantity so at
the optimal solution x
the gradient with respect to x better be
zero
this is a necessary condition that any
optimal solution
to the optimization problem should
satisfy
think of it as a generalization of the
condition that grad of f
equals zero for any minimizer of an
unconstrained optimization problem
[Music]
there is a nice geometric intuition
behind this condition
and let's see a visual explanation of
what is happening here
and for simplicity let's consider a case
where we have only one inequality
just like the example we saw before in
which case this condition is saying that
grad f and grad g are inversely
proportional to each other
let us delimit the feasible region given
by g
with a green line and consider an
arbitrary point x
inside this region at this point x
let's draw the gradient of g and the
gradient of f
and remember what gradients represent
when you follow the opposite direction
to the gradient of a function
the value of that function will decrease
at least locally
so we know that in this green rectangle
the value of g
will remain smaller than zero and
therefore
we can safely move x inside of this
region
without fear of violating the constraint
similarly if you nudge x to fall in the
yellow region
you will reduce and hence improve the
objective function f
so if the yellow and green region
intersect
it means that you can nudge your x a bit
stay feasible
and improve the objective value so such
an x cannot be optimal
said differently if x were optimal then
necessarily the two regions should not
intersect
which can only happen if the gradient of
f and g
are inversely proportional to each other
at this point it is a good idea to recap
what we have just seen
we have seen that if x is an optimal
solution
then it should satisfy this gradient
condition
and you might wonder if this condition
is also sufficient
in the sense that if for some point x
you succeeded in finding a scalar u
that makes this condition hold then that
makes x an optimal solution
after all for unconstrained optimization
problems
grad f equals 0 guarantees that x is an
optimal solution
unfortunately it turns out that this
implication is not quite true
and you need additional assumptions on x
and u
to make it old on the bright side
however
these additional assumptions are quite
natural
and they are as follows first and
foremost
we need x to be feasible second the
scalar u should be negative
and last that the penalty term u times g
should be zero only this last condition
is new
and the meaning of this condition is
that at the end of the day
if you choose x and u properly the
penalty term u times g
should not affect the optimal value of
our minimization problem
and hence should be zero these
conditions together
are known as the kkt conditions and
they are extremely important in convex
optimization
the big pro of kkt is that you
essentially reduce
solving an optimization problem to
solving a bunch of equations and
inequalities
and as you can imagine this is extremely
useful
for designing general purpose
optimization solvers
you can try to solve for these
conditions by hand for small examples
like the one we saw earlier
but that easily becomes complicated for
more involved problems so we need to
develop
a more sophisticated machinery
and that is exactly where the interior
point method comes in
[Music]
as i said earlier you can view the
interior point method
as a way to solve the kkkt conditions
there are two big insights or solution
techniques
behind the interior point method and
these techniques are actually useful
for a lot of other problems in
mathematics beyond just optimization
the first insight is that we are not
going to solve the kkt conditions
exactly
but we are going to solve a modified or
perturbed version of these conditions
that we call kkt of t where t
is a positive parameter that controls
the degree of this perturbation and
while we're at it
let's call the solution to these
perturbed equations x of t
and call the solution to the original
equations
x star the perturbation works as follows
we keep all of the kkt conditions the
same
except for the last equation in the
right hand side of this equation
we replace zero with a negative number
minus t
hopefully when t is small x of t and x
star will be close
or in other terms as the perturbation
parameter t goes to zero
x t converges to x star
so far we have talked about how to do
this perturbation
but not why we do this perturbation in
the first place
the main reason behind it is that and as
we will see now
the modified kkt conditions are much
easier to solve
if u times g equals minus t then we
immediately get the value of u
from the value of x as u equals minus t
over g of x
let's plug this value of u back in the
gradient equation
as it turns out this quantity is
actually
the gradient of f minus t of log minus g
and you might want to pause the video
here and work out the gradient by hand
to convince yourselves of this fact
now in order to solve this gradient
equation
we just need to minimize this function
indeed at the point where the minimum is
attained we know that the gradient
must be zero and as a bonus note that if
you succeed in minimizing this function
then the solution that you will find
will automatically satisfy the
constraint
g of x smaller than zero because if
g were positive log minus g would be
plus infinity
and as a bonus of this bonus you will be
automatically positive
so from now on we are in unconstrained
minimization territory
and you can use something like newton's
method to perform this minimization
there is a nice geometric interpretation
behind this minimization problem
it is as if the penalty term for
violating the constraint
was given by the log function and the
scalar t
controls the degree of this penalty
[Music]
the issue here is that we want to take t
to be as small as possible
so that the penalty term doesn't affect
our objective value too much
but taking t to be too small causes
issues
as it makes t times log behave like the
discontinuous zero infinity penalty
function
we saw earlier and practically speaking
what is going to happen is
that newton's method will be extremely
slow to converge
and will cause numerical issues the
second insight
alleviates this issue in a very clever
way
the second insight is that we don't have
to take t to be small right away
but start with a big t and make it
smaller and smaller
let's see why this is a useful strategy
if t
is a very big number then the log term
dominates
and it is as if the first term did not
exist
and because log is increasing this is
the same as
minimizing g of x which you can do by
you guessed it
newton's method in our toy example
this step would give the center of the
disk and
more generally the point you get at this
step is called the analytic center of
the feasible region
once you have a solution for a given t
you can try to solve for a
slightly smaller t prime say t prime
equals
0.9 times t we will again use newton's
method
but now the difference is that we will
specify x of t
as a starting point for newton's method
the one thing you need to know about
newton's method is that
if you initialize it with a point that
is close
to the optimal solution then it will run
super fast
and by design we have that x of t
is close to x of t prime so by
initializing newton from x of t
computing
x of t prime will be easy and
the interior point method will keep
iterating and making t
smaller and smaller in a similar fashion
until a t that is deemed close enough to
zero
is reached so to recap
the two insights we just saw the
interior
point method when applied to a convex
constraint problem
will move constraints to the objective
function via a log penalty function
multiplied by scalar t it will start
with a large t
effectively finding the analytic center
of the feasible set
then it will work its way to t equals 0
by going through the path
formed by the x's of t this path is
called the central path
the central path falls in the interior
of the feasible region
and this is how the interior point
method earned its name
and the end of the central path is the
solution that is eventually returned
by the interior point method
we have now reached the end of this
video series
to conclude let me say that today we
have barely scratched the surface of
convex optimization
there is a lot more to say about duality
and there is a lot more to say about the
interior point method
but hopefully this video has helped to
develop the necessary intuition
so that in the future if you read about
convex optimization in a wikipedia page
or research paper
you're not completely lost and if you
want to dive deeper into the topic
a natural reference i encourage you to
look at is this book by boyd and van der
bee
that i have used personally for
preparing this video and the pdf version
of this book is
freely available online and as always
thank you very much for following along
and see you next time