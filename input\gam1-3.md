 Thissectionshowshowtheparameters,β,ofthelinearmodel
 µ=Xβ, y∼N(µ,Inσ2)
 canbeestimatedbyleast squares. It isassumedthatXisafull rankmatrix,with
 nrowsandpcolumns.Itwillbeshownthattheresultingestimator, ˆ β, isunbiased,
 has the lowestvarianceofanypossible linearestimatorofβ, andthat, giventhe
 normalityofthedata, ˆ β∼N(β,(XTX)−1σ2).Resultsarealsoderivedforsetting
 confidencelimitsonparametersandfortestinghypothesesaboutparameters—in
 particularthehypothesisthatseveralelementsofβaresimultaneouslyzero.
 Inthissectionit is importantnot toconfusethelengthofavectorwithitsdi
mension.Forexample(1,1,1)Thasdimension3andlength√3.Alsonotethatno
 distinctionhasbeenmadenotationallybetweenrandomvariablesandparticularob
servationsof thoserandomvariables: it isusuallyclear fromthecontextwhichis
 meant.
12
 LINEARMODELS
 1.3.1 Least squares estimation of β
 Point estimates of the linear model parameters, β, can be obtained by the method of
 least squares, that is by minimizing
 n
 S =
 i=1
 (yi − µi)2,
 with respect to β. To use least squares with a linear model written in general matrix
vector form, first recall the link between the Euclidean length of a vector and the sum
 of squares of its elements. If v is any vector of dimension, n, then
 n
 v 2 ≡vTv≡
 Hence
 i=1
 v2
 i.
 S = y−µ2= y−Xβ 2.
 Since this expression is simply the squared (Euclidian) length of the vector y −Xβ,
 its value will be unchanged if y − Xβ is rotated. This observation is the basis for a
 practical method for finding ˆ
 β, and for developing the distributional results required
 to use linear models.
 Specifically, like any real matrix, X can always be decomposed
 X=Q R
 0 =QfR
 (1.5)
 where R is a p ×p upper triangular matrix,† and Q is an n × n orthogonal matrix,
 the first p columns of which form Qf (see B.6). Recall that orthogonal matrices
 rotate or reflect vectors, but do not change their length. Orthogonality also means
 that QQT = QTQ=In.ApplyingQT to y−Xβ impliesthat
 y−Xβ 2=QTy−QTXβ 2= QTy− R
 0 β
 2
 .
 Writing QTy = f
 r ,wheref is vector of dimension p, and hence r is a vector of
 dimension n − p, yields
 y−Xβ 2= f
 r − R
 0 β
 2
 = f−Rβ 2+ r 2.‡
 The length of r does not depend on β, while f − Rβ 2 can be reduced to zero by
 choosing β so that Rβ equals f. Hence
 ˆ
 β =R−1f
 †
 ‡
 P
 i.e., Ri,j = 0 if i > j.
 If the last equality isn’t obvious recall that x2 = P
 i x2
 i , so if x = 
(1.6)
 v
 w , x2=P
 iv2
 i+
 i w2
 i = v 2+w2.
THETHEORYOFLINEARMODELS
 13
 is the least squares estimator of β. Notice that r 2 = y−Xˆ β 2, the residual sum
 of squares for the model fit.
 1.3.2 The distribution of ˆ
 β
 The distribution of the estimator, ˆ
 β, follows from that of QTy. Multivariate normal
ity of QTy follows from that of y, and since the covariance matrix of y is Inσ2, the
 covariance matrix of QTy is
 VQTy =QTInQσ2 =Inσ2.
 Furthermore,
 i.e., we have that
 E f
 r =E(QTy)=QTXβ= R
 0 β
 ⇒E(f) =RβandE(r) = 0,
 f ∼N(Rβ,Ipσ2) and r ∼ N(0,In−pσ2)
 with both vectors independent of each other.
 Turning to the properties of ˆ
 β itself, unbiasedness follows immediately:
 E(ˆ β) = R−1E(f) = R−1Rβ = β.
 Since the covariance matrix of f is Ipσ2, it follows that the covariance matrix of ˆ
 Vˆ β = R−1IpR−Tσ2 = R−1R−Tσ2.
 Furthermore, since ˆ
 β is
 (1.7)
 β is just a linear transformation of the normal random variables
 f, it must have a multivariate normal distribution,
 ˆ
 β ∼N(β,Vˆ β).
 The foregoing distributional result is not usually directly useful for making infer
ences about β, since σ2 is generally unknown and must be estimated, thereby intro
ducing an extra component of variability that should be accounted for.
 1.3.3 (ˆ βi − βi)/ˆ σˆ βi 
∼ tn−p
 Since the elements of r are i.i.d. N(0,σ2), the ri/σ are i.i.d. N(0,1) random vari
ables, and hence
 1
 σ2
 r 2 = 1
 σ2 
n−p
 i=1
 r2
 i ∼ χ2
 n−p.
 The mean of a χ2
 n−p r.v. is n − p, so this result is sufficient (but not necessary: see
 exercise 7) to imply that
 ˆ
 σ2 = r 2/(n−p)
 (1.8)
14
 LINEARMODELS
 is an unbiased estimator of σ2.§ The independence of the elements of r and f also
 implies that ˆ
 β and ˆ σ2 are independent.
 Now consider a single parameter estimator, ˆ βi, with standard deviation, σˆ βi
 ,
 given by the square root of element i,i of Vˆ β. An unbiased estimator of Vˆ β is
 ˆ
 Vˆ β = Vˆ βˆ σ2/σ2 = R−1R−Tˆ σ2, so an estimator, ˆ σˆ βi
 , is given by the square root of
 element i,i of ˆ Vˆ β, and it is clear that ˆ σˆ βi 
= σˆ βi
 ˆ σ/σ. Hence
 ˆ
 βi −βi
 ˆ
 σˆ βi
 ˆ
 =
 βi −βi
 σˆ βi 
ˆ
 σ/σ =
 (ˆ βi − βi)/σˆ βi
 1
 σ2 
r 2/(n−p)
 ∼tn−p
 ∼ N(0,1)
 χ2 n−p/(n − p)
 (where the independenceof ˆ βi and ˆ σ2 has been used). This result enables confidence
 intervals for βi to be found, and is the basis for hypothesis tests about individual βi’s
 (for example, H0 : βi = 0).
 1.3.4 F-ratio results I
 Sometimes it is useful to be able to test H0 : Cβ = d, where C is q × p and rank
 q (< p). Under H0 we have Cˆ β − d ∼ N(0,CVˆ βCT,), from basic properties of
 the transformation of normal random vectors. Forming a Cholesky decomposition
 LTL = CVˆ βCT (see B.7), it is then easy to show that L−T(Cˆ β − d) ∼ N(0,I),
 so,
 (Cˆ β −d)T(CVˆ βCT)−1(Cˆ β −d) =
 (Cˆ β −d)TL−1L−T(Cˆ β−d) ∼
 As in the previous section, plugging in ˆ
 q
 i=1
 N(0,1)2 ∼ χ2
 q.
 Vˆ β = Vˆ βˆ σ2/σ2 gives the computable test
 statistic and its distribution under H0:
 1
 q
 (Cˆ β −d)T(Cˆ Vˆ βCT)−1(Cˆ β − d) = σ2
 =
 qˆ σ2 
(C ˆ
 β −d)T(CVˆ βCT)−1(Cˆ β−d)
 (Cˆ β −d)T(CVˆ βCT)−1(Cˆ β −d)/q
 1
 σ2 
r 2/(n−p)
 ∼ χ2
 q/q
 χ2 n−p/(n − p) ∼ Fq,n−p. (1.9)
 This result can be used to compute a p-value for the test.
 1.3.5 F-ratio results II
 An alternative F-ratio test derivation is also useful. Consider testing the simultane
ous equality to zero of several model parameters. Such tests are useful for making
 §Don’t forget that r 2 = y −Xˆ β 2.
THETHEORYOFLINEARMODELS
 15
 inferences about factor variables and their interactions, since each factor or interac
tion is typically represented by several elements of β. More specifically suppose that
 the model matrix is partitioned X = [X0 : X1], where X0 and X1 have p − q and
 q columns, respectively. Let β0 and β1 be the corresponding sub-vectors of β, and
 consider testing
 H0 : β1 = 0 versus H1 : β1=0.
 Any test involving the comparison of a linear model with a simplified null version
 of the model can be written in this form, by re-ordering of the columns of X or by
 re-parameterization. Now
 QTX0 = R0
 0 
where R0 is the first p − q rows and columns of R (Q and R are from (1.5)). So
 rotating y − X0β0 using QT implies that
 y−X0β0 2 =QTy−QTX0β0 2= f0−R0β0 2+ f1 2+ r 2
 where f has been partitioned so that f = f0
 f1
 (f1 being of dimension q). Hence
 f1 2 is the increase in residual sum of squares that results from dropping X1 from
 the model (i.e. setting β1 = 0).
 Now, since E(f) = Rβ andRisuppertriangular,then E(f1) = 0 if β1 = 0 (i.e.
 if H0 is true). Also, we already know that the elements of f1 are independent normal
 r.v.s with variance σ2. Hence, if H0 is true, f1 ∼ N(0,Iqσ2) and consequently
 1
 σ2
 f1 2 ∼χ2
 q.
 So, forming an ‘F-ratio statistic’, F, assuming H0, and recalling the independenceof
 f and r we have
 F = f1 2/q
 ˆ
 σ2
 1
 σ2 
f1 2/q
 =
 1
 σ2 
r 2/(n−p) ∼
 χ2
 q/q
 χ2 n−p/(n − p) ∼ Fq,n−p
 which can be used to compute a p-value for H0, thereby testing the significance of
 model terms. Notice that for comparingany null model with residual sum of squares,
 RSS0, to a full model with residual sum of squares, RSS1, the preceding derivation
 holds, but the test statistic can also be computed (without any initial re-ordering or
 re-parameterization) as
 F = (RSS0 −RSS1)/q
 RSS1/(n −p) .
 In slightly more generality, if β is partitioned into sub-vectors β0,β1 ...,βm
 (each usually relating to a different effect), of dimensions q0,q1,...,qm, then f can
 also be so partitioned, fT = [fT 0,fT 1,...,fT m], and tests of
 H0 : βj = 0versusH1 : βj= 0
16
 LINEARMODELS
 are conducted using the result that under H0
 F = fj 2/qj
 ˆ
 σ2
 ∼Fqj,n−p,
 with F larger than this suggests, if the alternative is true. This is the result used to
 draw upsequential ANOVAtables for a fitted model, of the sort producedby a single
 argument call to anova in R. Note, however, that the hypothesis test about βj is
 only valid in general if βk = 0 for all k such that j < k ≤ m: this follows from
 the way that the test was derived, and is the reason that the ANOVA tables resulting
 from such procedures are referred to as ‘sequential’ tables. The practical upshot is
 that, if models are reduced in a differentorder,the p-valuesobtained will be different.
 The exception to this is if the ˆ
 βj’s are mutually independent, in which case all the
 tests are simultaneously valid, and the ANOVA table for a model is not dependenton
 the order of model terms: such independent ˆ
 βj’s usually arise only in the context of
 balanced data, from designed experiments.
 Notice that sequential ANOVA tablesare veryeasy to calculate: once a modelhas
 been fitted by the QR method,all the relevant ‘sums of squares’ are easily calculated
 directly from the elements of f, with r 2 providing the residual sum of squares.
 1.3.6 The influence matrix
 One matrix that will feature extensively in the discussion of GAMs is the influence
 matrix (or hat matrix) of a linear model. This is the matrix which yields the fitted
 value vector, ˆ µ, when post-multiplied by the data vector, y. Recalling the definition
 of Qf, as being the first p columns of Q, f = QT
 fy and so
 ˆ
 β =R−1QT
 fy.
 Furthermore ˆ µ = Xˆ β and X = QfR so
 ˆ
 µ=QfRR−1QT
 fy = QfQT
 fy
 i.e., the matrix A ≡ QfQT
 f is the influence (hat) matrix such that ˆ µ = Ay.
 The influence matrix has a couple of interesting properties. Firstly, the trace of
 the influence matrix is the number of (identifiable) parameters in the model, since
 tr (A) = trQfQT
 f = tr QT
 fQf = tr(Ip) = p.
 Secondly, AA = A, a property known as idempotency. Again the proof is simple:
 AA=QfQT
 fQfQT
 f = QfIpQT
 f = QfQT
 f = A.
 1.3.7 The residuals, ˆ ǫ, and fitted values, ˆ µ
 The influence matrix is helpful in deriving properties of the fitted values, ˆ µ, and
 residuals, ˆ ǫ. ˆ
 µ is unbiased, since E(ˆ µ) = E(Xˆ β) = XE(ˆ β) = Xβ = µ. The
THETHEORYOFLINEARMODELS
 17
 covariance matrix of the fitted values is obtained from the fact that ˆ µ is a linear
 transformation of the random vector y, which has covariance matrix Inσ2, so that
 Vˆ µ = AInATσ2 = Aσ2,
 by the idempotence (and symmetry) of A. The distribution of ˆ µ is degenerate multi
variate normal.
 Similar arguments apply to the residuals.
 ˆ
 ǫ =(I−A)y,
 so
 E(ˆ ǫ) = E(y) −E(ˆ µ) = µ−µ = 0.
 Proceeding as in the fitted value case we have
 Vˆ ǫ = (In −A)In(In −A)Tσ2 = (In −2A+AA)σ2 =(In −A)σ2.
 Again, the distribution of the residuals will be degenerate normal. The results for the
 residuals are useful for model checking, since they allow the residuals to be stan
dardized so that they should have constant variance if the model is correct.
 1.3.8 Results in terms of X
 The presentation so far has been in terms of the method actually used to fit linear
 models in practice (the QR decomposition approach¶), which also greatly facilitates
 the derivation of the distributional results required for practical modelling. However,
 for historical reasons, these results are more usually presented in terms of the model
 matrix, X, rather than the components of its QR decomposition. For completeness
 some of the results are restated here, in terms of X.
 Firstly consider the covariance matrix of β. This turns out to be (XTX)−1σ2,
 which is easily seen to be equivalent to (1.7) as follows:
 Vˆ β = (XTX)−1σ2 = RTQT
 fQfR−1σ2 = RTR−1σ2 =R−1R−Tσ2.
 Theexpressionforthe least squaresestimatesis ˆ
 β =(XTX)−1XTy,whichisequiv
alent to (1.6):
 ˆ
 β =(XTX)−1XTy =R−1R−TRTQT
 fy =R−1QT
 fy =R−1f.
 Given this last result, it is easy to see that the influence matrix can be written:
 A=X(XTX)−1XT.
 These results are of largely historical and theoretical interest: they should not gener
ally be used for computational purposes, and derivation of the distributional results
 is much more difficult if one starts from these formulae.
 ¶Some software still fits models by solution of XTXˆ
 β =XTy,butthis isless computationally stable
 than the orthogonal decomposition method described here, although it is a bit faster.
18
 LINEARMODELS
 1.3.9 The Gauss Markov Theorem: What’s special about least squares?
 How good are least squares estimators? In particular, might it be possible to find
 better estimators, in the sense of having lower variance while still being unbiased?
 The Gauss Markov theoremshows that least squares estimators have the lowest vari
ance of all unbiased estimators that are linear (meaning that the data only enter the
 estimation process in a linear way).
 Theorem 1. Suppose that µ ≡ E(Y) = Xβ and Vy = σ2I, and let ˜ φ = cTY be
 any unbiased linear estimator of φ = tTβ, where t is an arbitrary vector. Then:
 var(˜ φ) ≥ var(ˆ φ)
 where ˆ φ = tT ˆ β, and ˆ β = (XTX)−1XTY istheleastsquaresestimator ofβ. Notice
 that, since t is arbitrary, this theorem implies that each element of ˆ
 β is a minimum
 variance unbiased estimator.
 Proof. Since ˜ φ is a linear transformation of Y, var(˜ φ) = cTcσ2. To compare vari
ances of ˆ φ and ˜ φ it is also useful to express var(ˆ φ) in terms of c. To do this, note that
 unbiasedness of ˜ φ implies that
 E(cTY) = tTβ ⇒cTE(Y) = tTβ ⇒cTXβ =tTβ ⇒cTX=tT.
 So the variance of ˆ φ can be written as
 var(ˆ φ) = var(tT ˆ
 β) =var(cTXˆ β).
 This is the variance of a linear transformation of ˆ
 is (XTX)−1σ2, so
 β, and the covariance matrix of ˆ β
 var(ˆ φ) = var(cTXˆ β) = cTX(XTX)−1XTcσ2 = cTAcσ2
 (where A is the influenceor hat matrix). Now the variancesof the two estimators can
 be directly compared, and it can be seen that var(˜ φ) ≥ var(ˆ φ) iff
 cT(I −A)c ≥ 0.
 This condition will always be met, because it is equivalent to:
 {(I −A)c}T(I −A)c ≥ 0
 by the idempotency and symmetry of A and henceof (I−A), but this last condition
 is saying that a sum of squares can not be less than 0, which is clearly true.
 Notice that this theorem uses independence and equal variance assumptions, but
 does not assume normality. Of course there is a sense in which the theorem is in
tuitively rather unsurprising, since it says that the minimum variance estimators are
 those obtained by seeking to minimize the residual variance