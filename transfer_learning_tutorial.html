<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Learning: A Comprehensive Tutorial</title>
    
    <!-- MathJax 3 for LaTeX math rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        MathJax = {
            tex: {
                inlineMath: [['\\(', '\\)']],
                displayMath: [['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <!-- Plotly.js for interactive graphs -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .toc {
            background: #f8f9fa;
            border-left: 4px solid #4CAF50;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
        }
        
        .toc h2 {
            color: #4CAF50;
            margin-bottom: 15px;
        }
        
        .toc ul {
            list-style: none;
        }
        
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .toc li:before {
            content: "→";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .toc a {
            color: #333;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .toc a:hover {
            color: #4CAF50;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #4CAF50;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .section h3 {
            color: #34495e;
            font-size: 1.6em;
            margin: 25px 0 15px 0;
            padding-left: 15px;
            border-left: 3px solid #3498db;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        
        .highlight-box:before {
            content: "💡";
            position: absolute;
            top: -10px;
            left: 20px;
            background: white;
            padding: 0 10px;
            font-size: 1.2em;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border: 1px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        
        .warning-box:before {
            content: "⚠️";
            position: absolute;
            top: -10px;
            left: 20px;
            background: white;
            padding: 0 10px;
            font-size: 1.2em;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #4CAF50;
        }
        
        .timeline {
            position: relative;
            padding: 20px 0;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #4CAF50;
            transform: translateX(-50%);
        }
        
        .timeline-item {
            display: flex;
            margin: 30px 0;
            position: relative;
        }
        
        .timeline-year {
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 50px;
            font-weight: bold;
            margin: 0 20px;
            z-index: 2;
            position: relative;
            min-width: 80px;
            text-align: center;
        }
        
        .timeline-content {
            flex: 1;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 3px solid #4CAF50;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #4CAF50;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f2f2f2;
        }
        
        .comparison-table tr:hover {
            background: #e8f5e8;
        }
        
        .diagram-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .progress-step {
            flex: 1;
            text-align: center;
            padding: 10px;
            margin: 0 5px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .progress-step:hover {
            transform: translateY(-2px);
        }
        
        .progress-step.active {
            background: #4CAF50;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
            
            .timeline:before {
                left: 30px;
            }
            
            .timeline-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .timeline-year {
                margin: 0 0 15px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>Transfer Learning: A Comprehensive Tutorial</h1>
            <p>From Historical Foundations to Modern Deep Learning Applications</p>
        </div>

        <!-- Table of Contents -->
        <div class="toc">
            <h2>📚 Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction to Transfer Learning</a></li>
                <li><a href="#history">2. Historical Evolution (1986-Present)</a></li>
                <li><a href="#pooling-isolation">3. Core Concepts: Pooling vs Isolation</a></li>
                <li><a href="#multitask-fundamentals">4. Multitask Learning Fundamentals</a></li>
                <li><a href="#decision-framework">5. Practical Decision Making Framework</a></li>
                <li><a href="#input-output">6. Input vs Output Strategy</a></li>
                <li><a href="#medical-cases">7. Real-World Applications: Medical Case Studies</a></li>
                <li><a href="#decision-trees">8. Decision Trees and Adaptive Transfer</a></li>
                <li><a href="#neural-networks">9. Neural Networks and Modern Deep Learning</a></li>
                <li><a href="#takeaways">10. Key Takeaways and Best Practices</a></li>
            </ul>
        </div>

        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step active">Introduction</div>
            <div class="progress-step">History</div>
            <div class="progress-step">Core Concepts</div>
            <div class="progress-step">Applications</div>
            <div class="progress-step">Best Practices</div>
        </div>

        <!-- Section 1: Introduction -->
        <section id="introduction" class="section">
            <h2>🚀 Introduction to Transfer Learning</h2>
            
            <div class="highlight-box">
                <strong>Transfer Learning Definition:</strong> The ability of a machine learning system to recognize and apply knowledge and skills learned in previous tasks to novel but related tasks.
            </div>

            <p>Transfer learning represents one of the most important paradigms in modern machine learning. Rather than learning each task from scratch, transfer learning enables systems to leverage previously acquired knowledge, much like how humans apply their past experiences to new situations.</p>

            <h3>Why Transfer Learning Matters</h3>
            
            <div class="diagram-container">
                <svg width="100%" height="300" viewBox="0 0 800 300">
                    <!-- Traditional Learning -->
                    <g transform="translate(50, 50)">
                        <rect x="0" y="0" width="150" height="80" fill="#ffcdd2" stroke="#f44336" stroke-width="2" rx="10"/>
                        <text x="75" y="35" text-anchor="middle" font-size="14" font-weight="bold">Task A</text>
                        <text x="75" y="55" text-anchor="middle" font-size="12">Learn from scratch</text>
                        
                        <rect x="0" y="100" width="150" height="80" fill="#ffcdd2" stroke="#f44336" stroke-width="2" rx="10"/>
                        <text x="75" y="135" text-anchor="middle" font-size="14" font-weight="bold">Task B</text>
                        <text x="75" y="155" text-anchor="middle" font-size="12">Learn from scratch</text>
                        
                        <text x="75" y="210" text-anchor="middle" font-size="16" font-weight="bold" fill="#f44336">Traditional Learning</text>
                    </g>
                    
                    <!-- Arrow -->
                    <g transform="translate(250, 130)">
                        <path d="M 0 0 L 50 0" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="25" y="-10" text-anchor="middle" font-size="14" font-weight="bold">VS</text>
                    </g>
                    
                    <!-- Transfer Learning -->
                    <g transform="translate(350, 50)">
                        <rect x="0" y="0" width="150" height="80" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="75" y="35" text-anchor="middle" font-size="14" font-weight="bold">Task A</text>
                        <text x="75" y="55" text-anchor="middle" font-size="12">Initial Learning</text>
                        
                        <path d="M 75 80 L 75 100" stroke="#4caf50" stroke-width="3" marker-end="url(#arrowhead-green)"/>
                        <text x="85" y="95" font-size="12" fill="#4caf50">Transfer</text>
                        
                        <rect x="0" y="100" width="150" height="80" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="75" y="135" text-anchor="middle" font-size="14" font-weight="bold">Task B</text>
                        <text x="75" y="155" text-anchor="middle" font-size="12">Faster Learning</text>
                        
                        <text x="75" y="210" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">Transfer Learning</text>
                    </g>
                    
                    <!-- Benefits Box -->
                    <g transform="translate(550, 50)">
                        <rect x="0" y="0" width="200" height="130" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">Benefits</text>
                        <text x="20" y="50" font-size="13" fill="#333">• Faster Learning</text>
                        <text x="20" y="70" font-size="13" fill="#333">• Better Performance</text>
                        <text x="20" y="90" font-size="13" fill="#333">• Less Data Required</text>
                        <text x="20" y="110" font-size="13" fill="#333">• Domain Knowledge Reuse</text>
                    </g>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                        <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#4caf50"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>Key Concepts Covered in this Tutorial</h3>
            
            <table class="comparison-table">
                <tr>
                    <th>Concept</th>
                    <th>Description</th>
                    <th>Practical Impact</th>
                </tr>
                <tr>
                    <td><strong>Multitask Learning</strong></td>
                    <td>Training a model simultaneously on multiple related tasks</td>
                    <td>Improved generalization and shared representations</td>
                </tr>
                <tr>
                    <td><strong>Inductive Transfer</strong></td>
                    <td>Using knowledge from source tasks to improve target tasks</td>
                    <td>Faster convergence and better performance</td>
                </tr>
                <tr>
                    <td><strong>Representation Learning</strong></td>
                    <td>Learning features that are useful across multiple tasks</td>
                    <td>More robust and transferable models</td>
                </tr>
                <tr>
                    <td><strong>Domain Adaptation</strong></td>
                    <td>Adapting models trained on one domain to work on another</td>
                    <td>Reduced need for labeled data in new domains</td>
                </tr>
            </table>

            <div class="warning-box">
                <strong>Important Note:</strong> Transfer learning is not always beneficial. The success depends on the similarity between source and target tasks, the quality of the source model, and the amount of target data available.
            </div>
        </section>

        <!-- Section 2: Historical Evolution -->
        <section id="history" class="section">
            <h2>📜 Historical Evolution (1986-Present)</h2>
            
            <p>Transfer learning has a rich history spanning over three decades. Understanding this evolution helps us appreciate the current state of the field and anticipate future developments.</p>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">1986</div>
                    <div class="timeline-content">
                        <h4>NETtalk System - The Beginning</h4>
                        <p>The first clear example of transfer in neural networks. Trained to predict ~200 output classes simultaneously, not for transfer specifically, but achieved it inadvertently. This system couldn't train 200 separate networks effectively, so they used one network - accidentally discovering transfer benefits.</p>
                        <div class="highlight-box">
                            <strong>Key Insight:</strong> Sometimes necessity leads to innovation. The computational limitations of the 1980s forced researchers into approaches that revealed transfer learning benefits.
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-year">1990</div>
                    <div class="timeline-content">
                        <h4>Rule Injection and Hints</h4>
                        <p><strong>Sudharsan, Holden & Kijowski:</strong> Injecting rules into neural networks</p>
                        <p><strong>Abu-Mostafa:</strong> Injecting "hints" to make networks monotone with respect to inputs - early form of inductive transfer</p>
                        <p><strong>Dean Pomerleau:</strong> Autonomous vehicle navigation using multiple output representations, achieving extra accuracy through what we now recognize as multitask learning</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-year">Early 90s</div>
                    <div class="timeline-content">
                        <h4>Speed-up Learning and Continual Learning</h4>
                        <p><strong>Lorien Pratt:</strong> Adding extra tasks to network outputs to improve training speed</p>
                        <p><strong>Mark Ring's CHILD system:</strong> Early continual learning - learning sequences of problems</p>
                        <p><strong>Virginia de Sa:</strong> Multimodal learning (visual + audio streams) - cow mouth opening with "moo" sound</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-year">Mid 90s</div>
                    <div class="timeline-content">
                        <h4>Multitask Learning Recognition</h4>
                        <p><strong>Rich Caruana's insight:</strong> "People learn amazingly fast because they bring good representations to the problem, representations that were learned on previous problems."</p>
                        <p>First explicit recognition that extra tasks should be actively sought to improve generalization on main tasks.</p>
                        
                        <div class="code-block">
# Rich Sutton's Wisdom (paraphrased)
"The standard machine learning methodology considers a single concept 
to be learned at a time - that's the crux of the problem. 

People face not one task, but a series of tasks. Different tasks have 
different solutions, but they often share the same useful representations.

If you can come to the nth task with a great representation learned 
from the preceding n-1 tasks, you can learn dramatically faster."
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-year">Late 90s</div>
                    <div class="timeline-content">
                        <h4>Theoretical Foundations</h4>
                        <p><strong>Jonathan Baxter:</strong> Representation learning theory</p>
                        <p><strong>Sebastian Thrun & Tom Mitchell:</strong> "Learning to Learn" - what would later become meta-learning</p>
                        <p><strong>Error Correcting Output Codes:</strong> Multiple representations of same tasks</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-year">2000s</div>
                    <div class="timeline-content">
                        <h4>Expansion and Formalization</h4>
                        <p><strong>Bayesian approaches:</strong> Theoretical frameworks for transfer</p>
                        <p><strong>SVM and Kernel methods:</strong> Transfer learning in support vector machines</p>
                        <p><strong>Gaussian Processes:</strong> Multitask learning in probabilistic frameworks</p>
                        <p><strong>Co-training:</strong> Multiple view learning approaches</p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-year">2010s-Present</div>
                    <div class="timeline-content">
                        <h4>Deep Learning Revolution</h4>
                        <p>Deep learning breathed new life into transfer learning. Deep networks have the capacity to learn almost anything we throw at them, and we now have the datasets and computational power to do it effectively.</p>
                        <div class="highlight-box">
                            <strong>Modern Era:</strong> Pre-trained models, fine-tuning, foundation models, and massive multitask learning with billions of parameters.
                        </div>
                    </div>
                </div>
            </div>

            <h3>Why This History Matters</h3>
            <p>Understanding this evolution reveals several important patterns:</p>
            
            <ul style="padding-left: 30px; margin: 20px 0;">
                <li><strong>Computational Constraints Drive Innovation:</strong> Many early discoveries happened due to limitations, not by design</li>
                <li><strong>Multiple Disciplines Contributed:</strong> Computer vision, speech recognition, robotics, and cognitive science all played roles</li>
                <li><strong>Theory Followed Practice:</strong> Many practical successes preceded theoretical understanding</li>
                <li><strong>Scale Enables New Possibilities:</strong> Modern computational power has transformed what's possible</li>
            </ul>
        </section>

        <!-- Section 3: Pooling vs Isolation -->
        <section id="pooling-isolation" class="section">
            <h2>🤔 Core Concepts: Pooling vs Isolation</h2>
            
            <p>One of the fundamental decisions in transfer learning is whether to <strong>pool resources</strong> (combine data/models) or maintain <strong>isolation</strong> (separate models). This decision significantly impacts model performance and generalization.</p>

            <h3>The Medical Data Thought Experiment</h3>
            
            <div class="highlight-box">
                <strong>Scenario:</strong> You have 100,000 patient records - 50,000 male and 50,000 female patients. You need to predict medical risk. Should you train separate models or one combined model?
            </div>

            <div class="diagram-container">
                <svg width="100%" height="400" viewBox="0 0 900 400">
                    <!-- Separate Models -->
                    <g transform="translate(50, 50)">
                        <text x="125" y="20" text-anchor="middle" font-size="18" font-weight="bold" fill="#e91e63">Separate Models</text>
                        
                        <!-- Male Model -->
                        <rect x="25" y="50" width="100" height="80" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="10"/>
                        <text x="75" y="80" text-anchor="middle" font-size="14" font-weight="bold">Male Model</text>
                        <text x="75" y="100" text-anchor="middle" font-size="12">50,000 patients</text>
                        
                        <!-- Female Model -->
                        <rect x="125" y="50" width="100" height="80" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="10"/>
                        <text x="175" y="80" text-anchor="middle" font-size="14" font-weight="bold">Female Model</text>
                        <text x="175" y="100" text-anchor="middle" font-size="12">50,000 patients</text>
                        
                        <!-- Pros/Cons -->
                        <rect x="0" y="150" width="250" height="120" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="5"/>
                        <text x="125" y="170" text-anchor="middle" font-size="14" font-weight="bold" fill="#28a745">Pros:</text>
                        <text x="10" y="190" font-size="12">• Specialized for each group</text>
                        <text x="10" y="210" font-size="12">• No interference between groups</text>
                        
                        <text x="125" y="235" text-anchor="middle" font-size="14" font-weight="bold" fill="#dc3545">Cons:</text>
                        <text x="10" y="255" font-size="12">• Less data per model</text>
                        <text x="10" y="275" font-size="12">• No knowledge sharing</text>
                    </g>
                    
                    <!-- VS -->
                    <g transform="translate(350, 150)">
                        <circle cx="50" cy="50" r="40" fill="#fff3cd" stroke="#ffc107" stroke-width="3"/>
                        <text x="50" y="58" text-anchor="middle" font-size="18" font-weight="bold" fill="#856404">VS</text>
                    </g>
                    
                    <!-- Combined Model -->
                    <g transform="translate(500, 50)">
                        <text x="125" y="20" text-anchor="middle" font-size="18" font-weight="bold" fill="#28a745">Combined Model</text>
                        
                        <!-- Single Model -->
                        <rect x="75" y="50" width="100" height="80" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="10"/>
                        <text x="125" y="80" text-anchor="middle" font-size="14" font-weight="bold">Joint Model</text>
                        <text x="125" y="100" text-anchor="middle" font-size="12">100,000 patients</text>
                        
                        <!-- Gender Input -->
                        <rect x="15" y="65" width="50" height="30" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="40" y="85" text-anchor="middle" font-size="11">Gender</text>
                        <path d="M 65 80 L 75 80" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- Pros/Cons -->
                        <rect x="0" y="150" width="250" height="120" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="5"/>
                        <text x="125" y="170" text-anchor="middle" font-size="14" font-weight="bold" fill="#28a745">Pros:</text>
                        <text x="10" y="190" font-size="12">• More data for training</text>
                        <text x="10" y="210" font-size="12">• Shared representations</text>
                        
                        <text x="125" y="235" text-anchor="middle" font-size="14" font-weight="bold" fill="#dc3545">Cons:</text>
                        <text x="10" y="255" font-size="12">• May not capture group differences</text>
                        <text x="10" y="275" font-size="12">• Potential for bias</text>
                    </g>
                </svg>
            </div>

            <h3>When to Choose Each Approach</h3>
            
            <table class="comparison-table">
                <tr>
                    <th>Medical Condition</th>
                    <th>Recommended Approach</th>
                    <th>Reasoning</th>
                </tr>
                <tr>
                    <td><strong>Ovarian Cancer</strong></td>
                    <td>Separate Models (Female only)</td>
                    <td>Gender-specific condition - male data not relevant</td>
                </tr>
                <tr>
                    <td><strong>Prostate Cancer</strong></td>
                    <td>Separate Models (Male only)</td>
                    <td>Gender-specific condition - female data not relevant</td>
                </tr>
                <tr>
                    <td><strong>Breast Cancer</strong></td>
                    <td>Combined Model</td>
                    <td>Occurs in both genders (5-10% in males) - can benefit from shared learning</td>
                </tr>
                <tr>
                    <td><strong>Heart Disease</strong></td>
                    <td>Combined Model</td>
                    <td>Similar across genders - was historically misunderstood as male-only</td>
                </tr>
                <tr>
                    <td><strong>Pre/Post-menopausal Issues</strong></td>
                    <td>Depends on specific condition</td>
                    <td>Requires domain expertise to determine similarity</td>
                </tr>
            </table>

            <div class="warning-box">
                <strong>Expert Knowledge Required:</strong> This decision requires both machine learning expertise and domain knowledge. Doctors can provide insights about disease similarity across groups, but the final decision is fundamentally a machine learning problem.
            </div>

            <h3>The Mathematical Perspective</h3>
            
            <p>Let's formalize this decision mathematically. For a dataset \(D\) with subgroups \(G_1, G_2, ..., G_k\):</p>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Separate Models:</strong></p>
                \[f_i(x) = \text{model trained on } D_i \text{ where } D_i = \{(x,y) \in D : \text{group}(x) = G_i\}\]
                
                <p><strong>Combined Model with Group Feature:</strong></p>
                \[f(x, g) = \text{model trained on } \{(x \oplus g, y) : (x,y) \in D, g = \text{group}(x)\}\]
                
                <p><strong>Multitask Learning:</strong></p>
                \[f(x) = [f_1(x), f_2(x), ..., f_k(x)] \text{ with shared representations}\]
            </div>

            <h3>Practical Guidelines</h3>
            
            <div class="highlight-box">
                <strong>Best Practice:</strong> Try multiple approaches and empirically evaluate performance. The "right" answer often depends on:
                <ul style="margin-top: 10px;">
                    <li>Task similarity across groups</li>
                    <li>Amount of data available</li>
                    <li>Computational constraints</li>
                    <li>Interpretability requirements</li>
                </ul>
            </div>
        </section>

        <!-- Section 4: Multitask Learning Fundamentals -->
        <section id="multitask-fundamentals" class="section">
            <h2>🔄 Multitask Learning Fundamentals</h2>
            
            <p>Multitask Learning (MTL) provides an elegant middle ground between complete isolation and complete pooling. It allows models to share representations while maintaining task-specific capabilities.</p>

            <h3>Two Approaches to Multitask Learning</h3>
            
            <div class="diagram-container">
                <svg width="100%" height="500" viewBox="0 0 1000 500">
                    <!-- Approach 1: Multiple Outputs -->
                    <g transform="translate(50, 50)">
                        <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#4CAF50">Approach 1: Multiple Outputs</text>
                        
                        <!-- Input Layer -->
                        <circle cx="100" cy="80" r="15" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
                        <text x="100" y="110" text-anchor="middle" font-size="12">Input Features</text>
                        
                        <!-- Hidden Layer -->
                        <circle cx="200" cy="60" r="12" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <circle cx="200" cy="90" r="12" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <circle cx="200" cy="120" r="12" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <text x="200" y="150" text-anchor="middle" font-size="12" font-weight="bold">Shared Hidden Layer</text>
                        
                        <!-- Outputs -->
                        <circle cx="320" cy="70" r="12" fill="#4CAF50" stroke="#388E3C" stroke-width="2"/>
                        <circle cx="320" cy="110" r="12" fill="#E91E63" stroke="#C2185B" stroke-width="2"/>
                        <text x="360" y="75" font-size="12">Male Risk</text>
                        <text x="360" y="115" font-size="12">Female Risk</text>
                        
                        <!-- Connections -->
                        <path d="M 115 80 L 185 60" stroke="#666" stroke-width="2"/>
                        <path d="M 115 80 L 185 90" stroke="#666" stroke-width="2"/>
                        <path d="M 115 80 L 185 120" stroke="#666" stroke-width="2"/>
                        
                        <path d="M 212 60 L 308 70" stroke="#666" stroke-width="2"/>
                        <path d="M 212 90 L 308 70" stroke="#666" stroke-width="2"/>
                        <path d="M 212 120 L 308 70" stroke="#666" stroke-width="2"/>
                        
                        <path d="M 212 60 L 308 110" stroke="#666" stroke-width="2"/>
                        <path d="M 212 90 L 308 110" stroke="#666" stroke-width="2"/>
                        <path d="M 212 120 L 308 110" stroke="#666" stroke-width="2"/>
                        
                        <!-- Description -->
                        <rect x="20" y="180" width="360" height="100" fill="#f0f8ff" stroke="#4CAF50" stroke-width="2" rx="10"/>
                        <text x="200" y="205" text-anchor="middle" font-size="14" font-weight="bold">Benefits:</text>
                        <text x="30" y="225" font-size="12">• Shared representation in hidden layers</text>
                        <text x="30" y="245" font-size="12">• Task-specific output weights</text>
                        <text x="30" y="265" font-size="12">• Can ignore irrelevant tasks if needed</text>
                    </g>
                    
                    <!-- Approach 2: Context Input -->
                    <g transform="translate(550, 50)">
                        <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#9C27B0">Approach 2: Context Input</text>
                        
                        <!-- Input Layer -->
                        <circle cx="80" cy="80" r="15" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
                        <circle cx="80" cy="120" r="15" fill="#FFC107" stroke="#FF8F00" stroke-width="2"/>
                        <text x="80" y="50" text-anchor="middle" font-size="12">Features</text>
                        <text x="80" y="150" text-anchor="middle" font-size="12">Gender Context</text>
                        
                        <!-- Hidden Layer -->
                        <circle cx="200" cy="80" r="12" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <circle cx="200" cy="110" r="12" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <circle cx="200" cy="140" r="12" fill="#FF9800" stroke="#F57C00" stroke-width="2"/>
                        <text x="200" y="170" text-anchor="middle" font-size="12" font-weight="bold">Context-Aware Layer</text>
                        
                        <!-- Output -->
                        <circle cx="320" cy="110" r="15" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
                        <text x="360" y="115" font-size="12">Risk Prediction</text>
                        
                        <!-- Connections -->
                        <path d="M 95 80 L 185 80" stroke="#666" stroke-width="2"/>
                        <path d="M 95 80 L 185 110" stroke="#666" stroke-width="2"/>
                        <path d="M 95 80 L 185 140" stroke="#666" stroke-width="2"/>
                        
                        <path d="M 95 120 L 185 80" stroke="#666" stroke-width="2"/>
                        <path d="M 95 120 L 185 110" stroke="#666" stroke-width="2"/>
                        <path d="M 95 120 L 185 140" stroke="#666" stroke-width="2"/>
                        
                        <path d="M 212 80 L 305 110" stroke="#666" stroke-width="2"/>
                        <path d="M 212 110 L 305 110" stroke="#666" stroke-width="2"/>
                        <path d="M 212 140 L 305 110" stroke="#666" stroke-width="2"/>
                        
                        <!-- Description -->
                        <rect x="20" y="180" width="360" height="100" fill="#f3e5f5" stroke="#9C27B0" stroke-width="2" rx="10"/>
                        <text x="200" y="205" text-anchor="middle" font-size="14" font-weight="bold">Benefits:</text>
                        <text x="30" y="225" font-size="12">• Context-aware representations</text>
                        <text x="30" y="245" font-size="12">• Single output, multiple contexts</text>
                        <text x="30" y="265" font-size="12">• Flexible context switching</text>
                    </g>
                    
                    <!-- Comparison -->
                    <g transform="translate(250, 350)">
                        <rect x="0" y="0" width="500" height="120" fill="#fff8e1" stroke="#FF6F00" stroke-width="2" rx="10"/>
                        <text x="250" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#E65100">Surprising Insight</text>
                        <text x="20" y="50" font-size="13">From 30,000 feet, these approaches are essentially the same:</text>
                        <text x="20" y="70" font-size="13">• Both see 100,000 patients with same features</text>
                        <text x="20" y="90" font-size="13">• Both know gender information</text>
                        <text x="20" y="110" font-size="13">• Different architectures, similar computational power</text>
                    </g>
                </svg>
            </div>

            <h3>The Decision Tree Revelation</h3>
            
            <p>A fascinating insight emerges when we consider how decision trees handle the "context input" approach. Trees can make adaptive decisions about when and where to share information:</p>

            <div class="diagram-container">
                <svg width="100%" height="350" viewBox="0 0 800 350">
                    <!-- Decision Tree Scenarios -->
                    
                    <!-- Scenario 1: Always Split on Gender -->
                    <g transform="translate(50, 50)">
                        <text x="120" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#f44336">Always Split on Gender</text>
                        
                        <!-- Root -->
                        <rect x="80" y="40" width="80" height="30" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="120" y="60" text-anchor="middle" font-size="12">Gender?</text>
                        
                        <!-- Left Branch -->
                        <rect x="20" y="100" width="60" height="30" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="5"/>
                        <text x="50" y="120" text-anchor="middle" font-size="11">Male Model</text>
                        
                        <!-- Right Branch -->
                        <rect x="140" y="100" width="60" height="30" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <text x="170" y="120" text-anchor="middle" font-size="11">Female Model</text>
                        
                        <!-- Connections -->
                        <path d="M 110 70 L 60 100" stroke="#333" stroke-width="2"/>
                        <path d="M 130 70 L 180 100" stroke="#333" stroke-width="2"/>
                        <text x="85" y="90" font-size="10">Male</text>
                        <text x="155" y="90" font-size="10">Female</text>
                        
                        <text x="120" y="160" text-anchor="middle" font-size="12" font-weight="bold" fill="#f44336">= Separate Models</text>
                    </g>
                    
                    <!-- Scenario 2: Never Split on Gender -->
                    <g transform="translate(300, 50)">
                        <text x="120" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">Never Split on Gender</text>
                        
                        <!-- Root -->
                        <rect x="80" y="40" width="80" height="30" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="120" y="60" text-anchor="middle" font-size="12">Age > 50?</text>
                        
                        <!-- Left Branch -->
                        <rect x="20" y="100" width="60" height="30" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="50" y="120" text-anchor="middle" font-size="11">Low Risk</text>
                        
                        <!-- Right Branch -->
                        <rect x="140" y="100" width="60" height="30" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
                        <text x="170" y="120" text-anchor="middle" font-size="11">High Risk</text>
                        
                        <!-- Connections -->
                        <path d="M 110 70 L 60 100" stroke="#333" stroke-width="2"/>
                        <path d="M 130 70 L 180 100" stroke="#333" stroke-width="2"/>
                        <text x="85" y="90" font-size="10">No</text>
                        <text x="155" y="90" font-size="10">Yes</text>
                        
                        <text x="120" y="160" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">= Pooled Model</text>
                    </g>
                    
                    <!-- Scenario 3: Partial Split -->
                    <g transform="translate(550, 50)">
                        <text x="120" y="20" text-anchor="middle" font-size="16" font-weight="bold" fill="#9c27b0">Partial Split on Gender</text>
                        
                        <!-- Root -->
                        <rect x="80" y="40" width="80" height="25" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="120" y="57" text-anchor="middle" font-size="11">Age > 65?</text>
                        
                        <!-- Left Branch -->
                        <rect x="20" y="85" width="60" height="25" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="50" y="102" text-anchor="middle" font-size="10">Gender?</text>
                        
                        <!-- Right Branch -->
                        <rect x="140" y="85" width="60" height="25" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="170" y="102" text-anchor="middle" font-size="10">High Risk</text>
                        
                        <!-- Sub-branches -->
                        <rect x="0" y="130" width="40" height="20" fill="#e3f2fd" stroke="#2196f3" stroke-width="1" rx="3"/>
                        <text x="20" y="143" text-anchor="middle" font-size="9">M</text>
                        
                        <rect x="50" y="130" width="40" height="20" fill="#fce4ec" stroke="#e91e63" stroke-width="1" rx="3"/>
                        <text x="70" y="143" text-anchor="middle" font-size="9">F</text>
                        
                        <!-- Connections -->
                        <path d="M 110 65 L 60 85" stroke="#333" stroke-width="2"/>
                        <path d="M 130 65 L 180 85" stroke="#333" stroke-width="2"/>
                        <path d="M 40 110 L 25 130" stroke="#333" stroke-width="1"/>
                        <path d="M 60 110 L 75 130" stroke="#333" stroke-width="1"/>
                        
                        <text x="120" y="180" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">= Adaptive Transfer</text>
                    </g>
                    
                    <!-- Bottom explanation -->
                    <g transform="translate(50, 250)">
                        <rect x="0" y="0" width="700" height="80" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="10"/>
                        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Key Insight: Trees Do Adaptive Multitask Learning Automatically</text>
                        <text x="20" y="45" font-size="13">• Shared learning above gender split, separate learning below</text>
                        <text x="20" y="65" font-size="13">• Ensemble of trees creates sophisticated adaptive transfer strategies</text>
                    </g>
                </svg>
            </div>

            <div class="highlight-box">
                <strong>Revolutionary Insight:</strong> Ensemble methods like Random Forest are already doing adaptive multitask learning! They automatically decide when and where to share information between subgroups. This explains why they're so effective and why it's been challenging to improve them with traditional multitask learning approaches.
            </div>

            <h3>Limitations and Opportunities</h3>
            
            <table class="comparison-table">
                <tr>
                    <th>Method</th>
                    <th>Adaptive Transfer</th>
                    <th>Soft Transfer</th>
                    <th>Complex Functions</th>
                </tr>
                <tr>
                    <td><strong>Decision Trees/Ensembles</strong></td>
                    <td>✅ Yes</td>
                    <td>❌ Hard splits only</td>
                    <td>⚠️ Limited by divide-and-conquer</td>
                </tr>
                <tr>
                    <td><strong>Neural Networks</strong></td>
                    <td>⚠️ Requires design</td>
                    <td>✅ Continuous weights</td>
                    <td>✅ Can learn complex functions</td>
                </tr>
                <tr>
                    <td><strong>Multitask Neural Nets</strong></td>
                    <td>✅ With proper architecture</td>
                    <td>✅ Yes</td>
                    <td>✅ Yes</td>
                </tr>
            </table>

            <div class="warning-box">
                <strong>Achilles Heel of Trees:</strong> To learn complex functions involving many features, trees must repeatedly divide and conquer the data. They will eventually run out of data before learning sufficiently complex functions. Neural networks don't have this limitation.
            </div>
        </section>

        <!-- Section 5: Input vs Output Strategy -->
        <section id="input-output" class="section">
            <h2>🔄 Input vs Output Strategy</h2>
            
            <p>One of the most practical insights from transfer learning is knowing when to use features as inputs versus outputs. This decision can significantly impact model performance, especially when dealing with missing data or feature selection constraints.</p>

            <h3>The Core Principle</h3>
            
            <div class="highlight-box">
                <strong>Key Insight:</strong> Just because information won't be available at runtime doesn't mean you have to ignore it during training. Consider using unavailable features as auxiliary outputs instead of discarding them entirely.
            </div>

            <div class="diagram-container">
                <svg width="100%" height="400" viewBox="0 0 1000 400">
                    <!-- Traditional Approach -->
                    <g transform="translate(50, 50)">
                        <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#f44336">Traditional Approach</text>
                        
                        <!-- Available Features -->
                        <rect x="50" y="60" width="120" height="40" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="110" y="85" text-anchor="middle" font-size="12" font-weight="bold">Available Features</text>
                        
                        <!-- Model -->
                        <rect x="100" y="130" width="80" height="40" fill="#e1f5fe" stroke="#2196f3" stroke-width="2" rx="5"/>
                        <text x="140" y="155" text-anchor="middle" font-size="12">Model</text>
                        
                        <!-- Output -->
                        <rect x="100" y="200" width="80" height="40" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="140" y="225" text-anchor="middle" font-size="12">Prediction</text>
                        
                        <!-- Discarded Features -->
                        <rect x="250" y="60" width="120" height="40" fill="#ffcdd2" stroke="#f44336" stroke-width="2" rx="5"/>
                        <text x="310" y="85" text-anchor="middle" font-size="12" font-weight="bold">Unavailable Features</text>
                        <text x="310" y="120" text-anchor="middle" font-size="14" fill="#f44336">❌ DISCARDED</text>
                        
                        <!-- Connections -->
                        <path d="M 140 100 L 140 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 140 170 L 140 200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    </g>
                    
                    <!-- MTL Approach -->
                    <g transform="translate(550, 50)">
                        <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#4caf50">Multitask Learning Approach</text>
                        
                        <!-- Available Features -->
                        <rect x="50" y="60" width="120" height="40" fill="#c8e6c9" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="110" y="85" text-anchor="middle" font-size="12" font-weight="bold">Available Features</text>
                        
                        <!-- Model -->
                        <rect x="100" y="130" width="80" height="40" fill="#e1f5fe" stroke="#2196f3" stroke-width="2" rx="5"/>
                        <text x="140" y="155" text-anchor="middle" font-size="12">Model</text>
                        
                        <!-- Main Output -->
                        <rect x="50" y="200" width="80" height="30" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                        <text x="90" y="220" text-anchor="middle" font-size="11">Main Prediction</text>
                        
                        <!-- Auxiliary Outputs -->
                        <rect x="150" y="200" width="80" height="30" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                        <text x="190" y="220" text-anchor="middle" font-size="11">Aux Predictions</text>
                        
                        <!-- Features as Outputs -->
                        <rect x="250" y="60" width="120" height="40" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="310" y="85" text-anchor="middle" font-size="12" font-weight="bold">Features as Outputs</text>
                        <text x="310" y="120" text-anchor="middle" font-size="14" fill="#4caf50">✅ UTILIZED</text>
                        
                        <!-- Connections -->
                        <path d="M 140 100 L 140 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 130 170 L 100 200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 150 170 L 180 200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 310 100 L 190 190" stroke="#9c27b0" stroke-width="2" stroke-dasharray="5,5"/>
                    </g>
                    
                    <!-- Performance Comparison -->
                    <g transform="translate(300, 280)">
                        <rect x="0" y="0" width="400" height="100" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
                        <text x="200" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Performance Impact</text>
                        <text x="20" y="50" font-size="13" fill="#f44336">Traditional: Loses valuable training signal</text>
                        <text x="20" y="70" font-size="13" fill="#4caf50">MTL: 5-10% error reduction from auxiliary tasks</text>
                        <text x="20" y="90" font-size="13" fill="#2196f3">Auxiliary outputs improve main task generalization</text>
                    </g>
                </svg>
            </div>

            <h3>When to Use This Strategy</h3>
            
            <table class="comparison-table">
                <tr>
                    <th>Scenario</th>
                    <th>Traditional Approach</th>
                    <th>Input vs Output Strategy</th>
                    <th>Expected Benefit</th>
                </tr>
                <tr>
                    <td><strong>Missing Values at Runtime</strong></td>
                    <td>Remove features entirely</td>
                    <td>Use as auxiliary outputs</td>
                    <td>5-10% error reduction</td>
                </tr>
                <tr>
                    <td><strong>Feature Selection</strong></td>
                    <td>Discard low-importance features</td>
                    <td>Keep discarded features as outputs</td>
                    <td>Improved generalization</td>
                </tr>
                <tr>
                    <td><strong>Expensive Features</strong></td>
                    <td>Avoid using expensive features</td>
                    <td>Predict expensive features from cheap ones</td>
                    <td>Cost-effective performance</td>
                </tr>
                <tr>
                    <td><strong>Temporal Dependencies</strong></td>
                    <td>Use only past information</td>
                    <td>Predict future values as auxiliary tasks</td>
                    <td>Better temporal modeling</td>
                </tr>
            </table>
        </section>

        <!-- Section 6: Medical Case Studies -->
        <section id="medical-cases" class="section">
            <h2>🏥 Real-World Applications: Medical Case Studies</h2>
            
            <p>The medical domain provides excellent examples of transfer learning principles in action. These case studies demonstrate practical applications and measurable benefits.</p>

            <h3>Case Study 1: Pneumonia Risk Prediction</h3>
            
            <div class="highlight-box">
                <strong>Challenge:</strong> Doctors need to decide who to admit to the hospital, but they want predictions based only on information available BEFORE admission (pre-hospital attributes). In-hospital test results won't be available for the admission decision.
            </div>

            <div class="diagram-container">
                <div id="pneumonia-chart"></div>
                <script>
                    // ROC Performance Chart
                    var trace1 = {
                        x: ['All Features<br/>(Not Practical)', 'Pre-hospital Only<br/>(Traditional)', 'Pre-hospital + MTL<br/>(Our Approach)'],
                        y: [0.89, 0.78, 0.83],
                        type: 'bar',
                        name: 'ROC Score',
                        marker: {
                            color: ['#ff6b6b', '#4ecdc4', '#45b7d1']
                        }
                    };

                    var layout = {
                        title: 'Pneumonia Risk Prediction Performance',
                        yaxis: {
                            title: 'ROC Score (Higher is Better)',
                            range: [0.7, 0.95]
                        },
                        width: 800,
                        height: 400
                    };

                    Plotly.newPlot('pneumonia-chart', [trace1], layout);
                </script>
            </div>

            <div class="warning-box">
                <strong>Doctor's Constraint:</strong> "We need predictions to help us decide hospital admission. Don't use in-hospital attributes because we won't know them for most patients."
            </div>

            <p><strong>Solution:</strong> Instead of discarding in-hospital measurements, we used them as auxiliary prediction tasks. The model learns to predict blood test results, oxygen levels, and other measurements from pre-hospital information, which improves the main pneumonia risk prediction.</p>

            <h3>Case Study 2: Feature Selection with Multitask Learning</h3>
            
            <div class="diagram-container">
                <div id="feature-selection-chart"></div>
                <script>
                    // Feature Selection Chart
                    var trace1 = {
                        x: [190, 150, 100, 50, 25, 10],
                        y: [0.825, 0.835, 0.845, 0.855, 0.840, 0.810],
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: 'Single Task',
                        line: {color: '#ff6b6b', width: 3}
                    };

                    var trace2 = {
                        x: [50, 50],
                        y: [0.855, 0.875],
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: 'MTL with Discarded Features',
                        line: {color: '#45b7d1', width: 3},
                        marker: {size: 10}
                    };

                    var layout = {
                        title: 'Feature Selection: Traditional vs Multitask Learning',
                        xaxis: {
                            title: 'Number of Input Features',
                            type: 'log'
                        },
                        yaxis: {
                            title: 'ROC Score',
                            range: [0.80, 0.88]
                        },
                        annotations: [{
                            x: 50,
                            y: 0.875,
                            text: '140 discarded features<br/>used as outputs',
                            showarrow: true,
                            arrowhead: 2,
                            arrowsize: 1,
                            arrowwidth: 2,
                            arrowcolor: '#45b7d1'
                        }],
                        width: 800,
                        height: 400
                    };

                    Plotly.newPlot('feature-selection-chart', [trace1, trace2], layout);
                </script>
            </div>

            <div class="highlight-box">
                <strong>Key Insight:</strong> Feature selection identified 50 most important features (out of 190). Instead of throwing away the other 140 features, we used them as auxiliary outputs. This improved performance from 0.855 to 0.875 ROC score.
            </div>

            <h3>Practical Implementation Guidelines</h3>

            <div class="code-block">
# Pseudocode for Input vs Output Strategy

# Traditional Approach
def traditional_model(available_features):
    model = train_model(
        inputs=available_features,
        outputs=main_target
    )
    return model

# Multitask Learning Approach  
def multitask_model(available_features, unavailable_features):
    model = train_model(
        inputs=available_features,
        outputs={
            'main_task': main_target,
            'aux_tasks': unavailable_features  # Predict these!
        }
    )
    return model

# At inference time, ignore auxiliary predictions
prediction = model.predict(available_features)['main_task']
            </div>

            <h3>When This Strategy Works Best</h3>
            
            <ul style="padding-left: 30px; margin: 20px 0;">
                <li><strong>High Correlation:</strong> Auxiliary features should be predictable from input features</li>
                <li><strong>Shared Representations:</strong> Main and auxiliary tasks should benefit from similar learned features</li>
                <li><strong>Sufficient Data:</strong> Need enough training examples to learn both main and auxiliary tasks</li>
                <li><strong>Computational Resources:</strong> Multiple outputs require more computation during training</li>
            </ul>

            <div class="warning-box">
                <strong>Important:</strong> Doctors typically don't trust auxiliary predictions (e.g., blood potassium levels). They measure these because they need accurate values. The auxiliary tasks are purely to improve the main task's performance through better representations.
            </div>
        </section>

        <!-- Section 7: Neural Networks and Modern Deep Learning -->
        <section id="neural-networks" class="section">
            <h2>🧠 Neural Networks and Modern Deep Learning</h2>
            
            <p>The deep learning revolution has transformed transfer learning from a specialized technique to a fundamental paradigm. Modern neural networks have the capacity to learn complex representations that transfer effectively across tasks and domains.</p>

            <h3>Why Deep Learning Revolutionized Transfer Learning</h3>
            
            <div class="diagram-container">
                <svg width="100%" height="400" viewBox="0 0 1000 400">
                    <!-- Traditional ML -->
                    <g transform="translate(50, 50)">
                        <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#f44336">Traditional ML Era</text>
                        
                        <!-- Limited Capacity -->
                        <rect x="50" y="60" width="200" height="80" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="10"/>
                        <text x="150" y="85" text-anchor="middle" font-size="14" font-weight="bold">Limited Model Capacity</text>
                        <text x="60" y="105" font-size="12">• Shallow representations</text>
                        <text x="60" y="125" font-size="12">• Task-specific features</text>
                        
                        <!-- Small Data -->
                        <rect x="50" y="160" width="200" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="10"/>
                        <text x="150" y="185" text-anchor="middle" font-size="14" font-weight="bold">Limited Data & Compute</text>
                        <text x="60" y="205" font-size="12">• Small datasets</text>
                        <text x="60" y="225" font-size="12">• Computational constraints</text>
                        
                        <!-- Result -->
                        <rect x="50" y="260" width="200" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="10"/>
                        <text x="150" y="285" text-anchor="middle" font-size="14" font-weight="bold">Result: Limited Transfer</text>
                        <text x="60" y="305" font-size="12">Transfer mostly accidental</text>
                    </g>
                    
                    <!-- Arrow -->
                    <g transform="translate(300, 180)">
                        <path d="M 0 0 L 100 0" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)"/>
                        <text x="50" y="-15" text-anchor="middle" font-size="16" font-weight="bold">Evolution</text>
                    </g>
                    
                    <!-- Deep Learning Era -->
                    <g transform="translate(450, 50)">
                        <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#4caf50">Deep Learning Era</text>
                        
                        <!-- High Capacity -->
                        <rect x="50" y="60" width="200" height="80" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="150" y="85" text-anchor="middle" font-size="14" font-weight="bold">Massive Model Capacity</text>
                        <text x="60" y="105" font-size="12">• Deep hierarchical features</text>
                        <text x="60" y="125" font-size="12">• Universal approximators</text>
                        
                        <!-- Big Data -->
                        <rect x="50" y="160" width="200" height="80" fill="#e1f5fe" stroke="#2196f3" stroke-width="2" rx="10"/>
                        <text x="150" y="185" text-anchor="middle" font-size="14" font-weight="bold">Big Data & Compute</text>
                        <text x="60" y="205" font-size="12">• Massive datasets</text>
                        <text x="60" y="225" font-size="12">• GPU acceleration</text>
                        
                        <!-- Result -->
                        <rect x="50" y="260" width="200" height="60" fill="#fff9c4" stroke="#ffc107" stroke-width="2" rx="10"/>
                        <text x="150" y="285" text-anchor="middle" font-size="14" font-weight="bold">Result: Powerful Transfer</text>
                        <text x="60" y="305" font-size="12">Transfer as core paradigm</text>
                    </g>
                    
                    <!-- Examples -->
                    <g transform="translate(750, 80)">
                        <rect x="0" y="0" width="200" height="200" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="bold">Modern Examples</text>
                        <text x="10" y="50" font-size="12">🖼️ ImageNet → Medical</text>
                        <text x="10" y="70" font-size="12">📝 BERT → Domain NLP</text>
                        <text x="10" y="90" font-size="12">🎮 Game AI → Robotics</text>
                        <text x="10" y="110" font-size="12">🔬 Protein → Drug Design</text>
                        <text x="10" y="130" font-size="12">🌍 GPT → Code/Math</text>
                        <text x="10" y="150" font-size="12">🎵 Audio → Speech</text>
                        <text x="10" y="170" font-size="12">📊 Foundation Models</text>
                    </g>
                </svg>
            </div>

            <h3>The Scientific Discovery Inspiration</h3>
            
            <div class="highlight-box">
                <strong>Original Motivation:</strong> "How would I use a neural net to do scientific discovery?" The challenge was making black-box neural networks learn general principles like the universal law of gravitation.
            </div>

            <p>The key insight was that a neural network trained on just the moon orbiting Earth would only learn to fit that specific trajectory. But a network trained simultaneously on multiple gravitational systems (moon-Earth, Earth-sun, Jupiter-sun, Saturn's rings, galactic motion) might be forced to learn more general gravitational principles through shared representations.</p>

            <div class="diagram-container">
                <svg width="100%" height="350" viewBox="0 0 900 350">
                    <!-- Single Task -->
                    <g transform="translate(50, 50)">
                        <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#f44336">Single Task Approach</text>
                        
                        <!-- Earth-Moon System -->
                        <circle cx="100" cy="100" r="20" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                        <text x="100" y="107" text-anchor="middle" font-size="10" fill="white">Earth</text>
                        
                        <circle cx="150" cy="80" r="8" fill="#9e9e9e" stroke="#616161" stroke-width="2"/>
                        <text x="150" y="70" text-anchor="middle" font-size="8">Moon</text>
                        
                        <!-- Elliptical orbit -->
                        <ellipse cx="100" cy="100" rx="60" ry="40" fill="none" stroke="#2196f3" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <!-- Neural Network -->
                        <rect x="50" y="170" width="100" height="60" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="10"/>
                        <text x="100" y="195" text-anchor="middle" font-size="12">Neural Net</text>
                        <text x="100" y="215" text-anchor="middle" font-size="10">Learns curve fitting</text>
                        
                        <!-- Arrow -->
                        <path d="M 100 150 L 100 170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <text x="100" y="260" text-anchor="middle" font-size="12" font-weight="bold" fill="#f44336">❌ No General Principles</text>
                    </g>
                    
                    <!-- Arrow -->
                    <g transform="translate(350, 150)">
                        <path d="M 0 0 L 80 0" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="40" y="-15" text-anchor="middle" font-size="14" font-weight="bold">vs</text>
                    </g>
                    
                    <!-- Multi-Task -->
                    <g transform="translate(500, 50)">
                        <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#4caf50">Multitask Approach</text>
                        
                        <!-- Multiple Systems -->
                        <!-- Earth-Moon -->
                        <circle cx="70" cy="80" r="8" fill="#4caf50"/>
                        <circle cx="90" cy="75" r="3" fill="#9e9e9e"/>
                        <text x="80" y="105" text-anchor="middle" font-size="8">Earth-Moon</text>
                        
                        <!-- Earth-Sun -->
                        <circle cx="140" cy="80" r="12" fill="#ff9800"/>
                        <circle cx="155" cy="75" r="4" fill="#4caf50"/>
                        <text x="147" y="105" text-anchor="middle" font-size="8">Earth-Sun</text>
                        
                        <!-- Jupiter-Sun -->
                        <circle cx="210" cy="80" r="12" fill="#ff9800"/>
                        <circle cx="230" cy="75" r="6" fill="#795548"/>
                        <text x="220" y="105" text-anchor="middle" font-size="8">Jupiter-Sun</text>
                        
                        <!-- Neural Network -->
                        <rect x="100" y="130" width="100" height="60" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="150" y="155" text-anchor="middle" font-size="12">Neural Net</text>
                        <text x="150" y="175" text-anchor="middle" font-size="10">Shared Physics</text>
                        
                        <!-- Arrows -->
                        <path d="M 80 110 L 130 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 147 110 L 150 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <path d="M 220 110 L 170 130" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <text x="150" y="220" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">✅ Universal Principles</text>
                    </g>
                    
                    <!-- Bottom insight -->
                    <g transform="translate(150, 280)">
                        <rect x="0" y="0" width="600" height="50" fill="#fff8e1" stroke="#ff8f00" stroke-width="2" rx="10"/>
                        <text x="300" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#e65100">Key Insight: Multiple Related Tasks Force Learning of General Representations</text>
                        <text x="300" y="45" text-anchor="middle" font-size="12">This principle applies beyond physics: language, vision, robotics, etc.</text>
                    </g>
                </svg>
            </div>

            <h3>Modern Transfer Learning Paradigms</h3>
            
            <table class="comparison-table">
                <tr>
                    <th>Paradigm</th>
                    <th>Description</th>
                    <th>Example</th>
                    <th>Key Benefit</th>
                </tr>
                <tr>
                    <td><strong>Pre-training + Fine-tuning</strong></td>
                    <td>Train on large generic dataset, then adapt to specific task</td>
                    <td>ImageNet → Medical Imaging</td>
                    <td>Leverages massive pre-trained features</td>
                </tr>
                <tr>
                    <td><strong>Foundation Models</strong></td>
                    <td>Large models trained on diverse data, adaptable to many tasks</td>
                    <td>GPT, BERT, CLIP</td>
                    <td>Zero/few-shot transfer capabilities</td>
                </tr>
                <tr>
                    <td><strong>Meta-Learning</strong></td>
                    <td>Learn to learn - models that adapt quickly to new tasks</td>
                    <td>Model-Agnostic Meta-Learning</td>
                    <td>Fast adaptation with minimal data</td>
                </tr>
                <tr>
                    <td><strong>Multitask Learning</strong></td>
                    <td>Simultaneous training on multiple related tasks</td>
                    <td>Joint NLP tasks, Multi-domain CV</td>
                    <td>Improved generalization</td>
                </tr>
            </table>

            <div class="warning-box">
                <strong>Scale Matters:</strong> Modern transfer learning benefits enormously from scale - larger models, more data, and more compute enable more effective transfer across increasingly diverse tasks.
            </div>
        </section>

        <!-- Section 8: Key Takeaways and Best Practices -->
        <section id="takeaways" class="section">
            <h2>🎯 Key Takeaways and Best Practices</h2>
            
            <p>After decades of evolution, transfer learning has established clear principles and practical guidelines for successful implementation.</p>

            <h3>The Fundamental Principles</h3>
            
            <div class="diagram-container">
                <svg width="100%" height="500" viewBox="0 0 1000 500">
                    <!-- Principle 1 -->
                    <g transform="translate(50, 50)">
                        <circle cx="80" cy="80" r="60" fill="#e3f2fd" stroke="#2196f3" stroke-width="3"/>
                        <text x="80" y="75" text-anchor="middle" font-size="14" font-weight="bold">Representation</text>
                        <text x="80" y="95" text-anchor="middle" font-size="14" font-weight="bold">Learning</text>
                        <text x="80" y="170" text-anchor="middle" font-size="12" font-weight="bold">Good representations are</text>
                        <text x="80" y="185" text-anchor="middle" font-size="12" font-weight="bold">the key to transfer</text>
                    </g>
                    
                    <!-- Principle 2 -->
                    <g transform="translate(250, 50)">
                        <circle cx="80" cy="80" r="60" fill="#e8f5e9" stroke="#4caf50" stroke-width="3"/>
                        <text x="80" y="75" text-anchor="middle" font-size="14" font-weight="bold">Task</text>
                        <text x="80" y="95" text-anchor="middle" font-size="14" font-weight="bold">Relatedness</text>
                        <text x="80" y="170" text-anchor="middle" font-size="12" font-weight="bold">Success depends on</text>
                        <text x="80" y="185" text-anchor="middle" font-size="12" font-weight="bold">similarity between tasks</text>
                    </g>
                    
                    <!-- Principle 3 -->
                    <g transform="translate(450, 50)">
                        <circle cx="80" cy="80" r="60" fill="#fff3e0" stroke="#ff9800" stroke-width="3"/>
                        <text x="80" y="75" text-anchor="middle" font-size="14" font-weight="bold">Adaptive</text>
                        <text x="80" y="95" text-anchor="middle" font-size="14" font-weight="bold">Sharing</text>
                        <text x="80" y="170" text-anchor="middle" font-size="12" font-weight="bold">Smart models decide</text>
                        <text x="80" y="185" text-anchor="middle" font-size="12" font-weight="bold">what/when to share</text>
                    </g>
                    
                    <!-- Principle 4 -->
                    <g transform="translate(650, 50)">
                        <circle cx="80" cy="80" r="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="3"/>
                        <text x="80" y="75" text-anchor="middle" font-size="14" font-weight="bold">Scale</text>
                        <text x="80" y="95" text-anchor="middle" font-size="14" font-weight="bold">Enables</text>
                        <text x="80" y="170" text-anchor="middle" font-size="12" font-weight="bold">More data/compute</text>
                        <text x="80" y="185" text-anchor="middle" font-size="12" font-weight="bold">enables better transfer</text>
                    </g>
                    
                    <!-- Decision Framework -->
                    <g transform="translate(100, 250)">
                        <rect x="0" y="0" width="700" height="200" fill="#f8f9fa" stroke="#6c757d" stroke-width="2" rx="15"/>
                        <text x="350" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">Decision Framework</text>
                        
                        <!-- Questions -->
                        <text x="50" y="60" font-size="14" font-weight="bold" fill="#e91e63">1. Are tasks related?</text>
                        <text x="70" y="80" font-size="12">• Same domain? Similar data types? Shared concepts?</text>
                        
                        <text x="50" y="110" font-size="14" font-weight="bold" fill="#2196f3">2. What's available when?</text>
                        <text x="70" y="130" font-size="12">• Training time vs inference time features</text>
                        
                        <text x="400" y="60" font-size="14" font-weight="bold" fill="#4caf50">3. How much data?</text>
                        <text x="420" y="80" font-size="12">• Sufficient for multitask learning?</text>
                        
                        <text x="400" y="110" font-size="14" font-weight="bold" fill="#ff9800">4. What's the goal?</text>
                        <text x="420" y="130" font-size="12">• Performance vs interpretability vs efficiency</text>
                        
                        <!-- Recommendations -->
                        <rect x="50" y="150" width="600" height="40" fill="#e8f5e9" stroke="#4caf50" stroke-width="1" rx="5"/>
                        <text x="350" y="175" text-anchor="middle" font-size="14" font-weight="bold" fill="#2e7d32">💡 Try multiple approaches and evaluate empirically</text>
                    </g>
                </svg>
            </div>

            <h3>Practical Implementation Checklist</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0;">
                <div>
                    <h4 style="color: #4caf50; margin-bottom: 15px;">✅ Do These</h4>
                    <ul style="padding-left: 20px;">
                        <li>Start with domain expertise consultation</li>
                        <li>Try both input and output strategies</li>
                        <li>Evaluate multiple sharing approaches</li>
                        <li>Use cross-validation for model selection</li>
                        <li>Monitor for negative transfer</li>
                        <li>Consider computational constraints</li>
                        <li>Start simple, then add complexity</li>
                        <li>Leverage pre-trained models when available</li>
                    </ul>
                </div>
                
                <div>
                    <h4 style="color: #f44336; margin-bottom: 15px;">❌ Avoid These</h4>
                    <ul style="padding-left: 20px;">
                        <li>Assuming transfer always helps</li>
                        <li>Ignoring task dissimilarity</li>
                        <li>Blindly pooling unrelated data</li>
                        <li>Overlooking computational costs</li>
                        <li>Using transfer without evaluation</li>
                        <li>Forcing complex solutions early</li>
                        <li>Ignoring domain constraints</li>
                        <li>Treating auxiliary tasks as important outputs</li>
                    </ul>
                </div>
            </div>

            <h3>The Future of Transfer Learning</h3>
            
            <div class="highlight-box">
                <strong>Looking Ahead:</strong> Transfer learning continues evolving with foundation models, few-shot learning, and cross-modal transfer. The principles remain constant, but the scale and scope continue expanding dramatically.
            </div>

            <h3>Final Wisdom from the Field</h3>
            
            <div class="code-block">
# The Transfer Learning Practitioner's Motto
"Good representations are the key to good learning.
People learn amazingly fast because they bring good representations 
to the problem - representations learned on previous problems.

The standard methodology of learning one concept at a time 
is the crux of the problem. 

For practical purposes, we face not one task, but a series of tasks.
If you can come to the nth task with great representations 
learned from the preceding n-1 tasks, 
you can learn dramatically faster and better."

- Rich Sutton (paraphrased), 1990s
            </div>

            <div class="warning-box">
                <strong>Remember:</strong> Transfer learning is as much art as science. The key is understanding your domain, trying multiple approaches, and carefully evaluating what works. There's no substitute for empirical validation.
            </div>
        </section>

        <!-- Conclusion -->
        <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; border-radius: 10px; margin: 40px 0;">
            <h2 style="margin-bottom: 20px;">🎓 Congratulations!</h2>
            <p style="font-size: 1.2em; margin-bottom: 15px;">You've completed the comprehensive transfer learning tutorial.</p>
            <p style="font-size: 1.1em;">Now go forth and transfer wisely! 🚀</p>
        </div>

    </div>
</body>
</html> 