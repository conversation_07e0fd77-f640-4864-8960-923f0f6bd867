3.1.5 Special Distributions
 As it turns out, there are some specific distributions that are used over and over in
 practice, thus they have been given special names. There is a random experiment
 behind each of these distributions. Since these random experiments model a lot of real
 life phenomenon, these special distributions are used frequently in different
 applications. That's why they have been given a name and we devote a section to
 study them. We will provide PMFs for all of these special random variables, but rather
 than trying to memorize the PMF, you should understand the random experiment
 behind each of them. If you understand the random experiments, you can simply
 derive the PMFs when you need them. Although it might seem that there are a lot of
 formulas in this section, there are in fact very few new concepts. Do not get intimidated
 by the large number of formulas, look at each distribution as a practice problem on
 discrete random variables.
 Bernoulli Distribution
 
What is the simplest discrete random variable (i.e., simplest PMF) that you can
 imagine? My answer to this question is a PMF that is nonzero at only one point. For
 example, if you define
 PX(x) =
 0
 {1 for x=1
 otherwise
 then 
X
 is a discrete random variable that can only take one value, i.e., 
X = 1
 with a
 probability of one. But this is not a very interesting distribution because it is not actually
 random. Then, you might ask what is the next simplest discrete distribution. And my
 answer to that is the <PERSON><PERSON><PERSON> distribution. A Bernoulli random variable is a random
 variable that can only take two possible values, usually 
0
 and 
1
 . This random variable
 models random experiments that have two possible outcomes, sometimes referred to
 as "success" and "failure." Here are some examples:
 You take a pass-fail exam. You either pass (resulting in 
X = 1
 ) or fail (resulting in
 X=0
 ).
 You toss a coin. The outcome is ether heads or tails.
 A child is born. The gender is either male or female.
 Formally, the <PERSON>oulli distribution is defined as follows:
Definition 3.4
 
A random variable 
X
 is said to be a <PERSON>oulli random variable with parameter 
p
 ,
 shown as 
X ∼ <PERSON>oulli(p)
 , if its PMF is given by
 p
 PX(x) =
 where 
0 < p <1
 .
 ⎧
 ⎪
 ⎨
 ⎩
 ⎪
 1 −p
 0
 for x = 1
 for x = 0
 otherwise
 Figure 3.2 shows the PMF of a 
Bernoulli(p)
 random variable.
 Fig.3.2 - PMF of a 
Bernoulli(p)
 random variable.
 A Bernoulli random variable is associated with a certain event 
A
 . If event 
A
 occurs (for
 example, if you pass the test), then 
X = 1
 ; otherwise 
X = 0
 . For this reason the
 Bernoulli random variable, is also called the indicator random variable. In particular,
 the indicator random variable 
IA
 for an event 
A
 is defined by
 IA =
 {1
 0
 if the event A occurs
 otherwise
 The indicator random variable for an event 
A
 has Bernoulli distribution with parameter 
p =P(A)
 , so we can write
 IA ∼Bernoulli(P(A)).
Geometric Distribution
 
The random experiment behind the geometric distribution is as follows. Suppose that I
 have a coin with 
P(H) = p
 . I toss the coin until I observe the first heads. We define 
X
 as the total number of coin tosses in this experiment. Then 
X
 is said to have geometric
 distribution with parameter 
p
 . In other words, you can think of this experiment as
 repeating independent Bernoulli trials until observing the first success. This is exactly
 the same distribution that we saw in 
Example 3.4. The range of 
X
 here is 
RX ={1,2,3,...}
 . In Example 3.4, we obtained
 PX(k) = P(X =k)=(1−p)k−1p, for k = 1,2,3,...
 We usually define 
q = 1 −p
 , so we can write 
PX(k) = pqk−1, for k = 1,2,3,...
 . To say
 that a random variable has geometric distribution with parameter 
p
 , we write 
X∼Geometric(p)
 . More formally, we have the following definition:
 Definition 3.5
 
A random variable 
X
 is said to be a geometric random variable with parameter 
p
 ,
 shown as 
X ∼ Geometric(p)
 , if its PMF is given by
 PX(k) =
 {p(1−p)k−1
 0
 where 
0 < p <1
 .
 for k = 1,2,3,...
 otherwise
 Figure 3.3 shows the PMF of a 
Geometric(0.3)
 random variable.
 Fig.3.3 - PMF of a 
Geometric(0.3)
 random variable.
We should note that some books define geometric random variables slightly differently.
 They define the geometric random variable 
X
 as the total number of failures before
 observing the first success. By this definition the range of 
X
 is 
RX = {0,1,2,...}
 and
 the PMF is given by
 for k = 0,1,2,3,...
 PX(k) =
 {p(1−p)k
 0
 otherwise
 In this book, whenever we write 
X ∼ Geometric(p)
 , we always mean 
X
 as the total
 number of trials as defined in Definition 3.5. Note that as long as you are consistent in
 your analysis, it does not matter which definition you use. That is why we emphasize
 that you should understand how to derive PMFs for these random variables rather
 than memorizing them.
 Binomial Distribution
 
The random experiment behind the binomial distribution is as follows. Suppose that I
 have a coin with 
P(H) = p
 . I toss the coin 
n
 times and define 
X
 to be the total number
 of heads that I observe. Then 
X
 is binomial with parameter 
n
 and 
p
 , and we write 
X∼Binomial(n,p)
 . The range of 
X
 in this case is 
RX = {0,1,2,...,n}
 . As we have
 seen in Section 2.1.3, the PMF of 
X
 in this case is given by binomial formula
 PX(k) =
 n
 k
 ( ) pk(1−p)n−k, for k = 0,1,2,...,n.
 We have the following definition:
 Definition 3.6
 
A random variable 
X
 is said to be a binomial random variable with parameters 
n
 and 
p
 , shown as 
X ∼ Binomial(n,p)
 , if its PMF is given by
 n
 k
 PX(k) =
 {( )pk(1−p)n−k
 0
 where 
0 < p <1
 .
 for k = 0,1,2,⋯,n
 otherwise
 Figures 3.4 and 3.5 show the 
Binomial(n,p)
 PMF for 
n = 10
 , 
p = 0.3
 and 
n = 20
 , 
p =0.6
 respectively.
Fig.3.4 - PMF of a 
Binomial(10,0.3)
 random variable.
 
Fig.3.5 - PMF of a 
Binomial(20,0.6)
 random variable.
 Binomial random variable as a sum of Bernoulli random variables
 
Here is a useful way of thinking about a binomial random variable. Note that a 
Binomial(n,p)
 random variable can be obtained by 
n
 independent coin tosses. If we
 think of each coin toss as a 
Bernoulli(p)
 random variable, the 
Binomial(n,p)
 random
 variable is a sum of 
n
 independent 
Bernoulli(p)
 random variables. This is stated more
 precisely in the following lemma.
 Lemma 3.1
 
If 
X1,X2,...,Xn
 are independent 
Bernoulli(p)
 random variables, then the random
 variable 
X
 defined by 
X = X1 +X2+...+Xn
 has a 
Binomial(n,p)
 distribution.
 To generate a random variable 
X ∼ Binomial(n,p)
 , we can toss a coin 
n
 times and
 count the number of heads. Counting the number of heads is exactly the same as
 finding 
X1 +X2+...+Xn
 , where each 
Xi
 is equal to one if the corresponding coin toss
 results in heads and zero otherwise. This interpretation of binomial random variables is
 sometimes very helpful. Let's look at an example.
Example 3.7
 
Let 
X ∼ Binomial(n,p)
 and 
Y ∼ Binomial(m,p)
 be two independent random
 variables. Define a new random variable as 
Z = X +Y
 . Find the PMF of 
Z
 .
 Solution
 Since 
X ∼ Binomial(n,p)
 , we can think of 
X
 as the number of heads in 
n
 independent coin tosses, i.e., we can write
 X=X1+X2+...+Xn,
 where the 
Xi
 's are independent 
Bernoulli(p)
 random variables. Similarly, since 
Y ∼Binomial(m,p)
 , we can think of 
Y
 as the number of heads in 
m
 independent coin
 tosses, i.e., we can write
 Y =Y1+Y2+...+Ym,
 where the 
Yj
 's are independent 
Bernoulli(p)
 random variables. Thus, the random
 variable 
Z = X +Y
 will be the total number of heads in 
n +m
 independent coin
 tosses:
 Z =X+Y =X1+X2+...+Xn+Y1+Y2+...+Ym,
 where the 
Xi
 's and 
Yj
 's are independent 
Bernoulli(p)
 random variables. Thus, by
 Lemma 3.1, 
Z
 is a binomial random variable with parameters 
m + n
 and 
p
 , i.e., 
Binomial(m+n,p)
 . Therefore, the PMF of 
Z
 is
 for k = 0,1,2,3,...,m +n
 PZ(k) =
 m+n
 k
 0
 {( )pk(1−p)m+n−k
 otherwise
 The above solution is elegant and simple, but we may also want to directly obtain the
 PMF of 
Z
 using probability rules. Here is another method to solve Example 3.7. First,
 we note that 
RZ = {0,1,2,...,m +n}
 . For 
k ∈ RZ
 , we can write
 PZ(k) = P(Z =k) =P(X+Y =k).
 We will find 
P(X +Y = k)
 by using conditioning and the law of total probability. In
 particular, we can write
 PZ(k) = P(X+Y =k)
 =∑n
 i=0 P(X+Y =k|X =i)P(X =i)
 =∑n
 i=0 P(Y =k−i|X =i)P(X =i)
 =∑n
 i=0 P(Y =k−i)P(X =i)
 (law of total probability)
m
 k−i
 m
 k−i
 n
 i
 n
 i
 =∑n
 i=0 ( )pk−i(1−p)m−k+i
 ( )pi(1−p)n−i
 =∑n
 i=0 ( )( )pk(1−p)m+n−k
 =pk(1−p)m+n−k
 ∑n
 i=0 ( )( )
 =( )pk(1−p)m+n−k
 m
 k−i
 m+n
 k
 n
 i
 (by Example 2.8 (part 3)).
 Thus, we have proved 
Z ∼ Binomial(m +n,p)
 by directly finding the PMF of 
Z
 .
 Negative Binomial (Pascal) Distribution
 
The negative binomial or Pascal distribution is a generalization of the geometric
 distribution. It relates to the random experiment of repeated independent trials until
 observing 
m
 successes. Again, different authors define the Pascal distribution slightly
 differently, and as we mentioned before if you understand one of them you can easily
 derive the other ones. Here is how we define the Pascal distribution in this book.
 Suppose that I have a coin with 
P(H) = p
 . I toss the coin until I observe 
m
 heads,
 where 
m ∈ N
 . We define 
X
 as the total number of coin tosses in this experiment. Then
 X
 is said to have Pascal distribution with parameter 
m
 and 
p
 . We write 
X∼Pascal(m,p)
 . Note that 
Pascal(1,p) = Geometric(p)
 . Note that by our definition
 the range of 
X
 is given by 
RX
 =
 {m
 , 
m +1
 , 
m +2
 , 
m +3
 , 
⋯}
 .
 Let us derive the PMF of a 
Pascal(m,p)
 random variable 
X
 . Suppose that I toss the
 coin until I observe 
m
 heads, and 
X
 is defined as the total number of coin tosses in
 this experiment. To find the probability of the event 
A = {X = k}
 , we argue as follows.
 By definition, event 
A
 can be written as 
A = B ∩ C
 , where
 B
 is the event that we observe 
m − 1
 heads (successes) in the first 
k − 1
 trials,
 and
 C
 is the event that we observe a heads in the 
k
 th trial.
 Note that 
B
 and 
C
 are independent events because they are related to different
 independent trials (coin tosses). Thus we can write
 P(A) =P(B∩C)=P(B)P(C).
 Now, we have 
P(C) = p
 . Note also that 
P(B)
 is the probability that I observe observe 
m−1
 heads in the 
k−1
 coin tosses. This probability is given by the binomial formula,
 in particular
 P(B) =
 (
 k−1
 m−1
 ) pm−1(1 −p)((k−1)−(m−1)) = (
 k−1
 m−1
 ) pm−1(1 −p)k−m
 .
Thus, we obtain
 (
 ) pm(1−p)k−m.
 P(A) =P(B∩C)=P(B)P(C)=
 k−1
 m−1
 To summarize, we have the following definition for the Pascal random variable
 Definition 3.7
 
A random variable 
X
 is said to be a Pascal random variable with parameters 
m
 and 
p
 , shown as 
X ∼ Pascal(m,p)
 , if its PMF is given by
 PX(k) =
 k−1
 m−1
 0
 {( )pm(1−p)k−m
 where 
0 < p <1
 .
 for k = m,m+1,m+2,m+3,...
 otherwise
 Figure 3.6 shows the PMF of a 
Pascal(m,p)
 random variable with 
m = 3
 and 
p = 0.5
 .
 Fig.3.6 - PMF of a 
Pascal(3,0.5)
 (negative binomial) random variable.
 Hypergeometric Distribution
 
Here is the random experiment behind the hypergeometric distribution. You have a
 bag that contains 
b
 blue marbles and 
r
 red marbles. You choose 
k ≤ b + r
 marbles at
 random (without replacement). Let 
X
 be the number of blue marbles in your sample.
 By this definition, we have 
X ≤ min(k,b)
 . Also, the number of red marbles in your
 sample must be less than or equal to 
r
 , so we conclude 
X ≥ max(0,k −r)
 . Therefore,
the range of 
X
 is given by 
RX ={max(0,k−r),max(0,k−r)+1,max(0,k−r)+2,...,min(k,b)}
 .
 b+r
 k
 To find 
PX(x)
 , note that the total number of ways to choose 
k
 marbles from 
b + r
 marbles is 
( )
 . The total number of ways to choose 
x
 blue marbles and 
k − x
 red
 marbles is 
( )( )
 . Thus, we have
 b
 x
 r
 k−x
 b
 x
 ( )( )
 r
 k−x
 PX(x) = , for x∈RX.
 ( )
 b+r
 k
 The following definition summarizes the discussion above.
 Definition 3.8
 
A random variable 
X
 is said to be a Hypergeometric random variable with
 parameters 
b,r
 and 
k
 , shown as 
X ∼ Hypergeometric(b,r,k)
 , if its range is 
RX ={max(0,k−r),max(0,k−r)+1,max(0,k−r)+2,...,min(k,b)}
 , and its
 PMF is given by
 b
 x
 r
 ( )( )
 k−x
 PX(x) =
 ⎧
 ⎪
 ⎨
 ⎪
 b+r
 k
 ( )
 0
 ⎩
 for x∈RX
 otherwise
 Again, there is no point to memorizing the PMF. All you need to know is how to solve
 problems that can be formulated as a hypergeometric random variable.
 Poisson Distribution
 
The Poisson distribution is one of the most widely used probability distributions. It is
 usually used in scenarios where we are counting the occurrences of certain events in
 an interval of time or space. In practice, it is often an approximation of a real-life
 random variable. Here is an example of a scenario where a Poisson random variable
 might be used. Suppose that we are counting the number of customers who visit a
 certain store from 
1pm
 to 
2pm
 . Based on data from previous days, we know that on
 average 
λ = 15
 customers visit the store. Of course, there will be more customers
 some days and fewer on others. Here, we may model the random variable 
X
 showing
 the number customers as a Poisson random variable with parameter 
λ = 15
 . Let us
 introduce the Poisson PMF first, and then we will talk about more examples and
 interpretations of this distribution.
Definition 3.9
 
A random variable 
X
 is said to be a Poisson random variable with parameter 
λ
 ,
 shown as 
X ∼ Poisson(λ)
 , if its range is 
RX = {0,1,2,3,...}
 , and its PMF is given
 by
 PX(k) =
 e−λλk
 k!
 0
 { for k∈RX
 otherwise
 Before going any further, let's check that this is a valid PMF. First, we note that 
PX(k) ≥ 0
 for all 
k
 . Next, we need to check 
∑k∈RX PX(k) = 1
 . To do that, let us first
 remember the Taylor series for 
ex
 , 
ex = ∑∞
 k=0
 xk
 k!
 . Now we can write
 ∑k∈RX 
PX(k) = ∑∞
 k=0
 e−λλk
 k!
 λk
 k!
 =e−λ
 ∑∞
 k=0
 =e−λeλ
 =1
 .
 Figures 3.7, 3.8, and 3.9 show the 
Poisson(λ)
 PMF for 
λ = 1
 , 
λ = 5
 , and 
λ = 10
 respectively.
Fig.3.7 - PMF of a 
Poisson(1)
 random variable.
 
Fig.3.8 - PMF of a 
Poisson(5)
 random variable.
 
Fig.3.9 - PMF of a 
Poisson(10)
 random variable.
 
Now let's look at an example.
 
Example 3.8
 The number of emails that I get in a weekday can be modeled by a Poisson
 distribution with an average of 
0.2
 emails per minute.
 1. What is the probability that I get no emails in an interval of length 
5
 minutes?
 2. What is the probability that I get more than 
3
 emails in an interval of length 
10 minutes?
 Solution
 1. Let 
X
 be the number of emails that I get in the 
5-minute interval. Then, by the
 assumption 
X
 is a Poisson random variable with parameter 
λ=5(0.2)=1
 ,
 P(X=0)=PX(0)= = = ≈0.3679
 2. Let 
Y
 be the number of emails that I get in the 
10-minute interval. Then by the
 assumption 
Y
 is a Poisson random variable with parameter 
λ=10(0.2)=2
 ,
 P(Y>3)=1−P(Y≤3)
 =1−(PY(0)+PY(1)+PY(2)+PY(3))
 =1−e−λ− − −
 =1−e−2− − −
 =1−e−2
 (1+2+2+ )
 =1− ≈0.1429
 
 
Poisson as an approximation for binomial
 The Poisson distribution can be viewed as the limit of binomial distribution. Suppose 
X∼Binomial(n,p)
 where 
n
 is very large and 
p
 is very small. In particular, assume
 that 
λ=np
 is a positive constant. We show that the PMF of 
X
 can be approximated by
 the PMF of a 
Poisson(λ)
 random variable. The importance of this is that Poisson PMF
 is much easier to compute than the binomial. Let us state this as a theorem.
 Theorem 3.1
 
e−λλ0
 0!
 e−1⋅1
 1
 1
 e
 e−λλ
 1!
 e−λλ2
 2!
 e−λλ3
 3!
 2e−2
 1
 4e−2
 2
 8e−2
 6
 8
 6
 19
 3e2
n
 λ
 Let 
X ∼ Binomial(n,p = )
 , where 
λ > 0
 is fixed. Then for any 
k ∈ {0,1,2,...}
 , we
 have
 lim
 n→∞PX(k) = .
 e−λλk
 k!
 Proof
 
We have
 λ
 limn→∞PX(k) = limn→∞( )( )
 k
 (1− )
 n−k
 n
 k
 λ
 n
 n!
 k!(n−k)!
 n
 1
 =λklimn→∞ ( )(1− )
 n−k
 nk
 λk
 k!
 λ
 n
 = .limn→∞
 n(n−1)(n−2)...(n−k+1)
 nk
 λ
 λ
 ([ ][(1− )
 n
 ][(1− )−k
 ]) .
 n
 Note that for a fixed 
k
 , we have
 limn→∞
 n(n−1)(n−2)...(n−k+1)
 nk
 λ
 n
 limn→∞(1− )−k 
=1
 n
 =e−λ
 .
 limn→∞(1− )
 λ
 n
 Thus, we conclude
 lim
 n→∞PX(k) = .
 e−λλk
 k!
 =1
 n
3.1.6 Solved Problems:
 
Discrete Random Variables
 Problem 1
 
Let 
X
 be a discrete random variable with the following PMF
 ⎧
 ⎪ 
⎪ 
⎪ 
⎪ 
PX(x) =
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪⎩
 0.1
 0.2
 0.2
 0.3
 0.2
 0
 for x = 0.2
 for x = 0.4
 for x = 0.5
 for x = 0.8
 for x = 1
 otherwise
 a. Find 
RX
 , the range of the random variable 
X
 .
 b. Find 
P(X ≤ 0.5)
 .
 c. Find 
P(0.25 < X < 0.75)
 .
 d. Find 
P(X = 0.2|X < 0.6)
 .
 Solution
 a. The range of 
X
 can be found from the PMF. The range of 
X
 consists of possible
 values for 
X
 . Here we have
 RX ={0.2,0.4,0.5,0.8,1}.
 b. The event 
X ≤ 0.5
 can happen only if 
X
 is 
0.2,0.4,
 or 
0.5
 . Thus,
 P(X ≤0.5) =P(X ∈ {0.2,0.4,0.5})
 =P(X=0.2)+P(X =0.4)+P(X =0.5)
 =PX(0.2)+PX(0.4)+PX(0.5)
 =0.1+0.2+0.2 =0.5
 c. Similarly, we have
 P(0.25 < X <0.75) =P(X ∈ {0.4,0.5})
 =P(X=0.4)+P(X =0.5)
 =PX(0.4)+PX(0.5)
 =0.2+0.2 =0.4
d. This is a conditional probability problem, so we can use our famous formula
 P(A|B)=
 . We have
 P(X=0.2|X<0.6)=
 =
 =
 = =0.2
 
 
Problem 2
 I roll two dice and observe two numbers 
X
 and 
Y
 .
 a. Find 
RX,RY
 and the PMFs of 
X
 and 
Y
 .
 b. Find 
P(X=2,Y=6)
 .
 c. Find 
P(X>3|Y=2)
 .
 d. Let 
Z=X+Y
 . Find the range and PMF of 
Z
 .
 e. Find 
P(X=4|Z=8)
 .
 Solution
 a. We have 
RX=RY={1,2,3,4,5,6}
 . Assuming the dice are fair, all values are
 equally likely so
 PX(k)=
 { for k=1,2,3,4,5,6
 0 otherwise
 Similarly for 
Y
 ,
 PY(k)=
 { for k=1,2,3,4,5,6
 0 otherwise
 b. Since 
X
 and 
Y
 are independent random variables, we can write
 P(X=2,Y=6)=P(X=2)P(Y=6)
 = ⋅ =
 .
 
P(A∩B)
 P(B)
 P((X=0.2) and (X<0.6))
 P(X<0.6)
 P(X=0.2)
 P(X<0.6)
 PX(0.2)
 PX(0.2)+PX(0.4)+PX(0.5)
 0.1
 0.1+0.2+0.2
 1
 6
 1
 6
 1
 6
 1
 6
 1
 36
c. Since 
X
 and 
Y
 are independent, knowing the value of 
X
 does not impact the
 probabilities for 
Y
 ,
 P(X>3|Y=2)=P(X>3)
 =PX(4)+PX(5)+PX(6)
 = + + =
 .
 
d. First, we have 
RZ={2,3,4,...,12}
 . Thus, we need to find 
PZ(k)
 for
 k=2,3,...,12
 . We have
 PZ(2)=P(Z=2)=P(X=1,Y=1)
 = ⋅ =
 ;
 PZ(3)=P(Z=3)=P(X=1,Y=2)+P(X=2,Y=1)
 =P(X=1)P(Y=2)+P(X=2)P(Y=1)
 = ⋅ + ⋅ =
 ;
 PZ(4)=P(Z=4)=P(X=1,Y=3)+P(X=2,Y=2)+P(X=3,Y=1)
 =3⋅ =
 .
 
We can continue similarly:
 PZ(5) = =
 ;
 PZ(6) =
 ;
 PZ(7) = =
 ;
 PZ(8) =
 ;
 PZ(9) = =
 ;
 PZ(10)= =
 ;
 PZ(11)= =
 ;
 PZ(12)=
 .
 
It is always a good idea to check our answers by verifying that 
∑z∈RZ
 PZ(z)=1
 .
 Here, we have
 ∑z∈RZPZ(z)= + + + + +
 + + + + +
 =1
 .
 
e. Note that here we cannot argue that 
X
 and 
Z
 are independent. Indeed, 
Z
 seems
 to completely depend on 
X
 , 
Z=X+Y
 . To find the conditional probability 
P(X=4|Z=8)
 , we use the formula for conditional probability
 1
 6
 1
 6
 1
 6
 1
 2
 1
 6
 1
 6
 1
 36
 1
 6
 1
 6
 1
 6
 1
 6
 1
 18
 1
 36
 1
 12
 4
 36
 1
 9
 5
 36
 6
 36
 1
 6
 5
 36
 4
 36
 1
 9
 3
 36
 1
 12
 2
 36
 1
 18
 1
 36
 1
 36
 2
 36
 3
 36
 4
 36
 5
 36
 6
 36
 5
 36
 4
 36
 3
 36
 2
 36
 1
 36
P(X=4|Z=8)=
 =
 =
 .
 
 
Problem 3
 I roll a fair die repeatedly until a number larger than 
4
 is observed. If 
N
 is the total
 number of times that I roll the die, find 
P(N=k)
 , for 
k=1,2,3,...
 .
 Solution
 In each trial, I may observe a number larger than 
4
 with probability 
=
 . Thus, you
 can think of this experiment as repeating a Bernoulli experiment with success
 probability 
p=
 until you observe the first success. Thus, 
N
 is a geometric random
 variable with parameter 
p=
 , 
N∼Geometric( )
 . Hence, we have
 PN(k)=
 { ( )k−1 for k=1,2,3,...
 0 otherwise
 
Problem 4
 You take an exam that contains 
20
 multiple-choice questions. Each question has 
4 possible options. You know the answer to 
10
 questions, but you have no idea about
 the other 
10
 questions so you choose answers randomly. Your score 
X
 on the exam is
 the total number of correct answers. Find the PMF of 
X
 . What is 
P(X>15)
 ?
 Solution
 Let's define the random variable 
Y
 as the number of your correct answers to the 
10 questions you answer randomly. Then your total score will be 
X=Y+10
 . First, let's
 find the PMF of 
Y
 . For each question your success probability is . Hence, you
 P(X=4,Z=8)
 P(Z=8)
 P(X=4,Y=4)
 P(Z=8)
 ⋅ 1
 6
 1
 6
 5
 36
 1
 5
 2
 6
 1
 3
 1
 3
 1
 3
 1
 3
 1
 3
 2
 3
 1
 4
perform 
10
 independent 
Bernoulli( )
 trials and 
Y
 is the number of successes. Thus,
 we conclude 
Y∼Binomial(10, )
 , so
 PY(y)=
 {( )( )y( )10−y for y=0,1,2,3,...,10
 0 otherwise
 Now we need to find the PMF of 
X=Y+10
 . First note that 
RX={10,11,12,...,20}
 .
 We can write
 PX(10)=P(X=10)=P(Y+10=10)
 =P(Y=0)=( )( )
 0
 ( )
 10−0
 =( )
 10;
 PX(11)=P(X=11)=P(Y+10=11)
 =P(Y=1)=( )( )
 1
 ( )
 10−1
 =10 ( )
 9.
 
So, you get the idea. In general for 
k∈RX={10,11,12,...,20}
 ,
 PX(k)=P(X=k)=P(Y+10=k)
 =P(Y=k−10)=( )( )
 k−10
 ( )20−k.
 
To summarize,
 PX(k)=
 {( )( )k−10( )20−k for k=10,11,12,...,20
 0 otherwise
 In order to calculate 
P(X>15)
 , we know we should consider 
y=6,7,8,9,10
 PY(y)=
 {( )( )y( )10−y for y=6,7,8,9,10
 0 otherwise
 PX(k)=
 {( )( )k−10( )20−k for k=16,17,...,20
 0 otherwise
 P(X>15)=PX(16)+PX(17)+PX(18)+PX(19)+PX(20)
 =( ) ( )6( )4+( ) ( )7( )3+( ) ( )8( )2
 +( ) ( )9( )1+( ) ( )10( )0.
 
Problem 5
 
1
 4
 1
 4
 10
 y
 1
 4
 3
 4
 10
 0
 1
 4
 3
 4
 3
 4
 10
 1
 1
 4
 3
 4
 1
 4
 3
 4
 10
 k−10
 1
 4
 3
 4
 10
 k−10
 1
 4
 3
 4
 10
 y
 1
 4
 3
 4
 10
 k−10
 1
 4
 3
 4
 10
 6
 1
 4
 3
 4
 10
 7
 1
 4
 3
 4
 10
 8
 1
 4
 3
 4
 10
 9
 1
 4
 3
 4
 10
 10
 1
 4
 3
 4
Let 
X ∼ Pascal(m,p)
 and 
Y ∼ Pascal(l,p)
 be two independent random variables.
 Define a new random variable as 
Z = X +Y
 . Find the PMF of 
Z
 .
 Solution
 This problem is very similar to 
Example 3.7, and we can solve it using the same
 methods. We will show that 
Z ∼ Pascal(m +l,p)
 . To see this, consider a sequence of
 H
 s and 
T
 s that is the result of independent coin tosses with 
P(H) = p
 , (Figure 3.2). If
 we define the random variable 
X
 as the number of coin tosses until the 
m
 th heads is
 observed, then 
X ∼ Pascal(m,p)
 . Now, if we look at the rest of the sequence and
 count the number of heads until we observe 
l
 more heads, then the number of coin
 tosses in this part of the sequence is 
Y ∼ Pascal(l,p)
 . Looking from the beginning, we
 have repeatedly tossed the coin until we have observed 
m + l
 heads. Thus, we
 conclude the random variable 
Z
 defined as 
Z = X +Y
 has a 
Pascal(m +l,p)
 distribution.
 Fig.3.2 - Sum of two Pascal random variables.
 In particular, remember that 
Pascal(1,p) = Geometric(p)
 . Thus, we have shown that if 
X
 and 
Y
 are two independent 
Geometric(p)
 random variables, then 
X +Y
 is a 
Pascal(2,p)
 random variable. More generally, we can say that if 
X1,X2,X3,...,Xm
 are
 m
 independent 
Geometric(p)
 random variables, then the random variable 
X
 defined
 by 
X =X1 +X2+...+Xm
 has a 
Pascal(m,p)
 distribution.
 Problem 6
 
The number of customers arriving at a grocery store is a Poisson random variable. On
 average 
10
 customers arrive per hour. Let 
X
 be the number of customers arriving from
 10am
 to 
11 : 30am
 . What is 
P(10 < X ≤ 15)
 ?
Solution
 We are looking at an interval of length 
1.5
 hours, so the number of customers in this
 interval is 
X ∼ Poisson(λ = 1.5 ×10 = 15)
 . Thus,
 P(10 <X ≤15)=∑15
 k=11PX(k)
 =∑15
 k=11
 e−1515k
 k!
 1511
 11!
 1512
 12!
 1513
 13!
 1514
 14!
 =e−15 
[ + + + + ]
 =0.4496
 1515
 15!
 Problem 7
 
Let 
X ∼ Poisson(α)
 and 
Y ∼ Poisson(β)
 be two independent random variables.
 Define a new random variable as 
Z = X +Y
 . Find the PMF of 
Z
 .
 Solution
 First note that since 
RX = {0,1,2,..}
 and 
RY = {0,1,2,..}
 , we can write 
RZ ={0,1,2,..}
 . We have
 PZ(k) = P(X+Y =k)
 =∑k
 i=0 P(X+Y =k|X =i)P(X =i) (law of total probability)
 =∑k
 i=0 P(Y =k−i|X =i)P(X =i)
 =∑k
 i=0 P(Y =k−i)P(X =i)
 =∑k
 i=0
 e−ββk−i
 (k−i)!
 =e−(α+β)
 ∑k
 i=0
 e−(α+β)
 k!
 e−ααi
 i!
 α
 iβk−i
 (k−i)!i!
 = ∑k
 i=0 αiβk−i
 k!
 (k−i)!i!
 e−(α+β)
 k!
 = ∑k
 i=0()αiβk−i
 e−(α+β)
 k!
 k
 i
 = (α+β)k
 Thus, we conclude that 
Z ∼ Poisson(α +β)
 .
 (by the binomial theorem)
 .
 
Problem 8
 Let 
X
 be a discrete random variable with the following PMF
 PX(k)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 for k=−2
 for k=−1
 for k=0
 for k=1
 for k=2
 0 otherwise
 I define a new random variable 
Y
 as 
Y=(X+1)2
 .
 a. Find the range of 
Y
 .
 b. Find the PMF of 
Y
 .
 Solution
 Here, the random variable 
Y
 is a function of the random variable 
X
 . This means that
 we perform the random experiment and obtain 
X=x
 , and then the value of 
Y
 is
 determined as 
Y=(x+1)2
 . Since 
X
 is a random variable, 
Y
 is also a random
 variable.
 a. To find 
RY
 , we note that 
RX={−2,−1,0,1,2}
 , and
 RY={y=(x+1)2|x∈RX}
 ={0,1,4,9}
 .
 
b. Now that we have found 
RY={0,1,4,9}
 , to find the PMF of 
Y
 we need to find
 PY(0),PY(1),PY(4)
 , and 
PY(9)
 :
 PY(0)=P(Y=0)=P((X+1)2=0)
 =P(X=−1)=
 ;
 PY(1)=P(Y=1)=P((X+1)2=1)
 =P((X=−2) or (X=0))
 ;
 PX(−2)+PX(0)= + =
 ;
 PY(4)=P(Y=4)=P((X+1)2=4)
 =P(X=1)=
 ;
 PY(9)=P(Y=9)=P((X+1)2=9)
 =P(X=2)=
 .
 1
 4
 1
 8
 1
 8
 1
 4
 1
 4
 1
 8
 1
 4
 1
 8
 3
 8
 1
 4
 1
 4
Again, it is always a good idea to check that 
∑y∈RY 
PY (y) = 1
 . We have
 ∑
 y∈RY
 1
 3
 1
 1
 PY(y) = + + + =1.