<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wald Test - Comprehensive Step-by-Step Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <!-- Plotly.js for interactive graphs -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --wald-color: #7c3aed;
            --stat-color: #059669;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #1e3a8a 0%, #7c3aed 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--wald-color), var(--accent-color));
        }

        .header h1 {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: var(--text-secondary);
            font-weight: 300;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            padding: 8px 16px;
            background: var(--wald-color);
            color: white;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
        }

        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border-left: 5px solid var(--primary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section h3 {
            color: var(--secondary-color);
            font-size: 1.6em;
            margin: 25px 0 15px 0;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 8px;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .highlight-box {
            background: linear-gradient(135deg, #dbeafe 0%, #f3e8ff 100%);
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .highlight-box::before {
            content: '📊';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .formula-box {
            background: #f8fafc;
            border: 2px solid var(--accent-color);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
        }

        .example-box {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            border: 2px solid var(--success-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .example-box::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .wald-box {
            background: linear-gradient(135deg, #f3e8ff 0%, #ede9fe 100%);
            border: 2px solid var(--wald-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .wald-box::before {
            content: '🎯';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .definition-box {
            background: linear-gradient(135deg, #fef3cd 0%, #fde68a 100%);
            border: 2px solid var(--warning-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .definition-box::before {
            content: '📚';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: var(--light-bg);
            border-radius: 10px;
        }

        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: var(--light-bg);
            border-radius: 10px;
            border-left: 4px solid var(--accent-color);
            position: relative;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: var(--accent-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .math-display {
            overflow-x: auto;
            padding: 10px 0;
        }

        .navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 200px;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            text-decoration: none;
            color: var(--text-secondary);
            border-radius: 5px;
            margin: 5px 0;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .nav-link:hover {
            background: var(--primary-color);
            color: white;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: var(--primary-color);
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table tr:nth-child(even) {
            background: var(--light-bg);
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>The Wald Test</h1>
            <p class="subtitle">A Comprehensive Step-by-Step Tutorial</p>
            <div>
                <span class="badge">Statistical Testing</span>
                <span class="badge">Asymptotic Theory</span>
                <span class="badge">Hypothesis Testing</span>
                <span class="badge">Maximum Likelihood</span>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <a href="#introduction" class="nav-link">Introduction</a>
            <a href="#mathematical-foundation" class="nav-link">Foundation</a>
            <a href="#test-statistic" class="nav-link">Test Statistic</a>
            <a href="#bernoulli-example" class="nav-link">Bernoulli Example</a>
            <a href="#power-analysis" class="nav-link">Power Analysis</a>
            <a href="#asymptotic-properties" class="nav-link">Asymptotic Properties</a>
            <a href="#practical-guide" class="nav-link">Practical Guide</a>
            <a href="#comparison" class="nav-link">Comparison</a>
        </div>

        <!-- Introduction Section -->
        <div class="section" id="introduction">
            <h2><span class="step-number">1</span>Introduction to the Wald Test</h2>
            
            <p>The <strong>Wald Test</strong> is a fundamental statistical test used when the <em>Neyman-Pearson (NP) test is no longer applicable</em>—specifically when we're testing a simple null hypothesis against a possibly composite alternative. Named after Abraham Wald, this test has become a cornerstone of modern statistical inference.</p>

            <div class="highlight-box">
                <h3>When to Use the Wald Test</h3>
                <p>The Wald test is particularly valuable when:</p>
                <ul>
                    <li><strong>NP test doesn't apply:</strong> When dealing with composite alternatives</li>
                    <li><strong>Large sample sizes:</strong> Relies on asymptotic normality</li>
                    <li><strong>Maximum Likelihood Estimation:</strong> When you have an MLE available</li>
                    <li><strong>General parametric models:</strong> Works across many statistical distributions</li>
                </ul>
            </div>

            <div class="definition-box">
                <h3>Problem Setup</h3>
                <p>We are interested in testing hypotheses in a parametric model:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$H_0: \theta = \theta_0$$
                        $$H_1: \theta \neq \theta_0$$
                    </div>
                </div>
                <p>Where $\theta$ is the parameter of interest and $\theta_0$ is the specific value we're testing against.</p>
            </div>

            <div class="visualization">
                <svg width="100%" height="350" viewBox="0 0 900 350">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#dbeafe;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="350" fill="url(#bgGradient1)" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">Wald Test Conceptual Framework</text>
                    
                    <!-- Data -->
                    <rect x="50" y="60" width="120" height="80" fill="#ecfdf5" rx="10" stroke="#059669" stroke-width="2"/>
                    <text x="110" y="85" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold">Sample Data</text>
                    <text x="110" y="105" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold">X₁, X₂, ..., Xₙ</text>
                    <text x="110" y="125" text-anchor="middle" fill="#059669" font-size="12">n → ∞</text>
                    
                    <!-- Estimator -->
                    <rect x="230" y="60" width="140" height="80" fill="#f3e8ff" rx="10" stroke="#7c3aed" stroke-width="2"/>
                    <text x="300" y="85" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold">Estimator θ̂</text>
                    <text x="300" y="105" text-anchor="middle" fill="#7c3aed" font-size="12">(Usually MLE)</text>
                    <text x="300" y="125" text-anchor="middle" fill="#7c3aed" font-size="12">θ̂ ~ N(θ₀, σ₀²)</text>
                    
                    <!-- Test Statistic -->
                    <rect x="430" y="60" width="140" height="80" fill="#dbeafe" rx="10" stroke="#3b82f6" stroke-width="2"/>
                    <text x="500" y="85" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="bold">Wald Statistic</text>
                    <text x="500" y="105" text-anchor="middle" fill="#3b82f6" font-size="12">Tₙ = (θ̂ - θ₀)/σ₀</text>
                    <text x="500" y="125" text-anchor="middle" fill="#3b82f6" font-size="12">Tₙ ~ N(0,1)</text>
                    
                    <!-- Decision -->
                    <rect x="630" y="60" width="140" height="80" fill="#fef3cd" rx="10" stroke="#d97706" stroke-width="2"/>
                    <text x="700" y="85" text-anchor="middle" fill="#d97706" font-size="14" font-weight="bold">Decision Rule</text>
                    <text x="700" y="105" text-anchor="middle" fill="#d97706" font-size="12">Reject H₀ if</text>
                    <text x="700" y="125" text-anchor="middle" fill="#d97706" font-size="12">|Tₙ| ≥ z_{α/2}</text>
                    
                    <!-- Arrows -->
                    <defs>
                        <marker id="arrow1" markerWidth="8" markerHeight="6" 
                                refX="8" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#374151" />
                        </marker>
                    </defs>
                    
                    <!-- Flow arrows -->
                    <line x1="170" y1="100" x2="220" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    <line x1="370" y1="100" x2="420" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    <line x1="570" y1="100" x2="620" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    
                    <!-- Key Properties -->
                    <rect x="50" y="180" width="800" height="140" fill="#fffbeb" rx="10" stroke="#f59e0b" stroke-width="2"/>
                    <text x="450" y="205" text-anchor="middle" fill="#92400e" font-size="16" font-weight="bold">Key Properties of the Wald Test</text>
                    
                    <text x="70" y="235" fill="#92400e" font-size="13">• <strong>Asymptotic:</strong> Based on large-sample theory (n → ∞)</text>
                    <text x="70" y="255" fill="#92400e" font-size="13">• <strong>General:</strong> Works with any asymptotically normal estimator</text>
                    <text x="470" y="235" fill="#92400e" font-size="13">• <strong>Flexible:</strong> Handles composite alternatives naturally</text>
                    <text x="470" y="255" fill="#92400e" font-size="13">• <strong>Practical:</strong> Easy to compute with MLE</text>
                    
                    <text x="70" y="285" fill="#92400e" font-size="13">• <strong>Distribution:</strong> Test statistic follows standard normal under H₀</text>
                    <text x="470" y="285" fill="#92400e" font-size="13">• <strong>Two-sided:</strong> Tests θ ≠ θ₀ (can be adapted for one-sided)</text>
                    
                    <text x="450" y="310" text-anchor="middle" fill="#92400e" font-size="12" font-style="italic">
                        The Wald test provides a unified framework for hypothesis testing in parametric models
                    </text>
                </svg>
            </div>

            <div class="wald-box">
                <h3>Core Intuition</h3>
                <p>The fundamental idea behind the Wald test is elegantly simple:</p>
                <p><em>If the null hypothesis is true, then our estimator $\hat{\theta}$ should be close to $\theta_0$. If they're "too far apart" (relative to the estimator's variability), we reject the null hypothesis.</em></p>
                <p>The "distance" is measured in units of standard deviations, which gives us a standardized test statistic that follows a known distribution.</p>
            </div>
        </div>

        <!-- Mathematical Foundation Section -->
        <div class="section" id="mathematical-foundation">
            <h2><span class="step-number">2</span>Mathematical Foundation</h2>
            
            <p>The Wald test is based on the <strong>asymptotic normality</strong> of estimators. Let's understand the mathematical foundation step by step.</p>

            <div class="definition-box">
                <h3>Asymptotically Normal Estimator</h3>
                <p>The Wald test requires access to an estimator $\hat{\theta}$ which, under the null hypothesis, satisfies:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\hat{\theta} \xrightarrow{d} N(\theta_0, \sigma_0^2)$$
                    </div>
                </div>
                <p>Where:</p>
                <ul>
                    <li>$\hat{\theta}$ is our estimator (typically the MLE)</li>
                    <li>$\theta_0$ is the true parameter value under $H_0$</li>
                    <li>$\sigma_0^2$ is the variance of the estimator under the null</li>
                    <li>$\xrightarrow{d}$ denotes convergence in distribution</li>
                </ul>
            </div>

            <div class="wald-box">
                <h3>The Canonical Example: Maximum Likelihood Estimator</h3>
                <p>The most common choice for $\hat{\theta}$ is the <strong>Maximum Likelihood Estimator (MLE)</strong> because:</p>
                <ol>
                    <li><strong>Consistency:</strong> $\hat{\theta}_{MLE} \xrightarrow{p} \theta_0$ under $H_0$</li>
                    <li><strong>Asymptotic Normality:</strong> Under regularity conditions:
                        <div class="formula-box">
                            <div class="math-display">
                                $$\sqrt{n}(\hat{\theta}_{MLE} - \theta_0) \xrightarrow{d} N(0, I^{-1}(\theta_0))$$
                            </div>
                        </div>
                        where $I(\theta_0)$ is the Fisher Information at $\theta_0$
                    </li>
                    <li><strong>Efficiency:</strong> Achieves the Cramér-Rao lower bound asymptotically</li>
                </ol>
            </div>

            <div class="visualization">
                <svg width="100%" height="400" viewBox="0 0 900 400">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ede9fe;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="400" fill="url(#bgGradient2)" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">Asymptotic Behavior of MLE</text>
                    
                    <!-- Small sample distribution -->
                    <text x="150" y="70" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">Small Sample (n=10)</text>
                    <ellipse cx="150" cy="130" rx="60" ry="30" fill="none" stroke="#7c3aed" stroke-width="3"/>
                    <text x="150" y="135" text-anchor="middle" fill="#7c3aed" font-size="12">Non-normal</text>
                    <text x="150" y="180" text-anchor="middle" fill="#7c3aed" font-size="11">High Variance</text>
                    
                    <!-- Medium sample distribution -->
                    <text x="450" y="70" text-anchor="middle" fill="#3b82f6" font-size="16" font-weight="bold">Medium Sample (n=100)</text>
                    <ellipse cx="450" cy="130" rx="40" ry="25" fill="none" stroke="#3b82f6" stroke-width="3"/>
                    <text x="450" y="135" text-anchor="middle" fill="#3b82f6" font-size="12">≈ Normal</text>
                    <text x="450" y="180" text-anchor="middle" fill="#3b82f6" font-size="11">Medium Variance</text>
                    
                    <!-- Large sample distribution -->
                    <text x="750" y="70" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">Large Sample (n→∞)</text>
                    <ellipse cx="750" cy="130" rx="25" ry="20" fill="none" stroke="#059669" stroke-width="3"/>
                    <text x="750" y="135" text-anchor="middle" fill="#059669" font-size="12">Normal</text>
                    <text x="750" y="180" text-anchor="middle" fill="#059669" font-size="11">Low Variance</text>
                    
                    <!-- True parameter line -->
                    <line x1="50" y1="130" x2="850" y2="130" stroke="#dc2626" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="850" y="125" fill="#dc2626" font-size="12" font-weight="bold">θ₀</text>
                    
                    <!-- Arrows showing convergence -->
                    <path d="M 200 130 Q 325 110 400 130" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                    <path d="M 500 130 Q 625 110 700 130" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                    
                    <!-- Mathematical expressions -->
                    <rect x="50" y="220" width="800" height="150" fill="#ffffff" rx="10" stroke="#6b7280" stroke-width="1"/>
                    <text x="450" y="245" text-anchor="middle" fill="#1f2937" font-size="16" font-weight="bold">Key Mathematical Properties</text>
                    
                    <text x="70" y="275" fill="#1f2937" font-size="14"><strong>1. Consistency:</strong></text>
                    <text x="90" y="295" fill="#1f2937" font-size="13">θ̂ₙ → θ₀ as n → ∞ (estimator converges to true value)</text>
                    
                    <text x="70" y="320" fill="#1f2937" font-size="14"><strong>2. Asymptotic Normality:</strong></text>
                    <text x="90" y="340" fill="#1f2937" font-size="13">√n(θ̂ₙ - θ₀) → N(0, I⁻¹(θ₀)) as n → ∞</text>
                    
                    <text x="470" y="275" fill="#1f2937" font-size="14"><strong>3. Variance Formula:</strong></text>
                    <text x="490" y="295" fill="#1f2937" font-size="13">Var(θ̂ₙ) ≈ I⁻¹(θ₀)/n for large n</text>
                    
                    <text x="470" y="320" fill="#1f2937" font-size="14"><strong>4. Standard Error:</strong></text>
                    <text x="490" y="340" fill="#1f2937" font-size="13">SE(θ̂ₙ) ≈ √(I⁻¹(θ₀)/n)</text>
                    
                    <text x="450" y="365" text-anchor="middle" fill="#6b7280" font-size="12" font-style="italic">
                        The asymptotic normality is the foundation that makes the Wald test possible
                    </text>
                </svg>
            </div>

            <div class="example-box">
                <h3>Why Asymptotic Normality Matters</h3>
                <p>The asymptotic normality property is crucial because:</p>
                <ol>
                    <li><strong>Known Distribution:</strong> We know the limiting distribution is normal</li>
                    <li><strong>Standardization:</strong> We can standardize to get a standard normal distribution</li>
                    <li><strong>Critical Values:</strong> We can use standard normal critical values for testing</li>
                    <li><strong>Generality:</strong> This property holds for a wide class of estimators</li>
                </ol>
            </div>
        </div>

        <!-- Test Statistic Construction Section -->
        <div class="section" id="test-statistic">
            <h2><span class="step-number">3</span>Test Statistic Construction</h2>
            
            <p>Now that we understand the mathematical foundation, let's construct the Wald test statistic step by step.</p>

            <div class="wald-box">
                <h3>Step 1: Basic Form</h3>
                <p>Given that $\hat{\theta} \sim N(\theta_0, \sigma_0^2)$ under $H_0$, we can standardize:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n = \frac{\hat{\theta} - \theta_0}{\sigma_0}$$
                    </div>
                </div>
                <p>Under the null hypothesis, $T_n \xrightarrow{d} N(0, 1)$ as $n \to \infty$.</p>
            </div>

            <div class="highlight-box">
                <h3>Step 2: Practical Challenge - Unknown Variance</h3>
                <p>In practice, $\sigma_0$ is usually unknown. We need to estimate it! This leads to the <strong>plug-in principle</strong>:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n = \frac{\hat{\theta} - \theta_0}{\hat{\sigma}_0}$$
                    </div>
                </div>
                <p>Where $\hat{\sigma}_0$ is a consistent estimator of $\sigma_0$.</p>
                
                <p><strong>Key Result:</strong> Under regularity conditions, this still gives us $T_n \xrightarrow{d} N(0, 1)$ under $H_0$.</p>
            </div>

            <div class="definition-box">
                <h3>Decision Rule</h3>
                <p>For a two-sided test at significance level $\alpha$:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Reject } H_0 \text{ if } |T_n| \geq z_{\alpha/2}$$
                    </div>
                </div>
                <p>Where $z_{\alpha/2}$ is the $(1-\alpha/2)$ quantile of the standard normal distribution.</p>
                
                <p><strong>Common Values:</strong></p>
                <ul>
                    <li>$\alpha = 0.05 \Rightarrow z_{\alpha/2} = z_{0.025} = 1.96$</li>
                    <li>$\alpha = 0.01 \Rightarrow z_{\alpha/2} = z_{0.005} = 2.576$</li>
                </ul>
            </div>

            <div class="visualization">
                <div id="normal-distribution-plot" style="width:100%; height:400px;"></div>
            </div>

            <script>
                // Create the normal distribution visualization
                document.addEventListener('DOMContentLoaded', function() {
                    // Generate x values
                    const x = [];
                    for (let i = -4; i <= 4; i += 0.01) {
                        x.push(i);
                    }
                    
                    // Generate y values for standard normal
                    const y = x.map(val => Math.exp(-0.5 * val * val) / Math.sqrt(2 * Math.PI));
                    
                    // Critical regions
                    const criticalLeft = x.filter(val => val <= -1.96);
                    const criticalRight = x.filter(val => val >= 1.96);
                    const yCriticalLeft = criticalLeft.map(val => Math.exp(-0.5 * val * val) / Math.sqrt(2 * Math.PI));
                    const yCriticalRight = criticalRight.map(val => Math.exp(-0.5 * val * val) / Math.sqrt(2 * Math.PI));
                    
                    const trace1 = {
                        x: x,
                        y: y,
                        type: 'scatter',
                        mode: 'lines',
                        name: 'N(0,1) under H₀',
                        line: {color: '#3b82f6', width: 3}
                    };
                    
                    const trace2 = {
                        x: criticalLeft,
                        y: yCriticalLeft,
                        type: 'scatter',
                        mode: 'lines',
                        fill: 'tonexty',
                        fillcolor: 'rgba(220, 38, 38, 0.3)',
                        name: 'Rejection Region (α/2)',
                        line: {color: '#dc2626', width: 2}
                    };
                    
                    const trace3 = {
                        x: criticalRight,
                        y: yCriticalRight,
                        type: 'scatter',
                        mode: 'lines',
                        fill: 'tonexty',
                        fillcolor: 'rgba(220, 38, 38, 0.3)',
                        name: 'Rejection Region (α/2)',
                        line: {color: '#dc2626', width: 2},
                        showlegend: false
                    };
                    
                    const layout = {
                        title: {
                            text: 'Wald Test: Standard Normal Distribution under H₀',
                            font: {size: 18, color: '#1f2937'}
                        },
                        xaxis: {
                            title: 'Test Statistic Tₙ',
                            gridcolor: '#e5e7eb'
                        },
                        yaxis: {
                            title: 'Density',
                            gridcolor: '#e5e7eb'
                        },
                        plot_bgcolor: '#f8fafc',
                        paper_bgcolor: '#ffffff',
                        annotations: [
                            {
                                x: -1.96,
                                y: 0.2,
                                text: '-z_{α/2} = -1.96',
                                showarrow: true,
                                arrowhead: 2,
                                arrowcolor: '#dc2626'
                            },
                            {
                                x: 1.96,
                                y: 0.2,
                                text: 'z_{α/2} = 1.96',
                                showarrow: true,
                                arrowhead: 2,
                                arrowcolor: '#dc2626'
                            },
                            {
                                x: 0,
                                y: 0.3,
                                text: 'Accept H₀<br>(1-α = 0.95)',
                                showarrow: false,
                                font: {color: '#059669', size: 14}
                            }
                        ]
                    };
                    
                    Plotly.newPlot('normal-distribution-plot', [trace1, trace2, trace3], layout, {responsive: true});
                });
                         </script>
         </div>

        <!-- Bernoulli Example Section -->
        <div class="section" id="bernoulli-example">
            <h2><span class="step-number">4</span>Detailed Example: Bernoulli Parameter Test</h2>
            
            <p>Let's work through the <strong>canonical example</strong> from the source material: testing the parameter of a Bernoulli distribution. This example beautifully illustrates all the key concepts of the Wald test.</p>

            <div class="definition-box">
                <h3>Problem Setup</h3>
                <p>Suppose we observe $X_1, X_2, \ldots, X_n \sim \text{Ber}(p)$ (independent Bernoulli trials) and we want to test:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$H_0: p = p_0 \quad \text{vs} \quad H_1: p \neq p_0$$
                    </div>
                </div>
                <p>where $p_0$ is some specific value we're testing against.</p>
            </div>

            <div class="example-box">
                <h3>Step 1: Find the Estimator</h3>
                <p>The natural estimator for the Bernoulli parameter is the <strong>sample proportion</strong>:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\hat{p} = \frac{1}{n}\sum_{i=1}^n X_i$$
                    </div>
                </div>
                <p>This is also the MLE for the Bernoulli parameter.</p>
            </div>

            <div class="wald-box">
                <h3>Step 2: Find the Asymptotic Distribution</h3>
                <p>Under the null hypothesis $H_0: p = p_0$, we know:</p>
                <ul>
                    <li><strong>Expected Value:</strong> $E[\hat{p}] = p_0$</li>
                    <li><strong>Variance:</strong> $\text{Var}(\hat{p}) = \frac{p_0(1-p_0)}{n}$</li>
                    <li><strong>Asymptotic Distribution:</strong> By the Central Limit Theorem:
                        <div class="formula-box">
                            <div class="math-display">
                                $$\hat{p} \xrightarrow{d} N\left(p_0, \frac{p_0(1-p_0)}{n}\right)$$
                            </div>
                        </div>
                    </li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>Step 3: Two Versions of the Wald Test</h3>
                <p>The source material presents <strong>two different versions</strong> of the test statistic:</p>
                
                <h4>Version 1: Using True Variance Under Null</h4>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n^{(1)} = \frac{\hat{p} - p_0}{\sqrt{\frac{p_0(1-p_0)}{n}}}$$
                    </div>
                </div>
                
                <h4>Version 2: Using Estimated Variance</h4>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n^{(2)} = \frac{\hat{p} - p_0}{\sqrt{\frac{\hat{p}(1-\hat{p})}{n}}}$$
                    </div>
                </div>
                
                <p><strong>Key Insight:</strong> Both versions have asymptotically standard normal distribution under $H_0$, but Version 2 has "more pleasant behavior under the alternative" as noted in the source.</p>
            </div>

            <div class="visualization">
                <svg width="100%" height="500" viewBox="0 0 900 500">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f0fdf4;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ecfdf5;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="500" fill="url(#bgGradient3)" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">Bernoulli Wald Test: Step-by-Step Construction</text>
                    
                    <!-- Step 1: Data -->
                    <rect x="50" y="60" width="150" height="90" fill="#ffffff" rx="10" stroke="#059669" stroke-width="2"/>
                    <text x="125" y="85" text-anchor="middle" fill="#059669" font-size="14" font-weight="bold">Step 1: Data</text>
                    <text x="125" y="105" text-anchor="middle" fill="#059669" font-size="12">X₁, X₂, ..., Xₙ</text>
                    <text x="125" y="125" text-anchor="middle" fill="#059669" font-size="12">~ Ber(p)</text>
                    <text x="125" y="140" text-anchor="middle" fill="#059669" font-size="11">Independent trials</text>
                    
                    <!-- Step 2: Estimator -->
                    <rect x="250" y="60" width="150" height="90" fill="#ffffff" rx="10" stroke="#7c3aed" stroke-width="2"/>
                    <text x="325" y="85" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold">Step 2: Estimator</text>
                    <text x="325" y="105" text-anchor="middle" fill="#7c3aed" font-size="12">p̂ = (1/n)∑Xᵢ</text>
                    <text x="325" y="125" text-anchor="middle" fill="#7c3aed" font-size="12">Sample proportion</text>
                    <text x="325" y="140" text-anchor="middle" fill="#7c3aed" font-size="11">(Also MLE)</text>
                    
                    <!-- Step 3: Distribution -->
                    <rect x="450" y="60" width="150" height="90" fill="#ffffff" rx="10" stroke="#3b82f6" stroke-width="2"/>
                    <text x="525" y="85" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="bold">Step 3: Distribution</text>
                    <text x="525" y="105" text-anchor="middle" fill="#3b82f6" font-size="12">p̂ ~ N(p₀, σ²/n)</text>
                    <text x="525" y="125" text-anchor="middle" fill="#3b82f6" font-size="12">σ² = p₀(1-p₀)</text>
                    <text x="525" y="140" text-anchor="middle" fill="#3b82f6" font-size="11">Under H₀</text>
                    
                    <!-- Step 4: Test Statistic -->
                    <rect x="650" y="60" width="150" height="90" fill="#ffffff" rx="10" stroke="#d97706" stroke-width="2"/>
                    <text x="725" y="85" text-anchor="middle" fill="#d97706" font-size="14" font-weight="bold">Step 4: Test Stat</text>
                    <text x="725" y="105" text-anchor="middle" fill="#d97706" font-size="12">T = (p̂-p₀)/SE</text>
                    <text x="725" y="125" text-anchor="middle" fill="#d97706" font-size="12">T ~ N(0,1)</text>
                    <text x="725" y="140" text-anchor="middle" fill="#d97706" font-size="11">Under H₀</text>
                    
                    <!-- Arrows -->
                    <line x1="200" y1="105" x2="240" y2="105" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    <line x1="400" y1="105" x2="440" y2="105" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    <line x1="600" y1="105" x2="640" y2="105" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    
                    <!-- Version Comparison -->
                    <rect x="50" y="200" width="800" height="280" fill="#ffffff" rx="10" stroke="#6b7280" stroke-width="1"/>
                    <text x="450" y="225" text-anchor="middle" fill="#1f2937" font-size="16" font-weight="bold">Two Versions of the Wald Test</text>
                    
                    <!-- Version 1 -->
                    <rect x="80" y="250" width="350" height="200" fill="#fef3cd" rx="8" stroke="#d97706" stroke-width="2"/>
                    <text x="255" y="275" text-anchor="middle" fill="#92400e" font-size="15" font-weight="bold">Version 1: Known Variance</text>
                    
                    <text x="100" y="305" fill="#92400e" font-size="13"><strong>Formula:</strong></text>
                    <text x="120" y="325" fill="#92400e" font-size="12">T₁ = (p̂ - p₀) / √[p₀(1-p₀)/n]</text>
                    
                    <text x="100" y="355" fill="#92400e" font-size="13"><strong>Pros:</strong></text>
                    <text x="120" y="375" fill="#92400e" font-size="11">• Uses true variance under H₀</text>
                    <text x="120" y="390" fill="#92400e" font-size="11">• Theoretically cleaner</text>
                    
                    <text x="100" y="420" fill="#92400e" font-size="13"><strong>Cons:</strong></text>
                    <text x="120" y="440" fill="#92400e" font-size="11">• Assumes p₀ is correct under H₁</text>
                    
                    <!-- Version 2 -->
                    <rect x="470" y="250" width="350" height="200" fill="#ecfdf5" rx="8" stroke="#059669" stroke-width="2"/>
                    <text x="645" y="275" text-anchor="middle" fill="#065f46" font-size="15" font-weight="bold">Version 2: Estimated Variance</text>
                    
                    <text x="490" y="305" fill="#065f46" font-size="13"><strong>Formula:</strong></text>
                    <text x="510" y="325" fill="#065f46" font-size="12">T₂ = (p̂ - p₀) / √[p̂(1-p̂)/n]</text>
                    
                    <text x="490" y="355" fill="#065f46" font-size="13"><strong>Pros:</strong></text>
                    <text x="510" y="375" fill="#065f46" font-size="11">• More robust to alternatives</text>
                    <text x="510" y="390" fill="#065f46" font-size="11">• Better power properties</text>
                    <text x="510" y="405" fill="#065f46" font-size="11">• Self-consistent variance</text>
                    
                    <text x="490" y="435" fill="#065f46" font-size="13"><strong>Note:</strong></text>
                    <text x="510" y="450" fill="#065f46" font-size="11">Still T₂ → N(0,1) under H₀</text>
                </svg>
            </div>

            <div class="example-box">
                <h3>Numerical Example</h3>
                <p>Let's say we flip a coin 100 times and get 60 heads. We want to test if the coin is fair ($p_0 = 0.5$).</p>
                
                <p><strong>Data:</strong> $n = 100$, $\hat{p} = 0.60$, $p_0 = 0.50$</p>
                
                <p><strong>Version 1 Test Statistic:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n^{(1)} = \frac{0.60 - 0.50}{\sqrt{\frac{0.50(1-0.50)}{100}}} = \frac{0.10}{\sqrt{0.0025}} = \frac{0.10}{0.05} = 2.00$$
                    </div>
                </div>
                
                <p><strong>Version 2 Test Statistic:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n^{(2)} = \frac{0.60 - 0.50}{\sqrt{\frac{0.60(1-0.60)}{100}}} = \frac{0.10}{\sqrt{0.0024}} = \frac{0.10}{0.049} = 2.04$$
                    </div>
                </div>
                
                <p><strong>Decision:</strong> At $\alpha = 0.05$, the critical value is $z_{0.025} = 1.96$. Since both $|T_n^{(1)}| = 2.00 > 1.96$ and $|T_n^{(2)}| = 2.04 > 1.96$, we <strong>reject the null hypothesis</strong> and conclude the coin is not fair.</p>
            </div>

            <div class="visualization">
                <div id="bernoulli-comparison-plot" style="width:100%; height:400px;"></div>
            </div>

            <script>
                // Create comparison plot for Bernoulli example
                document.addEventListener('DOMContentLoaded', function() {
                    // Sample sizes
                    const n_values = [10, 25, 50, 100, 200, 500, 1000];
                    
                    // True parameter and alternative
                    const p0 = 0.5;  // null hypothesis
                    const p1 = 0.6;  // alternative (what we observed)
                    
                    // Calculate standard errors for both versions
                    const se_version1 = n_values.map(n => Math.sqrt(p0 * (1 - p0) / n));
                    const se_version2 = n_values.map(n => Math.sqrt(p1 * (1 - p1) / n));
                    
                    // Calculate test statistics for both versions
                    const test_stat_v1 = n_values.map((n, i) => (p1 - p0) / se_version1[i]);
                    const test_stat_v2 = n_values.map((n, i) => (p1 - p0) / se_version2[i]);
                    
                    const trace1 = {
                        x: n_values,
                        y: test_stat_v1,
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: 'Version 1: SE = √[p₀(1-p₀)/n]',
                        line: {color: '#d97706', width: 3},
                        marker: {size: 8}
                    };
                    
                    const trace2 = {
                        x: n_values,
                        y: test_stat_v2,
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: 'Version 2: SE = √[p̂(1-p̂)/n]',
                        line: {color: '#059669', width: 3},
                        marker: {size: 8}
                    };
                    
                    // Critical value line
                    const critical_line = {
                        x: n_values,
                        y: Array(n_values.length).fill(1.96),
                        type: 'scatter',
                        mode: 'lines',
                        name: 'Critical Value (α=0.05)',
                        line: {color: '#dc2626', width: 2, dash: 'dash'}
                    };
                    
                    const layout = {
                        title: {
                            text: 'Bernoulli Wald Test: Comparison of Two Versions<br><sub>Testing p=0.5 vs observed p̂=0.6</sub>',
                            font: {size: 16, color: '#1f2937'}
                        },
                        xaxis: {
                            title: 'Sample Size (n)',
                            type: 'log',
                            gridcolor: '#e5e7eb'
                        },
                        yaxis: {
                            title: 'Test Statistic Value',
                            gridcolor: '#e5e7eb'
                        },
                        plot_bgcolor: '#f8fafc',
                        paper_bgcolor: '#ffffff',
                        annotations: [
                            {
                                x: 500,
                                y: 1.96,
                                text: 'Rejection Threshold',
                                showarrow: true,
                                arrowhead: 2,
                                arrowcolor: '#dc2626',
                                ax: 0,
                                ay: -40
                            }
                        ]
                    };
                    
                    Plotly.newPlot('bernoulli-comparison-plot', [trace1, trace2, critical_line], layout, {responsive: true});
                });
                         </script>
         </div>

        <!-- Power Analysis Section -->
        <div class="section" id="power-analysis">
            <h2><span class="step-number">5</span>Power Analysis of the Wald Test</h2>
            
            <p>Understanding the <strong>power function</strong> of the Wald test is crucial for practical applications. Let's explore how the test performs under the alternative hypothesis.</p>

            <div class="definition-box">
                <h3>General Setup for Power Analysis</h3>
                <p>Suppose we have an MLE with "standard asymptotics":</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\hat{\theta} - \theta \xrightarrow{d} N\left(0, \frac{1}{nI_1(\theta)}\right)$$
                    </div>
                </div>
                <p>where $I_1(\theta)$ is the Fisher Information per observation.</p>
                
                <p>We use the test statistic:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$T_n = \sqrt{nI_1(\hat{\theta})}(\hat{\theta} - \theta_0)$$
                    </div>
                </div>
            </div>

            <div class="wald-box">
                <h3>Key Power Formula from Source Material</h3>
                <p>When the true parameter value is $\theta_1 \neq \theta_0$, define:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\Delta = \sqrt{nI_1(\theta_1)}(\theta_0 - \theta_1)$$
                    </div>
                </div>
                
                <p>Then the <strong>power</strong> of the Wald test (probability of rejecting $H_0$ when $H_1$ is true) is asymptotically:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Power} = 1 - \Phi(\Delta + z_{\alpha/2}) + \Phi(\Delta - z_{\alpha/2})$$
                    </div>
                </div>
                <p>where $\Phi$ is the standard normal CDF.</p>
            </div>

            <div class="highlight-box">
                <h3>Key Insights from the Power Formula</h3>
                <p>The source material highlights three crucial aspects:</p>
                
                <ol>
                    <li><strong>Small Differences:</strong> If $|\theta_0 - \theta_1|$ is very small, then $\Delta \approx 0$ and the power approaches $\alpha$ (trivial power)</li>
                    
                    <li><strong>Large Sample Limit:</strong> As $n \to \infty$, $|\Delta| \to \infty$, so one of the $\Phi$ terms approaches 0 and the other approaches 1, making power approach 1</li>
                    
                    <li><strong>Rule of Thumb:</strong> The Wald test will have non-trivial power if $|\theta_0 - \theta_1| \gg \sqrt{\frac{1}{nI_1(\theta_1)}}$</li>
                </ol>
            </div>

            <div class="visualization">
                <svg width="100%" height="450" viewBox="0 0 900 450">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f3e8ff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#faf5ff;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="450" fill="url(#bgGradient4)" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">Power Analysis: Key Factors</text>
                    
                    <!-- Factor 1: Effect Size -->
                    <rect x="50" y="60" width="250" height="150" fill="#ffffff" rx="10" stroke="#7c3aed" stroke-width="2"/>
                    <text x="175" y="85" text-anchor="middle" fill="#7c3aed" font-size="15" font-weight="bold">Effect Size |θ₁ - θ₀|</text>
                    
                    <!-- Small effect -->
                    <circle cx="100" cy="120" r="15" fill="#fca5a5" stroke="#ef4444" stroke-width="2"/>
                    <text x="100" y="125" text-anchor="middle" fill="#1f2937" font-size="10" font-weight="bold">Small</text>
                    <text x="100" y="145" text-anchor="middle" fill="#ef4444" font-size="10">Low Power</text>
                    
                    <!-- Medium effect -->
                    <circle cx="175" cy="120" r="20" fill="#fde047" stroke="#eab308" stroke-width="2"/>
                    <text x="175" y="125" text-anchor="middle" fill="#1f2937" font-size="10" font-weight="bold">Medium</text>
                    <text x="175" y="150" text-anchor="middle" fill="#eab308" font-size="10">Moderate Power</text>
                    
                    <!-- Large effect -->
                    <circle cx="250" cy="120" r="25" fill="#86efac" stroke="#22c55e" stroke-width="2"/>
                    <text x="250" y="125" text-anchor="middle" fill="#1f2937" font-size="10" font-weight="bold">Large</text>
                    <text x="250" y="155" text-anchor="middle" fill="#22c55e" font-size="10">High Power</text>
                    
                    <text x="175" y="190" text-anchor="middle" fill="#7c3aed" font-size="12">Δ = √(nI₁(θ₁)) × |θ₀ - θ₁|</text>
                    
                    <!-- Factor 2: Sample Size -->
                    <rect x="350" y="60" width="250" height="150" fill="#ffffff" rx="10" stroke="#3b82f6" stroke-width="2"/>
                    <text x="475" y="85" text-anchor="middle" fill="#3b82f6" font-size="15" font-weight="bold">Sample Size n</text>
                    
                    <!-- Growth visualization -->
                    <rect x="380" y="110" width="20" height="40" fill="#dbeafe" stroke="#3b82f6"/>
                    <rect x="420" y="100" width="20" height="50" fill="#93c5fd" stroke="#3b82f6"/>
                    <rect x="460" y="90" width="20" height="60" fill="#60a5fa" stroke="#3b82f6"/>
                    <rect x="500" y="80" width="20" height="70" fill="#3b82f6" stroke="#3b82f6"/>
                    <rect x="540" y="70" width="20" height="80" fill="#1d4ed8" stroke="#3b82f6"/>
                    
                    <text x="390" y="165" text-anchor="middle" fill="#3b82f6" font-size="9">n=10</text>
                    <text x="430" y="165" text-anchor="middle" fill="#3b82f6" font-size="9">n=50</text>
                    <text x="470" y="165" text-anchor="middle" fill="#3b82f6" font-size="9">n=100</text>
                    <text x="510" y="165" text-anchor="middle" fill="#3b82f6" font-size="9">n=500</text>
                    <text x="550" y="165" text-anchor="middle" fill="#3b82f6" font-size="9">n=1000</text>
                    
                    <text x="475" y="190" text-anchor="middle" fill="#3b82f6" font-size="12">Power ↗ as n ↗</text>
                    
                    <!-- Factor 3: Fisher Information -->
                    <rect x="650" y="60" width="200" height="150" fill="#ffffff" rx="10" stroke="#059669" stroke-width="2"/>
                    <text x="750" y="85" text-anchor="middle" fill="#059669" font-size="15" font-weight="bold">Fisher Information</text>
                    
                    <!-- Info visualization -->
                    <ellipse cx="720" cy="130" rx="30" ry="15" fill="none" stroke="#059669" stroke-width="3"/>
                    <ellipse cx="780" cy="130" rx="20" ry="10" fill="none" stroke="#059669" stroke-width="3"/>
                    
                    <text x="720" y="160" text-anchor="middle" fill="#059669" font-size="10">High Variance</text>
                    <text x="720" y="175" text-anchor="middle" fill="#059669" font-size="10">Low I₁(θ)</text>
                    
                    <text x="780" y="160" text-anchor="middle" fill="#059669" font-size="10">Low Variance</text>
                    <text x="780" y="175" text-anchor="middle" fill="#059669" font-size="10">High I₁(θ)</text>
                    
                    <text x="750" y="190" text-anchor="middle" fill="#059669" font-size="12">More info → More power</text>
                    
                    <!-- Power curve illustration -->
                    <rect x="50" y="240" width="800" height="180" fill="#ffffff" rx="10" stroke="#6b7280" stroke-width="1"/>
                    <text x="450" y="265" text-anchor="middle" fill="#1f2937" font-size="16" font-weight="bold">Power Function Behavior</text>
                    
                    <!-- Three scenarios -->
                    <text x="80" y="295" fill="#1f2937" font-size="14" font-weight="bold">Scenario 1: Small n or Small Effect</text>
                    <path d="M 80 310 Q 200 300 320 305" stroke="#ef4444" stroke-width="3" fill="none"/>
                    <text x="200" y="330" text-anchor="middle" fill="#ef4444" font-size="12">Power ≈ α (poor)</text>
                    
                    <text x="380" y="295" fill="#1f2937" font-size="14" font-weight="bold">Scenario 2: Moderate n and Effect</text>
                    <path d="M 380 310 Q 450 280 520 270" stroke="#eab308" stroke-width="3" fill="none"/>
                    <text x="450" y="330" text-anchor="middle" fill="#eab308" font-size="12">Power = 0.6-0.8</text>
                    
                    <text x="580" y="295" fill="#1f2937" font-size="14" font-weight="bold">Scenario 3: Large n or Large Effect</text>
                    <path d="M 580 310 Q 650 270 720 250" stroke="#22c55e" stroke-width="3" fill="none"/>
                    <text x="650" y="330" text-anchor="middle" fill="#22c55e" font-size="12">Power → 1 (excellent)</text>
                    
                    <!-- Mathematical summary -->
                    <rect x="80" y="350" width="740" height="50" fill="#f8fafc" rx="5" stroke="#e5e7eb" stroke-width="1"/>
                    <text x="450" y="370" text-anchor="middle" fill="#1f2937" font-size="14" font-weight="bold">Rule of Thumb:</text>
                    <text x="450" y="390" text-anchor="middle" fill="#1f2937" font-size="13">Non-trivial power when |θ₀ - θ₁| ≫ 1/√(nI₁(θ₁))</text>
                </svg>
            </div>

            <div class="example-box">
                <h3>Practical Power Calculation Example</h3>
                <p>Let's return to our Bernoulli example and calculate the power when testing $H_0: p = 0.5$ against $H_1: p = 0.6$ with $n = 100$.</p>
                
                <p><strong>Fisher Information:</strong> For Bernoulli($p$), $I_1(p) = \frac{1}{p(1-p)}$</p>
                
                <p><strong>At $\theta_1 = 0.6$:</strong> $I_1(0.6) = \frac{1}{0.6 \times 0.4} = \frac{1}{0.24} = 4.167$</p>
                
                <p><strong>Calculate $\Delta$:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\Delta = \sqrt{100 \times 4.167} \times (0.5 - 0.6) = \sqrt{416.7} \times (-0.1) = -2.04$$
                    </div>
                </div>
                
                <p><strong>Power Calculation (at $\alpha = 0.05$):</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        \text{Power} &= 1 - \Phi(-2.04 + 1.96) + \Phi(-2.04 - 1.96)\\
                        &= 1 - \Phi(-0.08) + \Phi(-4.00)\\
                        &\approx 1 - 0.468 + 0 = 0.532
                        \end{align}
                    </div>
                </div>
                
                <p><strong>Interpretation:</strong> The test has about 53% power to detect the difference between $p = 0.5$ and $p = 0.6$ with $n = 100$ observations.</p>
            </div>

            <div class="visualization">
                <div id="power-curve-plot" style="width:100%; height:400px;"></div>
            </div>

            <script>
                // Create power curve visualization
                document.addEventListener('DOMContentLoaded', function() {
                    // True parameter values (alternatives)
                    const theta1_values = [];
                    for (let i = 0.1; i <= 0.9; i += 0.01) {
                        theta1_values.push(i);
                    }
                    
                    const theta0 = 0.5;  // null hypothesis
                    const n = 100;
                    const alpha = 0.05;
                    const z_alpha_2 = 1.96;
                    
                    // Calculate power for each alternative
                    const power_values = theta1_values.map(theta1 => {
                        if (theta1 === theta0) return alpha;  // Power equals alpha at null
                        
                        const I1 = 1 / (theta1 * (1 - theta1));  // Fisher info for Bernoulli
                        const delta = Math.sqrt(n * I1) * (theta0 - theta1);
                        
                        // Power formula
                        const term1 = 1 - normalCDF(delta + z_alpha_2);
                        const term2 = normalCDF(delta - z_alpha_2);
                        
                        return Math.max(0, Math.min(1, term1 + term2));
                    });
                    
                    // Helper function for normal CDF (approximation)
                    function normalCDF(x) {
                        return 0.5 * (1 + erf(x / Math.sqrt(2)));
                    }
                    
                    function erf(x) {
                        // Abramowitz and Stegun approximation
                        const a1 =  0.254829592;
                        const a2 = -0.284496736;
                        const a3 =  1.421413741;
                        const a4 = -1.453152027;
                        const a5 =  1.061405429;
                        const p  =  0.3275911;
                        
                        const sign = x >= 0 ? 1 : -1;
                        x = Math.abs(x);
                        
                        const t = 1.0 / (1.0 + p * x);
                        const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);
                        
                        return sign * y;
                    }
                    
                    const trace = {
                        x: theta1_values,
                        y: power_values,
                        type: 'scatter',
                        mode: 'lines',
                        name: 'Power Function',
                        line: {color: '#7c3aed', width: 4}
                    };
                    
                    // Add horizontal line at alpha
                    const alpha_line = {
                        x: theta1_values,
                        y: Array(theta1_values.length).fill(alpha),
                        type: 'scatter',
                        mode: 'lines',
                        name: 'Type I Error (α = 0.05)',
                        line: {color: '#dc2626', width: 2, dash: 'dash'}
                    };
                    
                    // Add vertical line at null hypothesis
                    const null_line = {
                        x: [theta0, theta0],
                        y: [0, 1],
                        type: 'scatter',
                        mode: 'lines',
                        name: 'Null Hypothesis (p₀ = 0.5)',
                        line: {color: '#374151', width: 2, dash: 'dot'}
                    };
                    
                    const layout = {
                        title: {
                            text: 'Power Function for Bernoulli Wald Test<br><sub>H₀: p = 0.5, n = 100, α = 0.05</sub>',
                            font: {size: 16, color: '#1f2937'}
                        },
                        xaxis: {
                            title: 'True Parameter Value (p)',
                            range: [0.1, 0.9],
                            gridcolor: '#e5e7eb'
                        },
                        yaxis: {
                            title: 'Power (Probability of Rejecting H₀)',
                            range: [0, 1],
                            gridcolor: '#e5e7eb'
                        },
                        plot_bgcolor: '#f8fafc',
                        paper_bgcolor: '#ffffff',
                        annotations: [
                            {
                                x: 0.3,
                                y: 0.8,
                                text: 'High Power<br>when p < 0.5',
                                showarrow: false,
                                font: {color: '#7c3aed', size: 12}
                            },
                            {
                                x: 0.7,
                                y: 0.8,
                                text: 'High Power<br>when p > 0.5',
                                showarrow: false,
                                font: {color: '#7c3aed', size: 12}
                            },
                            {
                                x: 0.5,
                                y: 0.15,
                                text: 'Power = α<br>at null',
                                showarrow: true,
                                arrowhead: 2,
                                arrowcolor: '#dc2626',
                                ax: 50,
                                ay: -30
                            }
                        ]
                    };
                    
                    Plotly.newPlot('power-curve-plot', [trace, alpha_line, null_line], layout, {responsive: true});
                });
                         </script>
         </div>

        <!-- Practical Implementation Guide Section -->
        <div class="section" id="practical-guide">
            <h2><span class="step-number">6</span>Practical Implementation Guide</h2>
            
            <p>Here's a step-by-step guide to implementing the Wald test in practice, with important considerations and common pitfalls to avoid.</p>

            <div class="wald-box">
                <h3>Step-by-Step Implementation</h3>
                <ol class="step-list">
                    <li><strong>Identify Your Model and Parameter:</strong> Clearly specify the parametric model and the parameter $\theta$ you want to test.</li>
                    
                    <li><strong>Choose Your Estimator:</strong> Select an asymptotically normal estimator $\hat{\theta}$ (typically the MLE).</li>
                    
                    <li><strong>Determine the Variance:</strong> Find or estimate $\sigma_0^2 = \text{Var}(\hat{\theta})$ under $H_0$.</li>
                    
                    <li><strong>Compute the Test Statistic:</strong> Calculate $T_n = \frac{\hat{\theta} - \theta_0}{\hat{\sigma}_0}$.</li>
                    
                    <li><strong>Make the Decision:</strong> Compare $|T_n|$ with $z_{\alpha/2}$ and decide whether to reject $H_0$.</li>
                </ol>
            </div>

            <div class="highlight-box">
                <h3>Key Practical Considerations</h3>
                
                <h4>Sample Size Requirements</h4>
                <p>The Wald test is <strong>asymptotic</strong>, so it requires "large" samples. Rules of thumb:</p>
                <ul>
                    <li>For most distributions: $n \geq 30$ (minimum)</li>
                    <li>For better accuracy: $n \geq 100$</li>
                    <li>For highly skewed distributions: $n \geq 200$ or more</li>
                </ul>
                
                <h4>Variance Estimation</h4>
                <p>Two common approaches for estimating $\sigma_0^2$:</p>
                <ul>
                    <li><strong>Under $H_0$:</strong> Use $\theta_0$ in the variance formula (like Version 1 in Bernoulli example)</li>
                    <li><strong>Under observed data:</strong> Use $\hat{\theta}$ in the variance formula (like Version 2 in Bernoulli example)</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Common Parametric Models</h3>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Distribution</th>
                            <th>Parameter</th>
                            <th>MLE</th>
                            <th>Fisher Information</th>
                            <th>Standard Error</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Bernoulli</td>
                            <td>$p$</td>
                            <td>$\hat{p} = \bar{X}$</td>
                            <td>$I_1(p) = \frac{1}{p(1-p)}$</td>
                            <td>$\sqrt{\frac{p(1-p)}{n}}$</td>
                        </tr>
                        <tr>
                            <td>Normal (known $\sigma^2$)</td>
                            <td>$\mu$</td>
                            <td>$\hat{\mu} = \bar{X}$</td>
                            <td>$I_1(\mu) = \frac{1}{\sigma^2}$</td>
                            <td>$\frac{\sigma}{\sqrt{n}}$</td>
                        </tr>
                        <tr>
                            <td>Poisson</td>
                            <td>$\lambda$</td>
                            <td>$\hat{\lambda} = \bar{X}$</td>
                            <td>$I_1(\lambda) = \frac{1}{\lambda}$</td>
                            <td>$\sqrt{\frac{\lambda}{n}}$</td>
                        </tr>
                        <tr>
                            <td>Exponential</td>
                            <td>$\theta$</td>
                            <td>$\hat{\theta} = \bar{X}$</td>
                            <td>$I_1(\theta) = \frac{1}{\theta^2}$</td>
                            <td>$\frac{\theta}{\sqrt{n}}$</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="definition-box">
                <h3>Important Warnings and Limitations</h3>
                
                <h4>⚠️ When NOT to Use the Wald Test</h4>
                <ul>
                    <li><strong>Small samples:</strong> The asymptotic approximation may be poor</li>
                    <li><strong>Parameters near boundaries:</strong> e.g., testing $p = 0$ or $p = 1$ for Bernoulli</li>
                    <li><strong>Non-regular cases:</strong> When standard regularity conditions fail</li>
                    <li><strong>Composite null hypotheses:</strong> The test is designed for simple null hypotheses</li>
                </ul>
                
                <h4>🔧 Alternative Tests to Consider</h4>
                <ul>
                    <li><strong>Score Test:</strong> Better for parameters near boundaries</li>
                    <li><strong>Likelihood Ratio Test:</strong> More robust in many situations</li>
                    <li><strong>Exact tests:</strong> When available for small samples</li>
                </ul>
            </div>

            <div class="visualization">
                <svg width="100%" height="350" viewBox="0 0 900 350">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#fef3cd;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#fde68a;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="350" fill="url(#bgGradient5)" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">Wald Test Decision Framework</text>
                    
                    <!-- Decision tree -->
                    <rect x="350" y="60" width="200" height="60" fill="#ffffff" rx="10" stroke="#d97706" stroke-width="2"/>
                    <text x="450" y="85" text-anchor="middle" fill="#d97706" font-size="14" font-weight="bold">Is n ≥ 30?</text>
                    <text x="450" y="105" text-anchor="middle" fill="#d97706" font-size="12">Sample size check</text>
                    
                    <!-- No branch -->
                    <rect x="100" y="160" width="200" height="60" fill="#fecaca" rx="10" stroke="#ef4444" stroke-width="2"/>
                    <text x="200" y="185" text-anchor="middle" fill="#ef4444" font-size="14" font-weight="bold">Consider exact tests</text>
                    <text x="200" y="205" text-anchor="middle" fill="#ef4444" font-size="12">or bootstrap methods</text>
                    
                    <!-- Yes branch -->
                    <rect x="600" y="160" width="200" height="60" fill="#d1fae5" rx="10" stroke="#22c55e" stroke-width="2"/>
                    <text x="700" y="185" text-anchor="middle" fill="#22c55e" font-size="14" font-weight="bold">Proceed with Wald</text>
                    <text x="700" y="205" text-anchor="middle" fill="#22c55e" font-size="12">test implementation</text>
                    
                    <!-- Final steps -->
                    <rect x="550" y="260" width="300" height="70" fill="#ffffff" rx="10" stroke="#22c55e" stroke-width="2"/>
                    <text x="700" y="285" text-anchor="middle" fill="#22c55e" font-size="13" font-weight="bold">1. Compute T = (θ̂ - θ₀)/SE</text>
                    <text x="700" y="305" text-anchor="middle" fill="#22c55e" font-size="13" font-weight="bold">2. Compare |T| with z_{α/2}</text>
                    <text x="700" y="320" text-anchor="middle" fill="#22c55e" font-size="13" font-weight="bold">3. Make decision</text>
                    
                    <!-- Arrows -->
                    <line x1="400" y1="120" x2="250" y2="150" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    <line x1="500" y1="120" x2="650" y2="150" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    <line x1="700" y1="220" x2="700" y2="250" stroke="#374151" stroke-width="2" marker-end="url(#arrow1)"/>
                    
                    <!-- Labels -->
                    <text x="320" y="140" fill="#ef4444" font-size="12" font-weight="bold">No</text>
                    <text x="580" y="140" fill="#22c55e" font-size="12" font-weight="bold">Yes</text>
                </svg>
            </div>
        </div>

        <!-- Summary and Comparison Section -->
        <div class="section" id="comparison">
            <h2><span class="step-number">7</span>Summary and Comparison with Other Tests</h2>
            
            <p>Let's conclude by summarizing the key features of the Wald test and comparing it with other common hypothesis tests.</p>

            <div class="wald-box">
                <h3>Wald Test Summary</h3>
                <ul>
                    <li><strong>Purpose:</strong> Tests simple null hypotheses in parametric models</li>
                    <li><strong>Foundation:</strong> Asymptotic normality of estimators (especially MLE)</li>
                    <li><strong>Test Statistic:</strong> $T_n = \frac{\hat{\theta} - \theta_0}{\hat{\sigma}_0} \sim N(0,1)$ under $H_0$</li>
                    <li><strong>Decision Rule:</strong> Reject $H_0$ if $|T_n| \geq z_{\alpha/2}$</li>
                    <li><strong>Requirements:</strong> Large sample size, regular parametric model</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>Comparison with Other Tests</h3>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Test</th>
                            <th>Advantages</th>
                            <th>Disadvantages</th>
                            <th>Best Use Cases</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Wald Test</strong></td>
                            <td>• Simple to compute<br>• Uses familiar MLE<br>• General framework</td>
                            <td>• Requires large samples<br>• Can be unstable near boundaries</td>
                            <td>Standard parametric testing with adequate sample size</td>
                        </tr>
                        <tr>
                            <td><strong>Likelihood Ratio Test</strong></td>
                            <td>• More robust<br>• Better finite-sample behavior<br>• Handles constraints well</td>
                            <td>• Requires computing two MLEs<br>• More computationally intensive</td>
                            <td>Complex models, nested hypotheses, boundaries</td>
                        </tr>
                        <tr>
                            <td><strong>Score Test</strong></td>
                            <td>• Only requires MLE under $H_0$<br>• Good near boundaries<br>• Computationally efficient</td>
                            <td>• Less familiar to practitioners<br>• Requires score function</td>
                            <td>When computing unrestricted MLE is difficult</td>
                        </tr>
                        <tr>
                            <td><strong>Exact Tests</strong></td>
                            <td>• Exact Type I error control<br>• No asymptotic approximation</td>
                            <td>• Limited to specific distributions<br>• Can be conservative</td>
                            <td>Small samples, discrete distributions</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="example-box">
                <h3>When to Choose the Wald Test</h3>
                <p>The Wald test is an excellent choice when:</p>
                <ul>
                    <li>✅ You have a <strong>large sample</strong> (n ≥ 100 is ideal)</li>
                    <li>✅ You're working with a <strong>standard parametric model</strong></li>
                    <li>✅ The parameter is <strong>not near boundaries</strong> of the parameter space</li>
                    <li>✅ You want a <strong>simple, well-understood</strong> test procedure</li>
                    <li>✅ The MLE is <strong>easy to compute</strong> and has standard asymptotics</li>
                </ul>
                
                <p><strong>Consider alternatives when:</strong></p>
                <ul>
                    <li>❌ Sample size is small (n < 30)</li>
                    <li>❌ Testing parameters near boundaries (e.g., variance = 0)</li>
                    <li>❌ The model has complex constraints</li>
                    <li>❌ You need exact finite-sample control</li>
                </ul>
            </div>

            <div class="definition-box">
                <h3>Final Recommendations</h3>
                <ol>
                    <li><strong>Start with sample size:</strong> If n < 30, consider exact tests or bootstrap methods</li>
                    <li><strong>Check your model:</strong> Ensure standard regularity conditions are met</li>
                    <li><strong>Validate assumptions:</strong> Check that your estimator is indeed asymptotically normal</li>
                    <li><strong>Consider the context:</strong> For critical applications, compare results with likelihood ratio tests</li>
                    <li><strong>Report appropriately:</strong> Always mention that results are based on asymptotic approximations</li>
                </ol>
            </div>

            <div class="visualization">
                <svg width="100%" height="300" viewBox="0 0 900 300">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient6" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e0e7ff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#c7d2fe;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="300" fill="url(#bgGradient6)" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">The Wald Test in the Statistical Testing Landscape</text>
                    
                    <!-- Central concept -->
                    <circle cx="450" cy="150" r="60" fill="#ffffff" stroke="#1e3a8a" stroke-width="3"/>
                    <text x="450" y="145" text-anchor="middle" fill="#1e3a8a" font-size="14" font-weight="bold">Wald Test</text>
                    <text x="450" y="165" text-anchor="middle" fill="#1e3a8a" font-size="12">Asymptotic</text>
                    <text x="450" y="180" text-anchor="middle" fill="#1e3a8a" font-size="12">Normal Theory</text>
                    
                    <!-- Connected concepts -->
                    <ellipse cx="250" cy="100" rx="70" ry="30" fill="#f3e8ff" stroke="#7c3aed" stroke-width="2"/>
                    <text x="250" y="95" text-anchor="middle" fill="#7c3aed" font-size="12" font-weight="bold">Maximum</text>
                    <text x="250" y="110" text-anchor="middle" fill="#7c3aed" font-size="12" font-weight="bold">Likelihood</text>
                    
                    <ellipse cx="650" cy="100" rx="70" ry="30" fill="#ecfdf5" stroke="#059669" stroke-width="2"/>
                    <text x="650" y="95" text-anchor="middle" fill="#059669" font-size="12" font-weight="bold">Fisher</text>
                    <text x="650" y="110" text-anchor="middle" fill="#059669" font-size="12" font-weight="bold">Information</text>
                    
                    <ellipse cx="250" cy="200" rx="70" ry="30" fill="#fef3cd" stroke="#d97706" stroke-width="2"/>
                    <text x="250" y="195" text-anchor="middle" fill="#d97706" font-size="12" font-weight="bold">Central Limit</text>
                    <text x="250" y="210" text-anchor="middle" fill="#d97706" font-size="12" font-weight="bold">Theorem</text>
                    
                    <ellipse cx="650" cy="200" rx="70" ry="30" fill="#dbeafe" stroke="#3b82f6" stroke-width="2"/>
                    <text x="650" y="195" text-anchor="middle" fill="#3b82f6" font-size="12" font-weight="bold">Hypothesis</text>
                    <text x="650" y="210" text-anchor="middle" fill="#3b82f6" font-size="12" font-weight="bold">Testing</text>
                    
                    <!-- Connection lines -->
                    <line x1="320" y1="120" x2="390" y2="140" stroke="#6b7280" stroke-width="2"/>
                    <line x1="580" y1="120" x2="510" y2="140" stroke="#6b7280" stroke-width="2"/>
                    <line x1="320" y1="180" x2="390" y2="160" stroke="#6b7280" stroke-width="2"/>
                    <line x1="580" y1="180" x2="510" y2="160" stroke="#6b7280" stroke-width="2"/>
                    
                    <!-- Bottom message -->
                    <text x="450" y="270" text-anchor="middle" fill="#1f2937" font-size="14" font-style="italic">
                        A bridge between classical statistics and modern computational methods
                    </text>
                </svg>
            </div>

            <div class="wald-box">
                <h3>🎯 Key Takeaways</h3>
                <ol>
                    <li>The Wald test provides a <strong>unified framework</strong> for hypothesis testing in parametric models</li>
                    <li>It leverages the <strong>asymptotic normality</strong> of estimators, particularly the MLE</li>
                    <li>The test is <strong>simple to implement</strong> but requires careful attention to sample size requirements</li>
                    <li>Understanding the <strong>power function</strong> helps in experimental design and result interpretation</li>
                    <li>While powerful and general, it's not always the best choice—<strong>consider alternatives</strong> for small samples or boundary cases</li>
                </ol>
                
                <p style="margin-top: 20px; font-style: italic; text-align: center;">
                    "The Wald test exemplifies the elegance of asymptotic theory applied to practical statistical inference."
                </p>
            </div>
        </div>
     </div>
 </body>
 </html> 