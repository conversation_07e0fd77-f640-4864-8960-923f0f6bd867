ing, nor share statistical strength across different sequence lengths
 and across different positions in time. Such sharing is particularly important when
 a specific piece of information can occur at multiple positions within the sequence.
 For example, consider the two sentences “I went to Nepal in 2009” and “In 2009,
 I went to Nepal.” If we ask a machine learning model to read each sentence and
 extract the year in which the narrator went to Nepal, we would like it to recognize
 the year 2009 as the relevant piece of information, whether it appears in the sixth
 373
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 word or the second word of the sentence. Suppose that we trained a feedforward
 network that processes sentences of fixed length. A traditional fully connected
 feedforward network would have separate parameters for each input feature, so it
 would need to learn all of the rules of the language separately at each position in
 the sentence. By comparison, a recurrent neural network shares the same weights
 across several time steps.
 A related idea is the use of convolution across a 1-D temporal sequence. This
 ,
 ;
 convolutional approach is the basis for time-delay neural networks (<PERSON> and
 <PERSON> 1988 <PERSON>
 et al.,
 ;
 1989 <PERSON>
 et al.,
 1990
 ). The convolution operation
 allows a network to share parameters across time, but is shallow. The output
 of convolution is a sequence where each member of the output is a function of
 a small number of neighboring members of the input. The idea of parameter
 sharing manifests in the application of the same convolution kernel at each time
 step. Recurrent networks share parameters in a different way. Each member of the
 output is a function of the previous members of the output. Each member of the
 output is produced using the same update rule applied to the previous outputs.
 This recurrent formulation results in the sharing of parameters through a very
 deep computational graph.
 For the simplicity of exposition, we refer to RNNs as operating on a sequence
 that contains vectors x( ) t with the time step index t ranging from to
 1
 τ. In
 practice, recurrent networks usually operate on minibatches of such sequences,
 with a different sequence length τ for each member of the minibatch. We have
 omitted the minibatch indices to simplify notation. Moreover, the time step index
 need not literally refer to the passage of time in the real world. Sometimes it refers
 only to the position in the sequence. RNNs may also be applied in two dimensions
 across spatial data such as images, and even when applied to data involving time,
 the network may have connections that go backwards in time, provided that the
 entire sequence is observed before it is provided to the network.
 This chapter extends the idea of a computational graph to include cycles. These
 cycles represent the influence of the present value of a variable on its own value
 at a future time step. Such computational graphs allow us to define recurrent
 neural networks. We then describe many different ways to construct, train, and
 use recurrent neural networks.
 For more information on recurrent neural networks than is available in this
 chapter, we refer the reader to the textbook of Graves 2012
 (
 ).
 374
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 10.1 UnfoldingComputationalGraphs
 Acomputationalgraphisawaytoformalizethestructureofasetofcomputations,
 suchas those involved inmappinginputsandparameterstooutputsandloss.
 Pleaserefertosection forageneral introduction. Inthissectionweexplain 6.5.1
 theideaofunfoldingarecursiveorrecurrentcomputationintoacomputational
 graphthathasarepetitivestructure, typicallycorrespondingtoachainofevents.
 Unfoldingthisgraphresultsinthesharingofparametersacrossadeepnetwork
 structure.
 Forexample,considertheclassical formofadynamicalsystem:
 s() t = ( fs( 1) t− ; ) θ, (10.1)
 wheres() t iscalledthestateofthesystem.
 Equation isrecurrentbecausethedefinitionof 10.1 sattimetrefersbackto
 thesamedefinitionattime . t−1
 Forafinitenumberoftimestepsτ, thegraphcanbeunfoldedbyapplying
 thedefinitionτ−1times.Forexample, ifweunfoldequation for 10.1 τ= 3time
 steps,weobtain
 s(3)= ( fs(2); ) θ (10.2)
 = ( ( f fs(1); ); ) θ θ (10.3)
 Unfoldingtheequationbyrepeatedlyapplyingthedefinitioninthiswayhas
 yieldedanexpressionthatdoesnot involverecurrence. Suchanexpressioncan
 nowberepresentedbyatraditionaldirectedacycliccomputationalgraph. The
 unfoldedcomputationalgraphofequation andequation is illustratedin 10.1 10.3
 figure . 10.1
 s(t−1) s(t−1) s() t s() t s(+1) t s(+1) t
 f f s( ) ... s( ) ... s( ) ... s( ) ...
 f f f f f f
 Figure10.1:Theclassicaldynamical systemdescribedbyequation , illustratedasan 10.1
 unfoldedcomputationalgraph. Eachnoderepresentsthestateatsometimetandthe
 functionfmapsthestateatttothestateatt+1.Thesameparameters(thesamevalue
 of usedtoparametrize )areusedforall timesteps. θ f
 Asanotherexample, letusconsideradynamical systemdrivenbyanexternal
 signalx() t ,
 s() t = ( fs( 1) t− ,x() t ; ) θ, (10.4)
 375
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 whereweseethatthestatenowcontainsinformationaboutthewholepastsequence.
 Recurrentneural networks canbebuilt inmanydifferentways. Muchas
 almostanyfunctioncanbeconsideredafeedforwardneuralnetwork,essentially
 anyfunctioninvolvingrecurrencecanbeconsideredarecurrentneuralnetwork.
 Manyrecurrentneuralnetworksuseequation orasimilarequationto 10.5
 definethevaluesoftheirhiddenunits. Toindicatethatthestateisthehidden
 unitsofthenetwork,wenowrewriteequation usingthevariable 10.4 htorepresent
 thestate:
 h() t = ( fh( 1) t− ,x() t ; ) θ, (10.5)
 illustratedinfigure , typicalRNNswilladdextraarchitectural featuressuch 10.2
 asoutputlayersthatreadinformationoutofthestate tomakepredictions. h
 Whentherecurrentnetworkistrainedtoperformataskthatrequirespredicting
 thefuturefromthepast, thenetworktypicallylearnstouseh() t asakindof lossy
 summaryofthetask-relevantaspectsofthepastsequenceof inputsuptot.This
 summaryisingeneralnecessarilylossy,sinceitmapsanarbitrarylengthsequence
 (x() t ,x( 1) t− ,x( 2) t− ,...,x(2),x(1))toafixedlengthvectorh() t .Dependingonthe
 trainingcriterion, thissummarymightselectivelykeepsomeaspectsofthepast
 sequencewithmoreprecisionthanotheraspects.Forexample, iftheRNNisused
 instatistical languagemodeling, typicallytopredictthenextwordgivenprevious
 words, itmaynotbenecessarytostorealloftheinformationintheinputsequence
 uptotimet,butratheronlyenoughinformationtopredicttherestofthesentence.
 Themostdemandingsituation iswhenweaskh() t toberichenoughtoallow
 onetoapproximatelyrecovertheinputsequence,as inautoencoderframeworks
 (chapter ). 14
 f f
 h h
 x x
 h(t−1) h(t−1) h() t h() t h(+1) t h(+1) t
 x(t−1) x(t−1) x() t x() t x(+1) t x(+1) t
 h( ) ... h( ) ... h( ) ... h( ) ...
 f f
 Unfold
 f f f f f
 Figure10.2:Arecurrentnetworkwithnooutputs.Thisrecurrentnetworkjustprocesses
 informationfromtheinputxbyincorporatingit intothestatehthatispassedforward
 throughtime. (Left)Circuitdiagram.Theblacksquareindicatesadelayofasingletime
 step. Thesamenetworkseenasanunfoldedcomputationalgraph,whereeach (Right)
 nodeisnowassociatedwithoneparticulartimeinstance.
 Equation canbedrawnintwodifferentways.OnewaytodrawtheRNN 10.5
 iswithadiagramcontainingonenodeforeverycomponentthatmightexist ina
 376
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 physical implementation of the model, such as a biological neural network. In this
 view, the network defines a circuit that operates in real time, with physical parts
 whose current state can influence their future state, as in the left of figure
 10.2
 .
 Throughout this chapter, we use a black square in a circuit diagram to indicate
 that an interaction takes place with a delay of a single time step, from the state
 at time t to the state at time t+ 1. The other way to draw the RNN is as an
 unfolded computational graph, in which each component is represented by many
 different variables, with one variable per time step, representing the state of the
 component at that point in time. Each variable for each time step is drawn as a
 separate node of the computational graph, as in the right of figure
 10.2
 . What we
 call unfolding is the operation that maps a circuit as in the left side of the figure
 to a computational graph with repeated pieces as in the right side. The unfolded
 graph now has a size that depends on the sequence length.
 We can represent the unfolded recurrence after steps with a function
 t
 h( ) t =g( ) t (x( ) t ,x( 1)
 t− ,x( 2)
 t− ,...,x(2),x(1))
 = ( fh( 1)
 t− ,x( ) t ; )
 θ
 The function g( ) t takes the whole past sequence (x( ) t ,x( 1)
 t− ,x( 2)
 g( ) t :
 (10.6)
 (10.7)
 t− ,...,x(2),x(1))
 as input and produces the current state, but the unfolded recurrent structure
 allows us to factorize g( ) t into repeated application of a function f. The unfolding
 process thus introduces two major advantages:
 1. Regardless of the sequence length, the learned model always has the same
 input size, because it is specified in terms of transition from one state to
 another state, rather than specified in terms of a variable-length history of
 states.
 2. It is possible to use the
 at every time step.
 same
 transition function
 f with the same parameters
 These two factors make it possible to learn a single model f that operates on
 all time steps and all sequence lengths, rather than needing to learn a separate
 model g( ) t for all possible time steps. Learning a single, shared model allows
 generalization to sequence lengths that did not appear in the training set, and
 allows the model to be estimated with far fewer training examples than would be
 required without parameter sharing.
 Both the recurrent graph and the unrolled graph have their uses. The recurrent
 graph is succinct. The unfolded graph provides an explicit description of which
 computations to perform. The unfolded graph also helps to illustrate the idea of
 377
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 informationflowforwardintime(computingoutputsandlosses)andbackward
 intime(computinggradients)byexplicitlyshowingthepathalongwhichthis
 informationflows.
 10.2 RecurrentNeuralNetworks
 Armedwiththegraphunrollingandparametersharingideasofsection ,we 10.1
 candesignawidevarietyofrecurrentneuralnetworks.
 U U
 VV
 W W
 o(t−1) o(t−1)
 h h
 oo
 yy
 L L
 x x
 o() t o() t o(+1) t o(+1) t
 L(t−1) L(t−1) L() t L() t L(+1) t L(+1) t
 y(t−1) y(t−1) y() t y() t y(+1) t y(+1) t
 h(t−1) h(t−1) h() t h() t h(+1) t h(+1) t
 x(t−1) x(t−1) x() t x() t x(+1) t x(+1) t
 W W W W W W W W
 h( ) ... h( ) ... h( ) ... h( ) ...
 VV VV VV
 U U U U U U
 Unfold
 Figure10.3:Thecomputationalgraphtocomputethetraininglossofarecurrentnetwork
 thatmapsaninputsequenceofxvaluestoacorrespondingsequenceofoutputovalues.
 AlossLmeasureshowfareachoisfromthecorrespondingtrainingtargety.Whenusing
 softmaxoutputs,weassumeoistheunnormalizedlogprobabilities.ThelossLinternally
 computesˆ y=softmax(o) andcomparesthistothetargety.TheRNNhasinputtohidden
 connectionsparametrizedbyaweightmatrixU,hidden-to-hiddenrecurrentconnections
 parametrizedbyaweightmatrixW,andhidden-to-outputconnectionsparametrizedby
 aweightmatrixV.Equation definesforwardpropagationinthismodel. 10.8 (Left)The
 RNNandits lossdrawnwithrecurrentconnections. (Right)Thesameseenasantime
unfoldedcomputationalgraph,whereeachnode isnowassociatedwithoneparticular
 timeinstance.
 Someexamplesof importantdesignpatterns forrecurrentneuralnetworks
 includethefollowing:
 378
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 • Recurrent networks that produce an output at each time step and have
 recurrent connections between hidden units, illustrated in figure
 10.3
 .
 • Recurrent networks that produce an output at each time step and have
 recurrent connections only from the output at one time step to the hidden
 units at the next time step, illustrated in figure 10.4
 • Recurrent networks with recurrent connections between hidden units, that
 10.5
 .
 read an entire sequence and then produce a single output, illustrated in
 f
 igure
 f
 igure
 10.3
 is a reasonably representative example that we return to throughout
 most of the chapter.
 The recurrent neural network of figure
 10.3
 and equation
 10.8
 is universal in the
 sense that any function computable by a Turing machine can be computed by such
 a recurrent network of a finite size. The output can be read from the RNN after
 a number of time steps that is asymptotically linear in the number of time steps
 used by the Turing machine and asymptotically linear in the length of the input
 ,
 ;
 (Siegelmann and Sontag 1991 Siegelmann 1995 Siegelmann and Sontag 1995
 ,
 Hyotyniemi 1996
 ,
 ;
 ,
 ;
 ). The functions computable by a Turing machine are discrete,
 so these results regard exact implementation of the function, not approximations.
 The RNN, when used as a Turing machine, takes a binary sequence as input and its
 outputs must be discretized to provide a binary output. It is possible to compute all
 (
 functions in this setting using a single specific RNN of finite size (Siegelmann and
 Sontag 1995
 ) use 886 units). The “input” of the Turing machine is a specification
 of the function to be computed, so the same network that simulates this Turing
 machine is sufficient for all problems. The theoretical RNN used for the proof
 can simulate an unbounded stack by representing its activations and weights with
 rational numbers of unbounded precision.
 10.3
 We now develop the forward propagation equations for the RNN depicted in
 f
 igure
 . The figure does not specify the choice of activation function for the
 hidden units. Here we assume the hyperbolic tangent activation function. Also,
 the figure does not specify exactly what form the output and loss function take.
 Here we assume that the output is discrete, as if the RNN is used to predict words
 or characters. A natural way to represent discrete variables is to regard the output
 o as giving the unnormalized log probabilities of each possible value of the discrete
 variable. We can then apply the softmax operation as a post-processing step to
 obtain a vector ˆ y of normalized probabilities over the output. Forward propagation
 begins with a specification of the initial state h(0). Then, for each time step from
 379
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 U
 V
 W
 o(t−1) o(t−1)
 h h
 oo
 yy
 L L
 x x
 o() t o() t o(+1) t o(+1) t
 L(t−1) L(t−1) L() t L() t L(+1) t L(+1) t
 y(t−1) y(t−1) y() t y() t y(+1) t y(+1) t
 h(t−1) h(t−1) h() t h() t h(+1) t h(+1) t
 x(t−1) x(t−1) x() t x() t x(+1) t x(+1) t
 W W W W
 o( ) ... o( ) ...
 h( ) ... h( ) ...
 V V V
 U U U
 Unfold
 Figure10.4:AnRNNwhoseonlyrecurrenceisthefeedbackconnectionfromtheoutput
 tothehiddenlayer.Ateachtimestept,theinput isxt,thehiddenlayeractivationsare
 h() t ,theoutputsareo() t ,thetargetsarey() t andtheloss isL() t . (Left)Circuitdiagram.
 (Right)Unfoldedcomputational graph. SuchanRNNis lesspowerful (canexpressa
 smallersetof functions)thanthoseinthefamilyrepresentedbyfigure .TheRNN 10.3
 infigure canchoosetoputanyinformationitwantsaboutthepast intoitshidden 10.3
 representationhandtransmithtothefuture. TheRNNinthisfigure is trainedto
 putaspecificoutputvalue intoo,andoistheonlyinformationit isallowedtosend
 tothefuture. Therearenodirectconnections fromhgoingforward. Theprevioush
 isconnectedtothepresentonlyindirectly,viathepredictions itwasusedtoproduce.
 Unlesso isveryhigh-dimensionalandrich, itwillusuallylackimportant information
 fromthepast.ThismakestheRNNinthisfigurelesspowerful,but itmaybeeasierto
 trainbecauseeachtimestepcanbetrainedinisolationfromtheothers,allowinggreater
 parallelizationduringtraining,asdescribedinsection . 10.2.1
 380
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 t
 t
 τ
 = 1 to = ,weapply the following update equations:
 a( ) t = +
 b Wh( 1)
 t− +Ux() t
 h( ) t = tanh(a( ) t )
 o( ) t = +
 c Vh() t
 ˆ
 y( ) t = softmax(o( ) t )
 (10.8)
 (10.9)
 (10.10)
 (10.11)
 where the parameters are the bias vectorsb and c along with the weight matrices
 U, V and W,respectively for input-to-hidden, hidden-to-output and hidden-to
hidden connections. This is an example of a recurrent network that maps an
 input sequence to an output sequence of the same length. The total loss for a
 x
 given sequence of values paired with a sequence of values would then be just
 y
 the sum of the losses over all the time steps. For example, if L( ) t is the negative
 log-likelihood of y( ) t given x(1),...,x( ) t , then
 
 L
 {x(1), . . . ,x( )
 
 =
 t
 L( ) t
 
 =−
 t
 
 τ } { , y(1),...,y( )
 τ }
 
 logpmodel
 y( ) t | {x(1),...,x( ) t}
 (10.12)
 (10.13)
 
 ,
 (10.14)
 where pmodel 
y( ) t | {x(1),...,x( ) t } is given by reading the entry fory( ) t from the
 model’s output vector ˆ y( ) t . Computing the gradient of this loss function with respect
 to the parameters is an expensive operation. The gradient computation involves
 performing a forward propagation pass moving left to right through our illustration
 of the unrolled graph in figure
 10.3
 , followed by a backward propagation pass
 moving right to left through the graph. The runtime is O(τ) and cannot be reduced
 by parallelization because the forward propagation graph is inherently sequential;
 each time step may only be computed after the previous one. States computed
 in the forward pass must be stored until they are reused during the backward
 pass, so the memory cost is also O(τ). The back-propagation algorithm applied
 to the unrolled graph with O(τ) cost is called back-propagation through time
 or BPTT and is discussed further in section
 10.2.2
 . The network with recurrence
 between hidden units is thus very powerful but also expensive to train. Is there an
 alternative?