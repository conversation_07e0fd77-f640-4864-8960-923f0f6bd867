<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Likelihood Ratio Test (LRT) - Step by Step Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <!-- Plotly.js for interactive graphs -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--success-color));
        }

        .header h1 {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: var(--text-secondary);
            font-weight: 300;
        }

        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border-left: 5px solid var(--primary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .section h3 {
            color: var(--secondary-color);
            font-size: 1.6em;
            margin: 25px 0 15px 0;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 8px;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .highlight-box {
            background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .highlight-box::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .formula-box {
            background: #f8fafc;
            border: 2px solid var(--accent-color);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
        }

        .example-box {
            background: linear-gradient(135deg, #fef3cd 0%, #fff2cd 100%);
            border: 2px solid var(--warning-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .example-box::before {
            content: '📝';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .definition-box {
            background: linear-gradient(135deg, #dcfce7 0%, #d1fae5 100%);
            border: 2px solid var(--success-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .definition-box::before {
            content: '📋';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: var(--light-bg);
            border-radius: 10px;
        }

        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            margin: 20px 0;
            padding: 20px;
            background: var(--light-bg);
            border-radius: 10px;
            border-left: 4px solid var(--accent-color);
            position: relative;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: var(--accent-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .math-display {
            overflow-x: auto;
            padding: 10px 0;
        }

        .navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            text-decoration: none;
            color: var(--text-secondary);
            border-radius: 5px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Likelihood Ratio Test (LRT)</h1>
            <p class="subtitle">A Comprehensive Step-by-Step Tutorial</p>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <a href="#introduction" class="nav-link">Introduction</a>
            <a href="#likelihood-review" class="nav-link">Likelihood Review</a>
            <a href="#simple-lrt" class="nav-link">Simple LRT</a>
            <a href="#example" class="nav-link">Example</a>
            <a href="#general-lrt" class="nav-link">General LRT</a>
            <a href="#procedure" class="nav-link">Procedure</a>
        </div>

        <!-- Introduction Section -->
        <div class="section" id="introduction">
            <h2><span class="step-number">1</span>Introduction to Likelihood Ratio Tests</h2>
            
            <p>The <strong>Likelihood Ratio Test (LRT)</strong> is a fundamental statistical method for hypothesis testing that provides a systematic way to choose between competing hypotheses based on observed data. It's one of the most powerful and widely applicable testing procedures in statistics.</p>

            <div class="highlight-box">
                <h3>Why Use Likelihood Ratio Tests?</h3>
                <ul>
                    <li><strong>Optimality:</strong> LRT has many optimal properties under certain conditions</li>
                    <li><strong>Generality:</strong> Can be applied to a wide variety of statistical problems</li>
                    <li><strong>Intuitive:</strong> Based on comparing how likely the data is under different hypotheses</li>
                    <li><strong>Asymptotic Properties:</strong> Well-established theoretical foundation</li>
                </ul>
            </div>

            <div class="visualization">
                <svg width="100%" height="300" viewBox="0 0 800 300">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e0f2fe;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f3e5f5;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="800" height="300" fill="url(#bgGradient)" rx="15"/>
                    
                    <!-- Central concept -->
                    <circle cx="400" cy="150" r="80" fill="#2563eb" opacity="0.9"/>
                    <text x="400" y="145" text-anchor="middle" fill="white" font-size="16" font-weight="bold">Likelihood</text>
                    <text x="400" y="165" text-anchor="middle" fill="white" font-size="16" font-weight="bold">Ratio Test</text>
                    
                    <!-- Data -->
                    <circle cx="150" cy="80" r="50" fill="#10b981" opacity="0.8"/>
                    <text x="150" y="75" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Observed</text>
                    <text x="150" y="90" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Data</text>
                    
                    <!-- Hypothesis 0 -->
                    <circle cx="150" cy="220" r="50" fill="#f59e0b" opacity="0.8"/>
                    <text x="150" y="215" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Hypothesis</text>
                    <text x="150" y="230" text-anchor="middle" fill="white" font-size="14" font-weight="bold">H₀</text>
                    
                    <!-- Hypothesis 1 -->
                    <circle cx="650" cy="80" r="50" fill="#ef4444" opacity="0.8"/>
                    <text x="650" y="75" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Hypothesis</text>
                    <text x="650" y="90" text-anchor="middle" fill="white" font-size="14" font-weight="bold">H₁</text>
                    
                    <!-- Decision -->
                    <circle cx="650" cy="220" r="50" fill="#8b5cf6" opacity="0.8"/>
                    <text x="650" y="215" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Statistical</text>
                    <text x="650" y="230" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Decision</text>
                    
                    <!-- Arrows -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                refX="10" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
                        </marker>
                    </defs>
                    
                    <!-- Arrows pointing to LRT -->
                    <line x1="220" y1="100" x2="340" y2="130" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="220" y1="200" x2="340" y2="170" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrows from LRT -->
                    <line x1="460" y1="130" x2="580" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="460" y1="170" x2="580" y2="200" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Labels -->
                    <text x="280" y="110" text-anchor="middle" fill="#374151" font-size="12">Data Input</text>
                    <text x="280" y="190" text-anchor="middle" fill="#374151" font-size="12">Model Input</text>
                    <text x="520" y="110" text-anchor="middle" fill="#374151" font-size="12">Likelihood Ratio</text>
                    <text x="520" y="190" text-anchor="middle" fill="#374151" font-size="12">Accept/Reject</text>
                </svg>
            </div>

            <p>The fundamental idea behind LRT is elegant: <em>compare how likely the observed data is under different hypotheses, and choose the hypothesis that makes the data more plausible</em>.</p>
        </div>

        <!-- Likelihood Function Review Section -->
        <div class="section" id="likelihood-review">
            <h2><span class="step-number">2</span>Review of the Likelihood Function</h2>
            
            <p>Before diving into LRT, let's review the <strong>likelihood function</strong> - the foundation upon which LRT is built.</p>

            <div class="definition-box">
                <h3>Definition: Likelihood Function</h3>
                <p>Let $X_1, X_2, X_3, \ldots, X_n$ be a random sample from a distribution with parameter $\theta$. Suppose we observe $X_1 = x_1, X_2 = x_2, \ldots, X_n = x_n$.</p>
                
                <div class="formula-box">
                    <p><strong>For Discrete Random Variables:</strong></p>
                    <div class="math-display">
                        $$L(x_1, x_2, \ldots, x_n; \theta) = P_{X_1X_2\cdots X_n}(x_1, x_2, \ldots, x_n; \theta)$$
                    </div>
                </div>
                
                <div class="formula-box">
                    <p><strong>For Continuous Random Variables:</strong></p>
                    <div class="math-display">
                        $$L(x_1, x_2, \ldots, x_n; \theta) = f_{X_1X_2\cdots X_n}(x_1, x_2, \ldots, x_n; \theta)$$
                    </div>
                </div>
            </div>

            <div class="highlight-box">
                <h3>Key Insight</h3>
                <p>The likelihood function $L(\text{data}; \theta)$ tells us: <em>"How likely is this observed data, given a specific value of the parameter $\theta$?"</em></p>
                <p>Notice that we treat the data as <strong>fixed</strong> (what we observed) and $\theta$ as <strong>variable</strong> (what we want to learn about).</p>
            </div>

            <div class="visualization">
                <svg width="100%" height="400" viewBox="0 0 800 400">
                    <!-- Background -->
                    <rect width="800" height="400" fill="#f8fafc" rx="15"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">Likelihood Function Concept</text>
                    
                    <!-- Data points (fixed) -->
                    <rect x="50" y="80" width="300" height="120" fill="#dcfce7" rx="10" stroke="#10b981" stroke-width="2"/>
                    <text x="200" y="105" text-anchor="middle" fill="#065f46" font-size="16" font-weight="bold">Observed Data (Fixed)</text>
                    <text x="200" y="125" text-anchor="middle" fill="#065f46" font-size="14">x₁ = 2.3, x₂ = 1.8, x₃ = 2.7</text>
                    <text x="200" y="145" text-anchor="middle" fill="#065f46" font-size="14">x₄ = 2.1, x₅ = 2.5, ...</text>
                    <circle cx="80" cy="170" r="4" fill="#10b981"/>
                    <circle cx="120" cy="170" r="4" fill="#10b981"/>
                    <circle cx="160" cy="170" r="4" fill="#10b981"/>
                    <circle cx="200" cy="170" r="4" fill="#10b981"/>
                    <circle cx="240" cy="170" r="4" fill="#10b981"/>
                    <circle cx="280" cy="170" r="4" fill="#10b981"/>
                    <circle cx="320" cy="170" r="4" fill="#10b981"/>
                    
                    <!-- Parameters (variable) -->
                    <rect x="450" y="80" width="300" height="120" fill="#fef3cd" rx="10" stroke="#f59e0b" stroke-width="2"/>
                    <text x="600" y="105" text-anchor="middle" fill="#92400e" font-size="16" font-weight="bold">Parameter θ (Variable)</text>
                    <text x="600" y="125" text-anchor="middle" fill="#92400e" font-size="14">θ = 0.5 → L = 0.023</text>
                    <text x="600" y="145" text-anchor="middle" fill="#92400e" font-size="14">θ = 1.0 → L = 0.087</text>
                    <text x="600" y="165" text-anchor="middle" fill="#92400e" font-size="14">θ = 2.2 → L = 0.156</text>
                    <text x="600" y="185" text-anchor="middle" fill="#92400e" font-size="14">θ = 3.0 → L = 0.098</text>
                    
                    <!-- Arrow -->
                    <defs>
                        <marker id="arrow2" markerWidth="10" markerHeight="7" 
                                refX="10" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#2563eb" />
                        </marker>
                    </defs>
                    <line x1="370" y1="140" x2="430" y2="140" stroke="#2563eb" stroke-width="3" marker-end="url(#arrow2)"/>
                    <text x="400" y="130" text-anchor="middle" fill="#2563eb" font-size="14" font-weight="bold">Likelihood</text>
                    <text x="400" y="155" text-anchor="middle" fill="#2563eb" font-size="14" font-weight="bold">Function</text>
                    
                    <!-- Likelihood curve -->
                    <g transform="translate(50, 250)">
                        <text x="350" y="-10" text-anchor="middle" fill="#1f2937" font-size="16" font-weight="bold">Likelihood as a function of θ</text>
                        
                        <!-- Axes -->
                        <line x1="50" y1="120" x2="650" y2="120" stroke="#374151" stroke-width="2"/>
                        <line x1="50" y1="120" x2="50" y2="20" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Axis labels -->
                        <text x="350" y="145" text-anchor="middle" fill="#374151" font-size="14">θ</text>
                        <text x="30" y="70" text-anchor="middle" fill="#374151" font-size="14" transform="rotate(-90, 30, 70)">L(data; θ)</text>
                        
                        <!-- Likelihood curve (bell-shaped) -->
                        <path d="M 70 110 Q 150 80, 200 40 Q 250 25, 300 30 Q 400 35, 450 45 Q 550 70, 600 100" 
                              stroke="#ef4444" stroke-width="3" fill="none"/>
                        
                        <!-- Maximum point -->
                        <circle cx="300" cy="30" r="5" fill="#ef4444"/>
                        <text x="300" y="15" text-anchor="middle" fill="#ef4444" font-size="12" font-weight="bold">Maximum</text>
                        <text x="300" y="135" text-anchor="middle" fill="#374151" font-size="12">θ̂ (MLE)</text>
                        
                        <!-- Tick marks -->
                        <line x1="100" y1="115" x2="100" y2="125" stroke="#374151"/>
                        <line x1="200" y1="115" x2="200" y2="125" stroke="#374151"/>
                        <line x1="300" y1="115" x2="300" y2="125" stroke="#374151"/>
                        <line x1="400" y1="115" x2="400" y2="125" stroke="#374151"/>
                        <line x1="500" y1="115" x2="500" y2="125" stroke="#374151"/>
                        
                        <text x="100" y="135" text-anchor="middle" fill="#374151" font-size="10">0.5</text>
                        <text x="200" y="135" text-anchor="middle" fill="#374151" font-size="10">1.5</text>
                        <text x="400" y="135" text-anchor="middle" fill="#374151" font-size="10">3.5</text>
                        <text x="500" y="135" text-anchor="middle" fill="#374151" font-size="10">4.5</text>
                    </g>
                </svg>
            </div>

            <div class="example-box">
                <h3>Simple Example</h3>
                <p>Suppose we flip a coin 5 times and get: H, H, T, H, T</p>
                <p>If the probability of heads is $\theta$, then:</p>
                <div class="formula-box">
                    $$L(\text{H,H,T,H,T}; \theta) = \theta \cdot \theta \cdot (1-\theta) \cdot \theta \cdot (1-\theta) = \theta^3(1-\theta)^2$$
                </div>
                <p>This function tells us how likely this sequence is for different values of $\theta$.</p>
                <ul>
                    <li>If $\theta = 0.6$: $L = (0.6)^3(0.4)^2 = 0.03456$</li>
                    <li>If $\theta = 0.5$: $L = (0.5)^3(0.5)^2 = 0.03125$</li>
                    <li>If $\theta = 0.3$: $L = (0.3)^3(0.7)^2 = 0.01323$</li>
                </ul>
                <p>The data is most likely when $\theta = 0.6$ (which makes intuitive sense: 3 heads out of 5 flips).</p>
            </div>
        </div>

        <!-- Simple LRT Section -->
        <div class="section" id="simple-lrt">
            <h2><span class="step-number">3</span>Likelihood Ratio Test for Simple Hypotheses</h2>
            
            <p>Now let's see how to use likelihood functions to test hypotheses. We'll start with the simplest case: <strong>simple hypotheses</strong>.</p>

            <div class="definition-box">
                <h3>Simple Hypotheses</h3>
                <p>A hypothesis is called <strong>simple</strong> if it completely specifies the parameter. For example:</p>
                <div class="formula-box">
                    <p><strong>H₀:</strong> $\theta = \theta_0$ (null hypothesis)</p>
                    <p><strong>H₁:</strong> $\theta = \theta_1$ (alternative hypothesis)</p>
                </div>
                <p>Both hypotheses specify exact values for $\theta$, not ranges.</p>
            </div>

            <h3>The Likelihood Ratio Statistic</h3>
            <p>For simple hypotheses, the likelihood ratio is defined as:</p>
            
            <div class="formula-box">
                <div class="math-display">
                    $$\lambda(x_1, x_2, \ldots, x_n) = \frac{L(x_1, x_2, \ldots, x_n; \theta_0)}{L(x_1, x_2, \ldots, x_n; \theta_1)}$$
                </div>
                <p>where:</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>$L(\text{data}; \theta_0)$ = likelihood under null hypothesis</li>
                    <li>$L(\text{data}; \theta_1)$ = likelihood under alternative hypothesis</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>Decision Rule</h3>
                <p>Choose a threshold constant $c > 0$:</p>
                <ul>
                    <li><strong>If $\lambda \geq c$:</strong> Accept H₀ (null hypothesis more likely)</li>
                    <li><strong>If $\lambda < c$:</strong> Reject H₀ (alternative hypothesis more likely)</li>
                </ul>
                <p>The value of $c$ is chosen based on the desired significance level $\alpha$.</p>
            </div>

            <div class="visualization">
                <svg width="100%" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="#f8fafc" rx="15"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">LRT Decision Process</text>
                    
                    <!-- Data box -->
                    <rect x="50" y="50" width="150" height="80" fill="#e0f2fe" rx="8" stroke="#0284c7" stroke-width="2"/>
                    <text x="125" y="75" text-anchor="middle" fill="#0c4a6e" font-size="14" font-weight="bold">Observed</text>
                    <text x="125" y="95" text-anchor="middle" fill="#0c4a6e" font-size="14" font-weight="bold">Data</text>
                    <text x="125" y="115" text-anchor="middle" fill="#0c4a6e" font-size="12">x₁, x₂, ..., xₙ</text>
                    
                    <!-- Likelihood calculations -->
                    <rect x="250" y="40" width="140" height="50" fill="#dcfce7" rx="8" stroke="#16a34a" stroke-width="2"/>
                    <text x="320" y="60" text-anchor="middle" fill="#15803d" font-size="12" font-weight="bold">L(data; θ₀)</text>
                    <text x="320" y="75" text-anchor="middle" fill="#15803d" font-size="10">Under H₀</text>
                    
                    <rect x="250" y="110" width="140" height="50" fill="#fef3cd" rx="8" stroke="#d97706" stroke-width="2"/>
                    <text x="320" y="130" text-anchor="middle" fill="#92400e" font-size="12" font-weight="bold">L(data; θ₁)</text>
                    <text x="320" y="145" text-anchor="middle" fill="#92400e" font-size="10">Under H₁</text>
                    
                    <!-- Ratio calculation -->
                    <rect x="450" y="75" width="100" height="50" fill="#f3e8ff" rx="8" stroke="#9333ea" stroke-width="2"/>
                    <text x="500" y="95" text-anchor="middle" fill="#7c3aed" font-size="14" font-weight="bold">λ = L₀/L₁</text>
                    <text x="500" y="110" text-anchor="middle" fill="#7c3aed" font-size="10">Ratio</text>
                    
                    <!-- Decision diamond -->
                    <polygon points="600,100 650,75 700,100 650,125" fill="#fee2e2" stroke="#dc2626" stroke-width="2"/>
                    <text x="650" y="95" text-anchor="middle" fill="#991b1b" font-size="12" font-weight="bold">λ ≥ c?</text>
                    <text x="650" y="110" text-anchor="middle" fill="#991b1b" font-size="10">Decision</text>
                    
                    <!-- Outcomes -->
                    <rect x="580" y="170" width="140" height="40" fill="#dcfce7" rx="8" stroke="#16a34a" stroke-width="2"/>
                    <text x="650" y="185" text-anchor="middle" fill="#15803d" font-size="12" font-weight="bold">Accept H₀</text>
                    <text x="650" y="200" text-anchor="middle" fill="#15803d" font-size="10">λ ≥ c</text>
                    
                    <rect x="580" y="230" width="140" height="40" fill="#fee2e2" rx="8" stroke="#dc2626" stroke-width="2"/>
                    <text x="650" y="245" text-anchor="middle" fill="#991b1b" font-size="12" font-weight="bold">Reject H₀</text>
                    <text x="650" y="260" text-anchor="middle" fill="#991b1b" font-size="10">λ < c</text>
                    
                    <!-- Arrows -->
                    <defs>
                        <marker id="arrow3" markerWidth="8" markerHeight="6" 
                                refX="8" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#374151" />
                        </marker>
                    </defs>
                    
                    <!-- Data to likelihoods -->
                    <line x1="200" y1="75" x2="240" y2="65" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    <line x1="200" y1="105" x2="240" y2="135" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    
                    <!-- Likelihoods to ratio -->
                    <line x1="390" y1="85" x2="440" y2="95" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    <line x1="390" y1="125" x2="440" y2="105" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    
                    <!-- Ratio to decision -->
                    <line x1="550" y1="100" x2="590" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    
                    <!-- Decision to outcomes -->
                    <line x1="650" y1="125" x2="650" y2="160" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    <line x1="650" y1="125" x2="650" y2="220" stroke="#374151" stroke-width="2" marker-end="url(#arrow3)"/>
                    
                    <!-- Labels for decision paths -->
                    <text x="670" y="145" fill="#15803d" font-size="10">Yes</text>
                    <text x="670" y="205" fill="#991b1b" font-size="10">No</text>
                    
                    <!-- Interpretation box -->
                    <rect x="50" y="190" width="480" height="120" fill="#fffbeb" rx="10" stroke="#f59e0b" stroke-width="2"/>
                    <text x="290" y="215" text-anchor="middle" fill="#92400e" font-size="16" font-weight="bold">Interpretation</text>
                    <text x="70" y="240" fill="#92400e" font-size="12">• Large λ: Data more likely under H₀ → Accept H₀</text>
                    <text x="70" y="260" fill="#92400e" font-size="12">• Small λ: Data more likely under H₁ → Reject H₀</text>
                    <text x="70" y="280" fill="#92400e" font-size="12">• Threshold c determines the "cutoff" based on desired α level</text>
                    <text x="70" y="300" fill="#92400e" font-size="12">• P(Type I error) = P(Reject H₀ | H₀ true) = α</text>
                                 </svg>
             </div>
         </div>

        <!-- Detailed Example Section -->
        <div class="section" id="example">
            <h2><span class="step-number">4</span>Worked Example: The Radar Problem</h2>
            
            <p>Let's work through a complete example to see how LRT works in practice. This is the radar detection problem from the original text.</p>

            <div class="example-box">
                <h3>Problem Setup</h3>
                <p>We observe a random variable $X$ given by:</p>
                <div class="formula-box">
                    $$X = \theta + W$$
                </div>
                <p>where $W \sim N(0, \sigma^2 = \frac{1}{9})$ (noise) and $\theta$ is the signal we want to detect.</p>
                
                <p><strong>Hypotheses:</strong></p>
                <ul>
                    <li><strong>H₀:</strong> $\theta = 0$ (no signal present)</li>
                    <li><strong>H₁:</strong> $\theta = 1$ (signal present)</li>
                </ul>
                <p><strong>Goal:</strong> Design a level $\alpha = 0.05$ test</p>
            </div>

            <h3>Step-by-Step Solution</h3>

            <ol class="step-list">
                <li>
                    <strong>Find the likelihood functions</strong>
                    <p>Under H₀: $\theta = 0$, so $X \sim N(0, \frac{1}{9})$</p>
                    <div class="formula-box">
                        $$L(x; \theta_0) = f_X(x; \theta_0) = \frac{3}{\sqrt{2\pi}} e^{-\frac{9x^2}{2}}$$
                    </div>
                    
                    <p>Under H₁: $\theta = 1$, so $X \sim N(1, \frac{1}{9})$</p>
                    <div class="formula-box">
                        $$L(x; \theta_1) = f_X(x; \theta_1) = \frac{3}{\sqrt{2\pi}} e^{-\frac{9(x-1)^2}{2}}$$
                    </div>
                </li>

                <li>
                    <strong>Calculate the likelihood ratio</strong>
                    <div class="formula-box">
                        $$\lambda(x) = \frac{L(x; \theta_0)}{L(x; \theta_1)} = \frac{e^{-\frac{9x^2}{2}}}{e^{-\frac{9(x-1)^2}{2}}} = e^{\frac{9(x-1)^2 - 9x^2}{2}}$$
                    </div>
                    
                    <p>Simplifying the exponent:</p>
                    <div class="formula-box">
                        $$\frac{9(x-1)^2 - 9x^2}{2} = \frac{9(x^2 - 2x + 1) - 9x^2}{2} = \frac{9(-2x + 1)}{2} = \frac{9(1-2x)}{2}$$
                    </div>
                    
                    <p>Therefore:</p>
                    <div class="formula-box">
                        $$\lambda(x) = \exp\left\{\frac{9(1-2x)}{2}\right\}$$
                    </div>
                </li>

                <li>
                    <strong>Set up the decision rule</strong>
                    <p>We accept H₀ if $\lambda(x) \geq c$, which means:</p>
                    <div class="formula-box">
                        $$\exp\left\{\frac{9(1-2x)}{2}\right\} \geq c$$
                    </div>
                    
                    <p>Taking natural log of both sides:</p>
                    <div class="formula-box">
                        $$\frac{9(1-2x)}{2} \geq \ln(c)$$
                        $$1-2x \geq \frac{2\ln(c)}{9}$$
                        $$x \leq \frac{1}{2} - \frac{\ln(c)}{9}$$
                    </div>
                    
                    <p>Let $c' = \frac{1}{2} - \frac{\ln(c)}{9}$. We accept H₀ if $X \leq c'$.</p>
                </li>

                <li>
                    <strong>Find the threshold using $\alpha = 0.05$</strong>
                    <p>The Type I error probability is:</p>
                    <div class="formula-box">
                        $$P(\text{Type I error}) = P(\text{Reject H₀} | \text{H₀ true}) = P(X > c' | \theta = 0)$$
                    </div>
                    
                    <p>Since $X \sim N(0, \frac{1}{9})$ under H₀:</p>
                    <div class="formula-box">
                        $$P(X > c') = 1 - \Phi(3c') = \alpha = 0.05$$
                    </div>
                    
                    <p>Therefore: $\Phi(3c') = 0.95$, which gives us $3c' = \Phi^{-1}(0.95) = 1.645$</p>
                    <div class="formula-box">
                        $$c' = \frac{1.645}{3} \approx 0.548$$
                    </div>
                </li>

                <li>
                    <strong>Final decision rule</strong>
                    <div class="highlight-box">
                        <p><strong>Decision Rule:</strong></p>
                        <ul>
                            <li><strong>If $X \leq 0.548$:</strong> Accept H₀ (no signal)</li>
                            <li><strong>If $X > 0.548$:</strong> Reject H₀ (signal detected)</li>
                        </ul>
                    </div>
                </li>
            </ol>

            <div class="visualization">
                <div id="radarExample" style="width:100%; height:400px;"></div>
                <script>
                    // Create interactive plot showing the decision regions
                    function normalPDF(x, mu, sigma) {
                        return (1 / (sigma * Math.sqrt(2 * Math.PI))) * Math.exp(-0.5 * Math.pow((x - mu) / sigma, 2));
                    }
                    
                    const x = [];
                    const y0 = [];
                    const y1 = [];
                    
                    for (let i = -1; i <= 3; i += 0.01) {
                        x.push(i);
                        y0.push(normalPDF(i, 0, 1/3)); // σ = 1/3 since σ² = 1/9
                        y1.push(normalPDF(i, 1, 1/3));
                    }
                    
                    const trace0 = {
                        x: x,
                        y: y0,
                        type: 'scatter',
                        mode: 'lines',
                        name: 'H₀: X ~ N(0, 1/9)',
                        line: {color: '#10b981', width: 3}
                    };
                    
                    const trace1 = {
                        x: x,
                        y: y1,
                        type: 'scatter',
                        mode: 'lines',
                        name: 'H₁: X ~ N(1, 1/9)',
                        line: {color: '#ef4444', width: 3}
                    };
                    
                    // Acceptance region
                    const acceptX = x.filter(val => val <= 0.548);
                    const acceptY = acceptX.map(val => normalPDF(val, 0, 1/3));
                    
                    const acceptRegion = {
                        x: acceptX,
                        y: acceptY,
                        type: 'scatter',
                        mode: 'lines',
                        fill: 'tozeroy',
                        fillcolor: 'rgba(16, 185, 129, 0.3)',
                        line: {color: 'rgba(16, 185, 129, 0)'},
                        name: 'Accept H₀',
                        showlegend: false
                    };
                    
                    // Rejection region
                    const rejectX = x.filter(val => val > 0.548);
                    const rejectY = rejectX.map(val => normalPDF(val, 0, 1/3));
                    
                    const rejectRegion = {
                        x: rejectX,
                        y: rejectY,
                        type: 'scatter',
                        mode: 'lines',
                        fill: 'tozeroy',
                        fillcolor: 'rgba(239, 68, 68, 0.3)',
                        line: {color: 'rgba(239, 68, 68, 0)'},
                        name: 'Reject H₀',
                        showlegend: false
                    };
                    
                    // Critical value line
                    const criticalLine = {
                        x: [0.548, 0.548],
                        y: [0, Math.max(...y0)],
                        type: 'scatter',
                        mode: 'lines',
                        line: {color: '#f59e0b', width: 3, dash: 'dash'},
                        name: 'Critical Value (0.548)'
                    };
                    
                    const layout = {
                        title: {
                            text: 'Radar Problem: LRT Decision Regions',
                            font: {size: 18}
                        },
                        xaxis: {title: 'Observed Value (X)'},
                        yaxis: {title: 'Probability Density'},
                        margin: {t: 60, b: 60, l: 60, r: 60},
                        showlegend: true,
                        legend: {x: 0.7, y: 0.9}
                    };
                    
                    Plotly.newPlot('radarExample', [acceptRegion, rejectRegion, trace0, trace1, criticalLine], layout, {responsive: true});
                </script>
            </div>

            <div class="highlight-box">
                <h3>Interpretation</h3>
                <p>This example demonstrates several key points:</p>
                <ul>
                    <li><strong>Geometric Intuition:</strong> We're comparing how well each hypothesis explains the data</li>
                    <li><strong>Optimal Threshold:</strong> The critical value 0.548 optimally balances Type I and Type II errors</li>
                    <li><strong>Equivalence:</strong> This LRT gives the same result as other optimal tests (like the uniformly most powerful test)</li>
                    <li><strong>Practical Application:</strong> Real radar systems use similar principles for signal detection</li>
                </ul>
            </div>
        </div>

        <!-- General LRT Section -->
        <div class="section" id="general-lrt">
            <h2><span class="step-number">5</span>General Likelihood Ratio Test</h2>
            
            <p>In practice, we often deal with <strong>composite hypotheses</strong> where the parameter isn't completely specified. The general LRT extends our approach to handle these cases.</p>

            <div class="definition-box">
                <h3>Composite Hypotheses</h3>
                <p>When hypotheses specify parameter ranges rather than exact values:</p>
                <div class="formula-box">
                    <p><strong>H₀:</strong> $\theta \in S_0$ (null parameter space)</p>
                    <p><strong>H₁:</strong> $\theta \in S_1$ (alternative parameter space)</p>
                </div>
                <p>where $S_0$ and $S_1$ are disjoint sets, and $S = S_0 \cup S_1$ is the entire parameter space.</p>
            </div>

            <h3>The General LRT Statistic</h3>
            <p>For composite hypotheses, we use the <strong>supremum</strong> (least upper bound) of the likelihood over each parameter space:</p>

            <div class="formula-box">
                <div class="math-display">
                    $$\lambda(x_1, x_2, \ldots, x_n) = \frac{\sup\{L(x_1, x_2, \ldots, x_n; \theta) : \theta \in S_0\}}{\sup\{L(x_1, x_2, \ldots, x_n; \theta) : \theta \in S\}}$$
                </div>
                <p>where:</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>Numerator: Maximum likelihood under H₀</li>
                    <li>Denominator: Maximum likelihood over all possible values</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>Key Properties</h3>
                <ul>
                    <li><strong>Range:</strong> $0 \leq \lambda \leq 1$ (always between 0 and 1)</li>
                    <li><strong>Interpretation:</strong> $\lambda$ close to 1 means H₀ fits the data well</li>
                    <li><strong>Decision Rule:</strong> Reject H₀ if $\lambda < c$ for some threshold $c \in [0,1]$</li>
                </ul>
            </div>

            <div class="visualization">
                <svg width="100%" height="450" viewBox="0 0 900 450">
                    <!-- Background -->
                    <rect width="900" height="450" fill="#f8fafc" rx="15"/>
                    
                    <!-- Title -->
                    <text x="450" y="25" text-anchor="middle" fill="#1f2937" font-size="18" font-weight="bold">General LRT: Finding Maximum Likelihoods</text>
                    
                    <!-- Parameter space visualization -->
                    <g transform="translate(50, 50)">
                        <!-- Full parameter space S -->
                        <rect x="50" y="50" width="300" height="150" fill="#e0f2fe" rx="10" stroke="#0284c7" stroke-width="3"/>
                        <text x="200" y="35" text-anchor="middle" fill="#0c4a6e" font-size="16" font-weight="bold">Full Parameter Space S</text>
                        
                        <!-- S0 (null space) -->
                        <rect x="70" y="70" width="120" height="110" fill="#dcfce7" rx="8" stroke="#16a34a" stroke-width="2"/>
                        <text x="130" y="90" text-anchor="middle" fill="#15803d" font-size="14" font-weight="bold">S₀</text>
                        <text x="130" y="110" text-anchor="middle" fill="#15803d" font-size="12">(Null Space)</text>
                        <text x="130" y="130" text-anchor="middle" fill="#15803d" font-size="12">Max: L₀</text>
                        <circle cx="130" cy="150" r="6" fill="#16a34a"/>
                        <text x="130" y="170" text-anchor="middle" fill="#15803d" font-size="10">θ̂₀</text>
                        
                        <!-- S1 (alternative space) -->
                        <rect x="210" y="70" width="120" height="110" fill="#fef3cd" rx="8" stroke="#d97706" stroke-width="2"/>
                        <text x="270" y="90" text-anchor="middle" fill="#92400e" font-size="14" font-weight="bold">S₁</text>
                        <text x="270" y="110" text-anchor="middle" fill="#92400e" font-size="12">(Alt Space)</text>
                        <text x="270" y="130" text-anchor="middle" fill="#92400e" font-size="12">Max: L₁</text>
                        <circle cx="270" cy="150" r="6" fill="#d97706"/>
                        <text x="270" y="170" text-anchor="middle" fill="#92400e" font-size="10">θ̂₁</text>
                        
                        <!-- Global maximum -->
                        <circle cx="240" cy="120" r="8" fill="#ef4444"/>
                        <text x="240" y="105" text-anchor="middle" fill="#ef4444" font-size="12" font-weight="bold">Global Max</text>
                        <text x="240" y="140" text-anchor="middle" fill="#ef4444" font-size="10">θ̂ (MLE)</text>
                    </g>
                    
                    <!-- Likelihood curve visualization -->
                    <g transform="translate(450, 80)">
                        <text x="200" y="0" text-anchor="middle" fill="#1f2937" font-size="16" font-weight="bold">Likelihood Function L(data; θ)</text>
                        
                        <!-- Axes -->
                        <line x1="20" y1="280" x2="380" y2="280" stroke="#374151" stroke-width="2"/>
                        <line x1="20" y1="280" x2="20" y2="50" stroke="#374151" stroke-width="2"/>
                        
                        <!-- Parameter regions -->
                        <rect x="30" y="270" width="120" height="15" fill="#dcfce7" opacity="0.7"/>
                        <text x="90" y="300" text-anchor="middle" fill="#15803d" font-size="12">S₀</text>
                        
                        <rect x="240" y="270" width="120" height="15" fill="#fef3cd" opacity="0.7"/>
                        <text x="300" y="300" text-anchor="middle" fill="#92400e" font-size="12">S₁</text>
                        
                        <!-- Likelihood curve -->
                        <path d="M 30 250 Q 80 200, 120 150 Q 160 80, 200 60 Q 240 55, 280 70 Q 320 100, 360 180" 
                              stroke="#2563eb" stroke-width="3" fill="none"/>
                        
                        <!-- Maximum points -->
                        <circle cx="90" cy="175" r="5" fill="#16a34a"/>
                        <text x="90" y="165" text-anchor="middle" fill="#15803d" font-size="11">L₀</text>
                        
                        <circle cx="300" cy="85" r="5" fill="#d97706"/>
                        <text x="300" y="75" text-anchor="middle" fill="#92400e" font-size="11">L₁</text>
                        
                        <circle cx="200" cy="60" r="6" fill="#ef4444"/>
                        <text x="200" y="45" text-anchor="middle" fill="#ef4444" font-size="11" font-weight="bold">L (Global)</text>
                        
                        <!-- Vertical lines to show maxima -->
                        <line x1="90" y1="175" x2="90" y2="280" stroke="#16a34a" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="200" y1="60" x2="200" y2="280" stroke="#ef4444" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="300" y1="85" x2="300" y2="280" stroke="#d97706" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <!-- Axis labels -->
                        <text x="200" y="320" text-anchor="middle" fill="#374151" font-size="14">θ</text>
                        <text x="10" y="165" text-anchor="middle" fill="#374151" font-size="14" transform="rotate(-90, 10, 165)">L(data; θ)</text>
                    </g>
                    
                    <!-- Formula box -->
                    <rect x="50" y="350" width="800" height="80" fill="#fffbeb" rx="10" stroke="#f59e0b" stroke-width="2"/>
                    <text x="450" y="375" text-anchor="middle" fill="#92400e" font-size="16" font-weight="bold">General LRT Formula</text>
                    <text x="450" y="400" text-anchor="middle" fill="#92400e" font-size="14">λ = max{L(data; θ) : θ ∈ S₀} / max{L(data; θ) : θ ∈ S}</text>
                    <text x="450" y="420" text-anchor="middle" fill="#92400e" font-size="12">= L₀ / L   (where L is the global maximum likelihood)</text>
                </svg>
            </div>

            <div class="example-box">
                <h3>Common Examples of General LRT</h3>
                <ul>
                    <li><strong>Testing a mean:</strong> H₀: μ = μ₀ vs H₁: μ ≠ μ₀</li>
                    <li><strong>Testing variance:</strong> H₀: σ² = σ₀² vs H₁: σ² ≠ σ₀²</li>
                    <li><strong>Testing regression:</strong> H₀: β₁ = β₂ = 0 vs H₁: at least one βᵢ ≠ 0</li>
                    <li><strong>Goodness of fit:</strong> H₀: data follows specified distribution vs H₁: doesn't follow</li>
                </ul>
            </div>

            <div class="highlight-box">
                <h3>Asymptotic Properties</h3>
                <p>Under regularity conditions and when H₀ is true:</p>
                <div class="formula-box">
                    $$-2\ln(\lambda) \xrightarrow{d} \chi^2_k$$
                </div>
                <p>where $k$ is the difference in the number of free parameters between H₁ and H₀.</p>
                <p>This gives us a way to find critical values for large samples!</p>
                         </div>
         </div>

        <!-- Step-by-Step Procedure Section -->
        <div class="section" id="procedure">
            <h2><span class="step-number">6</span>Complete LRT Procedure: A Practical Guide</h2>
            
            <p>Here's a comprehensive, step-by-step procedure for conducting any likelihood ratio test. Follow these steps systematically for reliable results.</p>

            <div class="definition-box">
                <h3>The Complete LRT Workflow</h3>
                <p>Whether dealing with simple or composite hypotheses, this workflow will guide you through the entire process.</p>
            </div>

            <ol class="step-list">
                <li>
                    <strong>Problem Formulation</strong>
                    <ul>
                        <li>Clearly state your null hypothesis H₀ and alternative hypothesis H₁</li>
                        <li>Identify the parameter(s) of interest: θ</li>
                        <li>Determine if hypotheses are simple (θ = specific value) or composite (θ ∈ range)</li>
                        <li>Choose your significance level α (commonly 0.05 or 0.01)</li>
                    </ul>
                </li>

                <li>
                    <strong>Model Specification</strong>
                    <ul>
                        <li>Specify the probability distribution of your data</li>
                        <li>Write down the likelihood function L(data; θ)</li>
                        <li>Verify that the likelihood is well-defined for all relevant θ values</li>
                    </ul>
                </li>

                <li>
                    <strong>Likelihood Calculation</strong>
                    <div class="highlight-box">
                        <h4>For Simple Hypotheses:</h4>
                        <ul>
                            <li>Calculate L(data; θ₀) under H₀</li>
                            <li>Calculate L(data; θ₁) under H₁</li>
                            <li>Form the ratio: λ = L(data; θ₀) / L(data; θ₁)</li>
                        </ul>
                        
                        <h4>For Composite Hypotheses:</h4>
                        <ul>
                            <li>Find θ̂₀ = argmax{L(data; θ) : θ ∈ S₀}</li>
                            <li>Find θ̂ = argmax{L(data; θ) : θ ∈ S} (global MLE)</li>
                            <li>Form the ratio: λ = L(data; θ̂₀) / L(data; θ̂)</li>
                        </ul>
                    </div>
                </li>

                <li>
                    <strong>Critical Value Determination</strong>
                    <div class="example-box">
                        <h4>Choose Your Method:</h4>
                        <ul>
                            <li><strong>Exact Distribution:</strong> If you know the exact distribution of λ under H₀</li>
                            <li><strong>Asymptotic Approximation:</strong> Use -2ln(λ) ~ χ²ₖ for large samples</li>
                            <li><strong>Simulation:</strong> Use Monte Carlo methods if neither above applies</li>
                            <li><strong>Bootstrap:</strong> Resample your data to estimate the distribution</li>
                        </ul>
                    </div>
                </li>

                <li>
                    <strong>Decision Making</strong>
                    <ul>
                        <li>Compare your calculated λ to the critical value c</li>
                        <li>If λ < c: Reject H₀ (evidence against null hypothesis)</li>
                        <li>If λ ≥ c: Accept H₀ (insufficient evidence against null hypothesis)</li>
                        <li>Report your conclusion with appropriate context</li>
                    </ul>
                </li>

                <li>
                    <strong>Validation and Interpretation</strong>
                    <ul>
                        <li>Check model assumptions (residual analysis, goodness of fit)</li>
                        <li>Consider practical significance vs. statistical significance</li>
                        <li>Calculate and report confidence intervals if appropriate</li>
                        <li>Discuss limitations and potential sources of error</li>
                    </ul>
                </li>
            </ol>

            <div class="visualization">
                <svg width="100%" height="500" viewBox="0 0 1000 500">
                    <!-- Background -->
                    <rect width="1000" height="500" fill="#f8fafc" rx="15"/>
                    
                    <!-- Title -->
                    <text x="500" y="25" text-anchor="middle" fill="#1f2937" font-size="20" font-weight="bold">LRT Decision Framework</text>
                    
                    <!-- Flowchart -->
                    <!-- Start -->
                    <ellipse cx="500" cy="60" rx="80" ry="25" fill="#10b981" stroke="#065f46" stroke-width="2"/>
                    <text x="500" y="65" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Start: Data & Hypotheses</text>
                    
                    <!-- Simple or Composite? -->
                    <polygon points="450,120 550,100 650,120 550,140" fill="#f59e0b" stroke="#92400e" stroke-width="2"/>
                    <text x="550" y="115" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Simple or</text>
                    <text x="550" y="130" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Composite?</text>
                    
                    <!-- Simple path -->
                    <rect x="200" y="180" width="160" height="60" fill="#3b82f6" rx="8" stroke="#1e40af" stroke-width="2"/>
                    <text x="280" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Simple Hypotheses</text>
                    <text x="280" y="220" text-anchor="middle" fill="white" font-size="11">λ = L(θ₀)/L(θ₁)</text>
                    
                    <!-- Composite path -->
                    <rect x="640" y="180" width="160" height="60" fill="#8b5cf6" rx="8" stroke="#7c3aed" stroke-width="2"/>
                    <text x="720" y="200" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Composite Hypotheses</text>
                    <text x="720" y="220" text-anchor="middle" fill="white" font-size="11">λ = L(θ̂₀)/L(θ̂)</text>
                    
                    <!-- Critical value -->
                    <rect x="400" y="280" width="200" height="50" fill="#ef4444" rx="8" stroke="#dc2626" stroke-width="2"/>
                    <text x="500" y="300" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Find Critical Value c</text>
                    <text x="500" y="315" text-anchor="middle" fill="white" font-size="11">Based on α and distribution</text>
                    
                    <!-- Decision -->
                    <polygon points="450,370 550,350 650,370 550,390" fill="#06b6d4" stroke="#0891b2" stroke-width="2"/>
                    <text x="550" y="365" text-anchor="middle" fill="white" font-size="12" font-weight="bold">λ < c ?</text>
                    
                    <!-- Outcomes -->
                    <rect x="250" y="430" width="120" height="40" fill="#dc2626" rx="8" stroke="#991b1b" stroke-width="2"/>
                    <text x="310" y="445" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Reject H₀</text>
                    <text x="310" y="460" text-anchor="middle" fill="white" font-size="10">λ < c</text>
                    
                    <rect x="630" y="430" width="120" height="40" fill="#16a34a" rx="8" stroke="#15803d" stroke-width="2"/>
                    <text x="690" y="445" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Accept H₀</text>
                    <text x="690" y="460" text-anchor="middle" fill="white" font-size="10">λ ≥ c</text>
                    
                    <!-- Arrows -->
                    <defs>
                        <marker id="arrow4" markerWidth="8" markerHeight="6" 
                                refX="8" refY="3" orient="auto">
                            <polygon points="0 0, 8 3, 0 6" fill="#374151" />
                        </marker>
                    </defs>
                    
                    <!-- Flow arrows -->
                    <line x1="500" y1="85" x2="500" y2="95" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="500" y1="145" x2="350" y2="175" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="600" y1="145" x2="650" y2="175" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="280" y1="240" x2="450" y2="275" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="720" y1="240" x2="550" y2="275" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="500" y1="330" x2="500" y2="345" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="500" y1="395" x2="350" y2="425" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    <line x1="600" y1="395" x2="650" y2="425" stroke="#374151" stroke-width="2" marker-end="url(#arrow4)"/>
                    
                    <!-- Labels -->
                    <text x="420" y="165" fill="#374151" font-size="10">Simple</text>
                    <text x="620" y="165" fill="#374151" font-size="10">Composite</text>
                    <text x="400" y="415" fill="#374151" font-size="10">Yes</text>
                    <text x="620" y="415" fill="#374151" font-size="10">No</text>
                    
                    <!-- Side panel with key formulas -->
                    <rect x="20" y="100" width="150" height="350" fill="#fffbeb" rx="10" stroke="#f59e0b" stroke-width="2"/>
                    <text x="95" y="125" text-anchor="middle" fill="#92400e" font-size="14" font-weight="bold">Key Formulas</text>
                    
                    <text x="30" y="150" fill="#92400e" font-size="11" font-weight="bold">Simple LRT:</text>
                    <text x="30" y="165" fill="#92400e" font-size="10">λ = L(θ₀)/L(θ₁)</text>
                    
                    <text x="30" y="190" fill="#92400e" font-size="11" font-weight="bold">General LRT:</text>
                    <text x="30" y="205" fill="#92400e" font-size="10">λ = sup L(θ∈S₀) /</text>
                    <text x="30" y="220" fill="#92400e" font-size="10">     sup L(θ∈S)</text>
                    
                    <text x="30" y="245" fill="#92400e" font-size="11" font-weight="bold">Asymptotic:</text>
                    <text x="30" y="260" fill="#92400e" font-size="10">-2ln(λ) ~ χ²ₖ</text>
                    
                    <text x="30" y="285" fill="#92400e" font-size="11" font-weight="bold">Type I Error:</text>
                    <text x="30" y="300" fill="#92400e" font-size="10">P(Reject H₀|H₀) = α</text>
                    
                    <text x="30" y="325" fill="#92400e" font-size="11" font-weight="bold">Power:</text>
                    <text x="30" y="340" fill="#92400e" font-size="10">P(Reject H₀|H₁)</text>
                    <text x="30" y="355" fill="#92400e" font-size="10">= 1 - β</text>
                    
                    <text x="30" y="380" fill="#92400e" font-size="11" font-weight="bold">p-value:</text>
                    <text x="30" y="395" fill="#92400e" font-size="10">P(λ ≤ λₒᵦₛ|H₀)</text>
                    
                    <text x="30" y="420" fill="#92400e" font-size="11" font-weight="bold">Decision:</text>
                    <text x="30" y="435" fill="#92400e" font-size="10">Reject if λ < c</text>
                </svg>
            </div>

            <div class="highlight-box">
                <h3>Common Pitfalls to Avoid</h3>
                <ul>
                    <li><strong>Model Misspecification:</strong> Ensure your likelihood function correctly represents the data-generating process</li>
                    <li><strong>Sample Size Issues:</strong> Asymptotic approximations may not hold for small samples</li>
                    <li><strong>Multiple Testing:</strong> Adjust significance levels when performing multiple LRTs</li>
                    <li><strong>Assumption Violations:</strong> Check independence, normality, and other distributional assumptions</li>
                    <li><strong>Computational Errors:</strong> Double-check your likelihood calculations and optimization</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Quick Reference: When to Use LRT</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4 style="color: #16a34a;">✅ LRT is Ideal When:</h4>
                        <ul>
                            <li>You have a well-specified likelihood function</li>
                            <li>Need to compare nested models</li>
                            <li>Want optimal properties (under regularity conditions)</li>
                            <li>Have sufficient sample size for asymptotics</li>
                            <li>Dealing with complex parameter spaces</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: #dc2626;">❌ Consider Alternatives When:</h4>
                        <ul>
                            <li>Likelihood is difficult to specify</li>
                            <li>Very small sample sizes</li>
                            <li>Non-nested model comparison needed</li>
                            <li>Computational constraints are severe</li>
                            <li>Non-parametric approach is preferred</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="section" id="summary">
            <h2><span class="step-number">🎯</span>Summary & Key Takeaways</h2>
            
            <p>Congratulations! You've learned one of the most important and versatile tools in statistical inference. Let's recap the essential points.</p>

            <div class="highlight-box">
                <h3>🔑 Key Concepts</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Core Principle</h4>
                        <p>Compare how well competing hypotheses explain the observed data by examining likelihood ratios.</p>
                        
                        <h4>Optimal Properties</h4>
                        <p>LRT has many desirable statistical properties, including asymptotic optimality under regularity conditions.</p>
                    </div>
                    <div>
                        <h4>Broad Applicability</h4>
                        <p>Works for both simple and composite hypotheses across many different statistical models.</p>
                        
                        <h4>Practical Implementation</h4>
                        <p>Can be computed numerically even when exact distributions are unknown.</p>
                    </div>
                </div>
            </div>

            <div class="definition-box">
                <h3>📋 The LRT Framework</h3>
                <div class="formula-box">
                    <h4>Simple Hypotheses</h4>
                    <div class="math-display">
                        $$\lambda = \frac{L(\text{data}; \theta_0)}{L(\text{data}; \theta_1)}$$
                    </div>
                    
                    <h4>Composite Hypotheses</h4>
                    <div class="math-display">
                        $$\lambda = \frac{\max_{\theta \in S_0} L(\text{data}; \theta)}{\max_{\theta \in S} L(\text{data}; \theta)}$$
                    </div>
                    
                    <h4>Asymptotic Distribution</h4>
                    <div class="math-display">
                        $$-2\ln(\lambda) \xrightarrow{d} \chi^2_k \text{ under } H_0$$
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>🚀 Where to Go Next</h3>
                <ul>
                    <li><strong>Practice:</strong> Apply LRT to your own datasets and research problems</li>
                    <li><strong>Extensions:</strong> Learn about Score tests, Wald tests, and other testing procedures</li>
                    <li><strong>Applications:</strong> Explore LRT in regression, time series, and survival analysis</li>
                    <li><strong>Advanced Topics:</strong> Study robust LRT, Bayesian hypothesis testing, and multiple testing</li>
                    <li><strong>Software Implementation:</strong> Master LRT in R, Python, or your preferred statistical software</li>
                </ul>
            </div>

            <div class="visualization">
                <svg width="100%" height="200" viewBox="0 0 800 200">
                    <!-- Background with gradient -->
                    <defs>
                        <linearGradient id="finalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="800" height="200" fill="url(#finalGrad)" rx="15"/>
                    
                    <!-- Central message -->
                    <text x="400" y="80" text-anchor="middle" fill="white" font-size="24" font-weight="bold">
                        Master the LRT: Your Gateway to
                    </text>
                    <text x="400" y="110" text-anchor="middle" fill="white" font-size="24" font-weight="bold">
                        Optimal Statistical Inference
                    </text>
                    
                    <!-- Decorative elements -->
                    <circle cx="100" cy="50" r="3" fill="white" opacity="0.8"/>
                    <circle cx="700" cy="150" r="4" fill="white" opacity="0.6"/>
                    <circle cx="150" cy="150" r="2" fill="white" opacity="0.9"/>
                    <circle cx="650" cy="60" r="5" fill="white" opacity="0.5"/>
                    
                    <!-- Bottom message -->
                    <text x="400" y="150" text-anchor="middle" fill="white" font-size="16" opacity="0.9">
                        Now you have the power to make data-driven decisions with confidence!
                    </text>
                </svg>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div style="text-align: center; padding: 40px; color: white; background: rgba(0,0,0,0.1);">
        <p style="font-size: 14px; opacity: 0.8;">
            This tutorial covered Likelihood Ratio Tests from basic concepts to practical implementation.<br>
            Continue your statistical journey by applying these concepts to real-world problems!
        </p>
    </div>
</body>
</html> 