 A full understanding of what is happening when models are fitted by least squares
 is facilitated by taking a geometric view of the fitting process. Some of the results
 derived in the last few sections become rather obvious when viewed in this way.
 1.4.1 Least squares
 Again consider the linear model,
 µ=Xβ, y∼N(µ,Inσ2),
 where X is an n ×p model matrix. But now consider an n-dimensional Euclidean
 space, ℜn, in which y defines the location of a single point. The space of all possible
 linear combinations of the columns of X defines a subspace of ℜn, the elements of
 this space being given by Xβ, where β can take any value in ℜp: this space will be
 referred to as the space of X (strictly the column space). So, a linear model states
 that µ, the expected value of Y, lies in the space of X. Estimating a linear model
 by least squares, amounts to finding the point, ˆ µ ≡ Xˆ β, in the space of X, that is
 closest to the observed data y. Equivalently, ˆ µ is the orthogonal projection of y on
 to the space of X. An obvious, but important, consequence of this is that the residual
 vector, ˆ ǫ = y − ˆ µ, is orthogonal to all vectors in the space of X.
 Figure 1.4 illustrates these ideas for the simple case of a straight line regression
 model for 3 data (shown conventionally in the left hand panel). The response data
 2
20 LINEARMODELS
 1
 2
 3
 1
 2
 3
 Figure1.5Thegeometryoffittingviaorthogonaldecompositions.Theleftpanel illustrates
 thegeometryof thesimplestraight linemodelof3dataintroducedinfigure1.4.Theright
 handpanelshowshowthisoriginalproblemappearsafterrotationbyQT, thetransposeof
 theorthogonal factor inaQRdecompositionofX.Noticethat intherotatedproblemthe
 model subspaceonlyhasnon-zerocomponentsrelativetopaxes(2axes for thisexample),
 whiletheresidualvectorhasonlyzerocomponentsrelativetothosesameaxes.
 andmodelare
 y=
 
 
 .04
 .41
 .62
 
 andµ=
 
 
 1 0.2
 1 1.0
 1 0.6
 
  β1
 β2
 .
 Sinceβisunknown,themodelsimplysaysthatµcouldbeanylinearcombinationof
 thevectors[1,1,1]Tand[.2,1,.6]T.Astherighthandpaneloffigure1.4illustrates,
 fittingthemodelbyleastsquaresamountstofindingtheparticularlinearcombination
 ofthecolumnsofthesevectorsthatisasclosetoyaspossible(intermsofEuclidean
 distance).
 1.4.2 Fittingbyorthogonaldecompositions
 Recallthattheactualcalculationofleastsquaresestimatesinvolvesfirstformingthe
 QRdecompositionofthemodelmatrix,sothat
 X=Q R
 0 ,
 whereQisann×northogonalmatrixandRisap×puppertriangularmatrix.
 Orthogonalmatricesrotatevectors(withoutchangingtheirlength)andthefirststep
 inleastsquaresestimationistorotateboththeresponsevector,y,andthecolumns
 ofthemodelmatrix,X,inexactlythesameway,bypre-multiplicationwithQT.
 InfacttheQRdecompositionisnotuniquelydefined,inthatthesignofrowsofQ,andcorresponding
 columnsofR,canbeswitched,withoutchangingX—thesesignchangesareequivalent toreflections
THEGEOMETRYOFLINEARMODELLING
 21
 3
 1
 1
 2
 Figure 1.6 The geometry of nested models.
 2
 3
 Figure 1.5 illustrates this rotation for the example shown in figure 1.4. The left
 panel shows the response data and model space, for the original problem, while the
 right hand panel shows the data and model space after rotation by QT. Notice that,
 since the problem has simply been rotated, the relative position of the data and basis
 vectors (columns of X) has not changed. What has changed is that the problem now
 has a particularly convenient orientation relative to the axes. The first two compo
nents of the fitted value vector can now be read directly from axes 1 and 2, while the
 third componentis simply zero. By contrast, the residual vector has zero components
 relative to axes 1 and 2, and its non-zero component can be read directly from axis
 3. In terms of section 1.3.1, these vectors are [fT,0T]T and [0T,rT]T, respectively.
 The ˆ β corresponding to the fitted values is now easily obtained. Of course we
 usually require fitted values and residuals to be expressed in terms of the un-rotated
 problem, but this is simply a matter of reversing the rotation using Q, i.e.,
 ˆ
 µ=Q f
 0 , and ˆ ǫ=Q 0
 r .
 1.4.3 Comparison of nested models
 A linear model with model matrix X0 is nested within a linear model with model
 matrix X1 if they are models for the same response data, and the columns of X0
 span a subspace of the space spanned by the columns of X1. Usually this simply
 means that X1 is X0 with some extra columns added.
 The vector of the differencebetween the fitted values of two nested linear models
 is entirely within the subspace of the larger model, and is therefore orthogonal to the
 residual vector for the larger model. This fact is geometrically obvious, as figure
 of vectors, and the sign leading to maximum numerical stability is usually selected in practice. These
 reflections don’t introduce any extra conceptual difficulty, but can make plots less easy to understand, so
 I have suppressed them in this example.
22
 LINEARMODELS
 1.6 illustrates, but it is a key reason why F-ratio statistics have a relatively simple
 distribution (under the simpler model).
 Figure 1.6 is again based on the same simple straight line model that forms the
 basis for figures 1.4 and 1.5, but this time also illustrates the least squares fit of the
 simplified model
 yi = β0 +ǫi,
 which is nested within the original straight line model. Again, both the original and
 rotated versions of the model and data are shown. This time the fine continuous line
 shows the projection of the response data onto the space of the simpler model, while
 the fine dashed line shows the vector of the difference in fitted values between the
 two models.Notice howthis vectoris orthogonalboth to the reducedmodelsubspace
 and the full model residual vector.
 The right panel of figure 1.6 illustrates that the rotation, using the transpose of
 the orthogonal factor, Q, of the full model matrix, has also lined up the problem
 very conveniently for estimation of the reduced model. The fitted value vector for
 the reduced model now has only one non-zero component, which is the component
 of the rotated response data (•) relative to axis 1. The residual vector has gained
 the component that the fitted value vector has lost, so it has zero component relative
 to axis 1, while its other components are the positions of the rotated response data
 relative to axes 2 and 3.
 So, much of the work required for estimating the simplified model has already
 been done, when estimating the full model. Note, however, that if our interest had
 been in comparing the full model to the model
 yi = β1xi +ǫi,
 then it would have been necessary to reorder the columns of the full model matrix,
 in order to avoid extra work in this way