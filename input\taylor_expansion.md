The Taylor series expansion is a widely used method for approximating a complicated function by a polynomial. <PERSON>’s theorem and its remainder can be expressed in several different forms depending the assumptions one is willing to make.
This page discusses Taylor series results for scalar-valued functions. See also Taylor series for vector-valued functions.
1.	Single variable
1.	Basic form
2.	Lagrange form
3.	Bounds on the remainder
4.	Other forms
2.	Multivariable
1.	Basic form
2.	Lagrange form
3.	Third order
Single variable
Basic form
The most basic statement of <PERSON>’s theorem is as follows:
Theorem (<PERSON>): Suppose n is a positive integer and f:R→R is n times differentiable at a point x0. Then
f(x)=∑k=0nf(k)(x0)k!(x−x0)k+Rn(x,x0),
where the remainder Rn satisfies
Rn(x,x0)=o(|x−x0|n) as x→x0.
This little o form of the remainder is sometimes called the “Peano form” of the remainder.
Lagrange form
Theorem (Taylor): If f(n+1) exists over an open interval containing (x,x0), then there exists x¯∈(x,x0):
Rn(x,x0)=f(n+1)(x¯)(n+1)!(x−x0)n+1.
This is also known as the mean-value form, as the mean value theorem is the central idea in proving the result.
Notes:
•	Depending on where you look, the necessary conditions for the above theorem are sometimes presented in a slightly more stringent form where f(n+1) is required to be continuous, not merely exist. This is not necessary, but makes the proof substantially easier. The conditions can also be loosened slightly since f(n+1) need not exist at the boundary points x and x0, so you sometimes see conditions that look like “If f(n+1) exists on the open interval and f(n) is continuous on the closed interval between x and x0, then…”. I prefer the above form for the sake of simplicity. The same conditions are present in the mean value theorem. For more discussion of this topic, see here.
•	Comparing the Basic and Lagrange forms for a second-order expansion,
(Basic)f(x)=f(x0)+f′(x0)(x−x0)+12f″(x0)(x−x0)2+o(|x−x0|2)(Lagrange)f(x)=f(x0)+f′(x0)(x−x0)+12f″(x¯)(x−x0)2
We can see that in the second case, we have a simpler expression, but to obtain it, we require f″ to exist along the entire interval from x to x0, not just at the point x0.
Bounds on the remainder
A related concept is that if we can bound the derivative over the interval, then we can bound the remainder.
Theorem (Lagrange error bound): If f(n+1) is continuous over an open interval containing (x,x0) and there exists M such that |f(n+1)(a)|≤M for all a∈(x,x0), then
Rn(x,x0)≤M(n+1)!|x−x0|n+1.
Taylor’s theorem can therefore be expressed in both little o and big O forms (keeping in mind that the big O form requires additional assumptions concerning differentiability):
(Basic)f(x)=f(x0)+f′(x0)(x−x0)+12f″(x0)(x−x0)2+o(|x−x0|2)(Big O)f(x)=f(x0)+f′(x0)(x−x0)+12f″(x0)(x−x0)2+O(|x−x0|3)
Other forms
The above forms (basic, Lagrange, and big O) are the most common forms of Taylor’s theorem, although the remainder term can be expressed in several other ways, including the integral form, Cauchy form, and Roche-Schlömilch form. For example, the integral form is given below.
Theorem (Integral form): If f(n+1) is continuous over an open interval containing (x,x0), then
Rn(x,x0)=∫x0xf(n+1)(t)(n+1)!(x−t)ndt.
Multivariable
For the multivariate case, the same concepts apply as they did above, so I’ll provide the theorems without much commentary. Also, I’ll give first- and second-order expansions explicitly rather than abstract formulas involving f(n), since the form of f(n) changes depending on n (scalar, vector, matrix, etc.).
Basic form
Theorem: Suppose f:Rd→R is differentiable at a point x0. Then
f(x)=f(x0)+∇f(x0)⊤(x−x0)+o(‖x−x0‖)
Theorem: Suppose f:Rd→R is twice differentiable at a point x0. Then
f(x)=f(x0)+∇f(x0)⊤(x−x0)+12(x−x0)⊤∇2f(x0)(x−x0)+o(‖x−x0‖2)
Lagrange form
For the theorems below, “x¯ on the line segment connecting x and x0” means that there exists w∈[0,1] such that x¯=wx+(1−w)(x0); I sometimes abbreviate this as x¯∈LS(x,x0) in proofs. The theorems also use neighborhood notation.
Theorem: Suppose f:Rd→R is differentiable on Nr(x0). Then for any x∈Nr(x0), there exists x¯ on the line segment connecting x and x0 such that
f(x)=f(x0)+∇f(x¯)⊤(x−x0)
Theorem: Suppose f:Rd→R is twice differentiable on Nr(x0). Then for any x∈Nr(x0), there exists x¯ on the line segment connecting x and x0 such that
f(x)=f(x0)+∇f(x0)⊤(x−x0)+12(x−x0)⊤∇2f(x¯)(x−x0)
Third order
Lastly, I’ll provide a form that goes out to third order. One could keep going, of course, but higher order terms for multivariate functions become rather cumbersome once they can no longer be represented with vectors and matrices.
Theorem: Suppose f:Rd→R is three times differentiable on Nr(x0). Then for any x∈Nr(x0), there exists x¯ on the line segment connecting x and x0 such that
f(x)=f(x0)+∑j=1d∂f(x0)∂xj(xj−x0j)+12∑j=1d∑k=1d∂2f(x0)∂xj∂xk(xj−x0j)(xk−x0k)+16∑j=1d∑k=1d∑ℓ=1d∂3f(x¯)∂xj∂xk∂xℓ(xj−x0j)(xk−x0k)(xℓ−x0ℓ),
where ∂f(x0)/∂xj is shorthand for ∂f(x)/∂xj evaluated at x0.
Unfortunately, Lagrange-type results do not hold for vector-valued functions. In other words, it is not true that there exists an x¯ such that
f(x)=f(x0)+∇f(x¯)⊤(x−x0);
such a point exists for each element of f separately, but these points will not be the same.
However, big O versions of the Taylor series are still valid, as in the following theorem.
Theorem: Suppose f:Rd→Rk is twice differentiable on Nr(x0), and that ∇2f is bounded on Nr(x0). Then for any x∈Nr(x0),
f(x)=f(x0)+[∇f(x0)+O(‖x−x0‖)]⊤(x−x0),
where O(⋅) applies to each element of the d×k matrix.
Proof: For any individual component j of f, there exists xj¯ on the line segment connecting x and x0 such that
fj(x)=fj(x0)+∇fj(x0)⊤(x−x0)+12(x−x0)⊤∇2fj(x¯j)(x−x0)=fj(x0)+[∇fj(x0)+12∇2fj(x¯j)(x−x0)]⊤(x−x0)=fj(x0)+[∇fj(x0)+O(1)(x−x0)]⊤(x−x0)
because ∇2f is bounded. Stacking these individual equations into a system of equations, we obtain the result stated in the theorem.
In statistics, this theorem is usually applied with respect to the score.

Theorem: Suppose regularity conditions (A)-(C) are met. Then for any consistent estimator θ^, we have
1nu(θ^)=1nu(θ∗)−{I(θ∗)+op(1)}n(θ^−θ∗).
If θ^ is n-consistent, then
1nu(θ^)=1nu(θ∗)−I(θ∗)n(θ^−θ∗)+op(1).
Proof: Taking Taylor series expansions of the contributions to the score vector, we have
ui(θ^)=ui(θ∗)−[Ii(θ∗)+M(xi)O(‖θ^−θ∗‖)]⊤(θ^−θ∗).
Summing these contributions and dividing by n,
1nu(θ^)=1nu(θ∗)−[1nIn(θ∗)+{1n∑i=1nM(xi)}O(‖θ^−θ∗‖)1]⊤n(θ^−θ∗).
Finally, note that
•	1nIn(θ∗)⟶pI(θ∗) by the Fisher information theorem
•	1n∑M(xi)=Op(1) by C(iii)
•	‖θ^−θ∗‖=op(1) because θ^ is consistent
Thus, the rules of O notation tell us that the entire term inside the square brackets is converging to I(θ∗) in probability.
Finally, if θ^ is n-consistent, then n(θ^−θ∗) is Op(1) and op(1)n(θ^−θ∗)=op(1).
Corollary
Similarly, for any two consistent estimators θ^1 and θ^2, we have
1nu(θ^1)=1nu(θ^2)−{I(θ∗)+op(1)}n(θ^1−θ^2).
If both estimators are n-consistent,

