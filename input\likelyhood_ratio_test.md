8.4.5 Likelihood Ratio Tests
So far we have focused on specific examples of hypothesis testing problems. Here, we would like to introduce a relatively general hypothesis testing procedure called the likelihood ratio test. Before doing so, let us quickly review the definition of the likelihood function, which was previously discussed in Section 8.2.3.

Review of the Likelihood Function:
Let X1
, X2
, X3
, ...
, Xn
 be a random sample from a distribution with a parameter θ
. Suppose that we have observed X1=x1
, X2=x2
, ⋯
, Xn=xn
.
- If the Xi
's are discrete, then the likelihood function is defined as
L(x1,x2,⋯,xn;θ)=PX1X2⋯Xn(x1,x2,⋯,xn;θ).
- If the Xi
's are jointly continuous, then the likelihood function is defined as
L(x1,x2,⋯,xn;θ)=fX1X2⋯Xn(x1,x2,⋯,xn;θ).
Likelihood Ratio Tests:
Consider a hypothesis testing problem in which both the null and the alternative hypotheses are simple. That is

 H0
: θ=θ0
,

 H1
: θ=θ1
.

Now, let X1
, X2
, X3
, ...
, Xn
 be a random sample from a distribution with a parameter θ
. Suppose that we have observed X1=x1
, X2=x2
, ⋯
, Xn=xn
. One way to decide between H0
 and H1
 is to compare the corresponding likelihood functions:
l0=L(x1,x2,⋯,xn;θ0),l1=L(x1,x2,⋯,xn;θ1).
More specifically, if l0
 is much larger than l1
, we should accept H0
. On the other hand if l1
 is much larger, we tend to reject H0
. Therefore, we can look at the ratio l0l1
 to decide between H0
 and H1
. This is the idea behind likelihood ratio tests.
Likelihood Ratio Test for Simple Hypotheses

Let X1
, X2
, X3
, ...
, Xn
 be a random sample from a distribution with a parameter θ
. Suppose that we have observed X1=x1
, X2=x2
, ⋯
, Xn=xn
. To decide between two simple hypotheses

 H0
: θ=θ0
,

 H1
: θ=θ1
,

we define
λ(x1,x2,⋯,xn)=L(x1,x2,⋯,xn;θ0)L(x1,x2,⋯,xn;θ1).
To perform a likelihood ratio test (LRT), we choose a constant c
. We reject H0
 if λ<c
 and accept it if λ≥c
. The value of c
 can be chosen based on the desired α
.
Let's look at an example to see how we can perform a likelihood ratio test.

Example 
Here, we look again at the radar problem (Example 8.23). More specifically, we observe the random variable X
:
X=θ+W,
where W∼N(0,σ2=19)
. We need to decide between

 H0
: θ=θ0=0
,

 H1
: θ=θ1=1
.

Let X=x
. Design a level 0.05
 test (α=0.05)
 to decide between H0
 and H1
.
Solution
If θ=θ0=0
, then X∼N(0,σ2=19)
. Therefore,
L(x;θ0)=fX(x;θ0)=32π−−√e−9x22.
On the other hand, if θ=θ1=1
, then X∼N(1,σ2=19)
. Therefore,
L(x;θ1)=fX(x;θ1)=32π−−√e−9(x−1)22.
Therefore,
λ(x)=L(x;θ0)L(x;θ1)=exp{−9x22+9(x−1)22}=exp{9(1−2x)2}.
Thus, we accept H0
 if
exp{9(1−2x)2}≥c,
where c
 is the threshold. Equivalently, we accept H0
 if
x≤12(1−29lnc).
Let us define c′=12(1−29lnc)
, where c′
 is a new threshold. Remember that x
 is the observed value of the random variable X
. Thus, we can summarize the decision rule as follows. We accept H0
 if
X≤c′.
How to do we choose c′
? We use the required α
.
P(type I error)=P(Reject H0|H0)=P(X>c′|H0)=P(X>c′)(where X∼N(0,19))=1−Φ(3c′).
Letting P(type I error)=α
, we obtain
c′=13Φ−1(1−α).
Letting α=0.05
, we obtain
c′=13Φ−1(.95)=0.548
As we see, in this case, the likelihood ratio test is exactly the same test that we obtained in Example 8.23.

How do we perform the likelihood ratio test if the hypotheses are not simple? Suppose that θ
 is an unknown parameter. Let S
 be the set of possible values for θ
 and suppose that we can partition S
 into two disjoint sets S0
 and S1
. Consider the following hypotheses:

 H0
: θ∈S0
,

 H1
: θ∈S1
.

The idea behind the general likelihood ratio test can be explained as follows: We first find the likelihoods corresponding to the most likely values of θ
 in S0
 and S
 respectively. That is, we find
l0l=max{L(x1,x2,⋯,xn;θ):θ∈S0},=max{L(x1,x2,⋯,xn;θ):θ∈S}.
(To be more accurate, we need to replace max
 by sup
.) Let us consider two extreme cases. First, if l0=l
, then we can say that the most likely value of θ
 belongs to S0
. This indicates that we should not reject H0
. On the other hand, if l0l
 is much smaller than 1
, we should probably reject H0
 in favor of H1
. To conduct a likelihood ratio test, we choose a threshold 0≤c≤1
 and compare l0l
 to c
. If l0l≥c
, we accept H0
. If l0l<c
, we reject H0
. The value of c
 can be chosen based on the desired α
.
Likelihood Ratio Tests

Let X1
, X2
, X3
, ...
, Xn
 be a random sample from a distribution with a parameter θ
. Suppose that we have observed X1=x1
, X2=x2
, ⋯
, Xn=xn
. Define
λ(x1,x2,⋯,xn)=sup{L(x1,x2,⋯,xn;θ):θ∈S0}sup{L(x1,x2,⋯,xn;θ):θ∈S}.
To perform a likelihood ratio test (LRT), we choose a constant c
 in [0,1]
. We reject H0
 if λ<c
 and accept it if λ≥c
. The value of c
 can be chosen based on the desired α
.