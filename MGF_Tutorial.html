<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moment Generating Functions: A Complete Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #1976d2;
            --secondary-color: #424242;
            --accent-color: #ff6f00;
            --success-color: #2e7d32;
            --warning-color: #f57c00;
            --error-color: #d32f2f;
            --info-color: #0288d1;
            --light-bg: #f5f5f5;
            --card-bg: #ffffff;
            --text-primary: #212121;
            --text-secondary: #757575;
            --border-color: #e0e0e0;
            --mgf-color: #6a1b9a;
            --moments-color: #388e3c;
            --examples-color: #f57c00;
            --theorem-color: #d32f2f;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #1976d2 0%, #6a1b9a 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--mgf-color), var(--accent-color));
        }

        .header h1 {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: var(--text-secondary);
            font-weight: 300;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            padding: 8px 16px;
            background: var(--mgf-color);
            color: white;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
        }

        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border-left: 5px solid var(--primary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .formula-box {
            background: #f8f9fa;
            border: 2px solid var(--info-color);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(2, 136, 209, 0.1);
        }

        .mgf-box {
            background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
            border: 2px solid var(--mgf-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .mgf-box::before {
            content: '📈';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .moments-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid var(--moments-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .moments-box::before {
            content: '🎯';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .example-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border: 2px solid var(--examples-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .example-box::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .theorem-box {
            background: linear-gradient(135deg, #ffebee 0%, #ef9a9a 100%);
            border: 2px solid var(--theorem-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .theorem-box::before {
            content: '⚖️';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .definition-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid var(--primary-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .definition-box::before {
            content: '📚';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 200px;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            text-decoration: none;
            color: var(--text-secondary);
            border-radius: 5px;
            margin: 5px 0;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .nav-link:hover {
            background: var(--primary-color);
            color: white;
        }

        .math-display {
            font-size: 1.2em;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Moment Generating Functions</h1>
            <p class="subtitle">A Complete Tutorial on MGFs and Their Applications</p>
            <div>
                <span class="badge">Moments</span>
                <span class="badge">Taylor Series</span>
                <span class="badge">Independence</span>
                <span class="badge">Uniqueness</span>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <a href="#introduction" class="nav-link">Introduction</a>
            <a href="#moments" class="nav-link">Moments</a>
            <a href="#mgf-definition" class="nav-link">MGF Definition</a>
            <a href="#finding-moments" class="nav-link">Finding Moments</a>
            <a href="#examples" class="nav-link">Examples</a>
            <a href="#uniqueness" class="nav-link">Uniqueness</a>
            <a href="#sums" class="nav-link">Sums</a>
            <a href="#applications" class="nav-link">Applications</a>
            <a href="#summary" class="nav-link">Summary</a>
        </div>

        <!-- Introduction Section -->
        <div class="section" id="introduction">
            <h2><span class="step-number">1</span>Introduction to Moment Generating Functions</h2>
            
            <p>Moment Generating Functions (MGFs) are powerful tools in probability theory that provide elegant ways to analyze random variables. They serve two main purposes that make them invaluable in statistical analysis.</p>

            <div class="mgf-box">
                <h3>Why are MGFs Important?</h3>
                <p>MGFs are useful for <strong>two fundamental reasons:</strong></p>
                <ol>
                    <li><strong>Moment Generation:</strong> The MGF gives us all moments of a random variable X (hence the name "moment generating function")</li>
                    <li><strong>Distribution Identification:</strong> The MGF uniquely determines the distribution - if two random variables have the same MGF, they have the same distribution</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 1000 400">
                    <defs>
                        <linearGradient id="introGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="400" fill="url(#introGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#1976d2" font-size="20" font-weight="bold">The MGF Universe</text>
                    
                    <!-- Random Variable -->
                    <g>
                        <circle cx="200" cy="150" r="50" fill="#6a1b9a" opacity="0.8"/>
                        <text x="200" y="155" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Random</text>
                        <text x="200" y="170" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Variable X</text>
                        <text x="200" y="220" text-anchor="middle" fill="#6a1b9a" font-size="12" font-weight="bold">Distribution Unknown</text>
                    </g>
                    
                    <!-- Arrow to MGF -->
                    <path d="M 270 150 L 370 150" stroke="#1976d2" stroke-width="4" marker-end="url(#arrowhead1)"/>
                    <text x="320" y="140" text-anchor="middle" fill="#1976d2" font-size="12" font-weight="bold">Calculate</text>
                    
                    <!-- MGF -->
                    <g>
                        <rect x="400" y="100" width="120" height="100" fill="#1976d2" opacity="0.9" rx="10"/>
                        <text x="460" y="125" text-anchor="middle" fill="white" font-size="14" font-weight="bold">MGF</text>
                        <text x="460" y="145" text-anchor="middle" fill="white" font-size="12">M_X(s)</text>
                        <text x="460" y="165" text-anchor="middle" fill="white" font-size="12">= E[e^{sX}]</text>
                        <text x="460" y="185" text-anchor="middle" fill="white" font-size="12">Magic Function!</text>
                    </g>
                    
                    <!-- Arrow to applications -->
                    <path d="M 540 150 L 640 120" stroke="#388e3c" stroke-width="4" marker-end="url(#arrowhead2)"/>
                    <path d="M 540 150 L 640 180" stroke="#f57c00" stroke-width="4" marker-end="url(#arrowhead3)"/>
                    
                    <!-- Applications -->
                    <g>
                        <!-- Moments -->
                        <rect x="670" y="80" width="150" height="60" fill="#388e3c" opacity="0.8" rx="8"/>
                        <text x="745" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">All Moments</text>
                        <text x="745" y="125" text-anchor="middle" fill="white" font-size="10">E[X], E[X²], E[X³], ...</text>
                        
                        <!-- Distribution -->
                        <rect x="670" y="160" width="150" height="60" fill="#f57c00" opacity="0.8" rx="8"/>
                        <text x="745" y="185" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Distribution</text>
                        <text x="745" y="205" text-anchor="middle" fill="white" font-size="10">Unique Identification</text>
                    </g>
                    
                    <!-- Key insight box -->
                    <rect x="100" y="280" width="800" height="100" fill="#ffffff" stroke="#1976d2" stroke-width="2" rx="10"/>
                    <text x="500" y="310" text-anchor="middle" fill="#1976d2" font-size="16" font-weight="bold">Key Insight</text>
                    <text x="500" y="335" text-anchor="middle" fill="#424242" font-size="14">MGFs transform complex probability problems into manageable calculus</text>
                    <text x="500" y="355" text-anchor="middle" fill="#424242" font-size="14">They bridge the gap between abstract distributions and concrete analysis</text>
                    
                    <!-- Arrow marker definitions -->
                    <defs>
                        <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2"/>
                        </marker>
                        <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#388e3c"/>
                        </marker>
                        <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#f57c00"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="definition-box">
                <h3>MGF Applications in Practice</h3>
                <p>MGFs are particularly useful when working with:</p>
                <ul>
                    <li><strong>Sums of independent random variables</strong> - MGFs make these calculations tractable</li>
                    <li><strong>Distribution identification</strong> - When you find an MGF, you've found the distribution</li>
                    <li><strong>Moment calculations</strong> - All moments can be derived from the MGF</li>
                    <li><strong>Limit theorems</strong> - MGFs provide elegant proofs for convergence results</li>
                </ul>
            </div>
        </div>

        <!-- Moments Section -->
        <div class="section" id="moments">
            <h2><span class="step-number">2</span>Mathematical Foundation: Moments</h2>
            
            <p>Before diving into MGFs, we need to understand what "moments" are. Moments are numerical characteristics that describe the shape and properties of a distribution.</p>

            <div class="definition-box">
                <h3>Definition: Moments</h3>
                <p><strong>The $n$-th moment</strong> of a random variable $X$ is defined as:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E[X^n]$$
                    </div>
                </div>
                
                <p><strong>The $n$-th central moment</strong> of $X$ is defined as:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E[(X - E[X])^n]$$
                    </div>
                </div>
            </div>

            <div class="moments-box">
                <h3>Important Specific Moments</h3>
                
                <p><strong>1st Moment:</strong> $E[X]$ = Expected value (mean)</p>
                <p>This tells us the "center" or "average" value of the distribution.</p>
                
                <p><strong>2nd Central Moment:</strong> $E[(X - E[X])^2] = \text{Var}(X)$ = Variance</p>
                <p>This measures the "spread" or "variability" of the distribution.</p>
                
                <p><strong>3rd Central Moment:</strong> Related to skewness (asymmetry)</p>
                <p>This tells us if the distribution is symmetric or leans to one side.</p>
                
                <p><strong>4th Central Moment:</strong> Related to kurtosis (tail heaviness)</p>
                <p>This measures how much probability is in the tails vs. the center.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="450" viewBox="0 0 900 450">
                    <defs>
                        <linearGradient id="momentsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="450" fill="url(#momentsGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#388e3c" font-size="18" font-weight="bold">Moments: What They Tell Us</text>
                    
                    <!-- 1st Moment: Mean -->
                    <g>
                        <rect x="50" y="60" width="180" height="80" fill="#ffffff" stroke="#2e7d32" stroke-width="2" rx="10"/>
                        <text x="140" y="85" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">1st Moment: Mean</text>
                        <text x="140" y="105" text-anchor="middle" fill="#424242" font-size="12">E[X]</text>
                        <text x="140" y="125" text-anchor="middle" fill="#424242" font-size="11">Location/Center</text>
                        
                        <!-- Bell curve with center marked -->
                        <path d="M 70 170 Q 140 150 210 170" stroke="#2e7d32" stroke-width="2" fill="none"/>
                        <line x1="140" y1="150" x2="140" y2="185" stroke="#d32f2f" stroke-width="3"/>
                        <text x="140" y="200" text-anchor="middle" fill="#d32f2f" font-size="10">μ = E[X]</text>
                    </g>
                    
                    <!-- 2nd Moment: Variance -->
                    <g>
                        <rect x="260" y="60" width="180" height="80" fill="#ffffff" stroke="#388e3c" stroke-width="2" rx="10"/>
                        <text x="350" y="85" text-anchor="middle" fill="#388e3c" font-size="14" font-weight="bold">2nd Moment: Variance</text>
                        <text x="350" y="105" text-anchor="middle" fill="#424242" font-size="12">E[(X-μ)²]</text>
                        <text x="350" y="125" text-anchor="middle" fill="#424242" font-size="11">Spread/Variability</text>
                        
                        <!-- Two curves: narrow and wide -->
                        <path d="M 280 160 Q 320 150 350 155 Q 380 150 420 160" stroke="#1976d2" stroke-width="2" fill="none"/>
                        <path d="M 290 175 Q 350 165 410 175" stroke="#ff6f00" stroke-width="2" fill="none"/>
                        <text x="350" y="195" text-anchor="middle" fill="#1976d2" font-size="9">Low Variance</text>
                        <text x="350" y="207" text-anchor="middle" fill="#ff6f00" font-size="9">High Variance</text>
                    </g>
                    
                    <!-- 3rd Moment: Skewness -->
                    <g>
                        <rect x="470" y="60" width="180" height="80" fill="#ffffff" stroke="#4caf50" stroke-width="2" rx="10"/>
                        <text x="560" y="85" text-anchor="middle" fill="#4caf50" font-size="14" font-weight="bold">3rd Moment: Skewness</text>
                        <text x="560" y="105" text-anchor="middle" fill="#424242" font-size="12">E[(X-μ)³]</text>
                        <text x="560" y="125" text-anchor="middle" fill="#424242" font-size="11">Asymmetry</text>
                        
                        <!-- Skewed curve -->
                        <path d="M 490 170 Q 520 155 560 160 Q 600 165 630 175" stroke="#4caf50" stroke-width="2" fill="none"/>
                        <text x="560" y="195" text-anchor="middle" fill="#4caf50" font-size="9">Right Skewed</text>
                    </g>
                    
                    <!-- 4th Moment: Kurtosis -->
                    <g>
                        <rect x="680" y="60" width="180" height="80" fill="#ffffff" stroke="#8bc34a" stroke-width="2" rx="10"/>
                        <text x="770" y="85" text-anchor="middle" fill="#8bc34a" font-size="14" font-weight="bold">4th Moment: Kurtosis</text>
                        <text x="770" y="105" text-anchor="middle" fill="#424242" font-size="12">E[(X-μ)⁴]</text>
                        <text x="770" y="125" text-anchor="middle" fill="#424242" font-size="11">Tail Heaviness</text>
                        
                        <!-- Heavy-tailed vs light-tailed -->
                        <path d="M 700 155 Q 770 145 840 155" stroke="#795548" stroke-width="2" fill="none"/>
                        <path d="M 710 175 Q 770 160 830 175" stroke="#9c27b0" stroke-width="2" fill="none"/>
                        <text x="770" y="195" text-anchor="middle" fill="#795548" font-size="9">Light Tails</text>
                        <text x="770" y="207" text-anchor="middle" fill="#9c27b0" font-size="9">Heavy Tails</text>
                    </g>
                    
                    <!-- Mathematical relationships -->
                    <rect x="100" y="250" width="700" height="160" fill="#ffffff" stroke="#388e3c" stroke-width="3" rx="10"/>
                    <text x="450" y="280" text-anchor="middle" fill="#388e3c" font-size="16" font-weight="bold">Mathematical Relationships</text>
                    
                    <text x="120" y="310" fill="#424242" font-size="13">• 1st Moment: E[X] = μ (population mean)</text>
                    <text x="120" y="330" fill="#424242" font-size="13">• 2nd Raw Moment: E[X²] (used in variance calculation)</text>
                    <text x="120" y="350" fill="#424242" font-size="13">• Variance: Var(X) = E[X²] - (E[X])² = E[X²] - μ²</text>
                    <text x="120" y="370" fill="#424242" font-size="13">• Standard Deviation: σ = √Var(X)</text>
                    <text x="120" y="390" fill="#424242" font-size="13">• Higher moments provide information about distribution shape</text>
                </svg>
            </div>

            <div class="example-box">
                <h3>Moments in Practice: Normal Distribution</h3>
                <p>For a normal distribution $X \sim N(\mu, \sigma^2)$:</p>
                <ul>
                    <li><strong>1st moment:</strong> $E[X] = \mu$</li>
                    <li><strong>2nd central moment:</strong> $\text{Var}(X) = \sigma^2$</li>
                    <li><strong>3rd central moment:</strong> $E[(X-\mu)^3] = 0$ (perfectly symmetric)</li>
                    <li><strong>4th central moment:</strong> $E[(X-\mu)^4] = 3\sigma^4$ (standard kurtosis)</li>
                </ul>
                <p>The normal distribution serves as a baseline for comparing other distributions!</p>
            </div>

            <div class="moments-box">
                <h3>Why Moments Matter for MGFs</h3>
                <p>Moments completely characterize a distribution's properties. The brilliant insight of MGFs is that we can encode <em>all</em> moments into a single function!</p>
                
                <p>Instead of calculating each moment separately:</p>
                <ul>
                    <li>$E[X], E[X^2], E[X^3], E[X^4], \ldots$</li>
                </ul>
                
                <p>We can find one function $M_X(s) = E[e^{sX}]$ that contains all this information and more!</p>
            </div>
        </div>

        <!-- MGF Definition Section -->
        <div class="section" id="mgf-definition">
            <h2><span class="step-number">3</span>MGF Definition and Properties</h2>
            
            <p>Now we're ready to define the Moment Generating Function formally and understand its key properties.</p>

            <div class="definition-box">
                <h3>Definition: Moment Generating Function</h3>
                <p>The <strong>Moment Generating Function (MGF)</strong> of a random variable $X$ is a function $M_X(s)$ defined as:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$M_X(s) = E[e^{sX}]$$
                    </div>
                </div>
                
                <p>We say that the MGF of $X$ <strong>exists</strong> if there exists a positive constant $a$ such that $M_X(s)$ is finite for all $s \in [-a, a]$.</p>
            </div>

            <div class="mgf-box">
                <h3>Breaking Down the Definition</h3>
                <p><strong>$M_X(s) = E[e^{sX}]$</strong></p>
                <ul>
                    <li><strong>$s$:</strong> A real parameter (often called the "dummy variable")</li>
                    <li><strong>$e^{sX}$:</strong> The exponential function applied to $sX$</li>
                    <li><strong>$E[\cdot]$:</strong> Expected value operator</li>
                    <li><strong>Domain:</strong> The function must be finite in some neighborhood around $s=0$</li>
                </ul>
                
                <p><strong>Key insight:</strong> The exponential function $e^{sX}$ serves as a "generating" mechanism that encodes all moments!</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="500" viewBox="0 0 1000 500">
                    <defs>
                        <linearGradient id="mgfGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="500" fill="url(#mgfGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#6a1b9a" font-size="20" font-weight="bold">The MGF: E[e^{sX}] Explained</text>
                    
                    <!-- Discrete Case -->
                    <g>
                        <rect x="50" y="70" width="400" height="180" fill="#ffffff" stroke="#6a1b9a" stroke-width="3" rx="10"/>
                        <text x="250" y="100" text-anchor="middle" fill="#6a1b9a" font-size="16" font-weight="bold">Discrete Case</text>
                        <text x="80" y="130" fill="#424242" font-size="14">For discrete X with PMF P_X(k):</text>
                        <text x="250" y="160" text-anchor="middle" fill="#1976d2" font-size="14">M_X(s) = Σ e^{sk} P_X(k)</text>
                        <text x="80" y="190" fill="#424242" font-size="12">Sum over all possible values k</text>
                        <text x="80" y="210" fill="#424242" font-size="12">Each term: e^{sk} weighted by probability</text>
                        <text x="80" y="230" fill="#424242" font-size="12">Result: Weighted average of exponentials</text>
                    </g>
                    
                    <!-- Continuous Case -->
                    <g>
                        <rect x="550" y="70" width="400" height="180" fill="#ffffff" stroke="#6a1b9a" stroke-width="3" rx="10"/>
                        <text x="750" y="100" text-anchor="middle" fill="#6a1b9a" font-size="16" font-weight="bold">Continuous Case</text>
                        <text x="580" y="130" fill="#424242" font-size="14">For continuous X with PDF f_X(x):</text>
                        <text x="750" y="160" text-anchor="middle" fill="#1976d2" font-size="14">M_X(s) = ∫ e^{sx} f_X(x) dx</text>
                        <text x="580" y="190" fill="#424242" font-size="12">Integral over all x values</text>
                        <text x="580" y="210" fill="#424242" font-size="12">Each point: e^{sx} weighted by density</text>
                        <text x="580" y="230" fill="#424242" font-size="12">Result: Continuous weighted "average"</text>
                    </g>
                    
                    <!-- Why the exponential function? -->
                    <rect x="100" y="280" width="800" height="180" fill="#ffffff" stroke="#d32f2f" stroke-width="3" rx="10"/>
                    <text x="500" y="310" text-anchor="middle" fill="#d32f2f" font-size="18" font-weight="bold">Why the Exponential Function e^{sX}?</text>
                    
                    <text x="130" y="340" fill="#424242" font-size="14">The exponential function has a special property: its Taylor series!</text>
                    <text x="500" y="370" text-anchor="middle" fill="#1976d2" font-size="16">e^{sX} = 1 + sX + (sX)²/2! + (sX)³/3! + (sX)⁴/4! + ...</text>
                    <text x="500" y="395" text-anchor="middle" fill="#1976d2" font-size="16">= 1 + sX + s²X²/2! + s³X³/3! + s⁴X⁴/4! + ...</text>
                    
                    <text x="130" y="425" fill="#424242" font-size="14">When we take the expected value E[e^{sX}], each coefficient becomes a moment!</text>
                    <text x="130" y="445" fill="#424242" font-size="14">This is the key insight that makes MGFs so powerful!</text>
                </svg>
            </div>

            <div class="formula-box">
                <h3>Important Property: MGF at s = 0</h3>
                <p>We always have:</p>
                <div class="math-display">
                    $$M_X(0) = E[e^{0 \cdot X}] = E[e^0] = E[1] = 1$$
                </div>
                <p>This serves as a useful check - every MGF must equal 1 when evaluated at $s = 0$!</p>
            </div>

            <div class="example-box">
                <h3>Simple Example: Discrete Random Variable</h3>
                <p>Let $X$ be a discrete random variable with PMF:</p>
                $$P_X(k) = \begin{cases} 
                \frac{1}{3} & \text{if } k = 1 \\
                \frac{2}{3} & \text{if } k = 2 \\
                0 & \text{otherwise}
                \end{cases}$$
                
                <p><strong>Solution:</strong></p>
                <p>The MGF is:</p>
                $$M_X(s) = E[e^{sX}] = \frac{1}{3}e^{s \cdot 1} + \frac{2}{3}e^{s \cdot 2} = \frac{1}{3}e^s + \frac{2}{3}e^{2s}$$
                
                <p>This is well-defined for all $s \in \mathbb{R}$.</p>
                
                <p><strong>Check:</strong> $M_X(0) = \frac{1}{3}e^0 + \frac{2}{3}e^0 = \frac{1}{3} + \frac{2}{3} = 1$ ✓</p>
            </div>

            <div class="example-box">
                <h3>Simple Example: Continuous Random Variable</h3>
                <p>Let $Y \sim \text{Uniform}(0,1)$ with PDF $f_Y(y) = 1$ for $y \in [0,1]$.</p>
                
                <p><strong>Solution:</strong></p>
                <p>The MGF is:</p>
                $$M_Y(s) = E[e^{sY}] = \int_0^1 e^{sy} \cdot 1 \, dy = \int_0^1 e^{sy} \, dy$$
                
                <p>Evaluating the integral:</p>
                $$M_Y(s) = \left[\frac{e^{sy}}{s}\right]_0^1 = \frac{e^s - e^0}{s} = \frac{e^s - 1}{s}$$
                
                <p><strong>Note:</strong> For $s = 0$, we use L'Hôpital's rule or the fact that $M_Y(0) = 1$.</p>
                
                <p><strong>Check:</strong> $\lim_{s \to 0} \frac{e^s - 1}{s} = 1$ ✓</p>
            </div>

            <div class="mgf-box">
                <h3>Existence of MGFs</h3>
                <p><strong>Important:</strong> Not all random variables have MGFs!</p>
                <ul>
                    <li><strong>Requirements:</strong> $M_X(s)$ must be finite in some neighborhood $[-a, a]$ around $s = 0$</li>
                    <li><strong>Heavy-tailed distributions:</strong> Some distributions (like Cauchy) don't have MGFs</li>
                    <li><strong>Light-tailed distributions:</strong> Normal, exponential, bounded distributions typically have MGFs</li>
                </ul>
                
                <p><strong>When MGF exists:</strong> It contains complete distributional information!</p>
            </div>
        </div>

        <!-- Finding Moments Section -->
        <div class="section" id="finding-moments">
            <h2><span class="step-number">4</span>Finding Moments from MGFs</h2>
            
            <p>Now we'll see the magic of MGFs: how they encode all moments and how to extract them using calculus.</p>

            <div class="moments-box">
                <h3>The Taylor Series Connection</h3>
                <p>Remember the Taylor series for the exponential function:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$e^x = \sum_{k=0}^{\infty} \frac{x^k}{k!} = 1 + x + \frac{x^2}{2!} + \frac{x^3}{3!} + \frac{x^4}{4!} + \cdots$$
                    </div>
                </div>
                
                <p>Applying this to $e^{sX}$:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$e^{sX} = \sum_{k=0}^{\infty} \frac{(sX)^k}{k!} = \sum_{k=0}^{\infty} \frac{X^k s^k}{k!}$$
                    </div>
                </div>
            </div>

            <div class="mgf-box">
                <h3>The Moment Extraction Formula</h3>
                <p>Taking the expected value of both sides:</p>
                $$M_X(s) = E[e^{sX}] = E\left[\sum_{k=0}^{\infty} \frac{X^k s^k}{k!}\right] = \sum_{k=0}^{\infty} \frac{E[X^k] s^k}{k!}$$
                
                <p>This shows that $M_X(s)$ is the Taylor series whose coefficients contain the moments!</p>
                
                <p><strong>Key Insight:</strong> The coefficient of $\frac{s^k}{k!}$ in the Taylor expansion of $M_X(s)$ is $E[X^k]$!</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 1000 400">
                    <defs>
                        <linearGradient id="taylorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:0.7" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="400" fill="url(#taylorGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#f57c00" font-size="20" font-weight="bold">Taylor Series Magic: Extracting Moments</text>
                    
                    <!-- Taylor series expansion -->
                    <text x="500" y="70" text-anchor="middle" fill="#1976d2" font-size="16">M_X(s) = E[1] + E[X]s + E[X²]s²/2! + E[X³]s³/3! + E[X⁴]s⁴/4! + ...</text>
                    
                    <!-- Moment extraction boxes -->
                    <g>
                        <!-- 0th moment -->
                        <rect x="80" y="120" width="160" height="80" fill="#ffffff" stroke="#2e7d32" stroke-width="2" rx="8"/>
                        <text x="160" y="145" text-anchor="middle" fill="#2e7d32" font-size="12" font-weight="bold">0th Moment</text>
                        <text x="160" y="165" text-anchor="middle" fill="#424242" font-size="11">Coefficient of s⁰</text>
                        <text x="160" y="185" text-anchor="middle" fill="#1976d2" font-size="12">E[X⁰] = E[1] = 1</text>
                        
                        <!-- 1st moment -->
                        <rect x="260" y="120" width="160" height="80" fill="#ffffff" stroke="#388e3c" stroke-width="2" rx="8"/>
                        <text x="340" y="145" text-anchor="middle" fill="#388e3c" font-size="12" font-weight="bold">1st Moment</text>
                        <text x="340" y="165" text-anchor="middle" fill="#424242" font-size="11">Coefficient of s¹</text>
                        <text x="340" y="185" text-anchor="middle" fill="#1976d2" font-size="12">E[X]</text>
                        
                        <!-- 2nd moment -->
                        <rect x="440" y="120" width="160" height="80" fill="#ffffff" stroke="#4caf50" stroke-width="2" rx="8"/>
                        <text x="520" y="145" text-anchor="middle" fill="#4caf50" font-size="12" font-weight="bold">2nd Moment</text>
                        <text x="520" y="165" text-anchor="middle" fill="#424242" font-size="11">Coefficient of s²/2!</text>
                        <text x="520" y="185" text-anchor="middle" fill="#1976d2" font-size="12">E[X²]</text>
                        
                        <!-- 3rd moment -->
                        <rect x="620" y="120" width="160" height="80" fill="#ffffff" stroke="#8bc34a" stroke-width="2" rx="8"/>
                        <text x="700" y="145" text-anchor="middle" fill="#8bc34a" font-size="12" font-weight="bold">3rd Moment</text>
                        <text x="700" y="165" text-anchor="middle" fill="#424242" font-size="11">Coefficient of s³/3!</text>
                        <text x="700" y="185" text-anchor="middle" fill="#1976d2" font-size="12">E[X³]</text>
                        
                        <!-- nth moment -->
                        <rect x="800" y="120" width="160" height="80" fill="#ffffff" stroke="#cddc39" stroke-width="2" rx="8"/>
                        <text x="880" y="145" text-anchor="middle" fill="#827717" font-size="12" font-weight="bold">nth Moment</text>
                        <text x="880" y="165" text-anchor="middle" fill="#424242" font-size="11">Coefficient of sⁿ/n!</text>
                        <text x="880" y="185" text-anchor="middle" fill="#1976d2" font-size="12">E[Xⁿ]</text>
                    </g>
                    
                    <!-- Derivative method -->
                    <rect x="100" y="230" width="800" height="140" fill="#ffffff" stroke="#f57c00" stroke-width="3" rx="10"/>
                    <text x="500" y="260" text-anchor="middle" fill="#f57c00" font-size="18" font-weight="bold">Practical Method: Derivatives!</text>
                    
                    <text x="130" y="290" fill="#424242" font-size="14">The coefficient of sᵏ/k! in the Taylor series is the kth derivative evaluated at s=0:</text>
                    <text x="500" y="320" text-anchor="middle" fill="#1976d2" font-size="16">E[Xᵏ] = d^k/ds^k M_X(s)|_{s=0}</text>
                    <text x="130" y="350" fill="#424242" font-size="14">This gives us a systematic way to find any moment from the MGF!</text>
                </svg>
            </div>

            <div class="formula-box">
                <h3>The General Moment Formula</h3>
                <p>For any positive integer $k$, the $k$-th moment of $X$ is:</p>
                <div class="math-display">
                    $$E[X^k] = \frac{d^k}{ds^k} M_X(s) \bigg|_{s=0}$$
                </div>
                <p>This formula is the key to extracting moments from MGFs!</p>
            </div>

            <div class="example-box">
                <h3>Example: Finding Moments of Uniform(0,1)</h3>
                <p>We found earlier that for $Y \sim \text{Uniform}(0,1)$:</p>
                $$M_Y(s) = \frac{e^s - 1}{s} \text{ for } s \neq 0, \text{ and } M_Y(0) = 1$$
                
                <p><strong>Finding E[Y]:</strong></p>
                <p>We need $\frac{d}{ds} M_Y(s) \big|_{s=0}$. Using the quotient rule:</p>
                $$\frac{d}{ds} M_Y(s) = \frac{d}{ds} \left(\frac{e^s - 1}{s}\right) = \frac{se^s - (e^s - 1)}{s^2} = \frac{se^s - e^s + 1}{s^2}$$
                
                <p>At $s = 0$, we need L'Hôpital's rule or Taylor expansion. The result is:</p>
                $$E[Y] = \frac{d}{ds} M_Y(s) \bigg|_{s=0} = \frac{1}{2}$$
                
                <p><strong>Finding E[Y²]:</strong></p>
                <p>Using the second derivative (calculation omitted for brevity):</p>
                $$E[Y^2] = \frac{d^2}{ds^2} M_Y(s) \bigg|_{s=0} = \frac{1}{3}$$
                
                <p><strong>Verification:</strong> For Uniform(0,1), we know $E[Y] = \frac{1}{2}$ and $E[Y^2] = \frac{1}{3}$ directly from integration. ✓</p>
            </div>

            <div class="example-box">
                <h3>Example: Exponential Distribution</h3>
                <p>Let $X \sim \text{Exponential}(\lambda)$ with PDF $f_X(x) = \lambda e^{-\lambda x}$ for $x \geq 0$.</p>
                
                <p><strong>Step 1: Find the MGF</strong></p>
                $$M_X(s) = E[e^{sX}] = \int_0^{\infty} e^{sx} \lambda e^{-\lambda x} dx = \lambda \int_0^{\infty} e^{(s-\lambda)x} dx$$
                
                <p>For $s < \lambda$:</p>
                $$M_X(s) = \lambda \left[\frac{e^{(s-\lambda)x}}{s-\lambda}\right]_0^{\infty} = \lambda \cdot \frac{0 - 1}{s-\lambda} = \frac{\lambda}{\lambda - s}$$
                
                <p><strong>Step 2: Find moments using derivatives</strong></p>
                <p>First moment (mean):</p>
                $$\frac{d}{ds} M_X(s) = \frac{d}{ds} \left(\frac{\lambda}{\lambda - s}\right) = \frac{\lambda}{(\lambda - s)^2}$$
                $$E[X] = \frac{\lambda}{(\lambda - 0)^2} = \frac{\lambda}{\lambda^2} = \frac{1}{\lambda}$$
                
                <p>Second moment:</p>
                $$\frac{d^2}{ds^2} M_X(s) = \frac{2\lambda}{(\lambda - s)^3}$$
                $$E[X^2] = \frac{2\lambda}{(\lambda - 0)^3} = \frac{2\lambda}{\lambda^3} = \frac{2}{\lambda^2}$$
                
                <p><strong>Variance:</strong> $\text{Var}(X) = E[X^2] - (E[X])^2 = \frac{2}{\lambda^2} - \left(\frac{1}{\lambda}\right)^2 = \frac{1}{\lambda^2}$ ✓</p>
            </div>

            <div class="moments-box">
                <h3>Why This Method is Powerful</h3>
                <ul>
                    <li><strong>Systematic:</strong> Once you have the MGF, all moments follow from derivatives</li>
                    <li><strong>Unified approach:</strong> Same method works for discrete and continuous distributions</li>
                    <li><strong>Complete information:</strong> MGF contains all moments, so it characterizes the distribution</li>
                    <li><strong>Computational advantage:</strong> Often easier than direct integration/summation for moments</li>
                </ul>
            </div>
        </div>

        <!-- Examples Section -->
        <div class="section" id="examples">
            <h2><span class="step-number">5</span>Worked Examples</h2>
            
            <p>Let's work through complete examples to solidify our understanding of MGFs.</p>

            <div class="example-box">
                <h3>Example: Poisson Distribution</h3>
                <p>Let $X \sim \text{Poisson}(\lambda)$ with PMF $P_X(k) = \frac{\lambda^k e^{-\lambda}}{k!}$ for $k = 0, 1, 2, \ldots$</p>
                
                <p><strong>Finding the MGF:</strong></p>
                $$M_X(s) = E[e^{sX}] = \sum_{k=0}^{\infty} e^{sk} \cdot \frac{\lambda^k e^{-\lambda}}{k!}$$
                $$= e^{-\lambda} \sum_{k=0}^{\infty} \frac{(e^s \lambda)^k}{k!} = e^{-\lambda} e^{\lambda e^s} = e^{\lambda(e^s - 1)}$$
                
                <p><strong>Key insight:</strong> We used the fact that $\sum_{k=0}^{\infty} \frac{x^k}{k!} = e^x$</p>
                
                <p><strong>Verification:</strong> $M_X(0) = e^{\lambda(e^0 - 1)} = e^{\lambda(1-1)} = e^0 = 1$ ✓</p>
                
                <p><strong>Finding moments:</strong></p>
                <p>First derivative: $\frac{d}{ds} M_X(s) = e^{\lambda(e^s - 1)} \cdot \lambda e^s$</p>
                <p>At $s = 0$: $E[X] = e^0 \cdot \lambda \cdot e^0 = \lambda$ ✓</p>
                
                <p>Second derivative: $\frac{d^2}{ds^2} M_X(s) = \lambda e^s \left[\lambda e^s e^{\lambda(e^s - 1)} + e^{\lambda(e^s - 1)}\right]$</p>
                <p>At $s = 0$: $E[X^2] = \lambda(\lambda + 1) = \lambda^2 + \lambda$</p>
                <p>Therefore: $\text{Var}(X) = E[X^2] - (E[X])^2 = \lambda^2 + \lambda - \lambda^2 = \lambda$ ✓</p>
            </div>

            <div class="example-box">
                <h3>Practice Problem: Quadratic Transformation</h3>
                <p><strong>Problem:</strong> If $U \sim \text{Uniform}(0,1)$, find the distribution of $Y = U^2$.</p>
                
                <p><strong>Traditional Method (for comparison):</strong></p>
                <p>Using CDF transformation: For $0 \leq y \leq 1$:</p>
                $$F_Y(y) = P(Y \leq y) = P(U^2 \leq y) = P(U \leq \sqrt{y}) = \sqrt{y}$$
                $$f_Y(y) = \frac{d}{dy} F_Y(y) = \frac{1}{2\sqrt{y}}$$
                
                <p><strong>MGF Method:</strong></p>
                <p>For $U \sim \text{Uniform}(0,1)$, we know $M_U(s) = \frac{e^s - 1}{s}$</p>
                <p>For $Y = U^2$: $M_Y(s) = E[e^{sY}] = E[e^{sU^2}]$</p>
                
                <p>This requires numerical integration in general, but the MGF approach shows the systematic nature of the method.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="350" viewBox="0 0 1000 350">
                    <defs>
                        <linearGradient id="exampleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:0.6" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="350" fill="url(#exampleGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#f57c00" font-size="18" font-weight="bold">Common Distribution MGFs</text>
                    
                    <!-- Distribution boxes -->
                    <g>
                        <!-- Normal -->
                        <rect x="50" y="60" width="200" height="100" fill="#ffffff" stroke="#1976d2" stroke-width="2" rx="8"/>
                        <text x="150" y="85" text-anchor="middle" fill="#1976d2" font-size="14" font-weight="bold">Normal(μ, σ²)</text>
                        <text x="150" y="110" text-anchor="middle" fill="#424242" font-size="12">M_X(s) = e^{μs + σ²s²/2}</text>
                        <text x="150" y="130" text-anchor="middle" fill="#424242" font-size="11">Mean: μ, Var: σ²</text>
                        <text x="150" y="145" text-anchor="middle" fill="#424242" font-size="11">Symmetric, bell-shaped</text>
                        
                        <!-- Exponential -->
                        <rect x="270" y="60" width="200" height="100" fill="#ffffff" stroke="#388e3c" stroke-width="2" rx="8"/>
                        <text x="370" y="85" text-anchor="middle" fill="#388e3c" font-size="14" font-weight="bold">Exponential(λ)</text>
                        <text x="370" y="110" text-anchor="middle" fill="#424242" font-size="12">M_X(s) = λ/(λ-s)</text>
                        <text x="370" y="130" text-anchor="middle" fill="#424242" font-size="11">s < λ</text>
                        <text x="370" y="145" text-anchor="middle" fill="#424242" font-size="11">Mean: 1/λ, Var: 1/λ²</text>
                        
                        <!-- Poisson -->
                        <rect x="490" y="60" width="200" height="100" fill="#ffffff" stroke="#ff6f00" stroke-width="2" rx="8"/>
                        <text x="590" y="85" text-anchor="middle" fill="#ff6f00" font-size="14" font-weight="bold">Poisson(λ)</text>
                        <text x="590" y="110" text-anchor="middle" fill="#424242" font-size="12">M_X(s) = e^{λ(e^s - 1)}</text>
                        <text x="590" y="130" text-anchor="middle" fill="#424242" font-size="11">Mean: λ, Var: λ</text>
                        <text x="590" y="145" text-anchor="middle" fill="#424242" font-size="11">Discrete, counts</text>
                        
                        <!-- Binomial -->
                        <rect x="710" y="60" width="200" height="100" fill="#ffffff" stroke="#9c27b0" stroke-width="2" rx="8"/>
                        <text x="810" y="85" text-anchor="middle" fill="#9c27b0" font-size="14" font-weight="bold">Binomial(n,p)</text>
                        <text x="810" y="110" text-anchor="middle" fill="#424242" font-size="12">M_X(s) = (1-p+pe^s)^n</text>
                        <text x="810" y="130" text-anchor="middle" fill="#424242" font-size="11">Mean: np, Var: np(1-p)</text>
                        <text x="810" y="145" text-anchor="middle" fill="#424242" font-size="11">Discrete, trials</text>
                    </g>
                    
                    <!-- Pattern recognition -->
                    <rect x="100" y="190" width="800" height="130" fill="#ffffff" stroke="#f57c00" stroke-width="3" rx="10"/>
                    <text x="500" y="220" text-anchor="middle" fill="#f57c00" font-size="16" font-weight="bold">Pattern Recognition in MGFs</text>
                    
                    <text x="130" y="250" fill="#424242" font-size="13">• Exponential family: MGFs often have exponential form</text>
                    <text x="130" y="270" fill="#424242" font-size="13">• Parameters appear as coefficients in the exponent</text>
                    <text x="130" y="290" fill="#424242" font-size="13">• Mean and variance can often be read directly from MGF structure</text>
                    <text x="130" y="310" fill="#424242" font-size="13">• Similar distributions have similar MGF forms</text>
                </svg>
            </div>
        </div>

        <!-- Uniqueness Theorem Section -->
        <div class="section" id="uniqueness">
            <h2><span class="step-number">6</span>The Uniqueness Theorem</h2>
            
            <p>One of the most important properties of MGFs is that they uniquely determine distributions.</p>

            <div class="theorem-box">
                <h3>Uniqueness Theorem</h3>
                <p><strong>Theorem:</strong> Consider two random variables $X$ and $Y$. Suppose that there exists a positive constant $c$ such that the MGFs of $X$ and $Y$ are finite and identical for all values of $s$ in $[-c, c]$. Then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$F_X(t) = F_Y(t) \text{ for all } t \in \mathbb{R}$$
                    </div>
                </div>
                <p>In other words, $X$ and $Y$ have the same distribution.</p>
            </div>

            <div class="mgf-box">
                <h3>What This Means in Practice</h3>
                <ul>
                    <li><strong>Distribution identification:</strong> If you can find the MGF of a random variable, you've identified its distribution</li>
                    <li><strong>Proof technique:</strong> To show two random variables have the same distribution, show their MGFs are equal</li>
                    <li><strong>Reverse engineering:</strong> Given an MGF, you can determine what distribution it came from</li>
                    <li><strong>Uniqueness guarantee:</strong> There's exactly one distribution for each MGF</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Example: Distribution Identification</h3>
                <p><strong>Problem:</strong> For a random variable $X$, we know that:</p>
                $$M_X(s) = \frac{2}{2-s} \text{ for } s \in (-2, 2)$$
                <p>Find the distribution of $X$.</p>
                
                <p><strong>Solution:</strong></p>
                <p>We can rewrite this as:</p>
                $$M_X(s) = \frac{2}{2-s} = \frac{\lambda}{\lambda - s} \text{ where } \lambda = 2$$
                
                <p>This is the MGF of an exponential distribution with parameter $\lambda = 2$!</p>
                
                <p><strong>Answer:</strong> $X \sim \text{Exponential}(2)$</p>
                
                <p><strong>Verification:</strong></p>
                <ul>
                    <li>Mean: $E[X] = \frac{1}{\lambda} = \frac{1}{2}$</li>
                    <li>Variance: $\text{Var}(X) = \frac{1}{\lambda^2} = \frac{1}{4}$</li>
                    <li>PDF: $f_X(x) = 2e^{-2x}$ for $x \geq 0$</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="350" viewBox="0 0 1000 350">
                    <defs>
                        <linearGradient id="uniquenessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ef9a9a;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="350" fill="url(#uniquenessGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#d32f2f" font-size="18" font-weight="bold">Uniqueness Theorem: One-to-One Correspondence</text>
                    
                    <!-- Distribution to MGF mapping -->
                    <g>
                        <!-- Distributions -->
                        <rect x="80" y="70" width="200" height="200" fill="#ffffff" stroke="#d32f2f" stroke-width="3" rx="10"/>
                        <text x="180" y="100" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Distributions</text>
                        
                        <circle cx="120" cy="140" r="15" fill="#1976d2"/>
                        <text x="145" y="145" fill="#424242" font-size="11">Normal(0,1)</text>
                        
                        <circle cx="120" cy="170" r="15" fill="#388e3c"/>
                        <text x="145" y="175" fill="#424242" font-size="11">Exponential(1)</text>
                        
                        <circle cx="120" cy="200" r="15" fill="#ff6f00"/>
                        <text x="145" y="205" fill="#424242" font-size="11">Poisson(3)</text>
                        
                        <circle cx="120" cy="230" r="15" fill="#9c27b0"/>
                        <text x="145" y="235" fill="#424242" font-size="11">Binomial(5,0.3)</text>
                        
                        <!-- MGFs -->
                        <rect x="720" y="70" width="200" height="200" fill="#ffffff" stroke="#d32f2f" stroke-width="3" rx="10"/>
                        <text x="820" y="100" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">MGFs</text>
                        
                        <circle cx="880" cy="140" r="15" fill="#1976d2"/>
                        <text x="760" y="145" fill="#424242" font-size="11">e^{s²/2}</text>
                        
                        <circle cx="880" cy="170" r="15" fill="#388e3c"/>
                        <text x="760" y="175" fill="#424242" font-size="11">1/(1-s)</text>
                        
                        <circle cx="880" cy="200" r="15" fill="#ff6f00"/>
                        <text x="760" y="205" fill="#424242" font-size="11">e^{3(e^s-1)}</text>
                        
                        <circle cx="880" cy="230" r="15" fill="#9c27b0"/>
                        <text x="760" y="235" fill="#424242" font-size="11">(0.7+0.3e^s)^5</text>
                    </g>
                    
                    <!-- Arrows showing one-to-one mapping -->
                    <path d="M 290 140 L 710 140" stroke="#1976d2" stroke-width="3" marker-end="url(#arrow1)"/>
                    <path d="M 290 170 L 710 170" stroke="#388e3c" stroke-width="3" marker-end="url(#arrow2)"/>
                    <path d="M 290 200 L 710 200" stroke="#ff6f00" stroke-width="3" marker-end="url(#arrow3)"/>
                    <path d="M 290 230 L 710 230" stroke="#9c27b0" stroke-width="3" marker-end="url(#arrow4)"/>
                    
                    <!-- Central message -->
                    <rect x="350" y="150" width="300" height="60" fill="#ffffff" stroke="#d32f2f" stroke-width="2" rx="10"/>
                    <text x="500" y="175" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">One-to-One</text>
                    <text x="500" y="195" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Correspondence</text>
                    
                    <text x="500" y="310" text-anchor="middle" fill="#424242" font-size="14">Each distribution has exactly one MGF, and each MGF corresponds to exactly one distribution</text>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrow1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#1976d2"/>
                        </marker>
                        <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#388e3c"/>
                        </marker>
                        <marker id="arrow3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#ff6f00"/>
                        </marker>
                        <marker id="arrow4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#9c27b0"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="definition-box">
                <h3>Practical Implications</h3>
                <p>The uniqueness theorem has several important consequences:</p>
                <ol>
                    <li><strong>Method of MGFs:</strong> To find the distribution of a random variable, find its MGF and match it to a known MGF</li>
                    <li><strong>Proving distributional equality:</strong> Show that $M_X(s) = M_Y(s)$ to prove $X$ and $Y$ have the same distribution</li>
                    <li><strong>Confidence in identification:</strong> Once you've matched an MGF, you know the distribution with certainty</li>
                    <li><strong>Foundation for convergence theorems:</strong> Convergence of MGFs implies convergence in distribution</li>
                </ol>
            </div>
        </div>

        <!-- Sums of Independent Random Variables Section -->
        <div class="section" id="sums">
            <h2><span class="step-number">7</span>Sums of Independent Random Variables</h2>
            
            <p>One of the most powerful applications of MGFs is analyzing sums of independent random variables.</p>

            <div class="mgf-box">
                <h3>The Fundamental Property</h3>
                <p>If $X_1, X_2, \ldots, X_n$ are $n$ independent random variables, and $Y = X_1 + X_2 + \cdots + X_n$, then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$M_Y(s) = M_{X_1}(s) \cdot M_{X_2}(s) \cdots M_{X_n}(s)$$
                    </div>
                </div>
                <p><strong>Key insight:</strong> The MGF of a sum of independent variables is the product of their individual MGFs!</p>
            </div>

            <div class="moments-box">
                <h3>Proof Sketch</h3>
                <p>Starting with the definition:</p>
                $$M_Y(s) = E[e^{sY}] = E[e^{s(X_1 + X_2 + \cdots + X_n)}] = E[e^{sX_1} \cdot e^{sX_2} \cdots e^{sX_n}]$$
                
                <p>Since the $X_i$ are independent, the random variables $e^{sX_i}$ are also independent, so:</p>
                $$M_Y(s) = E[e^{sX_1}] \cdot E[e^{sX_2}] \cdots E[e^{sX_n}] = M_{X_1}(s) \cdot M_{X_2}(s) \cdots M_{X_n}(s)$$
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 1000 400">
                    <defs>
                        <linearGradient id="sumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="400" fill="url(#sumGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#1976d2" font-size="18" font-weight="bold">MGFs Transform Addition to Multiplication</text>
                    
                    <!-- Independent RVs -->
                    <g>
                        <circle cx="120" cy="100" r="30" fill="#388e3c" opacity="0.8"/>
                        <text x="120" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X₁</text>
                        <text x="120" y="140" text-anchor="middle" fill="#424242" font-size="10">M_{X₁}(s)</text>
                        
                        <circle cx="220" cy="100" r="30" fill="#f57c00" opacity="0.8"/>
                        <text x="220" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X₂</text>
                        <text x="220" y="140" text-anchor="middle" fill="#424242" font-size="10">M_{X₂}(s)</text>
                        
                        <text x="270" y="105" text-anchor="middle" fill="#424242" font-size="14">...</text>
                        
                        <circle cx="320" cy="100" r="30" fill="#9c27b0" opacity="0.8"/>
                        <text x="320" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Xₙ</text>
                        <text x="320" y="140" text-anchor="middle" fill="#424242" font-size="10">M_{Xₙ}(s)</text>
                    </g>
                    
                    <!-- Addition arrows -->
                    <path d="M 150 170 L 210 170" stroke="#1976d2" stroke-width="3"/>
                    <text x="180" y="165" text-anchor="middle" fill="#1976d2" font-size="14">+</text>
                    <path d="M 230 170 L 290 170" stroke="#1976d2" stroke-width="3"/>
                    <text x="260" y="165" fill="#1976d2" font-size="14">+ ... +</text>
                    
                    <!-- Sum result -->
                    <circle cx="400" cy="180" r="40" fill="#1976d2" opacity="0.8"/>
                    <text x="400" y="180" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Y = Σ Xᵢ</text>
                    <text x="400" y="230" text-anchor="middle" fill="#424242" font-size="12">M_Y(s) = ?</text>
                    
                    <!-- MGF transformation -->
                    <path d="M 460 180 L 540 180" stroke="#d32f2f" stroke-width="4" marker-end="url(#magicArrow)"/>
                    <text x="500" y="170" text-anchor="middle" fill="#d32f2f" font-size="12" font-weight="bold">MGF Magic!</text>
                    
                    <!-- Product result -->
                    <rect x="570" y="160" width="300" height="120" fill="#ffffff" stroke="#d32f2f" stroke-width="3" rx="10"/>
                    <text x="720" y="190" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Product of MGFs</text>
                    <text x="720" y="215" text-anchor="middle" fill="#1976d2" font-size="13">M_Y(s) = M_{X₁}(s) × M_{X₂}(s) × ... × M_{Xₙ}(s)</text>
                    <text x="720" y="240" text-anchor="middle" fill="#424242" font-size="11">Addition becomes multiplication!</text>
                    <text x="720" y="260" text-anchor="middle" fill="#424242" font-size="11">Much easier to compute!</text>
                    
                    <!-- Key insight box -->
                    <rect x="100" y="320" width="800" height="60" fill="#ffffff" stroke="#1976d2" stroke-width="2" rx="8"/>
                    <text x="500" y="345" text-anchor="middle" fill="#1976d2" font-size="14" font-weight="bold">Key Insight: Complex probability problems become simple algebra!</text>
                    <text x="500" y="365" text-anchor="middle" fill="#424242" font-size="12">This property makes MGFs incredibly powerful for analyzing sums</text>
                    
                    <defs>
                        <marker id="magicArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
                            <polygon points="0 0, 12 4, 0 8" fill="#d32f2f"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="example-box">
                <h3>Example: Binomial Distribution from Bernoulli</h3>
                <p>Let's derive the MGF of $X \sim \text{Binomial}(n,p)$ by viewing it as a sum of independent Bernoulli trials.</p>
                
                <p><strong>Step 1:</strong> Express as a sum</p>
                <p>If $X_1, X_2, \ldots, X_n$ are i.i.d. $\text{Bernoulli}(p)$, then $X = X_1 + X_2 + \cdots + X_n \sim \text{Binomial}(n,p)$.</p>
                
                <p><strong>Step 2:</strong> Find MGF of a single Bernoulli trial</p>
                <p>For $X_i \sim \text{Bernoulli}(p)$:</p>
                $$M_{X_i}(s) = E[e^{sX_i}] = e^{s \cdot 0} \cdot (1-p) + e^{s \cdot 1} \cdot p = (1-p) + pe^s$$
                
                <p><strong>Step 3:</strong> Use the product property</p>
                $$M_X(s) = \prod_{i=1}^n M_{X_i}(s) = \prod_{i=1}^n (1-p + pe^s) = (1-p + pe^s)^n$$
                
                <p><strong>Result:</strong> $M_X(s) = (1-p + pe^s)^n$ for $X \sim \text{Binomial}(n,p)$ ✓</p>
            </div>

            <div class="example-box">
                <h3>Example: Proving Poisson Addition Property</h3>
                <p><strong>Problem:</strong> Show that if $X \sim \text{Poisson}(\lambda_1)$ and $Y \sim \text{Poisson}(\lambda_2)$ are independent, then $X + Y \sim \text{Poisson}(\lambda_1 + \lambda_2)$.</p>
                
                <p><strong>Solution using MGFs:</strong></p>
                
                <p><strong>Step 1:</strong> Write MGFs of X and Y</p>
                <ul>
                    <li>$M_X(s) = e^{\lambda_1(e^s - 1)}$</li>
                    <li>$M_Y(s) = e^{\lambda_2(e^s - 1)}$</li>
                </ul>
                
                <p><strong>Step 2:</strong> Find MGF of the sum</p>
                $$M_{X+Y}(s) = M_X(s) \cdot M_Y(s) = e^{\lambda_1(e^s - 1)} \cdot e^{\lambda_2(e^s - 1)}$$
                $$= e^{\lambda_1(e^s - 1) + \lambda_2(e^s - 1)} = e^{(\lambda_1 + \lambda_2)(e^s - 1)}$$
                
                <p><strong>Step 3:</strong> Identify the distribution</p>
                <p>This is the MGF of $\text{Poisson}(\lambda_1 + \lambda_2)$!</p>
                
                <p><strong>Conclusion:</strong> By the uniqueness theorem, $X + Y \sim \text{Poisson}(\lambda_1 + \lambda_2)$ ✓</p>
                
                <p><strong>Note:</strong> This proof is much more elegant than direct convolution!</p>
            </div>

            <div class="mgf-box">
                <h3>Applications of the Sum Property</h3>
                <ul>
                    <li><strong>Distribution identification:</strong> Find MGF of sum, match to known distribution</li>
                    <li><strong>Closure properties:</strong> Prove that families of distributions are closed under addition</li>
                    <li><strong>Moment calculations:</strong> Find moments of sums without complex integrations</li>
                    <li><strong>Central Limit Theorem:</strong> Foundation for convergence results</li>
                    <li><strong>Characteristic functions:</strong> Similar property holds for Fourier transforms</li>
                </ul>
            </div>
        </div>

        <!-- Applications Section -->
        <div class="section" id="applications">
            <h2><span class="step-number">8</span>Advanced Applications</h2>
            
            <p>MGFs have many advanced applications in probability theory and statistics.</p>

            <div class="theorem-box">
                <h3>Common Advanced Applications</h3>
                <ul>
                    <li><strong>Central Limit Theorem proofs:</strong> MGF convergence implies distributional convergence</li>
                    <li><strong>Exponential families:</strong> Natural parameterization and sufficient statistics</li>
                    <li><strong>Large deviations theory:</strong> Rate functions and exponential bounds</li>
                    <li><strong>Queueing theory:</strong> Analyzing service times and waiting times</li>
                    <li><strong>Finance:</strong> Option pricing and risk management</li>
                    <li><strong>Machine learning:</strong> Exponential family distributions in generalized linear models</li>
                </ul>
            </div>

            <div class="definition-box">
                <h3>Connections to Other Mathematical Tools</h3>
                <p><strong>Laplace Transform:</strong> MGFs are related to Laplace transforms of probability distributions</p>
                <p><strong>Characteristic Functions:</strong> $\phi_X(t) = E[e^{itX}]$ - similar properties but always exist</p>
                <p><strong>Cumulant Generating Function:</strong> $K_X(s) = \ln M_X(s)$ - generates cumulants instead of moments</p>
                <p><strong>Generating Functions:</strong> For discrete distributions, probability generating functions</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="300" viewBox="0 0 1000 300">
                    <defs>
                        <linearGradient id="appGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:0.8" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="300" fill="url(#appGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#6a1b9a" font-size="18" font-weight="bold">MGFs in the Mathematical Universe</text>
                    
                    <!-- Central MGF -->
                    <circle cx="500" cy="150" r="60" fill="#6a1b9a" opacity="0.9"/>
                    <text x="500" y="145" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Moment</text>
                    <text x="500" y="160" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Generating</text>
                    <text x="500" y="175" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Functions</text>
                    
                    <!-- Connected concepts -->
                    <g>
                        <!-- Laplace Transform -->
                        <circle cx="200" cy="80" r="40" fill="#1976d2" opacity="0.8"/>
                        <text x="200" y="75" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Laplace</text>
                        <text x="200" y="90" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Transform</text>
                        <path d="M 240 95 L 445 135" stroke="#1976d2" stroke-width="2"/>
                        
                        <!-- Characteristic Functions -->
                        <circle cx="800" cy="80" r="40" fill="#388e3c" opacity="0.8"/>
                        <text x="800" y="75" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Characteristic</text>
                        <text x="800" y="90" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Functions</text>
                        <path d="M 760 95 L 555 135" stroke="#388e3c" stroke-width="2"/>
                        
                        <!-- Central Limit Theorem -->
                        <circle cx="200" cy="220" r="40" fill="#f57c00" opacity="0.8"/>
                        <text x="200" y="215" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Central Limit</text>
                        <text x="200" y="230" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Theorem</text>
                        <path d="M 240 205 L 445 165" stroke="#f57c00" stroke-width="2"/>
                        
                        <!-- Exponential Families -->
                        <circle cx="800" cy="220" r="40" fill="#9c27b0" opacity="0.8"/>
                        <text x="800" y="215" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Exponential</text>
                        <text x="800" y="230" text-anchor="middle" fill="white" font-size="11" font-weight="bold">Families</text>
                        <path d="M 760 205 L 555 165" stroke="#9c27b0" stroke-width="2"/>
                    </g>
                    
                    <text x="500" y="280" text-anchor="middle" fill="#424242" font-size="12">MGFs connect probability theory to analysis, statistics, and applied mathematics</text>
                </svg>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="section" id="summary">
            <h2><span class="step-number">9</span>Summary and Key Takeaways</h2>
            
            <p>Let's consolidate everything we've learned about Moment Generating Functions.</p>

            <div class="mgf-box">
                <h3>The Complete MGF Story</h3>
                <ol>
                    <li><strong>Definition:</strong> $M_X(s) = E[e^{sX}]$ - encodes all distributional information</li>
                    <li><strong>Moment extraction:</strong> $E[X^k] = \frac{d^k}{ds^k} M_X(s) \big|_{s=0}$ - systematic moment calculation</li>
                    <li><strong>Uniqueness:</strong> One MGF ↔ One distribution - complete characterization</li>
                    <li><strong>Sum property:</strong> $M_{X+Y}(s) = M_X(s) \cdot M_Y(s)$ (independence) - transforms addition to multiplication</li>
                    <li><strong>Applications:</strong> Distribution identification, convergence proofs, advanced theory</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="500" viewBox="0 0 1200 500">
                    <defs>
                        <linearGradient id="summaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1200" height="500" fill="url(#summaryGradient)" rx="15"/>
                    
                    <text x="600" y="30" text-anchor="middle" fill="#2e7d32" font-size="20" font-weight="bold">The MGF Ecosystem: Everything Connected</text>
                    
                    <!-- Central concept grid -->
                    <g>
                        <!-- Definition box -->
                        <rect x="50" y="70" width="250" height="100" fill="#ffffff" stroke="#2e7d32" stroke-width="2" rx="8"/>
                        <text x="175" y="95" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Definition</text>
                        <text x="175" y="120" text-anchor="middle" fill="#1976d2" font-size="13">M_X(s) = E[e^{sX}]</text>
                        <text x="175" y="140" text-anchor="middle" fill="#424242" font-size="11">Exists in neighborhood of 0</text>
                        <text x="175" y="155" text-anchor="middle" fill="#424242" font-size="11">Always equals 1 at s=0</text>
                        
                        <!-- Moments box -->
                        <rect x="320" y="70" width="250" height="100" fill="#ffffff" stroke="#388e3c" stroke-width="2" rx="8"/>
                        <text x="445" y="95" text-anchor="middle" fill="#388e3c" font-size="14" font-weight="bold">Moment Extraction</text>
                        <text x="445" y="120" text-anchor="middle" fill="#1976d2" font-size="13">E[X^k] = d^k/ds^k M_X(s)|_{s=0}</text>
                        <text x="445" y="140" text-anchor="middle" fill="#424242" font-size="11">Taylor series coefficients</text>
                        <text x="445" y="155" text-anchor="middle" fill="#424242" font-size="11">All moments from one function</text>
                        
                        <!-- Uniqueness box -->
                        <rect x="590" y="70" width="250" height="100" fill="#ffffff" stroke="#4caf50" stroke-width="2" rx="8"/>
                        <text x="715" y="95" text-anchor="middle" fill="#4caf50" font-size="14" font-weight="bold">Uniqueness</text>
                        <text x="715" y="120" text-anchor="middle" fill="#1976d2" font-size="13">M_X(s) = M_Y(s) ⟹ X ≡ Y</text>
                        <text x="715" y="140" text-anchor="middle" fill="#424242" font-size="11">One-to-one correspondence</text>
                        <text x="715" y="155" text-anchor="middle" fill="#424242" font-size="11">Distribution identification</text>
                        
                        <!-- Sums box -->
                        <rect x="860" y="70" width="250" height="100" fill="#ffffff" stroke="#66bb6a" stroke-width="2" rx="8"/>
                        <text x="985" y="95" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Independence Property</text>
                        <text x="985" y="120" text-anchor="middle" fill="#1976d2" font-size="13">M_{X+Y}(s) = M_X(s)M_Y(s)</text>
                        <text x="985" y="140" text-anchor="middle" fill="#424242" font-size="11">Addition → Multiplication</text>
                        <text x="985" y="155" text-anchor="middle" fill="#424242" font-size="11">Powerful for analysis</text>
                    </g>
                    
                    <!-- Common distributions -->
                    <rect x="100" y="200" width="1000" height="120" fill="#ffffff" stroke="#2e7d32" stroke-width="3" rx="10"/>
                    <text x="600" y="230" text-anchor="middle" fill="#2e7d32" font-size="16" font-weight="bold">Key Distribution MGFs</text>
                    
                    <text x="130" y="260" fill="#424242" font-size="13">• Normal(μ,σ²): e^{μs + σ²s²/2}     • Exponential(λ): λ/(λ-s)     • Poisson(λ): e^{λ(e^s-1)}</text>
                    <text x="130" y="280" fill="#424242" font-size="13">• Binomial(n,p): (1-p+pe^s)^n     • Uniform(a,b): (e^{bs}-e^{as})/(s(b-a))     • Gamma(α,β): (1-s/β)^{-α}</text>
                    <text x="130" y="300" fill="#424242" font-size="13">Remember: MGF structure reflects distribution properties - parameters appear naturally!</text>
                    
                    <!-- Problem-solving strategy -->
                    <rect x="100" y="340" width="1000" height="130" fill="#ffffff" stroke="#1976d2" stroke-width="3" rx="10"/>
                    <text x="600" y="370" text-anchor="middle" fill="#1976d2" font-size="16" font-weight="bold">Problem-Solving Strategy with MGFs</text>
                    
                    <text x="130" y="400" fill="#424242" font-size="13">1. Find MGF: Calculate M_X(s) = E[e^{sX}] using definition (sum for discrete, integral for continuous)</text>
                    <text x="130" y="420" fill="#424242" font-size="13">2. Extract moments: Use derivatives M_X^(k)(0) = E[X^k] to find mean, variance, higher moments</text>
                    <text x="130" y="440" fill="#424242" font-size="13">3. Identify distribution: Match MGF pattern to known distributions using uniqueness theorem</text>
                    <text x="130" y="460" fill="#424242" font-size="13">4. Analyze sums: Use M_{X+Y}(s) = M_X(s)M_Y(s) for independent variables to find sum distributions</text>
                </svg>
            </div>

            <div class="example-box">
                <h3>When to Use MGFs</h3>
                <p><strong>MGFs are ideal when:</strong></p>
                <ul>
                    <li>You need <em>all</em> moments of a distribution</li>
                    <li>You're working with sums of independent random variables</li>
                    <li>You want to identify an unknown distribution</li>
                    <li>You're proving distributional properties</li>
                    <li>You're working with exponential family distributions</li>
                </ul>
                
                <p><strong>Limitations to remember:</strong></p>
                <ul>
                    <li>Not all distributions have MGFs (heavy-tailed distributions)</li>
                    <li>Computational complexity for some integrals</li>
                    <li>Characteristic functions are more general (always exist)</li>
                </ul>
            </div>

            <div class="moments-box">
                <h3>Final Thoughts: The Power of MGFs</h3>
                <p>Moment Generating Functions represent a beautiful synthesis of probability theory and analysis. They transform complex probabilistic problems into manageable calculus, provide complete distributional characterization through a single function, and reveal deep connections between different areas of mathematics.</p>
                
                <p>The key insight is that the exponential function $e^{sX}$, through its Taylor series expansion, naturally encodes all moments. This encoding, combined with the independence property and uniqueness theorem, makes MGFs one of the most powerful tools in probability theory.</p>
                
                <p><strong>Master these concepts and you'll have a powerful toolkit for advanced probability and statistics!</strong></p>
            </div>
        </div>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
