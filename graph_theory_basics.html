<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Graph Theory: Fundamentals, Algorithms and Applications</title>
    
    <!-- MathJax for mathematical notation -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            svg: {
                fontCache: 'global'
            },
            chtml: {
                displayAlign: 'left',
                displayIndent: '0',
                scale: 1,
                minScale: 0.5
            },
            options: {
                renderActions: {
                    addMenu: []
                },
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
                processHtmlClass: 'tex2jax_process'
            },
            startup: {
                pageReady: function() {
                    return MathJax.startup.defaultPageReady().then(function() {
                        // Force all containers to be inline
                        document.querySelectorAll('mjx-container').forEach(function(el) {
                            el.style.display = 'inline';
                            el.style.margin = '0';
                        });
                    });
                }
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
            --text-color: #333;
            --light-text: #f8f9fa;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: var(--light-bg);
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: var(--secondary-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        h1 {
            text-align: center;
            color: var(--primary-color);
            padding-bottom: 0.5em;
            border-bottom: 2px solid var(--primary-color);
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .note {
            background-color: #e9f7fe;
            border-left: 4px solid var(--primary-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .definition {
            background-color: #f0f7fa;
            border: 1px solid #d1e6f1;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .theorem {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .algorithm {
            background-color: #f5f5f5;
            border: 1px solid #e0e0e0;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }
        
        .example {
            background-color: #f1f8e9;
            border-left: 4px solid #8bc34a;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
        }
        
        img, svg {
            max-width: 100%;
            height: auto;
            margin: 20px auto;
            display: block;
        }
        
        code {
            background-color: #f8f8f8;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }
        
        pre {
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            color: var(--secondary-color);
        }
        
        /* Navigation styles */
        .toc {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .toc h2 {
            margin-top: 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc ul ul {
            padding-left: 20px;
        }
        
        .toc li {
            margin-bottom: 5px;
        }
        
        .toc a {
            text-decoration: none;
            color: var(--primary-color);
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
        
        /* MathJax styling to prevent line breaks */
        .mjx-chtml, .MathJax {
            display: inline !important;
        }
        
        /* Prevent list items with formulas from having line breaks */
        li .mjx-chtml, li .MathJax {
            display: inline !important;
        }
        
        /* Ensure inline math is truly inline */
        mjx-container {
            display: inline !important;
        }
        
        /* Additional MathJax container targeting */
        mjx-container[jax="CHTML"], mjx-container[jax="SVG"] {
            display: inline !important;
        }
        
        /* Force inline display for all MathJax rendered elements */
        mjx-container[jax="CHTML"][display="true"], mjx-container[jax="SVG"][display="true"] {
            display: inline !important;
            margin: 0 !important;
        }
        
        /* Specific handling for list item math */
        li mjx-container {
            display: inline !important;
        }
        
        /* Ensure paragraph formulas stay inline */
        p .mjx-chtml, p .MathJax {
            display: inline !important;
        }
        
        /* Vertical alignment for math elements */
        mjx-container {
            vertical-align: middle !important;
        }
        
        /* Fix for SVG alignment */
        mjx-container svg {
            display: inline !important;
            vertical-align: -0.25ex !important;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Understanding Graph Theory</h1>
        <h2 class="subtitle">Fundamentals, Algorithms and Applications</h2>
        
        <!-- Table of Contents -->
        <div class="toc" id="table-of-contents">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction to Graph Theory</a></li>
                <li><a href="#basic-definitions">2. Basic Definitions and Terminology</a>
                    <ul>
                        <li><a href="#graphs-vertices-edges">2.1 Graphs, Vertices, and Edges</a></li>
                        <li><a href="#adjacency-degree">2.2 Adjacency and Degree</a></li>
                        <li><a href="#special-graphs">2.3 Special Types of Graphs</a></li>
                    </ul>
                </li>
                <li><a href="#graph-representation">3. Graph Representation</a></li>
                <li><a href="#paths-connectivity">4. Paths and Connectivity</a></li>
                <li><a href="#trees-forests">5. Trees and Forests</a></li>
                <li><a href="#planar-graphs">6. Planar Graphs</a></li>
                <li><a href="#graph-coloring">7. Graph Coloring</a></li>
                <li><a href="#mathematical-background">8. Mathematical Background</a></li>
                <li><a href="#algorithms">9. Algorithms on Graphs</a></li>
                <li><a href="#applications">10. Real-world Applications</a></li>
                <li><a href="#references">References</a></li>
            </ul>
        </div>
        
        <!-- Introduction Section -->
        <div class="section" id="introduction">
            <h2>1. Introduction to Graph Theory</h2>
            <p>
                Graph theory is a branch of mathematics concerned with the study of graphs, which are mathematical structures used to model pairwise relations between objects. Graphs consist of vertices (also called nodes) connected by edges (also called links or lines).
            </p>
            
            <div class="note">
                <p>
                    <strong>Historical Note:</strong> The origins of graph theory can be traced back to Leonhard Euler's solution to the Königsberg bridge problem in 1736. The city of Königsberg (now Kaliningrad) was set on both sides of the Pregel River and included two large islands connected to each other and the mainland by seven bridges. The problem was to find a walk through the city that would cross each bridge exactly once.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Water areas -->
                    <path d="M100,100 C200,80 400,80 500,100 C500,140 500,160 500,200 C400,220 200,220 100,200 C100,160 100,140 100,100 Z" fill="#e3f2fd" />
                    <path d="M250,120 C270,110 330,110 350,120 C350,130 350,170 350,180 C330,190 270,190 250,180 C250,170 250,130 250,120 Z" fill="#e3f2fd" />
                    
                    <!-- Land areas -->
                    <circle cx="200" cy="150" r="40" fill="#c8e6c9" />
                    <circle cx="400" cy="150" r="40" fill="#c8e6c9" />
                    <path d="M0,0 L0,300 L100,300 L100,200 C100,160 100,140 100,100 L100,0 Z" fill="#c8e6c9" />
                    <path d="M600,0 L600,300 L500,300 L500,200 C500,140 500,140 500,100 L500,0 Z" fill="#c8e6c9" />
                    
                    <!-- Bridges -->
                    <line x1="100" y1="120" x2="170" y2="130" stroke="#795548" stroke-width="8" />
                    <line x1="100" y1="180" x2="170" y2="170" stroke="#795548" stroke-width="8" />
                    <line x1="230" y1="130" x2="260" y2="130" stroke="#795548" stroke-width="8" />
                    <line x1="230" y1="170" x2="260" y2="170" stroke="#795548" stroke-width="8" />
                    <line x1="340" y1="130" x2="370" y2="130" stroke="#795548" stroke-width="8" />
                    <line x1="340" y1="170" x2="370" y2="170" stroke="#795548" stroke-width="8" />
                    <line x1="430" y1="130" x2="500" y2="120" stroke="#795548" stroke-width="8" />
                    
                    <!-- Labels -->
                    <text x="300" y="30" text-anchor="middle" font-weight="bold" font-size="16">Königsberg Bridge Problem</text>
                    <text x="200" cy="150" text-anchor="middle" font-size="12">Island A</text>
                    <text x="400" cy="150" text-anchor="middle" font-size="12">Island B</text>
                    <text x="50" cy="150" text-anchor="middle" font-size="12">Bank C</text>
                    <text x="550" cy="150" text-anchor="middle" font-size="12">Bank D</text>
                </svg>
            </div>
            
            <p>
                Euler's insight was to represent each landmass as a vertex and each bridge as an edge, transforming the physical problem into an abstract mathematical one. He proved that such a path did not exist, developing what later became known as graph theory.
            </p>
            
            <p>
                Today, graph theory has evolved into a powerful tool with applications spanning multiple domains:
            </p>
            
            <ul>
                <li><strong>Computer Science:</strong> Graph theory is fundamental to algorithms, data structures, networks, and computational complexity.</li>
                <li><strong>Physics and Chemistry:</strong> Molecular structures, physical systems, and quantum mechanics often use graph representations.</li>
                <li><strong>Social Sciences:</strong> Social networks, organizational structures, and communication patterns can be modeled as graphs.</li>
                <li><strong>Biology:</strong> Ecological networks, protein interactions, and neural connections are studied using graph theory.</li>
                <li><strong>Operations Research:</strong> Transportation networks, project planning, and resource allocation often involve graph modeling.</li>
            </ul>
            
            <p>
                In this comprehensive guide, we'll explore the fundamental concepts of graph theory, its mathematical foundations, key algorithms, and practical applications. By the end, you'll have a solid understanding of how graphs can be used to model and solve complex problems across various domains.
            </p>
        </div>
        
        <!-- Basic Definitions and Terminology Section -->
        <div class="section" id="basic-definitions">
            <h2>2. Basic Definitions and Terminology</h2>
            
            <div id="graphs-vertices-edges">
                <h3>2.1 Graphs, Vertices, and Edges</h3>
                
                <div class="definition">
                    <p>
                        <strong>Graph:</strong> A graph $G$ is a pair $(V, E)$ of sets such that $E \subseteq [V]^2$, where $[V]^2$ represents the set of all 2-element subsets of $V$. The elements of $V$ are called vertices (or nodes), and the elements of $E$ are called edges (or lines).
                    </p>
                </div>
                
                <p>
                    In more intuitive terms, a graph consists of a set of points (vertices) and a set of lines (edges) connecting pairs of these points. The visual representation of a graph shows dots for vertices and lines between them for edges.
                </p>
                
                <div class="visualization">
                    <svg width="400" height="300" viewBox="0 0 400 300">
                        <!-- Background -->
                        <rect width="400" height="300" fill="#f8f9fa" rx="10" ry="10" />
                        
                        <!-- Title -->
                        <text x="200" y="30" text-anchor="middle" font-weight="bold" font-size="16">Basic Graph Example</text>
                        
                        <!-- Vertices -->
                        <circle cx="100" cy="100" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="300" cy="100" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="100" cy="200" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="300" cy="200" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="200" cy="150" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="100" y="105" text-anchor="middle" font-weight="bold">1</text>
                        <text x="300" y="105" text-anchor="middle" font-weight="bold">2</text>
                        <text x="100" y="205" text-anchor="middle" font-weight="bold">3</text>
                        <text x="300" y="205" text-anchor="middle" font-weight="bold">4</text>
                        <text x="200" y="155" text-anchor="middle" font-weight="bold">5</text>
                        
                        <!-- Edges -->
                        <line x1="110" y1="110" x2="190" y2="145" stroke="#333" stroke-width="2" />
                        <line x1="290" y1="110" x2="210" y2="145" stroke="#333" stroke-width="2" />
                        <line x1="110" y1="190" x2="190" y2="155" stroke="#333" stroke-width="2" />
                        <line x1="290" y1="190" x2="210" y2="155" stroke="#333" stroke-width="2" />
                        <line x1="100" y1="115" x2="100" y2="185" stroke="#333" stroke-width="2" />
                        
                        <!-- Labels -->
                        <text x="200" y="250" text-anchor="middle" font-size="14">$G = (V, E)$ where $V = \{1,2,3,4,5\}$</text>
                        <text x="200" y="270" text-anchor="middle" font-size="14">$E = \{\{1,5\},\{2,5\},\{3,5\},\{4,5\},\{1,3\}\}$</text>
                    </svg>
                </div>
                
                <p>Some important terms related to graphs:</p>
                
                <ul>
                    <li><strong>Order:</strong> The number of vertices in a graph, denoted by $|G|$.</li>
                    <li><strong>Size:</strong> The number of edges in a graph, denoted by $||G||$.</li>
                    <li><strong>Incident:</strong> A vertex $v$ is incident with an edge $e$ if $v \in e$.</li>
                    <li><strong>Ends:</strong> The two vertices incident with an edge are its ends or endvertices.</li>
                    <li><strong>Empty Graph:</strong> A graph with no vertices (and consequently no edges), denoted by $\emptyset$.</li>
                    <li><strong>Trivial Graph:</strong> A graph with a single vertex and no edges.</li>
                </ul>
            </div>
            
            <div id="adjacency-degree">
                <h3>2.2 Adjacency and Degree</h3>
                
                <div class="definition">
                    <p>
                        <strong>Adjacent Vertices:</strong> Two vertices are adjacent if they are connected by an edge.
                    </p>
                    <p>
                        <strong>Degree:</strong> The degree of a vertex $v$, denoted by $d(v)$, is the number of edges incident with $v$, which equals the number of neighbors of $v$.
                    </p>
                </div>
                
                <p>
                    The degree of a vertex tells us how many other vertices it is directly connected to. It's a fundamental property that helps characterize the connectivity and importance of vertices in a graph.
                </p>
                
                <div class="theorem">
                    <h4>Handshaking Lemma:</h4>
                    <p>
                        For any graph $G$, the sum of the degrees of all vertices equals twice the number of edges:
                        $\sum_{v \in V} d(v) = 2|E|$
                    </p>
                    <p>
                        This is because each edge contributes to the degree of both its endpoints.
                    </p>
                </div>
                
                <div class="theorem">
                    <h4>Degree Sum Formula:</h4>
                    <p>
                        The average degree of a graph is:
                        $d(G) = \frac{1}{|V|}\sum_{v \in V} d(v) = \frac{2|E|}{|V|}$
                    </p>
                </div>
                
                <div class="theorem">
                    <h4>Even Number of Odd-Degree Vertices:</h4>
                    <p>
                        Every graph contains an even number of vertices with odd degree.
                    </p>
                </div>
                
                <p>
                    Other important degree-related terms:
                </p>
                
                <ul>
                    <li><strong>Minimum Degree:</strong> The smallest degree of any vertex in the graph, denoted by $\delta(G)$.</li>
                    <li><strong>Maximum Degree:</strong> The largest degree of any vertex in the graph, denoted by $\Delta(G)$.</li>
                    <li><strong>Regular Graph:</strong> A graph where all vertices have the same degree.</li>
                    <li><strong>$k$-Regular Graph:</strong> A regular graph where every vertex has degree $k$.</li>
                    <li><strong>Isolated Vertex:</strong> A vertex with degree 0 (no connections).</li>
                </ul>
            </div>
            
            <div id="special-graphs">
                <h3>2.3 Special Types of Graphs</h3>
                
                <div class="definition">
                    <p>
                        <strong>Complete Graph:</strong> A graph in which every pair of distinct vertices is connected by an edge. A complete graph with $n$ vertices is denoted by $K_n$.
                    </p>
                    <p>
                        <strong>Bipartite Graph:</strong> A graph whose vertices can be divided into two disjoint sets such that every edge connects a vertex in the first set to a vertex in the second set.
                    </p>
                    <p>
                        <strong>Complete Bipartite Graph:</strong> A bipartite graph where every vertex in the first set is connected to every vertex in the second set. If the sets have sizes $m$ and $n$, the graph is denoted by $K_{m,n}$.
                    </p>
                </div>
                
                <div class="visualization">
                    <svg width="800" height="220" viewBox="0 0 800 220">
                        <!-- Complete Graph K5 -->
                        <g transform="translate(150, 110)">
                            <!-- Vertices -->
                            <circle cx="0" cy="-80" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            <circle cx="76" cy="-24" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            <circle cx="47" cy="64" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            <circle cx="-47" cy="64" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            <circle cx="-76" cy="-24" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            
                            <!-- Edges -->
                            <line x1="0" y1="-80" x2="76" y2="-24" stroke="#333" stroke-width="1.5" />
                            <line x1="0" y1="-80" x2="47" y2="64" stroke="#333" stroke-width="1.5" />
                            <line x1="0" y1="-80" x2="-47" y2="64" stroke="#333" stroke-width="1.5" />
                            <line x1="0" y1="-80" x2="-76" y2="-24" stroke="#333" stroke-width="1.5" />
                            <line x1="76" y1="-24" x2="47" y2="64" stroke="#333" stroke-width="1.5" />
                            <line x1="76" y1="-24" x2="-47" y2="64" stroke="#333" stroke-width="1.5" />
                            <line x1="76" y1="-24" x2="-76" y2="-24" stroke="#333" stroke-width="1.5" />
                            <line x1="47" y1="64" x2="-47" y2="64" stroke="#333" stroke-width="1.5" />
                            <line x1="47" y1="64" x2="-76" y2="-24" stroke="#333" stroke-width="1.5" />
                            <line x1="-47" y1="64" x2="-76" y2="-24" stroke="#333" stroke-width="1.5" />
                            
                            <!-- Label -->
                            <text x="0" y="110" text-anchor="middle" font-weight="bold">Complete Graph $K_5$</text>
                        </g>
                        
                        <!-- Complete Bipartite Graph K3,3 -->
                        <g transform="translate(450, 110)">
                            <!-- Vertices Set 1 -->
                            <circle cx="-80" cy="-60" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            <circle cx="-80" cy="0" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            <circle cx="-80" cy="60" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                            
                            <!-- Vertices Set 2 -->
                            <circle cx="80" cy="-60" r="10" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                            <circle cx="80" cy="0" r="10" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                            <circle cx="80" cy="60" r="10" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                            
                            <!-- Edges -->
                            <line x1="-80" y1="-60" x2="80" y2="-60" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="-60" x2="80" y2="0" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="-60" x2="80" y2="60" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="0" x2="80" y2="-60" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="0" x2="80" y2="0" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="0" x2="80" y2="60" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="60" x2="80" y2="-60" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="60" x2="80" y2="0" stroke="#333" stroke-width="1.5" />
                            <line x1="-80" y1="60" x2="80" y2="60" stroke="#333" stroke-width="1.5" />
                            
                            <!-- Label -->
                            <text x="0" y="110" text-anchor="middle" font-weight="bold">Complete Bipartite Graph $K_{3,3}$</text>
                        </g>
                        
                        <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Special Types of Graphs</text>
                    </svg>
                </div>
                
                <p>Other special types of graphs include:</p>
                
                <ul>
                    <li><strong>Path:</strong> A graph consisting of a sequence of vertices connected by edges with no repetitions of vertices or edges.</li>
                    <li><strong>Cycle:</strong> A path that starts and ends at the same vertex.</li>
                    <li><strong>Tree:</strong> A connected graph with no cycles.</li>
                    <li><strong>Star:</strong> A complete bipartite graph of the form $K_{1,n}$, resembling a star with one central vertex connected to $n$ outer vertices.</li>
                    <li><strong>Wheel:</strong> A graph formed by connecting a single vertex to all vertices of a cycle.</li>
                    <li><strong>Planar Graph:</strong> A graph that can be embedded in the plane so that no edges cross.</li>
                </ul>
            </div>
        </div>
        
        <!-- Graph Representation Section -->
        <div class="section" id="graph-representation">
            <h2>3. Graph Representation</h2>
            
            <p>
                To work with graphs computationally, we need efficient ways to represent their structure. Different representations offer various trade-offs in terms of memory usage and the efficiency of common operations.
            </p>
            
            <h3>3.1 Adjacency Matrix</h3>
            
            <div class="definition">
                <p>
                    <strong>Adjacency Matrix:</strong> For a graph $G$ with $n$ vertices, the adjacency matrix is an $n \times n$ matrix $A$ where the entry $A_{ij}$ is 1 if vertices $i$ and $j$ are adjacent, and 0 otherwise.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="650" height="300" viewBox="0 0 650 300">
                    <!-- Background -->
                    <rect width="650" height="300" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="325" y="30" text-anchor="middle" font-weight="bold" font-size="16">Adjacency Matrix Representation</text>
                    
                    <!-- Graph -->
                    <g transform="translate(150, 150)">
                        <!-- Vertices -->
                        <circle cx="0" cy="-80" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="76" cy="-24" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="47" cy="64" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-47" cy="64" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-76" cy="-24" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="0" y="-75" text-anchor="middle" font-weight="bold">1</text>
                        <text x="76" y="-19" text-anchor="middle" font-weight="bold">2</text>
                        <text x="47" y="69" text-anchor="middle" font-weight="bold">3</text>
                        <text x="-47" y="69" text-anchor="middle" font-weight="bold">4</text>
                        <text x="-76" y="-19" text-anchor="middle" font-weight="bold">5</text>
                        
                        <!-- Edges -->
                        <line x1="0" y1="-80" x2="76" y2="-24" stroke="#333" stroke-width="2" />
                        <line x1="76" y1="-24" x2="47" y2="64" stroke="#333" stroke-width="2" />
                        <line x1="47" y1="64" x2="-47" y2="64" stroke="#333" stroke-width="2" />
                        <line x1="-47" y1="64" x2="-76" y2="-24" stroke="#333" stroke-width="2" />
                        <line x1="-76" y1="-24" x2="0" y2="-80" stroke="#333" stroke-width="2" />
                    </g>
                    
                    <!-- Matrix -->
                    <g transform="translate(420, 150)">
                        <!-- Matrix background -->
                        <rect x="-100" y="-90" width="200" height="180" fill="white" stroke="#333" stroke-width="2" />
                        
                        <!-- Row and column headers -->
                        <text x="-120" y="-60" text-anchor="middle" font-weight="bold">1</text>
                        <text x="-120" y="-30" text-anchor="middle" font-weight="bold">2</text>
                        <text x="-120" y="0" text-anchor="middle" font-weight="bold">3</text>
                        <text x="-120" y="30" text-anchor="middle" font-weight="bold">4</text>
                        <text x="-120" y="60" text-anchor="middle" font-weight="bold">5</text>
                        
                        <text x="-60" y="-110" text-anchor="middle" font-weight="bold">1</text>
                        <text x="-20" y="-110" text-anchor="middle" font-weight="bold">2</text>
                        <text x="20" y="-110" text-anchor="middle" font-weight="bold">3</text>
                        <text x="60" y="-110" text-anchor="middle" font-weight="bold">4</text>
                        <text x="100" y="-110" text-anchor="middle" font-weight="bold">5</text>
                        
                        <!-- Grid lines -->
                        <line x1="-100" y1="-45" x2="100" y2="-45" stroke="#333" stroke-width="1" />
                        <line x1="-100" y1="-15" x2="100" y2="-15" stroke="#333" stroke-width="1" />
                        <line x1="-100" y1="15" x2="100" y2="15" stroke="#333" stroke-width="1" />
                        <line x1="-100" y1="45" x2="100" y2="45" stroke="#333" stroke-width="1" />
                        
                        <line x1="-60" y1="-90" x2="-60" y2="90" stroke="#333" stroke-width="1" />
                        <line x1="-20" y1="-90" x2="-20" y2="90" stroke="#333" stroke-width="1" />
                        <line x1="20" y1="-90" x2="20" y2="90" stroke="#333" stroke-width="1" />
                        <line x1="60" y1="-90" x2="60" y2="90" stroke="#333" stroke-width="1" />
                        
                        <!-- Matrix values -->
                        <text x="-60" y="-60" text-anchor="middle">0</text>
                        <text x="-20" y="-60" text-anchor="middle">1</text>
                        <text x="20" y="-60" text-anchor="middle">0</text>
                        <text x="60" y="-60" text-anchor="middle">0</text>
                        <text x="100" y="-60" text-anchor="middle">1</text>
                        
                        <text x="-60" y="-30" text-anchor="middle">1</text>
                        <text x="-20" y="-30" text-anchor="middle">0</text>
                        <text x="20" y="-30" text-anchor="middle">1</text>
                        <text x="60" y="-30" text-anchor="middle">0</text>
                        <text x="100" y="-30" text-anchor="middle">0</text>
                        
                        <text x="-60" y="0" text-anchor="middle">0</text>
                        <text x="-20" y="0" text-anchor="middle">1</text>
                        <text x="20" y="0" text-anchor="middle">0</text>
                        <text x="60" y="0" text-anchor="middle">1</text>
                        <text x="100" y="0" text-anchor="middle">0</text>
                        
                        <text x="-60" y="30" text-anchor="middle">0</text>
                        <text x="-20" y="30" text-anchor="middle">0</text>
                        <text x="20" y="30" text-anchor="middle">1</text>
                        <text x="60" y="30" text-anchor="middle">0</text>
                        <text x="100" y="30" text-anchor="middle">1</text>
                        
                        <text x="-60" y="60" text-anchor="middle">1</text>
                        <text x="-20" y="60" text-anchor="middle">0</text>
                        <text x="20" y="60" text-anchor="middle">0</text>
                        <text x="60" y="60" text-anchor="middle">1</text>
                        <text x="100" y="60" text-anchor="middle">0</text>
                    </g>
                </svg>
            </div>
            
            <div class="note">
                <p><strong>Properties of Adjacency Matrix:</strong></p>
                <ul>
                    <li>Space complexity: $O(n^2)$ where $n$ is the number of vertices</li>
                    <li>Checking if two vertices are adjacent: $O(1)$ time</li>
                    <li>Finding all neighbors of a vertex: $O(n)$ time</li>
                    <li>For an undirected graph, the adjacency matrix is symmetric</li>
                    <li>For a simple graph (no self-loops), the diagonal entries are all zeros</li>
                </ul>
            </div>
            
            <h3>3.2 Adjacency List</h3>
            
            <div class="definition">
                <p>
                    <strong>Adjacency List:</strong> For each vertex in the graph, maintain a list of all adjacent vertices. This representation uses an array of lists, where the array index corresponds to the vertex and the list contains its neighbors.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="650" height="300" viewBox="0 0 650 300">
                    <!-- Background -->
                    <rect width="650" height="300" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="325" y="30" text-anchor="middle" font-weight="bold" font-size="16">Adjacency List Representation</text>
                    
                    <!-- Graph -->
                    <g transform="translate(150, 150)">
                        <!-- Vertices -->
                        <circle cx="0" cy="-80" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="76" cy="-24" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="47" cy="64" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-47" cy="64" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-76" cy="-24" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="0" y="-75" text-anchor="middle" font-weight="bold">1</text>
                        <text x="76" y="-19" text-anchor="middle" font-weight="bold">2</text>
                        <text x="47" y="69" text-anchor="middle" font-weight="bold">3</text>
                        <text x="-47" y="69" text-anchor="middle" font-weight="bold">4</text>
                        <text x="-76" y="-19" text-anchor="middle" font-weight="bold">5</text>
                        
                        <!-- Edges -->
                        <line x1="0" y1="-80" x2="76" y2="-24" stroke="#333" stroke-width="2" />
                        <line x1="76" y1="-24" x2="47" y2="64" stroke="#333" stroke-width="2" />
                        <line x1="47" y1="64" x2="-47" y2="64" stroke="#333" stroke-width="2" />
                        <line x1="-47" y1="64" x2="-76" y2="-24" stroke="#333" stroke-width="2" />
                        <line x1="-76" y1="-24" x2="0" y2="-80" stroke="#333" stroke-width="2" />
                    </g>
                    
                    <!-- Adjacency List -->
                    <g transform="translate(420, 150)">
                        <!-- List background -->
                        <rect x="-100" y="-90" width="200" height="180" fill="white" stroke="#333" stroke-width="2" />
                        
                        <!-- List entries -->
                        <text x="-90" y="-60" text-anchor="start" font-weight="bold">1: </text>
                        <text x="-70" y="-60" text-anchor="start">[2, 5]</text>
                        
                        <text x="-90" y="-30" text-anchor="start" font-weight="bold">2: </text>
                        <text x="-70" y="-30" text-anchor="start">[1, 3]</text>
                        
                        <text x="-90" y="0" text-anchor="start" font-weight="bold">3: </text>
                        <text x="-70" y="0" text-anchor="start">[2, 4]</text>
                        
                        <text x="-90" y="30" text-anchor="start" font-weight="bold">4: </text>
                        <text x="-70" y="30" text-anchor="start">[3, 5]</text>
                        
                        <text x="-90" y="60" text-anchor="start" font-weight="bold">5: </text>
                        <text x="-70" y="60" text-anchor="start">[1, 4]</text>
                    </g>
                </svg>
            </div>
            
            <div class="note">
                <p><strong>Properties of Adjacency List:</strong></p>
                <ul>
                    <li>Space complexity: $O(n + m)$ where $n$ is the number of vertices and $m$ is the number of edges</li>
                    <li>Checking if two vertices are adjacent: $O(d)$ time, where $d$ is the degree of the first vertex</li>
                    <li>Finding all neighbors of a vertex: $O(d)$ time, where $d$ is the degree of the vertex</li>
                    <li>More space-efficient for sparse graphs (where $m \ll n^2$)</li>
                    <li>Less efficient for dense graphs compared to adjacency matrix</li>
                </ul>
            </div>
            
            <h3>3.3 Incidence Matrix</h3>
            
            <div class="definition">
                <p>
                    <strong>Incidence Matrix:</strong> For a graph with $n$ vertices and $m$ edges, the incidence matrix is an $n \times m$ matrix $B$ where the entry $B_{ij}$ is 1 if vertex $i$ is incident with edge $j$, and 0 otherwise.
                </p>
            </div>
            
            <p>
                In an undirected graph, each column of the incidence matrix contains exactly two 1s, corresponding to the two vertices that the edge connects. For directed graphs, the incidence matrix can use 1 for the source vertex and -1 for the target vertex of each edge.
            </p>
            
            <div class="note">
                <p><strong>Properties of Incidence Matrix:</strong></p>
                <ul>
                    <li>Space complexity: $O(nm)$ where $n$ is the number of vertices and $m$ is the number of edges</li>
                    <li>Useful for certain algebraic graph theory applications</li>
                    <li>Can be used to efficiently compute certain graph properties</li>
                    <li>Less commonly used for general-purpose graph algorithms due to space requirements</li>
                </ul>
            </div>
            
            <h3>3.4 Edge List</h3>
            
            <div class="definition">
                <p>
                    <strong>Edge List:</strong> A simple representation that stores all edges of the graph as pairs of vertices. For a weighted graph, each entry would be a triple containing the two vertices and the edge weight.
                </p>
            </div>
            
            <p>
                For our example graph, the edge list would be: [(1,2), (2,3), (3,4), (4,5), (5,1)]
            </p>
            
            <div class="note">
                <p><strong>Properties of Edge List:</strong></p>
                <ul>
                    <li>Space complexity: $O(m)$ where $m$ is the number of edges</li>
                    <li>Simple to implement and understand</li>
                    <li>Efficient for algorithms that need to iterate through all edges</li>
                    <li>Inefficient for checking if two vertices are adjacent or finding neighbors</li>
                    <li>Commonly used in algorithms like Kruskal's algorithm for minimum spanning trees</li>
                </ul>
            </div>
            
            <h3>3.5 Choosing the Right Representation</h3>
            
            <p>
                The choice of graph representation depends on several factors:
            </p>
            
            <table>
                <thead>
                    <tr>
                        <th>Representation</th>
                        <th>Space</th>
                        <th>Check Edge</th>
                        <th>Find Neighbors</th>
                        <th>Best For</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Adjacency Matrix</td>
                        <td>$O(n^2)$</td>
                        <td>$O(1)$</td>
                        <td>$O(n)$</td>
                        <td>Dense graphs, frequent edge queries</td>
                    </tr>
                    <tr>
                        <td>Adjacency List</td>
                        <td>$O(n+m)$</td>
                        <td>$O(d)$</td>
                        <td>$O(d)$</td>
                        <td>Sparse graphs, traversal algorithms</td>
                    </tr>
                    <tr>
                        <td>Incidence Matrix</td>
                        <td>$O(nm)$</td>
                        <td>$O(m)$</td>
                        <td>$O(m)$</td>
                        <td>Algebraic applications</td>
                    </tr>
                    <tr>
                        <td>Edge List</td>
                        <td>$O(m)$</td>
                        <td>$O(m)$</td>
                        <td>$O(m)$</td>
                        <td>Edge-focused algorithms</td>
                    </tr>
                </tbody>
            </table>
            
            <p>
                In practice, most graph algorithms use either adjacency matrices (for dense graphs) or adjacency lists (for sparse graphs). The choice can significantly impact the performance of graph algorithms.
            </p>
        </div>
        
        <!-- Paths and Connectivity Section -->
        <div class="section" id="paths-connectivity">
            <h2>4. Paths and Connectivity</h2>
            
            <p>
                The concepts of paths and connectivity are fundamental to graph theory and many graph algorithms. They help us understand how vertices in a graph are related and how information or resources can flow through a network.
            </p>
            
            <h3>4.1 Paths, Walks, and Cycles</h3>
            
            <div class="definition">
                <p>
                    <strong>Walk:</strong> A walk in a graph is a sequence of vertices and edges $v_0, e_0, v_1, e_1, \ldots, e_{k-1}, v_k$ where each edge $e_i$ connects vertices $v_i$ and $v_{i+1}$. A walk can repeat both vertices and edges.
                </p>
                <p>
                    <strong>Path:</strong> A path is a walk with no repeated vertices. Formally, a path is a non-empty graph $P = (V, E)$ of the form $V = \{x_0, x_1, \ldots, x_k\}$ and $E = \{x_0x_1, x_1x_2, \ldots, x_{k-1}x_k\}$ where all $x_i$ are distinct.
                </p>
                <p>
                    <strong>Cycle:</strong> A cycle is a non-empty path where the first and last vertices are the same. Formally, a cycle $C = (V, E)$ is a graph with $V = \{x_0, x_1, \ldots, x_{k-1}\}$ and $E = \{x_0x_1, x_1x_2, \ldots, x_{k-2}x_{k-1}, x_{k-1}x_0\}$ where $k \geq 3$ and all $x_i$ are distinct.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="750" height="250" viewBox="0 0 750 250">
                    <!-- Background -->
                    <rect width="750" height="250" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="375" y="30" text-anchor="middle" font-weight="bold" font-size="16">Paths, Walks, and Cycles</text>
                    
                    <!-- Path Example -->
                    <g transform="translate(150, 125)">
                        <!-- Vertices -->
                        <circle cx="-80" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="40" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="80" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-80" y="5" text-anchor="middle" font-weight="bold">a</text>
                        <text x="-40" y="5" text-anchor="middle" font-weight="bold">b</text>
                        <text x="0" y="5" text-anchor="middle" font-weight="bold">c</text>
                        <text x="40" y="5" text-anchor="middle" font-weight="bold">d</text>
                        <text x="80" y="5" text-anchor="middle" font-weight="bold">e</text>
                        
                        <!-- Edges -->
                        <line x1="-65" y1="0" x2="-55" y2="0" stroke="#333" stroke-width="2" />
                        <line x1="-25" y1="0" x2="-15" y2="0" stroke="#333" stroke-width="2" />
                        <line x1="15" y1="0" x2="25" y2="0" stroke="#333" stroke-width="2" />
                        <line x1="55" y1="0" x2="65" y2="0" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-40" text-anchor="middle" font-weight="bold">Path</text>
                        <text x="0" y="40" text-anchor="middle">Path from a to e</text>
                    </g>
                    
                    <!-- Cycle Example -->
                    <g transform="translate(380, 125)">
                        <!-- Vertices -->
                        <circle cx="0" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="38" cy="-12" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="24" cy="32" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-24" cy="32" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-38" cy="-12" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="0" y="-35" text-anchor="middle" font-weight="bold">a</text>
                        <text x="38" y="-7" text-anchor="middle" font-weight="bold">b</text>
                        <text x="24" y="37" text-anchor="middle" font-weight="bold">c</text>
                        <text x="-24" y="37" text-anchor="middle" font-weight="bold">d</text>
                        <text x="-38" y="-7" text-anchor="middle" font-weight="bold">e</text>
                        
                        <!-- Edges -->
                        <line x1="0" y1="-25" x2="24" y2="-16" stroke="#333" stroke-width="2" />
                        <line x1="38" y1="3" x2="32" y2="19" stroke="#333" stroke-width="2" />
                        <line x1="10" y1="32" x2="-10" y2="32" stroke="#333" stroke-width="2" />
                        <line x1="-38" y1="3" x2="-32" y2="19" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-25" x2="-24" y2="-16" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-70" text-anchor="middle" font-weight="bold">Cycle</text>
                        <text x="0" y="70" text-anchor="middle">Cycle a-b-c-d-e-a</text>
                    </g>
                    
                    <!-- Walk Example -->
                    <g transform="translate(610, 125)">
                        <!-- Vertices -->
                        <circle cx="-80" cy="-20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="-20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="-20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="40" cy="-20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-80" y="-15" text-anchor="middle" font-weight="bold">a</text>
                        <text x="-40" y="-15" text-anchor="middle" font-weight="bold">b</text>
                        <text x="0" y="-15" text-anchor="middle" font-weight="bold">c</text>
                        <text x="40" y="-15" text-anchor="middle" font-weight="bold">d</text>
                        <text x="-40" y="25" text-anchor="middle" font-weight="bold">e</text>
                        <text x="0" y="25" text-anchor="middle" font-weight="bold">f</text>
                        
                        <!-- Edges -->
                        <line x1="-65" y1="-20" x2="-55" y2="-20" stroke="#333" stroke-width="2" />
                        <line x1="-25" y1="-20" x2="-15" y2="-20" stroke="#333" stroke-width="2" />
                        <line x1="15" y1="-20" x2="25" y2="-20" stroke="#333" stroke-width="2" />
                        <line x1="-40" y1="-5" x2="-40" y2="5" stroke="#333" stroke-width="2" />
                        <line x1="-25" y1="20" x2="-15" y2="20" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-5" x2="0" y2="5" stroke="#333" stroke-width="2" />
                        
                        <!-- Walk path -->
                        <path d="M-80,-20 L-40,-20 L0,-20 L0,20 L-40,20 L-40,-20 L0,-20 L40,-20" fill="none" stroke="#f44336" stroke-width="2" stroke-dasharray="5,3" />
                        
                        <!-- Title -->
                        <text x="-20" y="-50" text-anchor="middle" font-weight="bold">Walk</text>
                        <text x="-20" y="50" text-anchor="middle">Walk a-b-c-f-e-b-c-d</text>
                        <text x="-20" y="70" text-anchor="middle">(repeats vertices b and c)</text>
                    </g>
                </svg>
            </div>
            
            <div class="note">
                <p><strong>Important Properties:</strong></p>
                <ul>
                    <li>The <strong>length</strong> of a path or cycle is the number of its edges</li>
                    <li>A path with $k$ edges is denoted by $P_k$</li>
                    <li>A cycle with $k$ edges (and vertices) is denoted by $C_k$</li>
                    <li>The <strong>girth</strong> of a graph is the length of its shortest cycle</li>
                    <li>The <strong>circumference</strong> of a graph is the length of its longest cycle</li>
                </ul>
            </div>
            
            <div class="theorem">
                <h4>Path-Cycle Relationship:</h4>
                <p>
                    Every graph with minimum degree $\delta(G) \geq 2$ contains a cycle.
                </p>
                <p>
                    <em>Proof idea:</em> Start at any vertex and keep walking until you visit a vertex for the second time. The segment of your walk between the two visits to that vertex forms a cycle.
                </p>
            </div>
            
            <h3>4.2 Distance and Diameter</h3>
            
            <div class="definition">
                <p>
                    <strong>Distance:</strong> The distance $d(u,v)$ between two vertices $u$ and $v$ in a graph is the length of the shortest path between them. If no such path exists, the distance is defined as infinity.
                </p>
                <p>
                    <strong>Diameter:</strong> The diameter of a graph $G$, denoted $\text{diam}(G)$, is the maximum distance between any pair of vertices in $G$. That is, $\text{diam}(G) = \max_{u,v \in V(G)} d(u,v)$.
                </p>
                <p>
                    <strong>Radius:</strong> The radius of a graph $G$, denoted $\text{rad}(G)$, is the minimum eccentricity of any vertex. The eccentricity of a vertex $v$ is the maximum distance from $v$ to any other vertex in the graph.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Relationship between Radius and Diameter:</h4>
                <p>
                    For any connected graph $G$:
                    $\text{rad}(G) \leq \text{diam}(G) \leq 2 \cdot \text{rad}(G)$
                </p>
            </div>
            
            <div class="theorem">
                <h4>Girth-Diameter Relationship:</h4>
                <p>
                    For any graph $G$ containing a cycle:
                    $g(G) \leq 2 \cdot \text{diam}(G) + 1$
                </p>
                <p>
                    where $g(G)$ is the girth of $G$.
                </p>
            </div>
            
            <h3>4.3 Connectivity</h3>
            
            <div class="definition">
                <p>
                    <strong>Connected Graph:</strong> A graph is connected if there exists a path between every pair of vertices. Otherwise, it is disconnected.
                </p>
                <p>
                    <strong>Component:</strong> A maximal connected subgraph of a graph is called a component.
                </p>
                <p>
                    <strong>Cut Vertex:</strong> A vertex whose removal increases the number of components in the graph.
                </p>
                <p>
                    <strong>Bridge:</strong> An edge whose removal increases the number of components in the graph.
                </p>
                <p>
                    <strong>$k$-Connected:</strong> A graph is $k$-connected if it has more than $k$ vertices and remains connected after removing any $k-1$ vertices.
                </p>
                <p>
                    <strong>Connectivity:</strong> The connectivity $\kappa(G)$ of a graph $G$ is the minimum number of vertices whose removal disconnects $G$ or makes it trivial.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="650" height="350" viewBox="0 0 650 350">
                    <!-- Background -->
                    <rect width="650" height="350" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="325" y="30" text-anchor="middle" font-weight="bold" font-size="16">Connectivity Concepts</text>
                    
                    <!-- Graph with Cut Vertex -->
                    <g transform="translate(200, 150)">
                        <!-- Vertices -->
                        <circle cx="-80" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="0" r="15" fill="#e8eaf6" stroke="#3f51b5" stroke-width="3" />
                        <circle cx="40" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="80" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-80" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-80" y="-35" text-anchor="middle" font-weight="bold">a</text>
                        <text x="-40" y="-35" text-anchor="middle" font-weight="bold">b</text>
                        <text x="0" y="5" text-anchor="middle" font-weight="bold">c</text>
                        <text x="40" y="45" text-anchor="middle" font-weight="bold">d</text>
                        <text x="80" y="45" text-anchor="middle" font-weight="bold">e</text>
                        <text x="-40" y="45" text-anchor="middle" font-weight="bold">f</text>
                        <text x="-80" y="45" text-anchor="middle" font-weight="bold">g</text>
                        
                        <!-- Edges -->
                        <line x1="-65" y1="-40" x2="-55" y2="-40" stroke="#333" stroke-width="2" />
                        <line x1="-25" y1="-40" x2="-15" y2="-10" stroke="#333" stroke-width="2" />
                        <line x1="15" y1="10" x2="30" y2="30" stroke="#333" stroke-width="2" />
                        <line x1="55" y1="40" x2="65" y2="40" stroke="#333" stroke-width="2" />
                        <line x1="-15" y1="10" x2="-30" y2="30" stroke="#333" stroke-width="2" />
                        <line x1="-55" y1="40" x2="-65" y2="40" stroke="#333" stroke-width="2" />
                        <line x1="-80" y1="-25" x2="-80" y2="25" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-80" text-anchor="middle" font-weight="bold">Cut Vertex</text>
                        <text x="0" y="80" text-anchor="middle">Vertex c is a cut vertex</text>
                    </g>
                    
                    <!-- Graph with Bridge -->
                    <g transform="translate(450, 150)">
                        <!-- Vertices -->
                        <circle cx="-80" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="40" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="80" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-80" y="-35" text-anchor="middle" font-weight="bold">h</text>
                        <text x="-40" y="-35" text-anchor="middle" font-weight="bold">i</text>
                        <text x="0" y="-35" text-anchor="middle" font-weight="bold">j</text>
                        <text x="40" y="45" text-anchor="middle" font-weight="bold">k</text>
                        <text x="80" y="45" text-anchor="middle" font-weight="bold">l</text>
                        <text x="0" y="45" text-anchor="middle" font-weight="bold">m</text>
                        
                        <!-- Edges -->
                        <line x1="-65" y1="-40" x2="-55" y2="-40" stroke="#333" stroke-width="2" />
                        <line x1="-25" y1="-40" x2="-15" y2="-40" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-25" x2="0" y2="25" stroke="#ff5722" stroke-width="3" />
                        <line x1="15" y1="40" x2="25" y2="40" stroke="#333" stroke-width="2" />
                        <line x1="55" y1="40" x2="65" y2="40" stroke="#333" stroke-width="2" />
                        <line x1="-80" y1="-25" x2="-40" y2="-25" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-25" x2="-40" y2="-25" stroke="#333" stroke-width="2" />
                        <line x1="40" y1="25" x2="0" y2="25" stroke="#333" stroke-width="2" />
                        <line x1="40" y1="25" x2="80" y2="25" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-80" text-anchor="middle" font-weight="bold">Bridge</text>
                        <text x="0" y="80" text-anchor="middle">Edge (j,m) is a bridge</text>
                    </g>
                    
                    <!-- Components Example -->
                    <g transform="translate(325, 280)">
                        <!-- Component 1 -->
                        <circle cx="-100" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-60" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <line x1="-85" y1="0" x2="-75" y2="0" stroke="#333" stroke-width="2" />
                        
                        <!-- Component 2 -->
                        <circle cx="0" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="40" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="20" cy="-30" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <line x1="0" y1="0" x2="40" y2="0" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="0" x2="20" y2="-30" stroke="#333" stroke-width="2" />
                        <line x1="40" y1="0" x2="20" y2="-30" stroke="#333" stroke-width="2" />
                        
                        <!-- Component 3 -->
                        <circle cx="100" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <text x="0" y="40" text-anchor="middle">A disconnected graph with three components</text>
                    </g>
                </svg>
            </div>
            
            <div class="theorem">
                <h4>Menger's Theorem:</h4>
                <p>
                    Let $G$ be a graph and $x,y$ be two non-adjacent vertices. The minimum number of vertices that must be removed to disconnect $x$ from $y$ is equal to the maximum number of pairwise vertex-disjoint paths from $x$ to $y$.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Connectivity Bounds:</h4>
                <p>
                    For any graph $G$:
                    $\kappa(G) \leq \lambda(G) \leq \delta(G)$
                </p>
                <p>
                    where $\kappa(G)$ is the vertex connectivity, $\lambda(G)$ is the edge connectivity, and $\delta(G)$ is the minimum degree of $G$.
                </p>
            </div>
            
            <h3>4.4 Euler Tours</h3>
            
            <div class="definition">
                <p>
                    <strong>Euler Tour:</strong> A closed walk in a graph that traverses each edge exactly once.
                </p>
                <p>
                    <strong>Eulerian Graph:</strong> A graph that contains an Euler tour.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Euler's Theorem (1736):</h4>
                <p>
                    A connected graph has an Euler tour if and only if every vertex has even degree.
                </p>
            </div>
            
            <div class="algorithm">
                <h4>Finding an Euler Tour:</h4>
                <pre>
function FindEulerTour(G):
    if G is not connected or some vertex has odd degree:
        return "No Euler tour exists"
    
    tour = empty list
    Start with any vertex v
    
    function DFS(vertex):
        for each edge e incident to vertex:
            if e has not been visited:
                Mark e as visited
                Let u be the other endpoint of e
                DFS(u)
        
        Add vertex to the beginning of tour
    
    DFS(v)
    return tour
                </pre>
            </div>
            
            <h3>4.5 Hamilton Cycles</h3>
            
            <div class="definition">
                <p>
                    <strong>Hamilton Path:</strong> A path in a graph that visits each vertex exactly once.
                </p>
                <p>
                    <strong>Hamilton Cycle:</strong> A cycle in a graph that visits each vertex exactly once (except the starting vertex, which is also the ending vertex).
                </p>
                <p>
                    <strong>Hamiltonian Graph:</strong> A graph that contains a Hamilton cycle.
                </p>
            </div>
            
            <div class="note">
                <p>
                    Unlike Euler tours, there is no simple characterization of Hamiltonian graphs. The problem of determining whether a graph has a Hamilton cycle is NP-complete.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Dirac's Theorem:</h4>
                <p>
                    If $G$ is a simple graph with $n \geq 3$ vertices and $\delta(G) \geq \frac{n}{2}$, then $G$ is Hamiltonian.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Ore's Theorem:</h4>
                <p>
                    If $G$ is a simple graph with $n \geq 3$ vertices and $d(u) + d(v) \geq n$ for every pair of non-adjacent vertices $u$ and $v$, then $G$ is Hamiltonian.
                </p>
            </div>
            
            <p>
                The contrast between Euler tours and Hamilton cycles highlights an interesting phenomenon in graph theory: some problems that seem similar can have vastly different complexities. While finding an Euler tour is a simple, polynomial-time problem, finding a Hamilton cycle is NP-complete.
            </p>
        </div>
        
        <!-- Trees and Forests Section -->
        <div class="section" id="trees-forests">
            <h2>5. Trees and Forests</h2>
            
            <p>
                Trees are among the most important and widely used structures in graph theory. They provide a way to organize hierarchical data and are fundamental to many algorithms in computer science.
            </p>
            
            <h3>5.1 Basic Definitions</h3>
            
            <div class="definition">
                <p>
                    <strong>Forest:</strong> A graph with no cycles (i.e., an acyclic graph).
                </p>
                <p>
                    <strong>Tree:</strong> A connected forest (i.e., a connected acyclic graph).
                </p>
                <p>
                    <strong>Leaf:</strong> A vertex of degree 1 in a tree.
                </p>
                <p>
                    <strong>Internal Vertex:</strong> A vertex that is not a leaf.
                </p>
                <p>
                    <strong>Spanning Tree:</strong> A subgraph of a graph $G$ that is a tree and includes all vertices of $G$.
                </p>
                <p>
                    <strong>Spanning Forest:</strong> A subgraph of a graph $G$ that is a forest and includes all vertices of $G$. Each component of a spanning forest is a spanning tree of the corresponding component of $G$.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="650" height="300" viewBox="0 0 650 300">
                    <!-- Background -->
                    <rect width="650" height="300" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="325" y="30" text-anchor="middle" font-weight="bold" font-size="16">Trees and Forests</text>
                    
                    <!-- Tree Example -->
                    <g transform="translate(150, 150)">
                        <!-- Vertices -->
                        <circle cx="0" cy="-60" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-40" cy="-20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="40" cy="-20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-60" cy="20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-20" cy="20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="20" cy="20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="60" cy="20" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="0" y="-55" text-anchor="middle" font-weight="bold">a</text>
                        <text x="-40" y="-15" text-anchor="middle" font-weight="bold">b</text>
                        <text x="40" y="-15" text-anchor="middle" font-weight="bold">c</text>
                        <text x="-60" y="25" text-anchor="middle" font-weight="bold">d</text>
                        <text x="-20" y="25" text-anchor="middle" font-weight="bold">e</text>
                        <text x="20" y="25" text-anchor="middle" font-weight="bold">f</text>
                        <text x="60" y="25" text-anchor="middle" font-weight="bold">g</text>
                        
                        <!-- Edges -->
                        <line x1="0" y1="-45" x2="-30" y2="-25" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-45" x2="30" y2="-25" stroke="#333" stroke-width="2" />
                        <line x1="-40" y1="-5" x2="-50" y2="10" stroke="#333" stroke-width="2" />
                        <line x1="-40" y1="-5" x2="-30" y2="10" stroke="#333" stroke-width="2" />
                        <line x1="40" y1="-5" x2="30" y2="10" stroke="#333" stroke-width="2" />
                        <line x1="40" y1="-5" x2="50" y2="10" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-90" text-anchor="middle" font-weight="bold">Tree</text>
                        <text x="0" y="60" text-anchor="middle">a is the root, d, e, f, g are leaves</text>
                    </g>
                    
                    <!-- Forest Example -->
                    <g transform="translate(450, 150)">
                        <!-- Tree 1 -->
                        <circle cx="-80" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-80" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-80" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <line x1="-80" y1="-25" x2="-80" y2="-15" stroke="#333" stroke-width="2" />
                        <line x1="-80" y1="15" x2="-80" y2="25" stroke="#333" stroke-width="2" />
                        
                        <!-- Tree 2 -->
                        <circle cx="0" cy="-40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-30" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="30" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="40" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <line x1="0" y1="-25" x2="-20" y2="-10" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-25" x2="20" y2="-10" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="25" x2="20" y2="10" stroke="#333" stroke-width="2" />
                        
                        <!-- Tree 3 -->
                        <circle cx="80" cy="0" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-90" text-anchor="middle" font-weight="bold">Forest</text>
                        <text x="0" y="70" text-anchor="middle">A forest with three trees</text>
                    </g>
                </svg>
            </div>
            
            <h3>5.2 Properties of Trees</h3>
            
            <div class="theorem">
                <h4>Equivalent Characterizations of Trees:</h4>
                <p>
                    The following statements are equivalent for a graph $T$:
                </p>
                <ol>
                    <li>$T$ is a tree (a connected acyclic graph).</li>
                    <li>Any two vertices of $T$ are connected by exactly one path.</li>
                    <li>$T$ is connected, but removing any edge disconnects it.</li>
                    <li>$T$ is acyclic, but adding any edge creates exactly one cycle.</li>
                    <li>$T$ is connected and has exactly $n-1$ edges, where $n$ is the number of vertices.</li>
                </ol>
            </div>
            
            <div class="theorem">
                <h4>Leaf Existence:</h4>
                <p>
                    Every non-trivial tree has at least one leaf (a vertex of degree 1). In fact, every tree with at least two vertices has at least two leaves.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Edge-Vertex Relationship:</h4>
                <p>
                    A graph with $n$ vertices is a tree if and only if it is connected and has exactly $n-1$ edges.
                </p>
            </div>
            
            <h3>5.3 Rooted Trees</h3>
            
            <div class="definition">
                <p>
                    <strong>Rooted Tree:</strong> A tree with one vertex designated as the root.
                </p>
                <p>
                    <strong>Parent:</strong> In a rooted tree, the parent of a vertex $v$ is the vertex connected to $v$ on the path from the root to $v$.
                </p>
                <p>
                    <strong>Child:</strong> In a rooted tree, a child of a vertex $v$ is a vertex of which $v$ is the parent.
                </p>
                <p>
                    <strong>Ancestor:</strong> A vertex $u$ is an ancestor of a vertex $v$ if $u$ lies on the path from the root to $v$.
                </p>
                <p>
                    <strong>Descendant:</strong> A vertex $v$ is a descendant of a vertex $u$ if $u$ is an ancestor of $v$.
                </p>
                <p>
                    <strong>Depth:</strong> The depth of a vertex in a rooted tree is the length of the path from the root to the vertex.
                </p>
                <p>
                    <strong>Height:</strong> The height of a rooted tree is the maximum depth of any vertex in the tree.
                </p>
            </div>
            
            <div class="note">
                <p>
                    Rooted trees naturally represent hierarchical relationships and are widely used in computer science for:
                </p>
                <ul>
                    <li>File systems</li>
                    <li>Organization structures</li>
                    <li>XML and HTML documents</li>
                    <li>Decision trees</li>
                    <li>Binary search trees and other data structures</li>
                </ul>
            </div>
            
            <h3>5.4 Spanning Trees</h3>
            
            <div class="definition">
                <p>
                    <strong>Spanning Tree:</strong> A spanning tree of a graph $G$ is a subgraph that is a tree and includes all vertices of $G$.
                </p>
                <p>
                    <strong>Minimum Spanning Tree (MST):</strong> In a weighted graph, a spanning tree whose total edge weight is minimized.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Existence of Spanning Trees:</h4>
                <p>
                    Every connected graph has at least one spanning tree.
                </p>
            </div>
            
            <div class="note">
                <p>
                    A graph can have multiple spanning trees. For a complete graph $K_n$, Cayley's formula states that the number of distinct spanning trees is $n^{n-2}$.
                </p>
            </div>
            
            <h3>5.5 Minimum Spanning Tree Algorithms</h3>
            
            <div class="algorithm">
                <h4>Kruskal's Algorithm:</h4>
                <pre>
function KruskalMST(G, w):
    A = empty set  // A will contain the edges of the MST
    for each vertex v in G:
        Make-Set(v)  // Create a set for each vertex
    
    Sort the edges of G by weight in non-decreasing order
    
    for each edge (u,v) in the sorted order:
        if Find-Set(u) != Find-Set(v):
            A = A ∪ {(u,v)}
            Union(u, v)
    
    return A
                </pre>
                <p>
                    Time Complexity: $O(E \log E)$ where $E$ is the number of edges.
                </p>
            </div>
            
            <div class="algorithm">
                <h4>Prim's Algorithm:</h4>
                <pre>
function PrimMST(G, w, r):
    for each vertex u in G:
        key[u] = infinity
        parent[u] = NIL
    
    key[r] = 0  // r is the starting vertex
    Q = all vertices of G
    
    while Q is not empty:
        u = Extract-Min(Q)  // Extract vertex with minimum key
        for each vertex v adjacent to u and in Q:
            if w(u,v) < key[v]:
                parent[v] = u
                key[v] = w(u,v)
    
    return the tree given by parent[]
                </pre>
                <p>
                    Time Complexity: $O(E \log V)$ with a binary heap, where $V$ is the number of vertices and $E$ is the number of edges.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Cut Property:</h4>
                <p>
                    For any cut $(S, V-S)$ in a graph, if edge $e$ is the minimum weight edge crossing the cut, then $e$ is in every minimum spanning tree.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Cycle Property:</h4>
                <p>
                    If $C$ is a cycle in $G$ and edge $e$ is the maximum weight edge in $C$, then $e$ is not in any minimum spanning tree of $G$.
                </p>
            </div>
            
            <h3>5.6 Applications of Trees</h3>
            
            <ul>
                <li><strong>Network Design:</strong> Minimum spanning trees are used for designing networks with minimal cost.</li>
                <li><strong>Cluster Analysis:</strong> Hierarchical clustering algorithms use tree structures.</li>
                <li><strong>Image Segmentation:</strong> MSTs can be used to segment images based on pixel similarities.</li>
                <li><strong>Approximation Algorithms:</strong> Many approximation algorithms for NP-hard problems use spanning trees.</li>
                <li><strong>Decision Trees:</strong> Used in machine learning for classification and regression tasks.</li>
                <li><strong>Syntax Trees:</strong> Used in compilers to represent the syntactic structure of code.</li>
                <li><strong>Huffman Coding:</strong> Uses binary trees for data compression.</li>
            </ul>
            
            <div class="example">
                <h4>Network Design Example:</h4>
                <p>
                    Suppose a company wants to connect 5 offices with fiber optic cables. The cost of laying cable between each pair of offices is given. By finding a minimum spanning tree, the company can connect all offices with minimum total cost.
                </p>
                <svg width="300" height="200" viewBox="0 0 300 200">
                    <!-- Offices -->
                    <circle cx="50" cy="50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                    <circle cx="250" cy="50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                    <circle cx="50" cy="150" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                    <circle cx="250" cy="150" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                    <circle cx="150" cy="100" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                    
                    <!-- Office labels -->
                    <text x="50" y="55" text-anchor="middle" font-weight="bold">A</text>
                    <text x="250" y="55" text-anchor="middle" font-weight="bold">B</text>
                    <text x="50" y="155" text-anchor="middle" font-weight="bold">C</text>
                    <text x="250" y="155" text-anchor="middle" font-weight="bold">D</text>
                    <text x="150" y="105" text-anchor="middle" font-weight="bold">E</text>
                    
                    <!-- MST edges -->
                    <line x1="65" y1="50" x2="135" y2="90" stroke="#4caf50" stroke-width="3" />
                    <line x1="165" y1="90" x2="235" y2="50" stroke="#4caf50" stroke-width="3" />
                    <line x1="150" y1="115" x2="250" y2="150" stroke="#4caf50" stroke-width="3" />
                    <line x1="65" y1="150" x2="135" y2="110" stroke="#4caf50" stroke-width="3" />
                    
                    <!-- Edge weights -->
                    <text x="100" y="60" text-anchor="middle" font-size="12">10</text>
                    <text x="200" y="60" text-anchor="middle" font-size="12">15</text>
                    <text x="200" y="130" text-anchor="middle" font-size="12">12</text>
                    <text x="100" y="130" text-anchor="middle" font-size="12">8</text>
                </svg>
            </div>
        </div>
        
        <!-- Continue with other sections... -->
        <div class="section" id="planar-graphs">
            <h2>6. Planar Graphs</h2>
            
            <p>
                Planar graphs are a fundamental class of graphs that can be drawn in the plane without edge crossings. They have many important applications and properties.
            </p>
            
            <h3>6.1 Basic Definitions</h3>
            
            <div class="definition">
                <p>
                    <strong>Planar Graph:</strong> A graph that can be embedded in the plane so that no edges cross.
                </p>
                <p>
                    <strong>Planar Embedding:</strong> A drawing of a planar graph in the plane where no edges cross.
                </p>
                <p>
                    <strong>Face:</strong> A region bounded by edges in a planar embedding. This includes the unbounded outer face.
                </p>
                <p>
                    <strong>Plane Graph:</strong> A planar graph with a specific planar embedding.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="650" height="300" viewBox="0 0 650 300">
                    <!-- Background -->
                    <rect width="650" height="300" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="325" y="30" text-anchor="middle" font-weight="bold" font-size="16">Planar Graphs</text>
                    
                    <!-- Planar Drawing of K4 -->
                    <g transform="translate(200, 150)">
                        <!-- Vertices -->
                        <circle cx="-50" cy="-50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="50" cy="-50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="50" cy="50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-50" cy="50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-50" y="-45" text-anchor="middle" font-weight="bold">1</text>
                        <text x="50" y="-45" text-anchor="middle" font-weight="bold">2</text>
                        <text x="50" y="55" text-anchor="middle" font-weight="bold">3</text>
                        <text x="-50" y="55" text-anchor="middle" font-weight="bold">4</text>
                        
                        <!-- Edges -->
                        <line x1="-50" y1="-50" x2="50" y2="-50" stroke="#333" stroke-width="2" />
                        <line x1="50" y1="-50" x2="50" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="50" y1="50" x2="-50" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="-50" y1="50" x2="-50" y2="-50" stroke="#333" stroke-width="2" />
                        <line x1="-50" y1="-50" x2="50" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="50" y1="-50" x2="-50" y2="50" stroke="#333" stroke-width="2" />
                        
                        <!-- Face labels -->
                        <text x="0" y="0" text-anchor="middle" fill="#e91e63" font-style="italic">F1</text>
                        <text x="0" y="-70" text-anchor="middle" fill="#e91e63" font-style="italic">F2</text>
                        <text x="70" y="0" text-anchor="middle" fill="#e91e63" font-style="italic">F3</text>
                        <text x="0" y="70" text-anchor="middle" fill="#e91e63" font-style="italic">F4</text>
                        <text x="-70" y="0" text-anchor="middle" fill="#e91e63" font-style="italic">F5</text>
                        
                        <!-- Title -->
                        <text x="0" y="-100" text-anchor="middle" font-weight="bold">Planar Drawing of K₄</text>
                        <text x="0" y="100" text-anchor="middle">5 faces (including outer face)</text>
                    </g>
                    
                    <!-- Non-Planar K5 and K3,3 -->
                    <g transform="translate(500, 150)">
                        <!-- K5 -->
                        <g transform="translate(-75, 0)">
                            <!-- Vertices -->
                            <circle cx="0" cy="-40" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="38" cy="-12" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="24" cy="32" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="-24" cy="32" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="-38" cy="-12" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            
                            <!-- Edges -->
                            <line x1="0" y1="-40" x2="38" y2="-12" stroke="#333" stroke-width="1.5" />
                            <line x1="0" y1="-40" x2="24" y2="32" stroke="#333" stroke-width="1.5" />
                            <line x1="0" y1="-40" x2="-24" y2="32" stroke="#333" stroke-width="1.5" />
                            <line x1="0" y1="-40" x2="-38" y2="-12" stroke="#333" stroke-width="1.5" />
                            <line x1="38" y1="-12" x2="24" y2="32" stroke="#333" stroke-width="1.5" />
                            <line x1="38" y1="-12" x2="-24" y2="32" stroke="#333" stroke-width="1.5" />
                            <line x1="38" y1="-12" x2="-38" y2="-12" stroke="#333" stroke-width="1.5" />
                            <line x1="24" y1="32" x2="-24" y2="32" stroke="#333" stroke-width="1.5" />
                            <line x1="24" y1="32" x2="-38" y2="-12" stroke="#333" stroke-width="1.5" />
                            <line x1="-24" y1="32" x2="-38" y2="-12" stroke="#333" stroke-width="1.5" />
                            
                            <text x="0" y="60" text-anchor="middle" font-weight="bold" font-size="12">K₅</text>
                        </g>
                        
                        <!-- K3,3 -->
                        <g transform="translate(75, 0)">
                            <!-- Vertices Set 1 -->
                            <circle cx="-30" cy="-30" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="-30" cy="0" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="-30" cy="30" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            
                            <!-- Vertices Set 2 -->
                            <circle cx="30" cy="-30" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="30" cy="0" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            <circle cx="30" cy="30" r="10" fill="#ffcdd2" stroke="#f44336" stroke-width="2" />
                            
                            <!-- Edges -->
                            <line x1="-30" y1="-30" x2="30" y2="-30" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="-30" x2="30" y2="0" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="-30" x2="30" y2="30" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="0" x2="30" y2="-30" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="0" x2="30" y2="0" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="0" x2="30" y2="30" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="30" x2="30" y2="-30" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="30" x2="30" y2="0" stroke="#333" stroke-width="1.5" />
                            <line x1="-30" y1="30" x2="30" y2="30" stroke="#333" stroke-width="1.5" />
                            
                            <text x="0" y="60" text-anchor="middle" font-weight="bold" font-size="12">K₃,₃</text>
                        </g>
                        
                        <text x="0" y="-70" text-anchor="middle" font-weight="bold">Non-Planar Graphs</text>
                        <text x="0" y="90" text-anchor="middle">Cannot be drawn without crossings</text>
                    </g>
                </svg>
            </div>
            
            <h3>6.2 Euler's Formula</h3>
            
            <div class="theorem">
                <h4>Euler's Formula:</h4>
                <p>
                    If $G$ is a connected planar graph with $n$ vertices, $e$ edges, and $f$ faces, then:
                    $n - e + f = 2$
                </p>
                <p>
                    This formula is fundamental to the theory of planar graphs and can be generalized to graphs embedded on other surfaces.
                </p>
            </div>
            
            <div class="example">
                <h4>Checking Euler's Formula:</h4>
                <p>
                    For the planar embedding of $K_4$ shown earlier:
                </p>
                <ul>
                    <li>Number of vertices: $n = 4$</li>
                    <li>Number of edges: $e = 6$</li>
                    <li>Number of faces: $f = 4$ (including the outer face)</li>
                </ul>
                <p>
                    Checking Euler's formula: $n - e + f = 4 - 6 + 4 = 2$ ✓
                </p>
            </div>
            
            <h3>6.3 Consequences of Euler's Formula</h3>
            
            <div class="theorem">
                <h4>Edge Bound for Planar Graphs:</h4>
                <p>
                    If $G$ is a simple planar graph with $n \geq 3$ vertices, then:
                    $e \leq 3n - 6$
                </p>
                <p>
                    where $e$ is the number of edges in $G$.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Edge Bound for Bipartite Planar Graphs:</h4>
                <p>
                    If $G$ is a simple bipartite planar graph with $n \geq 3$ vertices, then:
                    $e \leq 2n - 4$
                </p>
                <p>
                    where $e$ is the number of edges in $G$.
                </p>
            </div>
            
            <p>
                These bounds are tight and are achieved by maximal planar graphs (triangulations) and maximal bipartite planar graphs, respectively.
            </p>
            
            <h3>6.4 Characterization of Planar Graphs</h3>
            
            <div class="theorem">
                <h4>Kuratowski's Theorem (1930):</h4>
                <p>
                    A graph is planar if and only if it does not contain a subdivision of $K_5$ or $K_{3,3}$.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Wagner's Theorem (1937):</h4>
                <p>
                    A graph is planar if and only if it does not have $K_5$ or $K_{3,3}$ as a minor.
                </p>
            </div>
            
            <div class="note">
                <p>
                    These theorems provide different ways to characterize planar graphs. Kuratowski's theorem is in terms of subdivisions (topological minors), while Wagner's theorem is in terms of minors.
                </p>
            </div>
            
            <h3>6.5 Dual Graphs</h3>
            
            <div class="definition">
                <p>
                    <strong>Dual Graph:</strong> Given a plane graph $G$, its dual graph $G^*$ is constructed as follows:
                </p>
                <ul>
                    <li>Each face of $G$ corresponds to a vertex in $G^*$.</li>
                    <li>Two vertices in $G^*$ are adjacent if and only if the corresponding faces in $G$ share an edge.</li>
                </ul>
            </div>
            
            <div class="visualization">
                <svg width="500" height="220" viewBox="0 0 500 220">
                    <!-- Background -->
                    <rect width="500" height="220" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="250" y="30" text-anchor="middle" font-weight="bold">Dual Graph Example</text>
                    
                    <!-- Primal Graph -->
                    <g transform="translate(150, 120)">
                        <!-- Primal Vertices -->
                        <circle cx="-60" cy="-30" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="-60" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="60" cy="-30" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="60" cy="30" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="60" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-60" cy="30" r="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Primal Edges -->
                        <line x1="-60" y1="-30" x2="0" y2="-60" stroke="#2196f3" stroke-width="2" />
                        <line x1="0" y1="-60" x2="60" y2="-30" stroke="#2196f3" stroke-width="2" />
                        <line x1="60" y1="-30" x2="60" y2="30" stroke="#2196f3" stroke-width="2" />
                        <line x1="60" y1="30" x2="0" y2="60" stroke="#2196f3" stroke-width="2" />
                        <line x1="0" y1="60" x2="-60" y2="30" stroke="#2196f3" stroke-width="2" />
                        <line x1="-60" y1="30" x2="-60" y2="-30" stroke="#2196f3" stroke-width="2" />
                        <line x1="0" y1="-60" x2="0" y2="60" stroke="#2196f3" stroke-width="2" />
                        <line x1="-60" y1="-30" x2="60" y2="-30" stroke="#2196f3" stroke-width="2" />
                        <line x1="-60" y1="30" x2="60" y2="30" stroke="#2196f3" stroke-width="2" />
                        
                        <text x="0" y="90" text-anchor="middle">Original Graph $G$</text>
                    </g>
                    
                    <!-- Dual Graph -->
                    <g transform="translate(350, 120)">
                        <!-- Dual Vertices (representing faces) -->
                        <circle cx="-30" cy="0" r="8" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                        <circle cx="0" cy="-30" r="8" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                        <circle cx="30" cy="0" r="8" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                        <circle cx="0" cy="30" r="8" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                        <circle cx="0" cy="0" r="8" fill="#ffebee" stroke="#f44336" stroke-width="2" />
                        
                        <!-- Dual Edges -->
                        <line x1="-30" y1="0" x2="0" y2="-30" stroke="#f44336" stroke-width="1.5" />
                        <line x1="0" y1="-30" x2="30" y2="0" stroke="#f44336" stroke-width="1.5" />
                        <line x1="30" y1="0" x2="0" y2="30" stroke="#f44336" stroke-width="1.5" />
                        <line x1="0" y1="30" x2="-30" y2="0" stroke="#f44336" stroke-width="1.5" />
                        <line x1="-30" y1="0" x2="0" y2="0" stroke="#f44336" stroke-width="1.5" />
                        <line x1="0" y1="-30" x2="0" y2="0" stroke="#f44336" stroke-width="1.5" />
                        <line x1="30" y1="0" x2="0" y2="0" stroke="#f44336" stroke-width="1.5" />
                        <line x1="0" y1="30" x2="0" y2="0" stroke="#f44336" stroke-width="1.5" />
                        
                        <text x="0" y="90" text-anchor="middle">Dual Graph $G^*$</text>
                    </g>
                </svg>
            </div>
            
            <div class="theorem">
                <h4>Properties of Dual Graphs:</h4>
                <ul>
                    <li>If $G$ is connected, then $G^*$ is connected.</li>
                    <li>$(G^*)^* = G$ (The dual of the dual is the original graph).</li>
                    <li>An edge $e$ is a bridge in $G$ if and only if the corresponding edge $e^*$ is a loop in $G^*$.</li>
                    <li>An edge $e$ is in a cycle in $G$ if and only if the corresponding edge $e^*$ is a cut-edge in $G^*$.</li>
                </ul>
            </div>
            
            <h3>6.6 Applications of Planar Graphs</h3>
            
            <ul>
                <li><strong>Circuit Design:</strong> Planar graphs are essential in designing printed circuit boards (PCBs) where wire crossings are to be minimized.</li>
                <li><strong>Graph Drawing:</strong> Planar graph drawing algorithms help visualize networks clearly without edge crossings.</li>
                <li><strong>Map Coloring:</strong> The famous Four Color Theorem states that any planar graph can be colored with at most four colors.</li>
                <li><strong>VLSI Design:</strong> Planar graphs help in designing integrated circuits with minimal crossings.</li>
                <li><strong>Geographic Information Systems (GIS):</strong> Planar graphs are used to represent road networks and geographical features.</li>
                <li><strong>Computer Graphics:</strong> Planar triangulations are used in 3D modeling and surface reconstruction.</li>
            </ul>
            
            <div class="note">
                <p>
                    Testing whether a graph is planar and finding a planar embedding (if one exists) can be done in linear time using algorithms such as the Hopcroft-Tarjan algorithm or the Boyer-Myrvold algorithm.
                </p>
            </div>
        </div>
        
        <!-- Continue with other sections... -->
        <div class="section" id="graph-coloring">
            <h2>7. Graph Coloring</h2>
            
            <p>
                Graph coloring is one of the most studied topics in graph theory with wide-ranging applications from scheduling problems to frequency assignment in telecommunications.
            </p>
            
            <h3>7.1 Basic Definitions</h3>
            
            <div class="definition">
                <p>
                    <strong>Vertex Coloring:</strong> An assignment of colors to the vertices of a graph such that no two adjacent vertices receive the same color.
                </p>
                <p>
                    <strong>Chromatic Number:</strong> The minimum number of colors needed for a valid vertex coloring of a graph $G$, denoted by $\chi(G)$.
                </p>
                <p>
                    <strong>$k$-Colorable:</strong> A graph is $k$-colorable if it can be colored using at most $k$ colors.
                </p>
                <p>
                    <strong>Edge Coloring:</strong> An assignment of colors to the edges of a graph such that no two adjacent edges receive the same color.
                </p>
                <p>
                    <strong>Chromatic Index:</strong> The minimum number of colors needed for a valid edge coloring of a graph $G$, denoted by $\chi'(G)$.
                </p>
                <p>
                    <strong>Face Coloring:</strong> An assignment of colors to the faces of a planar graph such that no two faces that share an edge receive the same color.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="650" height="350" viewBox="0 0 650 350">
                    <!-- Background -->
                    <rect width="650" height="350" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="325" y="30" text-anchor="middle" font-weight="bold" font-size="16">Graph Coloring Examples</text>
                    
                    <!-- Vertex Coloring Example -->
                    <g transform="translate(160, 160)">
                        <!-- Vertices -->
                        <circle cx="-75" cy="-60" r="20" fill="#f44336" stroke="#333" stroke-width="2" />
                        <circle cx="0" cy="-60" r="20" fill="#2196f3" stroke="#333" stroke-width="2" />
                        <circle cx="75" cy="-60" r="20" fill="#f44336" stroke="#333" stroke-width="2" />
                        <circle cx="-75" cy="60" r="20" fill="#4caf50" stroke="#333" stroke-width="2" />
                        <circle cx="0" cy="60" r="20" fill="#f44336" stroke="#333" stroke-width="2" />
                        <circle cx="75" cy="60" r="20" fill="#2196f3" stroke="#333" stroke-width="2" />
                        
                        <!-- Edges -->
                        <line x1="-75" y1="-60" x2="0" y2="-60" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-60" x2="75" y2="-60" stroke="#333" stroke-width="2" />
                        <line x1="-75" y1="-60" x2="-75" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-60" x2="0" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="75" y1="-60" x2="75" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="-75" y1="60" x2="0" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="60" x2="75" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="-75" y1="-60" x2="0" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-60" x2="75" y2="60" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-110" text-anchor="middle" font-weight="bold">Vertex Coloring</text>
                        <text x="0" y="110" text-anchor="middle">$\chi(G) = 3$</text>
                    </g>
                    
                    <!-- Edge Coloring Example -->
                    <g transform="translate(490, 160)">
                        <!-- Vertices -->
                        <circle cx="-75" cy="-60" r="15" fill="#e3f2fd" stroke="#333" stroke-width="2" />
                        <circle cx="0" cy="-60" r="15" fill="#e3f2fd" stroke="#333" stroke-width="2" />
                        <circle cx="75" cy="-60" r="15" fill="#e3f2fd" stroke="#333" stroke-width="2" />
                        <circle cx="-75" cy="60" r="15" fill="#e3f2fd" stroke="#333" stroke-width="2" />
                        <circle cx="0" cy="60" r="15" fill="#e3f2fd" stroke="#333" stroke-width="2" />
                        <circle cx="75" cy="60" r="15" fill="#e3f2fd" stroke="#333" stroke-width="2" />
                        
                        <!-- Edges with colors -->
                        <line x1="-75" y1="-60" x2="0" y2="-60" stroke="#f44336" stroke-width="4" />
                        <line x1="0" y1="-60" x2="75" y2="-60" stroke="#2196f3" stroke-width="4" />
                        <line x1="-75" y1="-60" x2="-75" y2="60" stroke="#2196f3" stroke-width="4" />
                        <line x1="0" y1="-60" x2="0" y2="60" stroke="#4caf50" stroke-width="4" />
                        <line x1="75" y1="-60" x2="75" y2="60" stroke="#f44336" stroke-width="4" />
                        <line x1="-75" y1="60" x2="0" y2="60" stroke="#ff9800" stroke-width="4" />
                        <line x1="0" y1="60" x2="75" y2="60" stroke="#9c27b0" stroke-width="4" />
                        
                        <!-- Title -->
                        <text x="0" y="-110" text-anchor="middle" font-weight="bold">Edge Coloring</text>
                        <text x="0" y="110" text-anchor="middle">$\chi'(G) = 5$</text>
                    </g>
                    
                    <!-- Legend -->
                    <g transform="translate(325, 290)">
                        <rect x="-200" y="0" width="400" height="40" rx="5" ry="5" fill="white" stroke="#ddd" />
                        
                        <circle cx="-170" cy="20" r="10" fill="#f44336" />
                        <text x="-150" y="25" font-size="12">Red</text>
                        
                        <circle cx="-100" cy="20" r="10" fill="#2196f3" />
                        <text x="-80" y="25" font-size="12">Blue</text>
                        
                        <circle cx="-30" cy="20" r="10" fill="#4caf50" />
                        <text x="-10" y="25" font-size="12">Green</text>
                        
                        <line x1="40" y1="15" x2="60" y2="25" stroke="#ff9800" stroke-width="3" />
                        <text x="80" y="25" font-size="12">Orange</text>
                        
                        <line x1="130" y1="15" x2="150" y2="25" stroke="#9c27b0" stroke-width="3" />
                        <text x="170" y="25" font-size="12">Purple</text>
                    </g>
                </svg>
            </div>
            
            <h3>7.2 Bounds on Chromatic Number</h3>
            
            <div class="theorem">
                <h4>Basic Bounds:</h4>
                <p>
                    For any graph $G$ with maximum degree $\Delta$: $\omega(G) \leq \chi(G) \leq \Delta(G) + 1$ where $\omega(G)$ is the clique number (size of the largest complete subgraph).
                </p>
            </div>
            
            <div class="theorem">
                <h4>Brooks' Theorem (1941):</h4>
                <p>
                    For any connected graph $G$ that is neither a complete graph nor an odd cycle: $\chi(G) \leq \Delta(G)$
                </p>
            </div>
            
            <div class="theorem">
                <h4>Chromatic Number of Special Graphs:</h4>
                <ul>
                    <li>Complete graph: $\chi(K_n) = n$</li>
                    <li>Cycle of odd length: $\chi(C_{2n+1}) = 3$</li>
                    <li>Cycle of even length: $\chi(C_{2n}) = 2$</li>
                    <li>Bipartite graph: $\chi(G) = 2$ (if non-empty)</li>
                    <li>Tree: $\chi(T) = 2$ (if non-empty)</li>
                </ul>
            </div>
            
            <h3>7.3 The Four Color Theorem</h3>
            
            <div class="theorem">
                <h4>Four Color Theorem:</h4>
                <p>
                    Every planar graph can be colored using at most four colors, i.e., $\chi(G) \leq 4$ for any planar graph $G$.
                </p>
            </div>
            
            <div class="note">
                <p>
                    The Four Color Theorem has a fascinating history. It was first conjectured in 1852 and was finally proven in 1976 by Kenneth Appel and Wolfgang Haken. The proof was groundbreaking because it was the first major mathematical theorem to be proven using extensive computer assistance.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="500" height="280" viewBox="0 0 500 280">
                    <!-- Background -->
                    <rect width="500" height="280" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="250" y="30" text-anchor="middle" font-weight="bold" font-size="14">Four Color Theorem Example: Map Coloring</text>
                    
                    <!-- Map -->
                    <g transform="translate(250, 150)">
                        <!-- Region 1 -->
                        <path d="M-150,-80 L-50,-100 L-20,-40 L-80,0 L-120,-20 Z" fill="#f44336" stroke="#333" stroke-width="2" />
                        
                        <!-- Region 2 -->
                        <path d="M-50,-100 L50,-90 L80,-30 L-20,-40 Z" fill="#2196f3" stroke="#333" stroke-width="2" />
                        
                        <!-- Region 3 -->
                        <path d="M80,-30 L100,40 L0,80 L-80,0 L-20,-40 Z" fill="#4caf50" stroke="#333" stroke-width="2" />
                        
                        <!-- Region 4 -->
                        <path d="M-150,-80 L-120,-20 L-130,70 L-180,30 Z" fill="#2196f3" stroke="#333" stroke-width="2" />
                        
                        <!-- Region 5 -->
                        <path d="M-120,-20 L-80,0 L-40,60 L-90,90 L-130,70 Z" fill="#9c27b0" stroke="#333" stroke-width="2" />
                        
                        <!-- Region 6 -->
                        <path d="M-80,0 L0,80 L-40,60 Z" fill="#f44336" stroke="#333" stroke-width="2" />
                        
                        <!-- Region 7 -->
                        <path d="M-40,60 L0,80 L-90,90 Z" fill="#2196f3" stroke="#333" stroke-width="2" />
                        
                        <!-- Region names -->
                        <text x="-100" y="-50" text-anchor="middle" font-size="12" fill="white">R1</text>
                        <text x="0" y="-70" text-anchor="middle" font-size="12">R2</text>
                        <text x="30" y="10" text-anchor="middle" font-size="12">R3</text>
                        <text x="-150" y="10" text-anchor="middle" font-size="12">R4</text>
                        <text x="-100" y="30" text-anchor="middle" font-size="12" fill="white">R5</text>
                        <text x="-40" y="30" text-anchor="middle" font-size="12" fill="white">R6</text>
                        <text x="-45" y="80" text-anchor="middle" font-size="12">R7</text>
                    </g>
                    
                    <!-- Legend -->
                    <g transform="translate(250, 250)">
                        <rect x="-220" y="0" width="440" height="20" rx="5" ry="5" fill="white" stroke="#ddd" />
                        
                        <rect x="-200" y="5" width="15" height="10" fill="#f44336" />
                        <text x="-180" y="15" font-size="12" text-anchor="start">Red (R1,R6)</text>
                        
                        <rect x="-90" y="5" width="15" height="10" fill="#2196f3" />
                        <text x="-70" y="15" font-size="12" text-anchor="start">Blue (R2,R4,R7)</text>
                        
                        <rect x="30" y="5" width="15" height="10" fill="#4caf50" />
                        <text x="50" y="15" font-size="12" text-anchor="start">Green (R3)</text>
                        
                        <rect x="130" y="5" width="15" height="10" fill="#9c27b0" />
                        <text x="150" y="15" font-size="12" text-anchor="start">Purple (R5)</text>
                    </g>
                </svg>
            </div>
            
            <h3>7.4 Edge Coloring</h3>
            
            <div class="theorem">
                <h4>Vizing's Theorem (1964):</h4>
                <p>
                    For any simple graph $G$, the chromatic index $\chi'(G)$ is either $\Delta(G)$ or $\Delta(G) + 1$, where $\Delta(G)$ is the maximum degree of $G$.
                </p>
            </div>
            
            <div class="theorem">
                <h4>Edge Coloring of Bipartite Graphs:</h4>
                <p>
                    For any bipartite graph $G$, $\chi'(G) = \Delta(G)$.
                </p>
                <p>
                    This follows from König's Line Coloring Theorem and can be proven constructively using algorithms like the Alternating Path Algorithm.
                </p>
            </div>
            
            <h3>7.5 Algorithms for Graph Coloring</h3>
            
            <div class="algorithm">
                <h4>Greedy Coloring Algorithm:</h4>
                <ol>
                    <li>Order the vertices $v_1, v_2, \ldots, v_n$.</li>
                    <li>Assign color 1 to $v_1$.</li>
                    <li>For $i = 2$ to $n$:
                        <ul>
                            <li>Assign to $v_i$ the smallest color not used by any of its already-colored neighbors.</li>
                        </ul>
                    </li>
                </ol>
                <p>
                    This simple algorithm guarantees a coloring with at most $\Delta(G) + 1$ colors but may not achieve the chromatic number.
                </p>
            </div>
            
            <div class="algorithm">
                <h4>Welsh-Powell Algorithm:</h4>
                <ol>
                    <li>Sort vertices by degree in descending order.</li>
                    <li>Assign color 1 to the first vertex and to all vertices not adjacent to it.</li>
                    <li>Assign color 2 to the first uncolored vertex and to all vertices not adjacent to it.</li>
                    <li>Continue until all vertices are colored.</li>
                </ol>
                <p>
                    This approach often performs better than the basic greedy algorithm in practice.
                </p>
            </div>
            
            <div class="note">
                <p>
                    Finding the chromatic number of a graph is NP-hard, meaning there is no known polynomial-time algorithm guaranteed to find the optimal coloring for all graphs. However, for specific classes of graphs like bipartite graphs, perfect graphs, and interval graphs, efficient algorithms exist.
                </p>
            </div>
            
            <h3>7.6 Applications of Graph Coloring</h3>
            
            <ul>
                <li><strong>Scheduling:</strong> Scheduling conflicting events using vertex coloring, where vertices represent events and edges represent conflicts.</li>
                <li><strong>Register Allocation:</strong> Optimizing the use of CPU registers in compilers using graph coloring algorithms.</li>
                <li><strong>Frequency Assignment:</strong> Assigning frequencies to radio or mobile phone transmitters to avoid interference.</li>
                <li><strong>Map Coloring:</strong> Coloring geographical maps so that no adjacent regions have the same color.</li>
                <li><strong>Sudoku:</strong> The popular puzzle game Sudoku can be viewed as a graph coloring problem.</li>
                <li><strong>Pattern Recognition:</strong> Used in some image segmentation and pattern recognition algorithms.</li>
                <li><strong>Timetabling:</strong> Creating conflict-free timetables for schools and universities.</li>
            </ul>
            
            <div class="example">
                <h4>Scheduling Example:</h4>
                <p>
                    Consider scheduling final exams for university courses. We can represent each course as a vertex, with an edge between two courses if they share at least one student (and thus cannot be scheduled in the same time slot). A valid vertex coloring corresponds to a feasible exam schedule, with courses of the same color scheduled in the same time slot.
                </p>
            </div>
        </div>
        
        <div class="section" id="mathematical-background">
            <h2>8. Mathematical Background of Graph Theory</h2>
            
            <p>
                Graph theory is built upon several branches of mathematics, giving it a rich theoretical foundation. Understanding these mathematical underpinnings helps provide deeper insights into graph properties and algorithms.
            </p>
            
            <h3>8.1 Set Theory and Graph Theory</h3>
            
            <div class="definition">
                <p>
                    <strong>Set Representation:</strong> A graph $G = (V, E)$ consists of a set $V$ of vertices and a set $E$ of edges, where $E \subseteq V \times V$ for directed graphs or $E \subseteq \{\{u, v\} | u, v \in V, u \neq v\}$ for undirected graphs.
                </p>
            </div>
            
            <p>
                The set-theoretic representation of graphs allows us to apply set operations and properties to analyze graph structures. For example:
            </p>
            
            <ul>
                <li><strong>Subgraphs:</strong> A graph $H = (V_H, E_H)$ is a subgraph of $G = (V_G, E_G)$ if $V_H \subseteq V_G$ and $E_H \subseteq E_G$.</li>
                <li><strong>Graph Union:</strong> The union of two graphs $G_1 = (V_1, E_1)$ and $G_2 = (V_2, E_2)$ is the graph $G_1 \cup G_2 = (V_1 \cup V_2, E_1 \cup E_2)$.</li>
                <li><strong>Graph Intersection:</strong> The intersection of two graphs $G_1 = (V_1, E_1)$ and $G_2 = (V_2, E_2)$ is the graph $G_1 \cap G_2 = (V_1 \cap V_2, E_1 \cap E_2)$.</li>
                <li><strong>Graph Complement:</strong> The complement of a graph $G = (V, E)$ is the graph $\bar{G} = (V, \{\{u,v\} | u,v \in V, u \neq v, \{u,v\} \not\in E\})$.</li>
            </ul>
            
            <div class="visualization">
                <svg width="750" height="350" viewBox="0 0 750 350">
                    <!-- Background -->
                    <rect width="750" height="350" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="375" y="30" text-anchor="middle" font-weight="bold" font-size="16">Set Operations on Graphs</text>
                    
                    <!-- Graph G1 -->
                    <g transform="translate(150, 150)">
                        <!-- Vertices -->
                        <circle cx="-50" cy="-50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="50" cy="-50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="0" cy="50" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-50" y="-45" text-anchor="middle" font-weight="bold">1</text>
                        <text x="50" y="-45" text-anchor="middle" font-weight="bold">2</text>
                        <text x="0" y="55" text-anchor="middle" font-weight="bold">3</text>
                        
                        <!-- Edges -->
                        <line x1="-50" y1="-50" x2="50" y2="-50" stroke="#333" stroke-width="2" />
                        <line x1="-50" y1="-50" x2="0" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="50" y1="-50" x2="0" y2="50" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-90" text-anchor="middle" font-weight="bold">Graph G₁</text>
                        <text x="0" y="90" text-anchor="middle">V₁ = {1,2,3}, E₁ = {{1,2},{1,3},{2,3}}</text>
                    </g>
                    
                    <!-- Graph G2 -->
                    <g transform="translate(375, 150)">
                        <!-- Vertices -->
                        <circle cx="-50" cy="-50" r="15" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" />
                        <circle cx="50" cy="-50" r="15" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" />
                        <circle cx="0" cy="50" r="15" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" />
                        <circle cx="50" cy="50" r="15" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-50" y="-45" text-anchor="middle" font-weight="bold">1</text>
                        <text x="50" y="-45" text-anchor="middle" font-weight="bold">2</text>
                        <text x="0" y="55" text-anchor="middle" font-weight="bold">3</text>
                        <text x="50" y="55" text-anchor="middle" font-weight="bold">4</text>
                        
                        <!-- Edges -->
                        <line x1="-50" y1="-50" x2="50" y2="-50" stroke="#333" stroke-width="2" />
                        <line x1="50" y1="-50" x2="50" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="50" x2="50" y2="50" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-90" text-anchor="middle" font-weight="bold">Graph G₂</text>
                        <text x="0" y="90" text-anchor="middle">V₂ = {1,2,3,4}, E₂ = {{1,2},{2,4},{3,4}}</text>
                    </g>
                    
                    <!-- Graph G1 ∪ G2 -->
                    <g transform="translate(600, 150)">
                        <!-- Vertices -->
                        <circle cx="-70" cy="-50" r="15" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
                        <circle cx="0" cy="-50" r="15" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
                        <circle cx="-35" cy="50" r="15" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
                        <circle cx="35" cy="50" r="15" fill="#e8f5e9" stroke="#4caf50" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-70" y="-45" text-anchor="middle" font-weight="bold">1</text>
                        <text x="0" y="-45" text-anchor="middle" font-weight="bold">2</text>
                        <text x="-35" y="55" text-anchor="middle" font-weight="bold">3</text>
                        <text x="35" y="55" text-anchor="middle" font-weight="bold">4</text>
                        
                        <!-- Edges -->
                        <line x1="-70" y1="-50" x2="0" y2="-50" stroke="#333" stroke-width="2" />
                        <line x1="-70" y1="-50" x2="-35" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-50" x2="-35" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="0" y1="-50" x2="35" y2="50" stroke="#333" stroke-width="2" />
                        <line x1="-35" y1="50" x2="35" y2="50" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-90" text-anchor="middle" font-weight="bold">Graph G₁ ∪ G₂</text>
                        <text x="0" y="90" text-anchor="middle">Union of G₁ and G₂</text>
                    </g>
                    
                    <!-- Graph G1 ∩ G2 -->
                    <g transform="translate(195, 250)">
                        <!-- Vertices -->
                        <circle cx="-50" cy="0" r="15" fill="#fff3e0" stroke="#ff9800" stroke-width="2" />
                        <circle cx="50" cy="0" r="15" fill="#fff3e0" stroke="#ff9800" stroke-width="2" />
                        <circle cx="0" cy="70" r="15" fill="#fff3e0" stroke="#ff9800" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-50" y="5" text-anchor="middle" font-weight="bold">1</text>
                        <text x="50" y="5" text-anchor="middle" font-weight="bold">2</text>
                        <text x="0" y="75" text-anchor="middle" font-weight="bold">3</text>
                        
                        <!-- Edges -->
                        <line x1="-50" y1="0" x2="50" y2="0" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-40" text-anchor="middle" font-weight="bold">Graph G₁ ∩ G₂</text>
                        <text x="0" y="110" text-anchor="middle">Intersection of G₁ and G₂</text>
                    </g>
                </svg>
            </div>
            
            <div class="note">
                <p>
                    <strong>Power of Set Theory:</strong> The set-theoretic approach allows us to reason about graphs using established mathematical tools. For example, the handshaking lemma, which states that the sum of vertex degrees equals twice the number of edges, can be derived from basic counting principles in set theory.
                </p>
            </div>
            
            <h3>8.2 Linear Algebra and Matrices in Graph Theory</h3>
            
            <p>
                Matrices provide powerful ways to represent and analyze graphs. Several matrix representations are commonly used:
            </p>
            
            <div class="definition">
                <p>
                    <strong>Adjacency Matrix:</strong> For a graph with $n$ vertices, the adjacency matrix $A$ is an $n \times n$ matrix where $A_{ij} = 1$ if there is an edge from vertex $i$ to vertex $j$, and $A_{ij} = 0$ otherwise. For weighted graphs, $A_{ij}$ can represent the weight of the edge.
                </p>
                <p>
                    <strong>Incidence Matrix:</strong> For a graph with $n$ vertices and $m$ edges, the incidence matrix $B$ is an $n \times m$ matrix where $B_{ij} = 1$ if vertex $i$ is incident to edge $j$, and $B_{ij} = 0$ otherwise. For directed graphs, $B_{ij} = -1$ if vertex $i$ is the tail of edge $j$, and $B_{ij} = 1$ if vertex $i$ is the head of edge $j$.
                </p>
                <p>
                    <strong>Laplacian Matrix:</strong> The Laplacian matrix $L$ of a graph is defined as $L = D - A$, where $D$ is the degree matrix (a diagonal matrix with the degree of each vertex) and $A$ is the adjacency matrix.
                </p>
            </div>
            
            <div class="visualization">
                <svg width="750" height="350" viewBox="0 0 750 350">
                    <!-- Background -->
                    <rect width="750" height="350" fill="#f8f9fa" rx="10" ry="10" />
                    
                    <!-- Title -->
                    <text x="375" y="30" text-anchor="middle" font-weight="bold" font-size="16">Matrix Representations of Graphs</text>
                    
                    <!-- Example Graph -->
                    <g transform="translate(150, 150)">
                        <!-- Vertices -->
                        <circle cx="-60" cy="-60" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="60" cy="-60" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="60" cy="60" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        <circle cx="-60" cy="60" r="15" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" />
                        
                        <!-- Vertex labels -->
                        <text x="-60" y="-55" text-anchor="middle" font-weight="bold">1</text>
                        <text x="60" y="-55" text-anchor="middle" font-weight="bold">2</text>
                        <text x="60" y="65" text-anchor="middle" font-weight="bold">3</text>
                        <text x="-60" y="65" text-anchor="middle" font-weight="bold">4</text>
                        
                        <!-- Edges -->
                        <line x1="-60" y1="-60" x2="60" y2="-60" stroke="#333" stroke-width="2" />
                        <line x1="60" y1="-60" x2="60" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="60" y1="60" x2="-60" y2="60" stroke="#333" stroke-width="2" />
                        <line x1="-60" y1="60" x2="-60" y2="-60" stroke="#333" stroke-width="2" />
                        <line x1="-60" y1="-60" x2="60" y2="60" stroke="#333" stroke-width="2" />
                        
                        <!-- Title -->
                        <text x="0" y="-90" text-anchor="middle" font-weight="bold">Example Graph</text>
                    </g>
                    
                    <!-- Adjacency Matrix -->
                    <g transform="translate(450, 120)">
                        <!-- Title -->
                        <text x="0" y="-70" text-anchor="middle" font-weight="bold">Adjacency Matrix</text>
                        
                        <!-- Matrix -->
                        <rect x="-80" y="-50" width="160" height="100" fill="white" stroke="#333" stroke-width="1" />
                        
                        <!-- Column headers -->
                        <text x="-60" y="-60" text-anchor="middle" font-size="12">1</text>
                        <text x="-20" y="-60" text-anchor="middle" font-size="12">2</text>
                        <text x="20" y="-60" text-anchor="middle" font-size="12">3</text>
                        <text x="60" y="-60" text-anchor="middle" font-size="12">4</text>
                        
                        <!-- Row headers -->
                        <text x="-90" y="-30" text-anchor="middle" font-size="12">1</text>
                        <text x="-90" y="-5" text-anchor="middle" font-size="12">2</text>
                        <text x="-90" y="20" text-anchor="middle" font-size="12">3</text>
                        <text x="-90" y="45" text-anchor="middle" font-size="12">4</text>
                        
                        <!-- Matrix values -->
                        <text x="-60" y="-30" text-anchor="middle">0</text>
                        <text x="-20" y="-30" text-anchor="middle">1</text>
                        <text x="20" y="-30" text-anchor="middle">1</text>
                        <text x="60" y="-30" text-anchor="middle">1</text>
                        
                        <text x="-60" y="-5" text-anchor="middle">1</text>
                        <text x="-20" y="-5" text-anchor="middle">0</text>
                        <text x="20" y="-5" text-anchor="middle">1</text>
                        <text x="60" y="-5" text-anchor="middle">0</text>
                        
                        <text x="-60" y="20" text-anchor="middle">1</text>
                        <text x="-20" y="20" text-anchor="middle">1</text>
                        <text x="20" y="20" text-anchor="middle">0</text>
                        <text x="60" y="20" text-anchor="middle">1</text>
                        
                        <text x="-60" y="45" text-anchor="middle">1</text>
                        <text x="-20" y="45" text-anchor="middle">0</text>
                        <text x="20" y="45" text-anchor="middle">1</text>
                        <text x="60" y="45" text-anchor="middle">0</text>
                        
                        <!-- Horizontal grid lines -->
                        <line x1="-80" y1="-25" x2="80" y2="-25" stroke="#333" stroke-width="1" />
                        <line x1="-80" y1="0" x2="80" y2="0" stroke="#333" stroke-width="1" />
                        <line x1="-80" y1="25" x2="80" y2="25" stroke="#333" stroke-width="1" />
                        
                        <!-- Vertical grid lines -->
                        <line x1="-40" y1="-50" x2="-40" y2="50" stroke="#333" stroke-width="1" />
                        <line x1="0" y1="-50" x2="0" y2="50" stroke="#333" stroke-width="1" />
                        <line x1="40" y1="-50" x2="40" y2="50" stroke="#333" stroke-width="1" />
                    </g>
                    
                    <!-- Laplacian Matrix -->
                    <g transform="translate(450, 250)">
                        <!-- Title -->
                        <text x="0" y="-30" text-anchor="middle" font-weight="bold">Laplacian Matrix</text>
                        
                        <!-- Matrix -->
                        <rect x="-80" y="-10" width="160" height="100" fill="white" stroke="#333" stroke-width="1" />
                        
                        <!-- Row headers -->
                        <text x="-90" y="10" text-anchor="middle" font-size="12">1</text>
                        <text x="-90" y="35" text-anchor="middle" font-size="12">2</text>
                        <text x="-90" y="60" text-anchor="middle" font-size="12">3</text>
                        <text x="-90" y="85" text-anchor="middle" font-size="12">4</text>
                        
                        <!-- Matrix values -->
                        <text x="-60" y="10" text-anchor="middle">3</text>
                        <text x="-20" y="10" text-anchor="middle">-1</text>
                        <text x="20" y="10" text-anchor="middle">-1</text>
                        <text x="60" y="10" text-anchor="middle">-1</text>
                        
                        <text x="-60" y="35" text-anchor="middle">-1</text>
                        <text x="-20" y="35" text-anchor="middle">2</text>
                        <text x="20" y="35" text-anchor="middle">-1</text>
                        <text x="60" y="35" text-anchor="middle">0</text>
                        
                        <text x="-60" y="60" text-anchor="middle">-1</text>
                        <text x="-20" y="60" text-anchor="middle">-1</text>
                        <text x="20" y="60" text-anchor="middle">3</text>
                        <text x="60" y="60" text-anchor="middle">-1</text>
                        
                        <text x="-60" y="85" text-anchor="middle">-1</text>
                        <text x="-20" y="85" text-anchor="middle">0</text>
                        <text x="20" y="85" text-anchor="middle">-1</text>
                        <text x="60" y="85" text-anchor="middle">2</text>
                        
                        <!-- Horizontal grid lines -->
                        <line x1="-80" y1="15" x2="80" y2="15" stroke="#333" stroke-width="1" />
                        <line x1="-80" y1="40" x2="80" y2="40" stroke="#333" stroke-width="1" />
                        <line x1="-80" y1="65" x2="80" y2="65" stroke="#333" stroke-width="1" />
                        
                        <!-- Vertical grid lines -->
                        <line x1="-40" y1="-10" x2="-40" y2="90" stroke="#333" stroke-width="1" />
                        <line x1="0" y1="-10" x2="0" y2="90" stroke="#333" stroke-width="1" />
                        <line x1="40" y1="-10" x2="40" y2="90" stroke="#333" stroke-width="1" />
                        
                        <!-- Column headers -->
                        <text x="-60" y="-20" text-anchor="middle" font-size="12">1</text>
                        <text x="-20" y="-20" text-anchor="middle" font-size="12">2</text>
                        <text x="20" y="-20" text-anchor="middle" font-size="12">3</text>
                        <text x="60" y="-20" text-anchor="middle" font-size="12">4</text>
                    </g>
                </svg>
            </div>
            
            <div class="theorem">
                <h4>Properties of Graph Matrices:</h4>
                <ul>
                    <li>
                        <strong>Eigenvalues and Connectivity:</strong> The second smallest eigenvalue of the Laplacian matrix (known as the algebraic connectivity or Fiedler value) provides information about the graph's connectivity. A graph is connected if and only if this eigenvalue is greater than zero.
                    </li>
                    <li>
                        <strong>Adjacency Matrix Powers:</strong> The $(i,j)$-entry of $A^k$ gives the number of walks of length $k$ from vertex $i$ to vertex $j$.
                    </li>
                    <li>
                        <strong>Spectral Radius:</strong> The spectral radius (largest eigenvalue) of the adjacency matrix is related to several important graph properties, including the chromatic number and maximum degree.
                    </li>
                </ul>
            </div>
            
            <p>
                Linear algebra provides powerful tools for analyzing graph structure and properties. For instance, eigenvalues of the adjacency and Laplacian matrices reveal significant information about connectivity, partitioning, and dynamic processes on graphs.
            </p>
            
            <div class="example">
                <h4>Using Matrices for Graph Analysis:</h4>
                <p>
                    Consider the problem of counting triangles in a graph. The number of triangles in an undirected graph is given by:
                    $\frac{1}{6}\text{trace}(A^3)$
                    where $A$ is the adjacency matrix and trace is the sum of diagonal elements.
                </p>
                <p>
                    This works because $A^3$ counts the number of paths of length 3 between vertices, and when the start and end vertices are the same ($i=j$), we have a closed walk of length 3, which corresponds to a triangle in the graph.
                </p>
            </div>
        </div>
        <div class="section" id="references">
            <h2>References</h2>
            <ol>
                <li>Diestel, R. (2024). <em>Graph Theory (6th Edition)</em>. Graduate Texts in Mathematics, Springer-Verlag.</li>
                <li>Bondy, J.A., & Murty, U.S.R. (2008). <em>Graph Theory</em>. Graduate Texts in Mathematics, Springer-Verlag.</li>
                <li>West, D.B. (2000). <em>Introduction to Graph Theory (2nd Edition)</em>. Prentice Hall.</li>
                <li>Euler, L. (1736). "Solutio problematis ad geometriam situs pertinentis." <em>Commentarii Academiae Scientiarum Imperialis Petropolitanae</em>, 8, 128-140.</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Add any JavaScript functionality here
        // For example, collapsible sections, interactive visualizations, etc.
    </script>
</body>
</html> 