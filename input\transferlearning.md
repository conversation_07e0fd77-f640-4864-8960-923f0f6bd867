media_5 (2)_原文
2025年06月20日 16:47
发言人1   00:04
I'm going to be talking about joint work with a number of people. <PERSON>, who was one of my advisors at CMU when this work was done. <PERSON> did the early autonomous vehicle navigation work. <PERSON>, who was a cosgrave student in <PERSON>'s lab, <PERSON>, who was a visiting scientist at the time in <PERSON>'s lab, Virginia DEA, who is a professor done at UCSD working in coxsey. <PERSON>, one of my grad students at Cornell, and a number of other people. 

发言人1   00:30
So as was mentioned I'm going to do a very brief history of transfer learning before I jump into tricks of the trade. 

发言人1   00:38
So the first thing I could find which looked like multitask learning or transfer learning on a neural net, was this nettalk system that was trained in 1986. So they had about 200 different output classes that they needed to predict. They weren't all mutually exclusive, but they did train them on 1 net because they really couldn't train 200 separate nets at the time and get it to work. They weren't specifically going for transfer in this net, but they do get some transfer, even though I think maybe they didn't know it at the time. So this is the first example I've seen a clear transfer happening in a neural net. 

发言人1   01:13
So, and then <PERSON><PERSON>, a <PERSON><PERSON> a few years later, couldn't get decision trees to match the performance of this neural net. And they wondered why. And one of the explanations that they considered was maybe it was the fact that all 200 things were being trained on one net and that the decision trees were trained separately on 200 different tasks. Maybe that was the reason why this thing worked. 

发言人1   01:35
So then sudharz holden and kyrgiz, they started doing things like trying to inject rules into neural nets, like trying to make neural nets do our bidding when they were learning in 1990. And at about the same time, Abu <PERSON> was injecting hints like he would want to convince the net to be monotone with respect to some input. And he was doing that through the output. So that's a form of inductive transfer. 

发言人1   01:59
Dean Pomelo, who did this autonomous vehicle navigation neural net, which is one of the systems that put neural nets on the map in the 90s, he was using multiple output representations. And although he didn't talk about it as a form of inductive transfer, he was getting extra accuracy in his system because he had these multiple outputs on his network. 

发言人1   02:18
Laurie and Pratt, at about the same time, was trying to speed up neural nets, were really slow to train back then. So she was adding extra tasks onto the output of a network in order to improve the speed with which these things trained. There was also work on speed up learning by the two sharkies at about that same time, and then Mark Ring was working on continual learning. He had a system called Child back then, which was trying to sort of learn a sequence of problems. So that's an early form of inductive transfer that fits in with the never ending learning theme of the workshop. 

发言人1   02:53
And then there's my work on multitask learning. I think this is probably the first work that sort of recognized. While these extra tasks can be used to improve generalization. So when you really should go out and seek extra tasks, if they're related in some beneficial way to the main things you're trying to learn. So I focused on that. And most of my work was in neural nets, although I did some work in k near-urban neighbor and multi test learning for decision trees at about the same time, Sebastian thrun and Tom Mitchell were working on explanation based Sed neural nets, which there is a form of inductive transfer happening inside those networks. 

发言人1   03:28
And then Virginia Desa, who was sort of Cogs Sai motivated, was worried about things like you have a visual stream and you might see a cow open its mouth, and then you might have an audio stream where you would hear a cow make a sound like moo. And then by tying these streams together and trying to have them cole-trained other, you could actually do better than if you worked on the stream separately. So that's a form of inductive transfer. 

发言人1   03:53
And then at about this time, Jonathan Baxter was starting to look into representation learning. He was doing more theoretical work, but he was also starting to look at this inductive transfer multitask setting. And then Tom Mitchell and Sebastian Throne again started working on this learning one more thing kind of problem. So, so what what Sebastian will later call learning to learn and schmidhuber was also sort working on how to learn strategies for learning. So, so we wouldn't call back then, we might have called it meta-learning, although now it's different from what we do as meta-learning. And then just a few more things. So Tom dieteri again got back into things and they were doing these error correcting output codes on neural networks. And one of the reasons I think why those systems were working so well is, again, because it was a form of multitask learning, inductive transfer between these multiple representations of the same tasks. 

发言人1   04:46
Leo bryman, who's famous for bagging in random forests. So even Leo did some work called Kurds and way on inductive transfer, then Sebastian thrun doing his thesis on learning to learn. So that's well, i.n.n.n. you lifeline learning work. 

发言人1   05:05
Danny Silver started working on parallel transfer in neural nets, how you could take what was trained in one net and then move it serially to another net. A grad student, Joseph O'sullivan, started working on multitask learning in K nearest neighbor and had to cluster tasks to find sets of related tasks. This was a joint work with Sebastian. And then this is some work I'll mention later. People started working on combining this with ensembles. Then Tom and Aram Blum worked on cole-trained, which is another form of inductive transfer. 

发言人1   05:44
Then there started to be some more theoretical work, things started to go Bayesian. We started seeing multi test learning in Svms, and then we start seeing the kernel trick for this. We start seeing multitask learning in GP, more work in GP. And then of course, what happens is we hit the present day where we're starting to do deep learning. And I think deep learning is just breathing a whole new life into any sort of inductive transfer. And that's because these deep nets have the capacity to learn sort of anything we throw at them. And we now have data sets and horsepower to also do it. So we're seeing some very exciting new things that we never dreamed of doing back in the early days. 

发言人1   06:26
Okay, sorry, I apologize for that somewhat boring bullet list of things, but it gives you an idea that this stuff was sort of happening in a variety of ways back in the 90s. Let me briefly bore you with how I ended up doing this. It is a transfer kind of thing itself. 

发言人1   06:42
So Tom Mitchell was one of my advisors. Herb Simon was my other advisor, was easy for me to see how to work with Tom. We both did supervised learning Herb Simon though at the time, was doing scientific discovery. So in scientific discovery is like law of forming, you know PV equals NRT, the universal law of gravitation, things like that. And it was harder for me to see what I would do as a machine learning person with herb. 

发言人1   07:08
So I asked myself the question at some point, neural nets where we're getting popular in the early 90s, how would I use a neural net to do scientific discovery? And that seemed like a crazy question, because neural nets are like the ultimate black box non-spartan etric model, and laws are the exact opposite, right? You really need a mathematical formula or a statement that generalizes to many situations. And I couldn't figure out know how you would ever use a neural net to do a law kind of thing. 

发言人1   07:36
And it occurred to me, well, suppose I want a neural net to learn something about gravity. So I might give it different positions of the moon, say, going around the Earth. Now, what the neural net's going to do is it's just going to curve it. It's just going to find a sort of circle or ellipse that fits the data. It's not going to learn the universal law of gravitation. And I thought, well, the only thing forced this neural net to learn some general principle like a law would be to have it work on multiple gravitational systems at the same time. 

发言人1   08:04
That is the moon going around the Earth, the Earth going around the sun, Jupiter going around the sun, the rings of Saturn going around us, going around the galaxy. And I thought maybe if I were to train a neural net simultaneously on a bunch of different gravitational systems with some shared representation, we could somehow force that neural net to learn more general principles. Now, I think that's actually quite hard. And the truth is, I still don't know how to do that. So that's not what I ended up doing. 

发言人1   08:34
But at some point, I kind of realized, wow, this idea of having multiple outputs and then forcing better generalization in the network with a shared representation might actually be a useful thing. And I ended up doing that not in scientific discovery, where it really is quite difficult, I think, with the neural net. But I ended up going back to the Tom Mitchell side of things and doing it in supervised learning. So that for me was sort of transfer I'm thinking about to something like a neural net to learn general principles for scientific discovery. And I sort of stumbled upon maybe multiple outputs could help the network generalize better, and then ended up doing that for supervised learning. 

发言人1   09:12
So let me read this quote. I want to make sure that a lot of other people get credit for the ideas that were happening in the early 90s in inductive transfer. This is Rich Sutton, who is famous for reinforcement learning if you know that field I'll just paraphrase this, everyone knows good representations are the key to good learning. Why then has constructive induction basically failed, not made much success? People learn amazingly fast because they bring good representations to the problem, representations that were learned on previous problems, but the standard machine learning methodology is to consider a single concept to be learned at a time, that's the crux of the problem, this is not the way to do constructive induction. People get a better sense of a real task when they see other task. When we do this, I think we find the key differences that for all practical purposes, people face not one task, but a series of tasks, the different tasks have different solutions, but they often share the same useful representations, if you can come to the nth test with a great representation, learn from the preceding n -1 test, you can learn dramatically faster and better on the new task. A system without this benefit will learn no faster on the nth task than it on the preceding test. 

发言人1   10:29
So this is a quote from rich Sutton, this is just a year or two after I started doing my work in multitask learning. You can see a lot of people were thinking about these sorts of ideas in the 90s. So okay, sorry to bore you with that, that little history. Let's jump into tricks of the trade. I'm going to spend a little more time on the first few tricks, and then this afternoon I'll actually very, very quickly lightning talk style, go through the other tricks. We'll do a lot more tricks in the afternoon, but I wanted to sort of you get us thinking this morning about fundamentals of multitask learning. So the first few tricks were selected to help us do that. So here's some of the things we'll talk about when should you pool resources with NATO or when should you be an isolationist? 

发言人1   11:13
The joys of polygamy, multitask learning means never having to say you're sorry. They say that breaking up is hard to do. It's not always true, the future isn't what it's cracked up to be. I hope you're all paying attention. 

发言人1   11:29
Mtl doesn't necessarily mean one for all and all for one, and believe it or not, there is more to machine learning than just deep learning. That's a very controversial statement. Okay, so let's jump into the first of these, and I apologize, my titles are probably much more exciting than the material I'm going to present. 

发言人1   11:49
So let's talk about pooling versus isolation. So here's a thought experiment, so I'm going to give you a data set with 100000 records in it. So 100000 patients, 50000 of these are male patients, and 50000 of these are female patients. Now, should you train separate or trying to predict risk of something or the success of some treatment, whatever it is, should you train separate models for male and for female? So each of those models will then be trained on 50000 patients. But we'll have the benefit that if learning this problem for males and females is different, we'll have learned separate models. So should we do that or should we pull the data and just train one model? 

发言人1   12:35
Okay, so this is a model that now has 100000 patients going into it, and it's just trying to predict risk and there's no inductive transfer happening yet, right? That's two separate models. And this is one model just being trained on pooled data. Now, when you train this model, should we or shouldn't we include whether the patient is male or female as one of the inputs? 

发言人1   13:00
Okay, so should there be an extra input here going to the hidden layer, for example, that says the patient's male or female? And I think most of us would say, yes, that probably should be there. So an interesting question is, does that make it inductive transfer suddenly? Good people aren't sure. So let's think about this. When should you do this? Separate models versus 1 joint model? 

发言人1   13:25
Suppose you're trying to predict things like OVC cancer, prostate cancer, stuff like that. Well, pretty clearly OVC cancer, so female learning problem, prostate cancer is a male learning problem, probably doesn't make sense to pull the data, right? You should learn separate models for these two things. Okay? So I think most of us would agree with that. 

发言人1   13:45
Suppose you're trying to predict breast cancer, it turns out, although the majority of breast cancer is in women, 5% to 10% of breast cancer actually does occur in men. So now I'm not a doctor, I don't know, you know, if the disease in men and women are different or the same, but it's possible that you would actually benefit by training the model somehow to include men because it does occur in men. So, and that might be useful. 

发言人1   14:11
Heart disease, it used to be believed years and years ago that actually heart disease was more of a male problem and wasn't a female problem. That was a mistake. So it's now not believed that the disease is that different in men and women, that the prevalence of the disease is common in both genders. And now I think we would probably argue we probably should train one model for both men and for women, we would benefit by doing that. 

发言人1   14:37
Suppose however, we have pre and postmenopausal women. Well, I don't know, maybe the disease is different in pre and postmenopausal women. And maybe we should not only be training separate models for women, but maybe they should even be separate models for pre and postmenopausal women. Or maybe those should just be inputs to the model. Okay, so I think you can see this very simple question, You know I've just given you a bunch of data. I've given you one magic feature, gender, this very simple question of whether you should train one model or two models and whether that model should have his input or not. The gender feature, you know, isn't that easy thing to decide and it requires expertise. And by the way, nobody really has that expertise. 

发言人1   15:20
This is more of a machine learning question than it is a medical question, although doctors could perhaps give us information that would be useful in making this decision. If they say, hey, OVC cancer, prostate cancer, completely disjoint, heart disease, remarkably similar in the two genders, might as well pull the data pre postmenopausal. It actually doesn't make a difference for heart disease, so don't worry about that. They might be able to tell us things like that. 

发言人1   15:42
I think all of us who are good machine learning researchers would probably want to try it multiple ways and see what works. Okay, this is all single task learning, okay? We haven't done anything that's transferred yet, maybe, okay, multitask learning gives us some sort of intermediate option here. 

发言人1   16:01
Okay, so we can train one model on both male and female risk. Which we might view as being two related tasks. So it's different from pulling the data. It's different from training to disjoint models. 

发言人1   16:16
But there's at least two ways of doing this, even in the classic Mtl that we used to do back in the 90s. So one of them, which I think most of you are thinking of, and this is the kind of work I did, is we'll train one model on all 100000 patients, but we'll have two separate outputs on the model. One is for male and one is for female. So both outputs are learning risk of the same disease, but we're just going to separate male and female. And because these things share a hidden layer, there's going to be some stuff learned in the shared hidden layer that hopefully is beneficial to both of these tasks. But if those tasks are also somewhat different because of these different weights that are learned, it's possible that some of what this task needs will be ignored by this task and vice versa. So the task can separate as necessary, but they can also share if that's beneficial. 

发言人1   16:59
So that's the basic idea behind multi tested learning, as all of you know. So that's one way to do it. 

发言人1   17:06
There's another way to do it though, and this is kind of like Anthony platonius new work on providing context to a model and then using a generator function. But here we just provide, we're going to pull the data in some sense, but we're going to provide this male, female as some special signal going into the network. And you might think, well, that just looks like single test learning with an extra feature. And in some ways it is, but it's going to be interesting. 

发言人1   17:34
Just look at this for a second, viewed from like 30000 feet, these are essentially the same methods, right? I mean, both have 100000 patients, the multitask net, and then they both see 100000 patients. They have the same input features, they have the same training signals, the same outcomes, right? They both get to know whether a patient is male or female in the multitask output net. There's just two different output. So they know whether the training signal is coming from one side or the other side. In the other case, there's a special input which is telling it whether it's a male or female. 

发言人1   18:07
Okay, so now you might think, oh, but that's not multitask learning, providing it as an input. 

发言人1   18:11
Well, let's think about this. Suppose you're training decision trees on this. Now suppose the decision tree always splits on the male and female feature at the root of the tree. And maybe you're doing ensemble learning. Maybe you're training 1000 trees. If all of those trees split on male and female at the root of the tree, they are learning disjoint models now from male and female, right? Because the left side of the tree and the right side of the tree don't talk to each other. If they always do that, they are now doing single task disjoint learning, right? 

发言人1   18:43
Suppose the decision tree never splits on male or female anywhere in the tree. It just finds the feature not very useful. Well, now what's it doing? It's pooling the data, it's learning one model for all 100000 patients, a joint model. And it's even ignoring the magic feature, male and female. So that's kind of interesting that this decision tree has the power to decide by itself whether it should train a joint model or whether it should train two separate models. That's kind of fun. 

发言人1   19:15
Well, suppose the tree occasionally splits on the male female attribute somewhere in the middle of the tree. That's even more interesting. So now think about it. It's doing some sort of partial sharing because the part of the tree that's above the male female split is obviously shared by both genders. So that's pooled learning, and then the part of the tree structure that's below the male female split that is now unpolled single task, separate learning. But it's coming after part of a tree that is shared. 

发言人1   19:46
So the tree is really doing something kind of clever, right? It's doing its own adaptive decision about when and where in the machine learning subspace it should or should not share content between these two things. That's kind of fascinating, and that's is true if you do an ensemble, right? The ensemble really is sharing this information. 

发言人1   20:06
So one of the reasons I think why ensembles of trees do so well and have been so hard to get them to benefit from some sort of multitask learning, from some sort of inductive transfer, I think one of the reasons is because they're already doing it. They already have this ability to do a form of adaptive transfer. Now, it's still internally a fairly hard transfer. They can't sort of do a 0.5 transfer. They have to do an all or nothing transfer at some point in the tree. So maybe we can still help them by softening things a little bit. 

发言人1   20:39
But these trees, especially if you're doing ensembles of these things, really are doing some sort of adaptive transfer. So, so now how many of you sort of thought in advance that ensembles of trees, these very simple methods that have been around, we're already doing like adaptive multitask learning? So, and I got to be honest, I didn't think about it until I had to prepare this talk. So it's having to think about inputs versus outputs that led me down the path that made me sort of realize that these ensembles of trees are actually doing something pretty clever. And it's also after seeing work by people like Anthony where they have these sort of magic context inputs to the model that sort of made me realize there's something fascinating happening in trees. 

发言人1   21:23
Now it's a workshop, Hopefully a few of you are surprised, possibly even upset or annoyed by this. And maybe, maybe I've just confused a few of you, but this is good for a workshop that's success. 

发言人1   21:35
Now I'm not going to stop there. It's not like ensembles of trees are doing everything we want, so let's go on. Decision trees do have an Achilles heel, so in the Achilles heel is that to learn complex functions of many different features, they have to repeatedly divide and conquer the data. And they're going to run out of data before they get to learn a complex enough function. The good news is that neural nets, although they're not doing maybe this auto ML stuff for us yet, or maybe they will be soon, they don't have this divide and conquer problem. So multitask learning in neural nets is still a very interesting thing. 

发言人1   22:09
Okay, so I'm going to spend just a minute or two, and then we'll call it quits, and I'll do many more tricks of the trade in the afternoon. 

发言人1   22:16
So let me just show you 3 quick tricks, inputs versus outputs. We've talked about whether something is available as an extra input, single task learning, or possibly this clever auto tree stuff, or whether something is provided as an extra output. When should you use things as inputs or outputs? If there's a lot of missing values, if you're doing feature selection or maybe this weather forecasting, which I might talk about later. 

发言人1   22:42
So here's a problem we worked on. We're trying to predict pneumonia risk. We have a bunch of attributes which we know before a patient is put in the hospital, these are easy things. And then we have a bunch of attributes that we might not know until after a patient is put in the hospital and it turns out we can train a model with all of these attributes, or we train a model with just the prehospital attributes, depending on what we're trying to do. And it turns out down is good. 

发言人1   23:06
This is like an ROC kind of measure. And it turns out the training on just the presho spike of attributes is not as accurate as training on the both the prehospital, the inhospitable attributes, the in-hospital the very things they're going to measure if they put you in the hospitals. So of course, they're valuable. Doctors, however, said, you know, we'd really like you to train a model to help us decide who we should admit to the hospital. Because of that, we want you to not use the in hospitals attributes because for most patients, we won't know it, of course, we always listen to the doctors, so we remove them from the input side of the net. 

发言人1   23:37
Multitask learning gives us an extra opportunity, which is to use these things as extra outputs on the network as opposed to inputs. OK, so we don't have to just because the information won't be available at runtime doesn't mean we have to ignore it because it is in the training set. It's available. So we're now trying to do crazy things like predict your risk of dying from pneumonia. 

发言人1   23:57
At the same time, we're trying to predict the results of these blood tests and other measurements that would be made once you are in the hospital. And it turns out the doctors aren't going to look at these, they don't care. They don't trust any prediction we're going to make of your blood potassium level or partial pressure of oxygen in your blood. They really don't care, they don't. The reason why they measure those things is because they're important and they need to know them accurately. So we're going to try to predict these hard things from the sort of easier things that we know before we put you in the hospital. 

发言人1   24:24
And the funny thing is, it actually works. So the top line is single task learning without those extra features as outputs. And the bottom line is multitask learning, where we use those extra things as outputs now on the network. And while it's nowhere as dramatic as using them as inputs, we're not allowed to use them as inputs. So instead of ignore them, we use them as outputs. And we get a sort of 5% to 10% reduction in error by doing that. 

发言人1   24:49
So one thing you can do is if you're just told somehow, don't use these things as inputs, don't just throw them away, consider using them as outputs. That's one of the benefits. Hey, if doctors can tell us what features we can't use as inputs, maybe feature selection can tell us what features we can't use as inputs. So this is sort of fun work. 

发言人1   25:07
This is another pneumonia related problem. Here we have a number of input features, 190 input features, and we're going to do feature selection. So, and what happens is if you use all of the features, so up is good in this model, this is ROC. If you use all the features, you get a reasonably good model, 0.825 ROC. But by doing feature selection, throwing away some features from the input of the model, we do better until we get down to about 50 features that seem to have most of the information in it. That's where ROC is highest. And then if we keep throwing away features, eventually we throw away too many and our accuracy actually goes down and we end up here. 

发言人1   25:43
Okay, so we're going to stick with a model that has 50 input features, we just threw away 140 of the inputs by doing feature selection. Hey, try using them as outputs. So we'll take all those extra 140 things that feature selection tools not to use as inputs. We'll put them on the output of the network. That's what the top line is. So that's when you use those 50 features as inputs, plus you take these other 140 features and you put them as outputs. 

发言人1   26:11
Okay, I don't want to cut into the break. I'm just going to mention that we're going to then talk about some massive multitask learning, which is when we try to scale this up to what will turn out to be billions of output tasks. So that's where we're going to go and I'll go through tricks of the trade much faster. In the afternoon, we're going to do a dozen tricks of the trade and we'll do that in 15 minutes. So, okay, thanks, have a good break. This is a good time to put up posters, posters, we'd like you to put them up on either this wall or the wall in the back if there's room. So feel free to do that now during the break. And then the poster session starts officially at 11, so thank you. 
