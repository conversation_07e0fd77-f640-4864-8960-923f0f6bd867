36-705: Intermediate Statistics Fall 2019
Lecture 1: August 26
Lecturer: <PERSON><PERSON>
Our broad goal for the first few lectures is to try to understand the behaviour of sums of
independent random variables. We would like to find ways to formalize the fact:
Averages of independent random variables concentrate around their expectation.
We will try to answer this question from the asymptotic (i.e. the number of random variables
we average → ∞) and the non-asymptotic viewpoint (i.e. the number of random variables
is some fixed finite number). The asymptotic viewpoint is typically characterized by what
are known as the Laws of Large Numbers (LLNs) and Central Limit Theorems (CLTs) while
the non-asymptotic viewpoint is characterized by concentration inequalities.
We will need to first review what a random variable is, what its expectation is, and what we
precisely mean by concentration. This will be fairly terse. Please read Chapters 1-3 of the
Was<PERSON>man book for more details.
1.1 Random Variables
Let Ω be a sample space (a set of possible outcomes) with a probability distribution (also
called a probability measure) P. A random variable is a map X : Ω → R. We write
P(X ∈ A) = P({ω ∈ Ω : X(ω) ∈ A})
and we write X ∼ P to mean that X has distribution P. The cumulative distribution
function (cdf ) of X is
FX(x) = F(x) = P(X ≤ x).
A cdf has three properties:
1. F is right-continuous. At each x, F(x) = limn→∞ F(yn) = F(x) for any sequence
yn → x with yn > x.
2. F is non-decreasing. If x < y then F(x) ≤ F(y).
3. F is normalized. limx→−∞ F(x) = 0 and limx→∞ F(x) = 1.
1-1
1-2 Lecture 1: August 26
Conversely, any F satisfying these three properties is a cdf for some random variable.
If X is discrete, its probability mass function (pmf ) is
pX(x) = p(x) = P(X = x).
If X is continuous, then its probability density function function (pdf ) satisfies
P(X ∈ A) = Z
A
pX(x)dx =
Z
A
p(x)dx
and pX(x) = p(x) = F
0
(x). The following are all equivalent:
X ∼ P, X ∼ F, X ∼ p.
Suppose that X ∼ P and Y ∼ Q. We say that X and Y have the same distribution
if P(X ∈ A) = Q(Y ∈ A) for all A. In that case we say that X and Y are equal in
distribution and we write X
d= Y .
Lemma 1.1 X
d= Y if and only if FX(t) = FY (t) for all t.
1.2 Expected Values
The mean or expected value of g(X) is
E (g(X)) = Z
g(x)dF(x) = Z
g(x)dP(x) = ( R ∞
P−∞ g(x)p(x)dx if X is continuous
j
g(xj )p(xj ) if X is discrete.
Recall that:
1. Linearity of Expectations: E(
Pk
j=1 cjgj (X)) = Pk
j=1 cjE(gj (X)).
2. If X1, . . . , Xn are independent then
E
 Yn
i=1
Xi
!
=
Y
i
E (Xi).
3. We often write µ = E(X).
Lecture 1: August 26 1-3
4. σ
2 = Var(X) = E ((X − µ)
2
) is the Variance.
5. Var(X) = E (X2
) − µ
2
.
6. If X1, . . . , Xn are independent then
Var Xn
i=1
aiXi
!
=
X
i
a
2
iVar(Xi).
7. The covariance is
Cov(X, Y ) = E((X − µx)(Y − µy)) = E(XY ) − µXµY
and the correlation is ρ(X, Y ) = Cov(X, Y )/σxσy. Recall that −1 ≤ ρ(X, Y ) ≤ 1.
The conditional expectation of Y given X is the random variable E(Y |X) whose
value, when X = x is
E(Y |X = x) = Z
y p(y|x)dy
where p(y|x) = p(x, y)/p(x).
The Law of Total Expectation or Law of Iterated Expectation:
E(Y ) = E

E(Y |X)

=
Z
E(Y |X = x)pX(x)dx.
The Law of Total Variance is
Var(Y ) = Var
E(Y |X)

+ E

Var(Y |X)

.
The moment generating function (mgf ) is
MX(t) = E

e
tX
.
If MX(t) = MY (t) for all t in an interval around 0 then X
d= Y .
The moment generating function can be used to “generate” all the moments of a distribution,
i.e. we can take derivatives of the mgf with respect to t and evaluate at t = 0, i.e. we have
that
M
(n)
X (t)|t=0 = E (X
n
)
1-4 Lecture 1: August 26
1.3 Independence
X and Y are independent if and only if
P(X ∈ A, Y ∈ B) = P(X ∈ A)P(Y ∈ B)
for all A and B.
Theorem 1.2 Let (X, Y ) be a bivariate random vector with pX,Y (x, y). X and Y are independent iff pX,Y (x, y) = pX(x)pY (y).
X1, . . . , Xn are independent if and only if
P(X1 ∈ A1, . . . , Xn ∈ An) = Yn
i=1
P(Xi ∈ Ai).
Thus, pX1,...,Xn
(x1, . . . , xn) = Qn
i=1 pXi
(xi).
If X1, . . . , Xn are independent and identically distributed we say they are iid (or that they
are a random sample) and we write
X1, . . . , Xn ∼ P or X1, . . . , Xn ∼ F or X1, . . . , Xn ∼ p.
1.4 Transformations
Let Y = g(X) where g : R → R. Then
FY (y) = P(Y ≤ y) = P(g(X) ≤ y) = Z
A(y)
pX(x)dx
where
A(y) = {x : g(x) ≤ y}.
The density is pY (y) = F
0
Y
(y). If g is strictly monotonic, then
pY (y) = pX(h(y))




dh(y)
dy




where h = g
−1
.
Example 1.3 Let pX(x) = e
−x
for x > 0. Hence FX(x) = 1 − e
−x
. Let Y = g(X) = log X.
Then
FY (y) = P(Y ≤ y) = P(log(X) ≤ y)
= P(X ≤ e
y
) = FX(e
y
) = 1 − e
−e
y
and pY (y) = e
y
e
−e
y
for y ∈ R.
Lecture 1: August 26 1-5
Example 1.4 Practice problem. Let X be uniform on (−1, 2) and let Y = X2
. Find the
density of Y .
Let Z = g(X, Y ). For example, Z = X + Y or Z = X/Y . Then we find the pdf of Z as
follows:
1. For each z, find the set Az = {(x, y) : g(x, y) ≤ z}.
2. Find the CDF
FZ(z) = P(Z ≤ z) = P(g(X, Y ) ≤ z) = P({(x, y) : g(x, y) ≤ z}) = Z Z
Az
pX,Y (x, y)dxdy.
3. The pdf is pZ(z) = F
0
Z
(z).
Example 1.5 Practice problem. Let (X, Y ) be uniform on the unit square. Let Z =
X/Y . Find the density of Z.
1.5 Important Distributions
Normal (Gaussian). X ∼ N(µ, σ2
) if
p(x) = 1
σ
√
2π
e
−(x−µ)
2/(2σ
2
)
.
If X ∈ R
d
then X ∼ N(µ, Σ) if
p(x) = 1
(2π)
d/2
|Σ|
exp 
−
1
2
(x − µ)
TΣ
−1
(x − µ)

.
Chi-squared. X ∼ χ
2
p
if X =
Pp
j=1 Z
2
j where Z1, . . . , Zp ∼ N(0, 1).
Non-central chi-squared (more on this below). X ∼ χ
2
1
(µ
2
) if X = Z
2 where Z ∼
N(µ, 1).
Bernoulli. X ∼ Bernoulli(θ) if P(X = 1) = θ and P(X = 0) = 1 − θ and hence
p(x) = θ
x
(1 − θ)
1−x x = 0, 1.
1-6 Lecture 1: August 26
Binomial. X ∼ Binomial(θ) if
p(x) = P(X = x) = 
n
x

θ
x
(1 − θ)
n−x x ∈ {0, . . . , n}.
Uniform. X ∼ Uniform(0, θ) if p(x) = I(0 ≤ x ≤ θ)/θ.
Poisson. X ∼ Poisson(λ) if P(X = x) = e−λλ
x
x!
x = 0, 1, 2, . . .. The E (X) = Var(X) = λ
and MX(t) = e
λ(e
t−1). We can use the mgf to show: if X1 ∼ Poisson(λ1), X2 ∼ Poisson(λ2),
independent then Y = X1 + X2 ∼ Poisson(λ1 + λ2).
Multinomial. The multivariate version of a Binomial is called a Multinomial. Consider
drawing a ball from an urn with has balls with k different colors labeled “color 1, color
2, . . . , color k.” Let p = (p1, p2, . . . , pk) where P
j
pj = 1 and pj
is the probability of
drawing color j. Draw n balls from the urn (independently and with replacement) and let
X = (X1, X2, . . . , Xk) be the count of the number of balls of each color drawn. We say that
X has a Multinomial (n, p) distribution. The pdf is
p(x) = 
n
x1, . . . , xk

p
x1
1
. . . p
xk
k
.
Exponential. X ∼ exp(β) if pX(x) = 1
β
e
−x/β
, x > 0. Note that exp(β) = Γ(1, β).
Gamma. X ∼ Γ(α, β) if
pX(x) = 1
Γ(α)β
α
x
α−1
e
−x/β
for x > 0 where Γ(α) = R ∞
0
1
βα x
α−1
e
−x/βdx.
Remark: In all of the above, make sure you understand the distinction between random
variables and parameters.
More on the Multivariate Normal. Let Y ∈ R
d
. Then Y ∼ N(µ, Σ) if
p(y) = 1
(2π)
d/2
|Σ|
1/2
exp 
−
1
2
(y − µ)
TΣ
−1
(y − µ)

.
Then E(Y ) = µ and cov(Y ) = Σ. The moment generating function is
M(t) = exp 
µ
T
t +
t
TΣt
2

.
Lecture 1: August 26 1-7
Theorem 1.6 (a). If Y ∼ N(µ, Σ), then E(Y ) = µ, cov(Y ) = Σ.
(b). If Y ∼ N(µ, Σ) and c is a scalar, then cY ∼ N(cµ, c2Σ).
(c). Let Y ∼ N(µ, Σ). If A is p × n and b is p × 1, then AY + b ∼ N(Aµ + b, AΣAT
).
Theorem 1.7 Suppose that Y ∼ N(µ, Σ). Let
Y =

Y1
Y2

, µ =

µ1
µ2

, Σ = 
Σ11 Σ12
Σ21 Σ22
.
where Y1 and µ1 are p × 1, and Σ11 is p × p.
(a) Y1 ∼ Np(µ1, Σ11), Y2 ∼ Nn−p(µ2, Σ22).
(b) Y1 and Y2 are independent if and only if Σ12 = 0.
(c) If Σ22 > 0, then the condition distribution of Y1 given Y2 is
Y1|Y2 ∼ Np(µ1 + Σ12Σ
−1
22 (Y2 − µ2), Σ11 − Σ12Σ
−1
22 Σ21). (1.1)
Lemma 1.8 Let Y ∼ N(µ, σ2
I), where Y
T = (Y1, . . . , Yn), µT = (µ1, . . . , µn) and σ
2 > 0 is
a scalar. Then the Yi are independent, Yi ∼ N1(µ, σ2
) and
kY k
2
σ
2
=
Y
T Y
σ
2
∼ χ
2
n

µ
T µ
σ
2

.
Theorem 1.9 Let Y ∼ N(µ, Σ). Then:
(a) Y
TΣ
−1Y ∼ χ
2
n
(µ
TΣ
−1µ).
(b) (Y − µ)
TΣ
−1
(Y − µ) ∼ χ
2
n
(0).
1.6 Sample Mean and Variance
Let X1, . . . , Xn ∼ P. The sample mean is
µbn =
1
n
X
i
Xi
and the sample variance is
σb
2
n =
1
n − 1
X
i
(Xi − µbn)
2
.
The sampling distribution of µbn is
Gn(t) = P(µbn ≤ t).
1-8 Lecture 1: August 26
Practice Problem. Let X1, . . . , Xn be iid with µ = E(Xi) = µ and σ
2 = Var(Xi) = σ
2
.
Then
E(µbn) = µ, Var(µbn) = σ
2
n
, E(σb
2
n
) = σ
2
.
Theorem 1.10 If X1, . . . , Xn ∼ N(µ, σ2
) then
(a) µbn ∼ N(µ, σ
2
n
).
(b) (n−1)σb
2
n
σ2 ∼ χ
2
n−1
.
(c) µbn and σb
2
n are independent.
1.7 A preview of the next few lectures
Let us consider a simple experiment. I toss a fair coin n times, and if the outcome is heads
I record Xi = +1, and if the outcome is tails I record Xi = −1. Now, let us consider the
average:
µbn =
1
n
Xn
i=1
Xi
.
It is easy to see that E[µbn] = 0, and we would like to know how far µbn is from its expectation.
When all the Xi = +1 (for instance), we have have that µbn = 1. There is however a
remarkable phenomenon, known as the concentration of measure phenomenon, that asserts
that µbn “concentrates” much closer to E[µbn].
The average of n i.i.d random variables concentrates within an interval of length roughly
1/
√
n around the mean.
The basic intuition is that in order to pull an average from an expectation many independent random variables need to work together simultaneously, which is extremely unlikely.
Independence is the key. This seemingly simple fact underlies essentially all of statistics.