<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Geometry of Linear Modeling Tutorial</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        header {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        nav {
            background: #2d3436;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #74b9ff;
        }
        
        main {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            border-left: 5px solid #74b9ff;
        }
        
        h2 {
            color: #2d3436;
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 0.5rem;
        }
        
        h3 {
            color: #636e72;
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .formula-box {
            background: #fff;
            border: 2px solid #74b9ff;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }
        
        .definition-box {
            background: #e8f4f8;
            border-left: 4px solid #0984e3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .theorem-box {
            background: #f0e6ff;
            border-left: 4px solid #a29bfe;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .example-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .step-box {
            background: linear-gradient(45deg, #fd79a8 0%, #e84393 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        .visualization {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .calculation-box {
            background: #e8f4f8;
            border: 1px solid #0984e3;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .result-box {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .algorithm-box {
            background: #e7f3ff;
            border: 2px solid #74b9ff;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .code-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .property-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #74b9ff;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .property-card h4 {
            color: #2d3436;
            margin-bottom: 0.5rem;
        }
        
        .geometry-box {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .insight-box {
            background: #fff5f5;
            border-left: 4px solid #e17055;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        footer {
            background: #2d3436;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            header {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            main {
                padding: 1rem;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>The Geometry of Linear Modeling</h1>
            <p class="subtitle">Understanding Least Squares Through Geometric Intuition</p>
            <p>From vector spaces to orthogonal projections: A complete geometric perspective</p>
        </header>
        
        <nav>
            <div class="nav-links">
                <a href="#introduction">Introduction</a>
                <a href="#least-squares">Least Squares</a>
                <a href="#qr-decomposition">QR Decomposition</a>
                <a href="#orthogonal-fitting">Orthogonal Fitting</a>
                <a href="#nested-models">Nested Models</a>
            </div>
        </nav>
        
        <main>
            <!-- Introduction -->
            <section id="introduction" class="section">
                <h2>1. Introduction to Geometric View of Linear Models</h2>
                
                <div class="highlight-box">
                    <h3>🎯 Why Geometry Matters</h3>
                    <p>A full understanding of what is happening when models are fitted by least squares is facilitated by taking a <strong>geometric view</strong> of the fitting process. Some of the results derived become rather obvious when viewed in this way.</p>
                </div>
                
                <h3>1.1 The Euclidean Space Perspective</h3>
                
                <div class="definition-box">
                    <h4>📐 The Geometric Framework</h4>
                    <p>Consider an <strong>n-dimensional Euclidean space ℜⁿ</strong>, in which:</p>
                    <ul>
                        <li><strong>y</strong> defines the location of a single point</li>
                        <li><strong>Model matrix X</strong> defines a subspace through its column combinations</li>
                        <li><strong>Linear model</strong> states that μ lies in the space of X</li>
                    </ul>
                </div>
                
                <div class="formula-box">
                    <p><strong>Linear Model in Geometric Terms:</strong></p>
                    $$\boldsymbol{\mu} = \mathbf{X}\boldsymbol{\beta}, \quad \mathbf{y} \sim N(\boldsymbol{\mu}, \mathbf{I}_n\sigma^2)$$
                    <p>where <strong>X</strong> is an n×p model matrix</p>
                </div>
                
                <div class="visualization">
                    <h4>The Geometry of Linear Models</h4>
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#0984e3;stop-opacity:0.1" />
                            </linearGradient>
                        </defs>
                        <rect width="800" height="500" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" fill="#2d3436" font-weight="bold">
                            Linear Model as Geometric Projection in ℜⁿ
                        </text>
                        
                        <!-- 3D coordinate system -->
                        <g transform="translate(150,250)">
                            <!-- Axes -->
                            <line x1="0" y1="0" x2="150" y2="0" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="0" y1="0" x2="0" y2="-150" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="0" y1="0" x2="-75" y2="75" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Axis labels -->
                            <text x="160" y="5" font-size="14" fill="#636e72">x₁</text>
                            <text x="5" y="-160" font-size="14" fill="#636e72">x₂</text>
                            <text x="-85" y="85" font-size="14" fill="#636e72">x₃</text>
                            
                            <!-- Model subspace (plane) -->
                            <path d="M 50 -50 L 120 -20 L 100 50 L 30 20 Z" 
                                  fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Data point y -->
                            <circle cx="80" cy="-80" r="6" fill="#e17055" stroke="#d63031" stroke-width="2"/>
                            <text x="85" y="-85" font-size="12" fill="#d63031" font-weight="bold">y</text>
                            
                            <!-- Fitted value (projection) -->
                            <circle cx="70" cy="-30" r="5" fill="#00b894" stroke="#00a085" stroke-width="2"/>
                            <text x="75" y="-35" font-size="12" fill="#00a085" font-weight="bold">μ̂</text>
                            
                            <!-- Projection line -->
                            <line x1="80" y1="-80" x2="70" y2="-30" stroke="#e17055" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <!-- Residual vector -->
                            <path d="M 70 -30 L 80 -80" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="45" y="-55" font-size="12" fill="#e17055" font-weight="bold">ε̂ = y - μ̂</text>
                            
                            <!-- Model space label -->
                            <text x="60" y="10" font-size="12" fill="#0984e3" font-weight="bold">Space of X</text>
                            <text x="55" y="25" font-size="11" fill="#0984e3">(Column Space)</text>
                        </g>
                        
                        <!-- Right side explanation -->
                        <rect x="450" y="80" width="320" height="200" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="610" y="105" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">Key Geometric Concepts</text>
                        
                        <text x="460" y="130" font-size="13" fill="#2d3436" font-weight="bold">• Model Subspace:</text>
                        <text x="470" y="145" font-size="12" fill="#636e72">All possible Xβ combinations</text>
                        <text x="470" y="160" font-size="12" fill="#636e72">Forms a p-dimensional subspace</text>
                        
                        <text x="460" y="180" font-size="13" fill="#2d3436" font-weight="bold">• Least Squares:</text>
                        <text x="470" y="195" font-size="12" fill="#636e72">Find closest point in subspace to y</text>
                        <text x="470" y="210" font-size="12" fill="#636e72">Orthogonal projection of y onto space</text>
                        
                        <text x="460" y="230" font-size="13" fill="#2d3436" font-weight="bold">• Residual Vector:</text>
                        <text x="470" y="245" font-size="12" fill="#636e72">ε̂ = y - μ̂</text>
                        <text x="470" y="260" font-size="12" fill="#636e72">Orthogonal to all vectors in space of X</text>
                        
                        <!-- Example box -->
                        <rect x="450" y="300" width="320" height="160" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="610" y="325" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">Concrete Example</text>
                        
                        <text x="460" y="350" font-size="13" fill="#2d3436" font-weight="bold">Straight Line Model (n=3, p=2):</text>
                        <text x="460" y="370" font-size="12" fill="#636e72">y = [0.04, 0.41, 0.62]ᵀ</text>
                        <text x="460" y="385" font-size="12" fill="#636e72">X = [1  0.2]</text>
                        <text x="470" y="400" font-size="12" fill="#636e72">    [1  1.0]</text>
                        <text x="470" y="415" font-size="12" fill="#636e72">    [1  0.6]</text>
                        <text x="460" y="435" font-size="12" fill="#636e72">μ could be any linear combination</text>
                        <text x="460" y="450" font-size="12" fill="#636e72">of columns [1,1,1]ᵀ and [0.2,1,0.6]ᵀ</text>
                        
                        <!-- Arrows -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#636e72" />
                            </marker>
                            <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#e17055" />
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>1.2 The Space of X</h3>
                
                <div class="geometry-box">
                    <h4>🔮 Understanding the Model Subspace</h4>
                    <p>The space of all possible linear combinations of the columns of X defines a <strong>subspace of ℜⁿ</strong>. The elements of this space are given by <strong>Xβ</strong>, where β can take any value in ℜᵖ.</p>
                    <p style="margin-top: 1rem;"><strong>Key insight:</strong> A linear model states that μ, the expected value of Y, lies in the space of X.</p>
                </div>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>📏 Dimension</h4>
                        <p>The model subspace has dimension p (number of columns in X)</p>
                    </div>
                    <div class="property-card">
                        <h4>🎯 Estimation Goal</h4>
                        <p>Find μ̂ ≡ Xβ̂ in the space of X closest to observed data y</p>
                    </div>
                    <div class="property-card">
                        <h4>⊥ Orthogonality</h4>
                        <p>Residual vector ε̂ = y - μ̂ is orthogonal to all vectors in space of X</p>
                    </div>
                    <div class="property-card">
                        <h4>📐 Projection</h4>
                        <p>μ̂ is the orthogonal projection of y onto the space of X</p>
                    </div>
                </div>
            </section>
            
            <!-- Least Squares -->
            <section id="least-squares" class="section">
                <h2>2. Least Squares as Geometric Projection</h2>
                
                <h3>2.1 The Projection Principle</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Least Squares Geometric Interpretation</h4>
                    <p><strong>Estimating a linear model by least squares amounts to finding the point μ̂ ≡ Xβ̂ in the space of X that is closest to the observed data y.</strong></p>
                    <p>Equivalently, μ̂ is the <strong>orthogonal projection</strong> of y onto the space of X.</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Mathematical Formulation:</strong></p>
                    $$\hat{\boldsymbol{\mu}} = \text{proj}_{\text{Col}(\mathbf{X})} \mathbf{y}$$
                    $$\hat{\boldsymbol{\varepsilon}} = \mathbf{y} - \hat{\boldsymbol{\mu}} \perp \text{Col}(\mathbf{X})$$
                    <p>where Col(X) denotes the column space of X</p>
                </div>
                
                <div class="visualization">
                    <h4>Straight Line Regression: Complete Geometric View</h4>
                    <svg width="900" height="600" viewBox="0 0 900 600">
                        <!-- Background -->
                        <rect width="900" height="600" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Geometric View: Straight Line Model with 3 Data Points
                        </text>
                        
                        <!-- Left panel: Traditional view -->
                        <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">Traditional View</text>
                        
                        <!-- Traditional scatter plot -->
                        <g transform="translate(50,100)">
                            <!-- Axes -->
                            <line x1="0" y1="200" x2="300" y2="200" stroke="#636e72" stroke-width="2"/>
                            <line x1="50" y1="250" x2="50" y2="50" stroke="#636e72" stroke-width="2"/>
                            
                            <!-- Axis labels -->
                            <text x="320" y="205" font-size="12" fill="#636e72">x</text>
                            <text x="45" y="40" font-size="12" fill="#636e72">y</text>
                            
                            <!-- Tick marks and labels -->
                            <line x1="90" y1="195" x2="90" y2="205" stroke="#636e72"/>
                            <text x="85" y="220" font-size="10" fill="#636e72">0.2</text>
                            
                            <line x1="200" y1="195" x2="200" y2="205" stroke="#636e72"/>
                            <text x="195" y="220" font-size="10" fill="#636e72">1.0</text>
                            
                            <line x1="140" y1="195" x2="140" y2="205" stroke="#636e72"/>
                            <text x="135" y="220" font-size="10" fill="#636e72">0.6</text>
                            
                            <!-- Data points -->
                            <circle cx="90" cy="190" r="4" fill="#e17055" stroke="#d63031" stroke-width="2"/>
                            <circle cx="200" cy="120" r="4" fill="#e17055" stroke="#d63031" stroke-width="2"/>
                            <circle cx="140" cy="140" r="4" fill="#e17055" stroke="#d63031" stroke-width="2"/>
                            
                            <!-- Data values -->
                            <text x="95" y="185" font-size="10" fill="#d63031">0.04</text>
                            <text x="205" y="115" font-size="10" fill="#d63031">0.41</text>
                            <text x="145" y="135" font-size="10" fill="#d63031">0.62</text>
                            
                            <!-- Regression line -->
                            <line x1="70" y1="180" x2="220" y2="110" stroke="#74b9ff" stroke-width="3"/>
                            <text x="100" y="100" font-size="12" fill="#74b9ff" font-weight="bold">ŷ = β₀ + β₁x</text>
                        </g>
                        
                        <!-- Right panel: Geometric view -->
                        <text x="700" y="60" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">Geometric View in ℜ³</text>
                        
                        <g transform="translate(550,100)">
                            <!-- 3D coordinate system -->
                            <line x1="0" y1="200" x2="200" y2="200" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="300" x2="100" y2="100" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="200" x2="50" y2="250" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Axis labels -->
                            <text x="210" y="205" font-size="12" fill="#636e72">axis 1</text>
                            <text x="105" y="90" font-size="12" fill="#636e72">axis 2</text>
                            <text x="30" y="260" font-size="12" fill="#636e72">axis 3</text>
                            
                            <!-- Model plane (2D subspace) -->
                            <path d="M 120 180 L 180 160 L 160 220 L 100 240 Z" 
                                  fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Column vectors of X -->
                            <path d="M 100 200 L 150 150" stroke="#00b894" stroke-width="3" marker-end="url(#arrowhead3)"/>
                            <path d="M 100 200 L 130 180" stroke="#fdcb6e" stroke-width="3" marker-end="url(#arrowhead4)"/>
                            
                            <!-- Vector labels -->
                            <text x="155" y="145" font-size="11" fill="#00b894" font-weight="bold">[1,1,1]ᵀ</text>
                            <text x="135" y="175" font-size="11" fill="#fdcb6e" font-weight="bold">[0.2,1,0.6]ᵀ</text>
                            
                            <!-- Data vector y -->
                            <path d="M 100 200 L 170 130" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="175" y="125" font-size="11" fill="#e17055" font-weight="bold">y = [0.04,0.41,0.62]ᵀ</text>
                            
                            <!-- Fitted value -->
                            <circle cx="140" cy="170" r="4" fill="#a29bfe" stroke="#6c5ce7" stroke-width="2"/>
                            <text x="145" y="165" font-size="11" fill="#6c5ce7" font-weight="bold">μ̂</text>
                            
                            <!-- Projection line -->
                            <line x1="170" y1="130" x2="140" y2="170" stroke="#e17055" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <!-- Residual vector -->
                            <text x="110" y="130" font-size="11" fill="#e17055" font-weight="bold">ε̂ ⊥ plane</text>
                            
                            <!-- Model space label -->
                            <text x="120" y="250" font-size="12" fill="#0984e3" font-weight="bold">2D Model Subspace</text>
                            <text x="130" y="265" font-size="11" fill="#0984e3">span{X₁, X₂}</text>
                        </g>
                        
                        <!-- Mathematical representation -->
                        <rect x="50" y="400" width="800" height="180" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="450" y="425" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Mathematical Representation of the Example
                        </text>
                        
                        <text x="70" y="450" font-size="14" fill="#2d3436" font-weight="bold">Data and Model:</text>
                        <text x="70" y="470" font-size="13" fill="#636e72">
                            y = [0.04, 0.41, 0.62]ᵀ,  μ = X β = [1  0.2] [β₁]
                        </text>
                        <text x="420" y="485" font-size="13" fill="#636e72">
                            [1  1.0] [β₂]
                        </text>
                        <text x="420" y="500" font-size="13" fill="#636e72">
                            [1  0.6]
                        </text>
                        
                        <text x="70" y="525" font-size="14" fill="#2d3436" font-weight="bold">Geometric Interpretation:</text>
                        <text x="70" y="545" font-size="13" fill="#636e72">
                            • β is unknown, so μ could be any linear combination of [1,1,1]ᵀ and [0.2,1,0.6]ᵀ
                        </text>
                        <text x="70" y="560" font-size="13" fill="#636e72">
                            • Fitting by least squares finds the particular combination closest to y
                        </text>
                        
                        <!-- Arrows for projections -->
                        <defs>
                            <marker id="arrowhead3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#00b894" />
                            </marker>
                            <marker id="arrowhead4" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#fdcb6e" />
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>2.2 Orthogonality Condition</h3>
                
                <div class="insight-box">
                    <h4>💡 Fundamental Insight</h4>
                    <p>An obvious, but important, consequence of the geometric view is that <strong>the residual vector ε̂ = y - μ̂ is orthogonal to all vectors in the space of X</strong>.</p>
                    <p>This orthogonality condition is the key to deriving the normal equations!</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Orthogonality Conditions:</strong></p>
                    $$\hat{\boldsymbol{\varepsilon}} \perp \text{Col}(\mathbf{X}) \Rightarrow \mathbf{X}^T\hat{\boldsymbol{\varepsilon}} = \mathbf{0}$$
                    $$\mathbf{X}^T(\mathbf{y} - \mathbf{X}\hat{\boldsymbol{\beta}}) = \mathbf{0}$$
                    $$\mathbf{X}^T\mathbf{X}\hat{\boldsymbol{\beta}} = \mathbf{X}^T\mathbf{y}$$
                    <p><strong>Normal Equations!</strong></p>
                </div>
            </section>
            
            <!-- QR Decomposition -->
            <section id="qr-decomposition" class="section">
                <h2>3. QR Decomposition and Geometric Transformations</h2>
                
                <h3>3.1 The QR Decomposition</h3>
                
                <div class="highlight-box">
                    <h3>🔄 Understanding QR Decomposition Geometrically</h3>
                    <p>The actual calculation of least squares estimates involves first forming the <strong>QR decomposition</strong> of the model matrix. This is fundamentally a geometric rotation that simplifies the problem.</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>QR Decomposition:</strong></p>
                    $$\mathbf{X} = \mathbf{Q} \begin{bmatrix} \mathbf{R} \\ \mathbf{0} \end{bmatrix}$$
                    <p>where <strong>Q</strong> is an n×n orthogonal matrix and <strong>R</strong> is a p×p upper triangular matrix</p>
                </div>
                
                <div class="theorem-box">
                    <h4>🎯 Geometric Interpretation of QR</h4>
                    <p><strong>Orthogonal matrices rotate vectors (without changing their length)</strong> and the first step in least squares estimation is to rotate both the response vector y and the columns of the model matrix X in exactly the same way, by pre-multiplication with Q^T.</p>
                </div>
                
                <div class="visualization">
                    <h4>QR Decomposition: Rotation to Convenient Coordinates</h4>
                    <svg width="900" height="700" viewBox="0 0 900 700">
                        <!-- Background -->
                        <rect width="900" height="700" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Geometric Effect of QR Decomposition
                        </text>
                        
                        <!-- Original problem (left) -->
                        <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">Original Problem</text>
                        
                        <g transform="translate(50,100)">
                            <!-- 3D coordinate system -->
                            <line x1="50" y1="200" x2="200" y2="200" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="275" x2="125" y2="125" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="200" x2="75" y2="250" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Axis labels -->
                            <text x="210" y="205" font-size="12" fill="#636e72">e₁</text>
                            <text x="130" y="115" font-size="12" fill="#636e72">e₂</text>
                            <text x="55" y="260" font-size="12" fill="#636e72">e₃</text>
                            
                            <!-- Model plane -->
                            <path d="M 140 180 L 180 160 L 160 220 L 120 240 Z" 
                                  fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Column vectors of X -->
                            <path d="M 125 200 L 160 170" stroke="#00b894" stroke-width="3" marker-end="url(#arrowhead3)"/>
                            <path d="M 125 200 L 140 185" stroke="#fdcb6e" stroke-width="3" marker-end="url(#arrowhead4)"/>
                            
                            <!-- Vector labels -->
                            <text x="155" y="145" font-size="11" fill="#00b894" font-weight="bold">[1,1,1]ᵀ</text>
                            <text x="135" y="175" font-size="11" fill="#fdcb6e" font-weight="bold">[0.2,1,0.6]ᵀ</text>
                            
                            <!-- Data vector -->
                            <path d="M 125 200 L 170 150" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="175" y="145" font-size="11" fill="#e17055" font-weight="bold">y</text>
                            
                            <!-- Projection -->
                            <circle cx="150" cy="185" r="4" fill="#a29bfe" stroke="#6c5ce7" stroke-width="2"/>
                            <line x1="170" y1="150" x2="150" y2="185" stroke="#e17055" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <text x="80" y="280" font-size="12" fill="#2d3436" font-weight="bold">Arbitrary orientation</text>
                        </g>
                        
                        <!-- Transformation arrow -->
                        <g transform="translate(350,250)">
                            <path d="M 0 0 L 100 0" stroke="#a29bfe" stroke-width="4" marker-end="url(#arrowhead5)"/>
                            <text x="50" y="-10" text-anchor="middle" font-size="12" fill="#a29bfe" font-weight="bold">Q^T rotation</text>
                            <text x="50" y="25" text-anchor="middle" font-size="12" fill="#a29bfe">Preserves lengths</text>
                            <text x="50" y="40" text-anchor="middle" font-size="12" fill="#a29bfe">and angles</text>
                        </g>
                        
                        <!-- Rotated problem (right) -->
                        <text x="700" y="60" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">After Q^T Rotation</text>
                        
                        <g transform="translate(550,100)">
                            <!-- Rotated coordinate system -->
                            <line x1="50" y1="200" x2="200" y2="200" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="275" x2="125" y2="125" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="200" x2="75" y2="250" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Axis labels -->
                            <text x="210" y="205" font-size="12" fill="#636e72">q₁</text>
                            <text x="130" y="115" font-size="12" fill="#636e72">q₂</text>
                            <text x="55" y="260" font-size="12" fill="#636e72">q₃</text>
                            
                            <!-- Model space aligned with axes -->
                            <rect x="100" y="175" width="100" height="50" fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Rotated data vector -->
                            <path d="M 125 200 L 170 160" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="175" y="155" font-size="11" fill="#e17055" font-weight="bold">Q^T y</text>
                            
                            <!-- Aligned basis vectors -->
                            <path d="M 125 200 L 175 200" stroke="#00b894" stroke-width="3" marker-end="url(#arrowhead3)"/>
                            <path d="M 125 200 L 125 150" stroke="#fdcb6e" stroke-width="3" marker-end="url(#arrowhead4)"/>
                            
                            <!-- Decomposed vectors -->
                            <text x="135" y="240" font-size="11" fill="#00b894" font-weight="bold">R₁₁q₁</text>
                            <text x="80" y="180" font-size="11" fill="#fdcb6e" font-weight="bold">R₁₂q₁ + R₂₂q₂</text>
                            
                            <!-- Fitted value components -->
                            <circle cx="170" cy="160" r="2" fill="#a29bfe"/>
                            <line x1="170" y1="160" x2="170" y2="200" stroke="#a29bfe" stroke-width="2"/>
                            <line x1="170" y1="200" x2="125" y2="200" stroke="#a29bfe" stroke-width="2"/>
                            
                            <!-- Component labels -->
                            <text x="135" y="195" font-size="10" fill="#a29bfe">f₁</text>
                            <text x="175" y="180" font-size="10" fill="#a29bfe">f₂</text>
                            <text x="175" y="140" font-size="10" fill="#e17055">r₃</text>
                            
                            <text x="80" y="280" font-size="12" fill="#2d3436" font-weight="bold">Convenient orientation</text>
                        </g>
                        
                        <!-- Mathematical breakdown -->
                        <rect x="50" y="450" width="800" height="220" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="450" y="475" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Mathematical Breakdown of QR Rotation
                        </text>
                        
                        <text x="70" y="500" font-size="14" fill="#2d3436" font-weight="bold">Step 1: QR Decomposition</text>
                        <text x="70" y="520" font-size="13" fill="#636e72">X = Q [R]  where Q is orthogonal, R is upper triangular</text>
                        <text x="120" y="535" font-size="13" fill="#636e72">     [0]</text>
                        
                        <text x="70" y="560" font-size="14" fill="#2d3436" font-weight="bold">Step 2: Rotate the problem</text>
                        <text x="70" y="580" font-size="13" fill="#636e72">Multiply both sides by Q^T:</text>
                        <text x="70" y="595" font-size="13" fill="#636e72">Q^T y = Q^T X β = [R] β = [R β]</text>
                        <text x="250" y="610" font-size="13" fill="#636e72">             [0]     [0  ]</text>
                        
                        <text x="70" y="635" font-size="14" fill="#2d3436" font-weight="bold">Step 3: Convenient structure</text>
                        <text x="70" y="655" font-size="13" fill="#636e72">
                            After rotation: fitted values = [f^T, 0^T]^T,  residuals = [0^T, r^T]^T
                        </text>
                        
                        <!-- New arrows -->
                        <defs>
                            <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#a29bfe" />
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <h3>3.2 The Convenient Coordinate System</h3>
                
                <div class="result-box">
                    <h4>✨ Key Advantage of QR Rotation</h4>
                    <p>After rotation by Q^T, the problem has a particularly <strong>convenient orientation</strong> relative to the axes:</p>
                    <ul>
                        <li><strong>First p components</strong> of fitted value vector can be read directly from axes 1 to p</li>
                        <li><strong>Remaining components</strong> are simply zero</li>
                        <li><strong>Residual vector</strong> has zero components relative to axes 1 to p</li>
                        <li><strong>Non-zero residual components</strong> can be read directly from remaining axes</li>
                    </ul>
                </div>
            </section>
            
            <!-- Orthogonal Fitting -->
            <section id="orthogonal-fitting" class="section">
                <h2>4. Fitting by Orthogonal Decompositions</h2>
                
                <h3>4.1 The Rotation Process</h3>
                
                <div class="algorithm-box">
                    <h4>🔄 Step-by-Step Orthogonal Fitting Process</h4>
                    
                    <div class="step-box">
                        <span class="step-number">1</span>
                        <strong>QR Decomposition:</strong> Compute X = Q[R; 0]
                    </div>
                    
                    <div class="step-box">
                        <span class="step-number">2</span>
                        <strong>Rotate both vectors:</strong> Apply Q^T to y and X
                    </div>
                    
                    <div class="step-box">
                        <span class="step-number">3</span>
                        <strong>Read solutions:</strong> Fitted values and residuals become obvious
                    </div>
                    
                    <div class="step-box">
                        <span class="step-number">4</span>
                        <strong>Recover original:</strong> Apply Q to get back to original coordinates
                    </div>
                </div>
                
                <div class="visualization">
                    <h4>Complete Orthogonal Fitting Illustration</h4>
                    <svg width="900" height="800" viewBox="0 0 900 800">
                        <!-- Background -->
                        <rect width="900" height="800" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Complete Orthogonal Fitting Process: Original → Rotated → Recovery
                        </text>
                        
                        <!-- Original problem -->
                        <text x="150" y="70" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">1. Original Problem</text>
                        <g transform="translate(50,100)">
                            <!-- Coordinate system -->
                            <line x1="50" y1="150" x2="150" y2="150" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="200" x2="100" y2="100" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="150" x2="70" y2="180" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Model subspace -->
                            <path d="M 110 140 L 140 125 L 130 165 L 100 180 Z" 
                                  fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Data and vectors -->
                            <path d="M 100 150 L 130 120" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="135" y="115" font-size="11" fill="#e17055" font-weight="bold">y</text>
                            
                            <circle cx="120" cy="140" r="3" fill="#a29bfe"/>
                            <line x1="130" y1="120" x2="120" y2="140" stroke="#e17055" stroke-width="2" stroke-dasharray="3,3"/>
                        </g>
                        
                        <!-- Rotation step -->
                        <text x="450" y="70" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">2. After Q^T Rotation</text>
                        <g transform="translate(350,100)">
                            <!-- Rotated coordinate system -->
                            <line x1="50" y1="150" x2="150" y2="150" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="200" x2="100" y2="100" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="150" x2="70" y2="180" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Aligned model space -->
                            <rect x="80" y="130" width="70" height="40" fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Rotated vectors -->
                            <path d="M 100 150 L 130 125" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="135" y="120" font-size="11" fill="#e17055" font-weight="bold">Q^T y</text>
                            
                            <!-- Component decomposition -->
                            <line x1="130" y1="125" x2="130" y2="150" stroke="#a29bfe" stroke-width="2"/>
                            <line x1="130" y1="150" x2="100" y2="150" stroke="#a29bfe" stroke-width="2"/>
                            <text x="105" y="145" font-size="10" fill="#a29bfe">f₁</text>
                            <text x="135" y="138" font-size="10" fill="#a29bfe">f₂</text>
                            <text x="135" y="115" font-size="10" fill="#e17055">r₃</text>
                        </g>
                        
                        <!-- Recovery step -->
                        <text x="750" y="70" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">3. Recovery with Q</text>
                        <g transform="translate(650,100)">
                            <!-- Original coordinate system -->
                            <line x1="50" y1="150" x2="150" y2="150" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="200" x2="100" y2="100" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="100" y1="150" x2="70" y2="180" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Final result -->
                            <path d="M 110 140 L 140 125 L 130 165 L 100 180 Z" 
                                  fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <circle cx="120" cy="140" r="3" fill="#00b894" stroke="#00a085" stroke-width="2"/>
                            <text x="125" y="135" font-size="11" fill="#00a085" font-weight="bold">μ̂</text>
                            
                            <path d="M 120 140 L 130 120" stroke="#e17055" stroke-width="3" marker-end="url(#arrowhead2)"/>
                            <text x="135" y="115" font-size="11" fill="#e17055" font-weight="bold">ε̂</text>
                        </g>
                        
                        <!-- Mathematical formulation -->
                        <rect x="50" y="350" width="800" height="300" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="450" y="375" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Mathematical Formulation of the Process
                        </text>
                        
                        <text x="70" y="405" font-size="14" fill="#2d3436" font-weight="bold">The Complete Process:</text>
                        
                        <text x="70" y="430" font-size="13" fill="#636e72" font-weight="bold">1. QR Decomposition:</text>
                        <text x="100" y="450" font-size="12" fill="#636e72">X = Q [R]  ⟹  Q^T X = [R]</text>
                        <text x="130" y="465" font-size="12" fill="#636e72">     [0]              [0]</text>
                        
                        <text x="70" y="490" font-size="13" fill="#636e72" font-weight="bold">2. Rotate the response:</text>
                        <text x="100" y="510" font-size="12" fill="#636e72">Q^T y = [f]  where f contains fitted value information</text>
                        <text x="130" y="525" font-size="12" fill="#636e72">     [r]       r contains residual information</text>
                        
                        <text x="70" y="550" font-size="13" fill="#636e72" font-weight="bold">3. Solve in rotated coordinates:</text>
                        <text x="100" y="570" font-size="12" fill="#636e72">R β̂ = f  ⟹  β̂ = R^(-1) f</text>
                        <text x="100" y="585" font-size="12" fill="#636e72">(Upper triangular system - easy to solve by back substitution)</text>
                        
                        <text x="70" y="610" font-size="13" fill="#636e72" font-weight="bold">4. Recover original coordinates:</text>
                        <text x="100" y="630" font-size="12" fill="#636e72">μ̂ = Q [f] = Q [R β̂] = X β̂</text>
                        <text x="130" y="645" font-size="12" fill="#636e72">     [0]     [0  ]</text>
                        <text x="100" y="665" font-size="12" fill="#636e72">ε̂ = Q [0] = [residual vector in original coordinates]</text>
                        <text x="130" y="680" font-size="12" fill="#636e72">     [r]</text>
                    </svg>
                </div>
                
                <h3>4.2 Advantages of the Orthogonal Approach</h3>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>📊 Numerical Stability</h4>
                        <p>QR decomposition is more numerically stable than forming X^T X directly</p>
                    </div>
                    <div class="property-card">
                        <h4>🎯 Clear Separation</h4>
                        <p>Fitted values and residuals are clearly separated in rotated coordinates</p>
                    </div>
                    <div class="property-card">
                        <h4>🔄 Efficient</h4>
                        <p>Avoids matrix inversion - only requires back substitution</p>
                    </div>
                    <div class="property-card">
                        <h4>📐 Geometric Insight</h4>
                        <p>Makes the projection structure of least squares transparent</p>
                    </div>
                </div>
                
                <div class="formula-box">
                    <p><strong>Recovery Formulas:</strong></p>
                    $$\hat{\boldsymbol{\mu}} = \mathbf{Q} \begin{bmatrix} \mathbf{f} \\ \mathbf{0} \end{bmatrix}, \quad \hat{\boldsymbol{\varepsilon}} = \mathbf{Q} \begin{bmatrix} \mathbf{0} \\ \mathbf{r} \end{bmatrix}$$
                    <p>where f and r are the fitted and residual components in rotated coordinates</p>
                </div>
            </section>
            
            <!-- Nested Models -->
            <section id="nested-models" class="section">
                <h2>5. Comparison of Nested Models</h2>
                
                <h3>5.1 Definition of Nested Models</h3>
                
                <div class="definition-box">
                    <h4>🔗 Nested Models Definition</h4>
                    <p>A linear model with model matrix <strong>X₀</strong> is <strong>nested within</strong> a linear model with model matrix <strong>X₁</strong> if:</p>
                    <ul>
                        <li>They are models for the same response data</li>
                        <li>The columns of X₀ span a <strong>subspace</strong> of the space spanned by X₁</li>
                        <li>Usually this means X₁ is X₀ with some extra columns added</li>
                    </ul>
                </div>
                
                <div class="theorem-box">
                    <h4>🎯 Key Geometric Relationship</h4>
                    <p>The vector of the difference between the fitted values of two nested linear models is <strong>entirely within the subspace of the larger model</strong>, and is therefore <strong>orthogonal to the residual vector for the larger model</strong>.</p>
                    <p>This fact is geometrically obvious and is a key reason why F-ratio statistics have a relatively simple distribution.</p>
                </div>
                
                <div class="visualization">
                    <h4>Geometry of Nested Models</h4>
                    <svg width="900" height="800" viewBox="0 0 900 800">
                        <!-- Background -->
                        <rect width="900" height="800" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Nested Model Comparison: Full Model vs Reduced Model
                        </text>
                        
                        <!-- Original coordinates -->
                        <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">Original Coordinates</text>
                        
                        <g transform="translate(50,100)">
                            <!-- 3D coordinate system -->
                            <line x1="50" y1="200" x2="200" y2="200" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="275" x2="125" y2="125" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="200" x2="75" y2="250" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Full model subspace (2D) -->
                            <path d="M 140 180 L 180 160 L 160 220 L 120 240 Z" 
                                  fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            <text x="130" y="260" font-size="11" fill="#0984e3" font-weight="bold">Full Model Space</text>
                            
                            <!-- Reduced model subspace (1D line) -->
                            <line x1="110" y1="190" x2="170" y2="170" stroke="#fd79a8" stroke-width="4"/>
                            <text x="100" y="175" font-size="11" fill="#fd79a8" font-weight="bold">Reduced Model</text>
                            
                            <!-- Data point -->
                            <circle cx="160" cy="140" r="4" fill="#e17055" stroke="#d63031" stroke-width="2"/>
                            <text x="165" y="135" font-size="11" fill="#e17055" font-weight="bold">y</text>
                            
                            <!-- Full model fit -->
                            <circle cx="145" cy="175" r="3" fill="#00b894" stroke="#00a085" stroke-width="2"/>
                            <text x="130" y="170" font-size="10" fill="#00a085" font-weight="bold">μ̂₁</text>
                            
                            <!-- Reduced model fit -->
                            <circle cx="135" cy="185" r="3" fill="#fdcb6e" stroke="#e17055" stroke-width="2"/>
                            <text x="120" y="180" font-size="10" fill="#e17055" font-weight="bold">μ̂₀</text>
                            
                            <!-- Residual for full model -->
                            <line x1="160" y1="140" x2="145" y2="175" stroke="#00a085" stroke-width="2" stroke-dasharray="5,5"/>
                            <text x="110" y="155" font-size="10" fill="#00a085">ε̂₁</text>
                            
                            <!-- Difference vector -->
                            <line x1="135" y1="185" x2="145" y2="175" stroke="#a29bfe" stroke-width="3" marker-end="url(#arrowhead5)"/>
                            <text x="150" y="185" font-size="10" fill="#a29bfe" font-weight="bold">μ̂₁ - μ̂₀</text>
                            
                            <!-- Projection from reduced to full -->
                            <line x1="160" y1="140" x2="135" y2="185" stroke="#fd79a8" stroke-width="2" stroke-dasharray="3,3"/>
                        </g>
                        
                        <!-- Rotated coordinates -->
                        <text x="700" y="60" text-anchor="middle" font-size="14" fill="#2d3436" font-weight="bold">After Q^T Rotation</text>
                        
                        <g transform="translate(550,100)">
                            <!-- Rotated coordinate system -->
                            <line x1="50" y1="200" x2="200" y2="200" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="275" x2="125" y2="125" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="125" y1="200" x2="75" y2="250" stroke="#636e72" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Axis labels -->
                            <text x="210" y="205" font-size="11" fill="#636e72">axis 1</text>
                            <text x="130" y="115" font-size="11" fill="#636e72">axis 2</text>
                            <text x="55" y="260" font-size="11" fill="#636e72">axis 3</text>
                            
                            <!-- Full model space aligned with axes 1,2 -->
                            <rect x="100" y="175" width="100" height="50" fill="#74b9ff" opacity="0.3" stroke="#0984e3" stroke-width="2"/>
                            
                            <!-- Reduced model space aligned with axis 1 only -->
                            <line x1="100" y1="200" x2="200" y2="200" stroke="#fd79a8" stroke-width="4"/>
                            
                            <!-- Rotated data point -->
                            <circle cx="160" cy="150" r="4" fill="#e17055" stroke="#d63031" stroke-width="2"/>
                            <text x="165" y="145" font-size="11" fill="#e17055" font-weight="bold">Q^T y</text>
                            
                            <!-- Component breakdown -->
                            <!-- Full model fitted value -->
                            <circle cx="160" cy="185" r="3" fill="#00b894" stroke="#00a085" stroke-width="2"/>
                            <line x1="160" y1="150" x2="160" y2="185" stroke="#00a085" stroke-width="2"/>
                            <text x="165" y="180" font-size="10" fill="#00a085">f₂</text>
                            
                            <line x1="160" y1="185" x2="125" y2="185" stroke="#00a085" stroke-width="2"/>
                            <text x="140" y="180" font-size="10" fill="#00a085">f₁</text>
                            
                            <!-- Reduced model fitted value -->
                            <circle cx="140" cy="200" r="3" fill="#fdcb6e" stroke="#e17055" stroke-width="2"/>
                            <text x="145" y="195" font-size="10" fill="#e17055">reduced f₁</text>
                            
                            <!-- Residual for axis 3 -->
                            <text x="165" y="135" font-size="10" fill="#e17055">r₃</text>
                            
                            <!-- Convenient structure -->
                            <text x="80" y="280" font-size="11" fill="#2d3436" font-weight="bold">Convenient structure:</text>
                            <text x="80" y="295" font-size="10" fill="#636e72">• Reduced model: only axis 1</text>
                            <text x="80" y="310" font-size="10" fill="#636e72">• Full model: axes 1 & 2</text>
                            <text x="80" y="325" font-size="10" fill="#636e72">• Residual: axis 3</text>
                        </g>
                        
                        <!-- Mathematical explanation -->
                        <rect x="50" y="450" width="800" height="300" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="450" y="475" text-anchor="middle" font-size="16" fill="#2d3436" font-weight="bold">
                            Mathematical Structure of Nested Model Comparison
                        </text>
                        
                        <text x="70" y="505" font-size="14" fill="#2d3436" font-weight="bold">Example: Straight Line vs Intercept-Only Model</text>
                        
                        <text x="70" y="530" font-size="13" fill="#636e72" font-weight="bold">Full Model:</text>
                        <text x="100" y="550" font-size="12" fill="#636e72">y₍ᵢ₎ = β₀ + β₁xᵢ + εᵢ</text>
                        
                        <text x="300" y="530" font-size="13" fill="#636e72" font-weight="bold">Reduced Model:</text>
                        <text x="330" y="550" font-size="12" fill="#636e72">y₍ᵢ₎ = β₀ + εᵢ</text>
                        
                        <text x="70" y="580" font-size="14" fill="#2d3436" font-weight="bold">Key Geometric Relationships:</text>
                        
                        <text x="70" y="605" font-size="13" fill="#636e72">
                            1. Difference vector: μ̂₁ - μ̂₀ lies entirely within the full model subspace
                        </text>
                        
                        <text x="70" y="625" font-size="13" fill="#636e72">
                            2. Orthogonality: (μ̂₁ - μ̂₀) ⊥ ε̂₁  (difference orthogonal to full model residual)
                        </text>
                        
                        <text x="70" y="645" font-size="13" fill="#636e72">
                            3. This orthogonality is the foundation for F-test statistics
                        </text>
                        
                        <text x="70" y="670" font-size="14" fill="#2d3436" font-weight="bold">Computational Advantage:</text>
                        <text x="70" y="690" font-size="13" fill="#636e72">
                            Much of the work for the reduced model is already done when estimating the full model
                        </text>
                        <text x="70" y="710" font-size="13" fill="#636e72">
                            (provided columns are ordered appropriately to avoid extra work)
                        </text>
                        
                        <text x="70" y="730" font-size="13" fill="#636e72" font-weight="bold">
                            Important: Column ordering in X matters for computational efficiency!
                        </text>
                    </svg>
                </div>
                
                <h3>5.2 F-ratio Foundation</h3>
                
                <div class="insight-box">
                    <h4>🔍 Why F-ratios Work</h4>
                    <p>The orthogonality between the difference vector (μ̂₁ - μ̂₀) and the full model residual ε̂₁ is the <strong>key geometric reason</strong> why F-ratio statistics have relatively simple distributions under the null hypothesis.</p>
                    <p>This geometric insight shows why the components of variation can be cleanly separated and compared.</p>
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Computational Consideration</h4>
                    <p>Note that if our interest had been in comparing the full model to a different reduced model (e.g., y₍ᵢ₎ = β₁xᵢ + εᵢ), then it would have been necessary to <strong>reorder the columns</strong> of the full model matrix in order to avoid extra work.</p>
                    <p><strong>Column ordering matters</strong> for computational efficiency in nested model comparisons!</p>
                </div>
            </section>
        
        </main>
        
        <footer>
            <p>&copy; 2024 Geometric Linear Models Tutorial. Created for educational purposes.</p>
            <p>Understanding linear models through geometric intuition and orthogonal projections.</p>
        </footer>
    </div>
</body>
</html> 