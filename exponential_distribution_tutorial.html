<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exponential Distribution Tutorial</title>
    
    <!-- MathJax 3 Script for HD rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            border-radius: 15px;
            color: white;
            margin: -20px -20px 40px -20px;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #007bff;
        }
        
        .section h2 {
            color: #007bff;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .section h3 {
            color: #495057;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }
        
        .math-block {
            background: #ffffff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }
        
        .info-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #4caf50;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        
        .outline {
            background: #f1f3f4;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border: 2px solid #e8eaed;
        }
        
        .outline h3 {
            color: #1a73e8;
            margin-bottom: 15px;
        }
        
        .outline ol {
            padding-left: 25px;
        }
        
        .outline li {
            margin: 8px 0;
            font-weight: 500;
        }
        
        .outline li ol {
            margin-top: 5px;
        }
        
        .outline li ol li {
            font-weight: normal;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Exponential Distribution Tutorial</h1>
            <div class="subtitle">A Complete Guide to Understanding the Exponential Distribution</div>
        </div>
        
        <!-- Tutorial Outline -->
        <div class="outline">
            <h3>📚 Tutorial Outline</h3>
            <ol>
                <li><strong>Introduction & Overview</strong>
                    <ol type="a">
                        <li>What is the exponential distribution?</li>
                        <li>Common applications</li>
                        <li>Preview of key concepts</li>
                    </ol>
                </li>
                <li><strong>Mathematical Foundation</strong>
                    <ol type="a">
                        <li>PDF definition and formula</li>
                        <li>Parameter λ and its meaning</li>
                        <li>Unit step function</li>
                    </ol>
                </li>
                <li><strong>Cumulative Distribution Function (CDF)</strong>
                    <ol type="a">
                        <li>Derivation of CDF</li>
                        <li>Mathematical steps</li>
                    </ol>
                </li>
                <li><strong>Statistical Properties</strong>
                    <ol type="a">
                        <li>Expected value calculation</li>
                        <li>Variance derivation</li>
                        <li>Summary of key formulas</li>
                    </ol>
                </li>
                <li><strong>Intuitive Understanding</strong>
                    <ol type="a">
                        <li>Connection to geometric distribution</li>
                        <li>Real-world analogy (coin tossing)</li>
                        <li>Store customer example</li>
                    </ol>
                </li>
                <li><strong>The Memoryless Property</strong>
                    <ol type="a">
                        <li>Definition and meaning</li>
                        <li>Mathematical proof</li>
                        <li>Practical implications</li>
                    </ol>
                </li>
                <li><strong>Visual Summary & Key Takeaways</strong>
                    <ol type="a">
                        <li>Key formulas recap</li>
                        <li>Summary diagram</li>
                    </ol>
                </li>
            </ol>
        </div>
        
        <!-- Section 1: Introduction & Overview -->
        <div class="section">
            <h2>1. Introduction & Overview</h2>
            
            <p>The exponential distribution is one of the most widely used continuous probability distributions in statistics and engineering. It's particularly valuable for modeling the time elapsed between events, especially when those events occur continuously and independently at a constant average rate.</p>
            
            <div class="highlight-box">
                <h3>🎯 Key Applications</h3>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Reliability Engineering:</strong> Time until component failure</li>
                    <li><strong>Queueing Theory:</strong> Time between customer arrivals</li>
                    <li><strong>Physics:</strong> Radioactive decay processes</li>
                    <li><strong>Finance:</strong> Time between market events</li>
                    <li><strong>Biology:</strong> Time between cell divisions</li>
                </ul>
            </div>
            
            <h3>What Makes the Exponential Distribution Special?</h3>
            <p>The exponential distribution has several unique properties that make it extremely useful:</p>
            
            <div class="info-box">
                <strong>🔑 Preview of Key Concepts:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Single Parameter:</strong> Controlled by just one parameter λ (lambda)</li>
                    <li><strong>Memoryless Property:</strong> Past events don't affect future probabilities</li>
                    <li><strong>Simple Mathematical Form:</strong> Easy to work with analytically</li>
                    <li><strong>Connection to Poisson Process:</strong> Natural relationship with event counting</li>
                </ul>
            </div>
            
            <div class="warning-box">
                <strong>📝 Important Note:</strong> The exponential distribution only models positive values (time cannot be negative) and assumes a constant rate of occurrence.
            </div>
        </div>
        
        <!-- Section 2: Mathematical Foundation -->
        <div class="section">
            <h2>2. Mathematical Foundation</h2>
            
            <h3>Formal Definition</h3>
            <p>A continuous random variable $X$ is said to have an exponential distribution with parameter $\lambda > 0$, denoted as $X \sim \text{Exponential}(\lambda)$, if its probability density function (PDF) is given by:</p>
            
            <div class="math-block">
                $$f_X(x) = \begin{cases} 
                \lambda e^{-\lambda x} & \text{if } x > 0 \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>
            
            <h3>Understanding the Parameter λ (Lambda)</h3>
            <p>The parameter $\lambda$ is called the <strong>rate parameter</strong> and has several important interpretations:</p>
            
            <div class="info-box">
                <strong>🔍 Parameter λ Interpretations:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Rate:</strong> Average number of events per unit time</li>
                    <li><strong>Scale:</strong> Higher λ means shorter waiting times</li>
                    <li><strong>Shape:</strong> Controls how quickly the distribution decays</li>
                </ul>
            </div>
            
            <h3>The Unit Step Function</h3>
            <p>To write the PDF more compactly, we use the unit step function:</p>
            
            <div class="math-block">
                $$u(x) = \begin{cases} 
                1 & \text{if } x \geq 0 \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>
            
            <p>This allows us to write the exponential PDF as:</p>
            
            <div class="math-block">
                $$f_X(x) = \lambda e^{-\lambda x} u(x)$$
            </div>
            
            <h3>Visual Understanding</h3>
            <p>The shape of the exponential distribution depends on λ:</p>
            
            <svg width="100%" height="400" viewBox="0 0 800 400" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Grid lines -->
                <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                
                <!-- Axes -->
                <line x1="80" y1="350" x2="750" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                
                <!-- Axis labels -->
                <text x="415" y="380" text-anchor="middle" font-size="14" fill="#333">x</text>
                <text x="40" y="200" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90, 40, 200)">f(x)</text>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Exponential Distribution PDF for Different λ Values</text>
                
                <!-- λ = 0.5 (blue) -->
                <path d="M 80 350 Q 200 200 350 320 Q 500 340 750 348" stroke="#007bff" stroke-width="3" fill="none"/>
                
                <!-- λ = 1 (red) -->
                <path d="M 80 200 Q 150 280 250 330 Q 350 345 500 349" stroke="#dc3545" stroke-width="3" fill="none"/>
                
                <!-- λ = 2 (green) -->
                <path d="M 80 150 Q 120 250 180 320 Q 240 340 350 348" stroke="#28a745" stroke-width="3" fill="none"/>
                
                <!-- Legend -->
                <g transform="translate(550, 80)">
                    <rect x="0" y="0" width="180" height="120" fill="white" stroke="#ddd" stroke-width="1" rx="5"/>
                    <text x="90" y="20" text-anchor="middle" font-size="14" font-weight="bold">Legend</text>
                    
                    <line x1="20" y1="40" x2="50" y2="40" stroke="#007bff" stroke-width="3"/>
                    <text x="60" y="45" font-size="12">λ = 0.5</text>
                    
                    <line x1="20" y1="65" x2="50" y2="65" stroke="#dc3545" stroke-width="3"/>
                    <text x="60" y="70" font-size="12">λ = 1.0</text>
                    
                    <line x1="20" y1="90" x2="50" y2="90" stroke="#28a745" stroke-width="3"/>
                    <text x="60" y="95" font-size="12">λ = 2.0</text>
                </g>
                
                <!-- X-axis ticks -->
                <g stroke="#333" stroke-width="1">
                    <line x1="180" y1="350" x2="180" y2="355"/>
                    <text x="180" y="370" text-anchor="middle" font-size="10">1</text>
                    
                    <line x1="280" y1="350" x2="280" y2="355"/>
                    <text x="280" y="370" text-anchor="middle" font-size="10">2</text>
                    
                    <line x1="380" y1="350" x2="380" y2="355"/>
                    <text x="380" y="370" text-anchor="middle" font-size="10">3</text>
                    
                    <line x1="480" y1="350" x2="480" y2="355"/>
                    <text x="480" y="370" text-anchor="middle" font-size="10">4</text>
                </g>
            </svg>
            
            <div class="warning-box">
                <strong>📊 Key Observation:</strong> As λ increases, the distribution becomes more concentrated near x = 0, indicating shorter waiting times between events.
            </div>
        </div>
        
        <!-- Section 3: Cumulative Distribution Function (CDF) -->
        <div class="section">
            <h2>3. Cumulative Distribution Function (CDF)</h2>
            
            <p>The cumulative distribution function (CDF) gives us the probability that the random variable $X$ is less than or equal to a specific value $x$. Let's derive the CDF step by step.</p>
            
            <h3>Derivation of the CDF</h3>
            <p>For $x > 0$, we calculate:</p>
            
            <div class="math-block">
                $$F_X(x) = P(X \leq x) = \int_0^x \lambda e^{-\lambda t} \, dt$$
            </div>
            
            <p>Let's solve this integral step by step:</p>
            
            <div class="math-block">
                \begin{align}
                F_X(x) &= \int_0^x \lambda e^{-\lambda t} \, dt \\
                &= \lambda \int_0^x e^{-\lambda t} \, dt \\
                &= \lambda \left[ \frac{e^{-\lambda t}}{-\lambda} \right]_0^x \\
                &= \lambda \left( \frac{e^{-\lambda x}}{-\lambda} - \frac{e^{0}}{-\lambda} \right) \\
                &= \lambda \left( \frac{e^{-\lambda x} - 1}{-\lambda} \right) \\
                &= 1 - e^{-\lambda x}
                \end{align}
            </div>
            
            <p>Therefore, the complete CDF is:</p>
            
            <div class="highlight-box">
                <h3>📐 Exponential CDF</h3>
                $$F_X(x) = \begin{cases} 
                1 - e^{-\lambda x} & \text{if } x > 0 \\
                0 & \text{if } x \leq 0
                \end{cases}$$
                
                <p style="margin-top: 15px;">Or more compactly: $F_X(x) = (1 - e^{-\lambda x}) u(x)$</p>
            </div>
            
            <h3>Understanding the CDF</h3>
            <p>The CDF tells us important information about probabilities:</p>
            
            <div class="info-box">
                <strong>🎯 CDF Properties:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>$F_X(0) = 0$:</strong> Zero probability of negative or zero waiting time</li>
                    <li><strong>$\lim_{x \to \infty} F_X(x) = 1$:</strong> Eventually, an event will occur</li>
                    <li><strong>$F_X(x)$ is increasing:</strong> Longer waiting increases probability</li>
                    <li><strong>$P(X > x) = e^{-\lambda x}$:</strong> Survival function (complementary CDF)</li>
                </ul>
            </div>
            
            <h3>Visual Comparison: PDF vs CDF</h3>
            
            <svg width="100%" height="500" viewBox="0 0 800 500" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">PDF vs CDF of Exponential Distribution (λ = 1)</text>
                
                <!-- PDF Plot (Top) -->
                <g transform="translate(0, 0)">
                    <!-- Grid for PDF -->
                    <defs>
                        <pattern id="gridPDF" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f5f5f5" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="80" y="50" width="640" height="180" fill="url(#gridPDF)" />
                    
                    <!-- Axes for PDF -->
                    <line x1="80" y1="230" x2="720" y2="230" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="230" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- PDF Label -->
                    <text x="50" y="45" font-size="14" font-weight="bold" fill="#dc3545">PDF</text>
                    <text x="400" y="250" text-anchor="middle" font-size="12" fill="#333">x</text>
                    <text x="35" y="140" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90, 35, 140)">f(x)</text>
                    
                    <!-- PDF curve (exponential decay) -->
                    <path d="M 80 80 Q 150 120 220 160 Q 290 190 400 210 Q 550 225 720 229" stroke="#dc3545" stroke-width="3" fill="none"/>
                    
                    <!-- PDF area shading -->
                    <path d="M 80 230 L 80 80 Q 150 120 220 160 Q 290 190 400 210 Q 550 225 720 229 L 720 230 Z" fill="#dc3545" opacity="0.2"/>
                </g>
                
                <!-- CDF Plot (Bottom) -->
                <g transform="translate(0, 220)">
                    <!-- Grid for CDF -->
                    <rect x="80" y="50" width="640" height="180" fill="url(#gridPDF)" />
                    
                    <!-- Axes for CDF -->
                    <line x1="80" y1="230" x2="720" y2="230" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="230" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- CDF Label -->
                    <text x="50" y="45" font-size="14" font-weight="bold" fill="#28a745">CDF</text>
                    <text x="400" y="250" text-anchor="middle" font-size="12" fill="#333">x</text>
                    <text x="35" y="140" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90, 35, 140)">F(x)</text>
                    
                    <!-- CDF curve (1 - e^(-x)) -->
                    <path d="M 80 230 Q 150 150 250 90 Q 400 60 550 55 Q 650 53 720 52" stroke="#28a745" stroke-width="3" fill="none"/>
                    
                    <!-- Horizontal line at y=1 -->
                    <line x1="80" y1="50" x2="720" y2="50" stroke="#666" stroke-width="1" stroke-dasharray="5,5"/>
                    <text x="730" y="55" font-size="10" fill="#666">1</text>
                </g>
                
                <!-- X-axis ticks (shared) -->
                <g stroke="#333" stroke-width="1">
                    <!-- For PDF -->
                    <line x1="180" y1="230" x2="180" y2="235"/>
                    <text x="180" y="245" text-anchor="middle" font-size="10">1</text>
                    
                    <line x1="280" y1="230" x2="280" y2="235"/>
                    <text x="280" y="245" text-anchor="middle" font-size="10">2</text>
                    
                    <line x1="380" y1="230" x2="380" y2="235"/>
                    <text x="380" y="245" text-anchor="middle" font-size="10">3</text>
                    
                    <line x1="480" y1="230" x2="480" y2="235"/>
                    <text x="480" y="245" text-anchor="middle" font-size="10">4</text>
                    
                    <!-- For CDF -->
                    <line x1="180" y1="450" x2="180" y2="455"/>
                    <text x="180" y="465" text-anchor="middle" font-size="10">1</text>
                    
                    <line x1="280" y1="450" x2="280" y2="455"/>
                    <text x="280" y="465" text-anchor="middle" font-size="10">2</text>
                    
                    <line x1="380" y1="450" x2="380" y2="455"/>
                    <text x="380" y="465" text-anchor="middle" font-size="10">3</text>
                    
                    <line x1="480" y1="450" x2="480" y2="455"/>
                    <text x="480" y="465" text-anchor="middle" font-size="10">4</text>
                </g>
            </svg>
            
            <div class="warning-box">
                <strong>💡 Key Insight:</strong> The CDF approaches 1 asymptotically, meaning there's always a chance (however small) of very long waiting times, but the probability decreases exponentially.
            </div>
        </div>
        
        <!-- Section 4: Statistical Properties -->
        <div class="section">
            <h2>4. Statistical Properties</h2>
            
            <p>Now let's derive the key statistical properties of the exponential distribution: the expected value (mean) and variance.</p>
            
            <h3>Expected Value (Mean)</h3>
            <p>For $X \sim \text{Exponential}(\lambda)$, we calculate the expected value using integration by parts:</p>
            
            <div class="math-block">
                \begin{align}
                E[X] &= \int_0^{\infty} x \lambda e^{-\lambda x} \, dx \\
                &= \frac{1}{\lambda} \int_0^{\infty} y e^{-y} \, dy \quad \text{(substituting } y = \lambda x \text{)} \\
                &= \frac{1}{\lambda} \left[ -e^{-y} - ye^{-y} \right]_0^{\infty} \\
                &= \frac{1}{\lambda} \left[ 0 - (-1) \right] \\
                &= \frac{1}{\lambda}
                \end{align}
            </div>
            
            <h3>Second Moment</h3>
            <p>To find the variance, we first need $E[X^2]$:</p>
            
            <div class="math-block">
                \begin{align}
                E[X^2] &= \int_0^{\infty} x^2 \lambda e^{-\lambda x} \, dx \\
                &= \frac{1}{\lambda^2} \int_0^{\infty} y^2 e^{-y} \, dy \quad \text{(substituting } y = \lambda x \text{)} \\
                &= \frac{1}{\lambda^2} \left[ -2e^{-y} - 2ye^{-y} - y^2e^{-y} \right]_0^{\infty} \\
                &= \frac{1}{\lambda^2} \left[ 0 - (-2) \right] \\
                &= \frac{2}{\lambda^2}
                \end{align}
            </div>
            
            <h3>Variance</h3>
            <p>Using the formula $\text{Var}(X) = E[X^2] - (E[X])^2$:</p>
            
            <div class="math-block">
                \text{Var}(X) = \frac{2}{\lambda^2} - \left(\frac{1}{\lambda}\right)^2 = \frac{2}{\lambda^2} - \frac{1}{\lambda^2} = \frac{1}{\lambda^2}
            </div>
            
            <div class="highlight-box">
                <h3>🎯 Key Statistical Properties</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div>
                        <strong>Expected Value:</strong>
                        $$E[X] = \frac{1}{\lambda}$$
                    </div>
                    <div>
                        <strong>Variance:</strong>
                        $$\text{Var}(X) = \frac{1}{\lambda^2}$$
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Standard Deviation:</strong>
                    $$\sigma = \sqrt{\text{Var}(X)} = \frac{1}{\lambda}$$
                </div>
            </div>
            
            <h3>Interpretation of the Results</h3>
            
            <div class="info-box">
                <strong>🔍 What These Statistics Tell Us:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Mean = Standard Deviation:</strong> For exponential distributions, $E[X] = \sigma = \frac{1}{\lambda}$</li>
                    <li><strong>Coefficient of Variation = 1:</strong> The ratio $\frac{\sigma}{\mu} = 1$ is constant</li>
                    <li><strong>Higher λ = Shorter waits:</strong> Larger rate parameter means smaller mean waiting time</li>
                    <li><strong>High Variability:</strong> Standard deviation equals the mean, indicating high relative variability</li>
                </ul>
            </div>
            
            <h3>Visual Representation of Statistical Properties</h3>
            
            <svg width="100%" height="400" viewBox="0 0 800 400" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Mean and Standard Deviation for Different λ Values</text>
                
                <!-- Grid -->
                <defs>
                    <pattern id="gridStat" width="30" height="30" patternUnits="userSpaceOnUse">
                        <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect x="80" y="50" width="640" height="300" fill="url(#gridStat)" />
                
                <!-- Axes -->
                <line x1="80" y1="350" x2="720" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                
                <!-- Axis labels -->
                <text x="400" y="375" text-anchor="middle" font-size="14" fill="#333">x</text>
                <text x="40" y="200" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90, 40, 200)">f(x)</text>
                
                <!-- λ = 0.5: mean = 2, σ = 2 -->
                <g>
                    <path d="M 80 300 Q 180 200 280 280 Q 380 320 500 340 Q 620 345 720 348" stroke="#007bff" stroke-width="3" fill="none"/>
                    <!-- Mean line -->
                    <line x1="280" y1="50" x2="280" y2="350" stroke="#007bff" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="285" y="70" font-size="12" fill="#007bff">μ = 2</text>
                    <!-- Standard deviation bracket -->
                    <line x1="280" y1="340" x2="480" y2="340" stroke="#007bff" stroke-width="2"/>
                    <line x1="280" y1="335" x2="280" y2="345" stroke="#007bff" stroke-width="2"/>
                    <line x1="480" y1="335" x2="480" y2="345" stroke="#007bff" stroke-width="2"/>
                    <text x="380" y="335" text-anchor="middle" font-size="10" fill="#007bff">σ = 2</text>
                </g>
                
                <!-- λ = 1: mean = 1, σ = 1 -->
                <g>
                    <path d="M 80 200 Q 130 260 180 300 Q 230 330 330 345 Q 430 350 530 352" stroke="#dc3545" stroke-width="3" fill="none"/>
                    <!-- Mean line -->
                    <line x1="180" y1="50" x2="180" y2="350" stroke="#dc3545" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="185" y="90" font-size="12" fill="#dc3545">μ = 1</text>
                    <!-- Standard deviation bracket -->
                    <line x1="180" y1="320" x2="280" y2="320" stroke="#dc3545" stroke-width="2"/>
                    <line x1="180" y1="315" x2="180" y2="325" stroke="#dc3545" stroke-width="2"/>
                    <line x1="280" y1="315" x2="280" y2="325" stroke="#dc3545" stroke-width="2"/>
                    <text x="230" y="315" text-anchor="middle" font-size="10" fill="#dc3545">σ = 1</text>
                </g>
                
                <!-- λ = 2: mean = 0.5, σ = 0.5 -->
                <g>
                    <path d="M 80 150 Q 105 220 130 280 Q 155 320 205 340 Q 255 350 355 352" stroke="#28a745" stroke-width="3" fill="none"/>
                    <!-- Mean line -->
                    <line x1="130" y1="50" x2="130" y2="350" stroke="#28a745" stroke-width="2" stroke-dasharray="5,5"/>
                    <text x="135" y="110" font-size="12" fill="#28a745">μ = 0.5</text>
                    <!-- Standard deviation bracket -->
                    <line x1="130" y1="300" x2="180" y2="300" stroke="#28a745" stroke-width="2"/>
                    <line x1="130" y1="295" x2="130" y2="305" stroke="#28a745" stroke-width="2"/>
                    <line x1="180" y1="295" x2="180" y2="305" stroke="#28a745" stroke-width="2"/>
                    <text x="155" y="295" text-anchor="middle" font-size="10" fill="#28a745">σ = 0.5</text>
                </g>
                
                <!-- Legend -->
                <g transform="translate(550, 80)">
                    <rect x="0" y="0" width="150" height="140" fill="white" stroke="#ddd" stroke-width="1" rx="5"/>
                    <text x="75" y="20" text-anchor="middle" font-size="14" font-weight="bold">Legend</text>
                    
                    <line x1="15" y1="40" x2="45" y2="40" stroke="#007bff" stroke-width="3"/>
                    <text x="50" y="45" font-size="11">λ = 0.5</text>
                    
                    <line x1="15" y1="65" x2="45" y2="65" stroke="#dc3545" stroke-width="3"/>
                    <text x="50" y="70" font-size="11">λ = 1.0</text>
                    
                    <line x1="15" y1="90" x2="45" y2="90" stroke="#28a745" stroke-width="3"/>
                    <text x="50" y="95" font-size="11">λ = 2.0</text>
                    
                    <line x1="15" y1="115" x2="45" y2="115" stroke="#666" stroke-width="2" stroke-dasharray="3,3"/>
                    <text x="50" y="120" font-size="11">Mean (μ)</text>
                </g>
                
                <!-- X-axis ticks -->
                <g stroke="#333" stroke-width="1">
                    <line x1="130" y1="350" x2="130" y2="355"/>
                    <text x="130" y="370" text-anchor="middle" font-size="10">0.5</text>
                    
                    <line x1="180" y1="350" x2="180" y2="355"/>
                    <text x="180" y="370" text-anchor="middle" font-size="10">1</text>
                    
                    <line x1="280" y1="350" x2="280" y2="355"/>
                    <text x="280" y="370" text-anchor="middle" font-size="10">2</text>
                    
                    <line x1="380" y1="350" x2="380" y2="355"/>
                    <text x="380" y="370" text-anchor="middle" font-size="10">3</text>
                    
                    <line x1="480" y1="350" x2="480" y2="355"/>
                    <text x="480" y="370" text-anchor="middle" font-size="10">4</text>
                </g>
            </svg>
            
            <div class="warning-box">
                <strong>🎓 Important Insight:</strong> Notice that for exponential distributions, the mean and standard deviation are always equal. This is a unique property that makes the exponential distribution highly predictable in its variability pattern.
            </div>
        </div>
        
        <!-- Section 5: Intuitive Understanding -->
        <div class="section">
            <h2>5. Intuitive Understanding</h2>
            
            <p>The exponential distribution becomes much more intuitive when we understand its connection to the geometric distribution and see real-world examples.</p>
            
            <h3>Connection to the Geometric Distribution</h3>
            <p>The exponential distribution can be viewed as a <strong>continuous analogue</strong> of the geometric distribution. Here's how:</p>
            
            <div class="info-box">
                <strong>🎲 Discrete to Continuous Transition:</strong>
                <ol style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Geometric Distribution:</strong> Number of coin tosses until first success</li>
                    <li><strong>Make time continuous:</strong> Coin tosses every Δ seconds</li>
                    <li><strong>Make probability small:</strong> Success probability p = Δλ</li>
                    <li><strong>Take the limit:</strong> As Δ → 0, we get the exponential distribution</li>
                </ol>
            </div>
            
            <h3>The Coin Tossing Analogy</h3>
            <p>Imagine this scenario:</p>
            
            <div class="highlight-box">
                <h3>🪙 Continuous Coin Tossing</h3>
                <p style="margin: 15px 0;">Suppose coin tosses occur every <strong>Δ seconds</strong>, and in each toss the probability of success is <strong>p = Δλ</strong>. As Δ becomes very small:</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Coin tosses become very frequent (almost continuous)</li>
                    <li>Success probability becomes very small in each trial</li>
                    <li>The time until first success follows an exponential distribution</li>
                </ul>
            </div>
            
            <h3>Real-World Store Example</h3>
            <p>Let's apply this to a practical scenario:</p>
            
            <div class="math-block" style="text-align: left; background: #f8f9fa;">
                <h4 style="color: #007bff; margin-bottom: 15px;">🏪 Store Customer Arrival Example</h4>
                <p><strong>Scenario:</strong> You're waiting for the next customer to enter your store.</p>
                
                <p><strong>Model:</strong> In each millisecond, there's a very small probability that a customer enters.</p>
                
                <p><strong>Think of it as:</strong> Every millisecond, you "toss a coin" with a very small P(Heads) = probability of customer arrival.</p>
                
                <p><strong>Result:</strong> The time until the next customer arrives follows an exponential distribution.</p>
            </div>
            
            <h3>Visual Comparison: Discrete vs Continuous</h3>
            
            <svg width="100%" height="450" viewBox="0 0 800 450" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">From Geometric to Exponential: The Continuous Limit</text>
                
                <!-- Discrete Geometric (Top) -->
                <g transform="translate(0, 0)">
                    <text x="50" y="60" font-size="14" font-weight="bold" fill="#dc3545">Discrete: Geometric Distribution</text>
                    
                    <!-- Axes -->
                    <line x1="80" y1="180" x2="720" y2="180" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="180" x2="80" y2="70" stroke="#333" stroke-width="2"/>
                    
                    <!-- Discrete bars -->
                    <g fill="#dc3545" opacity="0.7">
                        <rect x="120" y="90" width="20" height="90"/>
                        <rect x="160" y="105" width="20" height="75"/>
                        <rect x="200" y="115" width="20" height="65"/>
                        <rect x="240" y="125" width="20" height="55"/>
                        <rect x="280" y="135" width="20" height="45"/>
                        <rect x="320" y="145" width="20" height="35"/>
                        <rect x="360" y="150" width="20" height="30"/>
                        <rect x="400" y="155" width="20" height="25"/>
                        <rect x="440" y="160" width="20" height="20"/>
                        <rect x="480" y="165" width="20" height="15"/>
                    </g>
                    
                    <!-- X-axis labels -->
                    <text x="130" y="200" text-anchor="middle" font-size="10">1</text>
                    <text x="170" y="200" text-anchor="middle" font-size="10">2</text>
                    <text x="210" y="200" text-anchor="middle" font-size="10">3</text>
                    <text x="250" y="200" text-anchor="middle" font-size="10">4</text>
                    <text x="290" y="200" text-anchor="middle" font-size="10">5</text>
                    
                    <text x="400" y="215" text-anchor="middle" font-size="12" fill="#333">Number of Trials</text>
                </g>
                
                <!-- Arrow -->
                <g transform="translate(350, 220)">
                    <path d="M 0 0 L 0 30 L -10 20 M 0 30 L 10 20" stroke="#666" stroke-width="2" fill="none"/>
                    <text x="50" y="20" font-size="12" fill="#666">As Δ → 0</text>
                </g>
                
                <!-- Continuous Exponential (Bottom) -->
                <g transform="translate(0, 240)">
                    <text x="50" y="30" font-size="14" font-weight="bold" fill="#28a745">Continuous: Exponential Distribution</text>
                    
                    <!-- Grid -->
                    <defs>
                        <pattern id="gridIntuitive" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f5f5f5" stroke-width="1"/>
                        </pattern>
                    </defs>
                    <rect x="80" y="40" width="640" height="140" fill="url(#gridIntuitive)" />
                    
                    <!-- Axes -->
                    <line x1="80" y1="180" x2="720" y2="180" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="180" x2="80" y2="40" stroke="#333" stroke-width="2"/>
                    
                    <!-- Continuous curve -->
                    <path d="M 80 60 Q 150 100 220 130 Q 320 155 450 170 Q 580 175 720 178" stroke="#28a745" stroke-width="3" fill="none"/>
                    
                    <!-- Area under curve -->
                    <path d="M 80 180 L 80 60 Q 150 100 220 130 Q 320 155 450 170 Q 580 175 720 178 L 720 180 Z" fill="#28a745" opacity="0.2"/>
                    
                    <!-- X-axis labels -->
                    <text x="180" y="200" text-anchor="middle" font-size="10">1</text>
                    <text x="280" y="200" text-anchor="middle" font-size="10">2</text>
                    <text x="380" y="200" text-anchor="middle" font-size="10">3</text>
                    <text x="480" y="200" text-anchor="middle" font-size="10">4</text>
                    
                    <text x="400" y="215" text-anchor="middle" font-size="12" fill="#333">Continuous Time</text>
                </g>
                
                <!-- Side explanation -->
                <g transform="translate(550, 100)">
                    <rect x="0" y="0" width="220" height="120" fill="#f8f9fa" stroke="#ddd" stroke-width="1" rx="5"/>
                    <text x="110" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Key Insight</text>
                    
                    <text x="10" y="40" font-size="11" fill="#333">• Discrete trials become</text>
                    <text x="20" y="55" font-size="11" fill="#333">continuous time</text>
                    
                    <text x="10" y="75" font-size="11" fill="#333">• Small success probability</text>
                    <text x="20" y="90" font-size="11" fill="#333">becomes continuous rate</text>
                    
                    <text x="10" y="110" font-size="11" fill="#333">• Result: Exponential waiting</text>
                </g>
            </svg>
            
            <h3>Why This Matters</h3>
            
            <div class="warning-box">
                <strong>🎯 Practical Implications:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Independence:</strong> Each "moment" is independent, just like coin tosses</li>
                    <li><strong>Constant Rate:</strong> The probability of an event in any small time interval is constant</li>
                    <li><strong>Memoryless Nature:</strong> Past waiting doesn't affect future waiting (just like independent coin tosses)</li>
                    <li><strong>Realistic Modeling:</strong> Many real processes follow this pattern naturally</li>
                </ul>
            </div>
            
            <h3>More Real-World Examples</h3>
            
            <div class="info-box">
                <strong>🌍 Where You'll Find Exponential Distributions:</strong>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
                    <div>
                        <strong>Technology:</strong>
                        <ul style="margin: 5px 0; padding-left: 15px; font-size: 14px;">
                            <li>Time between system failures</li>
                            <li>Network packet intervals</li>
                            <li>Server response times</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Nature & Life:</strong>
                        <ul style="margin: 5px 0; padding-left: 15px; font-size: 14px;">
                            <li>Radioactive decay</li>
                            <li>Time between earthquakes</li>
                            <li>Cell division intervals</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Section 6: The Memoryless Property -->
        <div class="section">
            <h2>6. The Memoryless Property</h2>
            
            <p>The <strong>memoryless property</strong> is perhaps the most distinctive and important characteristic of the exponential distribution. It states that the distribution of future waiting time is independent of how long you've already waited.</p>
            
            <h3>Mathematical Statement</h3>
            
            <div class="highlight-box">
                <h3>🧠 Memoryless Property</h3>
                <p>If $X \sim \text{Exponential}(\lambda)$, then for all $a, x \geq 0$:</p>
                $$P(X > x + a \mid X > a) = P(X > x)$$
                <p style="margin-top: 15px;"><strong>In words:</strong> "The probability of waiting an additional $x$ time units, given that you've already waited $a$ time units, is the same as the probability of waiting $x$ time units from the start."</p>
            </div>
            
            <h3>Intuitive Understanding</h3>
            
            <div class="math-block" style="text-align: left; background: #f8f9fa;">
                <h4 style="color: #007bff; margin-bottom: 15px;">🚌 Bus Stop Example</h4>
                <p><strong>Scenario:</strong> You're waiting for a bus that arrives according to an exponential distribution.</p>
                
                <p><strong>Memoryless means:</strong> Even if you've been waiting for 20 minutes, the probability that you'll wait another 10 minutes is exactly the same as if you had just arrived at the bus stop.</p>
                
                <p><strong>Why?</strong> The bus doesn't "remember" that you've been waiting. Each moment is independent.</p>
            </div>
            
            <h3>Mathematical Proof</h3>
            <p>Let's prove the memoryless property step by step:</p>
            
            <div class="math-block">
                \begin{align}
                P(X > x + a \mid X > a) &= \frac{P(X > x + a, X > a)}{P(X > a)} \\\\
                &= \frac{P(X > x + a)}{P(X > a)} \quad \text{(since } x + a > a \text{)} \\\\
                &= \frac{1 - F_X(x + a)}{1 - F_X(a)} \\\\
                &= \frac{e^{-\lambda(x + a)}}{e^{-\lambda a}} \\\\
                &= e^{-\lambda x} \\\\
                &= P(X > x)
                \end{align}
            </div>
            
            <h3>Step-by-Step Explanation</h3>
            
            <div class="info-box">
                <strong>🔍 Proof Breakdown:</strong>
                <ol style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Step 1:</strong> Use conditional probability definition</li>
                    <li><strong>Step 2:</strong> Simplify the intersection (if $X > x + a$, then automatically $X > a$)</li>
                    <li><strong>Step 3:</strong> Convert to CDF using $P(X > t) = 1 - F_X(t)$</li>
                    <li><strong>Step 4:</strong> Substitute exponential CDF: $F_X(t) = 1 - e^{-\lambda t}$</li>
                    <li><strong>Step 5:</strong> Simplify the fraction using properties of exponentials</li>
                    <li><strong>Step 6:</strong> Recognize this equals $P(X > x)$</li>
                </ol>
            </div>
            
            <h3>Visual Demonstration</h3>
            
            <svg width="100%" height="500" viewBox="0 0 800 500" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Memoryless Property Visualization</text>
                
                <!-- Timeline -->
                <line x1="100" y1="100" x2="700" y2="100" stroke="#333" stroke-width="3"/>
                
                <!-- Time markers -->
                <g stroke="#333" stroke-width="2">
                    <line x1="200" y1="95" x2="200" y2="105"/>
                    <text x="200" y="125" text-anchor="middle" font-size="12">a</text>
                    
                    <line x1="400" y1="95" x2="400" y2="105"/>
                    <text x="400" y="125" text-anchor="middle" font-size="12">a + x</text>
                    
                    <line x1="100" y1="95" x2="100" y2="105"/>
                    <text x="100" y="125" text-anchor="middle" font-size="12">0</text>
                </g>
                
                <!-- Waiting periods -->
                <g>
                    <!-- Period 1: 0 to a -->
                    <rect x="100" y="80" width="100" height="20" fill="#ff6b6b" opacity="0.6"/>
                    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#dc3545">Already waited</text>
                    
                    <!-- Period 2: a to a+x -->
                    <rect x="200" y="80" width="200" height="20" fill="#74b9ff" opacity="0.6"/>
                    <text x="300" y="70" text-anchor="middle" font-size="12" fill="#0984e3">Additional wait (x)</text>
                </g>
                
                <!-- Comparison scenarios -->
                <g transform="translate(0, 180)">
                    <text x="50" y="0" font-size="14" font-weight="bold" fill="#333">Memoryless Property Comparison:</text>
                    
                    <!-- Scenario 1 -->
                    <g transform="translate(50, 30)">
                        <rect x="0" y="0" width="300" height="80" fill="#f8f9fa" stroke="#dee2e6" rx="5"/>
                        <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc3545">Scenario 1: Already waited 'a'</text>
                        <text x="20" y="40" font-size="11">Question: What's P(wait another x)?</text>
                        <text x="20" y="55" font-size="11" fill="#dc3545">Answer: P(X > x + a | X > a)</text>
                        <text x="20" y="70" font-size="11" fill="#dc3545">= e^(-λx)</text>
                    </g>
                    
                    <!-- Scenario 2 -->
                    <g transform="translate(400, 30)">
                        <rect x="0" y="0" width="300" height="80" fill="#f8f9fa" stroke="#dee2e6" rx="5"/>
                        <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#28a745">Scenario 2: Just arrived</text>
                        <text x="20" y="40" font-size="11">Question: What's P(wait x)?</text>
                        <text x="20" y="55" font-size="11" fill="#28a745">Answer: P(X > x)</text>
                        <text x="20" y="70" font-size="11" fill="#28a745">= e^(-λx)</text>
                    </g>
                    
                    <!-- Equality -->
                    <g transform="translate(350, 140)">
                        <text x="50" y="0" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">SAME!</text>
                        <path d="M 0 -20 Q 25 -40 50 -20 Q 75 0 100 -20" stroke="#007bff" stroke-width="3" fill="none"/>
                    </g>
                </g>
                
                <!-- Real-world interpretation -->
                <g transform="translate(50, 380)">
                    <rect x="0" y="0" width="700" height="100" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="10"/>
                    <text x="350" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2e7d32">Real-World Interpretation</text>
                    
                    <text x="20" y="45" font-size="12" fill="#2e7d32"><strong>Customer Service:</strong> Whether you've been on hold for 5 minutes or just called,</text>
                    <text x="20" y="60" font-size="12" fill="#2e7d32">the expected additional wait time is the same.</text>
                    
                    <text x="20" y="80" font-size="12" fill="#2e7d32"><strong>System Reliability:</strong> A component that has worked for 1000 hours has the same</text>
                    <text x="20" y="95" font-size="12" fill="#2e7d32">probability of failing in the next hour as a brand new component.</text>
                </g>
            </svg>
            
            <h3>Why Does This Happen?</h3>
            
            <div class="warning-box">
                <strong>🤔 The Reason Behind Memorylessness:</strong>
                <p style="margin: 10px 0;">The memoryless property exists because the exponential distribution models processes where:</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Each moment is independent:</strong> Like independent coin tosses</li>
                    <li><strong>Constant rate:</strong> The "hazard rate" doesn't change over time</li>
                    <li><strong>No aging:</strong> There's no wear-and-tear or learning effect</li>
                    <li><strong>Pure randomness:</strong> Events occur purely by chance, not due to accumulating factors</li>
                </ul>
            </div>
            
            <h3>Practical Implications</h3>
            
            <div class="info-box">
                <strong>💡 When Memorylessness Applies (and When It Doesn't):</strong>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div>
                        <strong style="color: #28a745;">✅ Good Models:</strong>
                        <ul style="margin: 5px 0; padding-left: 15px; font-size: 14px;">
                            <li>Radioactive decay</li>
                            <li>Random customer arrivals</li>
                            <li>Network packet arrivals</li>
                            <li>Electronic component failures (sometimes)</li>
                        </ul>
                    </div>
                    <div>
                        <strong style="color: #dc3545;">❌ Poor Models:</strong>
                        <ul style="margin: 5px 0; padding-left: 15px; font-size: 14px;">
                            <li>Human lifespans (aging matters!)</li>
                            <li>Mechanical wear-out failures</li>
                            <li>Learning processes</li>
                            <li>Scheduled events</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Section 7: Visual Summary & Key Takeaways -->
        <div class="section">
            <h2>7. Visual Summary & Key Takeaways</h2>
            
            <p>Let's consolidate everything we've learned about the exponential distribution into a comprehensive summary.</p>
            
            <h3>Complete Formula Reference</h3>
            
            <div class="highlight-box">
                <h3>📋 Exponential Distribution - Complete Reference</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div>
                        <strong>Probability Density Function:</strong>
                        $$f_X(x) = \lambda e^{-\lambda x} \text{ for } x > 0$$
                        
                        <strong>Cumulative Distribution Function:</strong>
                        $$F_X(x) = 1 - e^{-\lambda x} \text{ for } x > 0$$
                    </div>
                    <div>
                        <strong>Expected Value:</strong>
                        $$E[X] = \frac{1}{\lambda}$$
                        
                        <strong>Variance:</strong>
                        $$\text{Var}(X) = \frac{1}{\lambda^2}$$
                        
                        <strong>Standard Deviation:</strong>
                        $$\sigma = \frac{1}{\lambda}$$
                    </div>
                </div>
                <div style="margin-top: 15px; text-align: center;">
                    <strong>Memoryless Property:</strong>
                    $$P(X > x + a \mid X > a) = P(X > x) = e^{-\lambda x}$$
                </div>
            </div>
            
            <h3>Comprehensive Summary Diagram</h3>
            
            <svg width="100%" height="600" viewBox="0 0 900 600" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="450" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#007bff">Exponential Distribution: Complete Visual Summary</text>
                
                <!-- Central concept -->
                <g transform="translate(400, 80)">
                    <circle r="60" fill="#007bff" opacity="0.1" stroke="#007bff" stroke-width="3"/>
                    <text x="0" y="-10" text-anchor="middle" font-size="14" font-weight="bold" fill="#007bff">Exponential</text>
                    <text x="0" y="5" text-anchor="middle" font-size="14" font-weight="bold" fill="#007bff">Distribution</text>
                    <text x="0" y="25" text-anchor="middle" font-size="12" fill="#007bff">X ~ Exp(λ)</text>
                </g>
                
                <!-- Key properties branches -->
                
                <!-- PDF Branch -->
                <g transform="translate(150, 200)">
                    <rect x="0" y="0" width="180" height="80" fill="#dc3545" opacity="0.1" stroke="#dc3545" stroke-width="2" rx="10"/>
                    <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#dc3545">PDF</text>
                    <text x="90" y="35" text-anchor="middle" font-size="11" fill="#dc3545">f(x) = λe^(-λx)</text>
                    <text x="90" y="50" text-anchor="middle" font-size="10" fill="#dc3545">Decreasing exponentially</text>
                    <text x="90" y="65" text-anchor="middle" font-size="10" fill="#dc3545">Always positive</text>
                    
                    <!-- Connection line -->
                    <path d="M 180 40 Q 250 40 320 100" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                </g>
                
                <!-- CDF Branch -->
                <g transform="translate(570, 200)">
                    <rect x="0" y="0" width="180" height="80" fill="#28a745" opacity="0.1" stroke="#28a745" stroke-width="2" rx="10"/>
                    <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#28a745">CDF</text>
                    <text x="90" y="35" text-anchor="middle" font-size="11" fill="#28a745">F(x) = 1 - e^(-λx)</text>
                    <text x="90" y="50" text-anchor="middle" font-size="10" fill="#28a745">Increasing to 1</text>
                    <text x="90" y="65" text-anchor="middle" font-size="10" fill="#28a745">S-shaped curve</text>
                    
                    <!-- Connection line -->
                    <path d="M 0 40 Q -70 40 -140 100" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                </g>
                
                <!-- Statistics Branch -->
                <g transform="translate(300, 350)">
                    <rect x="0" y="0" width="200" height="100" fill="#ffc107" opacity="0.1" stroke="#ffc107" stroke-width="2" rx="10"/>
                    <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#e67e22">Statistical Properties</text>
                    <text x="100" y="35" text-anchor="middle" font-size="11" fill="#e67e22">Mean = 1/λ</text>
                    <text x="100" y="50" text-anchor="middle" font-size="11" fill="#e67e22">Variance = 1/λ²</text>
                    <text x="100" y="65" text-anchor="middle" font-size="11" fill="#e67e22">Std Dev = 1/λ</text>
                    <text x="100" y="80" text-anchor="middle" font-size="10" fill="#e67e22">Mean = Standard Deviation</text>
                    <text x="100" y="95" text-anchor="middle" font-size="10" fill="#e67e22">Coefficient of Variation = 1</text>
                    
                    <!-- Connection line -->
                    <path d="M 100 0 Q 100 -50 400 -130" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                </g>
                
                <!-- Memoryless Property -->
                <g transform="translate(50, 500)">
                    <rect x="0" y="0" width="250" height="80" fill="#9b59b6" opacity="0.1" stroke="#9b59b6" stroke-width="2" rx="10"/>
                    <text x="125" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#9b59b6">Memoryless Property</text>
                    <text x="125" y="35" text-anchor="middle" font-size="11" fill="#9b59b6">P(X > x+a | X > a) = P(X > x)</text>
                    <text x="125" y="50" text-anchor="middle" font-size="10" fill="#9b59b6">Past doesn't affect future</text>
                    <text x="125" y="65" text-anchor="middle" font-size="10" fill="#9b59b6">Unique among continuous distributions</text>
                </g>
                
                <!-- Applications -->
                <g transform="translate(600, 500)">
                    <rect x="0" y="0" width="250" height="80" fill="#17a2b8" opacity="0.1" stroke="#17a2b8" stroke-width="2" rx="10"/>
                    <text x="125" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#17a2b8">Key Applications</text>
                    <text x="125" y="35" text-anchor="middle" font-size="10" fill="#17a2b8">• Time between arrivals</text>
                    <text x="125" y="48" text-anchor="middle" font-size="10" fill="#17a2b8">• Component lifetimes</text>
                    <text x="125" y="61" text-anchor="middle" font-size="10" fill="#17a2b8">• Radioactive decay</text>
                    <text x="125" y="74" text-anchor="middle" font-size="10" fill="#17a2b8">• Service times</text>
                </g>
                
                <!-- Parameter Lambda -->
                <g transform="translate(380, 250)">
                    <ellipse rx="80" ry="30" fill="#ff6b6b" opacity="0.1" stroke="#ff6b6b" stroke-width="2"/>
                    <text x="0" y="-5" text-anchor="middle" font-size="12" font-weight="bold" fill="#ff6b6b">Parameter λ</text>
                    <text x="0" y="8" text-anchor="middle" font-size="10" fill="#ff6b6b">Rate parameter</text>
                    <text x="0" y="20" text-anchor="middle" font-size="10" fill="#ff6b6b">Higher λ = Shorter waits</text>
                </g>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                    </marker>
                </defs>
            </svg>
            
            <h3>Key Learning Points</h3>
            
            <div class="info-box">
                <strong>🎓 What You Should Remember:</strong>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div>
                        <strong>Mathematical:</strong>
                        <ul style="margin: 5px 0; padding-left: 15px; font-size: 14px;">
                            <li>Single parameter λ controls everything</li>
                            <li>Mean equals standard deviation (1/λ)</li>
                            <li>Memoryless: P(X>x+a|X>a) = P(X>x)</li>
                            <li>PDF: λe^(-λx), CDF: 1-e^(-λx)</li>
                        </ul>
                    </div>
                    <div>
                        <strong>Intuitive:</strong>
                        <ul style="margin: 5px 0; padding-left: 15px; font-size: 14px;">
                            <li>Models "waiting times" between events</li>
                            <li>Past waiting doesn't help predict future</li>
                            <li>Continuous version of geometric distribution</li>
                            <li>Best for truly random, independent events</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <h3>When to Use Exponential Distribution</h3>
            
            <div class="warning-box">
                <strong>🎯 Decision Guide:</strong>
                <p style="margin: 10px 0;"><strong>Use exponential distribution when:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Events occur randomly and independently</li>
                    <li>Rate of occurrence is constant over time</li>
                    <li>You're modeling waiting times or lifetimes</li>
                    <li>No "aging" or "learning" effects are present</li>
                    <li>You need mathematical tractability</li>
                </ul>
                
                <p style="margin: 15px 0 10px 0;"><strong>Don't use when:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>There are aging or wear-out effects</li>
                    <li>Events are scheduled or predictable</li>
                    <li>Rate changes over time</li>
                    <li>Past events influence future ones</li>
                </ul>
            </div>
            
            <div class="highlight-box" style="margin-top: 30px;">
                <h3>🌟 Congratulations!</h3>
                <p style="margin: 15px 0;">You now have a comprehensive understanding of the exponential distribution - from its mathematical foundations to its real-world applications. You understand why it's memoryless, how it connects to the geometric distribution, and when to apply it in practice.</p>
                
                <p style="margin: 15px 0;"><strong>Next steps:</strong> Try applying these concepts to real datasets, explore the connection to Poisson processes, or investigate other continuous distributions like the gamma distribution (which generalizes the exponential).</p>
            </div>
        </div>
    </div>
</body>
</html> 