<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Theory of Linear Models Tutorial</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        nav {
            background: #34495e;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #3498db;
        }
        
        main {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            border-left: 5px solid #3498db;
        }
        
        h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .formula-box {
            background: #fff;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }
        
        .definition-box {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .theorem-box {
            background: #f0e6ff;
            border-left: 4px solid #8e44ad;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .proof-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .step-box {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        .visualization {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .calculation-box {
            background: #e8f4f8;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .result-box {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .derivation-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .property-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .property-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .notation-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .example-box {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .insight-box {
            background: #fff8e1;
            border-left: 4px solid #ff9800;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            header {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            main {
                padding: 1rem;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>The Theory of Linear Models</h1>
            <p class="subtitle">From Least Squares to Optimality Theory</p>
            <p>Complete mathematical framework for linear model estimation and inference</p>
        </header>
        
        <nav>
            <div class="nav-links">
                <a href="#introduction">Introduction</a>
                <a href="#least-squares">Least Squares</a>
                <a href="#distribution">Distribution</a>
                <a href="#t-tests">t-Tests</a>
                <a href="#f-tests">F-Tests</a>
                <a href="#influence">Influence Matrix</a>
                <a href="#residuals">Residuals</a>
                <a href="#gauss-markov">Gauss-Markov</a>
            </div>
        </nav>
        
        <main>
            <!-- Introduction -->
            <section id="introduction" class="section">
                <h2>1. Introduction to Linear Model Theory</h2>
                
                <div class="highlight-box">
                    <h3>🎯 Learning Objectives</h3>
                    <p>This section shows how the parameters β of the linear model can be estimated by least squares, deriving their distributional properties and establishing the optimality of least squares estimators.</p>
                </div>
                
                <h3>1.1 The Linear Model</h3>
                
                <div class="formula-box">
                    <p><strong>Linear Model Specification:</strong></p>
                    $$\boldsymbol{\mu} = \mathbf{X}\boldsymbol{\beta}, \quad \mathbf{y} \sim N(\boldsymbol{\mu}, \mathbf{I}_n\sigma^2)$$
                    <p>where <strong>X</strong> is a full rank matrix with n rows and p columns</p>
                </div>
                
                <div class="theorem-box">
                    <h4>🔍 Key Results to be Established</h4>
                    <ul>
                        <li><strong>Unbiasedness:</strong> E(β̂) = β</li>
                        <li><strong>Optimality:</strong> β̂ has lowest variance among all linear unbiased estimators</li>
                        <li><strong>Distribution:</strong> β̂ ~ N(β, (X^T X)^(-1) σ²)</li>
                        <li><strong>Inference:</strong> Confidence intervals and hypothesis tests</li>
                    </ul>
                </div>
                
                <h3>1.2 Important Notation and Concepts</h3>
                
                <div class="notation-box">
                    <h4>📝 Vector Concepts</h4>
                    <p><strong>Important distinction:</strong> Do not confuse the <em>length</em> of a vector with its <em>dimension</em>.</p>
                    <ul>
                        <li><strong>Example:</strong> (1,1,1)^T has dimension 3 and length √3</li>
                        <li><strong>Euclidean length:</strong> ||v||² = v^T v = Σv_i²</li>
                        <li><strong>Full rank assumption:</strong> X has rank p (columns are linearly independent)</li>
                    </ul>
                </div>
                
                <div class="visualization">
                    <h4>Linear Model Components</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
                            </linearGradient>
                        </defs>
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Components of the Linear Model
                        </text>
                        
                        <!-- Data vector y -->
                        <rect x="50" y="80" width="150" height="200" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="125" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Response Vector y</text>
                        <text x="125" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">y₁</text>
                        <text x="125" y="150" text-anchor="middle" font-size="12" fill="#2c3e50">y₂</text>
                        <text x="125" y="170" text-anchor="middle" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="125" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">yₙ</text>
                        
                        <text x="125" y="220" text-anchor="middle" font-size="11" fill="#3498db">n × 1</text>
                        <text x="125" y="240" text-anchor="middle" font-size="11" fill="#3498db">Observed data</text>
                        <text x="125" y="255" text-anchor="middle" font-size="11" fill="#3498db">Random</text>
                        
                        <!-- Equals sign -->
                        <text x="230" y="180" font-size="24" fill="#2c3e50">=</text>
                        
                        <!-- Model matrix X -->
                        <rect x="270" y="80" width="150" height="200" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="345" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Model Matrix X</text>
                        <text x="315" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">x₁₁</text>
                        <text x="345" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">x₁₂</text>
                        <text x="375" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">⋯</text>
                        <text x="315" y="150" text-anchor="middle" font-size="10" fill="#2c3e50">x₂₁</text>
                        <text x="345" y="150" text-anchor="middle" font-size="10" fill="#2c3e50">x₂₂</text>
                        <text x="375" y="150" text-anchor="middle" font-size="10" fill="#2c3e50">⋯</text>
                        <text x="345" y="170" text-anchor="middle" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="315" y="190" text-anchor="middle" font-size="10" fill="#2c3e50">xₙ₁</text>
                        <text x="345" y="190" text-anchor="middle" font-size="10" fill="#2c3e50">xₙ₂</text>
                        <text x="375" y="190" text-anchor="middle" font-size="10" fill="#2c3e50">⋯</text>
                        
                        <text x="345" y="220" text-anchor="middle" font-size="11" fill="#e74c3c">n × p</text>
                        <text x="345" y="240" text-anchor="middle" font-size="11" fill="#e74c3c">Fixed design</text>
                        <text x="345" y="255" text-anchor="middle" font-size="11" fill="#e74c3c">Full rank</text>
                        
                        <!-- Parameter vector β -->
                        <rect x="470" y="80" width="100" height="200" fill="#27ae60" opacity="0.3" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="520" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Parameters β</text>
                        <text x="520" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">β₁</text>
                        <text x="520" y="150" text-anchor="middle" font-size="12" fill="#2c3e50">β₂</text>
                        <text x="520" y="170" text-anchor="middle" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="520" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">βₚ</text>
                        
                        <text x="520" y="220" text-anchor="middle" font-size="11" fill="#27ae60">p × 1</text>
                        <text x="520" y="240" text-anchor="middle" font-size="11" fill="#27ae60">Unknown</text>
                        <text x="520" y="255" text-anchor="middle" font-size="11" fill="#27ae60">To estimate</text>
                        
                        <!-- Plus sign -->
                        <text x="600" y="180" font-size="24" fill="#2c3e50">+</text>
                        
                        <!-- Error vector -->
                        <rect x="630" y="80" width="120" height="200" fill="#f39c12" opacity="0.3" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="690" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Error ε</text>
                        <text x="690" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">ε₁</text>
                        <text x="690" y="150" text-anchor="middle" font-size="12" fill="#2c3e50">ε₂</text>
                        <text x="690" y="170" text-anchor="middle" font-size="12" fill="#2c3e50">⋮</text>
                        <text x="690" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">εₙ</text>
                        
                        <text x="690" y="220" text-anchor="middle" font-size="11" fill="#f39c12">n × 1</text>
                        <text x="690" y="240" text-anchor="middle" font-size="11" fill="#f39c12">ε ~ N(0, σ²I)</text>
                        <text x="690" y="255" text-anchor="middle" font-size="11" fill="#f39c12">Random</text>
                        
                        <!-- Key assumptions -->
                        <rect x="50" y="320" width="700" height="60" fill="white" stroke="#dee2e6" stroke-width="2" rx="5"/>
                        <text x="400" y="340" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Key Assumptions</text>
                        <text x="60" y="360" font-size="12" fill="#2c3e50">
                            • Linearity: E(y) = Xβ  • Independence: Cov(εᵢ, εⱼ) = 0 for i ≠ j  • Normality: ε ~ N(0, σ²I)  • Full rank: rank(X) = p
                        </text>
                    </svg>
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Important Note on Notation</h4>
                    <p>No distinction has been made notationally between <strong>random variables</strong> and particular <strong>observations</strong> of those random variables. It is usually clear from the context which is meant.</p>
                </div>
            </section>
            
            <!-- Least Squares Estimation -->
            <section id="least-squares" class="section">
                <h2>2. Least Squares Estimation</h2>
                
                <h3>2.1 The Least Squares Criterion</h3>
                
                <div class="definition-box">
                    <h4>📐 Least Squares Principle</h4>
                    <p>Point estimates of the linear model parameters β can be obtained by the method of <strong>least squares</strong>, that is by minimizing:</p>
                    $$S = \sum_{i=1}^{n} (y_i - \mu_i)^2$$
                    <p>with respect to β.</p>
                </div>
                
                <h3>2.2 Vector Formulation</h3>
                
                <div class="derivation-box">
                    <h4>🔧 From Sum of Squares to Vector Form</h4>
                    
                    <p><strong>Step 1:</strong> Recall the link between Euclidean length and sum of squares.</p>
                    <p>If v is any vector of dimension n, then:</p>
                    $$||v||^2 \equiv v^T v \equiv \sum_{i=1}^{n} v_i^2$$
                    
                    <p><strong>Step 2:</strong> Express the sum of squares in vector form.</p>
                    $$S = \sum_{i=1}^{n} (y_i - \mu_i)^2 = ||y - \mu||^2 = ||y - X\beta||^2$$
                    
                    <p><strong>Key insight:</strong> S is simply the squared Euclidean length of the vector y - Xβ!</p>
                </div>
                
                <h3>2.3 QR Decomposition Approach</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Geometric Insight</h4>
                    <p>Since S = ||y - Xβ||² is the squared Euclidean length of the vector y - Xβ, its value will be <strong>unchanged if y - Xβ is rotated</strong>. This observation is the basis for a practical method for finding β̂.</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>QR Decomposition:</strong></p>
                    $$\mathbf{X} = \mathbf{Q} \begin{bmatrix} \mathbf{R} \\ \mathbf{0} \end{bmatrix} = \mathbf{Q}_f \mathbf{R}$$
                    <p>where <strong>R</strong> is p×p upper triangular, <strong>Q</strong> is n×n orthogonal, and <strong>Q_f</strong> comprises the first p columns of Q</p>
                </div>
                
                <div class="visualization">
                    <h4>QR Decomposition and Least Squares Solution</h4>
                    <svg width="900" height="600" viewBox="0 0 900 600">
                        <!-- Background -->
                        <rect width="900" height="600" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            QR Decomposition Solution Process
                        </text>
                        
                        <!-- Step 1: Original problem -->
                        <text x="150" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Step 1: Original Problem</text>
                        <rect x="50" y="80" width="200" height="100" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="150" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">Minimize ||y - Xβ||²</text>
                        <text x="150" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">X: n×p matrix</text>
                        <text x="150" y="145" text-anchor="middle" font-size="12" fill="#2c3e50">β: p×1 parameters</text>
                        <text x="150" y="165" text-anchor="middle" font-size="12" fill="#2c3e50">y: n×1 response</text>
                        
                        <!-- Arrow 1 -->
                        <path d="M 260 130 L 310 130" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="285" y="120" text-anchor="middle" font-size="11" fill="#e74c3c">QR decompose</text>
                        
                        <!-- Step 2: QR decomposition -->
                        <text x="450" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Step 2: QR Decomposition</text>
                        <rect x="350" y="80" width="200" height="100" fill="#e74c3c" opacity="0.2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="450" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">X = Q[R]</text>
                        <text x="470" y="120" text-anchor="middle" font-size="12" fill="#2c3e50">[0]</text>
                        <text x="450" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">Q: n×n orthogonal</text>
                        <text x="450" y="160" text-anchor="middle" font-size="12" fill="#2c3e50">R: p×p upper triangular</text>
                        
                        <!-- Arrow 2 -->
                        <path d="M 560 130 L 610 130" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="585" y="120" text-anchor="middle" font-size="11" fill="#27ae60">Rotate</text>
                        
                        <!-- Step 3: Rotated problem -->
                        <text x="750" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Step 3: Rotated Problem</text>
                        <rect x="650" y="80" width="200" height="100" fill="#27ae60" opacity="0.2" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="750" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">||Q^T y - [R]β||²</text>
                        <text x="770" y="120" text-anchor="middle" font-size="12" fill="#2c3e50">[0]</text>
                        <text x="750" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">Q^T y = [f]</text>
                        <text x="770" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">[r]</text>
                        
                        <!-- Mathematical breakdown -->
                        <rect x="50" y="220" width="800" height="320" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="450" y="245" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Mathematical Derivation
                        </text>
                        
                        <text x="70" y="275" font-size="14" fill="#2c3e50" font-weight="bold">Key Transformation:</text>
                        <text x="70" y="295" font-size="13" fill="#636e72">
                            ||y - Xβ||² = ||Q^T y - Q^T Xβ||² = ||Q^T y - [R/0]β||²
                        </text>
                        
                        <text x="70" y="325" font-size="14" fill="#2c3e50" font-weight="bold">Partition Q^T y:</text>
                        <text x="70" y="345" font-size="13" fill="#636e72">
                            Q^T y = [f/r], where f is p×1 and r is (n-p)×1
                        </text>
                        
                        <text x="70" y="375" font-size="14" fill="#2c3e50" font-weight="bold">Decompose the norm:</text>
                        <text x="70" y="395" font-size="13" fill="#636e72">
                            ||[f/r] - [Rβ/0]||² = ||f - Rβ||² + ||r||²
                        </text>
                        
                        <text x="70" y="425" font-size="14" fill="#2c3e50" font-weight="bold">Key Insight:</text>
                        <text x="70" y="445" font-size="13" fill="#636e72">
                            • ||r||² does not depend on β
                        </text>
                        <text x="70" y="465" font-size="13" fill="#636e72">
                            • ||f - Rβ||² can be reduced to zero by choosing Rβ = f
                        </text>
                        
                        <text x="70" y="495" font-size="14" fill="#2c3e50" font-weight="bold">Solution:</text>
                        <text x="70" y="515" font-size="13" fill="#636e72" font-weight="bold">
                            β̂ = R⁻¹f
                        </text>
                        
                        <!-- Result box -->
                        <rect x="500" y="480" width="320" height="50" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="660" y="500" text-anchor="middle" font-size="14" fill="#155724" font-weight="bold">
                            Residual Sum of Squares
                        </text>
                        <text x="660" y="520" text-anchor="middle" font-size="12" fill="#155724">
                            RSS = ||r||² = ||y - Xβ̂||²
                        </text>
                        
                        <!-- Arrows -->
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
                            </marker>
                        </defs>
                    </svg>
                </div>
                
                <div class="result-box">
                    <h4>✅ Least Squares Estimator</h4>
                    <p>The least squares estimator is:</p>
                    $$\hat{\boldsymbol{\beta}} = \mathbf{R}^{-1}\mathbf{f}$$
                    <p>where f is the first p elements of Q^T y, and the residual sum of squares is ||r||² = ||y - Xβ̂||².</p>
                </div>
            </section>
            
            <!-- Distribution Theory -->
            <section id="distribution" class="section">
                <h2>3. Distribution Theory of β̂</h2>
                
                <h3>3.1 Distribution of Q^T y</h3>
                
                <div class="derivation-box">
                    <h4>🎲 From y to Q^T y</h4>
                    
                    <p><strong>Given:</strong> y ~ N(Xβ, I_n σ²)</p>
                    
                    <p><strong>Step 1:</strong> Multivariate normality is preserved under linear transformations</p>
                    <p>Since Q^T is a linear transformation and y is multivariate normal, Q^T y is also multivariate normal.</p>
                    
                    <p><strong>Step 2:</strong> Mean of Q^T y</p>
                    $$E(Q^T y) = Q^T E(y) = Q^T X\beta = Q^T \mathbf{Q}_f \mathbf{R} \beta = \begin{bmatrix} \mathbf{R} \\ \mathbf{0} \end{bmatrix} \beta$$
                    
                    <p><strong>Step 3:</strong> Covariance matrix of Q^T y</p>
                    $$\text{Var}(Q^T y) = Q^T \text{Var}(y) Q = Q^T (I_n \sigma^2) Q = I_n \sigma^2$$
                    
                    <p>This uses the fact that Q is orthogonal: Q^T Q = I_n</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Partitioned Results:</strong></p>
                    $$Q^T y = \begin{bmatrix} \mathbf{f} \\ \mathbf{r} \end{bmatrix}, \quad E \begin{bmatrix} \mathbf{f} \\ \mathbf{r} \end{bmatrix} = \begin{bmatrix} \mathbf{R}\beta \\ \mathbf{0} \end{bmatrix}$$
                    
                    <p>Therefore:</p>
                    $$\mathbf{f} \sim N(\mathbf{R}\beta, I_p \sigma^2) \quad \text{and} \quad \mathbf{r} \sim N(\mathbf{0}, I_{n-p} \sigma^2)$$
                    
                    <p>with f and r <strong>independent</strong> of each other</p>
                </div>
                
                <h3>3.2 Properties of β̂</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Unbiasedness</h4>
                    $$E(\hat{\boldsymbol{\beta}}) = E(\mathbf{R}^{-1}\mathbf{f}) = \mathbf{R}^{-1}E(\mathbf{f}) = \mathbf{R}^{-1}\mathbf{R}\boldsymbol{\beta} = \boldsymbol{\beta}$$
                    <p>Therefore, β̂ is an <strong>unbiased estimator</strong> of β.</p>
                </div>
                
                <div class="theorem-box">
                    <h4>🎯 Covariance Matrix</h4>
                    <p>Since Var(f) = I_p σ², and β̂ = R⁻¹f:</p>
                    $$\text{Var}(\hat{\boldsymbol{\beta}}) = \mathbf{R}^{-1} I_p \mathbf{R}^{-T} \sigma^2 = \mathbf{R}^{-1}\mathbf{R}^{-T}\sigma^2$$
                    
                    <p>Equivalently (in terms of X):</p>
                    $$\text{Var}(\hat{\boldsymbol{\beta}}) = (\mathbf{X}^T\mathbf{X})^{-1}\sigma^2$$
                </div>
                
                <div class="result-box">
                    <h4>✅ Complete Distribution</h4>
                    <p>Since β̂ is a linear transformation of the normal random vector f:</p>
                    $$\hat{\boldsymbol{\beta}} \sim N\left(\boldsymbol{\beta}, \mathbf{R}^{-1}\mathbf{R}^{-T}\sigma^2\right)$$
                </div>
                
                <div class="visualization">
                    <h4>Distribution Properties Visualization</h4>
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Distribution Flow: y → Q^T y → β̂
                        </text>
                        
                        <!-- Original distribution -->
                        <rect x="50" y="60" width="200" height="120" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="150" y="85" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">y ~ N(Xβ, I_n σ²)</text>
                        <text x="150" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">Original data</text>
                        <text x="150" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">Dimension: n</text>
                        <text x="150" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">Mean: Xβ</text>
                        <text x="150" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">Variance: I_n σ²</text>
                        
                        <!-- Transformation arrow 1 -->
                        <path d="M 260 120 L 290 120" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="275" y="105" text-anchor="middle" font-size="11" fill="#e74c3c">Q^T</text>
                        <text x="275" y="140" text-anchor="middle" font-size="11" fill="#e74c3c">Orthogonal</text>
                        <text x="275" y="155" text-anchor="middle" font-size="11" fill="#e74c3c">rotation</text>
                        
                        <!-- Rotated distribution -->
                        <rect x="300" y="60" width="200" height="120" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="400" y="85" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Q^T y ~ N(Q^T Xβ, I_n σ²)</text>
                        <text x="400" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">Rotated data</text>
                        <text x="400" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">Dimension: n</text>
                        <text x="400" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">Mean: [Rβ; 0]</text>
                        <text x="400" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">Variance: I_n σ²</text>
                        
                        <!-- Transformation arrow 2 -->
                        <path d="M 510 120 L 540 120" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="525" y="105" text-anchor="middle" font-size="11" fill="#27ae60">Extract</text>
                        <text x="525" y="140" text-anchor="middle" font-size="11" fill="#27ae60">first p</text>
                        <text x="525" y="155" text-anchor="middle" font-size="11" fill="#27ae60">elements</text>
                        
                        <!-- Parameter distribution -->
                        <rect x="550" y="60" width="200" height="120" fill="#27ae60" opacity="0.3" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="650" y="85" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">β̂ ~ N(β, R⁻¹R⁻ᵀσ²)</text>
                        <text x="650" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">Parameter estimates</text>
                        <text x="650" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">Dimension: p</text>
                        <text x="650" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">Mean: β (unbiased)</text>
                        <text x="650" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">Variance: (X^T X)⁻¹σ²</text>
                        
                        <!-- Detailed breakdown -->
                        <rect x="50" y="220" width="700" height="250" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="400" y="245" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Key Properties Summary
                        </text>
                        
                        <!-- Partition diagram -->
                        <text x="70" y="275" font-size="14" fill="#2c3e50" font-weight="bold">Partition of Q^T y:</text>
                        
                        <rect x="100" y="290" width="80" height="40" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="140" y="305" text-anchor="middle" font-size="12" fill="#2c3e50">f</text>
                        <text x="140" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">p × 1</text>
                        
                        <rect x="100" y="340" width="80" height="60" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="140" y="365" text-anchor="middle" font-size="12" fill="#2c3e50">r</text>
                        <text x="140" y="380" text-anchor="middle" font-size="12" fill="#2c3e50">(n-p) × 1</text>
                        
                        <text x="200" y="310" font-size="12" fill="#2c3e50">f ~ N(Rβ, I_p σ²)</text>
                        <text x="200" y="330" font-size="12" fill="#2c3e50">Used for β̂ = R⁻¹f</text>
                        <text x="200" y="370" font-size="12" fill="#2c3e50">r ~ N(0, I_{n-p} σ²)</text>
                        <text x="200" y="390" font-size="12" fill="#2c3e50">Used for σ̂² = ||r||²/(n-p)</text>
                        
                        <!-- Independence -->
                        <rect x="450" y="290" width="250" height="110" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="5"/>
                        <text x="575" y="310" text-anchor="middle" font-size="13" fill="#2c3e50" font-weight="bold">Independence Property</text>
                        <text x="575" y="330" text-anchor="middle" font-size="12" fill="#2c3e50">f and r are independent</text>
                        <text x="575" y="350" text-anchor="middle" font-size="12" fill="#2c3e50">⇓</text>
                        <text x="575" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">β̂ and σ̂² are independent</text>
                        <text x="575" y="390" text-anchor="middle" font-size="12" fill="#2c3e50">Crucial for t-tests!</text>
                        
                        <!-- Key insight -->
                        <rect x="100" y="420" width="600" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="400" y="435" text-anchor="middle" font-size="13" fill="#856404" font-weight="bold">
                            🔑 Key Insight: Orthogonal transformations preserve normality and simplify covariance structure
                        </text>
                        <text x="400" y="450" text-anchor="middle" font-size="12" fill="#856404">
                            This enables exact distributional results for finite samples (not just asymptotic)
                        </text>
                    </svg>
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Practical Note</h4>
                    <p>The distributional results are not usually directly useful for making inferences about β, since σ² is generally <strong>unknown</strong> and must be estimated, thereby introducing an extra component of variability that should be accounted for.</p>
                </div>
            </section>
            
            <!-- t-distribution Results -->
            <section id="t-tests" class="section">
                <h2>4. t-Distribution Results for Individual Parameters</h2>
                
                <h3>4.1 Estimating σ²</h3>
                
                <div class="derivation-box">
                    <h4>📊 Unbiased Estimator of σ²</h4>
                    
                    <p><strong>Step 1:</strong> Distribution of the residual vector</p>
                    <p>Since the elements of r are i.i.d. N(0, σ²), we have r_i/σ ~ N(0,1) independently.</p>
                    
                    <p><strong>Step 2:</strong> Chi-squared distribution</p>
                    $$\frac{1}{\sigma^2} ||r||^2 = \frac{1}{\sigma^2} \sum_{i=1}^{n-p} r_i^2 \sim \chi^2_{n-p}$$
                    
                    <p><strong>Step 3:</strong> Unbiased estimator</p>
                    <p>Since E(χ²_{n-p}) = n-p, we have:</p>
                    $$\hat{\sigma}^2 = \frac{||r||^2}{n-p}$$
                    <p>is an unbiased estimator of σ².</p>
                </div>
                
                <div class="result-box">
                    <h4>✅ Independence Result</h4>
                    <p>The independence of the elements of r and f implies that <strong>β̂ and σ̂² are independent</strong>. This is crucial for constructing t-tests.</p>
                </div>
                
                <h3>4.2 t-Distribution for Individual Parameters</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Main t-Distribution Result</h4>
                    <p>For any individual parameter β_i:</p>
                    $$\frac{\hat{\beta}_i - \beta_i}{\hat{\sigma}_{\hat{\beta}_i}} \sim t_{n-p}$$
                    
                    <p>where σ̂_{β̂_i} is the estimated standard error of β̂_i</p>
                </div>
                
                <div class="derivation-box">
                    <h4>🔧 Derivation of t-Statistic</h4>
                    
                    <p><strong>Step 1:</strong> Standard error components</p>
                    <ul>
                        <li>σ_{β̂_i} = square root of element (i,i) of Var(β̂) = R⁻¹R⁻ᵀσ²</li>
                        <li>σ̂_{β̂_i} = square root of element (i,i) of R⁻¹R⁻ᵀσ̂²</li>
                    </ul>
                    
                    <p><strong>Step 2:</strong> Relationship</p>
                    $$\hat{\sigma}_{\hat{\beta}_i} = \sigma_{\hat{\beta}_i} \frac{\hat{\sigma}}{\sigma}$$
                    
                    <p><strong>Step 3:</strong> t-statistic construction</p>
                    $$\frac{\hat{\beta}_i - \beta_i}{\hat{\sigma}_{\hat{\beta}_i}} = \frac{(\hat{\beta}_i - \beta_i)/\sigma_{\hat{\beta}_i}}{\hat{\sigma}/\sigma} = \frac{N(0,1)}{\sqrt{\chi^2_{n-p}/(n-p)}} \sim t_{n-p}$$
                    
                    <p>This uses the independence of β̂_i and σ̂².</p>
                </div>
                
                <div class="visualization">
                    <h4>t-Distribution Construction</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Construction of t-Statistic for β̂_i
                        </text>
                        
                        <!-- Numerator -->
                        <rect x="50" y="60" width="250" height="100" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="175" y="85" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Numerator</text>
                        <text x="175" y="110" text-anchor="middle" font-size="13" fill="#2c3e50">(β̂_i - β_i)/σ_{β̂_i}</text>
                        <text x="175" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">~ N(0,1)</text>
                        <text x="175" y="145" text-anchor="middle" font-size="12" fill="#2c3e50">Standardized parameter</text>
                        
                        <!-- Division symbol -->
                        <text x="350" y="110" font-size="24" fill="#2c3e50">÷</text>
                        
                        <!-- Denominator -->
                        <rect x="400" y="60" width="250" height="100" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="525" y="85" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Denominator</text>
                        <text x="525" y="110" text-anchor="middle" font-size="13" fill="#2c3e50">σ̂/σ = √(χ²_{n-p}/(n-p))</text>
                        <text x="525" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">√(Chi-squared/df)</text>
                        <text x="525" y="145" text-anchor="middle" font-size="12" fill="#2c3e50">Scale factor</text>
                        
                        <!-- Arrow down -->
                        <path d="M 400 180 L 400 200" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="400" y="175" text-anchor="middle" font-size="12" fill="#27ae60">Independent components</text>
                        
                        <!-- Result -->
                        <rect x="250" y="220" width="300" height="80" fill="#27ae60" opacity="0.3" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="400" y="245" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">t-Statistic</text>
                        <text x="400" y="270" text-anchor="middle" font-size="13" fill="#2c3e50">(β̂_i - β_i)/σ̂_{β̂_i} ~ t_{n-p}</text>
                        <text x="400" y="290" text-anchor="middle" font-size="12" fill="#2c3e50">t-distribution with n-p degrees of freedom</text>
                        
                        <!-- Applications -->
                        <rect x="50" y="320" width="700" height="60" fill="white" stroke="#dee2e6" stroke-width="2" rx="5"/>
                        <text x="400" y="340" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Applications</text>
                        <text x="120" y="360" font-size="12" fill="#2c3e50">• Confidence intervals: β̂_i ± t_{α/2,n-p} × σ̂_{β̂_i}</text>
                        <text x="450" y="360" font-size="12" fill="#2c3e50">• Hypothesis tests: H₀: β_i = 0</text>
                    </svg>
                </div>
                
                <div class="example-box">
                    <h4>💡 Practical Application</h4>
                    <p><strong>Confidence Interval for β_i:</strong></p>
                    $$\hat{\beta}_i \pm t_{\alpha/2, n-p} \times \hat{\sigma}_{\hat{\beta}_i}$$
                    
                    <p><strong>Hypothesis Test H₀: β_i = 0:</strong></p>
                    <p>Test statistic: t = β̂_i / σ̂_{β̂_i}</p>
                    <p>Reject H₀ if |t| > t_{α/2, n-p}</p>
                </div>
            </section>
            
            <!-- F-ratio Results I -->
            <section id="f-tests" class="section">
                <h2>5. F-Ratio Tests for Linear Hypotheses</h2>
                
                <h3>5.1 General Linear Hypothesis Testing</h3>
                
                <div class="definition-box">
                    <h4>📐 General Linear Hypothesis</h4>
                    <p>We often want to test hypotheses of the form:</p>
                    $$H_0: \mathbf{C}\boldsymbol{\beta} = \mathbf{d}$$
                    <p>where <strong>C</strong> is q×p matrix of rank q < p, and <strong>d</strong> is a q×1 vector.</p>
                    
                    <p><strong>Examples:</strong></p>
                    <ul>
                        <li>H₀: β₁ = β₂ = 0 (multiple parameters simultaneously zero)</li>
                        <li>H₀: β₁ = β₂ (equality constraints)</li>
                        <li>H₀: β₁ + β₂ = 1 (linear combinations)</li>
                    </ul>
                </div>
                
                <h3>5.2 Distribution Under H₀</h3>
                
                <div class="derivation-box">
                    <h4>🔧 Derivation of F-Test</h4>
                    
                    <p><strong>Step 1:</strong> Distribution of Cβ̂ - d under H₀</p>
                    <p>Under H₀, we have Cβ = d, so:</p>
                    $$\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d} \sim N(\mathbf{0}, \mathbf{C}\text{Var}(\hat{\boldsymbol{\beta}})\mathbf{C}^T)$$
                    $$= N(\mathbf{0}, \mathbf{C}\mathbf{R}^{-1}\mathbf{R}^{-T}\mathbf{C}^T\sigma^2)$$
                    
                    <p><strong>Step 2:</strong> Cholesky decomposition</p>
                    <p>Let L^T L = CVâr(β̂)C^T, then:</p>
                    $$\mathbf{L}^{-T}(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d}) \sim N(\mathbf{0}, \mathbf{I})$$
                    
                    <p><strong>Step 3:</strong> Chi-squared statistic</p>
                    $$(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d})^T(\mathbf{C}\text{Var}(\hat{\boldsymbol{\beta}})\mathbf{C}^T)^{-1}(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d}) \sim \chi^2_q$$
                    
                    <p><strong>Step 4:</strong> F-statistic</p>
                    <p>Replacing Var(β̂) with its estimator and using independence:</p>
                    $$F = \frac{1}{q} \frac{(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d})^T(\mathbf{C}\hat{\mathbf{V}}_{\hat{\boldsymbol{\beta}}}\mathbf{C}^T)^{-1}(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d})}{\hat{\sigma}^2} \sim F_{q,n-p}$$
                </div>
                
                <div class="formula-box">
                    <p><strong>F-Test Statistic:</strong></p>
                    $$F = \frac{(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d})^T(\mathbf{C}\hat{\mathbf{V}}_{\hat{\boldsymbol{\beta}}}\mathbf{C}^T)^{-1}(\mathbf{C}\hat{\boldsymbol{\beta}} - \mathbf{d})/q}{\hat{\sigma}^2} \sim F_{q,n-p}$$
                    
                    <p>where $\hat{\mathbf{V}}_{\hat{\boldsymbol{\beta}}} = \mathbf{R}^{-1}\mathbf{R}^{-T}\hat{\sigma}^2$</p>
                </div>
                
                <h3>5.3 Sequential ANOVA Approach</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Model Comparison Framework</h4>
                    <p>An alternative approach considers testing by comparing nested models:</p>
                    
                    <p><strong>Full model:</strong> X = [X₀ : X₁], where X₀ has p-q columns, X₁ has q columns</p>
                    <p><strong>Reduced model:</strong> Only X₀ (setting β₁ = 0)</p>
                    
                    <p><strong>Test:</strong> H₀: β₁ = 0 vs H₁: β₁ ≠ 0</p>
                </div>
                
                <div class="derivation-box">
                    <h4>🔧 Sequential ANOVA Derivation</h4>
                    
                    <p><strong>Step 1:</strong> Partition the QR decomposition</p>
                    $$Q^T X_0 = \begin{bmatrix} \mathbf{R}_0 \\ \mathbf{0} \end{bmatrix}$$
                    where R₀ is the first (p-q) rows and columns of R.
                    
                    <p><strong>Step 2:</strong> Decompose residual sum of squares</p>
                    $$||y - X_0\beta_0||^2 = ||Q^T y - Q^T X_0\beta_0||^2 = ||\mathbf{f}_0 - \mathbf{R}_0\beta_0||^2 + ||\mathbf{f}_1||^2 + ||r||^2$$
                    
                    <p>where f has been partitioned as f = [f₀^T, f₁^T]^T with f₁ having dimension q.</p>
                    
                    <p><strong>Step 3:</strong> Identify the extra sum of squares</p>
                    <p>||f₁||² is the <strong>increase in residual sum of squares</strong> from dropping X₁ from the model.</p>
                    
                    <p><strong>Step 4:</strong> Distribution under H₀</p>
                    <p>If H₀ is true (β₁ = 0), then E(f₁) = 0, and since f₁ ~ N(0, I_q σ²):</p>
                    $$\frac{1}{\sigma^2} ||f_1||^2 \sim \chi^2_q$$
                </div>
                
                <div class="result-box">
                    <h4>✅ Sequential ANOVA F-Test</h4>
                    $$F = \frac{||f_1||^2/q}{\hat{\sigma}^2} = \frac{(RSS_0 - RSS_1)/q}{RSS_1/(n-p)} \sim F_{q,n-p}$$
                    
                    <p>where:</p>
                    <ul>
                        <li>RSS₀ = residual sum of squares for reduced model</li>
                        <li>RSS₁ = residual sum of squares for full model</li>
                        <li>q = difference in number of parameters</li>
                    </ul>
                </div>
                
                <div class="visualization">
                    <h4>F-Test Geometric Interpretation</h4>
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Geometric View of F-Tests: Model Comparison
                        </text>
                        
                        <!-- Vector space representation -->
                        <ellipse cx="200" cy="200" rx="150" ry="80" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2"/>
                        <text x="200" y="240" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Space of X₀</text>
                        <text x="200" y="255" text-anchor="middle" font-size="11" fill="#2c3e50">(Reduced model)</text>
                        
                        <ellipse cx="350" cy="180" rx="200" ry="120" fill="#e74c3c" opacity="0.2" stroke="#e74c3c" stroke-width="2"/>
                        <text x="450" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Space of [X₀:X₁]</text>
                        <text x="450" y="235" text-anchor="middle" font-size="11" fill="#2c3e50">(Full model)</text>
                        
                        <!-- Data point -->
                        <circle cx="600" cy="150" r="5" fill="#27ae60"/>
                        <text x="615" y="145" font-size="12" fill="#27ae60" font-weight="bold">y</text>
                        <text x="615" y="160" font-size="11" fill="#27ae60">Observed data</text>
                        
                        <!-- Projections -->
                        <circle cx="250" cy="180" r="4" fill="#3498db"/>
                        <text x="265" y="175" font-size="11" fill="#3498db">μ̂₀</text>
                        <text x="265" y="190" font-size="10" fill="#3498db">Reduced fit</text>
                        
                        <circle cx="420" cy="160" r="4" fill="#e74c3c"/>
                        <text x="435" y="155" font-size="11" fill="#e74c3c">μ̂₁</text>
                        <text x="435" y="170" font-size="10" fill="#e74c3c">Full fit</text>
                        
                        <!-- Projection lines -->
                        <line x1="600" y1="150" x2="250" y2="180" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="600" y1="150" x2="420" y2="160" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <line x1="250" y1="180" x2="420" y2="160" stroke="#f39c12" stroke-width="3"/>
                        
                        <!-- Labels for distances -->
                        <text x="500" y="120" font-size="12" fill="#e74c3c">||y - μ̂₁||² = RSS₁</text>
                        <text x="350" y="100" font-size="12" fill="#3498db">||y - μ̂₀||² = RSS₀</text>
                        <text x="320" y="140" font-size="12" fill="#f39c12" font-weight="bold">||μ̂₁ - μ̂₀||² = RSS₀ - RSS₁</text>
                        
                        <!-- Key insight box -->
                        <rect x="50" y="320" width="700" height="160" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="400" y="345" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            F-Test Decomposition
                        </text>
                        
                        <text x="70" y="375" font-size="14" fill="#2c3e50" font-weight="bold">Key relationship:</text>
                        <text x="70" y="395" font-size="13" fill="#636e72">RSS₀ - RSS₁ = ||μ̂₁ - μ̂₀||² (improvement from adding X₁)</text>
                        
                        <text x="70" y="420" font-size="14" fill="#2c3e50" font-weight="bold">F-statistic measures:</text>
                        <text x="70" y="440" font-size="13" fill="#636e72">
                            F = (Improvement per parameter) / (Baseline variability)
                        </text>
                        <text x="70" y="455" font-size="13" fill="#636e72">
                            F = [(RSS₀ - RSS₁)/q] / [RSS₁/(n-p)]
                        </text>
                    </svg>
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Sequential ANOVA Caution</h4>
                    <p>The hypothesis test about β_j is only valid in general if β_k = 0 for all k such that j < k ≤ m. This is why ANOVA tables are called <strong>"sequential"</strong> - the p-values depend on the order of model terms. The exception is when the β̂_j's are mutually independent (usually only in balanced experimental designs).</p>
                </div>
                         </section>
            
            <!-- Influence Matrix -->
            <section id="influence" class="section">
                <h2>6. The Influence Matrix (Hat Matrix)</h2>
                
                <h3>6.1 Definition and Construction</h3>
                
                <div class="definition-box">
                    <h4>📐 Hat Matrix Definition</h4>
                    <p>The <strong>influence matrix</strong> (or hat matrix) is the matrix which yields the fitted value vector μ̂ when post-multiplied by the data vector y.</p>
                    
                    <p><strong>Construction from QR decomposition:</strong></p>
                    <p>Since β̂ = R⁻¹f and f = Q_f^T y, we have β̂ = R⁻¹Q_f^T y</p>
                    <p>Also, μ̂ = Xβ̂ and X = Q_f R, so:</p>
                    $$\hat{\boldsymbol{\mu}} = \mathbf{X}\hat{\boldsymbol{\beta}} = \mathbf{Q}_f \mathbf{R} \mathbf{R}^{-1} \mathbf{Q}_f^T \mathbf{y} = \mathbf{Q}_f \mathbf{Q}_f^T \mathbf{y}$$
                </div>
                
                <div class="formula-box">
                    <p><strong>Hat Matrix:</strong></p>
                    $$\mathbf{A} \equiv \mathbf{Q}_f \mathbf{Q}_f^T = \mathbf{X}(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T$$
                    <p>such that $\hat{\boldsymbol{\mu}} = \mathbf{A}\mathbf{y}$</p>
                </div>
                
                <h3>6.2 Properties of the Hat Matrix</h3>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>🔢 Trace Property</h4>
                        <p>The trace of the influence matrix equals the number of identifiable parameters:</p>
                        $$\text{tr}(\mathbf{A}) = \text{tr}(\mathbf{Q}_f \mathbf{Q}_f^T) = \text{tr}(\mathbf{Q}_f^T \mathbf{Q}_f) = \text{tr}(\mathbf{I}_p) = p$$
                    </div>
                    
                    <div class="property-card">
                        <h4>🔄 Idempotency</h4>
                        <p>The hat matrix is idempotent: A² = A</p>
                        $$\mathbf{A}\mathbf{A} = \mathbf{Q}_f \mathbf{Q}_f^T \mathbf{Q}_f \mathbf{Q}_f^T = \mathbf{Q}_f \mathbf{I}_p \mathbf{Q}_f^T = \mathbf{Q}_f \mathbf{Q}_f^T = \mathbf{A}$$
                    </div>
                    
                    <div class="property-card">
                        <h4>🪞 Symmetry</h4>
                        <p>The hat matrix is symmetric: A = A^T</p>
                        <p>This follows directly from the definition A = Q_f Q_f^T</p>
                    </div>
                    
                    <div class="property-card">
                        <h4>📊 Projection Matrix</h4>
                        <p>A is the orthogonal projection matrix onto the column space of X</p>
                        <p>μ̂ = Ay is the closest point in the column space of X to y</p>
                    </div>
                </div>
                
                <div class="visualization">
                    <h4>Geometric Interpretation of the Hat Matrix</h4>
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Hat Matrix as Orthogonal Projection
                        </text>
                        
                        <!-- Column space of X -->
                        <ellipse cx="350" cy="250" rx="200" ry="100" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="3"/>
                        <text x="350" y="300" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Column Space of X</text>
                        <text x="350" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">Span{x₁, x₂, ..., xₚ}</text>
                        
                        <!-- Data vector y -->
                        <circle cx="600" cy="150" r="5" fill="#e74c3c"/>
                        <text x="615" y="145" font-size="14" fill="#e74c3c" font-weight="bold">y</text>
                        <text x="615" y="165" font-size="12" fill="#e74c3c">Observed data</text>
                        
                        <!-- Fitted values -->
                        <circle cx="420" cy="220" r="5" fill="#27ae60"/>
                        <text x="435" y="215" font-size="14" fill="#27ae60" font-weight="bold">μ̂ = Ay</text>
                        <text x="435" y="235" font-size="12" fill="#27ae60">Fitted values</text>
                        
                        <!-- Projection line -->
                        <line x1="600" y1="150" x2="420" y2="220" stroke="#2c3e50" stroke-width="2"/>
                        <text x="510" y="175" font-size="12" fill="#2c3e50">Orthogonal projection</text>
                        
                        <!-- Residual vector -->
                        <line x1="420" y1="220" x2="600" y2="150" stroke="#f39c12" stroke-width="3"/>
                        <text x="480" y="200" font-size="12" fill="#f39c12" font-weight="bold">ε̂ = y - μ̂</text>
                        <text x="480" y="215" font-size="11" fill="#f39c12">Residual vector</text>
                        
                        <!-- Orthogonality indicator -->
                        <path d="M 410 215 L 415 210 L 420 215" stroke="#2c3e50" stroke-width="2" fill="none"/>
                        
                        <!-- Column vectors of X -->
                        <line x1="250" y1="250" x2="290" y2="200" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="255" y="195" font-size="11" fill="#3498db">x₁</text>
                        
                        <line x1="250" y1="250" x2="320" y2="220" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="290" y="210" font-size="11" fill="#3498db">x₂</text>
                        
                        <line x1="250" y1="250" x2="280" y2="280" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="250" y="295" font-size="11" fill="#3498db">xₚ</text>
                        
                        <!-- Properties box -->
                        <rect x="50" y="350" width="700" height="130" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="400" y="375" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Key Properties of Hat Matrix A
                        </text>
                        
                        <text x="70" y="400" font-size="13" fill="#2c3e50">
                            • <strong>Idempotent:</strong> A² = A (projection property)
                        </text>
                        <text x="70" y="420" font-size="13" fill="#2c3e50">
                            • <strong>Trace:</strong> tr(A) = p (degrees of freedom for signal)
                        </text>
                        <text x="70" y="440" font-size="13" fill="#2c3e50">
                            • <strong>Symmetric:</strong> A = A^T (self-adjoint operator)
                        </text>
                        <text x="70" y="460" font-size="13" fill="#2c3e50">
                            • <strong>Residual projection:</strong> I - A projects onto orthogonal complement
                        </text>
                    </svg>
                </div>
                
                <div class="insight-box">
                    <h4>🔑 Geometric Insight</h4>
                    <p>The hat matrix A performs orthogonal projection of the data vector y onto the column space of X. This is why:</p>
                    <ul>
                        <li>μ̂ = Ay lies in the column space of X</li>
                        <li>The residual ε̂ = (I - A)y is orthogonal to the column space</li>
                        <li>The name "hat" comes from putting a "hat" on y to get ŷ = μ̂</li>
                    </ul>
                </div>
            </section>
            
            <!-- Residuals and Fitted Values -->
            <section id="residuals" class="section">
                <h2>7. Residuals and Fitted Values</h2>
                
                <h3>7.1 Properties of Fitted Values</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Fitted Values Properties</h4>
                    
                    <p><strong>Unbiasedness:</strong></p>
                    $$E(\hat{\boldsymbol{\mu}}) = E(\mathbf{A}\mathbf{y}) = \mathbf{A}E(\mathbf{y}) = \mathbf{A}\mathbf{X}\boldsymbol{\beta} = \mathbf{X}\boldsymbol{\beta} = \boldsymbol{\mu}$$
                    
                    <p><strong>Covariance Matrix:</strong></p>
                    $$\text{Var}(\hat{\boldsymbol{\mu}}) = \mathbf{A}\text{Var}(\mathbf{y})\mathbf{A}^T = \mathbf{A}(\mathbf{I}_n\sigma^2)\mathbf{A}^T = \mathbf{A}\sigma^2$$
                    
                    <p>The last step uses the idempotence and symmetry of A.</p>
                </div>
                
                <h3>7.2 Properties of Residuals</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Residuals Properties</h4>
                    
                    <p><strong>Definition:</strong></p>
                    $$\hat{\boldsymbol{\varepsilon}} = (\mathbf{I} - \mathbf{A})\mathbf{y}$$
                    
                    <p><strong>Unbiasedness:</strong></p>
                    $$E(\hat{\boldsymbol{\varepsilon}}) = E(\mathbf{y}) - E(\hat{\boldsymbol{\mu}}) = \boldsymbol{\mu} - \boldsymbol{\mu} = \mathbf{0}$$
                    
                    <p><strong>Covariance Matrix:</strong></p>
                    $$\text{Var}(\hat{\boldsymbol{\varepsilon}}) = (\mathbf{I}_n - \mathbf{A})\mathbf{I}_n(\mathbf{I}_n - \mathbf{A})^T\sigma^2 = (\mathbf{I}_n - \mathbf{A})\sigma^2$$
                    
                    <p>This uses (I - A)² = I - 2A + A² = I - 2A + A = I - A (idempotence).</p>
                </div>
                
                <div class="visualization">
                    <h4>Residuals and Fitted Values Decomposition</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Decomposition: y = μ̂ + ε̂
                        </text>
                        
                        <!-- Data vector -->
                        <rect x="50" y="80" width="120" height="200" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="110" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">y</text>
                        <text x="110" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">Observed</text>
                        <text x="110" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">data</text>
                        <text x="110" y="170" text-anchor="middle" font-size="11" fill="#2c3e50">E(y) = Xβ</text>
                        <text x="110" y="185" text-anchor="middle" font-size="11" fill="#2c3e50">Var(y) = I_n σ²</text>
                        <text x="110" y="210" text-anchor="middle" font-size="11" fill="#2c3e50">Dimension: n</text>
                        <text x="110" y="240" text-anchor="middle" font-size="11" fill="#636e72">In n-space</text>
                        
                        <!-- Equals sign -->
                        <text x="200" y="180" font-size="24" fill="#2c3e50">=</text>
                        
                        <!-- Fitted values -->
                        <rect x="250" y="80" width="120" height="200" fill="#27ae60" opacity="0.3" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="310" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">μ̂ = Ay</text>
                        <text x="310" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">Fitted</text>
                        <text x="310" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">values</text>
                        <text x="310" y="170" text-anchor="middle" font-size="11" fill="#2c3e50">E(μ̂) = μ</text>
                        <text x="310" y="185" text-anchor="middle" font-size="11" fill="#2c3e50">Var(μ̂) = Aσ²</text>
                        <text x="310" y="210" text-anchor="middle" font-size="11" fill="#2c3e50">Rank: p</text>
                        <text x="310" y="240" text-anchor="middle" font-size="11" fill="#636e72">In col(X)</text>
                        
                        <!-- Plus sign -->
                        <text x="400" y="180" font-size="24" fill="#2c3e50">+</text>
                        
                        <!-- Residuals -->
                        <rect x="450" y="80" width="120" height="200" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="510" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">ε̂ = (I-A)y</text>
                        <text x="510" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">Residuals</text>
                        <text x="510" y="140" text-anchor="middle" font-size="12" fill="#2c3e50">(errors)</text>
                        <text x="510" y="170" text-anchor="middle" font-size="11" fill="#2c3e50">E(ε̂) = 0</text>
                        <text x="510" y="185" text-anchor="middle" font-size="11" fill="#2c3e50">Var(ε̂) = (I-A)σ²</text>
                        <text x="510" y="210" text-anchor="middle" font-size="11" fill="#2c3e50">Rank: n-p</text>
                        <text x="510" y="240" text-anchor="middle" font-size="11" fill="#636e72">⊥ col(X)</text>
                        
                        <!-- Key relationships -->
                        <rect x="600" y="80" width="150" height="200" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="5"/>
                        <text x="675" y="105" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Key Facts</text>
                        
                        <text x="610" y="130" font-size="11" fill="#2c3e50">• μ̂ and ε̂ orthogonal</text>
                        <text x="610" y="145" font-size="11" fill="#2c3e50">• ||y||² = ||μ̂||² + ||ε̂||²</text>
                        <text x="610" y="160" font-size="11" fill="#2c3e50">• μ̂ ∈ column space X</text>
                        <text x="610" y="175" font-size="11" fill="#2c3e50">• ε̂ ⊥ column space X</text>
                        <text x="610" y="195" font-size="11" fill="#2c3e50">• tr(A) = p</text>
                        <text x="610" y="210" font-size="11" fill="#2c3e50">• tr(I-A) = n-p</text>
                        <text x="610" y="230" font-size="11" fill="#2c3e50">• Both degenerate</text>
                        <text x="610" y="245" font-size="11" fill="#2c3e50">  multivariate normal</text>
                        
                        <!-- Orthogonality visualization -->
                        <rect x="50" y="310" width="700" height="70" fill="white" stroke="#dee2e6" stroke-width="2" rx="5"/>
                        <text x="400" y="330" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                            Orthogonal Decomposition in n-dimensional Space
                        </text>
                        <text x="70" y="350" font-size="12" fill="#636e72">
                            Fitted values μ̂ lie in p-dimensional subspace spanned by columns of X
                        </text>
                        <text x="70" y="365" font-size="12" fill="#636e72">
                            Residuals ε̂ lie in (n-p)-dimensional orthogonal complement to col(X)
                        </text>
                    </svg>
                </div>
                
                <h3>7.3 Model Checking Applications</h3>
                
                <div class="example-box">
                    <h4>💡 Standardized Residuals</h4>
                    <p>The residual variance structure allows standardization for model checking:</p>
                    
                    <p><strong>Raw residuals:</strong> ε̂_i = y_i - μ̂_i</p>
                    
                    <p><strong>Standardized residuals:</strong></p>
                    $$r_i = \frac{\hat{\varepsilon}_i}{\hat{\sigma}\sqrt{1 - A_{ii}}}$$
                    
                    <p>where A_ii is the i-th diagonal element of the hat matrix.</p>
                    
                    <p>Under correct model specification, the standardized residuals should have approximately constant variance.</p>
                </div>
                
                <div class="result-box">
                    <h4>✅ Distribution Summary</h4>
                    <p>Both fitted values and residuals follow degenerate multivariate normal distributions:</p>
                    <ul>
                        <li>μ̂ ~ degenerate N(μ, Aσ²) with rank p</li>
                        <li>ε̂ ~ degenerate N(0, (I-A)σ²) with rank n-p</li>
                        <li>μ̂ and ε̂ are NOT independent (both depend on y)</li>
                    </ul>
                </div>
            </section>
            
            <!-- Classical Results -->
            <section id="classical" class="section">
                <h2>8. Classical Results in Terms of X</h2>
                
                <div class="warning-box">
                    <h4>⚠️ Historical Context</h4>
                    <p>For historical reasons, linear model results are often presented in terms of the model matrix X rather than its QR decomposition components. These formulations are of largely <strong>theoretical interest</strong> - they should not generally be used for computational purposes.</p>
                </div>
                
                <h3>8.1 Classical Formulations</h3>
                
                <div class="formula-box">
                    <p><strong>Classical Least Squares Estimator:</strong></p>
                    $$\hat{\boldsymbol{\beta}} = (\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T\mathbf{y}$$
                    
                    <p><strong>Covariance Matrix:</strong></p>
                    $$\text{Var}(\hat{\boldsymbol{\beta}}) = (\mathbf{X}^T\mathbf{X})^{-1}\sigma^2$$
                    
                    <p><strong>Hat Matrix:</strong></p>
                    $$\mathbf{A} = \mathbf{X}(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T$$
                </div>
                
                <h3>8.2 Equivalence to QR Results</h3>
                
                <div class="derivation-box">
                    <h4>🔧 Showing Equivalence</h4>
                    
                    <p><strong>Covariance matrix equivalence:</strong></p>
                    $$(\mathbf{X}^T\mathbf{X})^{-1}\sigma^2 = (\mathbf{R}^T\mathbf{Q}_f^T\mathbf{Q}_f\mathbf{R})^{-1}\sigma^2 = (\mathbf{R}^T\mathbf{R})^{-1}\sigma^2 = \mathbf{R}^{-1}\mathbf{R}^{-T}\sigma^2$$
                    
                    <p><strong>Estimator equivalence:</strong></p>
                    $$(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T\mathbf{y} = \mathbf{R}^{-1}\mathbf{R}^{-T}\mathbf{R}^T\mathbf{Q}_f^T\mathbf{y} = \mathbf{R}^{-1}\mathbf{Q}_f^T\mathbf{y} = \mathbf{R}^{-1}\mathbf{f}$$
                </div>
                
                <div class="insight-box">
                    <h4>💡 Why QR is Preferred</h4>
                    <ul>
                        <li><strong>Numerical stability:</strong> Avoids forming X^T X which can amplify rounding errors</li>
                        <li><strong>Computational efficiency:</strong> Direct solution without matrix inversion</li>
                        <li><strong>Geometric insight:</strong> Clear orthogonal projection interpretation</li>
                        <li><strong>Easier distributional derivations:</strong> Orthogonal transformations preserve normality</li>
                    </ul>
                </div>
            </section>
            
            <!-- Gauss-Markov Theorem -->
            <section id="gauss-markov" class="section">
                <h2>9. The Gauss-Markov Theorem</h2>
                
                <div class="highlight-box">
                    <h3>🏆 Ultimate Optimality Result</h3>
                    <p>How good are least squares estimators? The Gauss-Markov theorem shows that least squares estimators have the <strong>lowest variance</strong> of all unbiased estimators that are linear in the data.</p>
                </div>
                
                <h3>9.1 Statement of the Theorem</h3>
                
                <div class="theorem-box">
                    <h4>📜 Gauss-Markov Theorem</h4>
                    <p><strong>Suppose:</strong> μ ≡ E(y) = Xβ and Var(y) = σ²I</p>
                    
                    <p><strong>Let:</strong> φ̃ = c^T y be any unbiased linear estimator of φ = t^T β, where t is arbitrary</p>
                    
                    <p><strong>Then:</strong> var(φ̃) ≥ var(φ̂)</p>
                    
                    <p>where φ̂ = t^T β̂, and β̂ = (X^T X)⁻¹ X^T y is the least squares estimator.</p>
                    
                    <p><strong>Conclusion:</strong> Each element of β̂ is a minimum variance unbiased estimator among all linear estimators.</p>
                </div>
                
                <h3>9.2 Proof of the Gauss-Markov Theorem</h3>
                
                <div class="proof-box">
                    <h4>📝 Complete Proof</h4>
                    
                    <p><strong>Step 1:</strong> Express variances in terms of c</p>
                    <p>Since φ̃ is linear: var(φ̃) = c^T c σ²</p>
                    
                    <p><strong>Step 2:</strong> Use unbiasedness constraint</p>
                    <p>Unbiasedness of φ̃ requires:</p>
                    $$E(c^T y) = t^T β \Rightarrow c^T E(y) = t^T β \Rightarrow c^T Xβ = t^T β$$
                    <p>This must hold for all β, so: c^T X = t^T</p>
                    
                    <p><strong>Step 3:</strong> Express least squares variance</p>
                    <p>Since Var(β̂) = (X^T X)⁻¹σ²:</p>
                    $$\text{var}(\hat{\phi}) = \text{var}(t^T \hat{\beta}) = t^T (X^T X)^{-1} t \sigma^2$$
                    
                    <p>But from the constraint c^T X = t^T, we can write:</p>
                    $$\text{var}(\hat{\phi}) = c^T X (X^T X)^{-1} X^T c \sigma^2 = c^T A c \sigma^2$$
                    
                    <p><strong>Step 4:</strong> Compare variances</p>
                    <p>We need to show: var(φ̃) ≥ var(φ̂), i.e., c^T c σ² ≥ c^T A c σ²</p>
                    <p>This is equivalent to: c^T (I - A) c ≥ 0</p>
                    
                    <p><strong>Step 5:</strong> Final argument</p>
                    <p>Since (I - A) is idempotent and symmetric:</p>
                    $$c^T (I - A) c = \{(I - A)c\}^T (I - A)c = ||(I - A)c||^2 \geq 0$$
                    
                    <p><strong>Therefore:</strong> var(φ̃) ≥ var(φ̂) with equality if and only if (I - A)c = 0, i.e., c = Ac.</p>
                </div>
                
                <div class="visualization">
                    <h4>Gauss-Markov Theorem Geometric Interpretation</h4>
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Gauss-Markov: Least Squares Minimizes Variance Among Linear Estimators
                        </text>
                        
                        <!-- Constraint set -->
                        <ellipse cx="300" cy="200" rx="180" ry="100" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2"/>
                        <text x="300" y="240" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Unbiased Linear Estimators</text>
                        <text x="300" y="260" text-anchor="middle" font-size="12" fill="#2c3e50">{c : c^T X = t^T}</text>
                        
                        <!-- Least squares estimator -->
                        <circle cx="350" cy="180" r="5" fill="#27ae60"/>
                        <text x="365" y="175" font-size="12" fill="#27ae60" font-weight="bold">LS estimator</text>
                        <text x="365" y="190" font-size="11" fill="#27ae60">c = Ac*</text>
                        <text x="365" y="205" font-size="11" fill="#27ae60">Minimum variance</text>
                        
                        <!-- Other estimators -->
                        <circle cx="250" cy="160" r="3" fill="#e74c3c"/>
                        <circle cx="320" cy="220" r="3" fill="#e74c3c"/>
                        <circle cx="280" cy="200" r="3" fill="#e74c3c"/>
                        <text x="180" y="155" font-size="11" fill="#e74c3c">Other linear</text>
                        <text x="180" y="170" font-size="11" fill="#e74c3c">unbiased estimators</text>
                        <text x="180" y="185" font-size="11" fill="#e74c3c">Higher variance</text>
                        
                        <!-- Variance circles -->
                        <circle cx="350" cy="180" r="30" fill="none" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,5"/>
                        <circle cx="250" cy="160" r="45" fill="none" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <circle cx="320" cy="220" r="50" fill="none" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <text x="500" y="130" font-size="12" fill="#27ae60">Smallest variance circle</text>
                        <text x="500" y="250" font-size="12" fill="#e74c3c">Larger variance circles</text>
                        
                        <!-- Key insight box -->
                        <rect x="450" y="300" width="320" height="180" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="610" y="325" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Key Insights
                        </text>
                        
                        <text x="470" y="350" font-size="13" fill="#2c3e50" font-weight="bold">1. Constraint:</text>
                        <text x="470" y="365" font-size="12" fill="#636e72">Unbiasedness requires c^T X = t^T</text>
                        
                        <text x="470" y="390" font-size="13" fill="#2c3e50" font-weight="bold">2. Optimality:</text>
                        <text x="470" y="405" font-size="12" fill="#636e72">Among all satisfying constraint,</text>
                        <text x="470" y="420" font-size="12" fill="#636e72">LS has minimum variance</text>
                        
                        <text x="470" y="445" font-size="13" fill="#2c3e50" font-weight="bold">3. No normality assumed:</text>
                        <text x="470" y="460" font-size="12" fill="#636e72">Only linearity, unbiasedness, and</text>
                        <text x="470" y="475" font-size="12" fill="#636e72">homoscedasticity required</text>
                        
                        <!-- Mathematical relationship -->
                        <rect x="50" y="300" width="380" height="180" fill="white" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        <text x="240" y="325" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Mathematical Framework
                        </text>
                        
                        <text x="70" y="350" font-size="13" fill="#2c3e50" font-weight="bold">General linear estimator:</text>
                        <text x="70" y="365" font-size="12" fill="#636e72">φ̃ = c^T y, where c ∈ ℝⁿ</text>
                        
                        <text x="70" y="390" font-size="13" fill="#2c3e50" font-weight="bold">Variance:</text>
                        <text x="70" y="405" font-size="12" fill="#636e72">var(φ̃) = c^T c σ² = ||c||² σ²</text>
                        
                        <text x="70" y="430" font-size="13" fill="#2c3e50" font-weight="bold">LS achieves:</text>
                        <text x="70" y="445" font-size="12" fill="#636e72">var(φ̂) = c^T A c σ²</text>
                        <text x="70" y="460" font-size="12" fill="#636e72">where A = X(X^T X)⁻¹X^T</text>
                    </svg>
                </div>
                
                <div class="result-box">
                    <h4>✅ Gauss-Markov Conclusions</h4>
                    <ul>
                        <li><strong>BLUE:</strong> Least squares estimators are Best Linear Unbiased Estimators</li>
                        <li><strong>No normality required:</strong> Result holds under weaker assumptions than full normal theory</li>
                        <li><strong>Optimality class:</strong> Only considers linear estimators - nonlinear estimators might be better</li>
                        <li><strong>Practical implication:</strong> Justifies widespread use of least squares in linear modeling</li>
                    </ul>
                </div>
                
                <div class="insight-box">
                    <h4>🤔 Theorem Limitations</h4>
                    <p>While powerful, the Gauss-Markov theorem has important limitations:</p>
                    <ul>
                        <li><strong>Linearity restriction:</strong> Only considers estimators linear in y</li>
                        <li><strong>Unbiasedness requirement:</strong> Biased estimators might have lower MSE</li>
                        <li><strong>Independence assumption:</strong> Requires Var(y) = σ²I</li>
                        <li><strong>Model specification:</strong> Assumes E(y) = Xβ is correct</li>
                    </ul>
                </div>
            </section>
            
            <!-- Conclusion -->
            <section class="section">
                <h2>🎓 Summary and Conclusions</h2>
                
                <div class="highlight-box">
                    <h3>Complete Theoretical Framework</h3>
                    <p>We have established the complete theoretical foundation for linear model inference, from estimation through hypothesis testing to optimality theory.</p>
                </div>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>🎯 Estimation Theory</h4>
                        <ul>
                            <li>QR decomposition approach</li>
                            <li>Unbiased estimators</li>
                            <li>Exact finite-sample distributions</li>
                            <li>Geometric interpretation</li>
                        </ul>
                    </div>
                    
                    <div class="property-card">
                        <h4>📊 Inference Framework</h4>
                        <ul>
                            <li>t-tests for individual parameters</li>
                            <li>F-tests for linear hypotheses</li>
                            <li>Sequential ANOVA procedures</li>
                            <li>Confidence intervals</li>
                        </ul>
                    </div>
                    
                    <div class="property-card">
                        <h4>🔧 Computational Methods</h4>
                        <ul>
                            <li>Orthogonal decompositions</li>
                            <li>Numerically stable algorithms</li>
                            <li>Hat matrix properties</li>
                            <li>Residual analysis</li>
                        </ul>
                    </div>
                    
                    <div class="property-card">
                        <h4>🏆 Optimality Results</h4>
                        <ul>
                            <li>Gauss-Markov theorem</li>
                            <li>BLUE properties</li>
                            <li>Minimum variance estimation</li>
                            <li>Linear estimator class</li>
                        </ul>
                    </div>
                </div>
                
                <div class="result-box">
                    <h4>🌟 Key Takeaways</h4>
                    <ol>
                        <li><strong>Geometric Foundation:</strong> Linear models are fundamentally about orthogonal projections in high-dimensional spaces</li>
                        <li><strong>QR Superiority:</strong> QR decomposition provides both computational stability and theoretical insight</li>
                        <li><strong>Exact Theory:</strong> All results are exact for finite samples under normality, not just asymptotic</li>
                        <li><strong>Independence Structure:</strong> The separation of parameter estimation (β̂) and variance estimation (σ̂²) enables exact inference</li>
                        <li><strong>Optimality:</strong> Least squares is optimal within the class of linear unbiased estimators</li>
                    </ol>
                </div>
                
                <div class="warning-box">
                    <h4>🚀 Extensions and Advanced Topics</h4>
                    <p>This foundation enables study of:</p>
                    <ul>
                        <li>Generalized linear models (GLMs)</li>
                        <li>Mixed effects models</li>
                        <li>Bayesian linear regression</li>
                        <li>Robust regression methods</li>
                        <li>Regularized estimation (ridge, lasso)</li>
                        <li>Generalized additive models (GAMs)</li>
                    </ul>
                </div>
            </section>
        </main>
        
        <footer>
            <p>&copy; 2024 Linear Models Theory Tutorial</p>
            <p>A comprehensive mathematical treatment of linear model estimation and inference</p>
        </footer>
    </div>
</body>
</html> 