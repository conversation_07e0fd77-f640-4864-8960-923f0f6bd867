<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Poisson Distribution: Key Theorems and Proofs</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .proof-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .step-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .proof-step {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 3px solid #17a2b8;
            border-radius: 0 5px 5px 0;
        }
        
        .theorem-title {
            color: #e67e22;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
        }
        
        .proof-title {
            color: #17a2b8;
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 15px;
        }
        
        .conclusion-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Poisson Distribution: Key Theorems</h1>
            <div class="subtitle">Binomial Approximation and Sum Properties</div>
        </div>

        <h2>🎯 Overview</h2>
        
        <div class="highlight">
            <p>This tutorial focuses on two fundamental theorems about the Poisson distribution:</p>
            <ol>
                <li><strong>Poisson Approximation to Binomial:</strong> How Poisson emerges as a limit of binomial distributions</li>
                <li><strong>Sum of Independent Poisson Variables:</strong> The additive property of Poisson distributions</li>
            </ol>
        </div>

        <h2>📈 Theorem 1: Poisson as Limit of Binomial</h2>
        
        <div class="theorem-box">
            <div class="theorem-title">Theorem 3.1: Poisson Approximation</div>
            <p>Let $X \sim \text{Binomial}(n, p = \frac{\lambda}{n})$, where $\lambda > 0$ is fixed. Then for any $k \in \{0,1,2,\ldots\}$, we have:</p>
            <div class="formula-box">
                $$\lim_{n \to \infty} P_X(k) = \frac{e^{-\lambda}\lambda^k}{k!}$$
            </div>
            <p><strong>Interpretation:</strong> When $n$ is large, $p$ is small, but $np = \lambda$ is moderate, the binomial distribution can be approximated by a Poisson distribution.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Binomial Converging to Poisson (λ = 3)
                </text>
                
                <!-- Left plot: Binomial(30, 0.1) -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Binomial(30, 0.1)
                </text>
                
                <!-- Left axes -->
                <line x1="50" y1="350" x2="350" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="50" y1="350" x2="50" y2="100" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- Binomial bars (n=30, p=0.1, λ=3) -->
                <rect x="70" y="280" width="15" height="70" fill="#e74c3c" opacity="0.8"/>
                <rect x="90" y="230" width="15" height="120" fill="#e74c3c" opacity="0.8"/>
                <rect x="110" y="200" width="15" height="150" fill="#e74c3c" opacity="0.8"/>
                <rect x="130" y="180" width="15" height="170" fill="#e74c3c" opacity="0.8"/>
                <rect x="150" y="190" width="15" height="160" fill="#e74c3c" opacity="0.8"/>
                <rect x="170" y="220" width="15" height="130" fill="#e74c3c" opacity="0.8"/>
                <rect x="190" y="260" width="15" height="90" fill="#e74c3c" opacity="0.8"/>
                <rect x="210" y="300" width="15" height="50" fill="#e74c3c" opacity="0.8"/>
                <rect x="230" y="330" width="15" height="20" fill="#e74c3c" opacity="0.8"/>
                
                <!-- Right plot: Poisson(3) -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Poisson(3) - Limit
                </text>
                
                <!-- Right axes -->
                <line x1="450" y1="350" x2="750" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="450" y1="350" x2="450" y2="100" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- Poisson bars (λ=3) -->
                <rect x="470" y="280" width="15" height="70" fill="#3498db" opacity="0.8"/>
                <rect x="490" y="230" width="15" height="120" fill="#3498db" opacity="0.8"/>
                <rect x="510" y="200" width="15" height="150" fill="#3498db" opacity="0.8"/>
                <rect x="530" y="180" width="15" height="170" fill="#3498db" opacity="0.8"/>
                <rect x="550" y="190" width="15" height="160" fill="#3498db" opacity="0.8"/>
                <rect x="570" y="220" width="15" height="130" fill="#3498db" opacity="0.8"/>
                <rect x="590" y="260" width="15" height="90" fill="#3498db" opacity="0.8"/>
                <rect x="610" y="300" width="15" height="50" fill="#3498db" opacity="0.8"/>
                <rect x="630" y="330" width="15" height="20" fill="#3498db" opacity="0.8"/>
                
                <!-- X-axis labels -->
                <text x="77" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="117" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                <text x="157" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">4</text>
                <text x="197" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">6</text>
                <text x="237" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">8</text>
                
                <text x="477" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="517" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                <text x="557" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">4</text>
                <text x="597" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">6</text>
                <text x="637" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">8</text>
                
                <!-- Arrow showing convergence -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12" />
                    </marker>
                </defs>
                <path d="M 350 200 Q 400 150 450 200" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
                <text x="400" y="140" text-anchor="middle" font-size="12" fill="#f39c12" font-weight="bold">n → ∞</text>
                <text x="400" y="155" text-anchor="middle" font-size="12" fill="#f39c12" font-weight="bold">p → 0</text>
                <text x="400" y="170" text-anchor="middle" font-size="12" fill="#f39c12" font-weight="bold">np = λ</text>
            </svg>
        </div>

        <div class="proof-box">
            <div class="proof-title">Proof of Theorem 3.1</div>
            
            <div class="proof-step">
                <p><span class="step-number">1</span><strong>Starting Point:</strong> We begin with the binomial PMF and substitute $p = \frac{\lambda}{n}$:</p>
                <div class="formula-box">
                    $$\lim_{n \to \infty} P_X(k) = \lim_{n \to \infty} \binom{n}{k} \left(\frac{\lambda}{n}\right)^k \left(1 - \frac{\lambda}{n}\right)^{n-k}$$
                </div>
            </div>
            
            <div class="proof-step">
                <p><span class="step-number">2</span><strong>Expand the Binomial Coefficient:</strong></p>
                <div class="formula-box">
                    $$= \lim_{n \to \infty} \frac{n!}{k!(n-k)!} \cdot \frac{\lambda^k}{n^k} \cdot \left(1 - \frac{\lambda}{n}\right)^{n-k}$$
                </div>
            </div>
            
            <div class="proof-step">
                <p><span class="step-number">3</span><strong>Factor out $\frac{\lambda^k}{k!}$:</strong></p>
                <div class="formula-box">
                    $$= \frac{\lambda^k}{k!} \lim_{n \to \infty} \frac{n(n-1)(n-2)\cdots(n-k+1)}{n^k} \left(1 - \frac{\lambda}{n}\right)^n \left(1 - \frac{\lambda}{n}\right)^{-k}$$
                </div>
            </div>
            
            <div class="proof-step">
                <p><span class="step-number">4</span><strong>Evaluate Each Limit:</strong> For fixed $k$, as $n \to \infty$:</p>
                <ul>
                    <li>$\lim_{n \to \infty} \frac{n(n-1)(n-2)\cdots(n-k+1)}{n^k} = 1$</li>
                    <li>$\lim_{n \to \infty} \left(1 - \frac{\lambda}{n}\right)^{-k} = 1$</li>
                    <li>$\lim_{n \to \infty} \left(1 - \frac{\lambda}{n}\right)^n = e^{-\lambda}$</li>
                </ul>
            </div>
            
            <div class="proof-step">
                <p><span class="step-number">5</span><strong>Final Result:</strong></p>
                <div class="formula-box">
                    $$\lim_{n \to \infty} P_X(k) = \frac{\lambda^k}{k!} \cdot 1 \cdot e^{-\lambda} \cdot 1 = \frac{e^{-\lambda}\lambda^k}{k!}$$
                </div>
            </div>
        </div>

        <div class="conclusion-box">
            <p><strong>Conclusion:</strong> This proves that $\text{Binomial}(n, \frac{\lambda}{n}) \to \text{Poisson}(\lambda)$ as $n \to \infty$.</p>
        </div>

        <h2>➕ Theorem 2: Sum of Independent Poisson Variables</h2>

        <div class="theorem-box">
            <div class="theorem-title">Theorem: Additive Property of Poisson</div>
            <p>Let $X \sim \text{Poisson}(\alpha)$ and $Y \sim \text{Poisson}(\beta)$ be two independent random variables. Define $Z = X + Y$. Then:</p>
            <div class="formula-box">
                $$Z \sim \text{Poisson}(\alpha + \beta)$$
            </div>
            <p><strong>Interpretation:</strong> The sum of independent Poisson random variables is also Poisson, with parameter equal to the sum of the individual parameters.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Sum of Independent Poisson Variables
                </text>

                <!-- X ~ Poisson(2) -->
                <text x="150" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    X ~ Poisson(2)
                </text>
                <rect x="50" y="80" width="200" height="120" fill="#e74c3c" opacity="0.1" stroke="#e74c3c" stroke-width="2" rx="5"/>

                <!-- Bars for X -->
                <rect x="70" y="160" width="12" height="30" fill="#e74c3c" opacity="0.8"/>
                <rect x="90" y="140" width="12" height="50" fill="#e74c3c" opacity="0.8"/>
                <rect x="110" y="120" width="12" height="70" fill="#e74c3c" opacity="0.8"/>
                <rect x="130" y="140" width="12" height="50" fill="#e74c3c" opacity="0.8"/>
                <rect x="150" y="160" width="12" height="30" fill="#e74c3c" opacity="0.8"/>
                <rect x="170" y="175" width="12" height="15" fill="#e74c3c" opacity="0.8"/>
                <rect x="190" y="185" width="12" height="5" fill="#e74c3c" opacity="0.8"/>

                <!-- Y ~ Poisson(3) -->
                <text x="400" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Y ~ Poisson(3)
                </text>
                <rect x="300" y="80" width="200" height="120" fill="#3498db" opacity="0.1" stroke="#3498db" stroke-width="2" rx="5"/>

                <!-- Bars for Y -->
                <rect x="320" y="170" width="12" height="20" fill="#3498db" opacity="0.8"/>
                <rect x="340" y="150" width="12" height="40" fill="#3498db" opacity="0.8"/>
                <rect x="360" y="130" width="12" height="60" fill="#3498db" opacity="0.8"/>
                <rect x="380" y="120" width="12" height="70" fill="#3498db" opacity="0.8"/>
                <rect x="400" y="130" width="12" height="60" fill="#3498db" opacity="0.8"/>
                <rect x="420" y="150" width="12" height="40" fill="#3498db" opacity="0.8"/>
                <rect x="440" y="170" width="12" height="20" fill="#3498db" opacity="0.8"/>
                <rect x="460" y="180" width="12" height="10" fill="#3498db" opacity="0.8"/>

                <!-- Plus sign -->
                <text x="275" y="140" text-anchor="middle" font-size="24" fill="#2c3e50" font-weight="bold">+</text>

                <!-- Equals sign -->
                <text x="525" y="140" text-anchor="middle" font-size="24" fill="#2c3e50" font-weight="bold">=</text>

                <!-- Z ~ Poisson(5) -->
                <text x="650" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Z ~ Poisson(5)
                </text>
                <rect x="550" y="80" width="200" height="120" fill="#2ecc71" opacity="0.1" stroke="#2ecc71" stroke-width="2" rx="5"/>

                <!-- Bars for Z -->
                <rect x="570" y="185" width="10" height="5" fill="#2ecc71" opacity="0.8"/>
                <rect x="585" y="175" width="10" height="15" fill="#2ecc71" opacity="0.8"/>
                <rect x="600" y="160" width="10" height="30" fill="#2ecc71" opacity="0.8"/>
                <rect x="615" y="140" width="10" height="50" fill="#2ecc71" opacity="0.8"/>
                <rect x="630" y="120" width="10" height="70" fill="#2ecc71" opacity="0.8"/>
                <rect x="645" y="110" width="10" height="80" fill="#2ecc71" opacity="0.8"/>
                <rect x="660" y="120" width="10" height="70" fill="#2ecc71" opacity="0.8"/>
                <rect x="675" y="140" width="10" height="50" fill="#2ecc71" opacity="0.8"/>
                <rect x="690" y="160" width="10" height="30" fill="#2ecc71" opacity="0.8"/>
                <rect x="705" y="175" width="10" height="15" fill="#2ecc71" opacity="0.8"/>

                <!-- Parameter labels -->
                <text x="150" y="220" text-anchor="middle" font-size="12" fill="#2c3e50">α = 2</text>
                <text x="400" y="220" text-anchor="middle" font-size="12" fill="#2c3e50">β = 3</text>
                <text x="650" y="220" text-anchor="middle" font-size="12" fill="#2c3e50">α + β = 5</text>

                <!-- Real-world interpretation -->
                <text x="400" y="260" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Real-World Example
                </text>
                <text x="150" y="280" text-anchor="middle" font-size="11" fill="#34495e">Emails from</text>
                <text x="150" y="295" text-anchor="middle" font-size="11" fill="#34495e">Source 1</text>
                <text x="400" y="280" text-anchor="middle" font-size="11" fill="#34495e">Emails from</text>
                <text x="400" y="295" text-anchor="middle" font-size="11" fill="#34495e">Source 2</text>
                <text x="650" y="280" text-anchor="middle" font-size="11" fill="#34495e">Total Emails</text>
                <text x="650" y="295" text-anchor="middle" font-size="11" fill="#34495e">Received</text>
            </svg>
        </div>

        <div class="proof-box">
            <div class="proof-title">Proof: Sum of Independent Poisson Variables</div>

            <div class="proof-step">
                <p><span class="step-number">1</span><strong>Setup:</strong> We need to find the PMF of $Z = X + Y$ where $X \sim \text{Poisson}(\alpha)$ and $Y \sim \text{Poisson}(\beta)$ are independent.</p>
                <p>First, note that $R_Z = \{0, 1, 2, \ldots\}$ since both $X$ and $Y$ have range $\{0, 1, 2, \ldots\}$.</p>
            </div>

            <div class="proof-step">
                <p><span class="step-number">2</span><strong>Apply Law of Total Probability:</strong></p>
                <div class="formula-box">
                    $$P_Z(k) = P(X + Y = k) = \sum_{i=0}^{k} P(X + Y = k | X = i) P(X = i)$$
                </div>
            </div>

            <div class="proof-step">
                <p><span class="step-number">3</span><strong>Simplify Using Independence:</strong></p>
                <div class="formula-box">
                    $$= \sum_{i=0}^{k} P(Y = k - i | X = i) P(X = i) = \sum_{i=0}^{k} P(Y = k - i) P(X = i)$$
                </div>
                <p>The last equality follows because $X$ and $Y$ are independent.</p>
            </div>

            <div class="proof-step">
                <p><span class="step-number">4</span><strong>Substitute Poisson PMFs:</strong></p>
                <div class="formula-box">
                    $$= \sum_{i=0}^{k} \frac{e^{-\beta}\beta^{k-i}}{(k-i)!} \cdot \frac{e^{-\alpha}\alpha^i}{i!}$$
                </div>
            </div>

            <div class="proof-step">
                <p><span class="step-number">5</span><strong>Factor Out Constants:</strong></p>
                <div class="formula-box">
                    $$= e^{-(\alpha + \beta)} \sum_{i=0}^{k} \frac{\alpha^i \beta^{k-i}}{i!(k-i)!}$$
                </div>
            </div>

            <div class="proof-step">
                <p><span class="step-number">6</span><strong>Multiply and Divide by $k!$:</strong></p>
                <div class="formula-box">
                    $$= \frac{e^{-(\alpha + \beta)}}{k!} \sum_{i=0}^{k} \frac{k!}{i!(k-i)!} \alpha^i \beta^{k-i}$$
                </div>
            </div>

            <div class="proof-step">
                <p><span class="step-number">7</span><strong>Recognize Binomial Theorem:</strong></p>
                <div class="formula-box">
                    $$= \frac{e^{-(\alpha + \beta)}}{k!} \sum_{i=0}^{k} \binom{k}{i} \alpha^i \beta^{k-i} = \frac{e^{-(\alpha + \beta)}}{k!} (\alpha + \beta)^k$$
                </div>
                <p>By the binomial theorem: $(a + b)^k = \sum_{i=0}^{k} \binom{k}{i} a^i b^{k-i}$</p>
            </div>

            <div class="proof-step">
                <p><span class="step-number">8</span><strong>Final Result:</strong></p>
                <div class="formula-box">
                    $$P_Z(k) = \frac{e^{-(\alpha + \beta)}(\alpha + \beta)^k}{k!}$$
                </div>
                <p>This is exactly the PMF of a $\text{Poisson}(\alpha + \beta)$ distribution!</p>
            </div>
        </div>

        <div class="conclusion-box">
            <p><strong>Conclusion:</strong> We have proven that $Z = X + Y \sim \text{Poisson}(\alpha + \beta)$.</p>
        </div>

        <h2>🎯 Applications and Implications</h2>

        <div class="step-box">
            <h3>Practical Applications of Theorem 1</h3>
            <ul>
                <li><strong>Quality Control:</strong> When testing many items (large $n$) with low defect rate ($p$ small), use Poisson approximation</li>
                <li><strong>Network Traffic:</strong> Packet arrivals when many users (large $n$) each send packets rarely ($p$ small)</li>
                <li><strong>Insurance Claims:</strong> Many policyholders (large $n$) with low claim probability ($p$ small)</li>
            </ul>
        </div>

        <div class="step-box">
            <h3>Practical Applications of Theorem 2</h3>
            <ul>
                <li><strong>Multiple Sources:</strong> Total arrivals from multiple independent Poisson processes</li>
                <li><strong>Time Aggregation:</strong> Events in consecutive time intervals</li>
                <li><strong>Spatial Aggregation:</strong> Events in adjacent regions</li>
            </ul>
        </div>

        <div class="warning">
            <p><strong>Important Note:</strong> The additive property only holds for <em>independent</em> Poisson random variables. If $X$ and $Y$ are dependent, the sum may not be Poisson.</p>
        </div>

        <div class="highlight">
            <p><strong>Key Takeaway:</strong> These theorems make the Poisson distribution extremely useful in practice. Theorem 1 provides a computational shortcut for binomial probabilities, while Theorem 2 allows us to combine multiple Poisson processes naturally.</p>
        </div>

    </div>
</body>
</html>
