 10.8 Echo State Networks
 The recurrent weights mapping from h( 1)
 t− toh( ) t and the input weights mapping
 from x( ) t to h( ) t are some of the most difficult parameters to learn in a recurrent
 network. One proposed (
 ,
 ;
 ,
 <PERSON><PERSON><PERSON> 2003 <PERSON> et al. 2002 <PERSON><PERSON><PERSON> and <PERSON> 2004
 ,
 <PERSON><PERSON><PERSON> 2007b
 ;
 ,
 ;
 ) approach to avoiding this difficulty is to set the recurrent weights
 such that the recurrent hidden units do a good job of capturing the history of past
 inputs, and learn only the output weights. This is the idea that was independently
 proposed forecho state networks or ESNs(
 ,
 <PERSON><PERSON><PERSON> and <PERSON> 2004 J<PERSON>ger 2007b
 ;
 and liquid state machines (
 <PERSON><PERSON> et al. 2002
 ,
 ,
 )
 ). The latter is similar, except
 that it uses spiking neurons (with binary outputs) instead of the continuous-valued
 hidden units used for ESNs. Both ESNs and liquid state machines are termed
 reservoir computing (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> 2009
 ,
 ) to denote the fact that
 the hidden units form of reservoir of temporal features which may capture different
 aspects of the history of inputs.
 One way to think about these reservoir computing recurrent networks is that
 they are similar to kernel machines: they map an arbitrary length sequence (the
 history of inputs up to time t) into a fixed-length vector (the recurrent state h( ) t ),
 on which a linear predictor (typically a linear regression) can be applied to solve
 the problem of interest. The training criterion may then be easily designed to be
 convex as a function of the output weights. For example, if the output consists
 of linear regression from the hidden units to the output targets, and the training
 criterion is mean squared error, then it is convex and may be solved reliably with
 simple learning algorithms (
 <PERSON>aeger 2003
 ,
 ).
 The important question is therefore: how do we set the input and recurrent
 weights so that a rich set of histories can be represented in the recurrent neural
 network state? The answer proposed in the reservoir computing literature is to
 view the recurrent net as a dynamical system, and set the input and recurrent
 weights such that the dynamical system is near the edge of stability.
 The original idea was to make the eigenvalues of the Jacobian of the state-to
state transition function be close to . As explained in section
 1
 8.2.5
 , an important
 characteristic of a recurrent network is the eigenvalue spectrum of the Jacobians
 J( ) t = ∂s( ) t
 ∂s( 1)
 t− 
. Of particular importance is the spectral radius of J( ) t , defined to
 be the maximum of the absolute values of its eigenvalues.
 404
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 To understand the effect of the spectral radius, consider the simple case of
 back-propagation with a Jacobian matrix J that does not change with t. This
 case happens, for example, when the network is purely linear. Suppose thatJ has
 an eigenvector v with corresponding eigenvalue λ. Consider what happens as we
 propagate a gradient vector backwards through time. If we begin with a gradient
 vector g, then after one step of back-propagation, we will have Jg, and after n
 steps we will have Jng. Now consider what happens if we instead back-propagate
 a perturbed version of g. If we begin with g +δv, then after one step, we will
 have J(g + δv). After n steps, we will have Jn(g +δv). From this we can see
 that back-propagation starting from g and back-propagation starting from g + δv
 diverge by δJnv after n steps of back-propagation. If v is chosen to be a unit
 eigenvector of J with eigenvalue λ, then multiplication by the Jacobian simply
 scales the difference at each step. The two executions of back-propagation are
 separated by a distance of δ λ
 | |n. When vcorresponds to the largest value of | |
 λ ,
 δ
 this perturbation achieves the widest possible separation of an initial perturbation
 of size .
 When | | λ >1, the deviation size δ λ
 | |n grows exponentially large. When | |
 λ <1,
 the deviation size becomes exponentially small.
 Of course, this example assumed that the Jacobian was the same at every
 time step, corresponding to a recurrent network with no nonlinearity. When a
 nonlinearity is present, the derivative of the nonlinearity will approach zero on
 many time steps, and help to prevent the explosion resulting from a large spectral
 radius. Indeed, the most recent work on echo state networks advocates using a
 spectral radius much larger than unity (
 ,
 Yildiz et al. 2012 Jaeger 2012
 ;
 ,
 ).
 Everything we have said about back-propagation via repeated matrix multipli
t
 cation applies equally to forward propagation in a network with no nonlinearity,
 where the state h( +1)
 = h( ) tW.
 When a linear map W always shrinks h as measured by the L2 norm, then
 we say that the map iscontractive. When the spectral radius is less than one,
 the mapping from h( ) t to h( +1)
 t
 is contractive, so a small change becomes smaller
 after each time step. This necessarily makes the network forget information about
 the past when we use a finite level of precision (such as 32 bit integers) to store
 the state vector.
 The Jacobian matrix tells us how a small change of h( ) t propagates one step
 forward, or equivalently, how the gradient on h( +1)
 t
 propagates one step backward,
 during back-propagation. Note that neither W nor J need to be symmetric (al
though they are square and real), so they can have complex-valued eigenvalues and
 eigenvectors, with imaginary components corresponding to potentially oscillatory
 405
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 behavior (if the same Jacobian was applied iteratively). Even though h( ) t or a
 small variation of h( ) t of interest in back-propagation are real-valued, they can
 be expressed in such a complex-valued basis. What matters is what happens to
 the magnitude (complex absolute value) of these possibly complex-valued basis
 coefficients, when we multiply the matrix by the vector. An eigenvalue with
 magnitude greater than one corresponds to magnification (exponential growth, if
 applied iteratively) or shrinking (exponential decay, if applied iteratively).
 With a nonlinear map, the Jacobian is free to change at each step. The
 dynamics therefore become more complicated. However, it remains true that a
 small initial variation can turn into a large variation after several steps. One
 difference between the purely linear case and the nonlinear case is that the use of
 a squashing nonlinearity such as tanh can cause the recurrent dynamics to become
 bounded. Note that it is possible for back-propagation to retain unbounded
 dynamics even when forward propagation has bounded dynamics, for example,
 when a sequence of tanh units are all in the middle of their linear regime and are
 connected by weight matrices with spectral radius greater than . However, it is
 1
 rare for all of the
 tanh
 units to simultaneously lie at their linear activation point.
 The strategy of echo state networks is simply to fix the weights to have some
 3
 spectral radius such as , where information is carried forward through time but
 does not explode due to the stabilizing effect of saturating nonlinearities like tanh.
 More recently, it has been shown that the techniques used to set the weights
 in ESNs could be used to
 initialize
 the weights in a fully trainable recurrent net
work (with the hidden-to-hidden recurrent weights trained using back-propagation
 through time), helping to learn long-term dependencies (Sutskever 2012 Sutskever
 ,
 et al.,
 2013
 ;
 ). In this setting, an initial spectral radius of 1.2 performs well, combined
 with the sparse initialization scheme described in section
 8.4
 .
 10.9 Leaky Units and Other Strategies for Multiple
 Time Scales
 One way to deal with long-term dependencies is to design a model that operates
 at multiple time scales, so that some parts of the model operate at fine-grained
 time scales and can handle small details, while other parts operate at coarse time
 scales and transfer information from the distant past to the present more efficiently.
 Various strategies for building both fine and coarse time scales are possible. These
 include the addition of skip connections across time, “leaky units” that integrate
 signals with different time constants, and the removal of some of the connections
 406
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 used to model fine-grained time scales.
 10.9.1 Adding Skip Connections through Time
 One way to obtain coarse time scales is to add direct connections from variables in
 the distant past to variables in the present. The idea of using such skip connections
 dates back to
 Lin et al. 1996
 (
 ) and follows from the idea of incorporating delays in
 feedforward neural networks (
 Lang and Hinton 1988
 ,
 ). In an ordinary recurrent
 network, a recurrent connection goes from a unit at timet to a unit at time t+ 1.
 It is possible to construct recurrent networks with longer delays (
 As we have seen in section
 8.2.5
 Bengio 1991
 ,
 ).
 , gradients may vanish or explode exponentially
 with respect to the number of time steps.
 Lin et al. 1996
 (
 ) introduced recurrent
 connections with a time-delay of d to mitigate this problem. Gradients now
 diminish exponentially as a function of τ
 d rather than τ. Since there are both
 delayed and single step connections, gradients may still explode exponentially in τ.
 This allows the learning algorithm to capture longer dependencies although not all
 long-term dependencies may be represented well in this way.
 10.9.2 Leaky Units and a Spectrum of Different Time Scales
 Another way to obtain paths on which the product of derivatives is close to one is to
 have units with linear self-connections and a weight near one on these connections.
 When we accumulate a running average µ( ) t of some value v( ) t by applying the
 update µ( ) t ← αµ( 1)
 t− +(1 −α)v( ) t the α parameter is an example of a linear self
connection from µ( 1)
 t− to µ( ) t . Whenα is near one, the running average remembers
 information about the past for a long time, and when α is near zero, information
 about the past is rapidly discarded. Hidden units with linear self-connections can
 behave similarly to such running averages. Such hidden units are called leaky
 units.
 Skip connections through d time steps are a way of ensuring that a unit can
 always learn to be influenced by a value from d time steps earlier. The use of a
 linear self-connection with a weight near one is a different way of ensuring that the
 unit can access values from the past. The linear self-connection approach allows
 this effect to be adapted more smoothly and flexibly by adjusting the real-valued
 α rather than by adjusting the integer-valued skip length.
 These ideas were proposed by
 Jaeger et al. 2007
 ,
 Mozer 1992
 (
 ) and by
 El Hihi and Bengio 1996
 (
 ).
 Leaky units were also found to be useful in the context of echo state networks
 (
 ).
 407
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 There are two basic strategies for setting the time constants used by leaky
 units. One strategy is to manually fix them to values that remain constant, for
 example by sampling their values from some distribution once at initialization time.
 Another strategy is to make the time constants free parameters and learn them.
 Having such leaky units at different time scales appears to help with long-term
 dependencies (
 ,
 Mozer 1992 Pascanu
 ;
 et al.,
 10.9.3 Removing Connections
 2013
 ).
 Another approach to handle long-term dependencies is the idea of organizing
 the state of the RNN at multiple time-scales (
 El Hihi and Bengio 1996
 ,
 ), with
 information flowing more easily through long distances at the slower time scales.
 This idea differs from the skip connections through time discussed earlier
 because it involves actively removing length-one connections and replacing them
 with longer connections. Units modified in such a way are forced to operate on a
 long time scale. Skip connections through time
 add
 edges. Units receiving such
 new connections may learn to operate on a long time scale but may also choose to
 focus on their other short-term connections.
 There are different ways in which a group of recurrent units can be forced to
 operate at different time scales. One option is to make the recurrent units leaky,
 but to have different groups of units associated with different fixed time scales.
 This was the proposal in
 et al. (
 2013
 Mozer 1992
 (
 ) and has been successfully used in
 Pascanu
 ). Another option is to have explicit and discrete updates taking place
 at different times, with a different frequency for different groups of units. This is
 the approach of
 El Hihi and Bengio 1996
 (
 well on a number of benchmark datasets.
 ) and
 Koutnik
 et al. (
 2014
 ). It worked
 10.10 The Long Short-Term Memory and Other Gated
 RNNs
 As of this writing, the most effective sequence models used in practical applications
 are called gated RNNs. These include the long short-term memory and
 networks based on the
 gated recurrent unit
 .
 Like leaky units, gated RNNs are based on the idea of creating paths through
 time that have derivatives that neither vanish nor explode. Leaky units did
 this with connection weights that were either manually chosen constants or were
 parameters. Gated RNNs generalize this to connection weights that may change
 408
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 at each time step.
 output
 ×
 +
 ×
 input
 self-loop
 state
 ×
 input gate
 forget gate
 output gate
 Figure 10.16: Block diagram of the LSTM recurrent network “cell.” Cells are connected
 recurrently to each other, replacing the usual hidden units of ordinary recurrent networks.
 An input feature is computed with a regular artificial neuron unit. Its value can be
 accumulated into the state if the sigmoidal input gate allows it. The state unit has a
 linear self-loop whose weight is controlled by the forget gate. The output of the cell can
 be shut off by the output gate. All the gating units have a sigmoid nonlinearity, while the
 input unit can have any squashing nonlinearity. The state unit can also be used as an
 extra input to the gating units. The black square indicates a delay of a single time step.
 Leaky units allow the network to accumulate information (such as evidence
 for a particular feature or category) over a long duration. However, once that
 information has been used, it might be useful for the neural network to forget the
 old state. For example, if a sequence is made of sub-sequences and we want a leaky
 unit to accumulate evidence inside each sub-subsequence, we need a mechanism to
 forget the old state by setting it to zero. Instead of manually deciding when to
 clear the state, we want the neural network to learn to decide when to do it. This
 409
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 iswhatgatedRNNsdo.
 10.10.1 LSTM
 Theclever ideaof introducingself-loops toproducepathswhere thegradient
 canflowfor longdurations isacorecontributionoftheinitial longshort-term
 memory(LSTM)model(HochreiterandSchmidhuber 1997 , ).Acrucialaddition
 hasbeentomaketheweightonthisself-loopconditionedonthecontext,ratherthan
 fixed( , ).Bymakingtheweightofthisself-loopgated(controlled Gersetal. 2000
 byanotherhiddenunit), thetimescaleof integrationcanbechangeddynamically.
 Inthiscase,wemeanthatevenforanLSTMwithfixedparameters,thetimescale
 of integrationcanchangebasedontheinputsequence,becausethetimeconstants
 areoutputbythemodel itself.TheLSTMhasbeenfoundextremelysuccessful
 inmanyapplications, suchasunconstrainedhandwritingrecognition(Graves
 etal., ), speechrecognition( 2009 Graves 2013 GravesandJaitly 2014 et al., ; , ),
 handwritinggeneration(Graves 2013 , ),machinetranslation(Sutskever 2014 etal., ),
 imagecaptioning ( , ; Kirosetal. 2014bVinyals 2014bXu 2015 etal., ; etal., )and
 parsing(Vinyals 2014a etal., ).
 TheLSTMblockdiagramis illustratedinfigure . Thecorresponding 10.16
 forwardpropagationequationsaregivenbelow, inthecaseofashallowrecurrent
 networkarchitecture.Deeperarchitectureshavealsobeensuccessfullyused(Graves
 etal., ; 2013Pascanu 2014a etal., ). Insteadofaunitthatsimplyappliesanelement
wisenonlinearitytotheaffinetransformationof inputsandrecurrentunits,LSTM
 recurrentnetworkshave“LSTMcells” thathaveaninternalrecurrence(aself-loop),
 inadditiontotheouterrecurrenceof theRNN.Eachcellhasthesame inputs
 andoutputsasanordinaryrecurrentnetwork,buthasmoreparametersanda
 systemofgatingunitsthatcontrolstheflowof information.Themost important
 component isthestateunits() t
 i thathasalinearself-loopsimilartothe leaky
 unitsdescribedintheprevioussection.However,here, theself-loopweight(orthe
 associatedtimeconstant) iscontrolledbyaforgetgateunitf() t
 i (fortimestept
 andcell ), thatsetsthisweighttoavaluebetween0and1viaasigmoidunit: i
 f() t
 i = σ
 
 bf
 i +
 
 j
 Uf
 i,jx() t
 j +
 
 j
 Wf
 i,jh( 1) t−
 j
 
 , (10.40)
 wherex() t isthecurrentinputvectorandh() t isthecurrenthiddenlayervector,
 containingtheoutputsofall theLSTMcells,andbf ,Uf,Wf arerespectively
 biases, inputweightsandrecurrentweightsfortheforgetgates.TheLSTMcell
 410
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 internalstateisthusupdatedasfollows,butwithaconditional self-loopweight
 f() t
 i :
 s() t
 i = f() t
 i s( 1) t−
 i +g() t
 i σ
 
 bi+
 
 j
 Ui,jx() t
 j +
 
 j
 Wi,jh( 1) t−
 j
 
 , (10.41)
 whereb,UandWrespectivelydenotethebiases, inputweightsandrecurrent
 weights intotheLSTMcell. Theexternal inputgateunitg() t
 i is computed
 similarlytotheforgetgate(withasigmoidunittoobtainagatingvaluebetween
 0and1),butwithitsownparameters:
 g() t
 i = σ
 
 bg
 i+
 
 j
 Ug
 i,jx() t
 j +
 
 j
 Wg
 i,jh( 1) t−
 j
 
 . (10.42)
 Theoutputh() t
 i oftheLSTMcellcanalsobeshutoff,viatheoutputgateq() t
 i ,
 whichalsousesasigmoidunitforgating:
 h() t
 i = tanh
 
 s() t
 i
 
 q() t
 i (10.43)
 q() t
 i = σ
 
 bo
 i+
 
 j
 Uo
 i,jx() t
 j +
 
 j
 Wo
 i,jh( 1) t−
 j
 
  (10.44)
 whichhasparametersbo,Uo,Wo for itsbiases, inputweightsandrecurrent
 weights, respectively.Amongthevariants,onecanchoosetousethecell states() t
 i
 asanextrainput(withitsweight)intothethreegatesofthei-thunit,asshown
 infigure .Thiswouldrequirethreeadditionalparameters. 10.16
 LSTMnetworkshavebeenshowntolearnlong-termdependenciesmoreeasily
 thanthesimplerecurrentarchitectures,firstonartificialdatasetsdesignedfor
 testingtheabilitytolearnlong-termdependencies( , ; Bengioetal. 1994Hochreiter
 andSchmidhuber 1997 Hochreiter 2001 , ; etal., ), thenonchallengingsequence
 processingtaskswherestate-of-the-artperformancewasobtained(Graves 2012 , ;
 Graves 2013 Sutskever 2014 etal., ; etal., ).VariantsandalternativestotheLSTM
 havebeenstudiedandusedandarediscussednext.
 10.10.2 OtherGatedRNNs
 Whichpieces of the LSTMarchitectureare actuallynecessary? What other
 successfularchitecturescouldbedesignedthatallowthenetworktodynamically
 control thetimescaleandforgettingbehaviorofdifferentunits?
 411
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 SomeanswerstothesequestionsaregivenwiththerecentworkongatedRNNs,
 whoseunitsarealsoknownasgatedrecurrentunitsorGRUs( , ; Choetal. 2014b
 Chung 2014 2015a Jozefowicz 2015Chrupala 2015 etal., , ; etal., ; etal., ).Themain
 differencewiththeLSTMisthatasinglegatingunitsimultaneouslycontrolsthe
 forgettingfactorandthedecisiontoupdatethestateunit.Theupdateequations
 arethefollowing:
 h() t
 i = u( 1) t−
 i h( 1) t−
 i +(1−u( 1) t−
 i )σ
 
 bi+
 
 j
 Ui,jx( 1) t−
 j +
 
 j
 Wi,jr( 1) t−
 j h( 1) t−
 j
 
 ,
 (10.45)
 whereustandsfor“update”gateandrfor“reset” gate.Theirvalueisdefinedas
 usual:
 u() t
 i = σ
 
 bu i+
 
 j
 Uu i,jx() t
 j +
 
 j
 Wu i,jh() t
 j
 
  (10.46)
 and
 r() t
 i = σ
 
 br
 i+
 
 j
 Ur
 i,jx() t
 j +
 
 j
 Wr
 i,jh() t
 j
 
 . (10.47)
 Theresetandupdatesgatescanindividually“ignore” partsofthestatevector.
 Theupdategatesact likeconditional leakyintegratorsthatcanlinearlygateany
 dimension, thuschoosingtocopyit(atoneextremeofthesigmoid)orcompletely
 ignoreit(attheotherextreme)byreplacingitbythenew“targetstate”value
 (towardswhichtheleakyintegratorwantstoconverge).Theresetgatescontrol
 whichpartsofthestategetusedtocomputethenexttargetstate, introducingan
 additionalnonlineareffectintherelationshipbetweenpaststateandfuturestate.
 Manymorevariantsaroundthis themecanbedesigned. For examplethe
 resetgate(orforgetgate)outputcouldbesharedacrossmultiplehiddenunits.
 Alternately, theproductofaglobalgate(coveringawholegroupofunits, suchas
 anentirelayer)andalocalgate(perunit)couldbeusedtocombineglobalcontrol
 andlocal control. However, several investigationsoverarchitecturalvariations
 oftheLSTMandGRUfoundnovariantthatwouldclearlybeatbothofthese
 acrossawiderangeof tasks( , ; Greffet al. 2015 Jozefowicz 2015 Greff et al., ).
 etal.( )foundthatacrucial ingredient istheforgetgate,while 2015 Jozefowicz
 etal.( ) foundthataddingabiasof1totheLSTMforgetgate,apractice 2015
 advocatedby ( ),makestheLSTMasstrongasthebestof the Gersetal. 2000
 exploredarchitecturalvariants.