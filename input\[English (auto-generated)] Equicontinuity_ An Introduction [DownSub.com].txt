hello students in this video we'll
discuss AC
continuity a family of
functions script
F such that these functions f are
mappings from a set a into
R so what our family looks like it's
called AG continuous
if for every Epsilon great than
zero there is a
Delta such that such that what here's
our equi continuity Criterion the
Criterion is the same as ordinary
continuity such that if xus Y is less
than Delta this implies that F
ofx minus F of Y is less than Epsilon
this has to be true for all X and Y in
the set a for all X and Y in the set a
and for all functions in the class for
every F in this class over here so
that's what the definition of equ
continuity is okay so it's for a family
function so let me show you an example
of something which is not equ continuous
and a family function which is equ
continuous okay so here's an
example so the family of functions x^ n
n goes greater than or equal to 1 is not
equicontinuous is not equicontinuous on
01 okay why not so this is an non
example of equ continuity so why not so
let me let take Epsilon to
be2 and then
consider
points of the form consider points let's
say a
n a n which is 1 - one/ n
right and so these points so a n a n
minus one is just equal to the how how
far are these points away from one they
are 1 over a 1 over n away from one
and let's look at the difference of f of
a n minus F of one okay well F of a n is
going to be what this is going to be 1 -
1 / n ^
nus1 okay and as n goes to Infinity this
tends to 1 over e so for n sufficiently
large for n large enough this is
strictly bigger than because 1 over e is
like 1 over three basically so this is
going to be bigger than strictly bigger
than 1/2 if n is sufficiently large n
large
enough so as n goes to in as n goes to
Infinity I'm violating this this
condition over here so as these points
getting arbitrary close F of one but
we're not continuous there so this
family is not equ continuous that shows
we're not equ continuous because I found
a sequence of Deltas namely these a n
such that the function value F of a n
minus F of that point1 we're not and so
that violates a condition for every X
and Y in the set 01 okay so this fun
that is not equicontinuous but what is
an example of an equicontinuous family
so here's an example of an
equicontinuous family
example if FN converges uniformly to F
on 01
and the FN are
continuous then the family FN
then
FN n goes from 1 to Infinity is
equicontinuous is
equicontinuous okay now how can we see
that well since the set is 01 the key
feature here is that this is a compact
set 01 is compact okay so in other words
these functions FN are all uniformly
continuous right so let's let Epson be
greater than
zero and then
pick n such that if
n and M are bigger than or equal to this
number n then FN of x - FM ofx is less
than
Epsilon
for M and N in this range for all X and
01 for all X and
01 we can do this of course because the
sequence converges uniform so over here
I'm using uniform convergence so we're
using uniform
convergence okay
good and so now up to this value n since
I have a finite since this N is a finite
number we can
find by the uniform
continuity
there're using the fact that they're
continuous in a compact set so defitely
continuous for
every one less than or equal to J less
than or equal to n capital what can we
say we can say that FJ of X there exists
a
Delta there exist a
Delta
and that Delta exists that's the same
Delta for every one of these JS such
that FJ of x minus FJ of Y is going to
be less than Epsilon over 3 if x -
Y is less than Delta right so F1 for
example is uniformly continuous I can
make that happen F2 F3 F4 F5 F6 all the
way up to FN are uniformly continuous so
for each one of these JS between zero
and as long as it's a finite number of
them and this N is a fixed finite number
I can make this less than Epsilon over 3
now I can use this condition over here I
should actually put this let me make
this an Epsilon over 3 to make my
arguments balance out correctly okay so
an Epsilon over 3 now what I like to do
is i' like to estimate for any so this
condition uh this is good right this is
so I need to estimate I need to find an
estimate like this for any n past this
range over here so let's look at FN so
let's suppose that n is bigger than or
equal to if n is bigger than or equal to
n
capital let's estimate FN of x - F N of
Y assuming that x - Y is less than this
Delta
right so what can we do over
here we can throw in an N for example
right because I know that that condition
is satis over there so let's add and
subtract in FN of X and FN of Y right so
this is equal to FN of X plus minus FN
capital of X Plus - FN capital of Y
minus FN of
Y okay and so now what can we say over
here so I can say now that for example
that this FN
minus so have a couple terms that we're
going to look at over here so we have
three terms we're going to have the f n
of x minus FN capital of X that's good
then we'll have an FN Capital minus FN
of Y then we'll have one of these terms
over here we'll have an f n capital of x
minus FN capital of Y and finally we'll
have an FN capital of
Y minus F little n of
Y like that okay now I have these three
terms so how am I going to estimate
these three terms over here well we can
we say about this term over here this
term over here since we're in this
regime since n is bigger than these are
valid for n bigger than equal n that's
less than Epsilon over 3 this term over
here is less than Epson over3 by the
uniform continuity of that guy and this
term over here is less than eps over
three but again by the um by the assumpt
by this assumption over here by the
uniform conversion so all three of those
things are Epsilon over 3 Epsilon over
3ep 3 so it's less than Epsilon so other
words I've just PR that FN of xus FN of
Y is less than Epsilon for any n bigger
than or equal to n capital x - Y is less
than Delta and that proves the uniform
continuity of the family thank you very
much