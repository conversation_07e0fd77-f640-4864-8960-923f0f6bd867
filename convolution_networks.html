<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Convolutional Neural Networks</title>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #166088;
            --accent-color: #e63946;
            --background-color: #f8f9fa;
            --text-color: #333;
            --light-gray: #e9ecef;
            --medium-gray: #dee2e6;
            --dark-gray: #adb5bd;
            --code-bg: #f5f5f5;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        h1 {
            color: var(--primary-color);
            font-size: 2.5em;
            margin-bottom: 0.5em;
        }
        
        h2 {
            color: var(--secondary-color);
            font-size: 1.8em;
            border-bottom: 2px solid var(--medium-gray);
            padding-bottom: 10px;
            margin-top: 40px;
        }
        
        h3 {
            color: var(--secondary-color);
            font-size: 1.4em;
            margin-top: 30px;
        }
        
        p {
            margin-bottom: 1.2em;
        }
        
        .concept-box {
            background-color: var(--light-gray);
            border-left: 5px solid var(--primary-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .note-box {
            background-color: #fff3cd;
            border-left: 5px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .example-box {
            background-color: #d1e7dd;
            border-left: 5px solid #198754;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .formula-box {
            background-color: white;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .visualization svg {
            max-width: 100%;
            height: auto;
        }
        
        .visualization figcaption {
            margin-top: 10px;
            font-style: italic;
            color: var(--dark-gray);
        }
        
        .code {
            font-family: 'Courier New', Courier, monospace;
            background-color: var(--code-bg);
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .comparison {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            flex: 1;
            min-width: 300px;
            background-color: white;
            padding: 15px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-item h4 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 1px solid var(--medium-gray);
            padding-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        table th, table td {
            padding: 10px;
            border: 1px solid var(--medium-gray);
        }
        
        table th {
            background-color: var(--light-gray);
            font-weight: bold;
        }
        
        table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        ul, ol {
            margin-bottom: 20px;
            padding-left: 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .toc {
            background-color: white;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .toc h2 {
            margin-top: 0;
            font-size: 1.5em;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 10px;
        }
        
        .toc li {
            margin-bottom: 10px;
        }
        
        .toc a {
            display: block;
            padding: 5px 0;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .comparison {
                flex-direction: column;
            }
        }
    </style>
    <!-- MathJax Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                tags: 'ams'
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
</head>
<body>
    <header>
        <h1>Understanding Convolutional Neural Networks</h1>
        <p>A comprehensive tutorial on the principles and applications of CNNs</p>
    </header>

    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#introduction">1. Introduction to Convolutional Networks</a></li>
            <li><a href="#convolution">2. The Convolution Operation</a></li>
            <li><a href="#motivation">3. Motivation for Using Convolution</a></li>
            <li><a href="#pooling">4. Pooling</a></li>
        </ul>
    </div>

    <section id="introduction">
        <h2>1. Introduction to Convolutional Networks</h2>
        
        <p>
            Convolutional networks (also known as convolutional neural networks or CNNs) are a specialized type of neural network designed for processing data with a known grid-like topology. They have revolutionized computer vision and have become the foundation for many state-of-the-art systems in image recognition, object detection, and numerous other applications.
        </p>
        
        <div class="concept-box">
            <p>
                <strong>Key definition:</strong> Convolutional neural networks are neural networks that use the convolution operation in place of general matrix multiplication in at least one of their layers.
            </p>
        </div>
        
        <h3>What Makes CNNs Special?</h3>
        
        <p>
            CNNs are particularly well-suited for processing:
        </p>
        
        <ul>
            <li><strong>Image data</strong> - 2D grids of pixels</li>
            <li><strong>Time-series data</strong> - 1D grids of samples taken at regular intervals</li>
            <li><strong>Any data with spatial or temporal relationships</strong> where local patterns are important</li>
        </ul>
        
        <p>
            The power of CNNs comes from their ability to automatically and adaptively learn spatial hierarchies of features from the input data. For example, when processing images:
        </p>
        
        <ul>
            <li>Lower layers might identify edges and simple textures</li>
            <li>Middle layers might recognize patterns like eyes, noses, or wheels</li>
            <li>Higher layers might detect entire objects like faces, people, or cars</li>
        </ul>
        
        <div class="visualization">
            <svg width="900" height="400" viewBox="0 0 900 400">
                <!-- Background -->
                <rect x="0" y="0" width="900" height="400" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="450" y="30" font-family="Arial" font-size="20" text-anchor="middle" fill="#166088">Convolutional Neural Network Architecture</text>
                
                <!-- Input Image -->
                <rect x="40" y="80" width="120" height="120" fill="#d1e7dd" stroke="#198754" stroke-width="2" rx="5" ry="5" />
                <text x="100" y="220" font-family="Arial" font-size="16" text-anchor="middle">Input Image</text>
                <text x="100" y="240" font-family="Arial" font-size="14" text-anchor="middle">28x28x1</text>
                
                <!-- Conv Layer 1 -->
                <rect x="200" y="60" width="100" height="160" fill="#cfe2ff" stroke="#0d6efd" stroke-width="2" rx="5" ry="5" />
                <text x="250" y="220" font-family="Arial" font-size="16" text-anchor="middle">Conv Layer 1</text>
                <text x="250" y="240" font-family="Arial" font-size="14" text-anchor="middle">24x24x32</text>
                
                <!-- Feature maps visualization in Conv1 -->
                <rect x="210" y="80" width="20" height="20" fill="#0d6efd" opacity="0.3" />
                <rect x="235" y="80" width="20" height="20" fill="#0d6efd" opacity="0.4" />
                <rect x="260" y="80" width="20" height="20" fill="#0d6efd" opacity="0.5" />
                <rect x="210" y="105" width="20" height="20" fill="#0d6efd" opacity="0.6" />
                <rect x="235" y="105" width="20" height="20" fill="#0d6efd" opacity="0.7" />
                <rect x="260" y="105" width="20" height="20" fill="#0d6efd" opacity="0.3" />
                <rect x="210" y="130" width="20" height="20" fill="#0d6efd" opacity="0.4" />
                <rect x="235" y="130" width="20" height="20" fill="#0d6efd" opacity="0.5" />
                <rect x="260" y="130" width="20" height="20" fill="#0d6efd" opacity="0.6" />
                <rect x="210" y="155" width="20" height="20" fill="#0d6efd" opacity="0.3" />
                <rect x="235" y="155" width="20" height="20" fill="#0d6efd" opacity="0.5" />
                <rect x="260" y="155" width="20" height="20" fill="#0d6efd" opacity="0.7" />
                
                <!-- Pooling Layer 1 -->
                <rect x="340" y="70" width="80" height="140" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5" ry="5" />
                <text x="380" y="220" font-family="Arial" font-size="16" text-anchor="middle">Pooling Layer</text>
                <text x="380" y="240" font-family="Arial" font-size="14" text-anchor="middle">12x12x32</text>
                
                <!-- Feature maps visualization in Pool1 -->
                <rect x="350" y="90" width="15" height="15" fill="#ffc107" opacity="0.4" />
                <rect x="370" y="90" width="15" height="15" fill="#ffc107" opacity="0.6" />
                <rect x="395" y="90" width="15" height="15" fill="#ffc107" opacity="0.3" />
                <rect x="350" y="115" width="15" height="15" fill="#ffc107" opacity="0.7" />
                <rect x="370" y="115" width="15" height="15" fill="#ffc107" opacity="0.4" />
                <rect x="395" y="115" width="15" height="15" fill="#ffc107" opacity="0.5" />
                <rect x="350" y="140" width="15" height="15" fill="#ffc107" opacity="0.3" />
                <rect x="370" y="140" width="15" height="15" fill="#ffc107" opacity="0.5" />
                <rect x="395" y="140" width="15" height="15" fill="#ffc107" opacity="0.7" />
                <rect x="350" y="165" width="15" height="15" fill="#ffc107" opacity="0.4" />
                <rect x="370" y="165" width="15" height="15" fill="#ffc107" opacity="0.6" />
                <rect x="395" y="165" width="15" height="15" fill="#ffc107" opacity="0.5" />
                
                <!-- Conv Layer 2 -->
                <rect x="460" y="60" width="100" height="160" fill="#cfe2ff" stroke="#0d6efd" stroke-width="2" rx="5" ry="5" />
                <text x="510" y="220" font-family="Arial" font-size="16" text-anchor="middle">Conv Layer 2</text>
                <text x="510" y="240" font-family="Arial" font-size="14" text-anchor="middle">8x8x64</text>
                
                <!-- Feature maps visualization in Conv2 -->
                <rect x="470" y="80" width="15" height="15" fill="#0d6efd" opacity="0.3" />
                <rect x="490" y="80" width="15" height="15" fill="#0d6efd" opacity="0.4" />
                <rect x="510" y="80" width="15" height="15" fill="#0d6efd" opacity="0.5" />
                <rect x="530" y="80" width="15" height="15" fill="#0d6efd" opacity="0.6" />
                <rect x="470" y="100" width="15" height="15" fill="#0d6efd" opacity="0.7" />
                <rect x="490" y="100" width="15" height="15" fill="#0d6efd" opacity="0.3" />
                <rect x="510" y="100" width="15" height="15" fill="#0d6efd" opacity="0.5" />
                <rect x="530" y="100" width="15" height="15" fill="#0d6efd" opacity="0.4" />
                <rect x="470" y="120" width="15" height="15" fill="#0d6efd" opacity="0.6" />
                <rect x="490" y="120" width="15" height="15" fill="#0d6efd" opacity="0.5" />
                <rect x="510" y="120" width="15" height="15" fill="#0d6efd" opacity="0.3" />
                <rect x="530" y="120" width="15" height="15" fill="#0d6efd" opacity="0.7" />
                <rect x="470" y="140" width="15" height="15" fill="#0d6efd" opacity="0.4" />
                <rect x="490" y="140" width="15" height="15" fill="#0d6efd" opacity="0.5" />
                <rect x="510" y="140" width="15" height="15" fill="#0d6efd" opacity="0.6" />
                <rect x="530" y="140" width="15" height="15" fill="#0d6efd" opacity="0.3" />
                <rect x="470" y="160" width="15" height="15" fill="#0d6efd" opacity="0.5" />
                <rect x="490" y="160" width="15" height="15" fill="#0d6efd" opacity="0.7" />
                <rect x="510" y="160" width="15" height="15" fill="#0d6efd" opacity="0.4" />
                <rect x="530" y="160" width="15" height="15" fill="#0d6efd" opacity="0.6" />
                
                <!-- Pooling Layer 2 -->
                <rect x="600" y="70" width="80" height="140" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5" ry="5" />
                <text x="640" y="220" font-family="Arial" font-size="16" text-anchor="middle">Pooling Layer</text>
                <text x="640" y="240" font-family="Arial" font-size="14" text-anchor="middle">4x4x64</text>
                
                <!-- Feature maps visualization in Pool2 -->
                <rect x="610" y="90" width="10" height="10" fill="#ffc107" opacity="0.5" />
                <rect x="625" y="90" width="10" height="10" fill="#ffc107" opacity="0.7" />
                <rect x="640" y="90" width="10" height="10" fill="#ffc107" opacity="0.4" />
                <rect x="655" y="90" width="10" height="10" fill="#ffc107" opacity="0.6" />
                <rect x="610" y="105" width="10" height="10" fill="#ffc107" opacity="0.3" />
                <rect x="625" y="105" width="10" height="10" fill="#ffc107" opacity="0.5" />
                <rect x="640" y="105" width="10" height="10" fill="#ffc107" opacity="0.7" />
                <rect x="655" y="105" width="10" height="10" fill="#ffc107" opacity="0.4" />
                <rect x="610" y="120" width="10" height="10" fill="#ffc107" opacity="0.6" />
                <rect x="625" y="120" width="10" height="10" fill="#ffc107" opacity="0.3" />
                <rect x="640" y="120" width="10" height="10" fill="#ffc107" opacity="0.5" />
                <rect x="655" y="120" width="10" height="10" fill="#ffc107" opacity="0.7" />
                <rect x="610" y="135" width="10" height="10" fill="#ffc107" opacity="0.4" />
                <rect x="625" y="135" width="10" height="10" fill="#ffc107" opacity="0.6" />
                <rect x="640" y="135" width="10" height="10" fill="#ffc107" opacity="0.3" />
                <rect x="655" y="135" width="10" height="10" fill="#ffc107" opacity="0.5" />
                
                <!-- Fully Connected Layer -->
                <rect x="720" y="90" width="60" height="100" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="5" ry="5" />
                <text x="750" y="220" font-family="Arial" font-size="16" text-anchor="middle">Fully Connected</text>
                <text x="750" y="240" font-family="Arial" font-size="14" text-anchor="middle">1024 neurons</text>
                
                <!-- FC Visualization -->
                <circle cx="735" cy="110" r="3" fill="#dc3545" />
                <circle cx="750" cy="110" r="3" fill="#dc3545" />
                <circle cx="765" cy="110" r="3" fill="#dc3545" />
                <circle cx="735" cy="125" r="3" fill="#dc3545" />
                <circle cx="750" cy="125" r="3" fill="#dc3545" />
                <circle cx="765" cy="125" r="3" fill="#dc3545" />
                <circle cx="735" cy="140" r="3" fill="#dc3545" />
                <circle cx="750" cy="140" r="3" fill="#dc3545" />
                <circle cx="765" cy="140" r="3" fill="#dc3545" />
                <circle cx="735" cy="155" r="3" fill="#dc3545" />
                <circle cx="750" cy="155" r="3" fill="#dc3545" />
                <circle cx="765" cy="155" r="3" fill="#dc3545" />
                <circle cx="735" cy="170" r="3" fill="#dc3545" />
                <circle cx="750" cy="170" r="3" fill="#dc3545" />
                <circle cx="765" cy="170" r="3" fill="#dc3545" />
                
                <!-- Output Layer -->
                <rect x="820" y="100" width="40" height="80" fill="#d1e7dd" stroke="#198754" stroke-width="2" rx="5" ry="5" />
                <text x="840" y="220" font-family="Arial" font-size="16" text-anchor="middle">Output</text>
                <text x="840" y="240" font-family="Arial" font-size="14" text-anchor="middle">10 classes</text>
                
                <!-- Output Visualization -->
                <circle cx="840" cy="115" r="4" fill="#198754" />
                <circle cx="840" cy="130" r="4" fill="#198754" />
                <circle cx="840" cy="145" r="4" fill="#198754" opacity="0.7" />
                <circle cx="840" cy="160" r="4" fill="#198754" />
                <circle cx="840" cy="175" r="4" fill="#198754" />
                
                <!-- Arrows -->
                <line x1="160" y1="140" x2="200" y1="140" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="300" y1="140" x2="340" y1="140" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="420" y1="140" x2="460" y1="140" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="560" y1="140" x2="600" y1="140" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="680" y1="140" x2="720" y1="140" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="780" y1="140" x2="820" y1="140" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)" />
                
                <!-- Arrowhead marker -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                </defs>
                
                <!-- Feature hierarchy labels -->
                <text x="100" y="300" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">Raw Pixels</text>
                <text x="250" y="300" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">Edges, Textures</text>
                <text x="510" y="300" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">Patterns, Parts</text>
                <text x="750" y="300" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">High-level Features</text>
                <text x="840" y="300" font-family="Arial" font-size="14" text-anchor="middle" fill="#333">Classification</text>
                
                <!-- Feature hierarchy lines -->
                <line x1="100" y1="260" x2="100" y1="290" stroke="#666" stroke-width="1" stroke-dasharray="4" />
                <line x1="250" y1="260" x2="250" y1="290" stroke="#666" stroke-width="1" stroke-dasharray="4" />
                <line x1="510" y1="260" x2="510" y1="290" stroke="#666" stroke-width="1" stroke-dasharray="4" />
                <line x1="750" y1="260" x2="750" y1="290" stroke="#666" stroke-width="1" stroke-dasharray="4" />
                <line x1="840" y1="260" x2="840" y1="290" stroke="#666" stroke-width="1" stroke-dasharray="4" />
            </svg>
            <figcaption>Figure 1: Typical architecture of a convolutional neural network for image classification.</figcaption>
        </div>
        
        <h3>Core Components of CNNs</h3>
        
        <p>
            There are three key components that make up a typical CNN:
        </p>
        
        <div class="comparison">
            <div class="comparison-item">
                <h4>Convolution Layer</h4>
                <p>Applies a set of learnable filters (also called kernels) to the input. Each filter activates when it detects a specific feature at some spatial position in the input.</p>
            </div>
            
            <div class="comparison-item">
                <h4>Nonlinearity (Activation)</h4>
                <p>Applies a non-linear activation function (commonly ReLU) to introduce non-linearity in the model, enabling it to learn complex patterns.</p>
            </div>
            
            <div class="comparison-item">
                <h4>Pooling Layer</h4>
                <p>Performs downsampling operation to reduce the spatial dimensions (width and height) of the input volume, reducing computation and helping to make features more robust.</p>
            </div>
        </div>
        
        <p>
            These three components form the building blocks of a CNN architecture. After several convolutional and pooling layers, the high-level reasoning in the neural network is done via fully connected layers, which perform classification or regression based on the features extracted by the convolutional layers.
        </p>
        
        <div class="note-box">
            <p>
                <strong>Note:</strong> While CNNs have become synonymous with image processing, they can be applied to any data with a grid-like topology, including 1D signals (time series, audio), 2D grids (images), and 3D volumes (videos, medical imaging).
            </p>
        </div>
    </section>

    <section id="convolution">
        <h2>2. The Convolution Operation</h2>
        
        <p>
            At the heart of convolutional neural networks is the convolution operation. To understand how CNNs work, we need to first grasp what convolution is and how it operates on data.
        </p>
        
        <div class="concept-box">
            <p>
                <strong>Key definition:</strong> Convolution is a mathematical operation that combines two functions to produce a third function expressing how the shape of one is modified by the other.
            </p>
        </div>
        
        <h3>2.1 Understanding Convolution Through an Example</h3>
        
        <p>
            Let's understand convolution through a real-world example. Imagine tracking a spaceship with a laser sensor that gives us the position x(t) at time t. However, our sensor readings are noisy, so we want to smooth them out.
        </p>
        
        <p>
            To reduce noise, we can create a weighted average of measurements, giving more weight to recent measurements. This weighting function w(a) (where a is the age of a measurement) is applied at every moment to get a smoothed estimate:
        </p>
        
        <div class="formula-box">
            $$s(t) = \int x(a)w(t-a)da$$
        </div>
        
        <p>
            This operation is called convolution and is typically denoted with an asterisk:
        </p>
        
        <div class="formula-box">
            $$s(t) = (x * w)(t)$$
        </div>
        
        <div class="visualization">
            <svg width="750" height="350" viewBox="0 0 750 350">
                <!-- Background -->
                <rect x="0" y="0" width="750" height="350" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="375" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Convolution as Weighted Average</text>
                
                <!-- X-axis for time -->
                <line x1="50" y1="250" x2="700" y1="250" stroke="black" stroke-width="1.5" />
                <text x="375" y="280" font-family="Arial" font-size="14" text-anchor="middle">Time</text>
                
                <!-- Y-axis for position -->
                <line x1="50" y1="50" x2="50" y1="250" stroke="black" stroke-width="1.5" />
                <text x="30" y="150" font-family="Arial" font-size="14" text-anchor="middle" transform="rotate(-90, 30, 150)">Position</text>
                
                <!-- Time ticks -->
                <line x1="100" y1="250" x2="100" y1="255" stroke="black" stroke-width="1.5" />
                <text x="100" y="270" font-family="Arial" font-size="12" text-anchor="middle">t-4</text>
                
                <line x1="200" y1="250" x2="200" y1="255" stroke="black" stroke-width="1.5" />
                <text x="200" y="270" font-family="Arial" font-size="12" text-anchor="middle">t-3</text>
                
                <line x1="300" y1="250" x2="300" y1="255" stroke="black" stroke-width="1.5" />
                <text x="300" y="270" font-family="Arial" font-size="12" text-anchor="middle">t-2</text>
                
                <line x1="400" y1="250" x2="400" y1="255" stroke="black" stroke-width="1.5" />
                <text x="400" y="270" font-family="Arial" font-size="12" text-anchor="middle">t-1</text>
                
                <line x1="500" y1="250" x2="500" y1="255" stroke="black" stroke-width="1.5" />
                <text x="500" y="270" font-family="Arial" font-size="12" text-anchor="middle">t</text>
                
                <line x1="600" y1="250" x2="600" y1="255" stroke="black" stroke-width="1.5" />
                <text x="600" y="270" font-family="Arial" font-size="12" text-anchor="middle">t+1</text>
                
                <!-- Noisy signal data points -->
                <circle cx="100" cy="180" r="4" fill="#e63946" />
                <circle cx="200" cy="120" r="4" fill="#e63946" />
                <circle cx="300" cy="210" r="4" fill="#e63946" />
                <circle cx="400" cy="110" r="4" fill="#e63946" />
                <circle cx="500" cy="170" r="4" fill="#e63946" />
                
                <!-- Noisy signal line -->
                <polyline points="100,180 200,120 300,210 400,110 500,170" fill="none" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- Smooth signal line -->
                <path d="M 100,170 Q 200,160 300,150 Q 400,140 500,160" fill="none" stroke="#4a6fa5" stroke-width="3" />
                
                <!-- Weight function visualization -->
                <rect x="300" y="50" width="200" height="80" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="400" y="70" font-family="Arial" font-size="14" text-anchor="middle">Weighting Function w(a)</text>
                
                <!-- Weight function curve -->
                <path d="M 310,110 Q 340,100 370,95 Q 400,90 430,95 Q 460,100 490,110" fill="none" stroke="#166088" stroke-width="2" />
                
                <!-- Weight annotations -->
                <line x1="350" y1="100" x2="350" y1="170" stroke="#166088" stroke-width="1" stroke-dasharray="3,2" />
                <line x1="400" y1="90" x2="400" y1="110" stroke="#166088" stroke-width="1" stroke-dasharray="3,2" />
                <line x1="450" y1="100" x2="450" y1="140" stroke="#166088" stroke-width="1" stroke-dasharray="3,2" />
                
                <text x="350" y="190" font-family="Arial" font-size="12" text-anchor="middle" fill="#166088">Lower weight</text>
                <text x="400" y="120" font-family="Arial" font-size="12" text-anchor="middle" fill="#166088">Highest weight</text>
                <text x="450" y="150" font-family="Arial" font-size="12" text-anchor="middle" fill="#166088">Medium weight</text>
                
                <!-- Legend -->
                <rect x="550" y="80" width="150" height="70" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                
                <line x1="560" y1="100" x2="590" y1="100" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                <text x="670" y="105" font-family="Arial" font-size="12" text-anchor="middle">Noisy signal x(t)</text>
                
                <line x1="560" y1="130" x2="590" y1="130" stroke="#4a6fa5" stroke-width="3" />
                <text x="670" y="135" font-family="Arial" font-size="12" text-anchor="middle">Smoothed signal s(t)</text>
                
                <!-- Weighted average annotation -->
                <path d="M 470,160 Q 510,190 550,200" fill="none" stroke="#666" stroke-width="1" marker-end="url(#arrow)" />
                <text x="600" y="210" font-family="Arial" font-size="12" text-anchor="middle">Weighted average at time t</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 2: Convolution as a weighted average for smoothing a noisy signal.</figcaption>
        </div>
        
        <p>
            In our example, w needs to be a valid probability density function for the output to be a weighted average. Also, w must be 0 for all negative arguments (we can't look into the future). These constraints are specific to our example though - convolution is more general.
        </p>
        
        <h3>2.2 Discrete Convolution</h3>
        
        <p>
            In practical implementations on computers, time is discretized and sensors provide data at regular intervals (e.g., once per second). When x and w are defined only on integer values of t, we can define the discrete convolution:
        </p>
        
        <div class="formula-box">
            $$s(t) = (x * w)(t) = \sum_{a=-\infty}^{\infty} x(a)w(t-a)$$
        </div>
        
        <p>
            In machine learning applications, we typically work with multidimensional arrays (tensors) of data:
        </p>
        
        <ul>
            <li>The <strong>input</strong> is a multidimensional array of data</li>
            <li>The <strong>kernel</strong> (also called filter) is a multidimensional array of parameters</li>
            <li>The output is sometimes called the <strong>feature map</strong></li>
        </ul>
        
        <p>
            Since we store values at discrete points, we implement the infinite summation as a summation over a finite number of array elements.
        </p>
        
        <h3>2.3 Two-Dimensional Convolution</h3>
        
        <p>
            For image processing, we typically use two-dimensional convolution. If we use a 2D image I as input with a 2D kernel K, the convolution operation is:
        </p>
        
        <div class="formula-box">
            $$S(i,j) = (I * K)(i,j) = \sum_m \sum_n I(m,n)K(i-m,j-n)$$
        </div>
        
        <p>
            Convolution is commutative, so we can equivalently write:
        </p>
        
        <div class="formula-box">
            $$S(i,j) = (K * I)(i,j) = \sum_m \sum_n I(i-m,j-n)K(m,n)$$
        </div>
        
        <div class="visualization">
            <svg width="750" height="450" viewBox="0 0 750 450">
                <!-- Background -->
                <rect x="0" y="0" width="750" height="450" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="375" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">2D Convolution Operation</text>
                
                <!-- Input Matrix -->
                <g transform="translate(50, 80)">
                    <rect width="200" height="200" fill="#d1e7dd" stroke="#198754" stroke-width="2" rx="5" ry="5" />
                    <text x="100" y="-15" font-family="Arial" font-size="16" text-anchor="middle">Input Matrix</text>
                    
                    <!-- Grid lines -->
                    <line x1="0" y1="50" x2="200" y1="50" stroke="#198754" stroke-width="1" />
                    <line x1="0" y1="100" x2="200" y1="100" stroke="#198754" stroke-width="1" />
                    <line x1="0" y1="150" x2="200" y1="150" stroke="#198754" stroke-width="1" />
                    
                    <line x1="50" y1="0" x2="50" y1="200" stroke="#198754" stroke-width="1" />
                    <line x1="100" y1="0" x2="100" y1="200" stroke="#198754" stroke-width="1" />
                    <line x1="150" y1="0" x2="150" y1="200" stroke="#198754" stroke-width="1" />
                    
                    <!-- Values -->
                    <text x="25" y="25" font-family="Arial" font-size="14" text-anchor="middle">a</text>
                    <text x="75" y="25" font-family="Arial" font-size="14" text-anchor="middle">b</text>
                    <text x="125" y="25" font-family="Arial" font-size="14" text-anchor="middle">c</text>
                    <text x="175" y="25" font-family="Arial" font-size="14" text-anchor="middle">d</text>
                    
                    <text x="25" y="75" font-family="Arial" font-size="14" text-anchor="middle">e</text>
                    <text x="75" y="75" font-family="Arial" font-size="14" text-anchor="middle">f</text>
                    <text x="125" y="75" font-family="Arial" font-size="14" text-anchor="middle">g</text>
                    <text x="175" y="75" font-family="Arial" font-size="14" text-anchor="middle">h</text>
                    
                    <text x="25" y="125" font-family="Arial" font-size="14" text-anchor="middle">i</text>
                    <text x="75" y="125" font-family="Arial" font-size="14" text-anchor="middle">j</text>
                    <text x="125" y="125" font-family="Arial" font-size="14" text-anchor="middle">k</text>
                    <text x="175" y="125" font-family="Arial" font-size="14" text-anchor="middle">l</text>
                    
                    <text x="25" y="175" font-family="Arial" font-size="14" text-anchor="middle">m</text>
                    <text x="75" y="175" font-family="Arial" font-size="14" text-anchor="middle">n</text>
                    <text x="125" y="175" font-family="Arial" font-size="14" text-anchor="middle">o</text>
                    <text x="175" y="175" font-family="Arial" font-size="14" text-anchor="middle">p</text>
                </g>
                
                <!-- Kernel -->
                <g transform="translate(320, 130)">
                    <rect width="100" height="100" fill="#cfe2ff" stroke="#0d6efd" stroke-width="2" rx="5" ry="5" />
                    <text x="50" y="-15" font-family="Arial" font-size="16" text-anchor="middle">Kernel</text>
                    
                    <!-- Grid lines -->
                    <line x1="0" y1="33.33" x2="100" y1="33.33" stroke="#0d6efd" stroke-width="1" />
                    <line x1="0" y1="66.66" x2="100" y1="66.66" stroke="#0d6efd" stroke-width="1" />
                    
                    <line x1="33.33" y1="0" x2="33.33" y1="100" stroke="#0d6efd" stroke-width="1" />
                    <line x1="66.66" y1="0" x2="66.66" y1="100" stroke="#0d6efd" stroke-width="1" />
                    
                    <!-- Values -->
                    <text x="16.67" y="16.67" font-family="Arial" font-size="14" text-anchor="middle">w</text>
                    <text x="50" y="16.67" font-family="Arial" font-size="14" text-anchor="middle">x</text>
                    <text x="83.33" y="16.67" font-family="Arial" font-size="14" text-anchor="middle">y</text>
                    
                    <text x="16.67" y="50" font-family="Arial" font-size="14" text-anchor="middle">z</text>
                    <text x="50" y="50" font-family="Arial" font-size="14" text-anchor="middle">1</text>
                    <text x="83.33" y="50" font-family="Arial" font-size="14" text-anchor="middle">2</text>
                    
                    <text x="16.67" y="83.33" font-family="Arial" font-size="14" text-anchor="middle">3</text>
                    <text x="50" y="83.33" font-family="Arial" font-size="14" text-anchor="middle">4</text>
                    <text x="83.33" y="83.33" font-family="Arial" font-size="14" text-anchor="middle">5</text>
                </g>
                
                <!-- Output Feature Map -->
                <g transform="translate(500, 80)">
                    <rect width="200" height="200" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5" ry="5" />
                    <text x="100" y="-15" font-family="Arial" font-size="16" text-anchor="middle">Output Feature Map</text>
                    
                    <!-- Grid lines -->
                    <line x1="0" y1="66.66" x2="200" y1="66.66" stroke="#ffc107" stroke-width="1" />
                    <line x1="0" y1="133.33" x2="200" y1="133.33" stroke="#ffc107" stroke-width="1" />
                    
                    <line x1="66.66" y1="0" x2="66.66" y1="200" stroke="#ffc107" stroke-width="1" />
                    <line x1="133.33" y1="0" x2="133.33" y1="200" stroke="#ffc107" stroke-width="1" />
                    
                    <!-- Values representing computation -->
                    <text x="33.33" y="33.33" font-family="Arial" font-size="12" text-anchor="middle">aw+bx+ez+f1</text>
                    <text x="100" y="33.33" font-family="Arial" font-size="12" text-anchor="middle">bw+cx+fz+g1</text>
                    <text x="166.66" y="33.33" font-family="Arial" font-size="12" text-anchor="middle">cw+dx+gz+h1</text>
                    
                    <text x="33.33" y="100" font-family="Arial" font-size="12" text-anchor="middle">ew+fx+iz+j1</text>
                    <text x="100" y="100" font-family="Arial" font-size="12" text-anchor="middle">fw+gx+jz+k1</text>
                    <text x="166.66" y="100" font-family="Arial" font-size="12" text-anchor="middle">gw+hx+kz+l1</text>
                    
                    <text x="33.33" y="166.66" font-family="Arial" font-size="12" text-anchor="middle">iw+jx+mz+n1</text>
                    <text x="100" y="166.66" font-family="Arial" font-size="12" text-anchor="middle">jw+kx+nz+o1</text>
                    <text x="166.66" y="166.66" font-family="Arial" font-size="12" text-anchor="middle">kw+lx+oz+p1</text>
                </g>
                
                <!-- Convolution Visualization -->
                <g transform="translate(150, 340)">
                    <rect width="450" height="80" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                    <text x="225" y="25" font-family="Arial" font-size="14" text-anchor="middle">
                        Convolution slides the kernel across the input, computing the element-wise
                    </text>
                    <text x="225" y="45" font-family="Arial" font-size="14" text-anchor="middle">
                        multiplication and sum at each position to create the output feature map.
                    </text>
                </g>
                
                <!-- Arrows -->
                <line x1="250" y1="180" x2="320" y1="180" stroke="#666" stroke-width="1.5" marker-end="url(#arrow2)" />
                <line x1="420" y1="180" x2="500" y1="180" stroke="#666" stroke-width="1.5" marker-end="url(#arrow2)" />
                
                <!-- Movement arrows showing kernel sliding -->
                <path d="M 75,300 C 100,320 150,320 175,300" fill="none" stroke="#e63946" stroke-width="2" marker-end="url(#arrowRed)" />
                <path d="M 175,300 C 200,280 250,280 275,300" fill="none" stroke="#e63946" stroke-width="2" marker-end="url(#arrowRed)" />
                
                <!-- Slide annotation -->
                <text x="175" y="335" font-family="Arial" font-size="14" fill="#e63946" text-anchor="middle">Kernel slides across input</text>
                
                <!-- Arrow markers -->
                <defs>
                    <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                    <marker id="arrowRed" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#e63946" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 3: Two-dimensional convolution operation showing how the kernel is applied to produce the output feature map.</figcaption>
        </div>
        
        <h3>2.4 Convolution vs. Cross-Correlation</h3>
        
        <p>
            In practice, many neural network libraries implement a related function called cross-correlation rather than true convolution. The difference is that cross-correlation doesn't flip the kernel:
        </p>
        
        <div class="formula-box">
            $$S(i,j) = (I * K)(i,j) = \sum_m \sum_n I(i+m,j+n)K(m,n)$$
        </div>
        
        <div class="note-box">
            <p>
                <strong>Important:</strong> Many machine learning libraries implement cross-correlation but call it convolution. This difference is rarely significant in practice because the kernels are learned, and a flipped kernel would just learn the same function as an unflipped one.
            </p>
        </div>
        
        <h3>2.5 Convolution as Matrix Multiplication</h3>
        
        <p>
            Discrete convolution can be viewed as multiplication by a matrix, but with special constraints:
        </p>
        
        <ul>
            <li>For 1D convolution, the matrix is a <strong>Toeplitz matrix</strong> where each row is constrained to be equal to the row above shifted by one element.</li>
            <li>For 2D convolution, the matrix is a <strong>doubly block circulant matrix</strong>.</li>
            <li>Convolution usually corresponds to a <strong>sparse matrix</strong> because the kernel is typically much smaller than the input.</li>
        </ul>
        
        <p>
            The sparseness of the convolution operation is one of its key advantages in neural networks, making it much more computationally efficient than standard matrix multiplication for large inputs.
        </p>
    </section>

    <section id="motivation">
        <h2>3. Motivation for Using Convolution</h2>
        
        <p>
            Convolutional networks leverage three important ideas that help improve machine learning systems:
        </p>
        
        <ol>
            <li><strong>Sparse interactions</strong> (or sparse connectivity)</li>
            <li><strong>Parameter sharing</strong></li>
            <li><strong>Equivariant representations</strong></li>
        </ol>
        
        <p>
            Additionally, convolution provides a means for working with inputs of variable size. Let's explore each of these concepts in detail.
        </p>
        
        <h3>3.1 Sparse Interactions</h3>
        
        <p>
            Traditional neural network layers use matrix multiplication where every output unit interacts with every input unit. This creates a dense connectivity pattern that can be inefficient.
        </p>
        
        <p>
            In contrast, convolutional networks have <strong>sparse interactions</strong>. This is achieved by making the kernel smaller than the input. For example, when processing an image with millions of pixels, we can detect meaningful features like edges with kernels that have just tens or hundreds of pixels.
        </p>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="400" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Sparse vs. Dense Connectivity</text>
                
                <!-- Part 1: Sparse Connectivity (Convolution) -->
                <text x="175" y="60" font-family="Arial" font-size="16" text-anchor="middle" fill="#166088">Convolutional Layer (Sparse)</text>
                
                <!-- Input units -->
                <circle cx="50" cy="100" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="50" y="105" font-family="Arial" font-size="12" text-anchor="middle">x₁</text>
                
                <circle cx="50" cy="150" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="50" y="155" font-family="Arial" font-size="12" text-anchor="middle">x₂</text>
                
                <circle cx="50" cy="200" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="50" y="205" font-family="Arial" font-size="12" text-anchor="middle">x₃</text>
                
                <circle cx="50" cy="250" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="50" y="255" font-family="Arial" font-size="12" text-anchor="middle">x₄</text>
                
                <circle cx="50" cy="300" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="50" y="305" font-family="Arial" font-size="12" text-anchor="middle">x₅</text>
                
                <!-- Output units -->
                <circle cx="300" cy="100" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="105" font-family="Arial" font-size="12" text-anchor="middle">s₁</text>
                
                <circle cx="300" cy="150" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="155" font-family="Arial" font-size="12" text-anchor="middle">s₂</text>
                
                <circle cx="300" cy="200" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="205" font-family="Arial" font-size="12" text-anchor="middle">s₃</text>
                
                <circle cx="300" cy="250" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="255" font-family="Arial" font-size="12" text-anchor="middle">s₄</text>
                
                <circle cx="300" cy="300" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="305" font-family="Arial" font-size="12" text-anchor="middle">s₅</text>
                
                <!-- Connections for x₃ (highlighted) -->
                <line x1="65" y1="200" x2="285" y1="150" stroke="#e63946" stroke-width="2" />
                <line x1="65" y1="200" x2="285" y1="200" stroke="#e63946" stroke-width="2" />
                <line x1="65" y1="200" x2="285" y1="250" stroke="#e63946" stroke-width="2" />
                
                <!-- Highlight x₃ -->
                <circle cx="50" cy="200" r="20" fill="none" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- Highlight affected outputs -->
                <circle cx="300" cy="150" r="20" fill="none" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                <circle cx="300" cy="200" r="20" fill="none" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                <circle cx="300" cy="250" r="20" fill="none" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- Other connections (non-highlighted) -->
                <line x1="65" y1="100" x2="285" y1="100" stroke="#adb5bd" stroke-width="1" />
                <line x1="65" y1="100" x2="285" y1="150" stroke="#adb5bd" stroke-width="1" />
                
                <line x1="65" y1="150" x2="285" y1="100" stroke="#adb5bd" stroke-width="1" />
                <line x1="65" y1="150" x2="285" y1="150" stroke="#adb5bd" stroke-width="1" />
                <line x1="65" y1="150" x2="285" y1="200" stroke="#adb5bd" stroke-width="1" />
                
                <line x1="65" y1="250" x2="285" y1="200" stroke="#adb5bd" stroke-width="1" />
                <line x1="65" y1="250" x2="285" y1="250" stroke="#adb5bd" stroke-width="1" />
                <line x1="65" y1="250" x2="285" y1="300" stroke="#adb5bd" stroke-width="1" />
                
                <line x1="65" y1="300" x2="285" y1="250" stroke="#adb5bd" stroke-width="1" />
                <line x1="65" y1="300" x2="285" y1="300" stroke="#adb5bd" stroke-width="1" />
                
                <!-- Part 2: Dense Connectivity (Traditional Neural Network) -->
                <text x="525" y="60" font-family="Arial" font-size="16" text-anchor="middle" fill="#166088">Traditional Layer (Dense)</text>
                
                <!-- Input units -->
                <circle cx="400" cy="100" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="400" y="105" font-family="Arial" font-size="12" text-anchor="middle">x₁</text>
                
                <circle cx="400" cy="150" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="400" y="155" font-family="Arial" font-size="12" text-anchor="middle">x₂</text>
                
                <circle cx="400" cy="200" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="400" y="205" font-family="Arial" font-size="12" text-anchor="middle">x₃</text>
                
                <circle cx="400" cy="250" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="400" y="255" font-family="Arial" font-size="12" text-anchor="middle">x₄</text>
                
                <circle cx="400" cy="300" r="15" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="400" y="305" font-family="Arial" font-size="12" text-anchor="middle">x₅</text>
                
                <!-- Output units -->
                <circle cx="650" cy="100" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="650" y="105" font-family="Arial" font-size="12" text-anchor="middle">s₁</text>
                
                <circle cx="650" cy="150" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="650" y="155" font-family="Arial" font-size="12" text-anchor="middle">s₂</text>
                
                <circle cx="650" cy="200" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="650" y="205" font-family="Arial" font-size="12" text-anchor="middle">s₃</text>
                
                <circle cx="650" cy="250" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="650" y="255" font-family="Arial" font-size="12" text-anchor="middle">s₄</text>
                
                <circle cx="650" cy="300" r="15" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="650" y="305" font-family="Arial" font-size="12" text-anchor="middle">s₅</text>
                
                <!-- Single parameter connection (to highlight no sharing) -->
                <line x1="415" y1="200" x2="635" y1="200" stroke="#e63946" stroke-width="2" />
                
                <!-- Weight label -->
                <rect x="525" y="190" width="30" height="20" fill="white" stroke="#e63946" stroke-width="1" />
                <text x="540" y="205" font-family="Arial" font-size="12" text-anchor="middle" fill="#e63946">w₁₃</text>
                
                <!-- Other connections with unique weights (faded) -->
                <line x1="415" y1="100" x2="635" y1="100" stroke="#adb5bd" stroke-width="1" />
                <text x="525" y="90" font-family="Arial" font-size="10" text-anchor="middle" fill="#adb5bd">w₁₁</text>
                
                <line x1="415" y1="150" x2="635" y1="150" stroke="#adb5bd" stroke-width="1" />
                <text x="525" y="140" font-family="Arial" font-size="10" text-anchor="middle" fill="#adb5bd">w₁₂</text>
                
                <line x1="415" y1="250" x2="635" y1="250" stroke="#adb5bd" stroke-width="1" />
                <text x="525" y="240" font-family="Arial" font-size="10" text-anchor="middle" fill="#adb5bd">w₁₄</text>
                
                <line x1="415" y1="300" x2="635" y1="300" stroke="#adb5bd" stroke-width="1" />
                <text x="525" y="290" font-family="Arial" font-size="10" text-anchor="middle" fill="#adb5bd">w₁₅</text>
                
                <!-- Annotation -->
                <text x="175" y="350" font-family="Arial" font-size="14" text-anchor="middle">In convolution, the same parameters</text>
                <text x="175" y="370" font-family="Arial" font-size="14" text-anchor="middle">are reused across different locations</text>
                
                <text x="525" y="350" font-family="Arial" font-size="14" text-anchor="middle">In fully connected layers, each connection</text>
                <text x="525" y="370" font-family="Arial" font-size="14" text-anchor="middle">has its own unique parameter</text>
                
                <!-- Bottom explanation -->
                <rect x="175" y="400" width="350" height="30" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="350" y="420" font-family="Arial" font-size="14" text-anchor="middle">
                    Parameter sharing dramatically reduces the number of parameters to learn
                </text>
            </svg>
            <figcaption>Figure 5: Parameter sharing in convolutional networks. On the left, the same weight (w₁) is reused for multiple connections. On the right, each connection in a fully connected network has its own unique weight.</figcaption>
        </div>
        
        <div class="concept-box">
            <p>
                <strong>Benefits of parameter sharing:</strong>
            </p>
            <ul>
                <li>Dramatically reduces the number of parameters that need to be stored</li>
                <li>Improves statistical efficiency of the model</li>
                <li>Keeps the memory requirements of convolution at O(k) parameters (where k is the kernel size)</li>
                <li>Enables the network to learn translation-invariant features</li>
            </ul>
        </div>
        
        <div class="example-box">
            <p>
                <strong>Example:</strong> Figure 9.6 in the original text shows how edge detection can be performed with just a 2-element convolution kernel, requiring only 267,960 operations. Describing the same transformation with a matrix multiplication would require a matrix with over 8 billion entries, making convolution approximately 4 billion times more efficient for representing this transformation.
            </p>
        </div>
        
        <h3>3.3 Equivariance to Translation</h3>
        
        <p>
            The parameter sharing in convolutional networks gives the layer an important property called <strong>equivariance to translation</strong>.
        </p>
        
        <p>
            A function is equivariant to a transformation if when the input changes by that transformation, the output changes in the same way. Mathematically, a function f(x) is equivariant to a function g if f(g(x)) = g(f(x)).
        </p>
        
        <p>
            In the case of convolution, if we translate the input, the output will be translated in exactly the same way. This is valuable for processing images where the same features (like edges) can appear at different locations.
        </p>
        
        <div class="visualization">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="300" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Translation Equivariance</text>
                
                <!-- Original Input Image (schematic) -->
                <rect x="50" y="70" width="100" height="100" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="100" y="60" font-family="Arial" font-size="14" text-anchor="middle">Input Image</text>
                
                <!-- Original Image Content (simplified cat) -->
                <circle cx="85" cy="95" r="10" fill="#198754" /> <!-- eye -->
                <circle cx="115" cy="95" r="10" fill="#198754" /> <!-- eye -->
                <path d="M 75,125 Q 100,145 125,125" fill="none" stroke="#198754" stroke-width="3" /> <!-- mouth -->
                <path d="M 75,85 L 60,65" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 75,95 L 55,95" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 75,105 L 60,125" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 125,85 L 140,65" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 125,95 L 145,95" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 125,105 L 140,125" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                
                <!-- Original Output Feature Map -->
                <rect x="250" y="70" width="100" height="100" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="60" font-family="Arial" font-size="14" text-anchor="middle">Feature Map</text>
                
                <!-- Simplified feature map showing edge detection -->
                <circle cx="285" cy="95" r="8" fill="#ffc107" /> <!-- eye outline -->
                <circle cx="315" cy="95" r="8" fill="#ffc107" /> <!-- eye outline -->
                <path d="M 275,125 Q 300,145 325,125" fill="none" stroke="#ffc107" stroke-width="2" /> <!-- mouth outline -->
                
                <!-- Translated Input Image (schematic) -->
                <rect x="50" y="200" width="100" height="100" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="100" y="190" font-family="Arial" font-size="14" text-anchor="middle">Translated Input</text>
                
                <!-- Translated Image Content (simplified cat shifted right) -->
                <circle cx="115" cy="225" r="10" fill="#198754" /> <!-- eye -->
                <circle cx="145" cy="225" r="10" fill="#198754" /> <!-- eye -->
                <path d="M 105,255 Q 130,275 155,255" fill="none" stroke="#198754" stroke-width="3" /> <!-- mouth -->
                <path d="M 105,215 L 90,195" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 105,225 L 85,225" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 105,235 L 90,255" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 155,215 L 170,195" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 155,225 L 175,225" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                <path d="M 155,235 L 170,255" fill="none" stroke="#198754" stroke-width="2" /> <!-- whisker -->
                
                <!-- Translated Output Feature Map -->
                <rect x="250" y="200" width="100" height="100" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="300" y="190" font-family="Arial" font-size="14" text-anchor="middle">Translated Features</text>
                
                <!-- Simplified feature map showing edge detection (shifted) -->
                <circle cx="315" cy="225" r="8" fill="#ffc107" /> <!-- eye outline -->
                <circle cx="345" cy="225" r="8" fill="#ffc107" /> <!-- eye outline -->
                <path d="M 305,255 Q 330,275 355,255" fill="none" stroke="#ffc107" stroke-width="2" /> <!-- mouth outline -->
                
                <!-- Convolution Operation -->
                <line x1="160" y1="120" x2="240" y1="120" stroke="#666" stroke-width="1.5" marker-end="url(#arrowEq)" />
                <text x="200" y="110" font-family="Arial" font-size="12" text-anchor="middle">Convolution</text>
                
                <line x1="160" y1="250" x2="240" y1="250" stroke="#666" stroke-width="1.5" marker-end="url(#arrowEq)" />
                <text x="200" y="240" font-family="Arial" font-size="12" text-anchor="middle">Convolution</text>
                
                <!-- Translation Arrows -->
                <line x1="100" y1="180" x2="100" y1="190" stroke="#e63946" stroke-width="2" marker-end="url(#arrowTrans)" />
                <text x="125" y="185" font-family="Arial" font-size="12" text-anchor="middle" fill="#e63946">Translate</text>
                
                <line x1="300" y1="180" x2="300" y1="190" stroke="#e63946" stroke-width="2" marker-end="url(#arrowTrans)" />
                <text x="325" y="185" font-family="Arial" font-size="12" text-anchor="middle" fill="#e63946">Translate</text>
                
                <!-- Equivariance Explanation -->
                <rect x="400" y="125" width="250" height="100" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="525" y="145" font-family="Arial" font-size="14" text-anchor="middle">
                    <tspan x="525" dy="0">Equivariance to Translation:</tspan>
                    <tspan x="525" dy="20">If we translate the input image,</tspan>
                    <tspan x="525" dy="20">the feature map will be translated</tspan>
                    <tspan x="525" dy="20">in exactly the same way.</tspan>
                </text>
                
                <!-- Arrow markers -->
                <defs>
                    <marker id="arrowEq" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                    <marker id="arrowTrans" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#e63946" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 6: Demonstration of translation equivariance in convolutional networks. If the input is translated, the output feature maps are translated in the same way.</figcaption>
        </div>
        
        <div class="note-box">
            <p>
                <strong>Note:</strong> While convolution is naturally equivariant to translation, it is not equivariant to other transformations like scaling or rotation. Other mechanisms are needed to handle these kinds of transformations.
            </p>
        </div>
        
        <h3>3.4 Handling Variable-Sized Inputs</h3>
        
        <p>
            Convolution also provides a natural way to process inputs of varying sizes. Unlike fully connected layers that require a fixed input size, convolutional layers can be applied to inputs of any size, producing outputs of corresponding sizes.
        </p>
        
        <p>
            This is particularly valuable for tasks like image segmentation, where the network needs to label each pixel in an image, regardless of the image's dimensions.
        </p>
    </section>

    <section id="pooling">
        <h2>4. Pooling</h2>
        
        <p>
            A typical layer of a convolutional network consists of three stages:
        </p>
        
        <ol>
            <li><strong>Convolution Stage:</strong> The layer performs several convolutions in parallel to produce a set of linear activations.</li>
            <li><strong>Detector Stage:</strong> Each linear activation is run through a nonlinear activation function (e.g., ReLU).</li>
            <li><strong>Pooling Stage:</strong> A pooling function further modifies the output of the layer.</li>
        </ol>
        
        <p>
            This section focuses on the pooling stage, which is a critical component in most convolutional neural networks.
        </p>
        
        <h3>4.1 What is Pooling?</h3>
        
        <p>
            Pooling replaces the output of a neural network at a certain location with a summary statistic of nearby outputs. Common pooling operations include:
        </p>
        
        <ul>
            <li><strong>Max pooling:</strong> Reports the maximum output within a rectangular neighborhood</li>
            <li><strong>Average pooling:</strong> Takes the average of values in a rectangular neighborhood</li>
            <li><strong>L2 pooling:</strong> Takes the square root of the sum of squares of values (L2 norm) in a neighborhood</li>
            <li><strong>Weighted average pooling:</strong> Takes a weighted average based on distance from the central pixel</li>
        </ul>
        
        <div class="visualization">
            <svg width="700" height="350" viewBox="0 0 700 350">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="350" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Pooling Operations</text>
                
                <!-- Convolution Stage Results -->
                <rect x="40" y="70" width="180" height="40" fill="#cfe2ff" stroke="#0d6efd" stroke-width="2" />
                <text x="130" y="60" font-family="Arial" font-size="14" text-anchor="middle">Convolution Output</text>
                
                <!-- Values in Convolution Output -->
                <rect x="40" y="70" width="36" height="40" fill="#cfe2ff" stroke="#0d6efd" stroke-width="1" />
                <text x="58" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="76" y="70" width="36" height="40" fill="#cfe2ff" stroke="#0d6efd" stroke-width="1" />
                <text x="94" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.5</text>
                
                <rect x="112" y="70" width="36" height="40" fill="#cfe2ff" stroke="#0d6efd" stroke-width="1" />
                <text x="130" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="148" y="70" width="36" height="40" fill="#cfe2ff" stroke="#0d6efd" stroke-width="1" />
                <text x="166" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.8</text>
                
                <rect x="184" y="70" width="36" height="40" fill="#cfe2ff" stroke="#0d6efd" stroke-width="1" />
                <text x="202" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <!-- Detector Stage (Non-linearity) -->
                <rect x="40" y="140" width="180" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="130" y="130" font-family="Arial" font-size="14" text-anchor="middle">After ReLU</text>
                
                <!-- Values after ReLU -->
                <rect x="40" y="140" width="36" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="58" y="165" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="76" y="140" width="36" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="94" y="165" font-family="Arial" font-size="14" text-anchor="middle">0.5</text>
                
                <rect x="112" y="140" width="36" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="130" y="165" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="148" y="140" width="36" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="166" y="165" font-family="Arial" font-size="14" text-anchor="middle">0.8</text>
                
                <rect x="184" y="140" width="36" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="202" y="165" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <!-- Max Pooling Operation -->
                <rect x="40" y="240" width="72" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="76" y="230" font-family="Arial" font-size="14" text-anchor="middle">Max Pooling</text>
                
                <!-- Max Pooling Values -->
                <rect x="40" y="240" width="36" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="58" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.5</text>
                
                <rect x="76" y="240" width="36" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="94" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.8</text>
                
                <!-- Average Pooling Operation -->
                <rect x="148" y="240" width="72" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" />
                <text x="184" y="230" font-family="Arial" font-size="14" text-anchor="middle">Avg Pooling</text>
                
                <!-- Average Pooling Values -->
                <rect x="148" y="240" width="36" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="1" />
                <text x="166" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="184" y="240" width="36" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="1" />
                <text x="202" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.4</text>
                
                <!-- Pool window indicator for Max Pooling -->
                <rect x="40" y="140" width="72" height="40" fill="none" stroke="#ffc107" stroke-width="2" stroke-dasharray="5,3" />
                <rect x="112" y="140" width="72" height="40" fill="none" stroke="#ffc107" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- Pool window indicator for Avg Pooling -->
                <rect x="40" y="140" width="72" height="40" fill="none" stroke="#dc3545" stroke-width="2" stroke-dasharray="5,3" opacity="0.3" />
                <rect x="112" y="140" width="72" height="40" fill="none" stroke="#dc3545" stroke-width="2" stroke-dasharray="5,3" opacity="0.3" />
                
                <!-- Arrows and labels -->
                <line x1="130" y1="110" x2="130" y1="130" stroke="#666" stroke-width="1.5" marker-end="url(#arrowPool)" />
                <text x="155" y="120" font-family="Arial" font-size="12" text-anchor="middle">ReLU(x) = max(0, x)</text>
                
                <line x1="76" y1="190" x2="76" y1="230" stroke="#ffc107" stroke-width="1.5" marker-end="url(#arrowPool)" />
                <line x1="184" y1="190" x2="184" y1="230" stroke="#dc3545" stroke-width="1.5" marker-end="url(#arrowPool)" />
                
                <!-- Pooling explanation -->
                <rect x="300" y="70" width="350" height="100" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="475" y="90" font-family="Arial" font-size="14" text-anchor="middle">
                    <tspan x="475" dy="0"><strong>Max Pooling</strong>: Reports the maximum value</tspan>
                    <tspan x="475" dy="20">within each pooling window.</tspan>
                    <tspan x="475" dy="30"><strong>Average Pooling</strong>: Takes the average of values</tspan>
                    <tspan x="475" dy="20">within each pooling window.</tspan>
                </text>
                
                <!-- Window Size and Stride explanation -->
                <rect x="300" y="200" width="350" height="100" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="475" y="220" font-family="Arial" font-size="14" text-anchor="middle">
                    <tspan x="475" dy="0"><strong>Pooling Parameters</strong>:</tspan>
                    <tspan x="475" dy="20">• Window size: Dimensions of the pooling region</tspan>
                    <tspan x="475" dy="20">• Stride: Distance between pooling regions</tspan>
                    <tspan x="475" dy="20">• Padding: How to handle edges of the input</tspan>
                </text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowPool" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 7: Visualization of max pooling and average pooling operations. The pooling window has a size of 2 and a stride of 2.</figcaption>
        </div>
        
        <h3>4.2 Benefits of Pooling</h3>
        
        <div class="concept-box">
            <p>
                <strong>Key benefit:</strong> Pooling helps make the representation approximately invariant to small translations of the input.
            </p>
        </div>
        
        <p>
            Translation invariance means that if we translate the input by a small amount, the values of most of the pooled outputs do not change. This is beneficial when we care more about whether a feature is present than exactly where it is.
        </p>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="400" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Max Pooling and Translation Invariance</text>
                
                <!-- Original Input section -->
                <text x="175" y="60" font-family="Arial" font-size="16" text-anchor="middle" fill="#166088">Original Input</text>
                
                <!-- Detector layer values - original -->
                <rect x="40" y="80" width="270" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="175" y="75" font-family="Arial" font-size="12" text-anchor="middle">Detector Stage Output</text>
                
                <!-- Individual detector values -->
                <rect x="40" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="55" y="105" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <rect x="70" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="85" y="105" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="100" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="115" y="105" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="130" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="145" y="105" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="160" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="175" y="105" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="190" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="205" y="105" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="220" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="235" y="105" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="250" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="265" y="105" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <rect x="280" y="80" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="295" y="105" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <!-- Pooling layer values - original -->
                <rect x="40" y="150" width="270" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="175" y="145" font-family="Arial" font-size="12" text-anchor="middle">Pooling Stage Output (Window Size 3, Stride 1)</text>
                
                <!-- Individual pooling values -->
                <rect x="40" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="55" y="175" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="70" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="85" y="175" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="100" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="115" y="175" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="130" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="145" y="175" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="160" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="175" y="175" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="190" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="205" y="175" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="220" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="235" y="175" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="250" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="265" y="175" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <rect x="280" y="150" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="295" y="175" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <!-- Shifted Input section -->
                <text x="175" y="220" font-family="Arial" font-size="16" text-anchor="middle" fill="#166088">Input Shifted Right by One Position</text>
                
                <!-- Detector layer values - shifted -->
                <rect x="40" y="240" width="270" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="175" y="235" font-family="Arial" font-size="12" text-anchor="middle">Detector Stage Output (Shifted)</text>
                
                <!-- Individual detector values - shifted -->
                <rect x="40" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="55" y="265" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <rect x="70" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="85" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <rect x="100" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="115" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="130" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="145" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="160" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="175" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="190" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="205" y="265" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="220" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="235" y="265" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="250" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="265" y="265" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="280" y="240" width="30" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="295" y="265" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <!-- Pooling layer values - shifted -->
                <rect x="40" y="310" width="270" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="175" y="305" font-family="Arial" font-size="12" text-anchor="middle">Pooling Stage Output (Shifted)</text>
                
                <!-- Individual pooling values - shifted -->
                <rect x="40" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="55" y="335" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <rect x="70" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="85" y="335" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="100" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="115" y="335" font-family="Arial" font-size="14" text-anchor="middle">0.3</text>
                
                <rect x="130" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="145" y="335" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="160" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="175" y="335" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="190" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="205" y="335" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="220" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="235" y="335" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="250" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="265" y="335" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="280" y="310" width="30" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="295" y="335" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <!-- Highlighting changes -->
                <rect x="40" y="240" width="270" height="40" fill="none" stroke="#e63946" stroke-width="2" stroke-dasharray="5,3" />
                <text x="330" y="260" font-family="Arial" font-size="14" fill="#e63946" text-anchor="start">Every value has changed</text>
                
                <!-- Highlighting unchanged pooling values -->
                <rect x="100" y="310" width="150" height="40" fill="none" stroke="#198754" stroke-width="2" stroke-dasharray="5,3" />
                <text x="330" y="330" font-family="Arial" font-size="14" fill="#198754" text-anchor="start">Half of the values remain unchanged</text>
                
                <!-- Explanation -->
                <rect x="400" y="80" width="250" height="140" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="525" y="100" font-family="Arial" font-size="14" text-anchor="middle">
                    <tspan x="525" dy="0"><strong>Translation Invariance:</strong></tspan>
                    <tspan x="525" dy="25">When the input is shifted, all values</tspan>
                    <tspan x="525" dy="20">in the detector layer change.</tspan>
                    <tspan x="525" dy="25">However, many of the pooling outputs</tspan>
                    <tspan x="525" dy="20">remain the same because max pooling</tspan>
                    <tspan x="525" dy="20">only cares about the maximum value</tspan>
                    <tspan x="525" dy="20">in each neighborhood, not its location.</tspan>
                </text>
            </svg>
            <figcaption>Figure 8: Max pooling introduces translation invariance. When the input is shifted right by one position (bottom), all detector values change, but many pooling values remain the same.</figcaption>
        </div>
        
        <p>
            This property is useful in many applications:
        </p>
        
        <ul>
            <li>When determining whether an image contains a face, we don't need to know the exact pixel locations of the eyes—we just need to know that there's an eye on the left side and an eye on the right side.</li>
            <li>In object recognition, small shifts in position shouldn't change the classification result.</li>
        </ul>
        
        <div class="note-box">
            <p>
                <strong>Note:</strong> In some cases, preserving the precise location of features is important (e.g., for finding corners defined by edges meeting at specific orientations). In such cases, pooling might be less desirable or might require careful design.
            </p>
        </div>
        
        <h3>4.3 Pooling with Downsampling</h3>
        
        <p>
            Because pooling summarizes responses over a neighborhood, we can use fewer pooling units than detector units. This is achieved by spacing the pooling regions apart, typically using a stride of k pixels between pooling regions (instead of 1 pixel).
        </p>
        
        <div class="visualization">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="300" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Pooling with Downsampling</text>
                
                <!-- Detector Stage -->
                <rect x="50" y="70" width="400" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="250" y="60" font-family="Arial" font-size="14" text-anchor="middle">Detector Layer Output</text>
                
                <!-- Individual detector values -->
                <rect x="50" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="70" y="95" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="90" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="110" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="130" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="150" y="95" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="170" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="190" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <rect x="210" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="230" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <rect x="250" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="270" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="290" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="310" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="330" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="350" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.0</text>
                
                <rect x="370" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="390" y="95" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <rect x="410" y="70" width="40" height="40" fill="#d1e7dd" stroke="#198754" stroke-width="1" />
                <text x="430" y="95" font-family="Arial" font-size="14" text-anchor="middle">...</text>
                
                <!-- Pooling Stage -->
                <rect x="50" y="170" width="210" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="250" y="160" font-family="Arial" font-size="14" text-anchor="middle">Pooling Layer Output (Pool Width = 3, Stride = 2)</text>
                
                <!-- Individual pooling values -->
                <rect x="50" y="170" width="60" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="80" y="195" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="110" y="170" width="60" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="140" y="195" font-family="Arial" font-size="14" text-anchor="middle">1.0</text>
                
                <rect x="170" y="170" width="60" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="200" y="195" font-family="Arial" font-size="14" text-anchor="middle">0.2</text>
                
                <rect x="230" y="170" width="60" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" />
                <text x="260" y="195" font-family="Arial" font-size="14" text-anchor="middle">0.1</text>
                
                <!-- Pooling regions -->
                <rect x="50" y="70" width="120" height="40" fill="none" stroke="#ffc107" stroke-width="2" stroke-dasharray="5,3" />
                <rect x="130" y="70" width="120" height="40" fill="none" stroke="#ffc107" stroke-width="2" stroke-dasharray="5,3" />
                <rect x="210" y="70" width="120" height="40" fill="none" stroke="#ffc107" stroke-width="2" stroke-dasharray="5,3" />
                <rect x="290" y="70" width="120" height="40" fill="none" stroke="#ffc107" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- Arrows showing mapping -->
                <line x1="110" y1="120" x2="80" y1="160" stroke="#666" stroke-width="1.5" marker-end="url(#arrowDown)" />
                <line x1="190" y1="120" x2="140" y1="160" stroke="#666" stroke-width="1.5" marker-end="url(#arrowDown)" />
                <line x1="270" y1="120" x2="200" y1="160" stroke="#666" stroke-width="1.5" marker-end="url(#arrowDown)" />
                <line x1="350" y1="120" x2="260" y1="160" stroke="#666" stroke-width="1.5" marker-end="url(#arrowDown)" />
                
                <!-- Explanation -->
                <rect x="480" y="90" width="200" height="120" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="580" y="110" font-family="Arial" font-size="14" text-anchor="middle">
                    <tspan x="580" dy="0"><strong>Downsampling:</strong></tspan>
                    <tspan x="580" dy="25">By using a stride > 1, the</tspan>
                    <tspan x="580" dy="20">output size is reduced by</tspan>
                    <tspan x="580" dy="20">a factor of the stride.</tspan>
                    <tspan x="580" dy="25">This reduces computation and</tspan>
                    <tspan x="580" dy="20">memory requirements.</tspan>
                </text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowDown" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 9: Pooling with downsampling. Using a stride of 2 reduces the representation size by a factor of 2, which reduces computational and statistical burden on the next layer.</figcaption>
        </div>
        
        <div class="concept-box">
            <p>
                <strong>Benefits of downsampling:</strong>
            </p>
            <ul>
                <li>Reduces computational load (the next layer has fewer inputs to process)</li>
                <li>Improves statistical efficiency when the next layer has parameters that depend on input size</li>
                <li>Reduces memory requirements for storing parameters</li>
                <li>Enables handling inputs of varying size by controlling the size of the pooling output</li>
            </ul>
        </div>
        
        <h3>4.4 Handling Variable-Sized Inputs with Pooling</h3>
        
        <p>
            For many tasks, pooling is essential for handling inputs of varying size. For example, when classifying images of different sizes, the input to the classification layer must have a fixed size.
        </p>
        
        <p>
            This is typically accomplished by varying the size of the offset between pooling regions so that the classification layer always receives the same number of summary statistics regardless of the input size. For example, the final pooling layer might be defined to output four sets of summary statistics, one for each quadrant of an image, regardless of the image's dimensions.
        </p>
    </section>

    <section id="conclusion">
        <h2>5. Conclusion</h2>
        
        <p>
            Convolutional neural networks (CNNs) have revolutionized the field of computer vision and have become an essential tool for many machine learning tasks that involve grid-structured data.
        </p>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="400" fill="#f9f9f9" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="350" y="30" font-family="Arial" font-size="18" text-anchor="middle" fill="#166088">Convolutional Network Architecture</text>
                
                <!-- Input layer -->
                <rect x="50" y="80" width="100" height="100" fill="#d1e7dd" stroke="#198754" stroke-width="2" />
                <text x="100" y="70" font-family="Arial" font-size="14" text-anchor="middle">Input Image</text>
                
                <!-- First convolution layer -->
                <rect x="200" y="80" width="80" height="80" fill="#cfe2ff" stroke="#0d6efd" stroke-width="2" />
                <text x="240" y="70" font-family="Arial" font-size="14" text-anchor="middle">Convolution</text>
                
                <!-- First pooling layer -->
                <rect x="330" y="80" width="60" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="360" y="70" font-family="Arial" font-size="14" text-anchor="middle">Pooling</text>
                
                <!-- Second convolution layer -->
                <rect x="440" y="80" width="50" height="50" fill="#cfe2ff" stroke="#0d6efd" stroke-width="2" />
                <text x="465" y="70" font-family="Arial" font-size="14" text-anchor="middle">Convolution</text>
                
                <!-- Second pooling layer -->
                <rect x="540" y="80" width="30" height="30" fill="#fff3cd" stroke="#ffc107" stroke-width="2" />
                <text x="555" y="70" font-family="Arial" font-size="14" text-anchor="middle">Pooling</text>
                
                <!-- Fully connected layer -->
                <rect x="620" y="80" width="30" height="30" fill="#f8d7da" stroke="#dc3545" stroke-width="2" />
                <text x="635" y="70" font-family="Arial" font-size="14" text-anchor="middle">FC</text>
                
                <!-- Convolution operations -->
                <line x1="150" y1="130" x2="190" y1="120" stroke="#666" stroke-width="1.5" marker-end="url(#arrowConc)" />
                <line x1="280" y1="120" x2="320" y1="110" stroke="#666" stroke-width="1.5" marker-end="url(#arrowConc)" />
                <line x1="390" y1="110" x2="430" y1="105" stroke="#666" stroke-width="1.5" marker-end="url(#arrowConc)" />
                <line x1="490" y1="105" x2="530" y1="95" stroke="#666" stroke-width="1.5" marker-end="url(#arrowConc)" />
                <line x1="570" y1="95" x2="610" y1="95" stroke="#666" stroke-width="1.5" marker-end="url(#arrowConc)" />
                
                <!-- Feature visualization -->
                <rect x="80" y="200" width="40" height="40" fill="white" stroke="#198754" stroke-width="1" />
                <text x="100" y="225" font-family="Arial" font-size="10" text-anchor="middle">Raw Image</text>
                
                <rect x="220" y="200" width="40" height="40" fill="white" stroke="#0d6efd" stroke-width="1" />
                <text x="240" y="225" font-family="Arial" font-size="10" text-anchor="middle">Edge Features</text>
                
                <rect x="340" y="200" width="40" height="40" fill="white" stroke="#ffc107" stroke-width="1" />
                <text x="360" y="225" font-family="Arial" font-size="10" text-anchor="middle">Pooled Edges</text>
                
                <rect x="445" y="200" width="40" height="40" fill="white" stroke="#0d6efd" stroke-width="1" />
                <text x="465" y="225" font-family="Arial" font-size="10" text-anchor="middle">Texture</text>
                
                <rect x="535" y="200" width="40" height="40" fill="white" stroke="#ffc107" stroke-width="1" />
                <text x="555" y="225" font-family="Arial" font-size="10" text-anchor="middle">Shapes</text>
                
                <rect x="615" y="200" width="40" height="40" fill="white" stroke="#dc3545" stroke-width="1" />
                <text x="635" y="225" font-family="Arial" font-size="10" text-anchor="middle">Objects</text>
                
                <!-- Key Concepts -->
                <rect x="50" y="270" width="600" height="100" fill="white" stroke="#666" stroke-width="1" rx="5" ry="5" />
                <text x="350" y="290" font-family="Arial" font-size="16" text-anchor="middle" fill="#166088">Key Concepts of Convolutional Networks:</text>
                
                <text x="70" y="320" font-family="Arial" font-size="14" text-anchor="start">
                    <tspan x="70" dy="0">• Convolution Operation: Applies learned filters across input data</tspan>
                    <tspan x="70" dy="25">• Sparse Interactions: Each output unit connects to a small subset of inputs</tspan>
                    <tspan x="70" dy="25">• Parameter Sharing: Same parameters used across different locations</tspan>
                </text>
                
                <text x="370" y="320" font-family="Arial" font-size="14" text-anchor="start">
                    <tspan x="370" dy="0">• Equivariance: Translation of input produces translation of output</tspan>
                    <tspan x="370" dy="25">• Pooling: Provides translation invariance and downsampling</tspan>
                    <tspan x="370" dy="25">• Hierarchical Learning: From simple features to complex concepts</tspan>
                </text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowConc" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                </defs>
            </svg>
            <figcaption>Figure 10: Overview of a typical convolutional neural network architecture showing the hierarchical feature learning from raw input to high-level features.</figcaption>
        </div>
        
        <p>
            Throughout this tutorial, we've explored the core components and principles that make CNNs so effective:
        </p>
        
        <ul>
            <li><strong>The convolution operation</strong> enables networks to process data with a grid-like topology efficiently by applying learned filters across the input.</li>
            <li><strong>Sparse interactions</strong> dramatically reduce the number of parameters and computations needed, making deep networks feasible.</li>
            <li><strong>Parameter sharing</strong> allows the network to learn a single set of weights applied at different positions, further reducing parameters while enabling translation equivariance.</li>
            <li><strong>Equivariance to translation</strong> means that when the input changes by a translation, the output changes in the same way—a useful property for recognizing patterns regardless of their position.</li>
            <li><strong>Pooling</strong> introduces a degree of translation invariance and reduces the spatial dimensions of the representation, making the network more robust to small shifts and distortions in the input.</li>
        </ul>
        
        <p>
            The hierarchical nature of CNNs allows them to build increasingly abstract representations of the input. Early layers typically detect simple features like edges and textures, while deeper layers combine these to represent more complex structures, textures, and eventually entire objects.
        </p>
        
        <p>
            This powerful architecture has enabled breakthroughs in image classification, object detection, semantic segmentation, and many other domains beyond computer vision where data has a grid-like structure (like speech processing, natural language processing, and time-series analysis).
        </p>
        
        <div class="concept-box">
            <p>
                <strong>Advanced topics:</strong> While this tutorial covers the fundamental concepts of convolutional networks, the field continues to evolve with innovations like:
            </p>
            <ul>
                <li>Depthwise separable convolutions for more efficient processing</li>
                <li>Dilated (atrous) convolutions to increase receptive field without increasing parameters</li>
                <li>Attention mechanisms integrated with convolutional networks</li>
                <li>Group convolutions to enable more efficient model architectures</li>
                <li>Various architectural innovations like ResNets, DenseNets, and EfficientNets</li>
            </ul>
        </div>
        
        <p>
            Understanding the core principles behind convolutional networks provides a solid foundation for exploring these more advanced concepts and applying CNNs to solve complex problems in various domains.
        </p>
    </section>

    <footer>
        <p>&copy; 2023 Convolutional Neural Networks Tutorial</p>
    </footer>
</body>
</html> 