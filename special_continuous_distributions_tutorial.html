<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special Continuous Distributions - Complete Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .definition-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .distribution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .distribution-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .distribution-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .distribution-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .parameter-list {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .memoryless-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .memoryless-title {
            color: #d63031;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Special Continuous Distributions</h1>
            <div class="subtitle">Uniform, Exponential, Normal, and Gamma Distributions</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Uniform Distribution</strong> - Equal probability density over an interval</li>
                <li><strong>Exponential Distribution</strong> - Modeling waiting times and memoryless property</li>
                <li><strong>Normal (Gaussian) Distribution</strong> - The most important distribution in statistics</li>
                <li><strong>Gamma Distribution</strong> - Generalizing exponential and connecting to normal</li>
                <li><strong>Solved Problems</strong> - Practical applications and key relationships</li>
            </ul>
        </div>

        <h2>📏 Uniform Distribution</h2>
        
        <div class="concept-box">
            <p><strong>Intuitive Understanding:</strong> The uniform distribution represents "complete randomness" over an interval. Every subinterval of the same length has the same probability.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "All values in the interval are equally likely."
            </p>
        </div>

        <div class="definition-box">
            <h3>Definition: Uniform Distribution</h3>
            <p>A continuous random variable $X$ is said to have a <strong>Uniform distribution</strong> over the interval $[a,b]$, shown as $X \sim \text{Uniform}(a,b)$, if its PDF is given by:</p>
            <div class="formula-box">
                $$f_X(x) = \begin{cases}
                \frac{1}{b-a} & \text{for } a < x < b \\
                0 & \text{for } x < a \text{ or } x > b
                \end{cases}$$
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Uniform Distribution: PDF and Key Properties
                </text>
                
                <!-- Axes -->
                <line x1="80" y1="280" x2="720" y2="280" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="280" x2="80" y2="60" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- X-axis labels -->
                <text x="150" y="300" text-anchor="middle" font-size="12" fill="#2c3e50">a-1</text>
                <text x="250" y="300" text-anchor="middle" font-size="12" fill="#2c3e50">a</text>
                <text x="550" y="300" text-anchor="middle" font-size="12" fill="#2c3e50">b</text>
                <text x="650" y="300" text-anchor="middle" font-size="12" fill="#2c3e50">b+1</text>
                <text x="400" y="320" text-anchor="middle" font-size="14" fill="#2c3e50">x</text>
                
                <!-- Y-axis labels -->
                <text x="70" y="285" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="70" y="180" text-anchor="end" font-size="12" fill="#2c3e50">1/(b-a)</text>
                <text x="40" y="170" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 40 170)">f_X(x)</text>
                
                <!-- Uniform PDF rectangle -->
                <rect x="250" y="180" width="300" height="100" fill="#3498db" opacity="0.6" stroke="#3498db" stroke-width="2"/>
                
                <!-- Zero regions -->
                <line x1="80" y1="280" x2="250" y2="280" stroke="#e74c3c" stroke-width="3"/>
                <line x1="550" y1="280" x2="720" y2="280" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Vertical lines at boundaries -->
                <line x1="250" y1="180" x2="250" y2="280" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="550" y1="180" x2="550" y2="280" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                
                <!-- PDF value label -->
                <text x="400" y="160" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    f_X(x) = 1/(b-a)
                </text>
                
                <!-- Area annotation -->
                <text x="400" y="230" text-anchor="middle" font-size="12" fill="white" font-weight="bold">
                    Area = (b-a) × 1/(b-a) = 1
                </text>
                
                <!-- Key properties box -->
                <rect x="50" y="60" width="300" height="100" fill="white" stroke="#f39c12" stroke-width="2" rx="5"/>
                <text x="200" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Uniform(a,b) Properties:</text>
                <text x="200" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">Mean: E[X] = (a+b)/2</text>
                <text x="200" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Variance: Var(X) = (b-a)²/12</text>
                <text x="200" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">CDF: F_X(x) = (x-a)/(b-a) for a ≤ x ≤ b</text>
                <text x="200" y="145" text-anchor="middle" font-size="11" fill="#2c3e50">Range: [a, b]</text>
            </svg>
        </div>

        <div class="example-box">
            <h4>Variance Calculation</h4>
            <p>To find the variance of $X \sim \text{Uniform}(a,b)$, we use $\text{Var}(X) = E[X^2] - (EX)^2$:</p>
            <div class="formula-box">
                $$E[X^2] = \int_a^b x^2 \cdot \frac{1}{b-a} dx = \frac{1}{b-a} \left[\frac{x^3}{3}\right]_a^b = \frac{a^2 + ab + b^2}{3}$$
                $$\text{Var}(X) = \frac{a^2 + ab + b^2}{3} - \left(\frac{a+b}{2}\right)^2 = \frac{(b-a)^2}{12}$$
            </div>
        </div>

        <h2>⏰ Exponential Distribution</h2>

        <div class="concept-box">
            <p><strong>Real-World Context:</strong> The exponential distribution models waiting times between events. Think of:</p>
            <ul>
                <li>Time between customer arrivals at a store</li>
                <li>Time until a radioactive particle decays</li>
                <li>Time between phone calls to a call center</li>
                <li>Lifetime of electronic components</li>
            </ul>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "The continuous analog of the geometric distribution"
            </p>
        </div>

        <div class="definition-box">
            <h3>Definition: Exponential Distribution</h3>
            <p>A continuous random variable $X$ is said to have an <strong>exponential distribution</strong> with parameter $\lambda > 0$, shown as $X \sim \text{Exponential}(\lambda)$, if its PDF is given by:</p>
            <div class="formula-box">
                $$f_X(x) = \begin{cases}
                \lambda e^{-\lambda x} & \text{for } x > 0 \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>
            <p>Using the unit step function $u(x)$, we can write: $f_X(x) = \lambda e^{-\lambda x} u(x)$</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Exponential Distribution PDFs for Different λ Values
                </text>

                <!-- Axes -->
                <line x1="80" y1="350" x2="720" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="60" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="150" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                <text x="250" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                <text x="350" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                <text x="450" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">4</text>
                <text x="550" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">5</text>
                <text x="400" y="390" text-anchor="middle" font-size="14" fill="#2c3e50">x</text>

                <!-- Y-axis labels -->
                <text x="70" y="355" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="70" y="280" text-anchor="end" font-size="12" fill="#2c3e50">0.5</text>
                <text x="70" y="210" text-anchor="end" font-size="12" fill="#2c3e50">1.0</text>
                <text x="70" y="140" text-anchor="end" font-size="12" fill="#2c3e50">1.5</text>
                <text x="70" y="70" text-anchor="end" font-size="12" fill="#2c3e50">2.0</text>
                <text x="40" y="210" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 40 210)">f_X(x)</text>

                <!-- Exponential curves -->
                <!-- λ = 0.5 (blue) -->
                <path d="M 80 280 Q 150 250 250 220 Q 350 200 450 185 Q 550 175 650 170 L 720 168"
                      stroke="#3498db" stroke-width="3" fill="none"/>

                <!-- λ = 1 (red) -->
                <path d="M 80 210 Q 120 180 180 150 Q 250 120 350 100 Q 450 85 550 75 L 720 65"
                      stroke="#e74c3c" stroke-width="3" fill="none"/>

                <!-- λ = 2 (green) -->
                <path d="M 80 70 Q 100 90 130 120 Q 180 160 250 200 Q 350 250 450 290 L 720 340"
                      stroke="#2ecc71" stroke-width="3" fill="none"/>

                <!-- Legend -->
                <rect x="550" y="80" width="200" height="120" fill="white" stroke="#2c3e50" stroke-width="1" rx="5"/>
                <text x="650" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Exponential PDFs</text>

                <line x1="570" y1="115" x2="590" y2="115" stroke="#3498db" stroke-width="3"/>
                <text x="600" y="120" font-size="11" fill="#2c3e50">λ = 0.5</text>

                <line x1="570" y1="135" x2="590" y2="135" stroke="#e74c3c" stroke-width="3"/>
                <text x="600" y="140" font-size="11" fill="#2c3e50">λ = 1</text>

                <line x1="570" y1="155" x2="590" y2="155" stroke="#2ecc71" stroke-width="3"/>
                <text x="600" y="160" font-size="11" fill="#2c3e50">λ = 2</text>

                <text x="650" y="180" text-anchor="middle" font-size="10" fill="#2c3e50">Higher λ → faster decay</text>
                <text x="650" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">Lower λ → slower decay</text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Key Properties of Exponential Distribution:</strong></p>
            <div class="formula-box">
                $$\text{CDF: } F_X(x) = (1 - e^{-\lambda x})u(x)$$
                $$\text{Mean: } E[X] = \frac{1}{\lambda}$$
                $$\text{Variance: } \text{Var}(X) = \frac{1}{\lambda^2}$$
            </div>
        </div>

        <div class="example-box">
            <h4>Derivation of Mean and Variance</h4>
            <p><strong>Mean calculation using integration by parts:</strong></p>
            <div class="formula-box">
                $$E[X] = \int_0^{\infty} x \lambda e^{-\lambda x} dx$$
                $$\text{Let } y = \lambda x, \text{ then } x = \frac{y}{\lambda}, dx = \frac{dy}{\lambda}$$
                $$E[X] = \int_0^{\infty} \frac{y}{\lambda} e^{-y} \frac{dy}{\lambda} = \frac{1}{\lambda^2} \int_0^{\infty} y e^{-y} dy = \frac{1}{\lambda^2} \cdot 1! = \frac{1}{\lambda}$$
            </div>

            <p><strong>Variance calculation:</strong></p>
            <div class="formula-box">
                $$E[X^2] = \int_0^{\infty} x^2 \lambda e^{-\lambda x} dx = \frac{1}{\lambda^2} \int_0^{\infty} y^2 e^{-y} dy = \frac{2!}{\lambda^2} = \frac{2}{\lambda^2}$$
                $$\text{Var}(X) = E[X^2] - (E[X])^2 = \frac{2}{\lambda^2} - \frac{1}{\lambda^2} = \frac{1}{\lambda^2}$$
            </div>
        </div>

        <div class="memoryless-box">
            <div class="memoryless-title">🧠 Memoryless Property</div>
            <p>The exponential distribution has a unique property: it "forgets" the past. Mathematically:</p>
            <div class="formula-box">
                $$P(X > x + a | X > a) = P(X > x) \text{ for all } a, x \geq 0$$
            </div>
            <p><strong>Intuitive meaning:</strong> If you've been waiting for time $a$ and nothing has happened, the probability of waiting an additional time $x$ is the same as if you just started waiting.</p>

            <p><strong>Proof:</strong></p>
            <div class="formula-box">
                $$P(X > x + a | X > a) = \frac{P(X > x + a, X > a)}{P(X > a)} = \frac{P(X > x + a)}{P(X > a)}$$
                $$= \frac{e^{-\lambda(x+a)}}{e^{-\lambda a}} = e^{-\lambda x} = P(X > x)$$
            </div>
        </div>

        <div class="concept-box">
            <p><strong>Connection to Geometric Distribution:</strong></p>
            <p>The exponential distribution can be viewed as the continuous limit of the geometric distribution. If we perform Bernoulli trials every $\Delta$ seconds with success probability $p = \lambda \Delta$, and let $\Delta \to 0$, the waiting time converges to $\text{Exponential}(\lambda)$.</p>
        </div>

        <h2>🔔 Normal (Gaussian) Distribution</h2>

        <div class="concept-box">
            <p><strong>Why is the Normal Distribution So Important?</strong></p>
            <p>The normal distribution is the most important probability distribution because of the <strong>Central Limit Theorem (CLT)</strong>. The CLT states that if you add many random variables together, their sum will be approximately normal under certain conditions.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Many real-world phenomena can be modeled as the sum of many small random effects."
            </p>
        </div>

        <div class="definition-box">
            <h3>Standard Normal Distribution</h3>
            <p>A continuous random variable $Z$ is said to be a <strong>standard normal</strong> (standard Gaussian) random variable, shown as $Z \sim N(0,1)$, if its PDF is given by:</p>
            <div class="formula-box">
                $$f_Z(z) = \frac{1}{\sqrt{2\pi}} \exp\left\{-\frac{z^2}{2}\right\}, \quad \text{for all } z \in \mathbb{R}$$
            </div>
            <p>The factor $\frac{1}{\sqrt{2\pi}}$ ensures that the area under the PDF equals 1.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Standard Normal Distribution N(0,1)
                </text>

                <!-- Axes -->
                <line x1="80" y1="320" x2="720" y2="320" stroke="#2c3e50" stroke-width="2"/>
                <line x1="400" y1="320" x2="400" y2="60" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="200" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">-3</text>
                <text x="280" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">-2</text>
                <text x="320" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">-1</text>
                <text x="400" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">0</text>
                <text x="480" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                <text x="520" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                <text x="600" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                <text x="400" y="360" text-anchor="middle" font-size="14" fill="#2c3e50">z</text>

                <!-- Y-axis labels -->
                <text x="390" y="325" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="390" y="280" text-anchor="end" font-size="12" fill="#2c3e50">0.1</text>
                <text x="390" y="240" text-anchor="end" font-size="12" fill="#2c3e50">0.2</text>
                <text x="390" y="200" text-anchor="end" font-size="12" fill="#2c3e50">0.3</text>
                <text x="390" y="160" text-anchor="end" font-size="12" fill="#2c3e50">0.4</text>
                <text x="360" y="190" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 360 190)">f_Z(z)</text>

                <!-- Bell curve -->
                <path d="M 120 318 Q 200 300 280 250 Q 320 200 400 160 Q 480 200 520 250 Q 600 300 680 318"
                      stroke="#3498db" stroke-width="4" fill="none"/>

                <!-- Fill area under curve -->
                <path d="M 120 318 Q 200 300 280 250 Q 320 200 400 160 Q 480 200 520 250 Q 600 300 680 318 L 680 320 L 120 320 Z"
                      fill="#3498db" opacity="0.2"/>

                <!-- Vertical line at mean -->
                <line x1="400" y1="160" x2="400" y2="320" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                <text x="410" y="155" font-size="12" fill="#e74c3c" font-weight="bold">μ = 0</text>

                <!-- Standard deviation markers -->
                <line x1="320" y1="200" x2="320" y2="320" stroke="#f39c12" stroke-width="1" stroke-dasharray="3,3"/>
                <line x1="480" y1="200" x2="480" y2="320" stroke="#f39c12" stroke-width="1" stroke-dasharray="3,3"/>
                <text x="360" y="340" text-anchor="middle" font-size="10" fill="#f39c12">σ = 1</text>

                <!-- Properties box -->
                <rect x="50" y="60" width="250" height="100" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="175" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Standard Normal Properties:</text>
                <text x="175" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">Mean: E[Z] = 0</text>
                <text x="175" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Variance: Var(Z) = 1</text>
                <text x="175" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">Symmetric around origin</text>
                <text x="175" y="145" text-anchor="middle" font-size="11" fill="#2c3e50">Bell-shaped curve</text>
            </svg>
        </div>

        <div class="theorem-box">
            <h3>Properties of Standard Normal Distribution</h3>
            <p>For $Z \sim N(0,1)$:</p>
            <ul>
                <li><strong>Symmetry:</strong> $f_Z(-z) = f_Z(z)$ for all $z$</li>
                <li><strong>Odd moments are zero:</strong> $E[Z^{2k+1}] = 0$ for $k = 0,1,2,\ldots$</li>
                <li><strong>Mean and variance:</strong> $E[Z] = 0$ and $\text{Var}(Z) = 1$</li>
            </ul>

            <p><strong>Proof that $E[Z] = 0$:</strong> Since $z \cdot f_Z(z)$ is an odd function and $\int_{-\infty}^{\infty} |z| f_Z(z) dz < \infty$, we have $E[Z] = 0$.</p>
        </div>

        <div class="definition-box">
            <h3>Standard Normal CDF: The Φ Function</h3>
            <p>The CDF of the standard normal distribution is denoted by $\Phi$:</p>
            <div class="formula-box">
                $$\Phi(x) = P(Z \leq x) = \frac{1}{\sqrt{2\pi}} \int_{-\infty}^x \exp\left\{-\frac{u^2}{2}\right\} du$$
            </div>

            <p><strong>Key Properties of $\Phi$:</strong></p>
            <ul>
                <li>$\lim_{x \to -\infty} \Phi(x) = 0$ and $\lim_{x \to \infty} \Phi(x) = 1$</li>
                <li>$\Phi(0) = \frac{1}{2}$ (symmetry around origin)</li>
                <li>$\Phi(-x) = 1 - \Phi(x)$ for all $x \in \mathbb{R}$ (symmetry property)</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>General Normal Distribution</h3>
            <p>If $Z \sim N(0,1)$ and we define $X = \sigma Z + \mu$ where $\sigma > 0$, then $X \sim N(\mu, \sigma^2)$.</p>

            <div class="formula-box">
                $$\text{PDF: } f_X(x) = \frac{1}{\sigma\sqrt{2\pi}} \exp\left\{-\frac{(x-\mu)^2}{2\sigma^2}\right\}$$
                $$\text{CDF: } F_X(x) = \Phi\left(\frac{x-\mu}{\sigma}\right)$$
                $$\text{Probabilities: } P(a < X \leq b) = \Phi\left(\frac{b-\mu}{\sigma}\right) - \Phi\left(\frac{a-\mu}{\sigma}\right)$$
            </div>
        </div>

        <div class="example-box">
            <h4>Example: Normal Distribution Calculations</h4>
            <p><strong>Problem:</strong> Let $X \sim N(-5, 4)$. Find:</p>
            <ol>
                <li>$P(X < 0)$</li>
                <li>$P(-7 < X < -3)$</li>
                <li>$P(X > -3 | X > -5)$</li>
            </ol>

            <p><strong>Solution:</strong> We have $\mu = -5$ and $\sigma = \sqrt{4} = 2$.</p>

            <p><strong>Part (a):</strong></p>
            <div class="formula-box">
                $$P(X < 0) = \Phi\left(\frac{0-(-5)}{2}\right) = \Phi(2.5) \approx 0.99$$
            </div>

            <p><strong>Part (b):</strong></p>
            <div class="formula-box">
                $$P(-7 < X < -3) = \Phi\left(\frac{-3-(-5)}{2}\right) - \Phi\left(\frac{-7-(-5)}{2}\right)$$
                $$= \Phi(1) - \Phi(-1) = 2\Phi(1) - 1 \approx 0.68$$
            </div>

            <p><strong>Part (c):</strong></p>
            <div class="formula-box">
                $$P(X > -3 | X > -5) = \frac{P(X > -3)}{P(X > -5)} = \frac{1-\Phi(1)}{1-\Phi(0)} = \frac{0.1587}{0.5} \approx 0.32$$
            </div>
        </div>

        <div class="theorem-box">
            <h3>Theorem: Linear Transformation of Normal Variables</h3>
            <p>If $X \sim N(\mu_X, \sigma_X^2)$ and $Y = aX + b$ where $a, b \in \mathbb{R}$, then:</p>
            <div class="formula-box">
                $$Y \sim N(a\mu_X + b, a^2\sigma_X^2)$$
            </div>
            <p><strong>Key insight:</strong> Linear transformations of normal random variables remain normal!</p>
        </div>

        <h2>🎲 Gamma Distribution</h2>

        <div class="concept-box">
            <p><strong>The Gamma Distribution's Importance:</strong></p>
            <p>The gamma distribution is widely used because of its connections to other important distributions:</p>
            <ul>
                <li><strong>Exponential:</strong> $\text{Gamma}(1, \lambda) = \text{Exponential}(\lambda)$</li>
                <li><strong>Chi-squared:</strong> Related to normal distributions</li>
                <li><strong>Sum of exponentials:</strong> Sum of $n$ independent $\text{Exponential}(\lambda)$ variables gives $\text{Gamma}(n, \lambda)$</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>The Gamma Function</h3>
            <p>Before defining the gamma distribution, we need the <strong>gamma function</strong> $\Gamma(\alpha)$:</p>
            <div class="formula-box">
                $$\Gamma(\alpha) = \int_0^{\infty} x^{\alpha-1} e^{-x} dx, \quad \text{for } \alpha > 0$$
            </div>

            <p><strong>Key Properties:</strong></p>
            <ul>
                <li>$\Gamma(\alpha + 1) = \alpha \Gamma(\alpha)$ (recursive property)</li>
                <li>$\Gamma(n) = (n-1)!$ for positive integers $n$</li>
                <li>$\Gamma(1/2) = \sqrt{\pi}$</li>
                <li>$\int_0^{\infty} x^{\alpha-1} e^{-\lambda x} dx = \frac{\Gamma(\alpha)}{\lambda^{\alpha}}$ for $\lambda > 0$</li>
            </ul>
        </div>

        <div class="example-box">
            <h4>Example: Gamma Function Calculations</h4>
            <p><strong>Problem:</strong> Find $\Gamma(7/2)$ and evaluate $\int_0^{\infty} x^6 e^{-5x} dx$.</p>

            <p><strong>Solution:</strong></p>
            <p><strong>Part 1:</strong> Using the recursive property:</p>
            <div class="formula-box">
                $$\Gamma(7/2) = \frac{5}{2} \Gamma(5/2) = \frac{5}{2} \cdot \frac{3}{2} \Gamma(3/2) = \frac{5}{2} \cdot \frac{3}{2} \cdot \frac{1}{2} \Gamma(1/2) = \frac{15}{8}\sqrt{\pi}$$
            </div>

            <p><strong>Part 2:</strong> Using the gamma function property with $\alpha = 7$ and $\lambda = 5$:</p>
            <div class="formula-box">
                $$\int_0^{\infty} x^6 e^{-5x} dx = \frac{\Gamma(7)}{5^7} = \frac{6!}{5^7} = \frac{720}{78125} \approx 0.0092$$
            </div>
        </div>

        <div class="definition-box">
            <h3>Gamma Distribution</h3>
            <p>A continuous random variable $X$ is said to have a <strong>gamma distribution</strong> with parameters $\alpha > 0$ and $\lambda > 0$, shown as $X \sim \text{Gamma}(\alpha, \lambda)$, if its PDF is given by:</p>
            <div class="formula-box">
                $$f_X(x) = \begin{cases}
                \frac{\lambda^{\alpha} x^{\alpha-1} e^{-\lambda x}}{\Gamma(\alpha)} & \text{for } x > 0 \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>

            <p><strong>Parameters:</strong></p>
            <ul>
                <li>$\alpha$ is the <strong>shape parameter</strong></li>
                <li>$\lambda$ is the <strong>rate parameter</strong></li>
            </ul>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Gamma Distribution PDFs for Different Parameters
                </text>

                <!-- Axes -->
                <line x1="80" y1="350" x2="720" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="60" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="150" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                <text x="250" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                <text x="350" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                <text x="450" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">4</text>
                <text x="550" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">5</text>
                <text x="400" y="390" text-anchor="middle" font-size="14" fill="#2c3e50">x</text>

                <!-- Y-axis labels -->
                <text x="70" y="355" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="70" y="280" text-anchor="end" font-size="12" fill="#2c3e50">0.5</text>
                <text x="70" y="210" text-anchor="end" font-size="12" fill="#2c3e50">1.0</text>
                <text x="70" y="140" text-anchor="end" font-size="12" fill="#2c3e50">1.5</text>
                <text x="40" y="210" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 40 210)">f_X(x)</text>

                <!-- Gamma curves -->
                <!-- α = 1, λ = 1 (exponential) -->
                <path d="M 80 210 Q 120 240 180 280 Q 250 310 350 330 Q 450 340 550 345 L 720 350"
                      stroke="#e74c3c" stroke-width="3" fill="none"/>

                <!-- α = 2, λ = 1 -->
                <path d="M 80 350 Q 120 300 180 250 Q 250 220 350 240 Q 450 270 550 300 L 720 340"
                      stroke="#3498db" stroke-width="3" fill="none"/>

                <!-- α = 3, λ = 1 -->
                <path d="M 80 350 Q 120 330 180 280 Q 250 200 350 180 Q 450 200 550 240 L 720 300"
                      stroke="#2ecc71" stroke-width="3" fill="none"/>

                <!-- α = 5, λ = 1 -->
                <path d="M 80 350 Q 150 340 220 300 Q 300 220 400 140 Q 500 160 600 220 L 720 280"
                      stroke="#9b59b6" stroke-width="3" fill="none"/>

                <!-- Legend -->
                <rect x="520" y="80" width="220" height="140" fill="white" stroke="#2c3e50" stroke-width="1" rx="5"/>
                <text x="630" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Gamma(α, λ=1) PDFs</text>

                <line x1="540" y1="115" x2="560" y2="115" stroke="#e74c3c" stroke-width="3"/>
                <text x="570" y="120" font-size="11" fill="#2c3e50">α = 1 (Exponential)</text>

                <line x1="540" y1="135" x2="560" y2="135" stroke="#3498db" stroke-width="3"/>
                <text x="570" y="140" font-size="11" fill="#2c3e50">α = 2</text>

                <line x1="540" y1="155" x2="560" y2="155" stroke="#2ecc71" stroke-width="3"/>
                <text x="570" y="160" font-size="11" fill="#2c3e50">α = 3</text>

                <line x1="540" y1="175" x2="560" y2="175" stroke="#9b59b6" stroke-width="3"/>
                <text x="570" y="180" font-size="11" fill="#2c3e50">α = 5</text>

                <text x="630" y="200" text-anchor="middle" font-size="10" fill="#2c3e50">Higher α → more bell-shaped</text>
                <text x="630" y="215" text-anchor="middle" font-size="10" fill="#2c3e50">α = 1 → exponential decay</text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Key Properties of Gamma Distribution:</strong></p>
            <div class="formula-box">
                $$\text{Mean: } E[X] = \frac{\alpha}{\lambda}$$
                $$\text{Variance: } \text{Var}(X) = \frac{\alpha}{\lambda^2}$$
            </div>

            <p><strong>Special Cases:</strong></p>
            <ul>
                <li>$\text{Gamma}(1, \lambda) = \text{Exponential}(\lambda)$</li>
                <li>Sum of $n$ independent $\text{Exponential}(\lambda)$ variables gives $\text{Gamma}(n, \lambda)$</li>
            </ul>
        </div>

        <div class="example-box">
            <h4>Derivation of Mean and Variance</h4>
            <p><strong>Mean calculation:</strong></p>
            <div class="formula-box">
                $$E[X] = \int_0^{\infty} x \cdot \frac{\lambda^{\alpha} x^{\alpha-1} e^{-\lambda x}}{\Gamma(\alpha)} dx = \frac{\lambda^{\alpha}}{\Gamma(\alpha)} \int_0^{\infty} x^{\alpha} e^{-\lambda x} dx$$
                $$= \frac{\lambda^{\alpha}}{\Gamma(\alpha)} \cdot \frac{\Gamma(\alpha+1)}{\lambda^{\alpha+1}} = \frac{\lambda^{\alpha}}{\Gamma(\alpha)} \cdot \frac{\alpha\Gamma(\alpha)}{\lambda^{\alpha+1}} = \frac{\alpha}{\lambda}$$
            </div>

            <p><strong>Variance calculation:</strong></p>
            <div class="formula-box">
                $$E[X^2] = \frac{\lambda^{\alpha}}{\Gamma(\alpha)} \int_0^{\infty} x^{\alpha+1} e^{-\lambda x} dx = \frac{\alpha(\alpha+1)}{\lambda^2}$$
                $$\text{Var}(X) = E[X^2] - (E[X])^2 = \frac{\alpha(\alpha+1)}{\lambda^2} - \frac{\alpha^2}{\lambda^2} = \frac{\alpha}{\lambda^2}$$
            </div>
        </div>

        <h2>📊 Distribution Summary and Comparison</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Distribution</th>
                    <th>Parameters</th>
                    <th>PDF</th>
                    <th>Mean</th>
                    <th>Variance</th>
                    <th>Key Property</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Uniform(a,b)</strong></td>
                    <td>a, b (endpoints)</td>
                    <td>$\frac{1}{b-a}$ for $a < x < b$</td>
                    <td>$\frac{a+b}{2}$</td>
                    <td>$\frac{(b-a)^2}{12}$</td>
                    <td>Equal probability density</td>
                </tr>
                <tr>
                    <td><strong>Exponential(λ)</strong></td>
                    <td>λ (rate)</td>
                    <td>$\lambda e^{-\lambda x}$ for $x > 0$</td>
                    <td>$\frac{1}{\lambda}$</td>
                    <td>$\frac{1}{\lambda^2}$</td>
                    <td>Memoryless property</td>
                </tr>
                <tr>
                    <td><strong>Normal(μ,σ²)</strong></td>
                    <td>μ (mean), σ² (variance)</td>
                    <td>$\frac{1}{\sigma\sqrt{2\pi}}e^{-\frac{(x-\mu)^2}{2\sigma^2}}$</td>
                    <td>μ</td>
                    <td>σ²</td>
                    <td>Central Limit Theorem</td>
                </tr>
                <tr>
                    <td><strong>Gamma(α,λ)</strong></td>
                    <td>α (shape), λ (rate)</td>
                    <td>$\frac{\lambda^{\alpha} x^{\alpha-1} e^{-\lambda x}}{\Gamma(\alpha)}$ for $x > 0$</td>
                    <td>$\frac{\alpha}{\lambda}$</td>
                    <td>$\frac{\alpha}{\lambda^2}$</td>
                    <td>Generalizes exponential</td>
                </tr>
            </tbody>
        </table>

        <h2>🎓 Key Takeaways</h2>

        <div class="distribution-grid">
            <div class="distribution-card">
                <div class="distribution-title">🎯 Understanding Distributions</div>
                <p>Each distribution models different real-world phenomena:</p>
                <ul>
                    <li><strong>Uniform:</strong> Complete randomness over an interval</li>
                    <li><strong>Exponential:</strong> Waiting times, memoryless events</li>
                    <li><strong>Normal:</strong> Sum of many small effects (CLT)</li>
                    <li><strong>Gamma:</strong> Sum of exponential waiting times</li>
                </ul>
            </div>

            <div class="distribution-card">
                <div class="distribution-title">🔗 Connections</div>
                <p>These distributions are interconnected:</p>
                <ul>
                    <li>Exponential is special case of Gamma</li>
                    <li>Normal emerges from CLT</li>
                    <li>Gamma generalizes exponential</li>
                    <li>All have important limiting relationships</li>
                </ul>
            </div>

            <div class="distribution-card">
                <div class="distribution-title">📈 Practical Applications</div>
                <p>Know when to use each distribution:</p>
                <ul>
                    <li><strong>Uniform:</strong> Random number generation, modeling uncertainty</li>
                    <li><strong>Exponential:</strong> Reliability engineering, queueing theory</li>
                    <li><strong>Normal:</strong> Measurement errors, natural phenomena</li>
                    <li><strong>Gamma:</strong> Waiting times, Bayesian statistics</li>
                </ul>
            </div>

            <div class="distribution-card">
                <div class="distribution-title">🧮 Computational Tips</div>
                <p>Master the key techniques:</p>
                <ul>
                    <li>Use standardization for normal calculations</li>
                    <li>Remember the memoryless property for exponential</li>
                    <li>Use gamma function properties for gamma distribution</li>
                    <li>Leverage symmetry properties when possible</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Study Strategy:</strong></p>
            <ul>
                <li><strong>Focus on intuition:</strong> Understand what each distribution models</li>
                <li><strong>Master the standard normal:</strong> All normal calculations reduce to this</li>
                <li><strong>Practice transformations:</strong> Know how parameters affect shape and scale</li>
                <li><strong>Memorize key properties:</strong> Means, variances, and special characteristics</li>
                <li><strong>Solve problems:</strong> Apply distributions to real-world scenarios</li>
            </ul>
        </div>

    </div>
</body>
</html>
