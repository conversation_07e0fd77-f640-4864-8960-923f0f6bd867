36-705: Intermediate Statistics Fall 2019
Lecture 3: August 30
Lecturer: <PERSON><PERSON>
3.1 Bounded Random Variables - <PERSON><PERSON><PERSON><PERSON>’s bound
We claimed in the previous lecture that many classes of RVs are sub-Gaussian. In this
section, we show this for an important special case: bounded random variables.
Example 1: Let us first consider a simple case, of Rademacher random variables, i.e.
random variables that take the values {+1, −1} equiprobably. In this case we can see that,
E[exp(tX)] = 1
2
[exp(t) + exp(−t)]
=
1
2
"X∞
k=0
(−t)
k
k!
+
X∞
k=0
t
k
k!
#
=
X∞
k=0
t
2k
(2k)! ≤
X∞
k=0
t
2k
2
kk!
= exp(t
2
/2).
This shows that Rademacher random variables are 1-sub Gaussian.
Detour: Jensen’s inequality: <PERSON>’s inequality states that for a convex function
g : R 7→ R we have that,
E[g(X)] ≥ g(E[X]).
If g is concave then the reverse inequality holds.
Proof: Let µ = E[X] and let Lµ(x) = a + bx be the tangent line to the function g at µ,
i.e. we have that Lµ(µ) = g(µ). By convexity we know that g(x) ≥ Lµ(x) for every point x.
Thus we have that,
E[g(X)] ≥ E[Lµ(X)] = E[a + bX]
= a + bµ = Lµ(µ) = g(µ).
Example 2: Bounded Random Variables. Let X be a random variable with zero
mean and with support on some bounded interval [a, b].
3-1
3-2 Lecture 3: August 30
You should convince yourself that the zero mean assumption does not matter in general (you
can always subtract the mean, i.e. define a new random variable Y = X − E[X] and use Y
in the calculation below).
Let X0 denote an independent copy of X then we have that,
EX[exp(tX)] = EX[exp(t(X − E[X
0
])] ≤ EX,X0[exp(t(X − X
0
)],
using Jensen’s inequality, and the convexity of the function g(x) = exp(x).
Now, let  be a Rademacher random variable. Then note that the distribution of X − X0
is
identical to the distribution of X0 − X and more importantly of (X − X0
). So we obtain
that,
EX,X0[exp(t(X − X
0
)] = EX,X0[E
[exp(t(X − X
0
)]]
≤ EX,X0[exp(t
2
(X − X
0
)
2
/2],
where we just use the result from Example 1, with (X, X0
) fixed by conditioning. Now
(X − X0
) using boundedness is at most (b − a) so we obtain that,
EX[exp(tX)] ≤ exp(t
2
(b − a)
2
/2),
which in turn shows that bounded random variables are (b − a)-sub Gaussian.
This in turn yields Hoeffding’s bound. Suppose that, X1, . . . , Xn are independent identically
distribution bounded random variables, with a ≤ Xi ≤ b for all i then,
P
 




1
n
Xn
i=1
Xi − µ





≥ t
!
≤ 2 exp 
−
nt2
2(b − a)
2

.
This is a two-sided exponential tail inequality for the averages of bounded random variables.
With some effort you can derive a slightly tighter bound on the MGF to obtain the stronger
bound that:
P
 




1
n
Xn
i=1
Xi − µ





≥ t
!
≤ 2 exp 
−
2nt2
(b − a)
2

.
3.1.1 A simple generalization
It is worth noting that none of the exponential tail inequalities we proved required the random
variables to be identically distributed. More generally, suppose that we have X1, . . . , Xn
which are each σ1, . . . , σn sub Gaussian. Then using just independence you can verify that
their average µb is σ-sub Gaussian, where,
σ =
1
n
vuutXn
i=1
σ
2
i
Lecture 3: August 30 3-3
This in turn yields the exponential tail inequality,
P
 




1
n
Xn
i=1
(Xi − E[Xi
])





≥ t
!
≤ exp(−t
2
/(2σ
2
)).
Note that the random variables still need to be independent but no longer need to be identically distributed (i.e. they can for instance have different means and sub-Gaussian parameters).
3.2 Other interesting concentration inequalities
The rest of this lecture will be mostly a summary of other useful exponential tail bounds. We
will not prove any of these in lecture, some of them follow similar lines of using Chernoff’s
method in clever ways. I can provide references if you are curious. In particular, we will go
through:
1. Bernstein’s inequality: sharper concentration for bounded random variables
2. McDiarmid’s inequality: Concentration of Lipschitz functions of bounded random variables
3. Levy’s inequality/Tsirelson’s inequality: Concentration of Lipschitz functions of Gaussian random variables
4. χ
2
tail bound
Finally, we will see an application of the χ
2
tail bound in proving the Johnson-Lindenstrauss
lemma.
3.3 Bernstein’s inequality
One nice thing about the Gaussian tail inequality was that it explicitly depended on the variance of the random variable X, i.e. roughly the inequality guaranteed us that the deviation
from the mean was at most σ
p
log(2/δ)/n with probability at least 1 − δ.
On the other hand Hoeffding’s bound depended only on the bounds of the random variable
but not explicitly on the variance of the RVs. The bound b − a, provides a (possibly loose)
upper bound on the standard deviation. One might at least hope that if the random variables
were bounded, and additionally had small variance we might be able to improve Hoeffding’s
bound.
3-4 Lecture 3: August 30
This is indeed the case. Such inequalities are typically known as Bernstein inequalities. As
a concrete example, suppose we had X1, . . . , Xn which were i.i.d from a distribution with
mean µ, bounded support [a, b], with variance E[(X − µ)
2
] = σ
2
. Then,
P(|µb − µ| ≥ t) ≤ 2 exp 
−
nt2
2(σ
2 + (b − a)t)

.
Roughly this inequality says with probability at least 1 − δ,
|µb − µ| ≤ 4σ
r
ln(2/δ)
n
+
4(b − a) ln(2/δ)
n
.
As an exercise work through the above algebra.
Upto some small constants this is never worse than Hoeffding’s bound, which just comes
from using the worst-case upper bound of σ ≤ b − a. When the RVs have small variance, i.e.
σ is small, this bound can be much sharper than Hoeffding’s bound. These are cases where
one has a random variable that occasionally takes large values (so the bounds are not great)
but has much smaller variance.
Intuitively, it captures more of the Chebyshev effect, i.e. that random variables with small
variance should be tightly concentrated around their mean.
3.4 McDiarmid’s inequality
So far we have mostly been focused on the concentration of averages. A natural question is
whether other functions of i.i.d. random variables also show exponential concentration. It
turns out that many other functions do concentrate sharply, and roughly the main property
of the function that we need is that if we change the value of one random variable the
function does not change dramatically.
Formally, we have i.i.d. RVs X1, . . . , Xn, where each Xi ∈ R. We have a function f : R
n
7→ R,
that satisfies the property that:
|f(x1, . . . , xn) − f(x1, . . . , xk−1, x0
k
, xk+1, . . . , xn)| ≤ Lk,
for every x, x0 ∈ R
n
, i.e. the function changes by at most Lk if its k-th co-ordinate is changed.
This is known as the bounded difference condition.
If the random variables X1, . . . , Xn are i.i.d then for all t ≥ 0
P(|f(X1, . . . , Xn) − E[f(X1, . . . , Xn)]| ≥ t) ≤ 2 exp 
−
2t
2
Pn
k=1 L
2
k

.
Lecture 3: August 30 3-5
Example 1: A simple example of this inequality in action is to see that it directly implies
the Hoeffding bound. In this case the function of interest is the average:
f(X1, . . . , Xn) = 1
n
Xn
i=1
Xi
,
and since the random variables are bounded we have that each Lk ≤ (b − a)/n. This in turn
directly yields Hoeffding’s bound (with slightly better constants).
Example 2: A perhaps more interesting example is that of U-statistics. A U-statistic is
defined by a kernel, which is just a function of two random variables, i.e. g : R
2
7→ R. The
U-statistic is then given as:
U(X1, . . . , Xn) := 1

n
2

X
j<k
g(Xj
, Xk).
There are many examples of U-statistics, for instance:
1. Variance: The usual estimator of the sample variance:
σb =
1
n − 1
Xn
i=1
(Xi − µb)
2
,
is the U-statistic that arises from taking g(Xj
, Xk) = 1
2
(Xi − Xj )
2
.
2. Mean absolute deviation: If we take g(Xj
, Xk) = |Xj − Xk|, this leads to a
U-statistic that is an unbiased estimator of the mean absolute deviation E|X1 − X2|.
For bounded U-statistics, i.e. if g(Xi
, Xj ) ≤ b, we can apply McDiarmid’s inequality to
obtain a concentration bound. Note that since each random variable Xi participates in
(n − 1) terms we have that,
|U(X1, . . . , Xn) − U(X1, . . . , X0
i
, . . . , Xn)| ≤ 1

n
2
(n − 1)(2b) = 4b
n
.
So that McDiarmid’s inequality tells us that,
P(|U(X1, . . . , Xn) − E[U(X1, . . . , Xn)]| ≥ t) ≤ 2 exp(−nt2
/(8b
2
)).
3.5 Levy’s inequality
There is a similar concentration inequality that applies to functions of Gaussian random
variables that are sufficiently smooth. In this case, the assumption is quite different.
3-6 Lecture 3: August 30
assume that:
|f(X1, . . . , Xn) − f(Y1, . . . , Yn)| ≤ L
vuutXn
i=1
(Xi − Yi)
2
,
for all X1, . . . , Xn, Y1, . . . , Yn ∈ R.
For such functions we have that if X1, . . . , Xn ∼ N(0, 1) then,
P(|f(X1, . . . , Xn) − E[f(X1, . . . , Xn)]| ≥ t) ≤ 2 exp 
−
t
2
2L2

.
3.6 χ
2
tail bounds
A χ
2
random variable with n degrees of freedom, denoted by Y ∼ χ
2
n
, is a RV that is a sum
of n i.i.d. standard Gaussian RVs, i.e. Y =
Pn
i=1 X2
i where each Xi ∼ N(0, 1). Suppose that
Z1, . . . , Zn ∼ N(0, 1), then the expected value E[Z
2
i
] = 1, and we have the χ
2
tail bound:
P
 




1
n
Xn
k=1
Z
2
k − 1





≥ t
!
≤ 2 exp(−nt2
/8) for all t ∈ (0, 1).
You will derive this in your HW using the Chernoff method. Analogous to the class of subGaussian RVs, χ
2
random variables belong to a class of what are known as sub-exponential
random variables. The main note-worthy difference is that the Gaussian-type behaviour of
the tail only holds for small values of the deviation t.
Detour: The union bound. This is also known as Boole’s inequality. It says that if we
have events A1, . . . , An then
P
 [n
i=1
Ai
!
≤
Xn
i=1
P(Ai).
In particular, if we consider a case when each event Ai
is a failure of some type, then the
above inequality says that the probability that even a single failure occurs is at most the
sum of the probabilities of each failure.
Example: The Johnson-Lindenstrauss Lemma. One very nice application of χ
2
tail
bounds is in the analysis of what are known as “random projections”. Suppose we have a
data set X1, . . . , Xn ∈ R
d where d is quite large. Storing such a dataset might be expensive
and as a result we often resort to “sketching” or “random projection” where the goal is
to create a map F : R
d
7→ R
m, with m  d. We then instead store the mapped dataset
{F(X1), . . . , F(Xn)}. The challenge is to design this map F in a way that preserves essential
Lecture 3: August 30 3-7
features of the original dataset. In particular, we would like that for every pair (Xi
, Xj ) we
have that,
(1 − )kXi − Xjk
2
2 ≤ kF(Xi) − F(Xj )k
2
2 ≤ (1 + )kXi − Xjk
2
2
,
i.e. the map preserves all the pair-wise distances up to a (1 ± ) factor. Of course, if m is
large we might expect this is not too difficult.
The Johnson-Lindenstrauss lemma is quite stunning: it says that a simple randomized construction will produce such a map with probability at least 1 − δ provided that,
m ≥
16 log(n/δ)

2
.
Notice that this is completely independent of the original dimension d and depends on
logarithmically on the number of points n. This map can result in huge savings in storage
cost while still essentially preserving all the pairwise distances.
The map itself is quite simple: we construct a matrix Z ∈ R
m×d
, where each entry of Z is
i.i.d N(0, 1). We then define the map as:
F(Xi) = ZXi
√
m
.
Now let us fix a pair (Xj
, Xk) and consider,
kF(Xj ) − F(Xk)k
2
2
kXj − Xkk
2
2
=








Z(Xj − Xk)
√
mkXj − Xkk2








2
2
=
1
m
Xm
i=1
hZi
,
Xj − Xk
kXj − Xkk2
i
2
| {z }
Ti
.
Now, for some fixed numbers aj the distribution of Pd
j=1 ajZij is Gaussian with mean 0 and
variance Pd
j=1 a
2
j
. So each term Ti
is an independent χ
2
random variable. Now applying the
χ
2
tail bound, we obtain that,
P




kF(Xj ) − F(Xk)k
2
2
kXj − Xkk
2
2
− 1




≥ 

≤ 2 exp(−m2
/8).
Thus for the fixed pair (Xi
, Xj ) the probability that our map fails to preserve the distance
is exponentially small, i.e. is at most 2 exp(−m2/8). Now, to find the probability that our
map fails to preserve any of our
n
2

pairwise distances we simply apply the union bound to
conclude that, the probability of any failure is at most:
P(failure) ≤ 2

n
2

exp(−m2
/8)
3-8 Lecture 3: August 30
Now, it is straightforward to verify that if
m ≥
16 log(n/δ)

2
,
then this probability is at most δ as desired. An important point to note is that the exponential concentration is what leads to such a small value for m (i.e. it only needs to grow
logarithmically with the sample size).