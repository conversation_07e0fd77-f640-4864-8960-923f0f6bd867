<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markov Random Fields and Bayesian Networks - Complete Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .definition-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .comparison-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .card-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .voting-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .voting-title {
            color: #d63031;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .factor-table {
            margin: 15px 0;
            border-collapse: collapse;
            width: 100%;
        }
        
        .factor-table th, .factor-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        .factor-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Markov Random Fields</h1>
            <div class="subtitle">Undirected Graphical Models and Bayesian Networks</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Introduction to Markov Random Fields</strong> - Undirected graphical models and motivation</li>
                <li><strong>Formal Definition of MRFs</strong> - Mathematical framework and cliques</li>
                <li><strong>Comparison with Bayesian Networks</strong> - Advantages and limitations</li>
                <li><strong>Independence in MRFs</strong> - Markov blankets and separation</li>
                <li><strong>Conditional Random Fields (CRFs)</strong> - Structured prediction applications</li>
                <li><strong>Factor Graphs</strong> - Bipartite representation of factors and variables</li>
                <li><strong>Practical Applications</strong> - Real-world examples and use cases</li>
            </ul>
        </div>

        <h2>🌐 Introduction to Markov Random Fields</h2>
        
        <div class="concept-box">
            <p><strong>Why do we need undirected models?</strong></p>
            <p>Bayesian networks are powerful, but they have limitations. Some probability distributions have independence assumptions that cannot be perfectly represented by directed graphs. In such cases, we need a more flexible representation.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Markov Random Fields use undirected graphs to represent dependencies that directed models cannot capture."
            </p>
        </div>

        <div class="voting-box">
            <div class="voting-title">🗳️ Motivating Example: Voting Preferences</div>
            <p>Consider modeling voting preferences among four people: A, B, C, D. Suppose:</p>
            <ul>
                <li>(A,B), (B,C), (C,D), and (D,A) are friends</li>
                <li>Friends tend to have similar voting preferences</li>
                <li>These influences form a cycle that cannot be naturally represented by a directed graph</li>
            </ul>
            
            <p><strong>Factor Definition:</strong> We define compatibility factors $\phi(X,Y)$ for friends:</p>
            <table class="factor-table">
                <tr>
                    <th>X</th>
                    <th>Y</th>
                    <th>φ(X,Y)</th>
                    <th>Interpretation</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>1</td>
                    <td>10</td>
                    <td>Both vote yes (high compatibility)</td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>0</td>
                    <td>5</td>
                    <td>Both vote no (medium compatibility)</td>
                </tr>
                <tr>
                    <td>1</td>
                    <td>0</td>
                    <td>1</td>
                    <td>Disagreement (low compatibility)</td>
                </tr>
                <tr>
                    <td>0</td>
                    <td>1</td>
                    <td>1</td>
                    <td>Disagreement (low compatibility)</td>
                </tr>
            </table>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Voting Example: Undirected vs Directed Models
                </text>
                
                <!-- Left side: Undirected model -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Undirected Model (MRF)
                </text>
                
                <!-- Undirected graph nodes -->
                <circle cx="150" cy="120" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="150" y="125" text-anchor="middle" font-size="14" fill="white" font-weight="bold">A</text>
                
                <circle cx="250" cy="120" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="250" y="125" text-anchor="middle" font-size="14" fill="white" font-weight="bold">B</text>
                
                <circle cx="250" cy="220" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="250" y="225" text-anchor="middle" font-size="14" fill="white" font-weight="bold">C</text>
                
                <circle cx="150" cy="220" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="150" y="225" text-anchor="middle" font-size="14" fill="white" font-weight="bold">D</text>
                
                <!-- Undirected edges -->
                <line x1="175" y1="120" x2="225" y2="120" stroke="#e74c3c" stroke-width="3"/>
                <line x1="250" y1="145" x2="250" y2="195" stroke="#e74c3c" stroke-width="3"/>
                <line x1="225" y1="220" x2="175" y2="220" stroke="#e74c3c" stroke-width="3"/>
                <line x1="150" y1="195" x2="150" y2="145" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Factor labels -->
                <text x="200" y="110" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">φ(A,B)</text>
                <text x="260" y="170" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">φ(B,C)</text>
                <text x="200" y="235" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">φ(C,D)</text>
                <text x="140" y="170" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">φ(D,A)</text>
                
                <!-- Right side: Directed attempts -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Directed Models (Inadequate)
                </text>
                
                <!-- Attempt 1: Chain -->
                <text x="500" y="90" text-anchor="middle" font-size="12" fill="#2c3e50">Chain Structure:</text>
                <circle cx="480" cy="110" r="15" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                <text x="480" y="115" text-anchor="middle" font-size="10" fill="white" font-weight="bold">A</text>
                
                <circle cx="520" cy="110" r="15" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                <text x="520" y="115" text-anchor="middle" font-size="10" fill="white" font-weight="bold">B</text>
                
                <circle cx="560" cy="110" r="15" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                <text x="560" y="115" text-anchor="middle" font-size="10" fill="white" font-weight="bold">C</text>
                
                <circle cx="600" cy="110" r="15" fill="#f39c12" stroke="#2c3e50" stroke-width="1"/>
                <text x="600" y="115" text-anchor="middle" font-size="10" fill="white" font-weight="bold">D</text>
                
                <!-- Chain arrows -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                <line x1="495" y1="110" x2="505" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="535" y1="110" x2="545" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="575" y1="110" x2="585" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <text x="540" y="135" text-anchor="middle" font-size="9" fill="#dc3545">Missing: A↔D dependency</text>
                
                <!-- Attempt 2: Star -->
                <text x="500" y="170" text-anchor="middle" font-size="12" fill="#2c3e50">Star Structure:</text>
                <circle cx="540" cy="190" r="15" fill="#9b59b6" stroke="#2c3e50" stroke-width="1"/>
                <text x="540" y="195" text-anchor="middle" font-size="10" fill="white" font-weight="bold">A</text>
                
                <circle cx="520" cy="210" r="15" fill="#9b59b6" stroke="#2c3e50" stroke-width="1"/>
                <text x="520" y="215" text-anchor="middle" font-size="10" fill="white" font-weight="bold">B</text>
                
                <circle cx="540" cy="230" r="15" fill="#9b59b6" stroke="#2c3e50" stroke-width="1"/>
                <text x="540" y="235" text-anchor="middle" font-size="10" fill="white" font-weight="bold">C</text>
                
                <circle cx="560" cy="210" r="15" fill="#9b59b6" stroke="#2c3e50" stroke-width="1"/>
                <text x="560" y="215" text-anchor="middle" font-size="10" fill="white" font-weight="bold">D</text>
                
                <!-- Star arrows -->
                <line x1="540" y1="205" x2="530" y2="210" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="540" y1="215" x2="540" y2="215" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="540" y1="205" x2="550" y2="210" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <text x="540" y="255" text-anchor="middle" font-size="9" fill="#dc3545">Extra dependencies</text>
                
                <!-- Formula -->
                <rect x="50" y="280" width="700" height="80" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="400" y="305" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    MRF Joint Distribution:
                </text>
                <text x="400" y="330" text-anchor="middle" font-size="12" fill="#2c3e50">
                    p(A,B,C,D) = (1/Z) × φ(A,B) × φ(B,C) × φ(C,D) × φ(D,A)
                </text>
                <text x="400" y="350" text-anchor="middle" font-size="11" fill="#2c3e50">
                    where Z is the partition function (normalization constant)
                </text>
            </svg>
        </div>

        <h2>📐 Formal Definition of MRFs</h2>

        <div class="definition-box">
            <h3>Definition: Markov Random Field</h3>
            <p>A <strong>Markov Random Field (MRF)</strong> is a probability distribution $p$ over variables $x_1, \ldots, x_n$ defined by an undirected graph $G$ where nodes correspond to variables $x_i$. The probability has the form:</p>
            <div class="formula-box">
                $$p(x_1, \ldots, x_n) = \frac{1}{Z} \prod_{c \in C} \phi_c(x_c)$$
            </div>
            <p>where:</p>
            <ul>
                <li>$C$ denotes the set of <strong>cliques</strong> (fully connected subgraphs) of $G$</li>
                <li>Each factor $\phi_c$ is a <strong>non-negative function</strong> over variables in clique $c$</li>
                <li>$Z$ is the <strong>partition function</strong> (normalization constant)</li>
            </ul>
        </div>

        <div class="formula-box">
            <p><strong>Partition Function:</strong></p>
            $$Z = \sum_{x_1, \ldots, x_n} \prod_{c \in C} \phi_c(x_c)$$
            <p>The partition function ensures the distribution sums to 1, but computing it can be exponentially expensive!</p>
        </div>

        <div class="concept-box">
            <p><strong>Key Concepts:</strong></p>
            <ul>
                <li><strong>Cliques:</strong> Fully connected subgraphs (single nodes, edges, triangles, etc.)</li>
                <li><strong>Factors:</strong> Non-negative functions that assign "compatibility scores" to variable assignments</li>
                <li><strong>Energy perspective:</strong> We can think of $-\log \phi_c(x_c)$ as "energy" - lower energy means higher probability</li>
                <li><strong>No directionality:</strong> Unlike Bayesian networks, we don't specify how variables generate each other</li>
            </ul>
        </div>

        <div class="example-box">
            <h4>Example: Computing the Partition Function</h4>
            <p>For our voting example with binary variables, we need to sum over all $2^4 = 16$ possible assignments:</p>
            <div class="formula-box">
                $$Z = \sum_{A,B,C,D \in \{0,1\}} \phi(A,B) \cdot \phi(B,C) \cdot \phi(C,D) \cdot \phi(D,A)$$
            </div>
            <p>For instance, when $A=B=C=D=1$:</p>
            <div class="formula-box">
                $$\tilde{p}(1,1,1,1) = \phi(1,1) \cdot \phi(1,1) \cdot \phi(1,1) \cdot \phi(1,1) = 10 \cdot 10 \cdot 10 \cdot 10 = 10,000$$
            </div>
            <p>This high score reflects that unanimous voting is highly compatible with the friendship structure.</p>
        </div>

        <h2>⚖️ Comparison with Bayesian Networks</h2>

        <div class="comparison-grid">
            <div class="comparison-card">
                <div class="card-title">🎯 Bayesian Networks (Directed)</div>
                <p><strong>Advantages:</strong></p>
                <ul>
                    <li>Natural causal interpretation</li>
                    <li>Easy to generate samples</li>
                    <li>Normalization constant Z = 1</li>
                    <li>Clear conditional dependencies</li>
                </ul>
                <p><strong>Limitations:</strong></p>
                <ul>
                    <li>Cannot represent all independence structures</li>
                    <li>Requires specifying causal direction</li>
                    <li>May need extra edges for some patterns</li>
                </ul>
            </div>

            <div class="comparison-card">
                <div class="card-title">🌐 Markov Random Fields (Undirected)</div>
                <p><strong>Advantages:</strong></p>
                <ul>
                    <li>Can represent cyclic dependencies</li>
                    <li>No need to specify causal direction</li>
                    <li>Natural for symmetric relationships</li>
                    <li>More general independence structures</li>
                </ul>
                <p><strong>Limitations:</strong></p>
                <ul>
                    <li>Computing Z is often intractable</li>
                    <li>Harder to interpret</li>
                    <li>Difficult to generate samples</li>
                    <li>May require approximation methods</li>
                </ul>
            </div>
        </div>

        <div class="theorem-box">
            <h3>Relationship Between Directed and Undirected Models</h3>
            <p><strong>Moralization:</strong> Any Bayesian network can be converted to an MRF by:</p>
            <ol>
                <li>Adding edges between all parents of each node ("marrying the parents")</li>
                <li>Removing edge directions</li>
                <li>Setting factors to be the original CPDs</li>
            </ol>
            <p><strong>Result:</strong> The resulting MRF has the same independence properties and Z = 1.</p>

            <p><strong>Converse:</strong> Converting MRF to Bayesian network is possible but may:</p>
            <ul>
                <li>Require a fully connected directed graph</li>
                <li>Be computationally intractable</li>
                <li>Lose the compact representation</li>
            </ul>
        </div>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Independence Structures: What Each Model Can Represent
                </text>

                <!-- Left: V-structure (Bayesian can represent, MRF cannot) -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    V-Structure (Explaining Away)
                </text>

                <circle cx="150" cy="100" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="150" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">X</text>

                <circle cx="250" cy="100" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="250" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Y</text>

                <circle cx="200" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="200" y="165" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Z</text>

                <!-- V-structure arrows -->
                <line x1="165" y1="115" x2="185" y2="145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="235" y1="115" x2="215" y2="145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                <text x="200" y="200" text-anchor="middle" font-size="11" fill="#2c3e50">X ⊥ Y (unconditionally)</text>
                <text x="200" y="215" text-anchor="middle" font-size="11" fill="#2c3e50">X ⊥̸ Y | Z (dependent given Z)</text>

                <rect x="120" y="230" width="160" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1" rx="3"/>
                <text x="200" y="245" text-anchor="middle" font-size="10" fill="#2c3e50">✓ Bayesian Network</text>
                <text x="200" y="260" text-anchor="middle" font-size="10" fill="#2c3e50">✗ MRF (needs extra edge)</text>

                <!-- Right: Cycle (MRF can represent, Bayesian cannot perfectly) -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Undirected Cycle
                </text>

                <circle cx="550" cy="100" r="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="550" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">A</text>

                <circle cx="650" cy="100" r="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="650" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">B</text>

                <circle cx="650" cy="160" r="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="650" y="165" text-anchor="middle" font-size="12" fill="white" font-weight="bold">C</text>

                <circle cx="550" cy="160" r="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="550" y="165" text-anchor="middle" font-size="12" fill="white" font-weight="bold">D</text>

                <!-- Cycle edges -->
                <line x1="570" y1="100" x2="630" y2="100" stroke="#e74c3c" stroke-width="3"/>
                <line x1="650" y1="120" x2="650" y2="140" stroke="#e74c3c" stroke-width="3"/>
                <line x1="630" y1="160" x2="570" y2="160" stroke="#e74c3c" stroke-width="3"/>
                <line x1="550" y1="140" x2="550" y2="120" stroke="#e74c3c" stroke-width="3"/>

                <text x="600" y="200" text-anchor="middle" font-size="11" fill="#2c3e50">A ⊥ C | {B,D}</text>
                <text x="600" y="215" text-anchor="middle" font-size="11" fill="#2c3e50">B ⊥ D | {A,C}</text>

                <rect x="520" y="230" width="160" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="1" rx="3"/>
                <text x="600" y="245" text-anchor="middle" font-size="10" fill="#2c3e50">✗ Bayesian Network</text>
                <text x="600" y="260" text-anchor="middle" font-size="10" fill="#2c3e50">✓ MRF (perfect representation)</text>

                <!-- Bottom summary -->
                <rect x="200" y="290" width="400" height="50" fill="white" stroke="#f39c12" stroke-width="2" rx="5"/>
                <text x="400" y="310" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Key Insight: Neither model is universally superior
                </text>
                <text x="400" y="325" text-anchor="middle" font-size="11" fill="#2c3e50">
                    Choose based on the natural structure of your problem
                </text>
            </svg>
        </div>

        <h2>🔗 Independence in Markov Random Fields</h2>

        <div class="concept-box">
            <p><strong>Markov Property:</strong> In an MRF, the independence structure is very intuitive:</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "A variable is independent of all others given its neighbors"
            </p>
        </div>

        <div class="definition-box">
            <h3>Markov Blanket in MRFs</h3>
            <p>For a node $X$ in an undirected graph, the <strong>Markov blanket</strong> is simply its set of neighbors. Formally:</p>
            <div class="formula-box">
                $$X \perp (V - \{X\} - \text{neighbors}(X)) \mid \text{neighbors}(X)$$
            </div>
            <p>This means: <em>Given its neighbors, $X$ is independent of all other variables in the graph.</em></p>
        </div>

        <div class="highlight">
            <p><strong>Global Independence:</strong> Variables $X$ and $Y$ are independent given a set $Z$ if $Z$ separates $X$ and $Y$ in the graph (i.e., all paths from $X$ to $Y$ pass through $Z$).</p>
        </div>

        <h2>🎯 Conditional Random Fields (CRFs)</h2>

        <div class="concept-box">
            <p><strong>Motivation:</strong> In many applications, we want to model $p(y|x)$ rather than the joint $p(x,y)$. This is especially common in:</p>
            <ul>
                <li><strong>Structured prediction:</strong> Predicting sequences, trees, or other structured outputs</li>
                <li><strong>Supervised learning:</strong> We observe $x$ and want to predict $y$</li>
                <li><strong>Discriminative modeling:</strong> Focus on decision boundaries rather than data generation</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition: Conditional Random Field</h3>
            <p>A <strong>Conditional Random Field (CRF)</strong> is a Markov network over variables $X \cup Y$ that specifies a conditional distribution:</p>
            <div class="formula-box">
                $$P(y|x) = \frac{1}{Z(x)} \prod_{c \in C} \phi_c(x_c, y_c)$$
            </div>
            <p>with partition function:</p>
            <div class="formula-box">
                $$Z(x) = \sum_{y \in Y} \prod_{c \in C} \phi_c(x_c, y_c)$$
            </div>
            <p><strong>Key insight:</strong> The partition function now depends on $x$, creating a different probability distribution for each input $x$.</p>
        </div>

        <div class="example-box">
            <h4>Example: Optical Character Recognition (OCR)</h4>
            <p><strong>Problem:</strong> Recognize a word from a sequence of character images.</p>
            <ul>
                <li><strong>Input:</strong> $x_i \in [0,1]^{d \times d}$ (pixel matrices)</li>
                <li><strong>Output:</strong> $y_i \in \{'a', 'b', \ldots, 'z'\}$ (alphabet letters)</li>
                <li><strong>Goal:</strong> Predict the entire word jointly, not each letter independently</li>
            </ul>

            <p><strong>Why joint prediction?</strong> Individual letters may be ambiguous, but context helps:</p>
            <ul>
                <li>The second letter could be 'U' or 'V'</li>
                <li>But given neighbors 'Q' and 'E', 'U' is much more likely</li>
                <li>This forms the word "QUEUE"</li>
            </ul>
        </div>

        <div class="svg-container">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <rect width="800" height="300" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Chain-Structured CRF for OCR
                </text>

                <!-- Input layer (observed) -->
                <text x="100" y="60" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Observed Images (x)</text>

                <!-- Input nodes -->
                <rect x="80" y="80" width="40" height="40" fill="#95a5a6" stroke="#2c3e50" stroke-width="2" rx="3"/>
                <text x="100" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">x₁</text>

                <rect x="180" y="80" width="40" height="40" fill="#95a5a6" stroke="#2c3e50" stroke-width="2" rx="3"/>
                <text x="200" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">x₂</text>

                <rect x="280" y="80" width="40" height="40" fill="#95a5a6" stroke="#2c3e50" stroke-width="2" rx="3"/>
                <text x="300" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">x₃</text>

                <rect x="380" y="80" width="40" height="40" fill="#95a5a6" stroke="#2c3e50" stroke-width="2" rx="3"/>
                <text x="400" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">x₄</text>

                <rect x="480" y="80" width="40" height="40" fill="#95a5a6" stroke="#2c3e50" stroke-width="2" rx="3"/>
                <text x="500" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">x₅</text>

                <!-- Output layer (hidden) -->
                <text x="100" y="160" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Predicted Letters (y)</text>

                <!-- Output nodes -->
                <circle cx="100" cy="180" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="100" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">y₁</text>

                <circle cx="200" cy="180" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="200" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">y₂</text>

                <circle cx="300" cy="180" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="300" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">y₃</text>

                <circle cx="400" cy="180" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="400" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">y₄</text>

                <circle cx="500" cy="180" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="500" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">y₅</text>

                <!-- Vertical connections (image factors) -->
                <line x1="100" y1="120" x2="100" y2="160" stroke="#e74c3c" stroke-width="3"/>
                <line x1="200" y1="120" x2="200" y2="160" stroke="#e74c3c" stroke-width="3"/>
                <line x1="300" y1="120" x2="300" y2="160" stroke="#e74c3c" stroke-width="3"/>
                <line x1="400" y1="120" x2="400" y2="160" stroke="#e74c3c" stroke-width="3"/>
                <line x1="500" y1="120" x2="500" y2="160" stroke="#e74c3c" stroke-width="3"/>

                <!-- Horizontal connections (pairwise factors) -->
                <line x1="120" y1="180" x2="180" y2="180" stroke="#2ecc71" stroke-width="3"/>
                <line x1="220" y1="180" x2="280" y2="180" stroke="#2ecc71" stroke-width="3"/>
                <line x1="320" y1="180" x2="380" y2="180" stroke="#2ecc71" stroke-width="3"/>
                <line x1="420" y1="180" x2="480" y2="180" stroke="#2ecc71" stroke-width="3"/>

                <!-- Factor labels -->
                <text x="100" y="145" text-anchor="middle" font-size="9" fill="#e74c3c" font-weight="bold">φ(x₁,y₁)</text>
                <text x="200" y="145" text-anchor="middle" font-size="9" fill="#e74c3c" font-weight="bold">φ(x₂,y₂)</text>
                <text x="300" y="145" text-anchor="middle" font-size="9" fill="#e74c3c" font-weight="bold">φ(x₃,y₃)</text>
                <text x="400" y="145" text-anchor="middle" font-size="9" fill="#e74c3c" font-weight="bold">φ(x₄,y₄)</text>
                <text x="500" y="145" text-anchor="middle" font-size="9" fill="#e74c3c" font-weight="bold">φ(x₅,y₅)</text>

                <text x="150" y="170" text-anchor="middle" font-size="9" fill="#2ecc71" font-weight="bold">φ(y₁,y₂)</text>
                <text x="250" y="170" text-anchor="middle" font-size="9" fill="#2ecc71" font-weight="bold">φ(y₂,y₃)</text>
                <text x="350" y="170" text-anchor="middle" font-size="9" fill="#2ecc71" font-weight="bold">φ(y₃,y₄)</text>
                <text x="450" y="170" text-anchor="middle" font-size="9" fill="#2ecc71" font-weight="bold">φ(y₄,y₅)</text>

                <!-- Example word -->
                <text x="100" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Q</text>
                <text x="200" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">U</text>
                <text x="300" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">E</text>
                <text x="400" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">U</text>
                <text x="500" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">E</text>

                <!-- Legend -->
                <rect x="580" y="80" width="180" height="120" fill="white" stroke="#2c3e50" stroke-width="1" rx="5"/>
                <text x="670" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">CRF Factors:</text>

                <line x1="600" y1="115" x2="620" y2="115" stroke="#e74c3c" stroke-width="3"/>
                <text x="630" y="120" font-size="10" fill="#2c3e50">Image factors φ(xᵢ,yᵢ)</text>
                <text x="630" y="132" font-size="9" fill="#2c3e50">Compatibility of letter with image</text>

                <line x1="600" y1="145" x2="620" y2="145" stroke="#2ecc71" stroke-width="3"/>
                <text x="630" y="150" font-size="10" fill="#2c3e50">Pairwise factors φ(yᵢ,yᵢ₊₁)</text>
                <text x="630" y="162" font-size="9" fill="#2c3e50">Letter transition probabilities</text>

                <text x="670" y="180" text-anchor="middle" font-size="9" fill="#2c3e50">Goal: Find most likely word</text>
                <text x="670" y="192" text-anchor="middle" font-size="9" fill="#2c3e50">given all images jointly</text>
            </svg>
        </div>

        <div class="formula-box">
            <p><strong>CRF for OCR:</strong></p>
            $$P(y|x) = \frac{1}{Z(x)} \prod_{i=1}^n \phi(x_i, y_i) \prod_{i=1}^{n-1} \phi(y_i, y_{i+1})$$
            <p>where:</p>
            <ul style="text-align: left; display: inline-block;">
                <li>$\phi(x_i, y_i)$: compatibility of letter $y_i$ with image $x_i$</li>
                <li>$\phi(y_i, y_{i+1})$: transition probability between consecutive letters</li>
            </ul>
        </div>

        <div class="theorem-box">
            <h3>CRF Features and Exponential Form</h3>
            <p>In practice, CRF factors often take the exponential form:</p>
            <div class="formula-box">
                $$\phi_c(x_c, y_c) = \exp(w_c^T f_c(x_c, y_c))$$
            </div>
            <p>where $f_c(x_c, y_c)$ are <strong>feature functions</strong> and $w_c$ are learned weights.</p>

            <p><strong>Advantages of this form:</strong></p>
            <ul>
                <li>Features can be arbitrarily complex</li>
                <li>Can depend on entire input $x$ (global features)</li>
                <li>Weights are learned from training data</li>
                <li>Maintains tractable inference structure</li>
            </ul>
        </div>

        <div class="example-box">
            <h4>CRF Features Example</h4>
            <p><strong>Image features</strong> $f(x_i, y_i)$:</p>
            <ul>
                <li>Output of CNN/logistic regression on pixels $x_i$</li>
                <li>Hand-crafted features (edge detectors, curves, etc.)</li>
                <li>Can depend on entire sequence: $f(x, y_i)$</li>
            </ul>

            <p><strong>Transition features</strong> $f(y_i, y_{i+1})$:</p>
            <ul>
                <li>Indicator functions: $I(y_i = 'q', y_{i+1} = 'u')$</li>
                <li>Learned from large text corpora</li>
                <li>Encode language model information</li>
            </ul>
        </div>

        <h2>🔗 Factor Graphs</h2>

        <div class="concept-box">
            <p><strong>Motivation:</strong> Sometimes it's useful to explicitly represent both variables and factors in the graph structure. Factor graphs provide this bipartite representation.</p>
        </div>

        <div class="definition-box">
            <h3>Definition: Factor Graph</h3>
            <p>A <strong>factor graph</strong> is a bipartite graph with two types of nodes:</p>
            <ul>
                <li><strong>Variable nodes:</strong> Represent random variables (drawn as circles)</li>
                <li><strong>Factor nodes:</strong> Represent factors/potentials (drawn as squares)</li>
            </ul>
            <p>Edges connect factors to the variables they depend on.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Factor Graph Representation
                </text>

                <!-- Left: Original MRF -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Original MRF
                </text>

                <!-- MRF nodes -->
                <circle cx="150" cy="100" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="150" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">A</text>

                <circle cx="250" cy="100" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="250" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">B</text>

                <circle cx="200" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="200" y="165" text-anchor="middle" font-size="12" fill="white" font-weight="bold">C</text>

                <!-- MRF edges -->
                <line x1="170" y1="100" x2="230" y2="100" stroke="#e74c3c" stroke-width="3"/>
                <line x1="165" y1="115" x2="185" y2="145" stroke="#e74c3c" stroke-width="3"/>
                <line x1="235" y1="115" x2="215" y2="145" stroke="#e74c3c" stroke-width="3"/>

                <!-- Right: Factor Graph -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Equivalent Factor Graph
                </text>

                <!-- Variable nodes -->
                <circle cx="500" cy="120" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="500" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">A</text>

                <circle cx="650" cy="120" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="650" y="125" text-anchor="middle" font-size="12" fill="white" font-weight="bold">B</text>

                <circle cx="575" cy="220" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="575" y="225" text-anchor="middle" font-size="12" fill="white" font-weight="bold">C</text>

                <!-- Factor nodes -->
                <rect x="565" y="110" width="20" height="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="575" y="125" text-anchor="middle" font-size="10" fill="white" font-weight="bold">f₁</text>

                <rect x="520" y="160" width="20" height="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="530" y="175" text-anchor="middle" font-size="10" fill="white" font-weight="bold">f₂</text>

                <rect x="620" y="160" width="20" height="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="630" y="175" text-anchor="middle" font-size="10" fill="white" font-weight="bold">f₃</text>

                <!-- Factor graph edges -->
                <line x1="520" y1="120" x2="565" y2="120" stroke="#2c3e50" stroke-width="2"/>
                <line x1="585" y1="120" x2="630" y2="120" stroke="#2c3e50" stroke-width="2"/>
                <line x1="510" y1="135" x2="525" y2="160" stroke="#2c3e50" stroke-width="2"/>
                <line x1="535" y1="180" x2="555" y2="210" stroke="#2c3e50" stroke-width="2"/>
                <line x1="640" y1="135" x2="625" y2="160" stroke="#2c3e50" stroke-width="2"/>
                <line x1="635" y1="180" x2="595" y2="210" stroke="#2c3e50" stroke-width="2"/>

                <!-- Factor labels -->
                <text x="575" y="95" text-anchor="middle" font-size="9" fill="#2c3e50">φ(A,B)</text>
                <text x="530" y="195" text-anchor="middle" font-size="9" fill="#2c3e50">φ(A,C)</text>
                <text x="630" y="195" text-anchor="middle" font-size="9" fill="#2c3e50">φ(B,C)</text>

                <!-- Legend -->
                <rect x="50" y="250" width="700" height="120" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="400" y="275" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Factor Graph Benefits
                </text>

                <div style="font-size: 12px;">
                    <text x="80" y="300" font-size="12" fill="#2c3e50" font-weight="bold">Advantages:</text>
                    <text x="80" y="320" font-size="11" fill="#2c3e50">• Explicit representation of factor dependencies</text>
                    <text x="80" y="335" font-size="11" fill="#2c3e50">• Facilitates message-passing algorithms</text>
                    <text x="80" y="350" font-size="11" fill="#2c3e50">• Clear separation of variables and computations</text>

                    <text x="450" y="300" font-size="12" fill="#2c3e50" font-weight="bold">Applications:</text>
                    <text x="450" y="320" font-size="11" fill="#2c3e50">• Belief propagation algorithms</text>
                    <text x="450" y="335" font-size="11" fill="#2c3e50">• Variational inference methods</text>
                    <text x="450" y="350" font-size="11" fill="#2c3e50">• Efficient computation of marginals</text>
                </div>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Key Insight:</strong> Factor graphs make the computational structure explicit, which is particularly useful for:</p>
            <ul>
                <li><strong>Message passing algorithms:</strong> Variables send messages to factors, factors send messages to variables</li>
                <li><strong>Inference algorithms:</strong> Belief propagation, sum-product algorithm</li>
                <li><strong>Learning algorithms:</strong> Expectation-maximization, gradient-based methods</li>
            </ul>
        </div>

        <h2>📊 Summary and Practical Guidelines</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Aspect</th>
                    <th>Bayesian Networks</th>
                    <th>Markov Random Fields</th>
                    <th>Conditional Random Fields</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Graph Type</strong></td>
                    <td>Directed (DAG)</td>
                    <td>Undirected</td>
                    <td>Undirected (conditional)</td>
                </tr>
                <tr>
                    <td><strong>Factorization</strong></td>
                    <td>$\prod P(X_i | \text{parents})$</td>
                    <td>$\frac{1}{Z} \prod \phi_c(x_c)$</td>
                    <td>$\frac{1}{Z(x)} \prod \phi_c(x_c, y_c)$</td>
                </tr>
                <tr>
                    <td><strong>Normalization</strong></td>
                    <td>Z = 1 (automatic)</td>
                    <td>Z often intractable</td>
                    <td>Z(x) depends on input</td>
                </tr>
                <tr>
                    <td><strong>Interpretation</strong></td>
                    <td>Causal/generative</td>
                    <td>Energy/compatibility</td>
                    <td>Discriminative</td>
                </tr>
                <tr>
                    <td><strong>Best for</strong></td>
                    <td>Causal modeling, sampling</td>
                    <td>Symmetric dependencies</td>
                    <td>Structured prediction</td>
                </tr>
                <tr>
                    <td><strong>Challenges</strong></td>
                    <td>Limited independence structures</td>
                    <td>Intractable partition function</td>
                    <td>Complex feature engineering</td>
                </tr>
            </tbody>
        </table>

        <div class="comparison-grid">
            <div class="comparison-card">
                <div class="card-title">🎯 When to Use Bayesian Networks</div>
                <ul>
                    <li>Clear causal relationships exist</li>
                    <li>Need to generate samples</li>
                    <li>Want interpretable model</li>
                    <li>Have prior knowledge about dependencies</li>
                    <li>Computational efficiency is important</li>
                </ul>
            </div>

            <div class="comparison-card">
                <div class="card-title">🌐 When to Use MRFs</div>
                <ul>
                    <li>Symmetric/undirected relationships</li>
                    <li>Cyclic dependencies in data</li>
                    <li>No natural causal ordering</li>
                    <li>Modeling spatial/temporal correlations</li>
                    <li>Can handle intractable partition function</li>
                </ul>
            </div>

            <div class="comparison-card">
                <div class="card-title">🎯 When to Use CRFs</div>
                <ul>
                    <li>Supervised learning tasks</li>
                    <li>Structured prediction problems</li>
                    <li>Don't need to model input distribution</li>
                    <li>Have rich feature representations</li>
                    <li>Focus on discriminative performance</li>
                </ul>
            </div>

            <div class="comparison-card">
                <div class="card-title">🔗 When to Use Factor Graphs</div>
                <ul>
                    <li>Complex factor structures</li>
                    <li>Need efficient inference algorithms</li>
                    <li>Message passing computations</li>
                    <li>Variational inference methods</li>
                    <li>Want explicit computational graph</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Key Takeaways:</strong></p>
            <ul>
                <li><strong>Complementary tools:</strong> Directed and undirected models capture different independence structures</li>
                <li><strong>Problem-driven choice:</strong> Select the model type based on your problem's natural structure</li>
                <li><strong>Computational trade-offs:</strong> MRFs are more expressive but computationally challenging</li>
                <li><strong>CRFs for prediction:</strong> When you only care about $p(y|x)$, not the full joint $p(x,y)$</li>
                <li><strong>Factor graphs for algorithms:</strong> Explicit representation enables efficient inference methods</li>
            </ul>
        </div>

        <div class="warning">
            <p><strong>Common Pitfalls:</strong></p>
            <ul>
                <li>Assuming one model type is always superior (they're complementary)</li>
                <li>Ignoring computational complexity of partition function in MRFs</li>
                <li>Over-engineering features in CRFs without considering interpretability</li>
                <li>Forgetting that factor graphs are just a representation, not a different model class</li>
            </ul>
        </div>

    </div>
</body>
</html>
