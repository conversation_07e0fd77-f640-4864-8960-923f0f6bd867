Training generative models becomes increasingly challenging as the dependencies among the dimensions grow more complex. Consider the task of generating transcriptomic profiles of single cells. If a partial profile suggests the presence of markers typical of a neuronal cell, the remaining profile cannot align with that of a muscle cell without resulting in an inaccurate or impossible cell type representation. It is beneficial for the model to first determine the cell type it intends to generate before it begins to assign values to specific genes. This decision-making process involves a latent variable. In practice, before simulating any gene expression data, the model would randomly select a cell type or a cell state, represented by a latent variable $$z$$, ensuring the gene expression aligns consistently with the chosen cell type. The term "latent" is used because, from the generated profile alone, we cannot directly discern the latent variable settings that led to the production of that specific cell profile; this would require additional analytical techniques, such as clustering in transcriptomics.

To make this discussion precise mathematically, the probability of each $$X$$ in the training set under the entire generative process can be derived using the <PERSON><PERSON>' rule:

$$P(X) = \int P(X, z; \theta)dz = \int P(X \mid z; \theta)P(z)dz \\ $$

During this generative process, the model first samples a vector of latent variables $$z$$ in a high-dimensional space $$\mathcal{Z}$$ according to some probability density function (PDF) $$P(z)$$. We then use an output distribution $$P(X \mid z; \theta)$$ which depends on the value of $$z$$ to draw data points.

# How do we choose output distribution?

In variational autoencoder, the choice of output distribution $$P(X \mid z)$$ is often Gaussian, i.e., $$P(X \mid z; \theta) = \mathcal{N}(X \mid f(z;\theta), \sigma^2 * I)$$. That is, it has mean $$f(z;θ)$$ and covariance equal to the identity matrix $$I$$ times some scalar $$\sigma$$ (which is a hyperparameter). $$f$$ is a family of deterministic functions parameterized by a vector $$\theta$$ in some space $$\Theta$$. We wish to find $$θ$$ such that the probability of $$P(X)$$ is maximized.

> $$P(X \mid z; \theta)$$ does not need to be Gaussian. For instance, if $$X$$ is binary, then $$P(X \mid z)$$ might be a Bernoulli parameterized by $$f(z; \theta)$$. The important property is simply that $$P(X \mid z)$$ can be computed, and is continuous in $$\theta$$.&#x20;

There are two remaining problems that VAEs must deal with: how to define the latent variables $$z$$ (i.e., decide what information they represent), and how to deal with the integral over $$z$$. VAEs give a definite answer to both.

The choice of $$P(z)$$ is **unimportant** as any distribution in $$d$$ dimensions can be generated by taking a set of $$d$$ variables that are normally distributed and mapping them through a sufficiently complicated function. Therefore, VAEs assert that samples of $$z$$ can be drawn from a simple distribution, i.e., $$\mathcal{N}(0,I)$$, where $$I$$ is the identity matrix. In fact, recall that $$P(X \mid z; \theta) = \mathcal{N}(X \mid f(z;\theta), \sigma^2 * I)$$. If $$f(z;θ)$$ is a multi-layer neural network, then we can imagine the network using its first few layers to map the normally distributed $$z$$’s to the latent values with exactly the right statistics. Then it can use later layers to map those latent values to a data point in space $$X$$.

Given $$z \sim \mathcal{N}(0, I)$$, now all that remains is to maximize the likelihood $$P(X)$$. As is common in machine learning, if we can find a computable formula for $$P(X)$$, and we can take the gradient of that formula, then we can optimize the model using stochastic gradient ascent. It is actually conceptually straightforward to compute $$P(X)$$ approximately: we first sample a large number of $$z$$ values $${z_1,...,z_n}$$, and compute $$P(X) \approx \frac{1}n \sum_i P(X \mid z_i)$$.

![](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=NzMyNmY3ZmNhNDYyNmI4OGM2YWIyNjcxZGI4ZmQwYTZfalEzMzNXczhsSVQ3MVIzWGFKRnRrcUhhS0xqcmhTWWZfVG9rZW46WVBSR2JITGhSb0YwZFN4M3RndmNHRlVwbkxkXzE3NDk2OTk5NjM6MTc0OTcwMzU2M19WNA)

To give you an example, assume a latent space dimensionality of 1 for simplicity, i.e., $$z$$ is a scalar sampled from $$\mathcal{N}(0, 1)$$. Let $$X$$ be a one-dimensional observable data, and let’s assume a simple generative model where $$X$$ given $$z$$ is also normally distributed, that is, $$X \mid z \sim \mathcal{N}(f(z), 1)$$. For simplicity, let's assume $$f$$ is a linear function such that $$f(z) = az + b$$, where $$a$$ and $$b$$ are parameters of the linear model. The goal is to estimate $$P(X)$$ using the Monte Carlo method, and then to compute the gradient of the likelihood with respect to the model parameters $$a$$ and $$b$$. Here is a code example that achieves the parameters learning:

```python
import numpy as np
from scipy.stats import norm
import matplotlib.pyplot as plt

np.random.seed(0)

# Parameters
X = 5
n_samples = 1000
a = 2.0  # Initial guess for slope
b = 1.0  # Initial guess for intercept to ensure lambda is positive
sigma = 1.0  # standard deviation of the noise

z_samples = np.random.normal(0, 1, n_samples)

L = []
learning_rate = 0.1
delta = None
while delta is None or delta > 0.000001:
    f_z = a * z_samples + b

    # Compute gradients and update parameters
    grad_a = np.mean((X - f_z) * z_samples / sigma**2)
    grad_b = np.mean((X - f_z) / sigma**2)
    a += learning_rate * grad_a
    b += learning_rate * grad_b

    log_prob_matrix = norm.logpdf(X, f_z, sigma)
    P_X = np.mean(log_prob_matrix)

    if len(L) > 0:
        delta = P_X - L[-1]
    L.append(P_X)

print(f'a = {a}, b = {b}')
print(L)
plt.hist(a * z_samples + b, 10, density=True)
plt.show()
```

The code above works pretty well when the dimensionality of the random variable is one, but has severe limitations when applying to high-dimensional data. In high-dimensional spaces, data points (observations) tend to be sparse. This means that the volume of the space grows exponentially with the increase in dimensions, but the actual data available to fill this space grows much slower. As a result, most of the space is empty, or in other words, most combinations of feature values are extremely unlikely or not represented in the data. When dealing with probability distributions in high dimensions, the density of any specific point (or configuration of feature values) becomes increasingly diluted. For a fixed amount of data, the probability mass spreads thinner across the expanding volume of the space. This causes the density at any specific point to decrease.

In high dimensions, the specificity required of $$z$$ to reconstruct $$X$$ accurately becomes higher. This means that for most values of $$z$$ (which do not correspond exactly or very closely to a specific $$X$$), the probability $$P(X \mid z)$$ (which quantifies how likely it is to observe $$X$$ given $$z$$) is very low. Essentially, only a few values of $$z$$ are good at explaining any given $$X$$, and for the rest, $$P(X \mid z)$$ drops sharply and hence contribute almost nothing to our estimate of $$P(X)$$.

# **Variational inference objective**

The key idea behind the variational autoencoder is to attempt to sample values of $$z$$ that are likely to have produced $$X$$, and compute $$P(X)$$ just from those. This means that we need to able to draw samples from $$P(z \mid X)$$ instead of $$P(z)$$. $$P(z \mid X)$$ is the posterior probability which is usually very difficult to compute. VAEs use a technique called variational inference to approximate the distribution of $$P(z \mid X)$$. Specifically, in variational inference, we first posit a family of approximate densities $$\mathcal{Q}$$. This is a set of densities over the latent variables. Then, we try to find the member of that family that minimizes the Kullback-Leibler (KL) divergence (KL divergence or $$\mathcal{D}$$) to the exact posterior,

$$Q^*(z)=\argmin_{Q(z)\in\mathcal{Q}}\mathcal{D}[Q(z)||P(z \mid X)] $$

We now expand the KL divergence term and try to interpret what it is optimizing,

$$\begin{aligned}
\mathcal{D}[Q(z)||P(z \mid X)] 
&= E_{z \sim Q}[\log Q(z) - \log P(z \mid X)] \\
&= E_{z \sim Q}[\log Q(z) - \log \frac{P(X \mid z)P(z)}{P(X)}] & \text{(Bayes's rule)} \\
&= E_{z \sim Q}[\log Q(z) - \log P(X \mid z) - \log P(z)] + \log P(X)
\end{aligned}$$

Here, $$\log P(X)$$ comes out of the expectation because it does not depend on $$z$$. However, this objective is not computable because it requires computing the evidence $$\log P(X)$$. Because we cannot compute the KL divergence, we choose to optimize an alternative objective. Negating both sides, rearranging, and contracting part of $$E_{z \sim Q}$$ into a KL-divergence term yields:

$$\log P(X) - \mathcal{D}[Q(z)||P(z \mid X)] = E_{z \sim Q}[\log P(X \mid z)] - \mathcal{D}[Q(z)||P(z)]$$

This function is called the **evidence lower bound (ELBO)**. Because $$\log P(X)$$ is a constant with respect to $$Q(z)$$, maximizing ELBO is equivalent to minimizing KL.

Intuitions about ELBO:

* The first term is an expected likelihood: it encourages densities that place their mass on configurations of the latent variables that explain the observed data.

* The second term is the negative divergence between the variational density and the prior: it encourages densities close to the prior. Thus, the variational objective mirrors the usual balance between likelihood and prior.

* Because $$\log P(X) = \mathcal{D}[Q(z) || p(z \mid x)] + ELBO(Q) \ge ELBO(Q)$$, ELBO lower-bounds the (log) evidence. The relationship between the ELBO and $$\log P(X)$$ has led to using the variational bound as a model selection criterion. The premise is that the bound is a good approximation of the marginal likelihood, which provides a basis for selecting a model. Though this sometimes works in practice, selecting based on a bound is not justified in theory.

Note that $$X$$ is fixed, and $$Q$$ can be any distribution. Since we’re interested in inferring $$P(X)$$, it makes sense to construct a $$Q$$ which does depend on $$X$$. In particular, VAEs use multi-layer neural networks to map $$X$$ into $$z$$. We therefore replace $$Q(z)$$ with $$Q(z \mid X)$$ in the above equation to make this dependency explicit.

$$\log P(X) - \mathcal{D}[Q(z \mid X)||P(z \mid X)] = E_{z \sim Q}[\log P(X \mid z)] - \mathcal{D}[Q(z \mid X)||P(z)]$$

This equation serves as the core of the variational autoencoder:

* On the left-hand side, we are maximizing $$\log P(X)$$ while simultaneously minimizing $$\mathcal{D}[Q(z \mid X) || P(z \mid X)]$$. Assuming we use an arbitrarily high-capacity model for $$Q(z \mid X)$$, then $$Q(z \mid X)$$ will hopefully actually match $$P(z \mid X)$$, in which case this KL divergence term will be zero, and we will be directly optimizing $$\log P(X)$$.

* On the right-hand side, $$Q(z \mid X)$$ is the encoder and $$P(X \mid z)$$ is the decoder, both of which can be instantiated by multi-layer neural networks and can be optimized via stochastic gradient descent.

# **Optimizing the objective**

So how can we perform stochastic gradient descent on the right hand side of the above equation? We usually set $$Q(z \mid X) = \mathcal{N}(z \mid µ(X; \theta), \Sigma(X; \theta))$$, where $$\mu$$ and $$\Sigma$$ are arbitrary deterministic functions with parameters $$\theta$$ that can be learned from data. In practice, $$\mu$$ and $$\Sigma$$ are again implemented via neural networks, and $$\Sigma$$ is constrained to be a diagonal matrix.

The last term $$\mathcal{D}[Q(z \mid X)||P(z)]$$ is now a KL-divergence between two multivariate Gaussian distributions, which can be computed in closed form.

To compute the first term, we need to use sampling to estimate $$E_{z \sim Q}[\log P(X \mid z)]$$, but getting a good estimate would require passing many samples of $$z$$ through the decoder, which would be expensive. Hence, as is standard in stochastic gradient descent, we take one sample of $$z$$ and treat $$\log P(X \mid z)$$ for that $$z$$ as an approximation of $$E_{z \sim Q}[\log P(X \mid z)]$$. After all, we are already doing stochastic gradient descent over different values of $$X$$ sampled from a dataset.

# Reparameterization trick

![A training-time variational autoencoder implemented as a feedforward neural network, where P(X|z) is Gaussian. Left is without the “reparameterization trick”, and right is with it. Red shows sampling operations that are non-differentiable. Blue shows loss layers. The feedforward behavior of these networks is identical, but backpropagation can be applied only to the right network.](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=MDQwMmYzNjc5Nzg5NmY0NTAyY2ZmZDM2Y2EzZDQ0NjhfaGxQaFdwRnJ4Zk51Tk45ZW9hNFd6NE5oREc4SVdXZ1ZfVG9rZW46SWxrSGJCU1p3bzJ4WkF4Qm9qQWNlVnY2bkFmXzE3NDk2OTk5NjM6MTc0OTcwMzU2M19WNA)

# Epilog

## Posterior collapse and latent variable non-identifiability

Posterior collapse is a phenomenon where the posterior of the latents in a VAE is equal to its uninformative prior, i.e., $$Q(z \mid X) = P(z)$$.

![](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=ZWM5YjE3YWRjNmY0NmNiOGIzMjkyZTYxNTBlN2Q0N2NfNkRqcXR5ZXUzVW9veWJwcDZuNkFTQUl2VldIZjVWZGxfVG9rZW46UVRTaGJRWWZrb3BRMEJ4RTB3aWNXMXBxbnBjXzE3NDk2OTk5NjM6MTc0OTcwMzU2M19WNA)

Reasons of collapse:

* Decoder is too powerful (Li+ 2019)

* The prior biases us (Higgins+ 2016)

* Approximate inference (Bowman+ 2015; Kingma+ 2016; Sønderby+ 2016)

* Training procedure; the order of parameter updates (He+ 2019)

* Local minima of optimization (Lucas+ 2019)

* Information preference (Chen+ 2016)

# **References**

1. <https://arxiv.org/pdf/1606.05908.pdf>
