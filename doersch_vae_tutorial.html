<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial on Variational Autoencoders - Complete Guide</title>
    
    <!-- MathJax 3 configuration for high-quality math rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true,
                packages: {'[+]': ['ams', 'newcommand', 'mathtools']},
                macros: {
                    // Common mathematical spaces
                    R: "\\mathbb{R}",
                    Z: "\\mathbb{Z}",
                    N: "\\mathbb{N}",
                    Q: "\\mathbb{Q}",
                    C: "\\mathbb{C}",
                    // Probability and statistics
                    E: "\\mathbb{E}",
                    Var: "\\text{Var}",
                    Cov: "\\text{Cov}",
                    // Distributions  
                    Normal: "\\mathcal{N}",
                    Uniform: "\\mathcal{U}",
                    <PERSON><PERSON><PERSON>: "\\text{<PERSON><PERSON><PERSON>}",
                    // KL divergence
                    KL: "D_{\\text{KL}}"
                }
            },
            svg: {
                fontCache: 'global'
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
                renderActions: {
                    addMenu: [0, '', '']
                }
            },
            startup: {
                ready: () => {
                    MathJax.startup.defaultReady();
                    console.log('MathJax is loaded and ready.');
                }
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"
        onerror="console.error('MathJax failed to load from CDN, trying alternative...'); this.src='https://polyfill.io/v3/polyfill.min.js?features=es6'; var script = document.createElement('script'); script.src = 'https://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_SVG'; document.head.appendChild(script);">
    </script>
    
    <!-- Plotly.js for interactive visualizations -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
            line-height: 1.7;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            color: white;
        }
        
        .header h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .header .author {
            font-size: 1.1em;
            opacity: 0.8;
            font-style: italic;
        }
        
        h2 {
            color: #2c3e50;
            margin: 50px 0 25px 0;
            font-size: 2.5em;
            border-left: 6px solid #667eea;
            padding-left: 25px;
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.1), transparent);
            padding: 15px 25px;
            border-radius: 0 10px 10px 0;
        }
        
        h3 {
            color: #34495e;
            margin: 35px 0 20px 0;
            font-size: 1.8em;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        h4 {
            color: #2980b9;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
        }
        
        p {
            margin-bottom: 20px;
            font-size: 1.1em;
            text-align: justify;
            line-height: 1.8;
        }
        
        .key-concept {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1));
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #3498db;
            position: relative;
            overflow: hidden;
        }
        
        .key-concept::before {
            content: '💡';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 2em;
            opacity: 0.3;
        }
        
        .highlight {
            background: linear-gradient(120deg, rgba(241, 196, 15, 0.2), rgba(230, 126, 34, 0.2));
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #f1c40f;
        }
        
        .warning {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(230, 126, 34, 0.1));
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        
        .math-display {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border: 2px solid #e9ecef;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .visualization {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            text-align: center;
            border: 2px solid #667eea;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }
        
        .equation-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2em;
        }
        
        .info-theory {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.1), rgba(142, 68, 173, 0.1));
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            border-left: 5px solid #9b59b6;
        }
        
        .toc {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border: 2px solid #bdc3c7;
        }
        
        .toc h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .toc ul {
            list-style: none;
            padding: 0;
        }
        
        .toc li {
            margin: 12px 0;
            padding: 10px 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            transition: transform 0.2s;
        }
        
        .toc li:hover {
            transform: translateX(5px);
        }
        
        .section-divider {
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            margin: 50px 0;
            border-radius: 2px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .plotly-graph-div {
            width: 100% !important;
            height: 450px !important;
        }
        
        .conclusion {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-top: 50px;
        }
        
        .abstract {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(155, 89, 182, 0.05));
            padding: 30px;
            border-radius: 12px;
            margin: 30px 0;
            border: 2px solid #3498db;
            font-style: italic;
            font-size: 1.05em;
        }
        
        /* Math error handling */
        .MathJax_Error, .mjx-error {
            background-color: #fff3cd !important;
            border: 1px solid #ffeaa7 !important;
            padding: 2px 5px !important;
            border-radius: 3px !important;
            color: #856404 !important;
        }
        
        .math-error-fallback {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .header h1 { font-size: 2.5em; }
            h2 { font-size: 2em; }
            h3 { font-size: 1.5em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Tutorial on Variational Autoencoders</h1>
            <div class="subtitle">A Complete Guide to Understanding VAEs</div>
            <div class="author">Based on Carl Doersch's Seminal Tutorial</div>
        </div>
        
        <div class="abstract">
            <h3>Abstract</h3>
            <p>In just a few years, Variational Autoencoders (VAEs) have emerged as one of the most popular approaches to unsupervised learning of complicated distributions. VAEs are appealing because they are built on top of standard function approximators (neural networks), and can be trained with stochastic gradient descent. This tutorial introduces the intuitions behind VAEs, explains the mathematics behind them, and describes their practical applications. No prior knowledge of variational Bayesian methods is assumed.</p>
        </div>
        
        <div class="toc">
            <h3>📚 Table of Contents</h3>
            <ul>
                <li><strong>1. Introduction to Generative Modeling</strong> - Understanding the problem and motivation</li>
                <li><strong>2. Latent Variable Models</strong> - Mathematical foundation and intuition</li>
                <li><strong>3. The Variational Framework</strong> - Core mathematics and ELBO derivation</li>
                <li><strong>4. Reparameterization Trick</strong> - Making backpropagation work</li>
                <li><strong>5. Information Theory Perspective</strong> - Alternative interpretations</li>
                <li><strong>6. Conditional VAEs</strong> - Extensions for structured prediction</li>
                <li><strong>7. Implementation Examples</strong> - MNIST and practical considerations</li>
                <li><strong>8. Advanced Topics</strong> - Theoretical insights and limitations</li>
            </ul>
        </div>
        
        <div class="section-divider"></div>
        
        <h2 id="introduction">1. Introduction to Generative Modeling</h2>
        
        <p><strong>Generative modeling</strong> is a broad area of machine learning which deals with models of distributions $P(X)$, defined over datapoints $X$ in some potentially high-dimensional space $\mathcal{X}$. For instance, images are a popular kind of data for which we might create generative models.</p>
        
        <div class="key-concept">
            <h4>The Core Challenge</h4>
            <p>Each datapoint (image) has thousands or millions of dimensions (pixels), and the generative model's job is to somehow capture the dependencies between pixels - that nearby pixels have similar color, are organized into objects, etc.</p>
        </div>
        
        <p>Exactly what it means to "capture" these dependencies depends on what we want to do with the model. One straightforward kind of generative model simply allows us to compute $P(X)$ numerically. In the case of images, $X$ values which look like real images should get high probability, whereas images that look like random noise should get low probability.</p>
        
        <div class="warning">
            <h4>⚠️ Limitation of Simple Probability Models</h4>
            <p>However, models like this are not necessarily useful: knowing that one image is unlikely does not help us synthesize one that is likely!</p>
        </div>
        
        <h3>The Goal: Learning to Generate</h3>
        
        <p>Instead, we often care about producing more examples that are like those already in a database, but not exactly the same. We could:</p>
        
        <ul style="margin: 20px 0; padding-left: 30px;">
            <li>Start with a database of raw images and synthesize new, unseen images</li>
            <li>Take a database of 3D models of plants and produce more to fill a forest in a video game</li>
            <li>Take handwritten text and try to produce more handwritten text</li>
        </ul>
        
        <div class="visualization">
            <h4>🎯 Generative Modeling Applications</h4>
            <svg width="100%" height="300" viewBox="0 0 1200 300">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
                    </linearGradient>
                    <linearGradient id="appGrad1" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#3498db" />
                        <stop offset="100%" style="stop-color:#2980b9" />
                    </linearGradient>
                    <linearGradient id="appGrad2" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#e74c3c" />
                        <stop offset="100%" style="stop-color:#c0392b" />
                    </linearGradient>
                    <linearGradient id="appGrad3" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#2ecc71" />
                        <stop offset="100%" style="stop-color:#27ae60" />
                    </linearGradient>
                    <linearGradient id="appGrad4" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#f39c12" />
                        <stop offset="100%" style="stop-color:#e67e22" />
                    </linearGradient>
                </defs>
                <rect width="1200" height="300" fill="url(#bgGrad1)"/>
                
                <!-- Image Generation -->
                <rect x="50" y="50" width="220" height="120" fill="url(#appGrad1)" rx="15"/>
                <text x="160" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Image Generation</text>
                <text x="160" y="105" text-anchor="middle" font-size="12" fill="white">Faces, Art, Landscapes</text>
                <text x="160" y="125" text-anchor="middle" font-size="12" fill="white">Medical Images</text>
                <text x="160" y="145" text-anchor="middle" font-size="12" fill="white">Synthetic Datasets</text>
                
                <!-- Text Generation -->
                <rect x="300" y="50" width="220" height="120" fill="url(#appGrad2)" rx="15"/>
                <text x="410" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Text Generation</text>
                <text x="410" y="105" text-anchor="middle" font-size="12" fill="white">Handwriting Synthesis</text>
                <text x="410" y="125" text-anchor="middle" font-size="12" fill="white">Document Generation</text>
                <text x="410" y="145" text-anchor="middle" font-size="12" fill="white">Creative Writing</text>
                
                <!-- 3D Models -->
                <rect x="550" y="50" width="220" height="120" fill="url(#appGrad3)" rx="15"/>
                <text x="660" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">3D Model Generation</text>
                <text x="660" y="105" text-anchor="middle" font-size="12" fill="white">Game Assets</text>
                <text x="660" y="125" text-anchor="middle" font-size="12" fill="white">Architecture</text>
                <text x="660" y="145" text-anchor="middle" font-size="12" fill="white">Product Design</text>
                
                <!-- Scientific Data -->
                <rect x="800" y="50" width="220" height="120" fill="url(#appGrad4)" rx="15"/>
                <text x="910" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Scientific Data</text>
                <text x="910" y="105" text-anchor="middle" font-size="12" fill="white">Molecular Structures</text>
                <text x="910" y="125" text-anchor="middle" font-size="12" fill="white">Climate Patterns</text>
                <text x="910" y="145" text-anchor="middle" font-size="12" fill="white">Genomic Sequences</text>
                
                <!-- Central concept -->
                <circle cx="600" cy="220" r="60" fill="rgba(255,255,255,0.9)" stroke="#2c3e50" stroke-width="3"/>
                <text x="600" y="215" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Learn P(X)</text>
                <text x="600" y="235" text-anchor="middle" font-size="12" fill="#2c3e50">Generate Similar</text>
            </svg>
        </div>
        
        <p>We can formalize this setup: we get examples $X$ distributed according to some unknown distribution $P_{gt}(X)$, and our goal is to learn a model $P$ which we can sample from, such that $P$ is as similar as possible to $P_{gt}$.</p>
        
        <h3>Historical Challenges</h3>
        
        <p>Training this type of model has been a long-standing problem in the machine learning community. Classically, most approaches have had one of three serious drawbacks:</p>
        
        <div class="highlight">
            <h4>Three Historical Problems</h4>
            <ol style="margin-left: 20px;">
                <li><strong>Strong Assumptions:</strong> They might require strong assumptions about the structure in the data</li>
                <li><strong>Severe Approximations:</strong> They might make severe approximations, leading to sub-optimal models</li>
                <li><strong>Expensive Inference:</strong> They might rely on computationally expensive inference procedures like Markov Chain Monte Carlo</li>
            </ol>
        </div>
        
        <p>More recently, advances in training neural networks as powerful function approximators through backpropagation have given rise to promising frameworks which can use backpropagation-based function approximators to build generative models.</p>
        
        <div class="key-concept">
            <h4>Why VAEs Are Popular</h4>
            <p>One of the most popular such frameworks is the <strong>Variational Autoencoder</strong>. The assumptions of this model are weak, and training is fast via backpropagation. VAEs do make an approximation, but the error introduced by this approximation is arguably small given high-capacity models.</p>
        </div>
        
        <div class="section-divider"></div>
        
        <h2 id="latent-models">2. Latent Variable Models</h2>
        
        <p>When training a generative model, the more complicated the dependencies between the dimensions, the more difficult the models are to train.</p>
        
        <h3>Motivating Example: Handwritten Digits</h3>
        
        <p>Take, for example, the problem of generating images of handwritten characters. Say for simplicity that we only care about modeling the digits 0-9.</p>
        
        <div class="visualization">
            <h4>🔤 The Handwritten Digit Challenge</h4>
            <svg width="100%" height="250" viewBox="0 0 900 250">
                <!-- Problem illustration -->
                <rect x="50" y="50" width="150" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                <text x="125" y="40" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Problematic Generation</text>
                
                <!-- Left half of 5 -->
                <path d="M 70 80 L 70 160 L 110 160 L 110 140 L 90 140 L 90 100 L 150 100" stroke="#e74c3c" stroke-width="4" fill="none"/>
                <text x="70" y="75" font-size="10" fill="#e74c3c">Left half of "5"</text>
                
                <!-- Right half of 0 -->
                <ellipse cx="140" cy="120" rx="15" ry="40" fill="none" stroke="#3498db" stroke-width="4" stroke-dasharray="5,5"/>
                <text x="155" y="180" font-size="10" fill="#3498db">Right half of "0"</text>
                
                <text x="125" y="220" text-anchor="middle" font-size="12" fill="#e74c3c">❌ Inconsistent!</text>
                
                <!-- Arrow -->
                <path d="M 220 125 L 280 125" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                <text x="250" y="115" text-anchor="middle" font-size="12" fill="#2c3e50">Solution</text>
                
                <!-- Correct approach -->
                <rect x="300" y="50" width="150" height="150" fill="#d5f4e6" stroke="#27ae60" stroke-width="2" rx="10"/>
                <text x="375" y="40" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Latent Variable Approach</text>
                
                <!-- Latent decision -->
                <circle cx="375" cy="90" r="25" fill="#f39c12" opacity="0.8"/>
                <text x="375" y="95" text-anchor="middle" font-size="12" fill="white">z="5"</text>
                
                <!-- Arrow down -->
                <path d="M 375 115 L 375 135" stroke="#f39c12" stroke-width="3" marker-end="url(#arrowhead)"/>
                
                <!-- Complete 5 -->
                <path d="M 340 150 L 340 180 L 380 180 L 380 165 L 355 165 L 355 150 L 410 150 L 410 135 L 340 135" stroke="#27ae60" stroke-width="4" fill="none"/>
                <text x="375" y="220" text-anchor="middle" font-size="12" fill="#27ae60">✅ Consistent!</text>
                
                <!-- Process explanation -->
                <rect x="500" y="70" width="350" height="110" fill="#f8f9fa" stroke="#667eea" stroke-width="2" rx="10"/>
                <text x="675" y="95" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Latent Variable Process</text>
                <text x="520" y="120" font-size="12" fill="#2c3e50">1. First decide digit identity (z)</text>
                <text x="520" y="140" font-size="12" fill="#2c3e50">2. Then generate pixels consistent with z</text>
                <text x="520" y="160" font-size="12" fill="#2c3e50">3. Ensures global consistency</text>
                
                <!-- Arrow markers -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <div class="key-concept">
            <h4>The Latent Variable Insight</h4>
            <p>If the left half of the character contains the left half of a 5, then the right half cannot contain the right half of a 0, or the character will not look like any real digit. Intuitively, it helps if the model first decides which character to generate before it assigns a value to any specific pixel. This kind of decision is formally called a <strong>latent variable</strong>.</p>
        </div>
        
        <p>That is, before our model draws anything, it first randomly samples a digit value $z$ from the set $[0, ..., 9]$, and then makes sure all the strokes match that character. $z$ is called 'latent' because given just a character produced by the model, we don't necessarily know which settings of the latent variables generated the character.</p>
        
        <h3>Formal Mathematical Framework</h3>
        
        <p>Before we can say that our model is representative of our dataset, we need to make sure that for every datapoint $X$ in the dataset, there is one (or many) settings of the latent variables which causes the model to generate something very similar to $X$.</p>
        
        <p>Formally, say we have a vector of latent variables $z$ in a high-dimensional space $\mathcal{Z}$ which we can easily sample according to some probability density function (PDF) $P(z)$ defined over $\mathcal{Z}$. Then, say we have a family of deterministic functions $f(z; \theta)$, parameterized by a vector $\theta$ in some space $\Theta$, where $f : \mathcal{Z} \times \Theta \rightarrow \mathcal{X}$.</p>
        
        <div class="math-display">
            <h4>Maximum Likelihood Objective</h4>
            $$P(X) = \int P(X|z; \theta) P(z) dz \qquad (1)$$
        </div>
        
        <p>Here, $f(z; \theta)$ has been replaced by a distribution $P(X|z; \theta)$, which allows us to make the dependence of $X$ on $z$ explicit by using the law of total probability. The intuition behind this framework—called "maximum likelihood"—is that if the model is likely to produce training set samples, then it is also likely to produce similar samples, and unlikely to produce dissimilar ones.</p>
        
        <h3>Choice of Output Distribution</h3>
        
        <p>In VAEs, the choice of this output distribution is often Gaussian:</p>
        
        <div class="math-display">
            $$P(X|z; \theta) = \mathcal{N}(X | f(z; \theta), \sigma^2 \cdot I)$$
        </div>
        
        <p>That is, it has mean $f(z; \theta)$ and covariance equal to the identity matrix $I$ times some scalar $\sigma$ (which is a hyperparameter).</p>
        
        <div class="warning">
            <h4>Why Not Deterministic?</h4>
            <p>This replacement is necessary to formalize the intuition that some $z$ needs to result in samples that are merely <em>like</em> $X$. In general, and particularly early in training, our model will not produce outputs that are identical to any particular $X$. By having a Gaussian distribution, we can use gradient descent to increase $P(X)$ by making $f(z; \theta)$ approach $X$ for some $z$. This wouldn't be possible if $P(X|z)$ was a Dirac delta function!</p>
        </div>
        
        <div class="highlight">
            <h4>Flexibility of Output Distributions</h4>
            <p>Note that the output distribution is not required to be Gaussian: for instance, if $X$ is binary, then $P(X|z)$ might be a Bernoulli parameterized by $f(z; \theta)$. The important property is simply that $P(X|z)$ can be computed, and is continuous in $\theta$.</p>
        </div>
        
        <div class="section-divider"></div>
        
        <h2 id="variational-framework">3. The Variational Framework</h2>
        
        <p>The mathematical basis of VAEs actually has relatively little to do with classical autoencoders. VAEs approximately maximize Equation (1), and they are called "autoencoders" only because the final training objective that derives from this setup does have an encoder and a decoder, and resembles a traditional autoencoder.</p>
        
        <h3>The Two Core Problems</h3>
        
        <p>To solve Equation (1), there are two problems that VAEs must deal with:</p>
        
        <ol style="margin: 20px 0; padding-left: 30px;">
            <li><strong>How to define the latent variables $z$</strong> (i.e., decide what information they represent)</li>
            <li><strong>How to deal with the integral over $z$</strong></li>
        </ol>
        
        <h3>VAE's Approach to Latent Variables</h3>
        
        <p>Returning to our digits example, the 'latent' decisions that the model needs to make before it begins painting the digit are actually rather complicated. It needs to choose not just the digit, but the angle that the digit is drawn, the stroke width, and also abstract stylistic properties. Worse, these properties may be correlated: a more angled digit may result if one writes faster, which also might tend to result in a thinner stroke.</p>
        
        <div class="key-concept">
            <h4>VAE's Elegant Solution</h4>
            <p>VAEs take an unusual approach: they assume that there is no simple interpretation of the dimensions of $z$, and instead assert that samples of $z$ can be drawn from a simple distribution, i.e., $\mathcal{N}(0, I)$, where $I$ is the identity matrix.</p>
        </div>
        
        <div class="visualization">
            <h4>🔄 Transformation of Distributions</h4>
            <svg width="100%" height="350" viewBox="0 0 1000 350">
                <!-- Background -->
                <rect width="1000" height="350" fill="rgba(102, 126, 234, 0.05)"/>
                
                <!-- Simple Gaussian -->
                <circle cx="200" cy="175" r="80" fill="none" stroke="#3498db" stroke-width="3"/>
                <circle cx="200" cy="175" r="50" fill="none" stroke="#3498db" stroke-width="2" opacity="0.6"/>
                <circle cx="200" cy="175" r="20" fill="#3498db" opacity="0.3"/>
                <text x="200" y="100" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Simple Distribution</text>
                <text x="200" y="120" text-anchor="middle" font-size="14" fill="#2c3e50">z ~ N(0, I)</text>
                <text x="200" y="270" text-anchor="middle" font-size="12" fill="#7f8c8d">Easy to sample from</text>
                
                <!-- Arrow and transformation -->
                <path d="M 320 175 L 380 175" stroke="#2c3e50" stroke-width="4" marker-end="url(#arrowhead2)"/>
                <text x="350" y="160" text-anchor="middle" font-size="14" fill="#2c3e50">Neural Network</text>
                <text x="350" y="200" text-anchor="middle" font-size="12" fill="#2c3e50">f(z; θ)</text>
                
                <!-- Complex distribution -->
                <path d="M 450 175 Q 500 120 550 175 Q 600 230 650 175 Q 700 120 750 175" stroke="#e74c3c" stroke-width="3" fill="none"/>
                <path d="M 480 175 Q 520 140 560 175 Q 600 210 640 175 Q 680 140 720 175" stroke="#e74c3c" stroke-width="2" fill="none" opacity="0.6"/>
                <ellipse cx="600" cy="175" rx="130" ry="40" fill="#e74c3c" opacity="0.2"/>
                <text x="600" y="100" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Complex Distribution</text>
                <text x="600" y="120" text-anchor="middle" font-size="14" fill="#2c3e50">X = f(z)</text>
                <text x="600" y="270" text-anchor="middle" font-size="12" fill="#7f8c8d">Matches data distribution</text>
                
                <!-- Examples -->
                <rect x="50" y="300" width="300" height="40" fill="#f8f9fa" stroke="#bdc3c7" rx="5"/>
                <text x="200" y="325" text-anchor="middle" font-size="12" fill="#2c3e50">Examples: Ring, spiral, manifold shapes</text>
                
                <rect x="650" y="300" width="300" height="40" fill="#f8f9fa" stroke="#bdc3c7" rx="5"/>
                <text x="800" y="325" text-anchor="middle" font-size="12" fill="#2c3e50">Examples: Images, text, audio data</text>
                
                <defs>
                    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <p>How is this possible? The key is to notice that any distribution in $d$ dimensions can be generated by taking a set of $d$ variables that are normally distributed and mapping them through a sufficiently complicated function. For example, if we wanted to construct a 2D random variable whose values lie on a ring, we could use $g(z) = z/10 + z/||z||$ where $z$ is 2D and normally distributed.</p>
        
        <h3>The Sampling Problem</h3>
        
        <p>Now all that remains is to maximize Equation (1), where $P(z) = \mathcal{N}(z|0, I)$. It is conceptually straightforward to compute $P(X)$ approximately: we first sample a large number of $z$ values $\{z_1, \ldots, z_n\}$, and compute $P(X) \approx \frac{1}{n} \sum_i P(X|z_i)$.</p>
        
        <div class="warning">
            <h4>⚠️ The High-Dimensional Problem</h4>
            <p>The problem here is that in high dimensional spaces, $n$ might need to be extremely large before we have an accurate estimate of $P(X)$. In practice, for most $z$, $P(X|z)$ will be nearly zero, and hence contribute almost nothing to our estimate of $P(X)$.</p>
        </div>
        
        <div class="visualization">
            <h4>📊 Sampling Efficiency Problem</h4>
            <svg width="100%" height="300" viewBox="0 0 1000 300">
                <!-- Background -->
                <rect width="1000" height="300" fill="rgba(231, 76, 60, 0.05)"/>
                
                <!-- Prior samples -->
                <circle cx="150" cy="150" r="100" fill="none" stroke="#3498db" stroke-width="2"/>
                <text x="150" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Prior P(z)</text>
                
                <!-- Many scattered points -->
                <circle cx="120" cy="120" r="3" fill="#3498db"/>
                <circle cx="180" cy="130" r="3" fill="#3498db"/>
                <circle cx="110" cy="180" r="3" fill="#3498db"/>
                <circle cx="190" cy="170" r="3" fill="#3498db"/>
                <circle cx="130" cy="200" r="3" fill="#3498db"/>
                <circle cx="170" cy="100" r="3" fill="#3498db"/>
                <circle cx="140" cy="160" r="3" fill="#3498db"/>
                <circle cx="160" cy="140" r="3" fill="#3498db"/>
                <text x="150" y="280" text-anchor="middle" font-size="12" fill="#7f8c8d">Most samples contribute little</text>
                
                <!-- Arrow -->
                <path d="M 280 150 L 350 150" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead3)"/>
                <text x="315" y="135" text-anchor="middle" font-size="12" fill="#2c3e50">Need smarter</text>
                <text x="315" y="165" text-anchor="middle" font-size="12" fill="#2c3e50">sampling</text>
                
                <!-- Posterior samples -->
                <ellipse cx="550" cy="150" rx="80" ry="40" fill="none" stroke="#e74c3c" stroke-width="2"/>
                <text x="550" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Q(z|X)</text>
                
                <!-- Concentrated points -->
                <circle cx="530" cy="140" r="3" fill="#e74c3c"/>
                <circle cx="540" cy="150" r="3" fill="#e74c3c"/>
                <circle cx="560" cy="145" r="3" fill="#e74c3c"/>
                <circle cx="550" cy="160" r="3" fill="#e74c3c"/>
                <circle cx="570" cy="155" r="3" fill="#e74c3c"/>
                <text x="550" y="280" text-anchor="middle" font-size="12" fill="#7f8c8d">Samples likely to produce X</text>
                
                <!-- Key insight -->
                <rect x="700" y="100" width="250" height="100" fill="#f8f9fa" stroke="#667eea" stroke-width="2" rx="10"/>
                <text x="825" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Insight</text>
                <text x="720" y="150" font-size="11" fill="#2c3e50">Sample from Q(z|X) instead</text>
                <text x="720" y="170" font-size="11" fill="#2c3e50">of P(z) to get better</text>
                <text x="720" y="190" font-size="11" fill="#2c3e50">estimates of P(X)</text>
                
                <defs>
                    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <h3>Variational Inference: The Core Idea</h3>
        
        <p>The key idea behind the variational autoencoder is to attempt to sample values of $z$ that are likely to have produced $X$, and compute $P(X)$ just from those. This means that we need a new function $Q(z|X)$ which can take a value of $X$ and give us a distribution over $z$ values that are likely to produce $X$.</p>
        
        <h3>ELBO Derivation</h3>
        
        <p>The relationship between $\mathbb{E}_{z \sim Q} P(X|z)$ and $P(X)$ is one of the cornerstones of variational Bayesian methods. We begin with the definition of Kullback-Leibler divergence between $P(z|X)$ and $Q(z)$:</p>
        
        <div class="math-display">
            $$D_{KL}[Q(z) \| P(z|X)] = \mathbb{E}_{z \sim Q}[\log Q(z) - \log P(z|X)]$$
        </div>
        
        <p>We can get both $P(X)$ and $P(X|z)$ into this equation by applying Bayes rule to $P(z|X)$:</p>
        
        <div class="math-display">
            $$D_{KL}[Q(z) \| P(z|X)] = \mathbb{E}_{z \sim Q}[\log Q(z) - \log P(X|z) - \log P(z)] + \log P(X)$$
        </div>
        
        <p>Here, $\log P(X)$ comes out of the expectation because it does not depend on $z$. Negating both sides, rearranging, and contracting part of $\mathbb{E}_{z \sim Q}$ into a KL-divergence term yields:</p>
        
        <div class="equation-box">
            <h4>The ELBO (Evidence Lower BOund)</h4>
            $$\log P(X) - D_{KL}[Q(z|X) \| P(z|X)] = \mathbb{E}_{z \sim Q}[\log P(X|z)] - D_{KL}[Q(z|X) \| P(z)]$$
        </div>
        
        <div class="key-concept">
            <h4>Understanding the ELBO</h4>
            <p>This equation serves as the core of the variational autoencoder. The <strong>left hand side</strong> has the quantity we want to maximize: $\log P(X)$ (plus an error term). The <strong>right hand side</strong> is something we can optimize via stochastic gradient descent given the right choice of $Q$.</p>
        </div>
        
        <div class="visualization">
            <h4>🧮 ELBO Components Breakdown</h4>
            <svg width="100%" height="400" viewBox="0 0 1200 400">
                <!-- Background -->
                <rect width="1200" height="400" fill="rgba(102, 126, 234, 0.05)"/>
                
                <!-- Left side: What we want -->
                <rect x="50" y="50" width="300" height="300" fill="rgba(46, 204, 113, 0.1)" stroke="#27ae60" stroke-width="3" rx="15"/>
                <text x="200" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">WHAT WE WANT</text>
                
                <rect x="80" y="110" width="240" height="60" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="8"/>
                <text x="200" y="135" text-anchor="middle" font-size="14" fill="#2c3e50">log P(X)</text>
                <text x="200" y="155" text-anchor="middle" font-size="12" fill="#7f8c8d">Data likelihood</text>
                
                <text x="200" y="200" text-anchor="middle" font-size="20" fill="#2c3e50">−</text>
                
                <rect x="80" y="220" width="240" height="60" fill="#ffffff" stroke="#e74c3c" stroke-width="2" rx="8"/>
                <text x="200" y="245" text-anchor="middle" font-size="14" fill="#2c3e50">D[Q(z|X) || P(z|X)]</text>
                <text x="200" y="265" text-anchor="middle" font-size="12" fill="#7f8c8d">Approximation error</text>
                
                <!-- Equals sign -->
                <text x="400" y="200" text-anchor="middle" font-size="30" font-weight="bold" fill="#2c3e50">=</text>
                
                <!-- Right side: What we can compute -->
                <rect x="450" y="50" width="300" height="300" fill="rgba(52, 152, 219, 0.1)" stroke="#3498db" stroke-width="3" rx="15"/>
                <text x="600" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">WHAT WE CAN COMPUTE</text>
                
                <rect x="480" y="110" width="240" height="60" fill="#ffffff" stroke="#3498db" stroke-width="2" rx="8"/>
                <text x="600" y="135" text-anchor="middle" font-size="14" fill="#2c3e50">E[log P(X|z)]</text>
                <text x="600" y="155" text-anchor="middle" font-size="12" fill="#7f8c8d">Reconstruction</text>
                
                <text x="600" y="200" text-anchor="middle" font-size="20" fill="#2c3e50">−</text>
                
                <rect x="480" y="220" width="240" height="60" fill="#ffffff" stroke="#9b59b6" stroke-width="2" rx="8"/>
                <text x="600" y="245" text-anchor="middle" font-size="14" fill="#2c3e50">D[Q(z|X) || P(z)]</text>
                <text x="600" y="265" text-anchor="middle" font-size="12" fill="#7f8c8d">Regularization</text>
                
                <!-- Explanation boxes -->
                <rect x="800" y="100" width="350" height="80" fill="#f8f9fa" stroke="#667eea" stroke-width="2" rx="10"/>
                <text x="975" y="125" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Reconstruction Term</text>
                <text x="820" y="150" font-size="12" fill="#2c3e50">How well can we reconstruct X</text>
                <text x="820" y="165" font-size="12" fill="#2c3e50">from z sampled from Q(z|X)?</text>
                
                <rect x="800" y="220" width="350" height="80" fill="#f8f9fa" stroke="#667eea" stroke-width="2" rx="10"/>
                <text x="975" y="245" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Regularization Term</text>
                <text x="820" y="270" font-size="12" fill="#2c3e50">How close is Q(z|X) to the</text>
                <text x="820" y="285" font-size="12" fill="#2c3e50">prior P(z) = N(0,I)?</text>
                
                <!-- Bottom explanation -->
                <rect x="200" y="370" width="800" height="25" fill="rgba(155, 89, 182, 0.1)" rx="5"/>
                <text x="600" y="387" text-anchor="middle" font-size="13" fill="#2c3e50">The ELBO is a lower bound on log P(X) that we can actually optimize!</text>
            </svg>
        </div>
        
        <p>Note that the framework—in particular, the right hand side—has suddenly taken a form which looks like an autoencoder, since $Q$ is "encoding" $X$ into $z$, and $P$ is "decoding" it to reconstruct $X$.</p>
        
        <h3>Practical Implementation</h3>
        
        <p>Starting with the left hand side, we are maximizing $\log P(X)$ while simultaneously minimizing $D_{KL}[Q(z|X) \| P(z|X)]$. $P(z|X)$ is not something we can compute analytically: it describes the values of $z$ that are likely to give rise to a sample like $X$ under our model. However, the second term on the left is pulling $Q(z|X)$ to match $P(z|X)$. Assuming we use an arbitrarily high-capacity model for $Q(z|X)$, then $Q(z|X)$ will hopefully actually match $P(z|X)$, in which case this KL-divergence term will be zero, and we will be directly optimizing $\log P(X)$.</p>
        
        <div class="info-theory">
            <h4>📊 Information Theory Perspective</h4>
            <p>The right hand side of the ELBO can be viewed as a two-step process to construct $X$:</p>
            <ol style="margin-left: 20px;">
                <li><strong>Step 1:</strong> Use some bits to construct $z$ (measured by $D_{KL}[Q(z|X) \| P(z)]$)</li>
                <li><strong>Step 2:</strong> Use $z$ to reconstruct $X$ (measured by $-\mathbb{E}[\log P(X|z)]$)</li>
            </ol>
            <p>The total number of bits ($-\log P(X)$) is the sum of these two steps, minus a penalty for $Q$ being sub-optimal.</p>
        </div>
        
        <div class="section-divider"></div>
        
        <h2 id="reparameterization">4. The Reparameterization Trick</h2>
        
        <p>How can we perform stochastic gradient descent on the right hand side of the ELBO? First we need to be more specific about the form that $Q(z|X)$ will take.</p>
        
        <h3>Gaussian Variational Families</h3>
        
        <p>The usual choice is to say that $Q(z|X) = \mathcal{N}(z|\mu(X; \phi), \Sigma(X; \phi))$, where $\mu$ and $\Sigma$ are arbitrary deterministic functions with parameters $\phi$ that can be learned from data. In practice, $\mu$ and $\Sigma$ are implemented via neural networks, and $\Sigma$ is constrained to be a diagonal matrix.</p>
        
        <div class="math-display">
            <h4>KL Divergence for Gaussians</h4>
            $$D_{KL}[\mathcal{N}(\mu(X), \Sigma(X)) \| \mathcal{N}(0, I)] = \frac{1}{2}[\text{tr}(\Sigma(X)) + \mu(X)^T\mu(X) - k - \log \det(\Sigma(X))]$$
        </div>
        
        <p>where $k$ is the dimensionality of the distribution. This can be computed in closed form!</p>
        
        <h3>The Gradient Problem</h3>
        
        <p>The first term on the right hand side of the ELBO is more tricky. We could use sampling to estimate $\mathbb{E}_{z \sim Q}[\log P(X|z)]$, but getting a good estimate would require passing many samples of $z$ through $f$, which would be expensive. Hence, as is standard in stochastic gradient descent, we take one sample of $z$ and treat $\log P(X|z)$ for that $z$ as an approximation.</p>
        
        <div class="warning">
            <h4>⚠️ The Backpropagation Problem</h4>
            <p>There is a significant problem: $\mathbb{E}_{z \sim Q}[\log P(X|z)]$ depends not just on the parameters of $P$, but also on the parameters of $Q$. However, if we simply sample $z$ from $Q(z|X)$, this dependency disappears! We need to drive $Q$ to produce codes for $X$ that $P$ can reliably decode.</p>
        </div>
        
        <div class="visualization">
            <h4>🚫 The Sampling Problem</h4>
            <svg width="100%" height="350" viewBox="0 0 1200 350">
                <!-- Background -->
                <rect width="1200" height="350" fill="rgba(231, 76, 60, 0.05)"/>
                
                <!-- Problematic network -->
                <rect x="50" y="50" width="500" height="250" fill="#ffffff" stroke="#e74c3c" stroke-width="3" rx="15"/>
                <text x="300" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">❌ PROBLEMATIC APPROACH</text>
                
                <!-- Input X -->
                <rect x="80" y="120" width="60" height="40" fill="#3498db" rx="5"/>
                <text x="110" y="145" text-anchor="middle" font-size="12" fill="white">X</text>
                
                <!-- Encoder -->
                <rect x="170" y="110" width="80" height="60" fill="#f39c12" rx="5"/>
                <text x="210" y="135" text-anchor="middle" font-size="12" fill="white">Encoder</text>
                <text x="210" y="150" text-anchor="middle" font-size="10" fill="white">μ, σ</text>
                
                <!-- Sampling (non-differentiable) -->
                <circle cx="300" cy="140" r="30" fill="#e74c3c"/>
                <text x="300" y="135" text-anchor="middle" font-size="10" fill="white">Sample</text>
                <text x="300" y="150" text-anchor="middle" font-size="10" fill="white">z ~ Q</text>
                <text x="300" y="190" text-anchor="middle" font-size="12" fill="#e74c3c">⚠️ No gradient!</text>
                
                <!-- Decoder -->
                <rect x="360" y="110" width="80" height="60" fill="#27ae60" rx="5"/>
                <text x="400" y="140" text-anchor="middle" font-size="12" fill="white">Decoder</text>
                
                <!-- Output -->
                <rect x="470" y="120" width="60" height="40" fill="#9b59b6" rx="5"/>
                <text x="500" y="145" text-anchor="middle" font-size="12" fill="white">X̂</text>
                
                <!-- Arrows -->
                <path d="M 140 140 L 165 140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 250 140 L 270 140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 330 140 L 355 140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 440 140 L 465 140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                
                <!-- Solution -->
                <rect x="650" y="50" width="500" height="250" fill="#ffffff" stroke="#27ae60" stroke-width="3" rx="15"/>
                <text x="900" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">✅ REPARAMETERIZATION TRICK</text>
                
                <!-- Input X -->
                <rect x="680" y="120" width="60" height="40" fill="#3498db" rx="5"/>
                <text x="710" y="145" text-anchor="middle" font-size="12" fill="white">X</text>
                
                <!-- Encoder -->
                <rect x="770" y="110" width="80" height="60" fill="#f39c12" rx="5"/>
                <text x="810" y="135" text-anchor="middle" font-size="12" fill="white">Encoder</text>
                <text x="810" y="150" text-anchor="middle" font-size="10" fill="white">μ, σ</text>
                
                <!-- External noise -->
                <circle cx="900" cy="100" r="25" fill="#95a5a6"/>
                <text x="900" y="105" text-anchor="middle" font-size="10" fill="white">ε ~ N(0,I)</text>
                
                <!-- Reparameterization -->
                <rect x="870" y="130" width="60" height="30" fill="#3498db" rx="5"/>
                <text x="900" y="150" text-anchor="middle" font-size="10" fill="white">z = μ + σε</text>
                
                <!-- Decoder -->
                <rect x="960" y="110" width="80" height="60" fill="#27ae60" rx="5"/>
                <text x="1000" y="140" text-anchor="middle" font-size="12" fill="white">Decoder</text>
                
                <!-- Output -->
                <rect x="1070" y="120" width="60" height="40" fill="#9b59b6" rx="5"/>
                <text x="1100" y="145" text-anchor="middle" font-size="12" fill="white">X̂</text>
                
                <!-- Arrows -->
                <path d="M 740 140 L 765 140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 850 140 L 865 145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 875 125 L 890 135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 930 145 L 955 145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <path d="M 1040 140 L 1065 140" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead4)"/>
                
                <text x="900" y="250" text-anchor="middle" font-size="12" fill="#27ae60">✅ Gradients flow through z = μ + σε</text>
                
                <defs>
                    <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <h3>The Solution: Reparameterization</h3>
        
        <p>The solution, called the "reparameterization trick", is to move the sampling to an input layer. Given $\mu(X)$ and $\Sigma(X)$—the mean and covariance of $Q(z|X)$—we can sample from $\mathcal{N}(\mu(X), \Sigma(X))$ by first sampling $\epsilon \sim \mathcal{N}(0, I)$, then computing:</p>
        
        <div class="equation-box">
            <h4>Reparameterization Formula</h4>
            $$z = \mu(X) + \Sigma^{1/2}(X) \odot \epsilon$$
        </div>
        
        <p>where $\odot$ denotes element-wise multiplication. Thus, the equation we actually take the gradient of is:</p>
        
        <div class="math-display">
            $$\mathbb{E}_{X \sim D}\left[\mathbb{E}_{\epsilon \sim \mathcal{N}(0,I)}[\log P(X|z = \mu(X) + \Sigma^{1/2}(X) \odot \epsilon)] - D_{KL}[Q(z|X) \| P(z)]\right]$$
        </div>
        
        <div class="key-concept">
            <h4>Why This Works</h4>
            <p>Note that none of the expectations are with respect to distributions that depend on our model parameters, so we can safely move a gradient symbol into them while maintaining equality. Given a fixed $X$ and $\epsilon$, this function is deterministic and continuous in the parameters of $P$ and $Q$, meaning backpropagation can compute a gradient that will work for stochastic gradient descent.</p>
        </div>
        
        <h3>Implementation Details</h3>
        
        <p>In practice, we take one sample of $X$ and one sample of $\epsilon$, and compute the gradient of:</p>
        
        <div class="math-display">
            $$\log P(X|z = \mu(X) + \Sigma^{1/2}(X) \odot \epsilon) - D_{KL}[Q(z|X) \| P(z)]$$
        </div>
        
        <div class="highlight">
            <h4>Constraints on the Reparameterization Trick</h4>
            <p>The "reparameterization trick" only works if we can sample from $Q(z|X)$ by evaluating a function $h(\eta, X)$, where $\eta$ is noise from a distribution that is not learned. Furthermore, $h$ must be continuous in $X$ so that we can backprop through it. This means $Q(z|X)$ (and therefore $P(z)$) can't be a discrete distribution!</p>
        </div>
        
        <div class="visualization">
            <h4>🔄 Complete VAE Architecture</h4>
            <svg width="100%" height="400" viewBox="0 0 1200 400">
                <!-- Background -->
                <rect width="1200" height="400" fill="rgba(102, 126, 234, 0.05)"/>
                
                <!-- Training time -->
                <text x="600" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">Complete VAE Training Architecture</text>
                
                <!-- Input -->
                <rect x="50" y="180" width="80" height="60" fill="#3498db" rx="10"/>
                <text x="90" y="215" text-anchor="middle" font-size="14" fill="white">Input X</text>
                <text x="90" y="270" text-anchor="middle" font-size="12" fill="#7f8c8d">Real data</text>
                
                <!-- Encoder Network -->
                <rect x="180" y="150" width="120" height="120" fill="#f39c12" rx="10"/>
                <text x="240" y="185" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Encoder</text>
                <text x="240" y="205" text-anchor="middle" font-size="12" fill="white">Neural Network</text>
                
                <!-- Mean and variance -->
                <rect x="200" y="220" width="35" height="20" fill="rgba(255,255,255,0.3)" rx="3"/>
                <text x="217" y="233" text-anchor="middle" font-size="10" fill="white">μ</text>
                <rect x="245" y="220" width="35" height="20" fill="rgba(255,255,255,0.3)" rx="3"/>
                <text x="262" y="233" text-anchor="middle" font-size="10" fill="white">σ²</text>
                
                <!-- Random noise -->
                <circle cx="400" cy="120" r="40" fill="#95a5a6"/>
                <text x="400" y="115" text-anchor="middle" font-size="12" fill="white">ε ~ N(0,I)</text>
                <text x="400" y="130" text-anchor="middle" font-size="10" fill="white">External noise</text>
                
                <!-- Reparameterization -->
                <rect x="350" y="180" width="100" height="60" fill="#e67e22" rx="10"/>
                <text x="400" y="200" text-anchor="middle" font-size="12" fill="white">z = μ + σ⊙ε</text>
                <text x="400" y="220" text-anchor="middle" font-size="11" fill="white">Reparameterization</text>
                <text x="400" y="270" text-anchor="middle" font-size="12" fill="#7f8c8d">Latent code</text>
                
                <!-- Decoder Network -->
                <rect x="500" y="150" width="120" height="120" fill="#27ae60" rx="10"/>
                <text x="560" y="185" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Decoder</text>
                <text x="560" y="205" text-anchor="middle" font-size="12" fill="white">Neural Network</text>
                <text x="560" y="225" text-anchor="middle" font-size="12" fill="white">f(z; θ)</text>
                
                <!-- Output -->
                <rect x="670" y="180" width="80" height="60" fill="#9b59b6" rx="10"/>
                <text x="710" y="215" text-anchor="middle" font-size="14" fill="white">Output X̂</text>
                <text x="710" y="270" text-anchor="middle" font-size="12" fill="#7f8c8d">Reconstruction</text>
                
                <!-- Loss components -->
                <rect x="800" y="120" width="150" height="80" fill="#e74c3c" rx="10"/>
                <text x="875" y="145" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Reconstruction Loss</text>
                <text x="875" y="165" text-anchor="middle" font-size="11" fill="white">-log P(X|z)</text>
                <text x="875" y="185" text-anchor="middle" font-size="10" fill="white">||X - X̂||²</text>
                
                <rect x="800" y="220" width="150" height="80" fill="#8e44ad" rx="10"/>
                <text x="875" y="245" text-anchor="middle" font-size="12" font-weight="bold" fill="white">KL Divergence</text>
                <text x="875" y="265" text-anchor="middle" font-size="11" fill="white">D[Q(z|X)||P(z)]</text>
                <text x="875" y="285" text-anchor="middle" font-size="10" fill="white">Regularization</text>
                
                <!-- Total Loss -->
                <rect x="1000" y="170" width="120" height="80" fill="#2c3e50" rx="10"/>
                <text x="1060" y="195" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Total Loss</text>
                <text x="1060" y="215" text-anchor="middle" font-size="11" fill="white">-ELBO</text>
                <text x="1060" y="235" text-anchor="middle" font-size="10" fill="white">Recon + KL</text>
                
                <!-- Arrows -->
                <path d="M 130 210 L 175 210" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead5)"/>
                <path d="M 300 210 L 345 210" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead5)"/>
                <path d="M 380 160 L 400 160" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead5)"/>
                <path d="M 450 210 L 495 210" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead5)"/>
                <path d="M 620 210 L 665 210" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead5)"/>
                <path d="M 750 200 L 795 160" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead5)"/>
                <path d="M 750 220 L 795 260" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead5)"/>
                <path d="M 950 210 L 995 210" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead5)"/>
                
                <!-- Generation path annotation -->
                <path d="M 400 330 L 560 330" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                <text x="480" y="350" text-anchor="middle" font-size="12" fill="#e74c3c">Generation: Sample z ~ N(0,I) → Decoder</text>
                
                <defs>
                    <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <h3>Testing Time</h3>
        
        <p>At test time, when we want to generate new samples, we simply input values of $z \sim \mathcal{N}(0, I)$ into the decoder. That is, we remove the "encoder," including the multiplication and addition operations that would change the distribution of $z$.</p>
        
        <div class="highlight">
            <h4>Model Evaluation</h4>
            <p>Say that we want to evaluate the probability of a testing example under the model. This is, in general, not tractable. However, $D_{KL}[Q(z|X) \| P(z|X)]$ is positive, meaning that the right hand side of the ELBO is a lower bound to $P(X)$. This lower bound can be a useful tool for getting a rough idea of how well our model is capturing a particular datapoint $X$.</p>
        </div>
        
        <div class="section-divider"></div>
        
        <h2 id="conditional-vaes">5. Conditional Variational Autoencoders</h2>
        
        <p>Let's extend our understanding to conditional VAEs (CVAEs), which modify the math by simply conditioning the entire generative process on an input. CVAEs allow us to tackle problems where the input-to-output mapping is one-to-many.</p>
        
        <h3>Motivation: Multi-modal Outputs</h3>
        
        <p>Say that we don't just want to generate new digits, but instead we want to add digits to an existing string of digits written by a single person. This is similar to practical problems in computer graphics like hole filling: given an existing image where a user has removed an unwanted object, the goal is to fill in the hole with plausible-looking pixels.</p>
        
        <div class="warning">
            <h4>⚠️ The Multi-modal Challenge</h4>
            <p>An important difficulty with both problems is that the space of plausible outputs is multi-modal: there are many possibilities for the next digit or the extrapolated pixels. A standard regression model will fail in this situation, because the training objective generally penalizes the distance between a single prediction and the ground truth. The best solution the regressor can produce is something which is <em>in between</em> the possibilities, resulting in meaningless blur.</p>
        </div>
        
        <div class="visualization">
            <h4>🎭 Multi-modal Generation Challenge</h4>
            <svg width="100%" height="300" viewBox="0 0 1200 300">
                <!-- Background -->
                <rect width="1200" height="300" fill="rgba(52, 152, 219, 0.05)"/>
                
                <!-- Input condition -->
                <rect x="50" y="100" width="120" height="100" fill="#3498db" rx="10"/>
                <text x="110" y="130" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Input Condition</text>
                <text x="110" y="150" text-anchor="middle" font-size="12" fill="white">Partial Image</text>
                <text x="110" y="170" text-anchor="middle" font-size="12" fill="white">Text Context</text>
                <text x="110" y="190" text-anchor="middle" font-size="12" fill="white">etc.</text>
                
                <!-- Multiple possible outputs -->
                <rect x="250" y="50" width="100" height="60" fill="#2ecc71" rx="8"/>
                <text x="300" y="75" text-anchor="middle" font-size="12" fill="white">Option 1</text>
                <text x="300" y="90" text-anchor="middle" font-size="10" fill="white">Valid output</text>
                
                <rect x="250" y="120" width="100" height="60" fill="#2ecc71" rx="8"/>
                <text x="300" y="145" text-anchor="middle" font-size="12" fill="white">Option 2</text>
                <text x="300" y="160" text-anchor="middle" font-size="10" fill="white">Valid output</text>
                
                <rect x="250" y="190" width="100" height="60" fill="#2ecc71" rx="8"/>
                <text x="300" y="215" text-anchor="middle" font-size="12" fill="white">Option N</text>
                <text x="300" y="230" text-anchor="middle" font-size="10" fill="white">Valid output</text>
                
                <!-- Regression problem -->
                <rect x="450" y="120" width="120" height="60" fill="#e74c3c" rx="8"/>
                <text x="510" y="140" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Regression</text>
                <text x="510" y="155" text-anchor="middle" font-size="11" fill="white">Average of all</text>
                <text x="510" y="170" text-anchor="middle" font-size="11" fill="white">→ Blurry mess</text>
                
                <!-- CVAE solution -->
                <rect x="650" y="50" width="120" height="200" fill="#9b59b6" rx="10"/>
                <text x="710" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="white">CVAE Solution</text>
                <text x="710" y="105" text-anchor="middle" font-size="11" fill="white">Sample z ~ N(0,I)</text>
                <text x="710" y="125" text-anchor="middle" font-size="11" fill="white">Different z values</text>
                <text x="710" y="145" text-anchor="middle" font-size="11" fill="white">produce different</text>
                <text x="710" y="165" text-anchor="middle" font-size="11" fill="white">valid outputs</text>
                <text x="710" y="190" text-anchor="middle" font-size="11" fill="white">P(Y|X,z)</text>
                <text x="710" y="220" text-anchor="middle" font-size="11" fill="white">Sharp & diverse!</text>
                
                <!-- Multiple CVAE outputs -->
                <rect x="850" y="50" width="80" height="40" fill="#f39c12" rx="5"/>
                <text x="890" y="75" text-anchor="middle" font-size="10" fill="white">Output 1</text>
                
                <rect x="850" y="100" width="80" height="40" fill="#f39c12" rx="5"/>
                <text x="890" y="125" text-anchor="middle" font-size="10" fill="white">Output 2</text>
                
                <rect x="850" y="150" width="80" height="40" fill="#f39c12" rx="5"/>
                <text x="890" y="175" text-anchor="middle" font-size="10" fill="white">Output 3</text>
                
                <rect x="850" y="200" width="80" height="40" fill="#f39c12" rx="5"/>
                <text x="890" y="225" text-anchor="middle" font-size="10" fill="white">Output N</text>
                
                <!-- Arrows -->
                <path d="M 170 150 L 245 80" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead6)"/>
                <path d="M 170 150 L 245 150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead6)"/>
                <path d="M 170 150 L 245 220" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead6)"/>
                <path d="M 350 150 L 445 150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead6)"/>
                <path d="M 770 150 L 845 150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead6)"/>
                
                <!-- Labels -->
                <text x="300" y="35" text-anchor="middle" font-size="12" fill="#27ae60">Multiple valid possibilities</text>
                <text x="510" y="220" text-anchor="middle" font-size="12" fill="#e74c3c">❌ Averages to blur</text>
                <text x="890" y="35" text-anchor="middle" font-size="12" fill="#f39c12">✅ Sharp & diverse</text>
                
                <defs>
                    <marker id="arrowhead6" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <h3>CVAE Mathematical Framework</h3>
        
        <p>Given an input $X$ and an output $Y$, we want to create a model $P(Y|X)$ which maximizes the probability of the ground truth. We define the model by introducing a latent variable $z \sim \mathcal{N}(0, I)$, such that:</p>
        
        <div class="math-display">
            $$P(Y|X) = \int P(Y|z, X) P(z) dz$$
        </div>
        
        <p>where $P(Y|z, X) = \mathcal{N}(Y | f(z, X; \theta), \sigma^2 \cdot I)$ and $f$ is a deterministic function that we can learn from data.</p>
        
        <div class="equation-box">
            <h4>CVAE ELBO</h4>
            $$\log P(Y|X) - D_{KL}[Q(z|Y, X) \| P(z|Y, X)] = \mathbb{E}_{z \sim Q}[\log P(Y|z, X)] - D_{KL}[Q(z|Y, X) \| P(z)]$$
        </div>
        
        <p>Note that $P(z|X)$ is still $\mathcal{N}(0, I)$ because our model assumes $z$ is sampled independently of $X$ at test time.</p>
        
        <div class="visualization">
            <h4>🏗️ CVAE Architecture</h4>
            <svg width="100%" height="350" viewBox="0 0 1200 350">
                <!-- Background -->
                <rect width="1200" height="350" fill="rgba(102, 126, 234, 0.05)"/>
                
                <!-- Training vs Testing -->
                <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">TRAINING TIME</text>
                <text x="900" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">TESTING TIME</text>
                
                <!-- Training side -->
                <rect x="50" y="60" width="500" height="250" fill="#ffffff" stroke="#3498db" stroke-width="2" rx="10"/>
                
                <!-- Input X -->
                <rect x="80" y="120" width="60" height="40" fill="#3498db" rx="5"/>
                <text x="110" y="145" text-anchor="middle" font-size="12" fill="white">X</text>
                
                <!-- Target Y -->
                <rect x="80" y="180" width="60" height="40" fill="#e74c3c" rx="5"/>
                <text x="110" y="205" text-anchor="middle" font-size="12" fill="white">Y</text>
                
                <!-- Encoder (takes both X and Y) -->
                <rect x="200" y="150" width="100" height="70" fill="#f39c12" rx="8"/>
                <text x="250" y="175" text-anchor="middle" font-size="12" fill="white">Encoder</text>
                <text x="250" y="195" text-anchor="middle" font-size="11" fill="white">Q(z|X,Y)</text>
                <text x="250" y="210" text-anchor="middle" font-size="10" fill="white">μ, σ</text>
                
                <!-- Latent z -->
                <circle cx="350" cy="185" r="25" fill="#9b59b6"/>
                <text x="350" y="190" text-anchor="middle" font-size="12" fill="white">z</text>
                
                <!-- Decoder (takes X and z) -->
                <rect x="420" y="150" width="100" height="70" fill="#27ae60" rx="8"/>
                <text x="470" y="175" text-anchor="middle" font-size="12" fill="white">Decoder</text>
                <text x="470" y="195" text-anchor="middle" font-size="11" fill="white">P(Y|X,z)</text>
                <text x="470" y="210" text-anchor="middle" font-size="10" fill="white">f(X,z)</text>
                
                <!-- Testing side -->
                <rect x="650" y="60" width="500" height="250" fill="#ffffff" stroke="#27ae60" stroke-width="2" rx="10"/>
                
                <!-- Input X only -->
                <rect x="680" y="150" width="60" height="40" fill="#3498db" rx="5"/>
                <text x="710" y="175" text-anchor="middle" font-size="12" fill="white">X</text>
                
                <!-- Sample z -->
                <circle cx="800" cy="120" r="30" fill="#95a5a6"/>
                <text x="800" y="115" text-anchor="middle" font-size="11" fill="white">z ~ N(0,I)</text>
                <text x="800" y="130" text-anchor="middle" font-size="10" fill="white">Sample</text>
                
                <!-- Decoder only -->
                <rect x="850" y="150" width="100" height="70" fill="#27ae60" rx="8"/>
                <text x="900" y="175" text-anchor="middle" font-size="12" fill="white">Decoder</text>
                <text x="900" y="195" text-anchor="middle" font-size="11" fill="white">P(Y|X,z)</text>
                <text x="900" y="210" text-anchor="middle" font-size="10" fill="white">f(X,z)</text>
                
                <!-- Generated Y -->
                <rect x="1000" y="150" width="60" height="40" fill="#f39c12" rx="5"/>
                <text x="1030" y="175" text-anchor="middle" font-size="12" fill="white">Ŷ</text>
                
                <!-- Arrows -->
                <path d="M 140 140 L 195 165" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                <path d="M 140 200 L 195 195" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                <path d="M 300 185 L 325 185" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                <path d="M 375 185 L 415 185" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                <path d="M 740 170 L 845 185" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                <path d="M 820 145 L 870 170" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                <path d="M 950 185 L 995 170" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead7)"/>
                
                <!-- Key differences -->
                <text x="250" y="280" text-anchor="middle" font-size="11" fill="#e74c3c">Encoder sees both X and Y</text>
                <text x="900" y="280" text-anchor="middle" font-size="11" fill="#27ae60">Only X and sampled z needed</text>
                
                <defs>
                    <marker id="arrowhead7" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <h3>Testing with CVAEs</h3>
        
        <p>At test time, we can sample from the distribution $P(Y|X)$ by simply sampling $z \sim \mathcal{N}(0, I)$ and passing both $X$ and $z$ through the decoder. Different samples of $z$ will produce different plausible outputs $Y$ conditioned on the same input $X$.</p>
        
        <div class="section-divider"></div>
        
        <h2 id="implementation">6. Implementation Examples & Practical Considerations</h2>
        
        <h3>MNIST VAE Example</h3>
        
        <p>To demonstrate the distribution learning capabilities of the VAE framework, let's consider training a variational autoencoder on MNIST. Although MNIST is real-valued, it is constrained between 0 and 1, so we can use the Sigmoid Cross Entropy loss for $P(X|z)$.</p>
        
        <div class="code-block">
import torch
import torch.nn as nn
import torch.nn.functional as F

class VAE(nn.Module):
    def __init__(self, latent_dim=20):
        super(VAE, self).__init__()
        
        # Encoder
        self.encoder = nn.Sequential(
            nn.Linear(784, 400),
            nn.ReLU(),
            nn.Linear(400, 400),
            nn.ReLU()
        )
        self.fc_mu = nn.Linear(400, latent_dim)
        self.fc_logvar = nn.Linear(400, latent_dim)
        
        # Decoder
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, 400),
            nn.ReLU(),
            nn.Linear(400, 400),
            nn.ReLU(),
            nn.Linear(400, 784),
            nn.Sigmoid()
        )
    
    def encode(self, x):
        h = self.encoder(x)
        return self.fc_mu(h), self.fc_logvar(h)
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        mu, logvar = self.encode(x.view(-1, 784))
        z = self.reparameterize(mu, logvar)
        return self.decode(z), mu, logvar

def loss_function(recon_x, x, mu, logvar):
    # Reconstruction loss
    BCE = F.binary_cross_entropy(recon_x, x.view(-1, 784), reduction='sum')
    
    # KL divergence
    KLD = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    
    return BCE + KLD
        </div>
        
        <h3>Key Practical Considerations</h3>
        
        <div class="highlight">
            <h4>Hyperparameter Sensitivity</h4>
            <ul style="margin-left: 20px;">
                <li><strong>Latent Dimensionality:</strong> The model seems quite insensitive to the dimensionality of $z$, unless $z$ is excessively large or small. Too few dimensions mean the model cannot capture all variation; too many can make optimization difficult.</li>
                <li><strong>Reconstruction vs. Regularization:</strong> The balance between reconstruction accuracy and latent space regularization is crucial for good generation quality.</li>
                <li><strong>Architecture Choices:</strong> Deeper networks generally perform better, but training becomes more challenging.</li>
            </ul>
        </div>
        
        <div class="warning">
            <h4>⚠️ Common Failure Modes</h4>
            <ul style="margin-left: 20px;">
                <li><strong>Posterior Collapse:</strong> The encoder learns to ignore the input and always predicts the prior</li>
                <li><strong>Blurry Reconstructions:</strong> Gaussian output assumptions can lead to averaging effects</li>
                <li><strong>Mode Collapse:</strong> The model fails to capture the full diversity of the data distribution</li>
            </ul>
        </div>
        
        <div class="visualization">
            <h4>📈 Training Dynamics Visualization</h4>
            <div id="training-plot" style="width:100%; height:400px;"></div>
        </div>
        
        <div class="section-divider"></div>
        
        <h2 id="advanced">7. Advanced Topics & Theoretical Insights</h2>
        
        <h3>Regularization and the β-VAE</h3>
        
        <p>Looking at the ELBO, it's interesting to view the $D_{KL}[Q(z|X) \| P(z)]$ as a regularization term. We can introduce a hyperparameter $\beta$ to control the strength of this regularization:</p>
        
        <div class="math-display">
            $$\mathcal{L}_{\beta} = \mathbb{E}_{z \sim Q}[\log P(X|z)] - \beta \cdot D_{KL}[Q(z|X) \| P(z)]$$
        </div>
        
        <div class="key-concept">
            <h4>β-VAE Benefits</h4>
            <ul style="margin-left: 20px;">
                <li><strong>β > 1:</strong> Encourages more disentangled representations</li>
                <li><strong>β < 1:</strong> Prioritizes reconstruction quality</li>
                <li><strong>β = 1:</strong> Standard VAE formulation</li>
            </ul>
        </div>
        
        <h3>Approximation Error Analysis</h3>
        
        <p>The tractability of VAEs relies on our assumption that $Q(z|X)$ can be modeled as a Gaussian. $P(X)$ converges to the true distribution if and only if $D_{KL}[Q(z|X) \| P(z|X)]$ goes to zero. Even if we assume $\mu(X)$ and $\Sigma(X)$ are arbitrarily high capacity, the posterior $P(z|X)$ is not necessarily Gaussian for an arbitrary decoder function.</p>
        
        <div class="info-theory">
            <h4>📊 Theoretical Guarantee</h4>
            <p>However, the good news is that given sufficiently high-capacity neural networks, there are many decoder functions that result in our model generating any given output distribution. Hence, all we need is one function that both maximizes $\log P(X)$ and results in $P(z|X)$ being Gaussian for all $X$. Under certain conditions (particularly when σ is small relative to the curvature of the ground truth distribution's CDF), such a function is guaranteed to exist.</p>
        </div>
        
        <h3>Modern Extensions</h3>
        
        <p>Since the original VAE paper, numerous extensions have been developed:</p>
        
        <div class="highlight">
            <h4>Notable VAE Variants</h4>
            <ul style="margin-left: 20px;">
                <li><strong>WAE (Wasserstein Autoencoder):</strong> Uses Wasserstein distance instead of KL divergence</li>
                <li><strong>β-VAE:</strong> Controllable disentanglement through β weighting</li>
                <li><strong>VQ-VAE:</strong> Vector quantized latent space for discrete representations</li>
                <li><strong>Normalizing Flows:</strong> More flexible posterior approximations</li>
                <li><strong>Hierarchical VAEs:</strong> Multiple levels of latent variables</li>
            </ul>
        </div>
        
        <div class="conclusion">
            <h2>🎯 Conclusion</h2>
            <p>Variational Autoencoders represent a powerful framework for generative modeling that elegantly combines:</p>
            <ul style="margin: 20px 0; text-align: left; display: inline-block;">
                <li><strong>Probabilistic Foundations:</strong> Rigorous mathematical framework based on variational inference</li>
                <li><strong>Practical Implementation:</strong> Trainable with standard backpropagation via the reparameterization trick</li>
                <li><strong>Flexible Applications:</strong> Extensible to conditional generation, representation learning, and more</li>
                <li><strong>Theoretical Insights:</strong> Connections to information theory and Bayesian inference</li>
            </ul>
            <p>While VAEs have limitations (such as blurry reconstructions), they remain a cornerstone of modern generative modeling and continue to inspire new research directions. Understanding VAEs provides essential intuition for more advanced generative models like Normalizing Flows, Diffusion Models, and beyond.</p>
        </div>
    </div>
    
    <script>
        // Initialize page functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('VAE Tutorial loaded successfully');
            
            // Add smooth scrolling for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
            
            // Create training dynamics plot
            createTrainingPlot();
            
            // Add MathJax error handling
            if (typeof MathJax !== 'undefined') {
                MathJax.Hub && MathJax.Hub.Register.MessageHook("Math Processing Error", function (message) {
                    console.warn("MathJax processing error:", message);
                    // Try to identify the problematic element and add a fallback
                    var element = message[1];
                    if (element && element.parentNode) {
                        element.parentNode.style.backgroundColor = '#ffeeee';
                        element.parentNode.title = 'Math rendering error: ' + message[2];
                    }
                });
                
                // For MathJax 3.x
                MathJax.startup && MathJax.startup.promise.then(() => {
                    console.log('MathJax 3.x startup complete');
                }).catch((err) => {
                    console.error('MathJax 3.x startup error:', err);
                });
            }
            
            // Fallback for mathematical expressions that fail to render
            setTimeout(function() {
                document.querySelectorAll('.MathJax_Error, .mjx-error').forEach(function(errorEl) {
                    console.warn('Found MathJax error element:', errorEl);
                    var parent = errorEl.closest('p, div, span');
                    if (parent) {
                        parent.style.backgroundColor = '#fff3cd';
                        parent.style.border = '1px solid #ffeaa7';
                        parent.style.padding = '5px';
                        parent.title = 'Mathematical expression rendering error';
                    }
                });
            }, 3000);
        });
        
        function createTrainingPlot() {
            // Sample training data for VAE
            const epochs = Array.from({length: 100}, (_, i) => i + 1);
            const reconstruction_loss = epochs.map(e => 
                200 * Math.exp(-e/20) + 50 + 10 * Math.sin(e/5) * Math.exp(-e/30)
            );
            const kl_divergence = epochs.map(e => 
                150 * (1 - Math.exp(-e/15)) + 20 * Math.sin(e/8) * Math.exp(-e/25)
            );
            const total_loss = epochs.map((e, i) => reconstruction_loss[i] + kl_divergence[i]);
            
            const trace1 = {
                x: epochs,
                y: reconstruction_loss,
                type: 'scatter',
                mode: 'lines',
                name: 'Reconstruction Loss',
                line: {color: '#e74c3c', width: 3}
            };
            
            const trace2 = {
                x: epochs,
                y: kl_divergence,
                type: 'scatter',
                mode: 'lines',
                name: 'KL Divergence',
                line: {color: '#9b59b6', width: 3}
            };
            
            const trace3 = {
                x: epochs,
                y: total_loss,
                type: 'scatter',
                mode: 'lines',
                name: 'Total Loss (ELBO)',
                line: {color: '#2c3e50', width: 4}
            };
            
            const layout = {
                title: {
                    text: 'VAE Training Dynamics',
                    font: {size: 18, color: '#2c3e50'}
                },
                xaxis: {
                    title: 'Training Epoch',
                    showgrid: true,
                    gridcolor: '#ecf0f1'
                },
                yaxis: {
                    title: 'Loss Value',
                    showgrid: true,
                    gridcolor: '#ecf0f1'
                },
                plot_bgcolor: '#f8f9fa',
                paper_bgcolor: '#ffffff',
                font: {
                    family: 'Segoe UI, sans-serif'
                },
                legend: {
                    x: 0.7,
                    y: 0.9
                }
            };
            
            const config = {
                responsive: true,
                displayModeBar: false
            };
            
            if (document.getElementById('training-plot')) {
                Plotly.newPlot('training-plot', [trace1, trace2, trace3], layout, config);
            }
        }
    </script>
</body>
</html> 