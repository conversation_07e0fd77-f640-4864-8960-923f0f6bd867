10.2.1 Teacher Forcing and Networks with Output Recurrence
 The network with recurrent connections only from the output at one time step to
 the hidden units at the next time step (shown in figure
 10.4
 ) is strictly less powerful
 381
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 because it lacks hidden-to-hidden recurrent connections. For example, it cannot
 simulate a universal Turing machine. Because this network lacks hidden-to-hidden
 recurrence, it requires that the output units capture all of the information about
 the past that the network will use to predict the future. Because the output units
 are explicitly trained to match the training set targets, they are unlikely to capture
 the necessary information about the past history of the input, unless the user
 knows how to describe the full state of the system and provides it as part of the
 training set targets. The advantage of eliminating hidden-to-hidden recurrence
 is that, for any loss function based on comparing the prediction at time t to the
 training target at time t, all the time steps are decoupled. Training can thus be
 parallelized, with the gradient for each step t computed in isolation. There is no
 need to compute the output for the previous time step first, because the training
 set provides the ideal value of that output.
 L( ) τ
 L( ) τ
 y( ) τ
 y( ) τ
 . . .
 . . .
 h(t−1)
 h(t−1)
 W
 W
 U
 x(t−1)
 x(t−1)
 h( ) t
 h( ) t
 U
 x( ) t
 x( ) t
 . . .
 . . .
 W
 U
 ...
 ...
 x( )
 x( )
 W
 τ
 τ
 o( )
 o( )
 V
 h( ) τ
 h( ) τ
 U
 x( ) τ
 x( ) τ
 Figure 10.5: Time-unfolded recurrent neural network with a single output at the end
 of the sequence. Such a network can be used to summarize a sequence and produce a
 f
 ixed-size representation used as input for further processing. There might be a target
 right at the end (as depicted here) or the gradient on the outputo( ) t can be obtained by
 back-propagating from further downstream modules.
 Models that have recurrent connections from their outputs leading back into
 the model may be trained with teacher forcing. Teacher forcing is a procedure
 that emerges from the maximum likelihood criterion, in which during training the
 model receives the ground truth output y( ) t as input at time t + 1. We can see
 this by examining a sequence with two time steps. The conditional maximum
 382
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 o(t−1) o(t−1) o() t o() t
 h(t−1) h(t−1) h() t h() t
 x(t−1) x(t−1) x() t x() t
 W
 V V
 U U
 o(t−1) o(t−1) o() t o() t
 L(t−1) L(t−1) L() t L() t
 y(t−1) y(t−1) y() t y() t
 h(t−1) h(t−1) h() t h() t
 x(t−1) x(t−1) x() t x() t
 W
 V V
 U U
 Train time Test time
 Figure10.6: Illustrationofteacherforcing.Teacherforcingisatrainingtechniquethat is
 applicabletoRNNsthathaveconnectionsfromtheiroutputtotheirhiddenstatesatthe
 nexttimestep. (Left)Attraintime,wefeedthecorrectoutputy() t drawnfromthetrain
 setas inputtoh(+1) t . Whenthemodel isdeployed,thetrueoutputisgenerally (Right)
 notknown. Inthiscase,weapproximatethecorrectoutputy() t withthemodel’soutput
 o() t,andfeedtheoutputbackintothemodel.
 383
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 likelihood criterion is
 
 logp
 =logp
 
 y(1), y(2) | x(1),x(2)
 y(2) | y(1),x(1),x(2)
 
 
 (10.15)
 
 +logp
 y(1) | x(1),x(2)
 
 (10.16)
 In this example, we see that at time t = 2, the model is trained to maximize the
 conditional probability of y(2) given both the x sequence so far and the previous y
 value from the training set. Maximum likelihood thus specifies that during training,
 rather than feeding the model’s own output back into itself, these connections
 should be fed with the target values specifying what the correct output should be.
 This is illustrated in figure
 10.6
 .
 Weoriginally motivated teacher forcing as allowing us to avoid back-propagation
 through time in models that lack hidden-to-hidden connections. Teacher forcing
 may still be applied to models that have hidden-to-hidden connections so long as
 they have connections from the output at one time step to values computed in the
 next time step. However, as soon as the hidden units become a function of earlier
 time steps, the BPTT algorithm is necessary. Some models may thus be trained
 with both teacher forcing and BPTT.
 The disadvantage of strict teacher forcing arises if the network is going to be
 later used in an open-loop mode, with the network outputs (or samples from
 the output distribution) fed back as input. In this case, the kind of inputs that
 the network sees during training could be quite different from the kind of inputs
 that it will see at test time. One way to mitigate this problem is to train with
 both teacher-forced inputs and with free-running inputs, for example by predicting
 the correct target a number of steps in the future through the unfolded recurrent
 output-to-input paths. In this way, the network can learn to take into account
 input conditions (such as those it generates itself in the free-running mode) not
 seen during training and how to map the state back towards one that will make
 2015b
 the network generate proper outputs after a few steps. Another approach (Bengio
 et al.,
 ) to mitigate the gap between the inputs seen at train time and the
 inputs seen at test time randomly chooses to use generated values or actual data
 values as input. This approach exploits a curriculum learning strategy to gradually
 use more of the generated values as input.
 10.2.2 Computing the Gradient in a Recurrent Neural Network
 Computing the gradient through a recurrent neural network is straightforward.
 One simply applies the generalized back-propagation algorithm of section 6.5.6
 384
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 totheunrolledcomputational graph. Nospecializedalgorithmsarenecessary.
 Gradientsobtainedbyback-propagationmaythenbeusedwithanygeneral-purpose
 gradient-basedtechniquestotrainanRNN.
 TogainsomeintuitionforhowtheBPTTalgorithmbehaves,weprovidean
 exampleofhowtocomputegradientsbyBPTTfortheRNNequationsabove
 (equation andequation ).Thenodesofourcomputationalgraphinclude 10.8 10.12
 theparametersU,V,W,bandcaswellasthesequenceofnodes indexedby
 tforx() t ,h() t ,o() t andL() t . ForeachnodeNweneedtocomputethegradient
 ∇NLrecursively,basedonthegradientcomputedatnodesthatfollowit inthe
 graph.Westarttherecursionwiththenodesimmediatelyprecedingthefinal loss
 ∂L
 ∂L() t
 = 1. (10.17)
 Inthisderivationweassumethattheoutputso() t areusedastheargumenttothe
 softmaxfunctiontoobtainthevectorˆ yofprobabilitiesovertheoutput.Wealso
 assumethatthelossisthenegativelog-likelihoodofthetruetargety() t giventhe
 inputsofar.Thegradient∇o( ) t
 Lontheoutputsattimestept, forall i,t, isas
 follows:
 (∇o( ) tL)i= ∂L
 ∂o() t
 i
 = ∂L
 ∂L() t
 ∂L() t
 ∂o() t
 i
 =ˆ y() t
 i −1i,y( ) t . (10.18)
 Weworkourwaybackwards,startingfromtheendofthesequence.Atthefinal
 timestep , τ h( ) τ onlyhaso( ) τ asadescendent,soitsgradient issimple:
 ∇h( ) τ
 L= V∇o( ) τL. (10.19)
 Wecantheniteratebackwards intimetoback-propagategradientsthroughtime,
 fromt=τ−1downtot= 1,notingthath() t (for t<τ)hasasdescendentsboth
 o() t andh(+1) t . Itsgradientisthusgivenby
 ∇h( ) t
 L=
 
 ∂h(+1) t
 ∂h() t
 
 (∇h(+1) t
 L)+
 
 ∂o() t
 ∂h() t
 
 (∇o( ) tL) (10.20)
 = W(∇h(+1) t L)diag
 
 1−
 
 h(+1) t
 2
 
 +V(∇o( ) tL) (10.21)
 wherediag
 
 1−h(+1) t
 2
 
 indicatesthediagonalmatrixcontainingtheelements
 1−(h(+1) t
 i )2.This istheJacobianofthehyperbolictangentassociatedwiththe
 hiddenunit attime . i t+1
 385
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 Once thegradientsonthe internal nodes of the computational graphare
 obtained, wecanobtainthegradientsontheparameternodes. Because the
 parametersaresharedacrossmanytime steps,wemusttakesomecarewhen
 denotingcalculusoperations involvingthesevariables.Theequationswewishto
 implementusethebpropmethodofsection , thatcomputesthecontribution 6.5.6
 ofasingleedgeinthecomputationalgraphtothegradient.However,the∇Wf
 operatorusedincalculustakesintoaccountthecontributionofWtothevalue
 of fdueto edges inthecomputationalgraph.Toresolvethisambiguity,we all
 introducedummyvariablesW() t thataredefinedtobecopiesofWbutwitheach
 W() t usedonlyattimestept.Wemaythenuse∇W( ) t todenotethecontribution
 oftheweightsattimestep tothegradient. t
 Usingthisnotation, thegradientontheremainingparameters isgivenby:
 ∇cL =
 
 t
 
 ∂o() t
 ∂c
 
 ∇o( ) t
 L=
 
 t
 ∇o( ) tL (10.22)
 ∇bL =
 
 t
 
 ∂h() t
 ∂b() t
 
 ∇h( ) tL=
 
 t
 diag
 
 1−
 
 h() t
 2
 
 ∇h( ) tL(10.23)
 ∇VL =
 
 t
 
 i
 
 ∂L
 ∂o() t
 i
 
 ∇Vo() t
 i =
 
 t
 (∇o( ) tL)h() t (10.24)
 ∇WL =
 
 t
 
 i
 
 ∂L
 ∂h() t
 i
 
 ∇W( ) th() t
 i (10.25)
 =
 
 t
 diag
 
 1−
 
 h() t
 2
 
 (∇h( ) tL)h( 1) t−  (10.26)
 ∇UL =
 
 t
 
 i
 
 ∂L
 ∂h() t
 i
 
 ∇U( ) t
 h() t
 i (10.27)
 =
 
 t
 diag
 
 1−
 
 h() t
 2
 
 (∇h( ) t
 L)x() t (10.28)
 Wedonotneedtocomputethegradientwithrespecttox() t fortrainingbecause
 itdoesnothaveanyparametersasancestors inthecomputationalgraphdefining
 theloss.
 386
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 10.2.3 Recurrent Networks as Directed Graphical Models
 In the example recurrent network we have developed so far, the losses L( ) t were
 cross-entropies between training targetsy( ) t and outputs o( ) t . As with a feedforward
 network, it is in principle possible to use almost any loss with a recurrent network.
 The loss should be chosen based on the task. As with a feedforward network, we
 usually wish to interpret the output of the RNN as a probability distribution, and
 we usually use the cross-entropy associated with that distribution to define the loss.
 Mean squared error is the cross-entropy loss associated with an output distribution
 that is a unit Gaussian, for example, just as with a feedforward network.
 10.12
 When we use a predictive log-likelihood training objective, such as equa
tion
 , we train the RNN to estimate the conditional distribution of the next
 sequence element y( ) t given the past inputs. This may mean that we maximize
 the log-likelihood
 log (
 p y( ) t | x(1),...,x( ) t),
 (10.29)
 or, if the model includes connections from the output at one time step to the next
 time step,
 log (
 p y( ) t | x(1),...,x( ) t ,y(1),...,y( 1)
 t− ).
 (10.30)
 Decomposing the joint probability over the sequence of y values as a series of
 one-step probabilistic predictions is one way to capture the full joint distribution
 across the whole sequence. When we do not feed past y values as inputs that
 condition the next step prediction, the directed graphical model contains no edges
 from any y( ) i in the past to the current y( ) t . In this case, the outputsy are
 conditionally independent given the sequence of x values. When we do feed the
 actual y values (not their prediction, but the actual observed or generated values)
 back into the network, the directed graphical model contains edges from all y( ) i
 values in the past to the current y( ) t value.
 As a simple example, let us consider the case where the RNN models only a
 sequence of scalar random variables Y ={y(1),...,y( )
 τ }, with no additional inputs
 x. The input at time step t is simply the output at time step t−1. The RNN then
 defines a directed graphical model over the y variables. We parametrize the joint
 distribution of these observations using the chain rule (equation
 probabilities:
 P
 Y
 ( ) = 
τ
 
 P
 (y(1), . . . , y( )
 τ ) =
 P(y( ) t | y( 1)
 t− ,y( 2)
 3.6
 ) for conditional
 t− ,...,y(1))
 t=1
 (10.31)
 where the right-hand side of the bar is empty for t = 1, of course. Hence the
 negative log-likelihood of a set of values {y(1),...,y( )
 τ } according to such a model
 387
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 y(1) y(1) y(2) y(2) y(3) y(3) y(4) y(4) y(5) y(5) y( ) ... y( ) ...
 Figure10.7: Fullyconnectedgraphicalmodel forasequencey(1),y(2),...,y() t ,...: every
 pastobservationy() i mayinfluencetheconditionaldistributionofsomey() t (for t>i),
 giventhepreviousvalues.Parametrizingthegraphicalmodeldirectlyaccordingtothis
 graph(as inequation )mightbeveryinefficient,withanevergrowingnumberof 10.6
 inputsandparameters for eachelementof the sequence. RNNsobtainthe same full
 connectivitybutefficientparametrization,as illustratedinfigure . 10.8
 is
 L=
 
 t
 L() t (10.32)
 where
 L() t = log ( − Py() t = y() t |y( 1) t− ,y( 2) t− ,...,y(1)). (10.33)
 y(1) y(1) y(2) y(2) y(3) y(3) y(4) y(4) y(5) y(5) y( ) ... y( ) ...
 h(1) h(1) h(2) h(2) h(3) h(3) h(4) h(4) h(5) h(5) h( ) ... h( ) ...
 Figure10.8: Introducingthe statevariable inthegraphicalmodel of theRNN, even
 thoughit isadeterministicfunctionof its inputs,helpstoseehowwecanobtainavery
 efficientparametrization,basedonequation .Everystageinthesequence(for 10.5 h() t
 andy() t )involvesthesamestructure(thesamenumberof inputsforeachnode)andcan
 sharethesameparameterswiththeotherstages.
 Theedgesinagraphicalmodelindicatewhichvariablesdependdirectlyonother
 variables.Manygraphicalmodelsaimtoachievestatisticalandcomputational
 efficiencybyomittingedgesthatdonotcorrespondtostronginteractions. For
 388
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 example, it is common to make the Markov assumption that the graphical model
 should only contain edges from {y( )
 t k
 t− } to y( ) t , rather than containing
 − ,...,y( 1)
 edges from the entire past history. However, in some cases, we believe that all past
 inputs should have an influence on the next element of the sequence. RNNs are
 useful when we believe that the distribution over y( ) t may depend on a value of y( ) i
 from the distant past in a way that is not captured by the effect of y( ) i on y( 1)
 t− .
 One way to interpret an RNN as a graphical model is to view the RNN as
 defining a graphical model whose structure is the complete graph, able to represent
 direct dependencies between any pair of y values. The graphical model over the y
 values with the complete graph structure is shown in figure
 10.7
 . The complete
 graph interpretation of the RNN is based on ignoring the hidden units h( ) t by
 marginalizing them out of the model.
 It is more interesting to consider the graphical model structure of RNNs that
 results from regarding the hidden units h( ) t as random variables.1 Including the
 hidden units in the graphical model reveals that the RNN provides a very efficient
 parametrization of the joint distribution over the observations. Suppose that we
 represented an arbitrary joint distribution over discrete values with a tabular
 representation—an array containing a separate entry for each possible assignment
 of values, with the value of that entry giving the probability of that assignment
 occurring. If y can take on k different values, the tabular representation would
 have O(kτ) parameters. By comparison, due to parameter sharing, the number of
 parameters in the RNN is O(1) as a function of sequence length. The number of
 parameters in the RNN may be adjusted to control model capacity but is not forced
 to scale with sequence length. Equation
 10.5
 shows that the RNN parametrizes
 long-term relationships between variables efficiently, using recurrent applications
 of the same function f and same parameters θ at each time step. Figure 10.8
 illustrates the graphical model interpretation. Incorporating the h( ) t nodes in
 the graphical model decouples the past and the future, acting as an intermediate
 quantity between them. A variable y( ) i in the distant past may influence a variable
 y( ) t via its effect on h. The structure of this graph shows that the model can be
 efficiently parametrized by using the same conditional probability distributions at
 each time step, and that when the variables are all observed, the probability of the
 joint assignment of all variables can be evaluated efficiently.
 Even with the efficient parametrization of the graphical model, some operations
 remain computationally challenging. For example, it is difficult to predict missing
 1The conditional distribution over these variables given their parents is deterministic. This is
 perfectly legitimate, though it is somewhat rare to design a graphical model with such deterministic
 hidden units.
 389
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 values in the middle of the sequence.
 optimizing
 The price recurrent networks pay for their reduced number of parameters is
 that
 the parameters may be difficult.
 The parameter sharing used in recurrent networks relies on the assumption
 that the same parameters can be used for different time steps. Equivalently, the
 assumption is that the conditional probability distribution over the variables at
 time t+1 given the variables at time t is stationary, meaning that the relationship
 between the previous time step and the next time step does not depend on t. In
 principle, it would be possible to use t as an extra input at each time step and let
 the learner discover any time-dependence while sharing as much as it can between
 different time steps. This would already be much better than using a different
 conditional probability distribution for each t, but the network would then have to
 extrapolate when faced with new values of . t
 To complete our view of an RNN as a graphical model, we must describe how
 to draw samples from the model. The main operation that we need to perform is
 simply to sample from the conditional distribution at each time step. However,
 there is one additional complication. The RNN must have some mechanism for
 determining the length of the sequence. This can be achieved in various ways.
 In the case when the output is a symbol taken from a vocabulary, one can
 add a special symbol corresponding to the end of a sequence (Schmidhuber 2012
 ,
 ).
 When that symbol is generated, the sampling process stops. In the training set,
 we insert this symbol as an extra member of the sequence, immediately after x( ) τ
 in each training example.
 Another option is to introduce an extra Bernoulli output to the model that
 represents the decision to either continue generation or halt generation at each
 time step. This approach is more general than the approach of adding an extra
 symbol to the vocabulary, because it may be applied to any RNN, rather than
 only RNNs that output a sequence of symbols. For example, it may be applied to
 an RNN that emits a sequence of real numbers. The new output unit is usually a
 sigmoid unit trained with the cross-entropy loss. In this approach the sigmoid is
 trained to maximize the log-probability of the correct prediction as to whether the
 sequence ends or continues at each time step.
 Another way to determine the sequence length τ is to add an extra output to
 the model that predicts the integer τ itself. The model can sample a value of τ
 and then sample τ steps worth of data. This approach requires adding an extra
 input to the recurrent update at each time step so that the recurrent update is
 aware of whether it is near the end of the generated sequence. This extra input
 can either consist of the value of τ or can consist of τ t
 − ,the number of remaining
 390
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 time steps. Without this extra input, the RNN might generate sequences that
 end abruptly, such as a sentence that ends before it is complete. This approach is
 based on the decomposition
 P(x(1),...,x( )
 τ ) = ( ) (
 2014d
 ).
 P τ P x(1),...,x( ) τ | τ . )
 (10.34)
 The strategy of predicting τ directly is used for example by Goodfellow et al.
 (
 10.2.4 Modeling Sequences Conditioned on Context with RNNs
 In the previous section we described how an RNN could correspond to a directed
 graphical model over a sequence of random variables y( ) t with no inputs x. Of
 course, our development of RNNs as in equation
 10.8
 inputs x(1),x(2),...,x( )
 included a sequence of
 τ . In general, RNNs allow the extension of the graphical
 model view to represent not only a joint distribution over the y variables but
 also a conditional distribution over y given x. As discussed in the context of
 P(y;θ)
 feedforward networks in section
 6.2.1.1
 , any model representing a variable
 can be reinterpreted as a model representing a conditional distribution P(y ω
 | )
 withω = θ. We can extend such a model to represent a distribution P(y x
 |
 using the same P(y ω
 |
 ) by
 ) as before, but making ω a function of x. In the case of
 an RNN, this can be achieved in different ways. We review here the most common
 and obvious choices.
 Previously, we have discussed RNNs that take a sequence of vectors x( ) t for
 t = 1,...,τ as input. Another option is to take only a single vector x as input.
 When x is a fixed-size vector, we can simply make it an extra input of the RNN
 that generates the y sequence. Some common ways of providing an extra input to
 an RNN are:
 1. as an extra input at each time step, or
 2. as the initial state h(0), or
 3. both.
 The first and most common approach is illustrated in figure
 10.9
 . The interaction
 between the input x and each hidden unit vector h( ) t is parametrized by a newly
 introduced weight matrix R that was absent from the model of only the sequence
 of y values. The same product xR is added as additional input to the hidden
 units at every time step. We can think of the choice of x as determining the value
 391
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 ofxRthat iseffectivelyanewbiasparameterusedforeachofthehiddenunits.
 Theweightsremainindependentoftheinput.Wecanthinkofthismodelastaking
 theparametersθofthenon-conditionalmodelandturningthemintoω,where
 thebiasparameterswithin arenowafunctionoftheinput. ω
 o(t−1) o(t−1) o() t o() t o(+1) t o(+1) t
 L(t−1) L(t−1) L() t L() t L(+1) t L(+1) t
 y(t−1) y(t−1) y() t y() t y(+1) t y(+1) t
 h(t−1) h(t−1) h() t h() t h(+1) t h(+1) t
 W W W W
 s( ) ... s( ) ... h( ) ... h( ) ...
 V V V
 U U U
 x x
 y( ) ... y( ) ...
 R R R R R
 Figure10.9:AnRNNthatmapsafixed-lengthvectorxintoadistributionoversequences
 Y.ThisRNNisappropriatefortaskssuchas imagecaptioning,whereasingleimageis
 usedas inputtoamodelthatthenproducesasequenceofwordsdescribingtheimage.
 Eachelementy() t oftheobservedoutputsequenceservesbothas input(forthecurrent
 timestep)and,duringtraining,astarget(fortheprevioustimestep).
 Ratherthanreceivingonlyasinglevectorxasinput, theRNNmayreceive
 asequenceofvectorsx() t as input. TheRNNdescribedinequation corre-10.8
 spondstoaconditionaldistributionP(y(1),...,y( ) τ |x(1),...,x( ) τ )thatmakesa
 conditional independenceassumptionthatthisdistributionfactorizesas
 
 t
 P(y() t |x(1),...,x() t ). (10.35)
 Toremovetheconditional independenceassumption,wecanaddconnectionsfrom
 theoutputattimettothehiddenunitattimet+1,asshowninfigure .The 10.10
 modelcanthenrepresentarbitraryprobabilitydistributionsovertheysequence.
 Thiskindofmodel representingadistributionoverasequencegivenanother
 392
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 o(t−1) o(t−1) o() t o() t o(+1) t o(+1) t
 L(t−1) L(t−1) L() t L() t L(+1) t L(+1) t
 y(t−1) y(t−1) y() t y() t y(+1) t y(+1) t
 h(t−1) h(t−1) h() t h() t h(+1) t h(+1) t
 W W W W
 h( ) ... h( ) ... h( ) ... h( ) ...
 V V V
 U U U
 x(t−1) x(t−1)
 R
 x() t x() t x(+1) t x(+1) t
 R R
 Figure10.10: Aconditionalrecurrentneuralnetworkmappingavariable-lengthsequence
 ofxvaluesintoadistributionoversequencesofyvaluesofthesamelength.Comparedto
 figure , thisRNNcontainsconnectionsfromthepreviousoutputtothecurrentstate. 10.3
 TheseconnectionsallowthisRNNtomodelanarbitrarydistributionoversequencesofy
 givensequencesofxofthesamelength.TheRNNoffigure isonlyabletorepresent 10.3
 distributionsinwhichtheyvaluesareconditionallyindependentfromeachothergiven
 the values. x
 393
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 sequencestillhasonerestriction,whichisthatthelengthofbothsequencesmust
 bethesame.Wedescribehowtoremovethisrestrictioninsection . 10.4
 o(t−1) o(t−1) o() t o() t o(+1) t o(+1) t
 L(t−1) L(t−1) L() t L() t L(+1) t L(+1) t
 y(t−1) y(t−1) y() t y() t y(+1) t y(+1) t
 h(t−1) h(t−1) h() t h() t h(+1) t h(+1) t
 x(t−1) x(t−1) x() t x() t x(+1) t x(+1) t
 g(t−1) g(t−1) g() t g() t g(+1) t g(+1) t
 Figure10.11: Computationofatypicalbidirectional recurrentneuralnetwork,meant
 tolearntomapinputsequencesxtotargetsequencesy,withlossL() t ateachstept.
 Thehrecurrencepropagates informationforwardintime(towardstheright)whilethe
 grecurrencepropagates informationbackwardintime(towardstheleft).Thusateach
 pointt,theoutputunitso() t canbenefitfromarelevantsummaryofthepast initsh() t
 inputandfromarelevantsummaryofthefutureinitsg() t input.