<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Recurrent Neural Networks Tutorial</title>
    <script>
    MathJax = {
        tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true,
            processEnvironments: true
        },
        svg: {
            fontCache: 'global'
        }
    };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #166088;
            --accent-color: #45b7d1;
            --light-accent: #e8f4f8;
            --dark-accent: #0d3b66;
            --text-color: #333;
            --light-grey: #f5f5f5;
            --medium-grey: #e0e0e0;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 30px 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: var(--box-shadow);
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        h2 {
            color: var(--secondary-color);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: var(--dark-accent);
            margin-top: 30px;
            font-size: 1.5em;
        }
        
        p {
            margin-bottom: 20px;
        }
        
        .note {
            background-color: var(--light-accent);
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .example {
            background-color: #f9f9f9;
            border: 1px solid var(--medium-grey);
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .example h4 {
            margin-top: 0;
            color: var(--secondary-color);
        }
        
        .visualization {
            text-align: center;
            margin: 30px 0;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            flex: 1;
            background-color: var(--light-grey);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--medium-grey);
        }
        
        .comparison-item h4 {
            margin-top: 0;
            color: var(--secondary-color);
            border-bottom: 1px solid var(--medium-grey);
            padding-bottom: 5px;
        }
        
        .equation {
            padding: 10px;
            overflow-x: auto;
        }
        
        .toc {
            background-color: var(--light-grey);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid var(--medium-grey);
        }
        
        .toc h3 {
            margin-top: 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 10px;
        }
        
        .toc a {
            color: var(--secondary-color);
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
            color: var(--accent-color);
        }
        
        code {
            background-color: var(--light-grey);
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', Courier, monospace;
        }
    </style>
</head>
<body>
    <header>
        <h1>Advanced Recurrent Neural Networks</h1>
        <p>Understanding RNN architectures, training, and applications for sequence modeling</p>
    </header>

    <div class="toc">
        <h3>Table of Contents</h3>
        <ul>
            <li><a href="#section1">1. Teacher Forcing and Networks with Output Recurrence</a></li>
            <li><a href="#section2">2. Computing the Gradient in a Recurrent Neural Network</a></li>
            <li><a href="#section3">3. Recurrent Networks as Directed Graphical Models</a></li>
            <li><a href="#section4">4. Modeling Sequences Conditioned on Context with RNNs</a></li>
            <li><a href="#section5">5. Bidirectional Recurrent Neural Networks</a></li>
        </ul>
    </div>

    <!-- Content will be added section by section -->
    <section id="section1">
        <h2>1. Teacher Forcing and Networks with Output Recurrence</h2>
        
        <h3>1.1 Networks with Output Recurrence</h3>
        <p>
            Recurrent neural networks (RNNs) can have different recurrent connection patterns. A network with recurrent connections 
            only from the output at one time step to the hidden units at the next time step is <strong>strictly less powerful</strong> 
            than a standard RNN with hidden-to-hidden connections. This limitation exists because such networks:
        </p>
        
        <ul>
            <li>Cannot simulate a universal Turing machine</li>
            <li>Require output units to capture all information about the past used to predict the future</li>
            <li>Rely on output units that are explicitly trained to match training targets, making them unlikely to capture necessary historical information</li>
        </ul>
        
        <div class="note">
            <p><strong>Note:</strong> The main advantage of eliminating hidden-to-hidden recurrence is that all time steps become decoupled for loss functions comparing predictions at time t to targets at time t. This allows for training to be parallelized, with gradients for each step computed independently.</p>
        </div>
        
        <div class="visualization">
            <svg width="700" height="350" viewBox="0 0 700 350">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="350" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold">Time-unfolded RNN with Single Output at Sequence End</text>
                
                <!-- Nodes and connections -->
                <!-- Time t-1 -->
                <circle cx="150" cy="150" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="150" y="155" text-anchor="middle" font-size="14">h(t-1)</text>
                
                <circle cx="150" cy="250" r="25" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="150" y="255" text-anchor="middle" font-size="14">x(t-1)</text>
                
                <!-- Time t -->
                <circle cx="300" cy="150" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="300" y="155" text-anchor="middle" font-size="14">h(t)</text>
                
                <circle cx="300" cy="250" r="25" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="300" y="255" text-anchor="middle" font-size="14">x(t)</text>
                
                <!-- ... -->
                <text x="375" y="150" text-anchor="middle" font-size="16">...</text>
                <text x="375" y="250" text-anchor="middle" font-size="16">...</text>
                
                <!-- Time τ -->
                <circle cx="450" cy="150" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="450" y="155" text-anchor="middle" font-size="14">h(τ)</text>
                
                <circle cx="450" cy="250" r="25" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="450" y="255" text-anchor="middle" font-size="14">x(τ)</text>
                
                <circle cx="450" cy="70" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                <text x="450" y="75" text-anchor="middle" font-size="14">o(τ)</text>
                
                <!-- Output Loss -->
                <circle cx="550" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                <text x="550" y="75" text-anchor="middle" font-size="14">L(τ)</text>
                
                <!-- Connections -->
                <!-- Hidden-to-hidden connections -->
                <line x1="175" y1="150" x2="275" y2="150" stroke="#166088" stroke-width="2" />
                <text x="225" y="140" text-anchor="middle" font-size="14">W</text>
                
                <line x1="325" y1="150" x2="425" y2="150" stroke="#166088" stroke-width="2" />
                <text x="375" y="140" text-anchor="middle" font-size="14">W</text>
                
                <!-- Input-to-hidden connections -->
                <line x1="150" y1="225" x2="150" y2="175" stroke="#888" stroke-width="2" />
                <text x="140" y="200" text-anchor="middle" font-size="14">U</text>
                
                <line x1="300" y1="225" x2="300" y2="175" stroke="#888" stroke-width="2" />
                <text x="290" y="200" text-anchor="middle" font-size="14">U</text>
                
                <line x1="450" y1="225" x2="450" y2="175" stroke="#888" stroke-width="2" />
                <text x="440" y="200" text-anchor="middle" font-size="14">U</text>
                
                <!-- Hidden-to-output connection -->
                <line x1="450" y1="125" x2="450" y2="95" stroke="#d64161" stroke-width="2" />
                <text x="465" y="110" text-anchor="middle" font-size="14">V</text>
                
                <!-- Output-to-loss connection -->
                <line x1="475" y1="70" x2="530" y2="70" stroke="#888" stroke-width="1.5" stroke-dasharray="4" />
            </svg>
            <p class="caption">Figure 1: Time-unfolded recurrent neural network with a single output at the end of the sequence. This network can be used to summarize a sequence and produce a fixed-size representation for further processing.</p>
        </div>
        
        <h3>1.2 Teacher Forcing</h3>
        <p>
            Models with recurrent connections from outputs back into the model can be trained with <strong>teacher forcing</strong>. 
            This is a procedure derived from the maximum likelihood criterion, where during training the model receives the 
            ground truth output y(t) as input at time t+1, rather than its own prediction.
        </p>
        
        <div class="equation">
            For a sequence with two time steps, the conditional maximum likelihood criterion is:
            $$\log p(y^{(1)}, y^{(2)} | x^{(1)}, x^{(2)}) = \log p(y^{(2)} | y^{(1)}, x^{(1)}, x^{(2)}) + \log p(y^{(1)} | x^{(1)}, x^{(2)})$$
        </div>
        
        <p>
            At time t=2, the model is trained to maximize the conditional probability of y(2) given both the x sequence 
            so far and the previous y value from the training set. Maximum likelihood specifies that during training, 
            rather than feeding the model's own output back into itself, these connections should use the target values 
            specifying what the correct output should be.
        </p>
        
        <div class="visualization">
            <svg width="700" height="500" viewBox="0 0 700 500">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="500" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold">Teacher Forcing</text>
                <text x="175" y="60" text-anchor="middle" font-size="14" font-weight="bold">Train Time</text>
                <text x="525" y="60" text-anchor="middle" font-size="14" font-weight="bold">Test Time</text>
                
                <!-- Train Time Side -->
                <!-- Time t-1 -->
                <circle cx="100" cy="150" r="20" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                <text x="100" y="155" text-anchor="middle" font-size="12">o(t-1)</text>
                
                <circle cx="100" cy="200" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                <text x="100" y="205" text-anchor="middle" font-size="12">L(t-1)</text>
                
                <circle cx="100" cy="250" r="20" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="100" y="255" text-anchor="middle" font-size="12">y(t-1)</text>
                
                <circle cx="100" cy="320" r="20" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="100" y="325" text-anchor="middle" font-size="12">h(t-1)</text>
                
                <circle cx="100" cy="400" r="20" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="100" y="405" text-anchor="middle" font-size="12">x(t-1)</text>
                
                <!-- Time t -->
                <circle cx="250" cy="150" r="20" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                <text x="250" y="155" text-anchor="middle" font-size="12">o(t)</text>
                
                <circle cx="250" cy="200" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                <text x="250" y="205" text-anchor="middle" font-size="12">L(t)</text>
                
                <circle cx="250" cy="250" r="20" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="250" y="255" text-anchor="middle" font-size="12">y(t)</text>
                
                <circle cx="250" cy="320" r="20" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="250" y="325" text-anchor="middle" font-size="12">h(t)</text>
                
                <circle cx="250" cy="400" r="20" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="250" y="405" text-anchor="middle" font-size="12">x(t)</text>
                
                <!-- Connections Train Time -->
                <line x1="120" y1="320" x2="230" y2="320" stroke="#166088" stroke-width="2" />
                <text x="175" y="315" text-anchor="middle" font-size="12">W</text>
                
                <line x1="100" y1="270" x2="100" y2="300" stroke="#598234" stroke-width="2" stroke-dasharray="5,2" />
                <line x1="100" y1="340" x2="100" y2="380" stroke="#888" stroke-width="2" />
                <line x1="250" y1="340" x2="250" y2="380" stroke="#888" stroke-width="2" />
                
                <line x1="100" y1="300" x2="100" y2="340" stroke="#45b7d1" stroke-width="2" />
                <line x1="250" y1="300" x2="250" y2="340" stroke="#45b7d1" stroke-width="2" />
                
                <line x1="100" y1="130" x2="100" y2="230" stroke="#d64161" stroke-width="2" />
                <line x1="250" y1="130" x2="250" y2="230" stroke="#d64161" stroke-width="2" />
                
                <!-- The key teacher forcing connection -->
                <line x1="120" y1="250" x2="230" y2="300" stroke="#598234" stroke-width="3" />
                <text x="175" y="265" text-anchor="middle" font-size="12" font-weight="bold">Teacher Forcing</text>
                
                <!-- Test Time Side -->
                <!-- Time t-1 -->
                <circle cx="450" cy="150" r="20" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                <text x="450" y="155" text-anchor="middle" font-size="12">o(t-1)</text>
                
                <circle cx="450" cy="200" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                <text x="450" y="205" text-anchor="middle" font-size="12">L(t-1)</text>
                
                <circle cx="450" cy="250" r="20" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="450" y="255" text-anchor="middle" font-size="12">y(t-1)</text>
                
                <circle cx="450" cy="320" r="20" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="450" y="325" text-anchor="middle" font-size="12">h(t-1)</text>
                
                <circle cx="450" cy="400" r="20" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="450" y="405" text-anchor="middle" font-size="12">x(t-1)</text>
                
                <!-- Time t -->
                <circle cx="600" cy="150" r="20" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                <text x="600" y="155" text-anchor="middle" font-size="12">o(t)</text>
                
                <circle cx="600" cy="200" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                <text x="600" y="205" text-anchor="middle" font-size="12">L(t)</text>
                
                <circle cx="600" cy="250" r="20" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="600" y="255" text-anchor="middle" font-size="12">y(t)</text>
                
                <circle cx="600" cy="320" r="20" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="600" y="325" text-anchor="middle" font-size="12">h(t)</text>
                
                <circle cx="600" cy="400" r="20" fill="#f5f5f5" stroke="#888" stroke-width="2" />
                <text x="600" y="405" text-anchor="middle" font-size="12">x(t)</text>
                
                <!-- Connections Test Time -->
                <line x1="470" y1="320" x2="580" y2="320" stroke="#166088" stroke-width="2" />
                <text x="525" y="315" text-anchor="middle" font-size="12">W</text>
                
                <line x1="450" y1="340" x2="450" y2="380" stroke="#888" stroke-width="2" />
                <line x1="600" y1="340" x2="600" y2="380" stroke="#888" stroke-width="2" />
                
                <line x1="450" y1="300" x2="450" y2="340" stroke="#45b7d1" stroke-width="2" />
                <line x1="600" y1="300" x2="600" y2="340" stroke="#45b7d1" stroke-width="2" />
                
                <line x1="450" y1="130" x2="450" y2="230" stroke="#d64161" stroke-width="2" />
                <line x1="600" y1="130" x2="600" y2="230" stroke="#d64161" stroke-width="2" />
                
                <!-- The key model's own prediction connection -->
                <line x1="470" y1="150" x2="580" y2="300" stroke="#d64161" stroke-width="3" />
                <text x="525" y="230" text-anchor="middle" font-size="12" font-weight="bold">Model's Own Prediction</text>
                
                <!-- Divider -->
                <line x1="350" y1="70" x2="350" y2="470" stroke="#888" stroke-width="1" stroke-dasharray="5,5" />
            </svg>
            <p class="caption">Figure 2: Illustration of teacher forcing. (Left) At train time, we feed the correct output y(t) from the training set as input to h(t+1). (Right) When the model is deployed, the true output is generally not known. In this case, we use the model's output o(t) as input to the next time step.</p>
        </div>
        
        <h3>1.3 Teacher Forcing Applications and Limitations</h3>
        <p>
            Originally, teacher forcing was motivated as a way to avoid back-propagation through time in models that lack 
            hidden-to-hidden connections. However, teacher forcing can still be applied to models with hidden-to-hidden 
            connections as long as they have connections from the output at one time step to values computed in the next time step.
        </p>
        
        <p>
            As soon as hidden units become a function of earlier time steps, the back-propagation through time (BPTT) 
            algorithm becomes necessary. Some models may thus be trained with both teacher forcing and BPTT.
        </p>
        
        <div class="note">
            <p><strong>Disadvantage of Teacher Forcing:</strong> The main disadvantage arises if the network will later be used in an open-loop mode, with the network's own outputs fed back as input. In this case, the inputs seen during training could be quite different from those seen at test time.</p>
        </div>
        
        <h3>1.4 Mitigating Teacher Forcing Limitations</h3>
        <p>
            There are several approaches to address the train-test discrepancy caused by teacher forcing:
        </p>
        
        <div class="comparison">
            <div class="comparison-item">
                <h4>Hybrid Training Approach</h4>
                <p>Train with both teacher-forced inputs and free-running inputs by predicting the correct target several steps in the future through the unfolded recurrent paths.</p>
                <p>This helps the network learn to handle self-generated inputs and map the state back to one that produces proper outputs.</p>
            </div>
            <div class="comparison-item">
                <h4>Curriculum Strategy (Bengio et al., 2015)</h4>
                <p>Randomly choose between using generated values or actual data values as input.</p>
                <p>This approach uses a curriculum learning strategy to gradually increase the proportion of generated values used as input during training.</p>
            </div>
        </div>
    </section>

    <section id="section2">
        <h2>2. Computing the Gradient in a Recurrent Neural Network</h2>
        
        <h3>2.1 Backpropagation Through Time (BPTT)</h3>
        <p>
            Computing gradients through a recurrent neural network is straightforward in principle. We simply apply the 
            generalized backpropagation algorithm to the unrolled computational graph. No specialized algorithms are needed, 
            though the implementation details can be complex.
        </p>
        
        <div class="note">
            <p>
                <strong>Key Insight:</strong> Once we unfold the RNN across time steps, we can treat it as a very deep 
                feedforward network with shared weights, and apply standard backpropagation. This approach is called 
                <strong>Backpropagation Through Time (BPTT)</strong>.
            </p>
        </div>
        
        <p>
            The gradients obtained by backpropagation can then be used with any general-purpose gradient-based techniques 
            to train an RNN, such as stochastic gradient descent (SGD), Adam, RMSProp, etc.
        </p>
        
        <div class="visualization">
            <svg width="700" height="450" viewBox="0 0 700 450">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="450" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold">Backpropagation Through Time (BPTT)</text>
                
                <!-- Forward pass -->
                <text x="150" y="70" text-anchor="middle" font-size="14" font-weight="bold" fill="#166088">Forward Pass</text>
                <line x1="40" y1="80" x2="260" y2="80" stroke="#166088" stroke-width="1" />
                
                <!-- Time steps for forward pass -->
                <g transform="translate(50, 120)">
                    <!-- Time t-2 -->
                    <rect x="0" y="0" width="100" height="60" fill="#e8f4f8" rx="5" ry="5" stroke="#45b7d1" stroke-width="2" />
                    <text x="50" y="25" text-anchor="middle" font-size="12">h(t-2)</text>
                    <text x="50" y="45" text-anchor="middle" font-size="10">Forward computation</text>
                    
                    <!-- Time t-1 -->
                    <rect x="150" y="0" width="100" height="60" fill="#e8f4f8" rx="5" ry="5" stroke="#45b7d1" stroke-width="2" />
                    <text x="200" y="25" text-anchor="middle" font-size="12">h(t-1)</text>
                    <text x="200" y="45" text-anchor="middle" font-size="10">Forward computation</text>
                    
                    <!-- Time t -->
                    <rect x="300" y="0" width="100" height="60" fill="#e8f4f8" rx="5" ry="5" stroke="#45b7d1" stroke-width="2" />
                    <text x="350" y="25" text-anchor="middle" font-size="12">h(t)</text>
                    <text x="350" y="45" text-anchor="middle" font-size="10">Forward computation</text>
                    
                    <!-- Time t+1 -->
                    <rect x="450" y="0" width="100" height="60" fill="#e8f4f8" rx="5" ry="5" stroke="#45b7d1" stroke-width="2" />
                    <text x="500" y="25" text-anchor="middle" font-size="12">h(t+1)</text>
                    <text x="500" y="45" text-anchor="middle" font-size="10">Forward computation</text>
                    
                    <!-- Arrows connecting forward nodes -->
                    <line x1="100" y1="30" x2="150" y2="30" stroke="#166088" stroke-width="2" marker-end="url(#arrowhead)" />
                    <line x1="250" y1="30" x2="300" y2="30" stroke="#166088" stroke-width="2" marker-end="url(#arrowhead)" />
                    <line x1="400" y1="30" x2="450" y2="30" stroke="#166088" stroke-width="2" marker-end="url(#arrowhead)" />
                    
                    <!-- Outputs -->
                    <rect x="300" y="-50" width="100" height="30" fill="#f9d5e5" rx="5" ry="5" stroke="#d64161" stroke-width="2" />
                    <text x="350" y="-30" text-anchor="middle" font-size="12">o(t)</text>
                    <line x1="350" y1="0" x2="350" y2="-20" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    
                    <rect x="450" y="-50" width="100" height="30" fill="#f9d5e5" rx="5" ry="5" stroke="#d64161" stroke-width="2" />
                    <text x="500" y="-30" text-anchor="middle" font-size="12">o(t+1)</text>
                    <line x1="500" y1="0" x2="500" y2="-20" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    
                    <!-- Losses -->
                    <rect x="300" y="-100" width="100" height="30" fill="#eeeeee" rx="5" ry="5" stroke="#888" stroke-width="2" />
                    <text x="350" y="-80" text-anchor="middle" font-size="12">L(t)</text>
                    <line x1="350" y1="-50" x2="350" y2="-70" stroke="#888" stroke-width="1.5" stroke-dasharray="4" marker-end="url(#arrowhead)" />
                    
                    <rect x="450" y="-100" width="100" height="30" fill="#eeeeee" rx="5" ry="5" stroke="#888" stroke-width="2" />
                    <text x="500" y="-80" text-anchor="middle" font-size="12">L(t+1)</text>
                    <line x1="500" y1="-50" x2="500" y2="-70" stroke="#888" stroke-width="1.5" stroke-dasharray="4" marker-end="url(#arrowhead)" />
                </g>
                
                <!-- Backward pass -->
                <text x="550" y="260" text-anchor="middle" font-size="14" font-weight="bold" fill="#d64161">Backward Pass</text>
                <line x1="440" y1="270" x2="660" y2="270" stroke="#d64161" stroke-width="1" />
                
                <!-- Gradient flow -->
                <g transform="translate(50, 290)">
                    <!-- Backward arrows -->
                    <line x1="400" y1="30" x2="450" y2="30" stroke="#166088" stroke-width="0.5" stroke-opacity="0.2" />
                    <line x1="250" y1="30" x2="300" y2="30" stroke="#166088" stroke-width="0.5" stroke-opacity="0.2" />
                    <line x1="100" y1="30" x2="150" y2="30" stroke="#166088" stroke-width="0.5" stroke-opacity="0.2" />
                    
                    <!-- Gradient flows backwards -->
                    <line x1="450" y1="50" x2="400" y2="50" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    <line x1="300" y1="50" x2="250" y2="50" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    <line x1="150" y1="50" x2="100" y2="50" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    
                    <!-- Gradient computation nodes -->
                    <rect x="0" y="20" width="100" height="60" fill="#fcf5dd" rx="5" ry="5" stroke="#efca66" stroke-width="2" />
                    <text x="50" y="45" text-anchor="middle" font-size="12">∇h(t-2)L</text>
                    <text x="50" y="65" text-anchor="middle" font-size="10">Gradient computation</text>
                    
                    <rect x="150" y="20" width="100" height="60" fill="#fcf5dd" rx="5" ry="5" stroke="#efca66" stroke-width="2" />
                    <text x="200" y="45" text-anchor="middle" font-size="12">∇h(t-1)L</text>
                    <text x="200" y="65" text-anchor="middle" font-size="10">Gradient computation</text>
                    
                    <rect x="300" y="20" width="100" height="60" fill="#fcf5dd" rx="5" ry="5" stroke="#efca66" stroke-width="2" />
                    <text x="350" y="45" text-anchor="middle" font-size="12">∇h(t)L</text>
                    <text x="350" y="65" text-anchor="middle" font-size="10">Gradient computation</text>
                    
                    <rect x="450" y="20" width="100" height="60" fill="#fcf5dd" rx="5" ry="5" stroke="#efca66" stroke-width="2" />
                    <text x="500" y="45" text-anchor="middle" font-size="12">∇h(t+1)L</text>
                    <text x="500" y="65" text-anchor="middle" font-size="10">Gradient computation</text>
                    
                    <!-- Output gradients -->
                    <line x1="350" y1="-50" x2="350" y2="-30" stroke="#d64161" stroke-width="0.5" stroke-opacity="0.2" />
                    <line x1="350" y1="0" x2="350" y2="20" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    
                    <line x1="500" y1="-50" x2="500" y2="-30" stroke="#d64161" stroke-width="0.5" stroke-opacity="0.2" />
                    <line x1="500" y1="0" x2="500" y2="20" stroke="#d64161" stroke-width="2" marker-end="url(#arrowhead)" />
                    
                    <rect x="300" y="-30" width="100" height="30" fill="#ffe6ea" rx="5" ry="5" stroke="#d64161" stroke-width="2" />
                    <text x="350" y="-10" text-anchor="middle" font-size="12">∇o(t)L</text>
                    
                    <rect x="450" y="-30" width="100" height="30" fill="#ffe6ea" rx="5" ry="5" stroke="#d64161" stroke-width="2" />
                    <text x="500" y="-10" text-anchor="middle" font-size="12">∇o(t+1)L</text>
                </g>
                
                <!-- Parameter gradients -->
                <rect x="50" y="410" width="600" height="30" fill="#e9f7ef" rx="5" ry="5" stroke="#27ae60" stroke-width="2" />
                <text x="350" y="430" text-anchor="middle" font-size="14">∇WL, ∇UL, ∇VL, ∇bL, ∇cL (Accumulate gradients across all time steps)</text>
                
                <!-- Arrowhead definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" />
                    </marker>
                </defs>
            </svg>
            <p class="caption">Figure 3: Backpropagation Through Time (BPTT) for recurrent neural networks. The forward pass computes activations through time, while the backward pass propagates gradients in reverse, from later time steps to earlier ones. Parameter gradients are accumulated across all time steps.</p>
        </div>
        
        <h3>2.2 Detailed Gradient Computation in RNNs</h3>
        <p>
            To provide intuition about how the BPTT algorithm behaves, let's look at a detailed example of computing gradients 
            for a standard RNN. The computational graph includes parameters U, V, W, b, and c, as well as the sequence of nodes 
            indexed by t for x(t), h(t), o(t), and L(t).
        </p>
        
        <p>
            For each node N in the computational graph, we need to compute the gradient ∇<sub>N</sub>L recursively, based on 
            gradients computed at nodes that follow it in the graph. We start the recursion with the nodes immediately preceding 
            the final loss:
        </p>
        
        <div class="equation">
            $$\frac{\partial L}{\partial L^{(t)}} = 1$$
        </div>
        
        <p>
            In this derivation, we assume that the outputs o(t) are used as the argument to the softmax function to obtain 
            the vector ŷ of probabilities over the output. We also assume that the loss is the negative log-likelihood of 
            the true target y(t) given the inputs so far.
        </p>
        
        <h3>2.3 Gradient Flow at Each Time Step</h3>
        <p>
            The gradient ∇<sub>o(t)</sub>L on the outputs at time step t, for all i,t, is:
        </p>
        
        <div class="equation">
            $$(\nabla_{o^{(t)}}L)_i = \frac{\partial L}{\partial o^{(t)}_i} = \frac{\partial L}{\partial L^{(t)}} \frac{\partial L^{(t)}}{\partial o^{(t)}_i} = \hat{y}^{(t)}_i - 1_{i,y^{(t)}}$$
        </div>
        
        <p>
            Where 1<sub>i,y(t)</sub> is the indicator function that equals 1 when i equals y(t) and 0 otherwise.
        </p>
        
        <p>
            We work our way backwards, starting from the end of the sequence. At the final time step τ, h(τ) only has o(τ) as a descendant, so its gradient is simple:
        </p>
        
        <div class="equation">
            $$\nabla_{h^{(\tau)}}L = V^{\top}\nabla_{o^{(\tau)}}L$$
        </div>
        
        <p>
            We can then iterate backwards in time to back-propagate gradients through time, from t = τ-1 down to t = 1, 
            noting that h(t) (for t &lt; τ) has as descendants both o(t) and h(t+1). Its gradient is thus given by:
        </p>
        
        <div class="equation">
            \begin{align}
            \nabla_{h^{(t)}}L &= \left(\frac{\partial h^{(t+1)}}{\partial h^{(t)}}\right)^{\top} (\nabla_{h^{(t+1)}}L) + \left(\frac{\partial o^{(t)}}{\partial h^{(t)}}\right)^{\top} (\nabla_{o^{(t)}}L) \\
            &= W^{\top}(\nabla_{h^{(t+1)}}L) \text{diag}\left(1-(h^{(t+1)})^2\right) + V^{\top}(\nabla_{o^{(t)}}L)
            \end{align}
        </div>
        
        <p>
            Where diag(1-(h(t+1))²) indicates the diagonal matrix containing the elements 1-(h<sub>i</sub><sup>(t+1)</sup>)². 
            This is the Jacobian of the hyperbolic tangent associated with the hidden unit i at time t+1.
        </p>
        
        <h3>2.4 Parameter Gradient Computation</h3>
        <p>
            Once the gradients on the internal nodes of the computational graph are obtained, we can obtain the gradients on the parameter nodes. 
            Because the parameters are shared across many time steps, we must take some care when denoting calculus operations involving these variables.
        </p>
        
        <p>
            The equations we wish to implement use the backpropagation method that computes the contribution of a single edge in the computational graph to the gradient. 
            However, the ∇<sub>W</sub>f operator used in calculus takes into account the contribution of W to the value of f due to <em>all</em> edges in the computational graph.
        </p>
        
        <p>
            To resolve this ambiguity, we introduce dummy variables W(t) that are defined to be copies of W but with each W(t) used only at time step t. 
            We may then use ∇<sub>W(t)</sub> to denote the contribution of the weights at time step t to the gradient.
        </p>
        
        <p>
            Using this notation, the gradient on the remaining parameters is given by:
        </p>
        
        <div class="equation">
            \begin{align}
            \nabla_c L &= \sum_t \left(\frac{\partial o^{(t)}}{\partial c}\right)^{\top} \nabla_{o^{(t)}}L = \sum_t \nabla_{o^{(t)}}L \\
            \nabla_b L &= \sum_t \left(\frac{\partial h^{(t)}}{\partial b^{(t)}}\right)^{\top} \nabla_{h^{(t)}}L = \sum_t \text{diag}\left(1-(h^{(t)})^2\right) \nabla_{h^{(t)}}L \\
            \nabla_V L &= \sum_t \sum_i \left(\frac{\partial L}{\partial o^{(t)}_i}\right) \nabla_V o^{(t)}_i = \sum_t (\nabla_{o^{(t)}}L) (h^{(t)})^{\top} \\
            \nabla_W L &= \sum_t \sum_i \left(\frac{\partial L}{\partial h^{(t)}_i}\right) \nabla_{W^{(t)}} h^{(t)}_i \\
            &= \sum_t \text{diag}\left(1-(h^{(t)})^2\right) (\nabla_{h^{(t)}}L) (h^{(t-1)})^{\top} \\
            \nabla_U L &= \sum_t \sum_i \left(\frac{\partial L}{\partial h^{(t)}_i}\right) \nabla_{U^{(t)}} h^{(t)}_i \\
            &= \sum_t \text{diag}\left(1-(h^{(t)})^2\right) (\nabla_{h^{(t)}}L) (x^{(t)})^{\top}
            \end{align}
        </div>
        
        <div class="note">
            <p>
                <strong>Note:</strong> We don't need to compute the gradient with respect to x(t) for training because it doesn't 
                have any parameters as ancestors in the computational graph defining the loss.
            </p>
        </div>
        
        <h3>2.5 Practical Considerations for RNN Gradient Computation</h3>
        <p>
            While the equations for BPTT are straightforward, there are several practical challenges when training RNNs:
        </p>
        
        <ul>
            <li>
                <strong>Vanishing and Exploding Gradients:</strong> The recursive nature of gradient computation can lead to gradients 
                that either vanish (approach zero) or explode (grow very large) as they are propagated backward through many time steps. 
                This is especially problematic for long sequences.
            </li>
            <li>
                <strong>Computational Efficiency:</strong> Computing the full gradient through a long sequence can be computationally expensive. 
                Techniques like truncated BPTT (computing gradients only for a limited number of time steps) are often used in practice.
            </li>
            <li>
                <strong>Memory Requirements:</strong> Storing all the intermediate activations for a long sequence can require 
                significant memory. Various methods exist to trade off computation and memory.
            </li>
        </ul>
        
        <p>
            These challenges have led to the development of specialized RNN architectures like LSTMs and GRUs, which are designed 
            to address the vanishing gradient problem and better capture long-term dependencies.
        </p>
    </section>

    <section id="section3">
        <h2>3. Recurrent Networks as Directed Graphical Models</h2>
        
        <h3>3.1 Probabilistic Interpretation of RNNs</h3>
        <p>
            In the example recurrent network we have developed so far, the losses L(t) were cross-entropies between 
            training targets y(t) and outputs o(t). As with feedforward networks, it's possible to use almost any loss 
            function with a recurrent network, chosen based on the task at hand.
        </p>
        
        <p>
            Typically, we want to interpret the output of an RNN as a probability distribution, and we usually use the 
            cross-entropy associated with that distribution to define the loss. For instance, mean squared error is the 
            cross-entropy loss associated with an output distribution that is a unit Gaussian.
        </p>
        
        <h3>3.2 RNNs for Sequence Modeling</h3>
        <p>
            When we use a predictive log-likelihood training objective, we train the RNN to estimate the conditional 
            distribution of the next sequence element y(t) given the past inputs. This may mean that we maximize 
            the log-likelihood:
        </p>
        
        <div class="equation">
            $$\log p(y^{(t)} | x^{(1)}, \ldots, x^{(t)})$$
        </div>
        
        <p>
            Or, if the model includes connections from the output at one time step to the next time step:
        </p>
        
        <div class="equation">
            $$\log p(y^{(t)} | x^{(1)}, \ldots, x^{(t)}, y^{(1)}, \ldots, y^{(t-1)})$$
        </div>
        
        <p>
            Decomposing the joint probability over the sequence of y values as a series of one-step probabilistic 
            predictions is one way to capture the full joint distribution across the whole sequence.
        </p>
        
        <div class="note">
            <p>
                <strong>Conditional Independence:</strong> When we don't feed past y values as inputs that condition the next step 
                prediction, the directed graphical model contains no edges from any y(i) in the past to the current y(t). In this case, 
                the outputs y are conditionally independent given the sequence of x values.
            </p>
            <p>
                When we do feed the actual y values (not their prediction, but the observed or generated values) back into the network, 
                the directed graphical model contains edges from all y(i) values in the past to the current y(t) value.
            </p>
        </div>
        
        <h3>3.3 Modeling Sequences Without External Inputs</h3>
        <p>
            Let's consider a case where the RNN models only a sequence of scalar random variables Y = {y(1), ..., y(τ)}, with no additional 
            inputs x. The input at time step t is simply the output at time step t-1. The RNN then defines a directed graphical 
            model over the y variables.
        </p>
        
        <p>
            We can parametrize the joint distribution of these observations using the chain rule for conditional probabilities:
        </p>
        
        <div class="equation">
            $$P(Y) = P(y^{(1)}, \ldots, y^{(\tau)}) = \prod_{t=1}^{\tau} P(y^{(t)} | y^{(t-1)}, y^{(t-2)}, \ldots, y^{(1)})$$
        </div>
        
        <p>
            Where the right-hand side of the conditioning bar is empty for t = 1. The negative log-likelihood of a set of 
            values {y(1), ..., y(τ)} according to such a model is:
        </p>
        
        <div class="equation">
            $$L = \sum_t L^{(t)}$$
        </div>
        
        <p>
            where
        </p>
        
        <div class="equation">
            $$L^{(t)} = -\log P(y^{(t)} = y^{(t)} | y^{(t-1)}, y^{(t-2)}, \ldots, y^{(1)})$$
        </div>
        
        <div class="visualization">
            <svg width="700" height="220" viewBox="0 0 700 220">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="220" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold">Fully Connected Graphical Model for a Sequence</text>
                
                <!-- Nodes -->
                <circle cx="100" cy="100" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="100" y="105" text-anchor="middle" font-size="14">y(1)</text>
                
                <circle cx="200" cy="100" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="200" y="105" text-anchor="middle" font-size="14">y(2)</text>
                
                <circle cx="300" cy="100" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="300" y="105" text-anchor="middle" font-size="14">y(3)</text>
                
                <circle cx="400" cy="100" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="400" y="105" text-anchor="middle" font-size="14">y(4)</text>
                
                <circle cx="500" cy="100" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="500" y="105" text-anchor="middle" font-size="14">y(5)</text>
                
                <text x="600" y="100" text-anchor="middle" font-size="20">...</text>
                
                <!-- Connections (showing full connectivity) -->
                <!-- From y(1) to all future nodes -->
                <line x1="125" y1="100" x2="175" y2="100" stroke="#598234" stroke-width="2" />
                <line x1="115" y1="85" x2="285" y2="85" stroke="#598234" stroke-width="1.5" />
                <line x1="115" y1="80" x2="385" y2="80" stroke="#598234" stroke-width="1" />
                <line x1="115" y1="75" x2="485" y2="75" stroke="#598234" stroke-width="0.8" />
                
                <!-- From y(2) to all future nodes -->
                <line x1="225" y1="100" x2="275" y2="100" stroke="#598234" stroke-width="2" />
                <line x1="215" y1="85" x2="385" y2="85" stroke="#598234" stroke-width="1.5" />
                <line x1="215" y1="80" x2="485" y2="80" stroke="#598234" stroke-width="1" />
                
                <!-- From y(3) to all future nodes -->
                <line x1="325" y1="100" x2="375" y2="100" stroke="#598234" stroke-width="2" />
                <line x1="315" y1="85" x2="485" y2="85" stroke="#598234" stroke-width="1.5" />
                
                <!-- From y(4) to all future nodes -->
                <line x1="425" y1="100" x2="475" y2="100" stroke="#598234" stroke-width="2" />
                
                <!-- From y(5) to future (implied) -->
                <line x1="525" y1="100" x2="575" y2="100" stroke="#598234" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- Caption -->
                <text x="350" y="170" text-anchor="middle" font-size="14">
                    Every past observation y(i) may influence the conditional distribution of some y(t) (for t > i)
                </text>
                <text x="350" y="190" text-anchor="middle" font-size="14">
                    Direct parametrization would be inefficient with an ever-growing number of parameters
                </text>
            </svg>
            <p class="caption">Figure 4: Fully connected graphical model for a sequence y(1), y(2), ..., y(t), ...: every past observation y(i) may influence the conditional distribution of some y(t) (for t > i), given the previous values.</p>
        </div>
        
        <h3>3.4 The Efficiency of RNN Parametrization</h3>
        <p>
            The edges in a graphical model indicate which variables depend directly on other variables. Many graphical models aim 
            to achieve statistical and computational efficiency by omitting edges that don't correspond to strong interactions.
        </p>
        
        <p>
            For example, it's common to make the Markov assumption that the graphical model should only contain edges from 
            {y(t-k), ..., y(t-1)} to y(t), rather than containing edges from the entire past history. However, in some cases, 
            we believe that all past inputs should influence the next element of the sequence.
        </p>
        
        <p>
            RNNs are useful when we believe that the distribution over y(t) may depend on a value of y(i) from the distant past 
            in a way that is not captured by the effect of y(i) on y(t-1).
        </p>
        
        <div class="visualization">
            <svg width="700" height="250" viewBox="0 0 700 250">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="250" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold">RNN as Efficient Parametrization of Graphical Model</text>
                
                <!-- y nodes -->
                <circle cx="100" cy="80" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="100" y="85" text-anchor="middle" font-size="14">y(1)</text>
                
                <circle cx="200" cy="80" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="200" y="85" text-anchor="middle" font-size="14">y(2)</text>
                
                <circle cx="300" cy="80" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="300" y="85" text-anchor="middle" font-size="14">y(3)</text>
                
                <circle cx="400" cy="80" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="400" y="85" text-anchor="middle" font-size="14">y(4)</text>
                
                <circle cx="500" cy="80" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                <text x="500" y="85" text-anchor="middle" font-size="14">y(5)</text>
                
                <text x="600" y="80" text-anchor="middle" font-size="20">...</text>
                
                <!-- h nodes -->
                <circle cx="100" cy="170" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="100" y="175" text-anchor="middle" font-size="14">h(1)</text>
                
                <circle cx="200" cy="170" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="200" y="175" text-anchor="middle" font-size="14">h(2)</text>
                
                <circle cx="300" cy="170" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="300" y="175" text-anchor="middle" font-size="14">h(3)</text>
                
                <circle cx="400" cy="170" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="400" y="175" text-anchor="middle" font-size="14">h(4)</text>
                
                <circle cx="500" cy="170" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                <text x="500" y="175" text-anchor="middle" font-size="14">h(5)</text>
                
                <text x="600" y="170" text-anchor="middle" font-size="20">...</text>
                
                <!-- Connections -->
                <!-- h-to-h connections (recurrent) -->
                <line x1="125" y1="170" x2="175" y2="170" stroke="#45b7d1" stroke-width="2" />
                <line x1="225" y1="170" x2="275" y2="170" stroke="#45b7d1" stroke-width="2" />
                <line x1="325" y1="170" x2="375" y2="170" stroke="#45b7d1" stroke-width="2" />
                <line x1="425" y1="170" x2="475" y2="170" stroke="#45b7d1" stroke-width="2" />
                <line x1="525" y1="170" x2="575" y2="170" stroke="#45b7d1" stroke-width="2" stroke-dasharray="5,3" />
                
                <!-- h-to-y connections -->
                <line x1="100" y1="145" x2="100" y2="105" stroke="#166088" stroke-width="2" />
                <line x1="200" y1="145" x2="200" y2="105" stroke="#166088" stroke-width="2" />
                <line x1="300" y1="145" x2="300" y2="105" stroke="#166088" stroke-width="2" />
                <line x1="400" y1="145" x2="400" y2="105" stroke="#166088" stroke-width="2" />
                <line x1="500" y1="145" x2="500" y2="105" stroke="#166088" stroke-width="2" />
                
                <!-- y-to-h connections -->
                <line x1="125" y1="95" x2="190" y2="150" stroke="#598234" stroke-width="1.5" />
                <line x1="225" y1="95" x2="290" y2="150" stroke="#598234" stroke-width="1.5" />
                <line x1="325" y1="95" x2="390" y2="150" stroke="#598234" stroke-width="1.5" />
                <line x1="425" y1="95" x2="490" y2="150" stroke="#598234" stroke-width="1.5" />
                
                <!-- Caption -->
                <text x="350" y="220" text-anchor="middle" font-size="14">
                    Introducing the state variable h(t) in the graphical model provides efficient parametrization
                </text>
            </svg>
            <p class="caption">Figure 5: Introducing the state variable in the graphical model of the RNN, even though it is a deterministic function of its inputs, helps to see how we can obtain a very efficient parametrization.</p>
        </div>
        
        <h3>3.5 Graphical Model Interpretation</h3>
        <p>
            One way to interpret an RNN as a graphical model is to view the RNN as defining a graphical model whose structure 
            is the complete graph, able to represent direct dependencies between any pair of y values. The graphical model over 
            the y values with the complete graph structure is shown in Figure 4.
        </p>
        
        <p>
            The complete graph interpretation of the RNN is based on ignoring the hidden units h(t) by marginalizing them out of the model.
        </p>
        
        <p>
            It is more interesting to consider the graphical model structure of RNNs that results from regarding the hidden units h(t) 
            as random variables. Including the hidden units in the graphical model reveals that the RNN provides a very efficient 
            parametrization of the joint distribution over the observations.
        </p>
        
        <div class="note">
            <p>
                <strong>Parameter Efficiency:</strong> Suppose we represented an arbitrary joint distribution over discrete values with a tabular 
                representation—an array containing a separate entry for each possible assignment of values, with the value of that entry giving 
                the probability of that assignment occurring. If y can take on k different values, the tabular representation would have O(k<sup>τ</sup>) 
                parameters. By comparison, due to parameter sharing, the number of parameters in the RNN is O(1) as a function of sequence length.
            </p>
        </div>
        
        <p>
            The number of parameters in the RNN may be adjusted to control model capacity but is not forced to scale with sequence length. 
            The RNN parametrizes long-term relationships between variables efficiently, using recurrent applications of the same function f 
            and same parameters θ at each time step.
        </p>
        
        <p>
            Figure 5 illustrates this graphical model interpretation. Incorporating the h(t) nodes in the graphical model decouples the past 
            and the future, acting as an intermediate quantity between them. A variable y(i) in the distant past may influence a variable y(t) 
            via its effect on h. The structure of this graph shows that the model can be efficiently parametrized by using the same conditional 
            probability distributions at each time step, and that when the variables are all observed, the probability of the joint assignment 
            of all variables can be evaluated efficiently.
        </p>
        
        <h3>3.6 Challenges in RNN Graphical Models</h3>
        <p>
            Even with the efficient parametrization of the graphical model, some operations remain computationally challenging. 
            For example, it is difficult to predict missing values in the middle of the sequence.
        </p>
        
        <p>
            The price recurrent networks pay for their reduced number of parameters is that optimizing the parameters may be difficult. This 
            challenge is one of the main focal points of research in recurrent networks.
        </p>
        
        <p>
            The parameter sharing used in recurrent networks relies on the assumption that the same parameters can be used for different time steps. 
            Equivalently, the assumption is that the conditional probability distribution over the variables at time t+1 given the variables at time t 
            is stationary, meaning that the relationship between the previous time step and the next time step does not depend on t.
        </p>
        
        <p>
            In principle, it would be possible to use t as an extra input at each time step and let the learner discover any time-dependence 
            while sharing as much as it can between different time steps. This would already be much better than using a different conditional 
            probability distribution for each t, but the network would then have to extrapolate when faced with new values of t.
        </p>
    </section>

    <section id="section4">
        <h2>4. Modeling Sequences Conditioned on Context with RNNs</h2>
        
        <h3>4.1 Extending RNNs with Conditional Context</h3>
        <p>
            In the previous section, we described how an RNN could correspond to a directed graphical model over a sequence 
            of random variables y(t) with no inputs x. Of course, our development of RNNs included a sequence of inputs 
            x(1), x(2), ..., x(τ).
        </p>
        
        <p>
            In general, RNNs allow the extension of the graphical model view to represent not only a joint distribution 
            over the y variables but also a conditional distribution over y given x. As discussed in the context of 
            feedforward networks, any model representing a variable P(y;θ) can be reinterpreted as a model representing a 
            conditional distribution P(y|ω) with ω = θ.
        </p>
        
        <p>
            We can extend such a model to represent a distribution P(y|x) using the same P(y|ω) as before, but making ω a 
            function of x. In the case of an RNN, this can be achieved in different ways. We review here the most common 
            and obvious choices.
        </p>
        
        <h3>4.2 RNNs with Fixed-Size Vector Input</h3>
        <p>
            Previously, we have discussed RNNs that take a sequence of vectors x(t) for t = 1, ..., τ as input. Another option 
            is to take only a single vector x as input. When x is a fixed-size vector, we can simply make it an extra input 
            of the RNN that generates the y sequence.
        </p>
        
        <p>
            Some common ways of providing an extra input to an RNN are:
        </p>
        
        <ol>
            <li>As an extra input at each time step, or</li>
            <li>As the initial state h(0), or</li>
            <li>Both</li>
        </ol>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="400" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold">RNN Mapping Fixed-length Vector to Sequence</text>
                
                <!-- Time steps -->
                <!-- t-1 -->
                <g transform="translate(100, 120)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t-1)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t-1)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t-1)</text>
                    
                    <circle cx="0" cy="200" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="205" text-anchor="middle" font-size="14">h(t-1)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="155" x2="0" y2="175" stroke="#598234" stroke-width="1.5" />
                </g>
                
                <!-- t -->
                <g transform="translate(250, 120)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t)</text>
                    
                    <circle cx="0" cy="200" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="205" text-anchor="middle" font-size="14">h(t)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="155" x2="0" y2="175" stroke="#598234" stroke-width="1.5" />
                </g>
                
                <!-- t+1 -->
                <g transform="translate(400, 120)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t+1)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t+1)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t+1)</text>
                    
                    <circle cx="0" cy="200" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="205" text-anchor="middle" font-size="14">h(t+1)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="155" x2="0" y2="175" stroke="#598234" stroke-width="1.5" />
                </g>
                
                <!-- Continue -->
                <text x="550" y="150" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="200" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="250" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="320" text-anchor="middle" font-size="20">...</text>
                
                <!-- Fixed input vector x -->
                <rect x="300" y="300" width="100" height="50" rx="5" ry="5" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                <text x="350" y="330" text-anchor="middle" font-size="16">x</text>
                
                <!-- Recurrent connections -->
                <line x1="125" y1="320" x2="225" y2="320" stroke="#166088" stroke-width="2" />
                <text x="175" y="315" text-anchor="middle" font-size="14">W</text>
                
                <line x1="275" y1="320" x2="375" y2="320" stroke="#166088" stroke-width="2" />
                <text x="325" y="315" text-anchor="middle" font-size="14">W</text>
                
                <line x1="425" y1="320" x2="525" y2="320" stroke="#166088" stroke-width="2" stroke-dasharray="5,2" />
                <text x="475" y="315" text-anchor="middle" font-size="14">W</text>
                
                <!-- Input connections to hidden units -->
                <line x1="350" y1="300" x2="250" y2="330" stroke="#ff7043" stroke-width="2" />
                <text x="280" y="300" text-anchor="middle" font-size="14">R</text>
                
                <line x1="350" y1="300" x2="100" y2="330" stroke="#ff7043" stroke-width="2" />
                <text x="150" y="300" text-anchor="middle" font-size="14">R</text>
                
                <line x1="350" y1="300" x2="400" y2="330" stroke="#ff7043" stroke-width="2" />
                <text x="390" y="300" text-anchor="middle" font-size="14">R</text>
                
                <line x1="400" y1="310" x2="500" y2="330" stroke="#ff7043" stroke-width="2" stroke-dasharray="5,2" />
                <text x="480" y="300" text-anchor="middle" font-size="14">R</text>
                
                <!-- output to recurrent connections -->
                <line x1="125" y1="130" x2="225" y2="200" stroke="#598234" stroke-width="1.5" />
                <line x1="275" y1="130" x2="375" y2="200" stroke="#598234" stroke-width="1.5" />
            </svg>
            <p class="caption">Figure 6: An RNN that maps a fixed-length vector x into a distribution over sequences Y. This is appropriate for tasks like image captioning, where a single image is used as input to a model that produces a sequence of words describing the image.</p>
        </div>
        
        <p>
            The first and most common approach is illustrated in Figure 6. The interaction between the input x and each hidden 
            unit vector h(t) is parametrized by a newly introduced weight matrix R that was absent from the model of only the 
            sequence of y values. The same product xR is added as additional input to the hidden units at every time step.
        </p>
        
        <p>
            We can think of the choice of x as determining the value of xR that is effectively a new bias parameter used for each 
            of the hidden units. The weights remain independent of the input. We can think of this model as taking the parameters 
            θ of the non-conditional model and turning them into ω, where the bias parameters within ω are now a function of the input.
        </p>
        
        <h3>4.3 RNNs with Sequence Input</h3>
        <p>
            Rather than receiving only a single vector x as input, the RNN may receive a sequence of vectors x(t) as input. 
            A standard RNN corresponds to a conditional distribution P(y(1),...,y(τ)|x(1),...,x(τ)) that makes a conditional 
            independence assumption that this distribution factorizes as:
        </p>
        
        <div class="equation">
            $$\prod_t P(y^{(t)} | x^{(1)}, \ldots, x^{(t)})$$
        </div>
        
        <p>
            To remove the conditional independence assumption, we can add connections from the output at time t to the hidden unit 
            at time t+1, as shown in Figure 7. The model can then represent arbitrary probability distributions over the y sequence.
        </p>
        
        <div class="visualization">
            <svg width="700" height="450" viewBox="0 0 700 450">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="450" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold">Conditional RNN Mapping Sequences to Sequences</text>
                
                <!-- Time steps -->
                <!-- t-1 -->
                <g transform="translate(100, 120)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t-1)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t-1)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t-1)</text>
                    
                    <circle cx="0" cy="200" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="205" text-anchor="middle" font-size="14">h(t-1)</text>
                    
                    <circle cx="0" cy="270" r="25" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                    <text x="0" y="275" text-anchor="middle" font-size="14">x(t-1)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="155" x2="0" y2="175" stroke="#598234" stroke-width="1.5" />
                    <line x1="0" y1="225" x2="0" y2="245" stroke="#45b7d1" stroke-width="1.5" />
                </g>
                
                <!-- t -->
                <g transform="translate(250, 120)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t)</text>
                    
                    <circle cx="0" cy="200" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="205" text-anchor="middle" font-size="14">h(t)</text>
                    
                    <circle cx="0" cy="270" r="25" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                    <text x="0" y="275" text-anchor="middle" font-size="14">x(t)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="155" x2="0" y2="175" stroke="#598234" stroke-width="1.5" />
                    <line x1="0" y1="225" x2="0" y2="245" stroke="#45b7d1" stroke-width="1.5" />
                </g>
                
                <!-- t+1 -->
                <g transform="translate(400, 120)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t+1)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t+1)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t+1)</text>
                    
                    <circle cx="0" cy="200" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="205" text-anchor="middle" font-size="14">h(t+1)</text>
                    
                    <circle cx="0" cy="270" r="25" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                    <text x="0" y="275" text-anchor="middle" font-size="14">x(t+1)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="155" x2="0" y2="175" stroke="#598234" stroke-width="1.5" />
                    <line x1="0" y1="225" x2="0" y2="245" stroke="#45b7d1" stroke-width="1.5" />
                </g>
                
                <!-- Continue -->
                <text x="550" y="120" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="190" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="250" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="320" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="390" text-anchor="middle" font-size="20">...</text>
                
                <!-- Recurrent connections (hidden-to-hidden) -->
                <line x1="125" y1="320" x2="225" y2="320" stroke="#45b7d1" stroke-width="2" />
                <text x="175" y="315" text-anchor="middle" font-size="14">W</text>
                
                <line x1="275" y1="320" x2="375" y2="320" stroke="#45b7d1" stroke-width="2" />
                <text x="325" y="315" text-anchor="middle" font-size="14">W</text>
                
                <line x1="425" y1="320" x2="525" y2="320" stroke="#45b7d1" stroke-width="2" stroke-dasharray="5,2" />
                <text x="475" y="315" text-anchor="middle" font-size="14">W</text>
                
                <!-- Input to hidden connections -->
                <line x1="100" y1="245" x2="100" y2="225" stroke="#ff7043" stroke-width="2" />
                <text x="90" y="235" text-anchor="middle" font-size="14">U</text>
                
                <line x1="250" y1="245" x2="250" y2="225" stroke="#ff7043" stroke-width="2" />
                <text x="240" y="235" text-anchor="middle" font-size="14">U</text>
                
                <line x1="400" y1="245" x2="400" y2="225" stroke="#ff7043" stroke-width="2" />
                <text x="390" y="235" text-anchor="middle" font-size="14">U</text>
                
                <!-- Output to hidden connections (key feature of this model) -->
                <line x1="125" y1="130" x2="225" y2="190" stroke="#598234" stroke-width="2.5" />
                <line x1="275" y1="130" x2="375" y2="190" stroke="#598234" stroke-width="2.5" />
                
                <!-- Hidden to output connections -->
                <line x1="100" y1="175" x2="100" y2="155" stroke="#45b7d1" stroke-width="2" />
                <text x="115" y="165" text-anchor="middle" font-size="14">V</text>
                
                <line x1="250" y1="175" x2="250" y2="155" stroke="#45b7d1" stroke-width="2" />
                <text x="265" y="165" text-anchor="middle" font-size="14">V</text>
                
                <line x1="400" y1="175" x2="400" y2="155" stroke="#45b7d1" stroke-width="2" />
                <text x="415" y="165" text-anchor="middle" font-size="14">V</text>
                
                <!-- Special note highlighting the key feature -->
                <text x="350" y="410" text-anchor="middle" font-size="14" font-weight="bold" fill="#598234">
                    Output-to-hidden connections allow for arbitrary probability distributions
                </text>
                <text x="350" y="430" text-anchor="middle" font-size="14" fill="#598234">
                    over sequences of y given sequences of x of the same length
                </text>
            </svg>
            <p class="caption">Figure 7: A conditional recurrent neural network mapping a variable-length sequence of x values into a distribution over sequences of y values of the same length. This RNN contains connections from the previous output to the current state, allowing it to model an arbitrary distribution over sequences of y given sequences of x.</p>
        </div>
        
        <p>
            This kind of model representing a distribution over a sequence given another sequence still has one restriction, 
            which is that the length of both sequences must be the same. To remove this restriction, more advanced architectures 
            like encoder-decoder models or attention mechanisms are required, which are beyond the scope of this chapter.
        </p>
        
        <h3>4.4 Practical Applications</h3>
        <p>
            RNNs conditioned on context have many practical applications:
        </p>
        
        <div class="note">
            <ul>
                <li><strong>Image Captioning:</strong> A fixed-size image vector conditions an RNN that generates a variable-length text description.</li>
                <li><strong>Machine Translation:</strong> A sequence of words in one language conditions the generation of a sequence in another language.</li>
                <li><strong>Speech Recognition:</strong> A sequence of audio features conditions the generation of a transcript.</li>
                <li><strong>Question Answering:</strong> A question conditions the generation of an answer.</li>
            </ul>
        </div>
        
        <p>
            In many of these applications, bidirectional RNNs (discussed in the next section) are often used to better capture 
            context from both past and future in the input sequence, especially when the entire input sequence is available at once.
        </p>
    </section>

    <section id="section5">
        <h2>5. Bidirectional Recurrent Neural Networks</h2>
        
        <h3>5.1 Motivation and Overview</h3>
        <p>
            All of the recurrent neural networks we have considered up to now process information only in the forward 
            direction, from past to future. However, in many applications, we want to predict an output y(t) that may 
            depend on the whole input sequence, not just the past elements x(1), ..., x(t).
        </p>
        
        <p>
            For example, in speech recognition, the correct interpretation of the current sound may depend on sounds 
            that come after it as well as before it. This is particularly true for Japanese, in which the probability 
            that the current phoneme is a vowel is highly dependent on whether the next phoneme is a consonant.
        </p>
        
        <p>
            Bidirectional recurrent neural networks (BiRNNs) were designed to address this need by introducing a second 
            recurrent connection that flows in the opposite temporal direction. This allows the output units to compute 
            a representation that depends on both the past and the future but is most sensitive to the input values 
            around the current time step.
        </p>
        
        <div class="visualization">
            <svg width="700" height="400" viewBox="0 0 700 400">
                <!-- Background -->
                <rect x="0" y="0" width="700" height="400" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold">Bidirectional RNN Architecture</text>
                
                <!-- Time steps -->
                <!-- t-1 -->
                <g transform="translate(100, 180)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t-1)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t-1)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t-1)</text>
                    
                    <!-- Forward hidden state -->
                    <circle cx="0" cy="-80" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="-75" text-anchor="middle" font-size="14">h→(t-1)</text>
                    
                    <!-- Backward hidden state -->
                    <circle cx="0" cy="-140" r="25" fill="#fce4ec" stroke="#ec407a" stroke-width="2" />
                    <text x="0" y="-135" text-anchor="middle" font-size="14">h←(t-1)</text>
                    
                    <circle cx="0" cy="-30" r="25" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                    <text x="0" y="-25" text-anchor="middle" font-size="14">x(t-1)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="-55" x2="0" y2="-5" stroke="#45b7d1" stroke-width="1.5" />
                    <line x1="0" y1="-115" x2="0" y2="-5" stroke="#ec407a" stroke-width="1.5" />
                    <line x1="0" y1="-5" x2="0" y2="-30" stroke="#ff7043" stroke-width="0.5" opacity="0" />
                </g>
                
                <!-- t -->
                <g transform="translate(250, 180)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t)</text>
                    
                    <!-- Forward hidden state -->
                    <circle cx="0" cy="-80" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="-75" text-anchor="middle" font-size="14">h→(t)</text>
                    
                    <!-- Backward hidden state -->
                    <circle cx="0" cy="-140" r="25" fill="#fce4ec" stroke="#ec407a" stroke-width="2" />
                    <text x="0" y="-135" text-anchor="middle" font-size="14">h←(t)</text>
                    
                    <circle cx="0" cy="-30" r="25" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                    <text x="0" y="-25" text-anchor="middle" font-size="14">x(t)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="-55" x2="0" y2="-5" stroke="#45b7d1" stroke-width="1.5" />
                    <line x1="0" y1="-115" x2="0" y2="-5" stroke="#ec407a" stroke-width="1.5" />
                    <line x1="0" y1="-5" x2="0" y2="-30" stroke="#ff7043" stroke-width="0.5" opacity="0" />
                </g>
                
                <!-- t+1 -->
                <g transform="translate(400, 180)">
                    <circle cx="0" cy="0" r="25" fill="#f9d5e5" stroke="#d64161" stroke-width="2" />
                    <text x="0" y="5" text-anchor="middle" font-size="14">o(t+1)</text>
                    
                    <circle cx="0" cy="70" r="20" fill="#eeeeee" stroke="#888" stroke-width="1" />
                    <text x="0" y="75" text-anchor="middle" font-size="14">L(t+1)</text>
                    
                    <circle cx="0" cy="130" r="25" fill="#d1f0a6" stroke="#598234" stroke-width="2" />
                    <text x="0" y="135" text-anchor="middle" font-size="14">y(t+1)</text>
                    
                    <!-- Forward hidden state -->
                    <circle cx="0" cy="-80" r="25" fill="#e8f4f8" stroke="#45b7d1" stroke-width="2" />
                    <text x="0" y="-75" text-anchor="middle" font-size="14">h→(t+1)</text>
                    
                    <!-- Backward hidden state -->
                    <circle cx="0" cy="-140" r="25" fill="#fce4ec" stroke="#ec407a" stroke-width="2" />
                    <text x="0" y="-135" text-anchor="middle" font-size="14">h←(t+1)</text>
                    
                    <circle cx="0" cy="-30" r="25" fill="#ffccbc" stroke="#ff7043" stroke-width="2" />
                    <text x="0" y="-25" text-anchor="middle" font-size="14">x(t+1)</text>
                    
                    <!-- Connections -->
                    <line x1="0" y1="25" x2="0" y2="50" stroke="#d64161" stroke-width="1.5" />
                    <line x1="0" y1="90" x2="0" y2="105" stroke="#888" stroke-width="1.5" />
                    <line x1="0" y1="-55" x2="0" y2="-5" stroke="#45b7d1" stroke-width="1.5" />
                    <line x1="0" y1="-115" x2="0" y2="-5" stroke="#ec407a" stroke-width="1.5" />
                    <line x1="0" y1="-5" x2="0" y2="-30" stroke="#ff7043" stroke-width="0.5" opacity="0" />
                </g>
                
                <!-- Continue -->
                <text x="550" y="180" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="100" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="40" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="250" text-anchor="middle" font-size="20">...</text>
                <text x="550" y="310" text-anchor="middle" font-size="20">...</text>
                
                <!-- Forward recurrent connections -->
                <line x1="125" y1="100" x2="225" y2="100" stroke="#45b7d1" stroke-width="2" />
                <text x="175" y="95" text-anchor="middle" font-size="14">W→</text>
                
                <line x1="275" y1="100" x2="375" y2="100" stroke="#45b7d1" stroke-width="2" />
                <text x="325" y="95" text-anchor="middle" font-size="14">W→</text>
                
                <line x1="425" y1="100" x2="525" y2="100" stroke="#45b7d1" stroke-width="2" stroke-dasharray="5,2" />
                <text x="475" y="95" text-anchor="middle" font-size="14">W→</text>
                
                <!-- Backward recurrent connections -->
                <line x1="375" y1="40" x2="275" y2="40" stroke="#ec407a" stroke-width="2" />
                <text x="325" y="35" text-anchor="middle" font-size="14">W←</text>
                
                <line x1="225" y1="40" x2="125" y2="40" stroke="#ec407a" stroke-width="2" />
                <text x="175" y="35" text-anchor="middle" font-size="14">W←</text>
                
                <line x1="525" y1="40" x2="425" y2="40" stroke="#ec407a" stroke-width="2" stroke-dasharray="5,2" />
                <text x="475" y="35" text-anchor="middle" font-size="14">W←</text>
                
                <!-- Input to hidden connections -->
                <line x1="100" y1="150" x2="100" y2="180" stroke="#ff7043" stroke-width="1.5" />
                <line x1="250" y1="150" x2="250" y2="180" stroke="#ff7043" stroke-width="1.5" />
                <line x1="400" y1="150" x2="400" y2="180" stroke="#ff7043" stroke-width="1.5" />
                
                <!-- Key element: both directions feed into output -->
                <line x1="100" y1="100" x2="100" y2="25" stroke="#45b7d1" stroke-width="2" />
                <line x1="100" y1="40" x2="100" y2="25" stroke="#ec407a" stroke-width="2" />
                
                <line x1="250" y1="100" x2="250" y2="25" stroke="#45b7d1" stroke-width="2" />
                <line x1="250" y1="40" x2="250" y2="25" stroke="#ec407a" stroke-width="2" />
                
                <line x1="400" y1="100" x2="400" y2="25" stroke="#45b7d1" stroke-width="2" />
                <line x1="400" y1="40" x2="400" y2="25" stroke="#ec407a" stroke-width="2" />
            </svg>
            <p class="caption">Figure 8: A bidirectional recurrent neural network. Information flows in both directions: the forward hidden states h→(t) process information from left to right, while the backward hidden states h←(t) process information from right to left. Both are combined to predict the output at each time step.</p>
        </div>
        
        <h3>5.2 Architecture and Information Flow</h3>
        <p>
            A bidirectional RNN combines two RNNs: one that moves forward through time (from left to right) and another 
            that moves backward through time (from right to left). This structure allows the output to depend on future 
            inputs as well as past ones.
        </p>
        
        <p>
            As illustrated in Figure 8, the forward states h→ process the sequence from left to right, while the backward 
            states h← process it from right to left. The output units o can get information from both directions.
        </p>
        
        <p>
            The equations describing a bidirectional RNN with forward and backward hidden states are:
        </p>
        
        <div class="equation">
            \begin{align}
            h^{\rightarrow(t)} &= f(U^{\rightarrow}x^{(t)} + W^{\rightarrow}h^{\rightarrow(t-1)} + b^{\rightarrow}) \\
            h^{\leftarrow(t)} &= f(U^{\leftarrow}x^{(t)} + W^{\leftarrow}h^{\leftarrow(t+1)} + b^{\leftarrow}) \\
            o^{(t)} &= V^{\rightarrow}h^{\rightarrow(t)} + V^{\leftarrow}h^{\leftarrow(t)} + c
            \end{align}
        </div>
        
        <p>
            where U→, W→, b→ are the parameters for the forward RNN, U←, W←, b← are the parameters for the backward RNN, 
            and V→, V←, c are the output parameters.
        </p>
        
        <div class="note">
            <p>
                <strong>Important Note:</strong> Training bidirectional RNNs requires having the entire sequence available 
                beforehand, as the backward pass needs to start from the end of the sequence. This makes them unsuitable for 
                online prediction tasks where future inputs are not available at prediction time.
            </p>
        </div>
        
        <h3>5.3 Applications of Bidirectional RNNs</h3>
        <p>
            Bidirectional RNNs are especially useful for:
        </p>
        
        <ul>
            <li>
                <strong>Natural Language Processing:</strong> In tasks like named entity recognition, part-of-speech tagging, 
                and sentiment analysis, the meaning of a word often depends on both its preceding and following context.
            </li>
            <li>
                <strong>Speech Recognition:</strong> The interpretation of phonemes can depend on sounds both before and after.
            </li>
            <li>
                <strong>Bioinformatics:</strong> Protein structure prediction and DNA sequence analysis benefit from bidirectional 
                context.
            </li>
            <li>
                <strong>Machine Translation:</strong> Understanding the complete source sentence before translation improves accuracy.
            </li>
        </ul>
        
        <h3>5.4 Combining with Deep Architectures</h3>
        <p>
            Bidirectional RNNs can be combined with other architectural innovations in deep learning:
        </p>
        
        <ul>
            <li>
                <strong>Bidirectional LSTMs and GRUs:</strong> The bidirectional concept can be applied to LSTM and GRU 
                architectures, creating Bidirectional LSTMs (BiLSTMs) and Bidirectional GRUs (BiGRUs), which better capture 
                long-term dependencies in both directions.
            </li>
            <li>
                <strong>Deep Bidirectional Networks:</strong> Multiple layers of bidirectional RNNs can be stacked to form 
                deep architectures, where each layer receives input from both the forward and backward states of the layer below.
            </li>
            <li>
                <strong>Attention Mechanisms:</strong> Bidirectional RNNs are often used with attention mechanisms, which allow 
                the model to focus on different parts of the input sequence when producing each element of the output sequence.
            </li>
        </ul>
        
        <h3>5.5 Limitations and Considerations</h3>
        <p>
            While bidirectional RNNs offer significant advantages, they also have some limitations:
        </p>
        
        <ul>
            <li>
                <strong>Requirement for Complete Sequences:</strong> They need access to the entire sequence before processing 
                can begin, making them unsuitable for real-time or streaming applications.
            </li>
            <li>
                <strong>Increased Computational Complexity:</strong> Running two RNNs instead of one nearly doubles the 
                computational cost and memory requirements.
            </li>
            <li>
                <strong>Training Challenges:</strong> Like all RNNs, bidirectional RNNs can suffer from vanishing and exploding 
                gradient problems, especially when sequences are long.
            </li>
        </ul>
        
        <p>
            Despite these limitations, bidirectional RNNs represent a powerful extension to the standard RNN architecture 
            and have become a standard tool in many sequence modeling applications where context from both directions is valuable.
        </p>
    </section>
</body>
</html> 