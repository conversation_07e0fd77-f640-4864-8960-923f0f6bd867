<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Continuous Random Variables and Their Distributions</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .definition-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .property-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .property-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .property-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .property-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .lotus-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .lotus-title {
            color: #d63031;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Continuous Random Variables</h1>
            <div class="subtitle">Distributions, PDFs, and Transformations</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Introduction to Continuous Random Variables</strong> - From discrete to continuous</li>
                <li><strong>Probability Density Function (PDF)</strong> - The continuous analog of PMF</li>
                <li><strong>Expected Value and Variance</strong> - Integrals replace summations</li>
                <li><strong>Functions of Continuous Random Variables</strong> - Transformations and their distributions</li>
                <li><strong>Method of Transformations</strong> - Direct PDF computation techniques</li>
                <li><strong>Solved Problems</strong> - Practical applications and examples</li>
            </ul>
        </div>

        <h2>🌊 Introduction to Continuous Random Variables</h2>
        
        <div class="concept-box">
            <p><strong>Key Difference from Discrete Variables:</strong></p>
            <p>For continuous random variables, individual points have probability zero: $P(X = x) = 0$ for all $x$. Instead, we talk about probabilities over intervals.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Continuous random variables are characterized by smooth, continuous CDFs without jumps."
            </p>
        </div>

        <div class="definition-box">
            <h3>Definition 4.1: Continuous Random Variable</h3>
            <p>A random variable $X$ with CDF $F_X(x)$ is said to be <strong>continuous</strong> if $F_X(x)$ is a continuous function for all $x \in \mathbb{R}$.</p>
            <p><strong>Key Property:</strong> The CDF has no jumps, which reflects the fact that $P(X = x) = 0$ for all $x$.</p>
        </div>

        <div class="example-box">
            <h4>Example 4.1: Uniform Distribution</h4>
            <p><strong>Problem:</strong> Choose a real number uniformly at random from interval $[a,b]$. Find the CDF.</p>
            <p><strong>Solution:</strong> "Uniformly at random" means all intervals of the same length have the same probability.</p>
            <div class="formula-box">
                $$F_X(x) = \begin{cases}
                0 & \text{for } x < a \\
                \frac{x-a}{b-a} & \text{for } a \leq x \leq b \\
                1 & \text{for } x > b
                \end{cases}$$
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Discrete vs Continuous CDF Comparison
                </text>
                
                <!-- Left plot: Discrete CDF -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Discrete CDF (Step Function)
                </text>
                
                <!-- Left axes -->
                <line x1="80" y1="350" x2="320" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="100" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- Discrete CDF steps -->
                <line x1="80" y1="350" x2="150" y2="350" stroke="#e74c3c" stroke-width="3"/>
                <line x1="150" y1="300" x2="200" y2="300" stroke="#e74c3c" stroke-width="3"/>
                <line x1="200" y1="250" x2="250" y2="250" stroke="#e74c3c" stroke-width="3"/>
                <line x1="250" y1="150" x2="320" y2="150" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Vertical jumps -->
                <line x1="150" y1="350" x2="150" y2="300" stroke="#e74c3c" stroke-width="3" stroke-dasharray="3,3"/>
                <line x1="200" y1="300" x2="200" y2="250" stroke="#e74c3c" stroke-width="3" stroke-dasharray="3,3"/>
                <line x1="250" y1="250" x2="250" y2="150" stroke="#e74c3c" stroke-width="3" stroke-dasharray="3,3"/>
                
                <!-- Jump points -->
                <circle cx="150" cy="300" r="3" fill="#e74c3c"/>
                <circle cx="200" cy="250" r="3" fill="#e74c3c"/>
                <circle cx="250" cy="150" r="3" fill="#e74c3c"/>
                
                <!-- Open circles -->
                <circle cx="150" cy="350" r="3" fill="white" stroke="#e74c3c" stroke-width="2"/>
                <circle cx="200" cy="300" r="3" fill="white" stroke="#e74c3c" stroke-width="2"/>
                <circle cx="250" cy="250" r="3" fill="white" stroke="#e74c3c" stroke-width="2"/>
                
                <!-- Right plot: Continuous CDF -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Continuous CDF (Smooth Function)
                </text>
                
                <!-- Right axes -->
                <line x1="480" y1="350" x2="720" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="480" y1="350" x2="480" y2="100" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- Continuous CDF curve -->
                <path d="M 480 350 L 550 350 Q 600 300 650 150 L 720 150" 
                      stroke="#3498db" stroke-width="3" fill="none"/>
                
                <!-- Labels -->
                <text x="200" y="380" text-anchor="middle" font-size="12" fill="#2c3e50">Jumps at discrete values</text>
                <text x="600" y="380" text-anchor="middle" font-size="12" fill="#2c3e50">Smooth, no jumps</text>
                
                <!-- Y-axis labels -->
                <text x="70" y="355" text-anchor="end" font-size="10" fill="#2c3e50">0</text>
                <text x="70" y="155" text-anchor="end" font-size="10" fill="#2c3e50">1</text>
                <text x="470" y="355" text-anchor="end" font-size="10" fill="#2c3e50">0</text>
                <text x="470" y="155" text-anchor="end" font-size="10" fill="#2c3e50">1</text>
                
                <!-- Key insight -->
                <rect x="250" y="200" width="300" height="80" fill="white" stroke="#f39c12" stroke-width="2" rx="5"/>
                <text x="400" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Key Insight:</text>
                <text x="400" y="240" text-anchor="middle" font-size="11" fill="#2c3e50">Discrete: P(X = x) > 0 → Jumps in CDF</text>
                <text x="400" y="255" text-anchor="middle" font-size="11" fill="#2c3e50">Continuous: P(X = x) = 0 → Smooth CDF</text>
                <text x="400" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">Focus shifts to intervals: P(a < X ≤ b)</text>
            </svg>
        </div>

        <h2>📊 Probability Density Function (PDF)</h2>
        
        <div class="concept-box">
            <p><strong>Why do we need PDF?</strong></p>
            <p>Since $P(X = x) = 0$ for continuous variables, PMF doesn't work. Instead, we use the <strong>Probability Density Function (PDF)</strong> - the density of probability per unit length.</p>
        </div>

        <div class="definition-box">
            <h3>Definition 4.2: Probability Density Function</h3>
            <p>For a continuous random variable $X$ with CDF $F_X(x)$, the <strong>PDF</strong> is defined as:</p>
            <div class="formula-box">
                $$f_X(x) = \frac{dF_X(x)}{dx} = F'_X(x)$$
            </div>
            <p><strong>Intuitive meaning:</strong> $f_X(x)$ gives the "probability density" at point $x$.</p>
            <div class="formula-box">
                $$f_X(x) = \lim_{\Delta \to 0^+} \frac{P(x < X \leq x + \Delta)}{\Delta}$$
            </div>
        </div>

        <div class="example-box">
            <h4>Uniform Distribution PDF</h4>
            <p>For $X \sim \text{Uniform}(a,b)$, taking the derivative of the CDF:</p>
            <div class="formula-box">
                $$f_X(x) = \begin{cases}
                \frac{1}{b-a} & \text{for } a < x < b \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>
            <p><strong>Why constant?</strong> "Uniform" means equal density everywhere in the interval.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    PDF Visualization: Uniform(2, 6) Distribution
                </text>

                <!-- Axes -->
                <line x1="80" y1="300" x2="720" y2="300" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="300" x2="80" y2="50" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="150" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">0</text>
                <text x="250" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                <text x="450" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">6</text>
                <text x="550" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">8</text>
                <text x="400" y="340" text-anchor="middle" font-size="14" fill="#2c3e50">x</text>

                <!-- Y-axis labels -->
                <text x="70" y="305" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="70" y="200" text-anchor="end" font-size="12" fill="#2c3e50">0.25</text>
                <text x="70" y="100" text-anchor="end" font-size="12" fill="#2c3e50">0.5</text>
                <text x="40" y="175" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 40 175)">f_X(x)</text>

                <!-- PDF rectangle -->
                <rect x="250" y="200" width="200" height="100" fill="#3498db" opacity="0.6" stroke="#3498db" stroke-width="2"/>

                <!-- PDF value label -->
                <text x="350" y="180" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    f_X(x) = 1/(6-2) = 0.25
                </text>

                <!-- Zero regions -->
                <line x1="80" y1="300" x2="250" y2="300" stroke="#e74c3c" stroke-width="3"/>
                <line x1="450" y1="300" x2="720" y2="300" stroke="#e74c3c" stroke-width="3"/>

                <!-- Area annotation -->
                <text x="350" y="250" text-anchor="middle" font-size="12" fill="white" font-weight="bold">
                    Area = 1
                </text>
                <text x="350" y="265" text-anchor="middle" font-size="11" fill="white">
                    (4 × 0.25 = 1)
                </text>

                <!-- Key properties box -->
                <rect x="500" y="60" width="280" height="120" fill="white" stroke="#f39c12" stroke-width="2" rx="5"/>
                <text x="640" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">PDF Properties:</text>
                <text x="640" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">1. f_X(x) ≥ 0 for all x</text>
                <text x="640" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">2. ∫_{-∞}^{∞} f_X(x)dx = 1</text>
                <text x="640" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">3. P(a < X ≤ b) = ∫_a^b f_X(x)dx</text>
                <text x="640" y="145" text-anchor="middle" font-size="11" fill="#2c3e50">4. F_X(x) = ∫_{-∞}^x f_X(u)du</text>
                <text x="640" y="160" text-anchor="middle" font-size="11" fill="#2c3e50">5. f_X(x) can be > 1 (it's density!)</text>
            </svg>
        </div>

        <div class="property-grid">
            <div class="property-card">
                <div class="property-title">🔢 PDF vs PMF</div>
                <ul>
                    <li><strong>PMF:</strong> $P_X(x) = P(X = x)$ (actual probabilities)</li>
                    <li><strong>PDF:</strong> $f_X(x)$ (probability density, not probability)</li>
                    <li><strong>Key difference:</strong> PDF can exceed 1, PMF cannot</li>
                    <li><strong>Interpretation:</strong> $P(x < X \leq x + \delta) \approx f_X(x) \cdot \delta$</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">📊 Working with PDFs</div>
                <ul>
                    <li><strong>Probabilities:</strong> Always integrate over intervals</li>
                    <li><strong>Area under curve:</strong> Must equal 1</li>
                    <li><strong>Higher density:</strong> More likely values</li>
                    <li><strong>CDF recovery:</strong> $F_X(x) = \int_{-\infty}^x f_X(u)du$</li>
                </ul>
            </div>
        </div>

        <div class="example-box">
            <h4>Example 4.2: Finding the Constant</h4>
            <p><strong>Problem:</strong> Let $X$ have PDF $f_X(x) = ce^{-x}$ for $x \geq 0$, and $0$ otherwise. Find $c$.</p>
            <p><strong>Solution:</strong> Use the fact that the total area under the PDF must equal 1:</p>
            <div class="formula-box">
                $$1 = \int_{-\infty}^{\infty} f_X(x)dx = \int_0^{\infty} ce^{-x}dx = c[-e^{-x}]_0^{\infty} = c[0 - (-1)] = c$$
            </div>
            <p>Therefore, $c = 1$, and $f_X(x) = e^{-x}$ for $x \geq 0$ (exponential distribution).</p>
        </div>

        <h2>🎯 Expected Value and Variance</h2>

        <div class="concept-box">
            <p><strong>From Discrete to Continuous:</strong> The theory is very similar, but summations become integrals and PMFs become PDFs.</p>
        </div>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Concept</th>
                    <th>Discrete</th>
                    <th>Continuous</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Expected Value</strong></td>
                    <td>$EX = \sum_{x_k} x_k P_X(x_k)$</td>
                    <td>$EX = \int_{-\infty}^{\infty} x f_X(x)dx$</td>
                </tr>
                <tr>
                    <td><strong>LOTUS</strong></td>
                    <td>$E[g(X)] = \sum_{x_k} g(x_k) P_X(x_k)$</td>
                    <td>$E[g(X)] = \int_{-\infty}^{\infty} g(x) f_X(x)dx$</td>
                </tr>
                <tr>
                    <td><strong>Variance</strong></td>
                    <td>$\text{Var}(X) = E[X^2] - (EX)^2$</td>
                    <td>$\text{Var}(X) = E[X^2] - (EX)^2$</td>
                </tr>
                <tr>
                    <td><strong>Linearity</strong></td>
                    <td>$E[aX + b] = aEX + b$</td>
                    <td>$E[aX + b] = aEX + b$</td>
                </tr>
            </tbody>
        </table>

        <div class="example-box">
            <h4>Example 4.3: Uniform Distribution Mean</h4>
            <p><strong>Problem:</strong> Find $EX$ for $X \sim \text{Uniform}(a,b)$.</p>
            <p><strong>Solution:</strong></p>
            <div class="formula-box">
                $$EX = \int_{-\infty}^{\infty} x f_X(x)dx = \int_a^b x \cdot \frac{1}{b-a}dx$$
                $$= \frac{1}{b-a} \int_a^b x dx = \frac{1}{b-a} \left[\frac{x^2}{2}\right]_a^b = \frac{1}{b-a} \cdot \frac{b^2-a^2}{2} = \frac{a+b}{2}$$
            </div>
            <p><strong>Intuitive result:</strong> The mean is the midpoint of the interval!</p>
        </div>

        <div class="lotus-box">
            <div class="lotus-title">🪷 Law of the Unconscious Statistician (LOTUS)</div>
            <p>For continuous random variables, LOTUS becomes:</p>
            <div class="formula-box">
                $$E[g(X)] = \int_{-\infty}^{\infty} g(x) f_X(x)dx$$
            </div>
            <p><strong>Power of LOTUS:</strong> You can find $E[g(X)]$ directly using the PDF of $X$, without finding the distribution of $g(X)$ first!</p>
        </div>

        <div class="example-box">
            <h4>Example 4.4: Using LOTUS</h4>
            <p><strong>Problem:</strong> Let $X$ have PDF $f_X(x) = 2x$ for $0 \leq x \leq 1$. Find $E[X^n]$.</p>
            <p><strong>Solution:</strong> Using LOTUS:</p>
            <div class="formula-box">
                $$E[X^n] = \int_{-\infty}^{\infty} x^n f_X(x)dx = \int_0^1 x^n \cdot 2x dx = \int_0^1 2x^{n+1}dx$$
                $$= 2 \left[\frac{x^{n+2}}{n+2}\right]_0^1 = \frac{2}{n+2}$$
            </div>
        </div>

        <h2>🔄 Functions of Continuous Random Variables</h2>

        <div class="concept-box">
            <p>If $X$ is continuous and $Y = g(X)$, then $Y$ is also a random variable. We need to find the distribution of $Y$.</p>
            <p><strong>General approach:</strong> Start with the CDF, then differentiate to get the PDF.</p>
        </div>

        <div class="example-box">
            <h4>Example 4.7: Exponential Transformation</h4>
            <p><strong>Problem:</strong> Let $X \sim \text{Uniform}(0,1)$ and $Y = e^X$. Find the PDF of $Y$.</p>
            <p><strong>Solution:</strong></p>
            <p><span class="step-number">1</span><strong>Find the range:</strong> Since $X \in [0,1]$ and $e^x$ is increasing, $Y \in [1,e]$.</p>
            <p><span class="step-number">2</span><strong>Find the CDF:</strong> For $y \in [1,e]$:</p>
            <div class="formula-box">
                $$F_Y(y) = P(Y \leq y) = P(e^X \leq y) = P(X \leq \ln y) = F_X(\ln y) = \ln y$$
            </div>
            <p><span class="step-number">3</span><strong>Find the PDF:</strong> Differentiate the CDF:</p>
            <div class="formula-box">
                $$f_Y(y) = F'_Y(y) = \frac{d}{dy}(\ln y) = \frac{1}{y} \text{ for } 1 \leq y \leq e$$
            </div>
        </div>

        <h2>⚙️ Method of Transformations</h2>

        <div class="concept-box">
            <p>When $g(x)$ satisfies certain properties, we can find the PDF of $Y = g(X)$ directly without going through the CDF first.</p>
        </div>

        <div class="theorem-box">
            <h3>Theorem 4.1: Transformation Method (Monotonic Case)</h3>
            <p>Suppose $X$ is continuous and $g: \mathbb{R} \to \mathbb{R}$ is <strong>strictly monotonic</strong> and <strong>differentiable</strong>. If $Y = g(X)$, then:</p>
            <div class="formula-box">
                $$f_Y(y) = \begin{cases}
                f_X(x_1) \left|\frac{dx_1}{dy}\right| & \text{where } g(x_1) = y \\
                0 & \text{if } g(x) = y \text{ has no solution}
                \end{cases}$$
            </div>
            <p><strong>Key insight:</strong> $\left|\frac{dx_1}{dy}\right| = \frac{1}{|g'(x_1)|}$ (inverse function theorem)</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Method of Transformations Visualization
                </text>

                <!-- Left: Original distribution -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    X ~ Uniform(0,1)
                </text>

                <!-- Left axes -->
                <line x1="80" y1="300" x2="320" y2="300" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="300" x2="80" y2="150" stroke="#2c3e50" stroke-width="2"/>

                <!-- Uniform PDF -->
                <rect x="120" y="200" width="160" height="100" fill="#3498db" opacity="0.6" stroke="#3498db" stroke-width="2"/>
                <text x="200" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">f_X(x) = 1</text>

                <!-- Transformation arrow -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7"
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12" />
                    </marker>
                </defs>
                <path d="M 320 200 Q 400 150 480 200" stroke="#f39c12" stroke-width="4" fill="none" marker-end="url(#arrowhead)"/>
                <text x="400" y="140" text-anchor="middle" font-size="12" fill="#f39c12" font-weight="bold">Y = g(X)</text>

                <!-- Right: Transformed distribution -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Y = g(X)
                </text>

                <!-- Right axes -->
                <line x1="480" y1="300" x2="720" y2="300" stroke="#2c3e50" stroke-width="2"/>
                <line x1="480" y1="300" x2="480" y2="150" stroke="#2c3e50" stroke-width="2"/>

                <!-- Transformed PDF (example: decreasing function) -->
                <path d="M 520 180 Q 580 200 620 250 L 680 280" stroke="#e74c3c" stroke-width="3" fill="none"/>
                <text x="600" y="170" text-anchor="middle" font-size="12" fill="#2c3e50">f_Y(y)</text>

                <!-- Formula box -->
                <rect x="200" y="320" width="400" height="60" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="400" y="340" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Transformation Formula:
                </text>
                <text x="400" y="360" text-anchor="middle" font-size="11" fill="#2c3e50">
                    f_Y(y) = f_X(x₁) × |dx₁/dy| = f_X(x₁) / |g'(x₁)|
                </text>
                <text x="400" y="375" text-anchor="middle" font-size="10" fill="#2c3e50">
                    where g(x₁) = y
                </text>
            </svg>
        </div>

        <div class="example-box">
            <h4>Example 4.9: Using the Transformation Method</h4>
            <p><strong>Problem:</strong> Let $X$ have PDF $f_X(x) = 4x^3$ for $0 < x \leq 1$. Find the PDF of $Y = \frac{1}{X}$.</p>
            <p><strong>Solution:</strong></p>
            <p><span class="step-number">1</span><strong>Check conditions:</strong> $g(x) = \frac{1}{x}$ is strictly decreasing and differentiable on $(0,1]$.</p>
            <p><span class="step-number">2</span><strong>Find the range:</strong> $R_Y = [1, \infty)$</p>
            <p><span class="step-number">3</span><strong>Apply the formula:</strong></p>
            <ul>
                <li>$g'(x) = -\frac{1}{x^2}$</li>
                <li>For $y \geq 1$: $x_1 = g^{-1}(y) = \frac{1}{y}$</li>
                <li>$|g'(x_1)| = \left|-\frac{1}{x_1^2}\right| = \frac{1}{x_1^2} = y^2$</li>
            </ul>
            <div class="formula-box">
                $$f_Y(y) = \frac{f_X(x_1)}{|g'(x_1)|} = \frac{4x_1^3}{y^2} = \frac{4 \cdot (1/y)^3}{y^2} = \frac{4}{y^5} \text{ for } y \geq 1$$
            </div>
        </div>

        <div class="theorem-box">
            <h3>Theorem 4.2: General Transformation Method</h3>
            <p>If $g$ is not monotonic, partition the domain into regions where $g$ is monotonic. Then:</p>
            <div class="formula-box">
                $$f_Y(y) = \sum_{i=1}^n \frac{f_X(x_i)}{|g'(x_i)|}$$
            </div>
            <p>where $x_1, x_2, \ldots, x_n$ are all real solutions to $g(x) = y$.</p>
        </div>

        <div class="example-box">
            <h4>Example 4.10: Non-Monotonic Transformation</h4>
            <p><strong>Problem:</strong> Let $X$ have PDF $f_X(x) = \frac{1}{\sqrt{2\pi}}e^{-x^2/2}$ and $Y = X^2$. Find $f_Y(y)$.</p>
            <p><strong>Solution:</strong></p>
            <p><span class="step-number">1</span><strong>Partition domain:</strong> $g(x) = x^2$ is decreasing on $(-\infty, 0)$ and increasing on $(0, \infty)$.</p>
            <p><span class="step-number">2</span><strong>Find solutions:</strong> For $y > 0$, we have $x_1 = \sqrt{y}$ and $x_2 = -\sqrt{y}$.</p>
            <p><span class="step-number">3</span><strong>Apply formula:</strong> $g'(x) = 2x$, so:</p>
            <div class="formula-box">
                $$f_Y(y) = \frac{f_X(\sqrt{y})}{|2\sqrt{y}|} + \frac{f_X(-\sqrt{y})}{|-2\sqrt{y}|} = \frac{f_X(\sqrt{y}) + f_X(-\sqrt{y})}{2\sqrt{y}}$$
            </div>
            <p>Since $f_X$ is symmetric: $f_X(\sqrt{y}) = f_X(-\sqrt{y})$, so:</p>
            <div class="formula-box">
                $$f_Y(y) = \frac{2f_X(\sqrt{y})}{2\sqrt{y}} = \frac{f_X(\sqrt{y})}{\sqrt{y}} = \frac{1}{\sqrt{2\pi y}}e^{-y/2} \text{ for } y > 0$$
            </div>
        </div>

        <h2>🎓 Key Takeaways and Summary</h2>

        <div class="property-grid">
            <div class="property-card">
                <div class="property-title">📈 Continuous vs Discrete</div>
                <ul>
                    <li><strong>Individual points:</strong> $P(X = x) = 0$ for continuous</li>
                    <li><strong>Focus on intervals:</strong> $P(a < X \leq b)$</li>
                    <li><strong>Smooth CDFs:</strong> No jumps in continuous case</li>
                    <li><strong>PDF replaces PMF:</strong> Density, not probability</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">🔧 Working with PDFs</div>
                <ul>
                    <li><strong>Always integrate:</strong> For probabilities over intervals</li>
                    <li><strong>Total area = 1:</strong> $\int_{-\infty}^{\infty} f_X(x)dx = 1$</li>
                    <li><strong>Can exceed 1:</strong> PDF is density, not probability</li>
                    <li><strong>Higher values:</strong> More likely regions</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">🎯 Expectations</div>
                <ul>
                    <li><strong>Replace sums with integrals:</strong> $EX = \int x f_X(x)dx$</li>
                    <li><strong>LOTUS still works:</strong> $E[g(X)] = \int g(x) f_X(x)dx$</li>
                    <li><strong>Linearity preserved:</strong> $E[aX + b] = aEX + b$</li>
                    <li><strong>Variance formula:</strong> $\text{Var}(X) = E[X^2] - (EX)^2$</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">🔄 Transformations</div>
                <ul>
                    <li><strong>CDF method:</strong> Always works, start with $F_Y(y)$</li>
                    <li><strong>Transformation method:</strong> Direct PDF formula when applicable</li>
                    <li><strong>Monotonic case:</strong> $f_Y(y) = f_X(x_1)/|g'(x_1)|$</li>
                    <li><strong>Non-monotonic:</strong> Sum over all solutions</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Study Strategy:</strong></p>
            <ul>
                <li><strong>Understand the concepts:</strong> Focus on the intuition behind PDFs and transformations</li>
                <li><strong>Practice integration:</strong> Most continuous probability involves integration</li>
                <li><strong>Master LOTUS:</strong> It's your best friend for finding expectations</li>
                <li><strong>Transformation techniques:</strong> Know when to use CDF method vs transformation method</li>
                <li><strong>Check your work:</strong> PDFs must integrate to 1, expectations should make intuitive sense</li>
            </ul>
        </div>

        <div class="warning">
            <p><strong>Common Mistakes to Avoid:</strong></p>
            <ul>
                <li>Treating PDF values as probabilities (they can exceed 1!)</li>
                <li>Forgetting absolute value in transformation formula</li>
                <li>Not checking that PDF integrates to 1</li>
                <li>Confusing $P(X = x)$ with $f_X(x)$ for continuous variables</li>
            </ul>
        </div>

    </div>
</body>
</html>
