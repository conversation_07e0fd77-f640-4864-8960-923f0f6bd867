<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Convolutional Networks: Advanced Concepts</title>
    
    <!-- MathJax Configuration -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
            --text-color: #333;
            --light-text: #f8f9fa;
            --border-radius: 8px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #fff;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--secondary-color);
            color: var(--light-text);
            padding: 30px 20px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        h2 {
            font-size: 2rem;
            color: var(--secondary-color);
            margin: 30px 0 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
        }
        
        h3 {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin: 25px 0 15px;
        }
        
        p {
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: var(--light-bg);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background-color: white;
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .visualization {
            margin: 30px auto;
            text-align: center;
            max-width: 100%;
        }
        
        .note {
            background-color: #fef9e7;
            border-left: 4px solid #f1c40f;
            padding: 15px;
            margin: 20px 0;
        }
        
        .important {
            background-color: #fadbd8;
            border-left: 4px solid var(--accent-color);
            padding: 15px;
            margin: 20px 0;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            h2 {
                font-size: 1.6rem;
            }
            
            h3 {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Convolutional Networks: Advanced Concepts</h1>
        <p>A detailed exploration of convolution variants and their theoretical foundations</p>
    </header>
    
    <div class="outline">
        <h2>Tutorial Outline</h2>
        <ol>
            <li>Introduction</li>
            <li>Convolution and Pooling as an Infinitely Strong Prior
                <ul>
                    <li>Understanding Priors in Deep Learning</li>
                    <li>Weak vs. Strong Priors</li>
                    <li>Convolution as an Infinitely Strong Prior</li>
                    <li>Implications and Insights</li>
                </ul>
            </li>
            <li>Variants of the Basic Convolution Function
                <ul>
                    <li>Multi-Channel Convolution</li>
                    <li>Strided Convolution</li>
                    <li>Zero Padding Strategies</li>
                    <li>Locally Connected Layers</li>
                    <li>Tiled Convolution</li>
                    <li>Backpropagation in Convolutional Networks</li>
                </ul>
            </li>
            <li>Summary and Key Takeaways</li>
        </ol>
    </div>
    
    <!-- Introduction Section -->
    <div class="section" id="introduction">
        <h2>1. Introduction</h2>
        <p>Convolutional neural networks (CNNs) have revolutionized the field of computer vision and beyond, providing state-of-the-art results in image classification, object detection, segmentation, and many other tasks. While the basic principles of convolution in neural networks are relatively straightforward, there are many nuances and variations that can significantly impact network performance and efficiency.</p>
        
        <p>This tutorial explores two advanced topics in convolutional networks:</p>
        <ol>
            <li>How convolution and pooling operations can be understood as imposing an infinitely strong prior on the network's weights</li>
            <li>Various implementations and modifications of the basic convolution operation used in practice</li>
        </ol>
        
        <p>Understanding these concepts will provide deeper insights into why convolutional networks work so well for certain problems, when they might struggle, and how to choose the right convolutional architecture for specific tasks.</p>
        
        <div class="visualization">
            <svg width="800" height="200" viewBox="0 0 800 200">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="200" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Input Image -->
                <rect x="50" y="70" width="100" height="80" fill="#3498db" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="100" y="115" text-anchor="middle" fill="white" font-weight="bold">Input</text>
                
                <!-- Convolution Layers -->
                <rect x="220" y="40" width="90" height="50" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="265" y="65" text-anchor="middle" fill="white" font-size="12">Conv1</text>
                
                <rect x="220" y="110" width="90" height="50" fill="#e74c3c" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="265" y="135" text-anchor="middle" fill="white" font-size="12">Conv2</text>
                
                <!-- Pooling Layers -->
                <rect x="380" y="40" width="90" height="50" fill="#f1c40f" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="425" y="65" text-anchor="middle" fill="white" font-size="12">Pool1</text>
                
                <rect x="380" y="110" width="90" height="50" fill="#f1c40f" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="425" y="135" text-anchor="middle" fill="white" font-size="12">Pool2</text>
                
                <!-- Fully Connected -->
                <rect x="540" y="70" width="100" height="80" fill="#9b59b6" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="590" y="115" text-anchor="middle" fill="white" font-size="12">Fully Connected</text>
                
                <!-- Output -->
                <rect x="700" y="70" width="50" height="80" fill="#2ecc71" stroke="#2c3e50" stroke-width="2" rx="5" ry="5" />
                <text x="725" y="115" text-anchor="middle" fill="white" font-weight="bold">Output</text>
                
                <!-- Connecting Arrows -->
                <line x1="150" y1="110" x2="220" y2="65" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="150" y1="110" x2="220" y2="135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="310" y1="65" x2="380" y2="65" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="310" y1="135" x2="380" y2="135" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="470" y1="65" x2="540" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="470" y1="135" x2="540" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="640" y1="110" x2="700" y2="110" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                
                <!-- Arrow Marker Definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <p><em>Figure: Basic structure of a convolutional neural network with convolution, pooling, and fully connected layers.</em></p>
        </div>
        
        <p>Before diving into the advanced topics, let's briefly review what makes convolutional networks special. Unlike fully connected networks, convolutional networks leverage three key ideas:</p>
        
        <ol>
            <li><strong>Local connectivity:</strong> Each neuron connects only to a small region of the input volume.</li>
            <li><strong>Parameter sharing:</strong> The same set of weights is used across different spatial locations.</li>
            <li><strong>Spatial invariance:</strong> Features can be detected regardless of their position in the input.</li>
        </ol>
        
        <p>These properties make CNNs particularly well-suited for processing data with grid-like topology, such as images (2D grids of pixels) or time series (1D grids of samples). Now, let's explore how these properties can be understood from a probabilistic perspective and how they can be implemented in various ways.</p>
    </div>
    
    <!-- Convolution and Pooling as an Infinitely Strong Prior Section -->
    <div class="section" id="infinitely-strong-prior">
        <h2>2. Convolution and Pooling as an Infinitely Strong Prior</h2>
        
        <h3>Understanding Priors in Deep Learning</h3>
        <p>In Bayesian statistics and machine learning, a prior probability distribution represents our beliefs about the parameters of a model before we observe any data. When we train a neural network, the learning process can be viewed as combining this prior with the evidence from the data to form a posterior distribution over the parameters.</p>
        
        <div class="formula-box">
            <p>Bayes' rule gives us:</p>
            $$P(\theta|D) = \frac{P(D|\theta)P(\theta)}{P(D)}$$
            <p>where:</p>
            <ul>
                <li>$P(\theta|D)$ is the posterior probability of the parameters given the data</li>
                <li>$P(D|\theta)$ is the likelihood of the data given the parameters</li>
                <li>$P(\theta)$ is the prior probability of the parameters</li>
                <li>$P(D)$ is the evidence (marginal likelihood of the data)</li>
            </ul>
        </div>
        
        <p>In deep learning, the prior is often implicit in the model architecture, regularization techniques, and parameter initialization strategies we choose. The architectural choices we make encode our beliefs about what types of functions are reasonable for solving our problem.</p>
        
        <h3>Weak vs. Strong Priors</h3>
        <p>Priors can be classified based on how concentrated the probability density is:</p>
        
        <ul>
            <li><strong>Weak Prior:</strong> A prior with high entropy (such as a Gaussian distribution with high variance). This type of prior is not very informative and allows the data to have a stronger influence on where the parameters end up.</li>
            <li><strong>Strong Prior:</strong> A prior with low entropy (such as a Gaussian distribution with low variance). This prior plays a more active role in determining the final parameter values, potentially even overriding weak signals from the data.</li>
            <li><strong>Infinitely Strong Prior:</strong> An extreme case where the prior places zero probability on certain parameter values, effectively forbidding them regardless of what the data suggests.</li>
        </ul>
        
        <div class="visualization">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="300" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Comparison of Prior Strengths</text>
                
                <!-- Coordinate System -->
                <line x1="100" y1="250" x2="700" y2="250" stroke="black" stroke-width="2" />
                <line x1="100" y1="250" x2="100" y2="50" stroke="black" stroke-width="2" />
                <text x="400" y="280" text-anchor="middle">Parameter Value</text>
                <text x="50" y="150" text-anchor="middle" transform="rotate(-90,50,150)">Probability Density</text>
                
                <!-- Weak Prior (Gaussian with high variance) -->
                <path d="M100,250 C100,250 150,240 200,200 C250,160 300,100 400,90 C500,100 550,160 600,200 C650,240 700,250 700,250" 
                      fill="none" stroke="#3498db" stroke-width="3" />
                <text x="180" y="190" fill="#3498db" font-weight="bold">Weak Prior</text>
                <text x="180" y="210" fill="#3498db" font-size="12">(High Variance)</text>
                
                <!-- Strong Prior (Gaussian with low variance) -->
                <path d="M100,250 C100,250 200,250 300,240 C350,230 370,150 400,140 C430,150 450,230 500,240 C600,250 700,250 700,250" 
                      fill="none" stroke="#e74c3c" stroke-width="3" />
                <text x="500" y="190" fill="#e74c3c" font-weight="bold">Strong Prior</text>
                <text x="500" y="210" fill="#e74c3c" font-size="12">(Low Variance)</text>
                
                <!-- Infinitely Strong Prior (Zero outside certain range) -->
                <path d="M100,250 L350,250 L350,100 L450,100 L450,250 L700,250" 
                      fill="none" stroke="#2ecc71" stroke-width="3" />
                <text x="400" y="80" fill="#2ecc71" font-weight="bold">Infinitely Strong Prior</text>
                <text x="400" y="100" fill="#2ecc71" font-size="12">(Zero probability outside range)</text>
            </svg>
            <p><em>Figure: Visual comparison of weak, strong, and infinitely strong priors. The infinitely strong prior places zero probability on certain parameter values, making them completely forbidden regardless of the data.</em></p>
        </div>
        
        <h3>Convolution as an Infinitely Strong Prior</h3>
        <p>We can view a convolutional neural network as a special case of a fully connected network with an infinitely strong prior on its weights. This prior enforces two key constraints:</p>
        
        <ol>
            <li><strong>Parameter sharing constraint:</strong> The weights for one hidden unit must be identical to the weights of its neighbor, but shifted in space. This encodes our belief that features should be equivariant to translation (i.e., if an input is translated, the output should be translated by the same amount).</li>
            <li><strong>Sparse connectivity constraint:</strong> The weights must be zero except for in the small, spatially contiguous receptive field assigned to each hidden unit. This encodes our belief that the function we're trying to learn involves only local interactions.</li>
        </ol>
        
        <p>These constraints are so strict that they reduce the number of learnable parameters from $O(n^2)$ in a fully connected layer to $O(k)$ in a convolutional layer, where $n$ is the number of neurons and $k$ is the kernel size. This is a dramatic reduction that makes training more efficient and reduces overfitting.</p>
        
        <div class="visualization">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="400" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Fully Connected vs. Convolutional Layer</text>
                
                <!-- Fully Connected Layer -->
                <text x="200" y="70" text-anchor="middle" font-weight="bold">Fully Connected Layer</text>
                
                <!-- Input Layer for FC -->
                <circle cx="100" cy="120" r="15" fill="#3498db" />
                <circle cx="100" cy="160" r="15" fill="#3498db" />
                <circle cx="100" cy="200" r="15" fill="#3498db" />
                <circle cx="100" cy="240" r="15" fill="#3498db" />
                <circle cx="100" cy="280" r="15" fill="#3498db" />
                <text x="70" y="200" text-anchor="middle">Input</text>
                
                <!-- Output Layer for FC -->
                <circle cx="300" cy="120" r="15" fill="#e74c3c" />
                <circle cx="300" cy="160" r="15" fill="#e74c3c" />
                <circle cx="300" cy="200" r="15" fill="#e74c3c" />
                <circle cx="300" cy="240" r="15" fill="#e74c3c" />
                <circle cx="300" cy="280" r="15" fill="#e74c3c" />
                <text x="330" y="200" text-anchor="middle">Output</text>
                
                <!-- Connections for FC (showing all connections with unique colors) -->
                <line x1="100" y1="120" x2="300" y2="120" stroke="#2c3e50" stroke-width="1" />
                <line x1="100" y1="120" x2="300" y2="160" stroke="#7f8c8d" stroke-width="1" />
                <line x1="100" y1="120" x2="300" y2="200" stroke="#95a5a6" stroke-width="1" />
                <line x1="100" y1="120" x2="300" y2="240" stroke="#bdc3c7" stroke-width="1" />
                <line x1="100" y1="120" x2="300" y2="280" stroke="#ecf0f1" stroke-width="1" />
                
                <line x1="100" y1="160" x2="300" y2="120" stroke="#1abc9c" stroke-width="1" />
                <line x1="100" y1="160" x2="300" y2="160" stroke="#16a085" stroke-width="1" />
                <line x1="100" y1="160" x2="300" y2="200" stroke="#2ecc71" stroke-width="1" />
                <line x1="100" y1="160" x2="300" y2="240" stroke="#27ae60" stroke-width="1" />
                <line x1="100" y1="160" x2="300" y2="280" stroke="#3498db" stroke-width="1" />
                
                <!-- More connections omitted for clarity -->
                <text x="200" y="320" text-anchor="middle" font-size="12">All connections have unique weights</text>
                <text x="200" y="340" text-anchor="middle" font-size="12">O(n²) parameters</text>
                
                <!-- Convolutional Layer -->
                <text x="600" y="70" text-anchor="middle" font-weight="bold">Convolutional Layer</text>
                
                <!-- Input Layer for Conv -->
                <circle cx="500" cy="120" r="15" fill="#3498db" />
                <circle cx="500" cy="160" r="15" fill="#3498db" />
                <circle cx="500" cy="200" r="15" fill="#3498db" />
                <circle cx="500" cy="240" r="15" fill="#3498db" />
                <circle cx="500" cy="280" r="15" fill="#3498db" />
                <text x="470" y="200" text-anchor="middle">Input</text>
                
                <!-- Output Layer for Conv -->
                <circle cx="700" cy="120" r="15" fill="#e74c3c" />
                <circle cx="700" cy="160" r="15" fill="#e74c3c" />
                <circle cx="700" cy="200" r="15" fill="#e74c3c" />
                <circle cx="700" cy="240" r="15" fill="#e74c3c" />
                <circle cx="700" cy="280" r="15" fill="#e74c3c" />
                <text x="730" y="200" text-anchor="middle">Output</text>
                
                <!-- Connections for Conv (showing limited connectivity with shared weights) -->
                <!-- Kernel 1 (red) -->
                <line x1="500" y1="120" x2="700" y2="120" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="160" x2="700" y2="120" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="200" x2="700" y2="120" stroke="#e74c3c" stroke-width="2" />
                
                <!-- Kernel 1 (red) reused -->
                <line x1="500" y1="160" x2="700" y2="160" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="200" x2="700" y2="160" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="240" x2="700" y2="160" stroke="#e74c3c" stroke-width="2" />
                
                <!-- Kernel 1 (red) reused again -->
                <line x1="500" y1="200" x2="700" y2="200" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="240" x2="700" y2="200" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="280" x2="700" y2="200" stroke="#e74c3c" stroke-width="2" />
                
                <!-- Kernel 1 (red) reused again -->
                <line x1="500" y1="240" x2="700" y2="240" stroke="#e74c3c" stroke-width="2" />
                <line x1="500" y1="280" x2="700" y2="240" stroke="#e74c3c" stroke-width="2" />
                
                <text x="600" y="320" text-anchor="middle" font-size="12">Same weights reused across spatial locations</text>
                <text x="600" y="340" text-anchor="middle" font-size="12">O(k) parameters where k is kernel size</text>
            </svg>
            <p><em>Figure: Comparison between fully connected layer (left) and convolutional layer (right). The convolutional layer has sparse connectivity and shared parameters across spatial locations.</em></p>
        </div>
        
        <h3>Pooling as an Infinitely Strong Prior</h3>
        <p>Similarly, pooling operations (like max-pooling or average pooling) can be viewed as imposing an infinitely strong prior that the function the layer should learn must be invariant to small translations. This prior effectively forces the model to discard precise spatial information in favor of detecting features regardless of their exact position.</p>
        
        <p>The pooling operation typically:</p>
        <ol>
            <li>Divides the input feature map into non-overlapping rectangles</li>
            <li>Outputs the maximum or average value for each such rectangle</li>
        </ol>
        
        <p>This operation introduces invariance to small spatial shifts and reduces the spatial dimensions of the representation, which in turn reduces the number of parameters in subsequent layers.</p>
        
        <h3>Implications and Insights</h3>
        <p>Understanding convolution and pooling as infinitely strong priors yields several important insights:</p>
        
        <div class="important">
            <h4>Potential for Underfitting</h4>
            <p>Like any prior, the constraints imposed by convolution and pooling are only useful when the assumptions they encode are accurate for the task at hand. If a task requires preserving precise spatial information, pooling can cause underfitting. Similarly, if a task involves long-range dependencies, the local connectivity constraint of convolution may be inappropriate.</p>
        </div>
        
        <p>For this reason, some CNN architectures are designed to be more flexible:</p>
        <ul>
            <li>Some networks apply pooling only to certain channels, preserving spatial information in others</li>
            <li>Attention mechanisms and dilated convolutions can help capture long-range dependencies</li>
            <li>Residual connections allow the network to bypass the convolutional constraints when needed</li>
        </ul>
        
        <div class="note">
            <h4>Fair Comparison in Benchmarks</h4>
            <p>Another important insight is that convolutional models should only be compared to other convolutional models in benchmarks. Models without convolution would need to learn the concept of spatial relationships from scratch, whereas convolutional models have this knowledge hard-coded into their architecture.</p>
        </div>
        
        <p>In summary, the convolutional and pooling operations provide a powerful inductive bias that makes CNNs efficient and effective for many tasks involving grid-structured data. However, these same biases can be limitations when the assumptions they encode don't match the task requirements.</p>
    </div>
    
    <!-- Variants of the Basic Convolution Function Section -->
    <div class="section" id="convolution-variants">
        <h2>3. Variants of the Basic Convolution Function</h2>
        
        <p>When discussing convolution in neural networks, we typically use a slightly different operation than the standard discrete convolution operation defined in mathematical literature. Let's explore these differences and understand the various implementations used in practice.</p>
        
        <h3>Multi-Channel Convolution</h3>
        <p>In neural networks, convolution is typically performed on multi-channel inputs (like the RGB channels of an image) and produces multi-channel outputs. This is more accurately described as many applications of convolution in parallel.</p>
        
        <div class="note">
            <p>A single-channel convolution can only extract one kind of feature at each spatial location. With multi-channel convolution, we can extract multiple types of features simultaneously.</p>
        </div>
        
        <p>In a convolutional neural network, the input to each layer is usually a 3D tensor with dimensions:</p>
        <ul>
            <li>Channel dimension (e.g., RGB channels in the input layer, or feature channels in deeper layers)</li>
            <li>Height dimension (rows)</li>
            <li>Width dimension (columns)</li>
        </ul>
        
        <p>When implementing CNNs, we often use a 4D tensor in batch mode, with the fourth dimension indexing different examples in the batch.</p>
        
        <div class="visualization">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="400" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Multi-Channel Convolution</text>
                
                <!-- Input Volume -->
                <rect x="100" y="100" width="150" height="150" fill="#3498db" opacity="0.3" />
                <rect x="110" y="110" width="150" height="150" fill="#3498db" opacity="0.5" />
                <rect x="120" y="120" width="150" height="150" fill="#3498db" opacity="0.7" />
                <text x="195" y="200" text-anchor="middle" fill="white" font-weight="bold" font-size="14">Input Volume</text>
                <text x="195" y="220" text-anchor="middle" fill="white" font-size="12">Multiple Channels</text>
                
                <!-- Kernels -->
                <rect x="350" y="150" width="40" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" />
                <rect x="355" y="155" width="40" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" opacity="0.8" />
                <rect x="360" y="160" width="40" height="40" fill="#e74c3c" stroke="#2c3e50" stroke-width="1" opacity="0.6" />
                <text x="380" y="185" text-anchor="middle" fill="white" font-weight="bold" font-size="10">Kernel 1</text>
                
                <rect x="350" y="220" width="40" height="40" fill="#2ecc71" stroke="#2c3e50" stroke-width="1" />
                <rect x="355" y="225" width="40" height="40" fill="#2ecc71" stroke="#2c3e50" stroke-width="1" opacity="0.8" />
                <rect x="360" y="230" width="40" height="40" fill="#2ecc71" stroke="#2c3e50" stroke-width="1" opacity="0.6" />
                <text x="380" y="255" text-anchor="middle" fill="white" font-weight="bold" font-size="10">Kernel 2</text>
                
                <!-- Output Feature Maps -->
                <rect x="550" y="150" width="100" height="100" fill="#e74c3c" opacity="0.7" />
                <text x="600" y="200" text-anchor="middle" fill="white" font-weight="bold" font-size="12">Feature Map 1</text>
                
                <rect x="550" y="270" width="100" height="100" fill="#2ecc71" opacity="0.7" />
                <text x="600" y="320" text-anchor="middle" fill="white" font-weight="bold" font-size="12">Feature Map 2</text>
                
                <!-- Arrows -->
                <line x1="270" y1="170" x2="350" y2="170" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="270" y1="240" x2="350" y2="240" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="400" y1="170" x2="550" y2="200" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <line x1="400" y1="240" x2="550" y2="320" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                
                <!-- Arrow Marker Definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
            </svg>
            <p><em>Figure: Multi-channel convolution. Each kernel is applied to the entire input volume (across all channels) to produce a single feature map in the output. Multiple kernels produce multiple output feature maps.</em></p>
        </div>
        
        <h4>Mathematical Formulation</h4>
        <p>Let's define a multi-channel convolution mathematically:</p>
        
        <div class="formula-box">
            <p>Assume we have a 4D kernel tensor $K$ where:</p>
            <ul>
                <li>$K_{i,j,k,l}$ gives the connection strength between a unit in channel $i$ of the output and a unit in channel $j$ of the input</li>
                <li>With an offset of $k$ rows and $l$ columns between the output unit and the input unit</li>
            </ul>
            
            <p>And our input is a 3D tensor $V$ where:</p>
            <ul>
                <li>$V_{i,j,k}$ is the value of the input unit within channel $i$ at row $j$ and column $k$</li>
            </ul>
            
            <p>If the output $Z$ has the same format as $V$, then without flipping the kernel, we have:</p>
            
            $$Z_{i,j,k} = \sum_{l,m,n} V_{l,j+m-1,k+n-1}K_{i,l,m,n}$$
            
            <p>Where the summation over $l$, $m$, and $n$ is over all values for which the tensor indexing operations inside the summation are valid.</p>
        </div>
        
        <p>Note that this operation is not generally commutative, even if kernel-flipping is used (unlike standard single-channel convolution). It's only commutative if each operation has the same number of output channels as input channels.</p>
        
        <h3>Strided Convolution</h3>
        <p>In standard convolution, the kernel slides one pixel at a time. However, for efficiency, we often want to skip over some positions of the kernel to reduce computational cost. This is known as strided convolution.</p>
        
        <p>We can think of strided convolution as downsampling the output of a full convolution. If we want to sample only every $s$ pixels in each direction, we can define:</p>
        
        <div class="formula-box">
            $$Z_{i,j,k} = c(K, V, s)_{i,j,k} = \sum_{l,m,n} V_{l,(j-1) \times s + m, (k-1) \times s + n}K_{i,l,m,n}$$
        </div>
        
        <p>Here, $s$ is the stride of the convolution. It's also possible to define different strides for different directions of motion.</p>
        
        <div class="visualization">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="300" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Strided Convolution (Stride = 2)</text>
                
                <!-- Input Grid (5x5) -->
                <g transform="translate(100, 80)">
                    <!-- Grid cells -->
                    <rect x="0" y="0" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="30" y="0" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="60" y="0" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="90" y="0" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="120" y="0" width="30" height="30" fill="white" stroke="#2c3e50" />
                    
                    <rect x="0" y="30" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="30" y="30" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="60" y="30" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="90" y="30" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="120" y="30" width="30" height="30" fill="white" stroke="#2c3e50" />
                    
                    <rect x="0" y="60" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="30" y="60" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="60" y="60" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="90" y="60" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="120" y="60" width="30" height="30" fill="white" stroke="#2c3e50" />
                    
                    <rect x="0" y="90" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="30" y="90" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="60" y="90" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="90" y="90" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="120" y="90" width="30" height="30" fill="white" stroke="#2c3e50" />
                    
                    <rect x="0" y="120" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="30" y="120" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="60" y="120" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="90" y="120" width="30" height="30" fill="white" stroke="#2c3e50" />
                    <rect x="120" y="120" width="30" height="30" fill="white" stroke="#2c3e50" />
                    
                    <!-- Kernel positions with stride=2 -->
                    <!-- Position 1 -->
                    <rect x="0" y="0" width="60" height="60" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" />
                    <text x="30" y="30" text-anchor="middle" fill="#e74c3c" font-weight="bold">1</text>
                    
                    <!-- Position 2 -->
                    <rect x="60" y="0" width="60" height="60" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" />
                    <text x="90" y="30" text-anchor="middle" fill="#e74c3c" font-weight="bold">2</text>
                    
                    <!-- Position 3 -->
                    <rect x="0" y="60" width="60" height="60" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" />
                    <text x="30" y="90" text-anchor="middle" fill="#e74c3c" font-weight="bold">3</text>
                    
                    <!-- Position 4 -->
                    <rect x="60" y="60" width="60" height="60" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" />
                    <text x="90" y="90" text-anchor="middle" fill="#e74c3c" font-weight="bold">4</text>
                    
                    <text x="75" y="170" text-anchor="middle" font-weight="bold">Input (5x5)</text>
                </g>
                
                <!-- Arrow -->
                <line x1="300" y1="150" x2="400" y2="150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <text x="350" y="130" text-anchor="middle">Stride = 2</text>
                
                <!-- Output Grid (2x2) -->
                <g transform="translate(450, 110)">
                    <!-- Grid cells -->
                    <rect x="0" y="0" width="40" height="40" fill="white" stroke="#2c3e50" />
                    <rect x="40" y="0" width="40" height="40" fill="white" stroke="#2c3e50" />
                    <rect x="0" y="40" width="40" height="40" fill="white" stroke="#2c3e50" />
                    <rect x="40" y="40" width="40" height="40" fill="white" stroke="#2c3e50" />
                    
                    <!-- Output values -->
                    <text x="20" y="25" text-anchor="middle" font-weight="bold">1</text>
                    <text x="60" y="25" text-anchor="middle" font-weight="bold">2</text>
                    <text x="20" y="65" text-anchor="middle" font-weight="bold">3</text>
                    <text x="60" y="65" text-anchor="middle" font-weight="bold">4</text>
                    
                    <text x="40" y="100" text-anchor="middle" font-weight="bold">Output (2x2)</text>
                </g>
                
                <!-- Explanation -->
                <text x="600" y="110" font-size="12">Instead of sliding the kernel one</text>
                <text x="600" y="130" font-size="12">pixel at a time, we move it</text>
                <text x="600" y="150" font-size="12">by 2 pixels (stride=2),</text>
                <text x="600" y="170" font-size="12">reducing the output size and</text>
                <text x="600" y="190" font-size="12">computational cost.</text>
            </svg>
            <p><em>Figure: Strided convolution with a stride of 2. The kernel (represented by colored squares) jumps 2 positions at a time instead of sliding pixel by pixel, resulting in a smaller output feature map.</em></p>
        </div>
        
        <p>Strided convolution is mathematically equivalent to performing a regular convolution and then downsampling the result, but it's more computationally efficient since it avoids computing values that would later be discarded.</p>
        
        <div class="important">
            <p>One important consideration with strided convolution: if the stride is greater than 1, we typically can't perform the gradient computation using the convolution operation itself. Special gradient computation operations are needed.</p>
        </div>
        
        <h3>Zero Padding Strategies</h3>
        <p>An essential feature of convolutional networks is the ability to implicitly zero-pad the input to make it wider. Without zero padding, the width of the representation would shrink by one pixel less than the kernel width at each layer, limiting the network's depth and expressiveness.</p>
        
        <p>Zero padding allows us to control the kernel width and the output size independently. Without it, we face a difficult choice between:</p>
        <ol>
            <li>Shrinking the spatial dimensions of the network rapidly, or</li>
            <li>Using small kernels with limited receptive fields</li>
        </ol>
        <p>Both options significantly limit the expressive power of the network.</p>
        
        <div class="visualization">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="400" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Zero Padding Strategies</text>
                
                <!-- No Zero Padding (Valid Convolution) -->
                <text x="200" y="60" text-anchor="middle" font-weight="bold">No Zero Padding ("Valid")</text>
                
                <!-- Layer 1 -->
                <rect x="100" y="80" width="200" height="30" fill="#3498db" />
                <text x="200" y="100" text-anchor="middle" fill="white" font-weight="bold">Input (16 pixels)</text>
                
                <!-- Layer 2 -->
                <rect x="110" y="130" width="180" height="30" fill="#e74c3c" />
                <text x="200" y="150" text-anchor="middle" fill="white" font-weight="bold">Layer 1 (11 pixels)</text>
                
                <!-- Layer 3 -->
                <rect x="120" y="180" width="160" height="30" fill="#e74c3c" />
                <text x="200" y="200" text-anchor="middle" fill="white" font-weight="bold">Layer 2 (6 pixels)</text>
                
                <!-- Layer 4 -->
                <rect x="130" y="230" width="140" height="30" fill="#e74c3c" />
                <text x="200" y="250" text-anchor="middle" fill="white" font-weight="bold">Layer 3 (1 pixel)</text>
                
                <!-- Arrow -->
                <line x1="50" y1="95" x2="50" y2="245" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <text x="35" y="170" text-anchor="middle" transform="rotate(-90,35,170)">Shrinking</text>
                
                <!-- Same Zero Padding -->
                <text x="600" y="60" text-anchor="middle" font-weight="bold">With Zero Padding ("Same")</text>
                
                <!-- Layer 1 -->
                <rect x="500" y="80" width="200" height="30" fill="#3498db" />
                <text x="600" y="100" text-anchor="middle" fill="white" font-weight="bold">Input (16 pixels)</text>
                
                <!-- Layer 2 -->
                <rect x="500" y="130" width="200" height="30" fill="#2ecc71" />
                <text x="600" y="150" text-anchor="middle" fill="white" font-weight="bold">Layer 1 (16 pixels)</text>
                
                <!-- Layer 3 -->
                <rect x="500" y="180" width="200" height="30" fill="#2ecc71" />
                <text x="600" y="200" text-anchor="middle" fill="white" font-weight="bold">Layer 2 (16 pixels)</text>
                
                <!-- Layer 4 -->
                <rect x="500" y="230" width="200" height="30" fill="#2ecc71" />
                <text x="600" y="250" text-anchor="middle" fill="white" font-weight="bold">Layer 3 (16 pixels)</text>
                
                <!-- Arrow -->
                <line x1="450" y1="95" x2="450" y2="245" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" />
                <text x="435" y="170" text-anchor="middle" transform="rotate(-90,435,170)">Maintaining Size</text>
                
                <!-- Kernel Size Indication -->
                <rect x="150" y="300" width="120" height="20" fill="#f39c12" />
                <text x="210" y="315" text-anchor="middle" font-weight="bold">Kernel Width = 6</text>
                
                <!-- Formula -->
                <text x="400" y="340" text-anchor="middle" font-weight="bold">Output width = Input width - Kernel width + 1</text>
                <text x="400" y="370" text-anchor="middle" font-weight="bold" fill="#e74c3c">(without padding)</text>
            </svg>
            <p><em>Figure: The effect of zero padding on network size. Without zero padding (left), the representation shrinks by kernel_width-1 pixels at each layer, limiting network depth. With zero padding (right), the spatial dimensions can be preserved, allowing for arbitrarily deep networks.</em></p>
        </div>
        
        <h4>Types of Zero Padding</h4>
        <p>There are three common zero-padding strategies:</p>
        
        <div class="formula-box">
            <ol>
                <li><strong>Valid Convolution (No Padding):</strong> The kernel can only visit positions where it's completely contained within the image. If the input has width <em>m</em> and the kernel has width <em>k</em>, the output width will be <em>m-k+1</em>.</li>
                <li><strong>Same Convolution:</strong> Just enough zero-padding is added to keep the output size equal to the input size. This allows the network to have as many convolutional layers as needed without shrinking.</li>
                <li><strong>Full Convolution:</strong> Enough zeros are added for every pixel to be visited <em>k</em> times in each direction, resulting in an output width of <em>m+k-1</em>.</li>
            </ol>
        </div>
        
        <h4>Considerations for Different Padding Strategies</h4>
        <p>Each padding strategy has its own advantages and disadvantages:</p>
        
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: var(--secondary-color); color: white;">
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Padding Strategy</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Advantages</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Disadvantages</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Valid</strong> (No Padding)</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <ul>
                            <li>All output pixels are a function of the same number of input pixels</li>
                            <li>Behavior of output pixels is more regular</li>
                        </ul>
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <ul>
                            <li>Output size shrinks at each layer</li>
                            <li>Limits the depth of the network</li>
                            <li>Can be dramatic shrinkage if kernels are large</li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Same</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <ul>
                            <li>Maintains spatial dimensions</li>
                            <li>Allows for arbitrarily deep networks</li>
                            <li>Preserves more information from the input</li>
                        </ul>
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <ul>
                            <li>Border pixels are somewhat underrepresented</li>
                            <li>Pixels near the center influence more output pixels than those near the border</li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #ddd;"><strong>Full</strong></td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <ul>
                            <li>Ensures every input pixel is fully utilized</li>
                            <li>Preserves information at the borders</li>
                            <li>Output is larger than input</li>
                        </ul>
                    </td>
                    <td style="padding: 10px; border: 1px solid #ddd;">
                        <ul>
                            <li>Output pixels near the border are a function of fewer input pixels</li>
                            <li>Can make it difficult to learn a single kernel that performs well at all positions</li>
                            <li>Increases computational cost due to larger outputs</li>
                        </ul>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="note">
            <p>In practice, the optimal amount of zero padding (in terms of test set classification accuracy) usually lies somewhere between "valid" and "same" convolution, depending on the specific task and architecture.</p>
        </div>
        
        <h3>Locally Connected Layers</h3>
        <p>In some applications, we may want to preserve the local connectivity pattern of convolutional networks but remove the parameter sharing constraint. These are called locally connected layers (or unshared convolution).</p>
        
        <p>In a locally connected layer, each neuron still connects only to a small region of the input volume, but the weights are not shared across different spatial locations. This means that each spatial location in the output feature map has its own set of weights.</p>
        
        <div class="formula-box">
            <p>Mathematically, a locally connected layer can be described using a 6D tensor $W$ with indices:</p>
            <ul>
                <li>$i$: output channel</li>
                <li>$j$: output row</li>
                <li>$k$: output column</li>
                <li>$l$: input channel</li>
                <li>$m$: row offset within the input</li>
                <li>$n$: column offset within the input</li>
            </ul>
            
            <p>The linear part of a locally connected layer is then given by:</p>
            
            $$Z_{i,j,k} = \sum_{l,m,n} [V_{l,j+m-1,k+n-1}w_{i,j,k,l,m,n}]$$
        </div>
        
        <div class="visualization">
            <svg width="800" height="320" viewBox="0 0 800 320">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="320" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Locally Connected vs. Convolutional Layer</text>
                
                <!-- Local Connections Visualization -->
                <text x="200" y="60" text-anchor="middle" font-weight="bold">Locally Connected Layer</text>
                
                <!-- Input units -->
                <circle cx="100" cy="100" r="15" fill="#3498db" />
                <circle cx="100" cy="140" r="15" fill="#3498db" />
                <circle cx="100" cy="180" r="15" fill="#3498db" />
                <circle cx="100" cy="220" r="15" fill="#3498db" />
                <circle cx="100" cy="260" r="15" fill="#3498db" />
                <text x="70" y="180" text-anchor="middle">Input</text>
                
                <!-- Output units -->
                <circle cx="300" cy="100" r="15" fill="#e74c3c" />
                <circle cx="300" cy="140" r="15" fill="#e74c3c" />
                <circle cx="300" cy="180" r="15" fill="#e74c3c" />
                <circle cx="300" cy="220" r="15" fill="#e74c3c" />
                <circle cx="300" cy="260" r="15" fill="#e74c3c" />
                <text x="330" y="180" text-anchor="middle">Output</text>
                
                <!-- Local connections with different colors for different weights -->
                <line x1="100" y1="100" x2="300" y2="100" stroke="#1abc9c" stroke-width="2" />
                <line x1="100" y1="140" x2="300" y2="100" stroke="#2ecc71" stroke-width="2" />
                
                <line x1="100" y1="140" x2="300" y2="140" stroke="#3498db" stroke-width="2" />
                <line x1="100" y1="180" x2="300" y2="140" stroke="#9b59b6" stroke-width="2" />
                
                <line x1="100" y1="180" x2="300" y2="180" stroke="#f1c40f" stroke-width="2" />
                <line x1="100" y1="220" x2="300" y2="180" stroke="#e67e22" stroke-width="2" />
                
                <line x1="100" y1="220" x2="300" y2="220" stroke="#e74c3c" stroke-width="2" />
                <line x1="100" y1="260" x2="300" y2="220" stroke="#c0392b" stroke-width="2" />
                
                <line x1="100" y1="260" x2="300" y2="260" stroke="#7f8c8d" stroke-width="2" />
                
                <text x="200" y="290" text-anchor="middle" font-size="12">Each connection has its own weight</text>
                
                <!-- Convolutional Layer Visualization -->
                <text x="600" y="60" text-anchor="middle" font-weight="bold">Convolutional Layer</text>
                
                <!-- Input units -->
                <circle cx="500" cy="100" r="15" fill="#3498db" />
                <circle cx="500" cy="140" r="15" fill="#3498db" />
                <circle cx="500" cy="180" r="15" fill="#3498db" />
                <circle cx="500" cy="220" r="15" fill="#3498db" />
                <circle cx="500" cy="260" r="15" fill="#3498db" />
                <text x="470" y="180" text-anchor="middle">Input</text>
                
                <!-- Output units -->
                <circle cx="700" cy="100" r="15" fill="#e74c3c" />
                <circle cx="700" cy="140" r="15" fill="#e74c3c" />
                <circle cx="700" cy="180" r="15" fill="#e74c3c" />
                <circle cx="700" cy="220" r="15" fill="#e74c3c" />
                <circle cx="700" cy="260" r="15" fill="#e74c3c" />
                <text x="730" y="180" text-anchor="middle">Output</text>
                
                <!-- Convolutional connections with same colors for shared weights -->
                <line x1="500" y1="100" x2="700" y2="100" stroke="#1abc9c" stroke-width="2" />
                <line x1="500" y1="140" x2="700" y2="100" stroke="#2ecc71" stroke-width="2" />
                
                <line x1="500" y1="140" x2="700" y2="140" stroke="#1abc9c" stroke-width="2" />
                <line x1="500" y1="180" x2="700" y2="140" stroke="#2ecc71" stroke-width="2" />
                
                <line x1="500" y1="180" x2="700" y2="180" stroke="#1abc9c" stroke-width="2" />
                <line x1="500" y1="220" x2="700" y2="180" stroke="#2ecc71" stroke-width="2" />
                
                <line x1="500" y1="220" x2="700" y2="220" stroke="#1abc9c" stroke-width="2" />
                <line x1="500" y1="260" x2="700" y2="220" stroke="#2ecc71" stroke-width="2" />
                
                <line x1="500" y1="260" x2="700" y2="260" stroke="#1abc9c" stroke-width="2" />
                
                <text x="600" y="290" text-anchor="middle" font-size="12">Weights are shared across spatial positions</text>
            </svg>
            <p><em>Figure: Comparison between locally connected layers (left) and convolutional layers (right). In the locally connected layer, each connection has its own weight parameter (represented by different colors). In the convolutional layer, the same weights are reused across spatial locations (same colors repeated).</em></p>
        </div>
        
        <h4>When to Use Locally Connected Layers</h4>
        <p>Locally connected layers can be useful when:</p>
        <ul>
            <li>We know that each feature should be a function of a small part of space (preserving local connectivity)</li>
            <li>There's no reason to think that the same feature should occur across all of space (dropping parameter sharing)</li>
        </ul>
        
        <p>For example, if we're building a face detection system, we might only need to look for the mouth in the bottom half of the image, and for eyes in the upper half. Different features are relevant at different spatial locations.</p>
        
        <div class="note">
            <p>The trade-off is that locally connected layers have many more parameters than convolutional layers, making them more prone to overfitting and requiring more memory and computation.</p>
        </div>
        
        <h3>Tiled Convolution</h3>
        <p>Tiled convolution offers a compromise between standard convolution and locally connected layers. Instead of learning a separate set of weights at every spatial location (as in locally connected layers) or sharing the same weights everywhere (as in standard convolution), tiled convolution learns a set of kernels that it rotates through as it moves through space.</p>
        
        <div class="formula-box">
            <p>Mathematically, tiled convolution can be defined as:</p>
            
            $$Z_{i,j,k} = \sum_{l,m,n} V_{l,j+m-1,k+n-1}K_{i,l,m,n,j \% t + 1, k \% t + 1}$$
            
            <p>where % is the modulo operation, and $t$ is the number of different kernels to cycle through in each direction. If $t$ is equal to the output width, this becomes equivalent to a locally connected layer.</p>
        </div>
        
        <div class="visualization">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="350" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">Tiled Convolution (t=2)</text>
                
                <!-- Input Grid (1D for simplicity) -->
                <line x1="100" y1="100" x2="700" y2="100" stroke="#2c3e50" stroke-width="2" />
                
                <!-- Input Nodes -->
                <circle cx="150" cy="100" r="10" fill="#3498db" />
                <text x="150" cy="85" text-anchor="middle" font-size="12">x₁</text>
                
                <circle cx="230" cy="100" r="10" fill="#3498db" />
                <text x="230" cy="85" text-anchor="middle" font-size="12">x₂</text>
                
                <circle cx="310" cy="100" r="10" fill="#3498db" />
                <text x="310" cy="85" text-anchor="middle" font-size="12">x₃</text>
                
                <circle cx="390" cy="100" r="10" fill="#3498db" />
                <text x="390" cy="85" text-anchor="middle" font-size="12">x₄</text>
                
                <circle cx="470" cy="100" r="10" fill="#3498db" />
                <text x="470" cy="85" text-anchor="middle" font-size="12">x₅</text>
                
                <circle cx="550" cy="100" r="10" fill="#3498db" />
                <text x="550" cy="85" text-anchor="middle" font-size="12">x₆</text>
                
                <circle cx="630" cy="100" r="10" fill="#3498db" />
                <text x="630" cy="85" text-anchor="middle" font-size="12">x₇</text>
                
                <!-- Output Line -->
                <line x1="100" y1="200" x2="700" y2="200" stroke="#2c3e50" stroke-width="2" />
                
                <!-- Output Nodes -->
                <circle cx="150" cy="200" r="10" fill="#e74c3c" />
                <text x="150" cy="215" text-anchor="middle" font-size="12">s₁</text>
                
                <circle cx="230" cy="200" r="10" fill="#e74c3c" />
                <text x="230" cy="215" text-anchor="middle" font-size="12">s₂</text>
                
                <circle cx="310" cy="200" r="10" fill="#e74c3c" />
                <text x="310" cy="215" text-anchor="middle" font-size="12">s₃</text>
                
                <circle cx="390" cy="200" r="10" fill="#e74c3c" />
                <text x="390" cy="215" text-anchor="middle" font-size="12">s₄</text>
                
                <circle cx="470" cy="200" r="10" fill="#e74c3c" />
                <text x="470" cy="215" text-anchor="middle" font-size="12">s₅</text>
                
                <circle cx="550" cy="200" r="10" fill="#e74c3c" />
                <text x="550" cy="215" text-anchor="middle" font-size="12">s₆</text>
                
                <!-- Connections using 2 different kernels -->
                <!-- Kernel 1 (a-b) connections -->
                <line x1="150" y1="100" x2="150" y2="200" stroke="#1abc9c" stroke-width="2" />
                <text x="140" cy="150" text-anchor="middle" font-size="10" fill="#1abc9c">a</text>
                
                <line x1="230" y1="100" x2="150" y2="200" stroke="#1abc9c" stroke-width="2" />
                <text x="180" cy="150" text-anchor="middle" font-size="10" fill="#1abc9c">b</text>
                
                <line x1="310" y1="100" x2="310" y2="200" stroke="#1abc9c" stroke-width="2" />
                <text x="300" cy="150" text-anchor="middle" font-size="10" fill="#1abc9c">a</text>
                
                <line x1="390" y1="100" x2="310" y2="200" stroke="#1abc9c" stroke-width="2" />
                <text x="340" cy="150" text-anchor="middle" font-size="10" fill="#1abc9c">b</text>
                
                <line x1="470" y1="100" x2="470" y2="200" stroke="#1abc9c" stroke-width="2" />
                <text x="460" cy="150" text-anchor="middle" font-size="10" fill="#1abc9c">a</text>
                
                <line x1="550" y1="100" x2="470" y2="200" stroke="#1abc9c" stroke-width="2" />
                <text x="500" cy="150" text-anchor="middle" font-size="10" fill="#1abc9c">b</text>
                
                <!-- Kernel 2 (c-d) connections -->
                <line x1="230" y1="100" x2="230" y2="200" stroke="#e74c3c" stroke-width="2" />
                <text x="220" cy="150" text-anchor="middle" font-size="10" fill="#e74c3c">c</text>
                
                <line x1="310" y1="100" x2="230" y2="200" stroke="#e74c3c" stroke-width="2" />
                <text x="260" cy="150" text-anchor="middle" font-size="10" fill="#e74c3c">d</text>
                
                <line x1="390" y1="100" x2="390" y2="200" stroke="#e74c3c" stroke-width="2" />
                <text x="380" cy="150" text-anchor="middle" font-size="10" fill="#e74c3c">c</text>
                
                <line x1="470" y1="100" x2="390" y2="200" stroke="#e74c3c" stroke-width="2" />
                <text x="420" cy="150" text-anchor="middle" font-size="10" fill="#e74c3c">d</text>
                
                <line x1="550" y1="100" x2="550" y2="200" stroke="#e74c3c" stroke-width="2" />
                <text x="540" cy="150" text-anchor="middle" font-size="10" fill="#e74c3c">c</text>
                
                <line x1="630" y1="100" x2="550" y2="200" stroke="#e74c3c" stroke-width="2" />
                <text x="580" cy="150" text-anchor="middle" font-size="10" fill="#e74c3c">d</text>
                
                <!-- Legend -->
                <rect x="150" y="250" width="120" height="60" fill="white" stroke="#2c3e50" />
                <text x="210" y="270" text-anchor="middle" font-weight="bold">Kernel 1</text>
                <line x1="170" y1="290" x2="190" y2="290" stroke="#1abc9c" stroke-width="2" />
                <text x="200" y="290" fill="#1abc9c">Weight a</text>
                <line x1="170" y1="310" x2="190" y2="310" stroke="#1abc9c" stroke-width="2" />
                <text x="200" y="310" fill="#1abc9c">Weight b</text>
                
                <rect x="350" y="250" width="120" height="60" fill="white" stroke="#2c3e50" />
                <text x="410" y="270" text-anchor="middle" font-weight="bold">Kernel 2</text>
                <line x1="370" y1="290" x2="390" y2="290" stroke="#e74c3c" stroke-width="2" />
                <text x="400" y="290" fill="#e74c3c">Weight c</text>
                <line x1="370" y1="310" x2="390" y2="310" stroke="#e74c3c" stroke-width="2" />
                <text x="400" y="310" fill="#e74c3c">Weight d</text>
                
                <text x="550" y="290" text-anchor="middle" font-size="12">Kernels repeat every t=2 positions</text>
            </svg>
            <p><em>Figure: Tiled convolution with t=2. Two different kernels (a-b and c-d) are used in alternating positions. The pattern repeats every t positions, creating a compromise between standard convolution and locally connected layers.</em></p>
        </div>
        
        <h4>Advantages of Tiled Convolution</h4>
        <p>Tiled convolution offers several benefits:</p>
        <ul>
            <li>Like locally connected layers, it allows neighboring units in the output to have different filters</li>
            <li>Unlike locally connected layers, the memory requirements increase only by a factor of $t$ (the number of kernels), rather than by the size of the entire output feature map</li>
            <li>Provides more flexibility than standard convolution while maintaining parameter efficiency</li>
        </ul>
        
        <div class="important">
            <h4>Interaction with Pooling</h4>
            <p>Both locally connected layers and tiled convolutional layers have an interesting interaction with max-pooling: their detector units are driven by different filters. If these filters learn to detect different transformed versions of the same underlying features, then the max-pooled units can become invariant to the learned transformations.</p>
            <p>This is in contrast to standard convolutional layers, which are hard-coded to be invariant specifically to translation.</p>
        </div>
        
        <h3>Backpropagation in Convolutional Networks</h3>
        <p>Implementing a convolutional network requires not only the forward pass (computing outputs from inputs) but also the backward pass for learning through gradient descent. Let's look at the operations needed for backpropagation in convolutional networks.</p>
        
        <h4>The Matrix View of Convolution</h4>
        <p>It's helpful to think of convolution as a linear operation that can be described as a matrix multiplication (after reshaping the input tensor into a flat vector). The matrix involved is a function of the convolution kernel, and it's sparse with each element of the kernel copied to several elements of the matrix.</p>
        
        <p>This view helps us derive the operations needed for backpropagation:</p>
        
        <ol>
            <li><strong>Forward propagation:</strong> Applying the convolution operation (already discussed)</li>
            <li><strong>Backprop from output to weights:</strong> Computing the gradient with respect to the kernel, given the gradient with respect to the outputs</li>
            <li><strong>Backprop from output to inputs:</strong> Computing the gradient with respect to the inputs, needed to propagate errors to earlier layers</li>
        </ol>
        
        <div class="formula-box">
            <p>Let's consider a convolutional network with:</p>
            <ul>
                <li>Kernel stack $K$</li>
                <li>Input $V$</li>
                <li>Stride $s$</li>
                <li>Output $Z = c(K, V, s)$ as defined earlier</li>
                <li>Some loss function $J(V, K)$</li>
            </ul>
            
            <p>During backpropagation, we receive a tensor $G$ where:</p>
            $$G_{i,j,k} = \frac{\partial J}{\partial Z_{i,j,k}}(V, K)$$
            
            <p>To train the network, we need to compute the derivatives with respect to the weights in the kernel:</p>
            $$g(G, V, s)_{i,j,k,l} = \frac{\partial J}{\partial K_{i,j,k,l}}(V, K) = \sum_{m,n} G_{i,m,n}V_{j,(m-1) \times s + k, (n-1) \times s + l}$$
            
            <p>If this is not the bottom layer, we also need to compute the gradient with respect to $V$ to backpropagate the error:</p>
            $$h(K, G, s)_{i,j,k} = \frac{\partial J}{\partial V_{i,j,k}}(V, K) = \sum_{l,m \text{ s.t. } (l-1) \times s + m = j} \sum_{n,p \text{ s.t. } (n-1) \times s + p = k} \sum_{q} K_{q,i,m,p}G_{q,l,n}$$
        </div>
        
        <p>These three operations—convolution (forward), backprop to weights, and backprop to inputs—are sufficient to compute all the gradients needed to train any depth of feedforward convolutional network.</p>
        
        <div class="note">
            <h4>Transpose Convolution</h4>
            <p>The operation for backpropagating from output to inputs is sometimes called "transpose convolution" because it corresponds to multiplication by the transpose of the matrix defined by convolution.</p>
            <p>This operation is also useful for reconstructing visible units from hidden units in models like autoencoders, RBMs, and sparse coding.</p>
        </div>
        
        <div class="important">
            <h4>Strided Convolution Consideration</h4>
            <p>For strided convolution (s > 1), neither the gradient with respect to the weights nor the gradient with respect to the inputs can be computed using the standard convolution operation. Special implementations are required.</p>
        </div>
        
        <h4>Parameter Sharing in Biases</h4>
        <p>So far, we've focused on the convolutional weights, but we also need to consider how to handle biases in convolutional networks.</p>
        
        <p>There are several approaches to handling biases:</p>
        <ul>
            <li><strong>For convolutional layers:</strong> Typically, one bias per channel of the output is used and shared across all spatial locations within each feature map.</li>
            <li><strong>For locally connected layers:</strong> It's natural to give each unit its own bias.</li>
            <li><strong>For tiled convolution:</strong> Biases are typically shared with the same tiling pattern as the kernels.</li>
        </ul>
        
        <p>For inputs of known, fixed size, it's also possible to learn a separate bias at each location of the output map. This may slightly reduce the statistical efficiency of the model but allows it to correct for differences in image statistics at different locations.</p>
        
        <div class="note">
            <p>For example, when using implicit zero padding, detector units at the edge of the image receive less total input and may need larger biases to compensate.</p>
        </div>
    </div>
    
    <!-- Summary Section -->
    <div class="section" id="summary">
        <h2>4. Summary and Key Takeaways</h2>
        
        <p>In this tutorial, we've explored the theoretical foundations and practical variations of convolutional neural networks:</p>
        
        <h3>Convolution and Pooling as Prior Knowledge</h3>
        <ul>
            <li>Convolutional networks can be viewed as fully connected networks with an infinitely strong prior over their weights</li>
            <li>This prior enforces parameter sharing and local connectivity</li>
            <li>Pooling operations encode the prior that features should be invariant to small translations</li>
            <li>These priors are only beneficial when they align with the task requirements</li>
        </ul>
        
        <h3>Variants of Convolution</h3>
        <ul>
            <li><strong>Multi-Channel Convolution:</strong> Extracts multiple feature types in parallel</li>
            <li><strong>Strided Convolution:</strong> Reduces computational cost by skipping positions</li>
            <li><strong>Zero Padding:</strong> Controls the output size and enables deeper networks</li>
            <li><strong>Locally Connected Layers:</strong> Preserve local connectivity but drop parameter sharing</li>
            <li><strong>Tiled Convolution:</strong> Offers a compromise with a repeating set of kernels</li>
        </ul>
        
        <h3>Implementation Considerations</h3>
        <ul>
            <li>Proper backpropagation requires specialized operations, especially for strided convolution</li>
            <li>Bias parameters can be shared in different ways depending on the layer type</li>
            <li>The choice of convolution variant should be guided by the specific requirements of the task</li>
        </ul>
        
        <div class="visualization">
            <svg width="800" height="250" viewBox="0 0 800 250">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="250" fill="#f8f9fa" rx="10" ry="10" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-weight="bold" font-size="16">CNN Design Space</text>
                
                <!-- Axes -->
                <line x1="100" y1="200" x2="700" y2="200" stroke="#2c3e50" stroke-width="2" />
                <text x="400" y="230" text-anchor="middle" font-weight="bold">Parameter Sharing</text>
                <text x="100" y="220" text-anchor="middle" font-size="12">None</text>
                <text x="700" y="220" text-anchor="middle" font-size="12">Complete</text>
                
                <line x1="100" y1="200" x2="100" y2="50" stroke="#2c3e50" stroke-width="2" />
                <text x="50" y="125" text-anchor="middle" font-weight="bold" transform="rotate(-90,50,125)">Connectivity</text>
                <text x="80" y="200" text-anchor="middle" font-size="12" transform="rotate(-90,80,200)">Local</text>
                <text x="80" y="50" text-anchor="middle" font-size="12" transform="rotate(-90,80,50)">Full</text>
                
                <!-- Points -->
                <!-- Fully Connected -->
                <circle cx="100" cy="50" r="10" fill="#9b59b6" />
                <text x="100" y="40" text-anchor="middle" font-size="12">Fully Connected</text>
                
                <!-- Locally Connected -->
                <circle cx="100" cy="150" r="10" fill="#e74c3c" />
                <text x="100" y="170" text-anchor="middle" font-size="12">Locally Connected</text>
                
                <!-- Tiled Convolution -->
                <circle cx="400" cy="150" r="10" fill="#f1c40f" />
                <text x="400" y="170" text-anchor="middle" font-size="12">Tiled Convolution</text>
                
                <!-- Standard Convolution -->
                <circle cx="700" cy="150" r="10" fill="#2ecc71" />
                <text x="700" y="170" text-anchor="middle" font-size="12">Standard Convolution</text>
                
                <!-- Arrows indicating relationship -->
                <line x1="130" y1="150" x2="370" y2="150" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)" />
                <line x1="430" y1="150" x2="670" y2="150" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)" />
                <text x="250" y="140" text-anchor="middle" font-size="10">Increasing t</text>
                <text x="550" y="140" text-anchor="middle" font-size="10">t = 1</text>
            </svg>
            <p><em>Figure: The design space of convolutional network variants, showing the trade-offs between parameter sharing and connectivity patterns.</em></p>
        </div>
        
        <p>By understanding these variants and their properties, you can make informed decisions when designing convolutional architectures for specific applications. The choice between standard convolution, locally connected layers, tiled convolution, or other variants should be guided by the nature of the problem, the available data, and computational constraints.</p>
    </div>
    
</body>
</html> 