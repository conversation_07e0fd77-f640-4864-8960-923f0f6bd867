5.2 First Order Differential Equations ¶
🔗In many fields such as physics, biology or business, a relationship is often known or assumed between some unknown quantity and its rate of change, which does not involve any higher derivatives. It is therefore of interest to study first order differential equations in particular.

🔗Definition 5.7. First Order DE.  A first order differential equation is an equation of the form 
F
(
t
,
y
,
y
′
)
=
0
.
 A solution of a first order differential equation is a function 
f
(
t
)
 that makes 
F
(
t
,
f
(
t
)
,
f
′
(
t
)
)
=
0
 for every value of 
t
.
🔗Here, 
F
 is a function of three variables which we label 
t
,
 
y
,
 and 
y
′
.
 It is understood that 
y
′
 will explicitly appear in the equation although 
t
 and 
y
 need not. The variable 
y
 itself is dependent on 
t
,
 hence it is understood that 
y
′
 must be the derivative of 
y
 with respect to 
t
.
 Since only the first derivative of 
y
 appears, but no higher order derivative, this is a first order differential equation.

🔗Throughout the notes, we use the independent variable 
t
 as many applications are based on the independent variable representing time. If no meaning is attributed to the independent variable, we may want to write a first order differential equation in the usual manner as

F
(
x
,
y
,
y
′
)
=
0
.
fit width

🔗
Example5.8. Simple First Order Differential Equation.
y
′
=
t
2
+
1
 is a first order linear differential equation; 
F
(
t
,
y
,
y
′
)
=
y
′
−
t
2
−
1
.
 Show that all solutions to this equation are of the form 
y
=
t
3
/
3
+
t
+
C
.
🔗
Example5.9. Graphical Solution to First Order Differential Equation.
Sketch various solutions to the differential equation 
d
y
d
x
=
2
x
.
🔗5.2.1 Initial Value Problems
🔗Definition 5.10. Initial Conditions.  Initial condition(s) are a set of points that the solution (or its derivatives) must satisfy.
🔗
Example5.11. Initial Conditions.
For a differential equation involving a function 
f
(
t
)
,
 initial conditions are of the form:
f
(
t
0
)
=
f
0
,
f
′
(
t
0
)
=
f
1
,
f
′′
(
t
0
)
=
f
2
,
…
e
t
c
.
🔗Definition 5.12. Initial Value Problem.  An initial value problem (IVP) is a differential equation along with a set of initial conditions.
🔗
Example5.13. First Order Initial Value Problem.
Solve the initial value problem:
d
y
d
x
=
2
x
,
y
(
0
)
=
2
.
🔗
Example5.14. Simple Initial Value Problem.
Verify that the initial value problem 
y
′
=
t
2
+
1
,
 
y
(
1
)
=
4
 has solution 
f
(
t
)
=
t
3
/
3
+
t
+
8
/
3
.
🔗The general first order equation is too general, so we can't describe methods that will work on them all, or even a large portion of them. We can make progress with specific kinds of first order differential equations. For example, much can be said about equations of the form 
y
′
=
ϕ
(
t
,
y
)
 where 
ϕ
 is a function of the two variables 
t
 and 
y
.
 Under reasonable conditions on 
ϕ
,
 such an equation has a solution and the corresponding initial value problem has a unique solution. However, in general, these equations can be very difficult or impossible to solve explicitly.

🔗
Example5.15. IVP for Newton's Law of Cooling.
Consider this specific example of an initial value problem for Newton's law of cooling:
y
′
=
2
(
25
−
y
)
,
 
 
y
(
0
)
=
40
.
Discuss the solutions for this initial value problem.

🔗5.2.2 Separable Equations
🔗Why could we solve  from the previous section? Our solution depended on rewriting the equation so that all instances of 
y
 were on one side of the equation and all instances of 
t
 were on the other. Of course, in this case the only 
t
 was originally hidden, since we didn't write 
d
y
/
d
t
 in the original equation. This is not required, however. This idea of being able to separate the independent and dependent variables in a first order differential equation leads to a classification of first order differential equations into separable and non-separable equations as follows.

🔗Definition 5.16. Separable DE.  A first order differential equation is separable if it can be written in the form
d
y
d
t
=
f
(
t
)
g
(
y
)
.
🔗Let's come back to all first order differential equations on our list from the previous section and decide which ones are separable or not:

y
′
=
e
x
sec
y
 has order 1, is non-linear, is separable

y
′
−
e
x
y
+
3
=
0
 has order 1, is linear, is not separable

y
′
−
e
x
y
=
0
 has order 1, is linear, is separable

🔗As in the examples, we can attempt to solve a separable equation by converting to the form

∫
1
g
(
y
)
d
y
=
∫
f
(
t
)
d
t
.
🔗This technique is called separation of variables. The simplest (in principle) sort of separable equation is one in which 
g
(
y
)
=
1
,
 in which case we attempt to solve

∫
1
d
y
=
∫
f
(
t
)
d
t
.
🔗We can do this if we can find an antiderivative of 
f
(
t
)
.

🔗As we have seen so far, a differential equation typically has an infinite number of solutions. Such a solution is called a general solution. A corresponding initial value problem will give rise to just one solution. Such a solution in which there are no unknown constants remaining is called a specific solution.

🔗The general approach to separable equations is as follows: Suppose we wish to solve 
y
′
=
f
(
t
)
g
(
y
)
 where 
f
 and 
g
 are continuous functions. If 
g
(
a
)
=
0
 for some 
a
 then 
y
(
t
)
=
a
 is a constant solution of the equation, since in this case 
y
′
=
0
=
f
(
t
)
g
(
a
)
.
 For example, 
y
′
=
y
2
−
1
 has constant solutions 
y
(
t
)
=
1
 and 
y
(
t
)
=
−
1
.

🔗To find the non-constant solutions, we note that the function 
1
/
g
(
y
)
 is continuous where 
g
≠
0
,
 so 
1
/
g
 has an antiderivative 
G
.
 Let 
F
 be an antiderivative of 
f
.
 Now we write

G
(
y
)
=
∫
1
g
(
y
)
d
y
=
∫
f
(
t
)
d
t
=
F
(
t
)
+
C
,
🔗so 
G
(
y
)
=
F
(
t
)
+
C
.
 Now we solve this equation for 
y
.

🔗Of course, there are a few places this ideal description could go wrong: Finding the antiderivatives 
G
 and 
F
,
 and solving the final equation for 
y
.
 The upshot is that the solutions to the original differential equation are the constant solutions, if any, and all functions 
y
 that satisfy 
G
(
y
)
=
F
(
t
)
+
C
.

🔗
Guideline for Separation of Variables.
Given the differential equation

d
y
d
t
=
f
(
t
)
g
(
y
)
,
follow these steps to find the non-constant solutions.

Separate the variables:

d
y
g
(
y
)
=
f
(
t
)
d
t
Apply the integration operator:

∫
d
y
g
(
y
)
=
∫
f
(
t
)
d
t
If an antiderivative exists for 
f
 and for 
1
/
g
,
 and we can solve for 
y
,
 then

G
(
y
)
=
F
(
t
)
+
C
for some constant 
C
.

fit width

🔗
Example5.17. Solving a Separable Differential Equation I.
Solve the differential equation 
y
′
=
2
t
(
25
−
y
)
.
🔗
Example5.18. Solving a Seperable Differential Equation II.
Find the solutions to the differential equation
sec
(
t
)
d
y
d
t
−
e
y
+
sin
(
t
)
=
0
.
🔗5.2.3 Simple Growth and Decay Model
🔗
Example5.19. Rate of Change Proportional to Size.
Find the solutions to the differential equation 
y
′
=
k
y
,
 which models a quantity 
y
 that grows or decays proportionally to its size depending on whether 
k
 is positive or negative.
🔗The constant 
k
 in the above differential equation is referred to as the growth rate constant. Furthermore, this type of differential equation is known as a simple model for growth and decay of some quantity, since it only considers that the growth rate is proportional to the size of the quantity itself without any other factors influencing 
y
.
 The graph below shows the typical 
J
-shape of such a solution for some 
y
0
.


🔗
Simple Growth and Decay Model.
The differential equation

d
y
d
t
=
k
y
with growth rate constant 
k
 models simple growth and decay of a quantity 
y
 at time 
t
 with solution

y
=
y
0
e
k
t
,
where 
y
0
 is the initial value at time 
t
=
0
.

When 
k
>
0
,
 this describes certain simple cases of population growth: It says that the change in the population 
y
 is proportional to the population. The underlying assumption is that each organism in the current population reproduces at a fixed rate, so the larger the population the more new organisms are produced. While this is too simple to model most real populations, it is useful in some cases over a limited time.

When 
k
<
0
,
 the differential equation describes a quantity that decreases in proportion to the current value. This can be used to model radioactive decay.

🔗Interactive Demonstration. Use the sliders below to investigate the differential equation 
d
y
d
t
=
f
(
t
,
y
)
 where 

🔗Note: The simple growth and decay model is unrestricted because the quantity 
y
 grows without bound as 
t
→
∞
 if 
k
>
0
.
 In the decay case, the solution only becomes unbounded if time 
t
 is allowed to approach 
−
∞
.

fit width

🔗
Example5.20. Simple Growth Model.
Suppose $5,000 is deposited into an account which earns continuously compounded interest. Under these conditions, the balance in the account grows at a rate proportional to the current balance. Suppose that after 4 years the account is worth $7,000.
How much is the account worth after 5 years?

How many years does it take for the balance to double?

🔗5.2.4 Logistic Growth Model
🔗The simple growth model is unrealistic because a quantity that represents something from real life, say, population, does not grow unrestricted. Typically, food resources, competition, predators, or diseases, to name but a few factors, influence the growth of the population, and how much of the population can be sustained in such an environment.

🔗A more realistic model is the so-called logistic growth model, which mimics that as a quantity is growing other factors will influence the growth and slow it down until a certain maximum size is being approached. For example, if a population is growing, then food may become scarce or diseases may break out among the population, and the population growth slows down until a certain sustainable size is reached. Replacing 
k
 in the simple model with 
r
(
M
−
y
)
 achieves that as the quantity 
y
 increases the growth rate decreases, and furthermore, that the maximum population size that is sustained is 
M
.
 This maximum is referred to as the carrying capacity. So the simple model becomes

d
y
d
t
=
r
y
(
M
−
y
)
🔗for some positive 
r
 and 
M
.

🔗In other words, the rate of growth of the quantity 
y
 is proportional to both itself and the remaining carrying capacity that the quantity can still grow to. As usual, 
r
 is the growth rate constant.

🔗To solve this first order non-linear differential equation, notice that the equation is separable

d
y
y
(
M
−
y
)
=
r
d
t
.
🔗Integrating both sides we obtain

∫
d
y
y
(
M
−
y
)
=
∫
r
d
t
∫
(
1
y
+
1
M
−
y
)
d
y
=
∫
r
M
d
t
ln
|
y
|
−
ln
|
M
−
y
|
=
r
M
t
+
C
ln
∣
∣
∣
M
−
y
y
∣
∣
∣
=
−
r
M
t
−
C
∣
∣
∣
M
−
y
y
∣
∣
∣
=
e
−
r
M
t
−
C
M
−
y
y
=
A
e
−
r
M
t
,
🔗where 
A
=
M
−
y
0
y
0
 for 
t
0
=
0
.

🔗Lastly, we solve for 
y
 and get the solution

y
=
M
1
+
A
e
−
r
M
t
 with 
A
=
M
−
y
0
y
0
🔗for some constant 
y
0
 at time 
t
0
=
0
.

🔗The graph below shows the typical 
S
-shape of such a solution for some 
y
0
 between 
0
 and 
M
.
 For 
y
0
≥
M
,
 the solution decays exponentially to 
M
.
 And for 
y
0
=
M
,
 the solution remains constant.


🔗Note: Let us rewrite

d
y
d
t
=
r
y
(
M
−
y
)
🔗as

d
y
d
t
=
r
M
(
M
−
y
M
)
y
.
🔗Then we can make the following interpretations.

When the quantity 
y
 is small, then the term 
M
−
y
M
 is close in value to one, and so the differential equation

d
y
d
t
=
r
y
(
M
−
y
)
≈
r
M
y
.
In other words, the growth is exponential.

However, when the quantity 
y
 is near that of the carrying capacity 
M
,
 then the term 
M
−
y
M
 is close in value to zero. Hence, the less carrying capacity that remains the more the growth rate is slowed down.

🔗
Logistic Growth Model.
The differential equation

d
y
d
t
=
r
y
(
M
−
y
)
with positive growth constant 
r
 and carrying capacity 
M
 models logistic growth of a quantity 
y
 at time 
t
 with solution

y
=
M
1
+
A
e
−
r
M
t
,
where 
A
=
M
−
y
0
y
0
 at time 
t
0
=
0
.

🔗Interactive Demonstration. Use the sliders below to investigate the differential equation 
d
y
d
t
=
f
(
t
,
y
)
 where
 5.3 First Order Linear Differential Equations ¶
🔗5.3.1 Homogeneous DEs ¶
🔗A simple, but important and useful, type of separable equation is the first order homogeneous linear equation:

🔗Definition 5.21. First Order Homogeneous Linear DE.  A first order homogeneous linear differential equation is one of the form 
y
′
+
p
(
t
)
y
=
0
 or equivalently 
y
′
=
−
p
(
t
)
y
.
🔗We have already seen a first order homogeneous linear differential equation, namely the simple growth and decay model 
y
′
=
k
y
.

🔗Since first order homogeneous linear equations are separable, we can solve them in the usual way:

y
′
=
−
p
(
t
)
y
∫
1
y
d
y
=
∫
−
p
(
t
)
d
t
ln
|
y
|
=
P
(
t
)
+
C
y
=
±
e
P
(
t
)
+
C
y
=
A
e
P
(
t
)
,
🔗where 
P
(
t
)
 is an antiderivative of 
−
p
(
t
)
.
 As in previous examples, if we allow 
A
=
0
 we get the constant solution 
y
=
0
.

🔗
Example5.22. Solving an IVP I.
Solve the initial value problem
y
′
+
y
cos
t
=
0
,
subject to

y
(
0
)
=
1
/
2

y
(
2
)
=
1
/
2

🔗
Example5.23. Solving an IVP II.
Solve the initial value problem 
t
y
′
+
3
y
=
0
,
 
y
(
1
)
=
2
,
 assuming 
t
>
0
.
🔗5.3.2 Non-Homogeneous DEs ¶
🔗As you might guess, a first order non-homogeneous linear differential equation has the form 
y
′
+
p
(
t
)
y
=
f
(
t
)
.
 Not only is this closely related in form to the first order homogeneous linear equation, we can use what we know about solving homogeneous equations to solve the general linear equation.

🔗Definition 5.24. First Order Non-Homogeneous Linear DE.  A first order non-homogeneous linear differential equation is one of the form
y
′
+
p
(
t
)
y
=
f
(
t
)
.
🔗Note: When the coefficient of the first derivative is one in the first order non-homogeneous linear differential equation as in the above definition, then we say the DE is in standard form.

🔗Let us now discuss how we can find all solutions to a first order non-homogeneous linear differential equation. Suppose that 
y
1
(
t
)
 and 
y
2
(
t
)
 are solutions to 
y
′
+
p
(
t
)
y
=
f
(
t
)
.
 Let 
g
(
t
)
=
y
1
−
y
2
.
 Then

g
′
(
t
)
+
p
(
t
)
g
(
t
)
=
y
′
1
−
y
′
2
+
p
(
t
)
(
y
1
−
y
2
)
=
(
y
′
1
+
p
(
t
)
y
1
)
−
(
y
′
2
+
p
(
t
)
y
2
)
=
f
(
t
)
−
f
(
t
)
=
0
.
🔗In other words, 
g
(
t
)
=
y
1
−
y
2
 is a solution to the homogeneous equation 
y
′
+
p
(
t
)
y
=
0
.
 Turning this around, any solution to the linear equation 
y
′
+
p
(
t
)
y
=
f
(
t
)
,
 call it 
y
1
,
 can be written as 
y
2
+
g
(
t
)
,
 for some particular 
y
2
 and some solution 
g
(
t
)
 of the homogeneous equation 
y
′
+
p
(
t
)
y
=
0
.
 Since we already know how to find all solutions of the homogeneous equation, finding just one solution to the equation 
y
′
+
p
(
t
)
y
=
f
(
t
)
 will give us all of them.

🔗Theorem 5.25. General Solution of First Order Non-Homogeneous Linear DE.  Given a first order non-homogeneous linear differential equation
y
′
+
p
(
t
)
y
=
f
(
t
)
,
let 
h
(
t
)
 be a particular solution, and let 
g
(
t
)
 be the general solution to the corresponding homogeneous DE

y
′
+
p
(
t
)
y
=
0
.
Then the general solution to the non-homogeneous DE is constructed as the sum of the above two solutions:

y
(
t
)
=
g
(
t
)
+
h
(
t
)
.
🔗5.3.2.1 Variation of Parameters
🔗We now introduce the first one of two methods discussed in these notes to solve a first order non-homogeneous linear differential equation. Again, it turns out that what we already know helps. We know that the general solution to the homogeneous equation 
y
′
+
p
(
t
)
y
=
0
 looks like 
A
e
P
(
t
)
,
 where 
P
(
t
)
 is an antiderivative of 
−
p
(
t
)
.
 We now make an inspired guess: Consider the function 
v
(
t
)
e
P
(
t
)
,
 in which we have replaced the constant parameter 
A
 with the function 
v
(
t
)
.
 This technique is called variation of parameters. For convenience write this as 
s
(
t
)
=
v
(
t
)
h
(
t
)
,
 where 
h
(
t
)
=
e
P
(
t
)
 is a solution to the homogeneous equation. Now let's compute a bit with 
s
(
t
)
:

s
′
(
t
)
+
p
(
t
)
s
(
t
)
=
v
(
t
)
h
′
(
t
)
+
v
′
(
t
)
h
(
t
)
+
p
(
t
)
v
(
t
)
h
(
t
)
=
v
(
t
)
(
h
′
(
t
)
+
p
(
t
)
h
(
t
)
)
+
v
′
(
t
)
h
(
t
)
=
v
′
(
t
)
h
(
t
)
.
🔗The last equality is true because 
h
′
(
t
)
+
p
(
t
)
h
(
t
)
=
0
,
 since 
h
(
t
)
 is a solution to the homogeneous equation. We are hoping to find a function 
s
(
t
)
 so that 
s
′
(
t
)
+
p
(
t
)
s
(
t
)
=
f
(
t
)
;
 we will have such a function if we can arrange to have 
v
′
(
t
)
h
(
t
)
=
f
(
t
)
,
 that is, 
v
′
(
t
)
=
f
(
t
)
/
h
(
t
)
.
 But this is as easy (or hard) as finding an antiderivative of 
f
(
t
)
/
h
(
t
)
.
 Putting this all together, the general solution to 
y
′
+
p
(
t
)
y
=
f
(
t
)
 is

v
(
t
)
h
(
t
)
+
A
e
P
(
t
)
=
v
(
t
)
e
P
(
t
)
+
A
e
P
(
t
)
.
🔗
Method of Variation of Parameters.
Given a first order non-homogeneous linear differential equation

y
′
+
p
(
t
)
y
=
f
(
t
)
,
using variation of parameters the general solution is given by

y
(
t
)
=
v
(
t
)
e
P
(
t
)
+
A
e
P
(
t
)
,
where 
v
′
(
t
)
=
e
−
P
(
t
)
f
(
t
)
 and 
P
(
t
)
 is an antiderivative of 
−
p
(
t
)
.

🔗Note: The method of variation of parameters makes more sense after taking linear algebra since the method uses determinants. We therefore restrict ourselves to just one example to illustrate this method.

fit width

🔗
Example5.26. Solving an IVP Using Variation of Parameters.
Find the solution of the initial value problem 
y
′
+
3
y
/
t
=
t
2
,
 
y
(
1
)
=
1
/
2
.
🔗******* Integrating Factor
🔗Another common method for solving such a differential equation is by means of an integrating factor . In the differential equation 
y
′
+
p
(
t
)
y
=
f
(
t
)
,
 we note that if we multiply through by a function 
I
(
t
)
 to get 
I
(
t
)
y
′
+
I
(
t
)
p
(
t
)
y
=
I
(
t
)
f
(
t
)
,
 the left hand side looks like it could be a derivative computed by the Product Rule:

d
d
t
(
I
(
t
)
y
)
=
I
(
t
)
y
′
+
I
′
(
t
)
y
.
🔗Now if we could choose 
I
(
t
)
 so that 
I
′
(
t
)
=
I
(
t
)
p
(
t
)
,
 this would be exactly the left hand side of the differential equation. But this is just a first order homogeneous linear equation, and we know a solution is 
I
(
t
)
=
e
Q
(
t
)
,
 where 
Q
(
t
)
=
∫
p
(
t
)
d
t
.
 Note that 
Q
(
t
)
=
−
P
(
t
)
,
 where 
P
(
t
)
 appears in the variation of parameters method and 
P
′
(
t
)
=
−
p
(
t
)
.
 Now the modified differential equation is

e
−
P
(
t
)
y
′
+
e
−
P
(
t
)
p
(
t
)
y
=
e
−
P
(
t
)
f
(
t
)
d
d
t
(
e
−
P
(
t
)
y
)
=
e
−
P
(
t
)
f
(
t
)
.
🔗Integrating both sides gives

e
−
P
(
t
)
y
=
∫
e
−
P
(
t
)
f
(
t
)
d
t
y
=
e
P
(
t
)
∫
e
−
P
(
t
)
f
(
t
)
d
t
.
Note: If you look carefully, you will see that this is exactly the same solution we found by variation of parameters, because 
e
−
P
(
t
)
f
(
t
)
=
f
(
t
)
/
h
(
t
)
.
 Some people find it easier to remember how to use the integrating factor method, rather than variation of parameters. Since ultimately they require the same calculation, you should use whichever of the two methods appeals to you more.
🔗Definition 5.27. Integrating Factor.  Given a first order non-homogeneous linear differential equation
y
′
+
p
(
t
)
y
=
f
(
t
)
,
the integrating factor is given by

I
(
t
)
=
e
∫
p
(
t
)
d
t
.
🔗
Method of Integrating Factor.
Given a first order non-homogeneous linear differential equation

y
′
+
p
(
t
)
y
=
f
(
t
)
,
follow these steps to determine the general solution 
y
(
t
)
 using an integrating factor:

Calculate the integrating factor 
I
(
t
)
.

Multiply the standard form equation by 
I
(
t
)
.

Simplify the left-hand side to

d
d
t
[
I
(
t
)
y
]
.
Integrate both sides of the equation.

Solve for 
y
(
t
)
.

The solution can be compactly written as

y
(
t
)
=
e
−
∫
p
(
t
)
d
t
[
∫
e
∫
p
(
t
)
d
t
f
(
t
)
d
t
+
C
]
.
🔗Using this method, the solution of the previous example would look just a bit different.

fit width

🔗
Example5.28. Solving an IVP Using Integrating Factor.
Find the solution of the initial value problem 
y
′
+
3
y
/
t
=
t
2
,
 
y
(
1
)
=
1
/
2
.
🔗
Example5.29. General Solution Using Integrating Factor.
Determine the general solution of the differential equation
d
y
d
t
+
3
t
2
y
=
6
t
2
.
🔗Exercises for Section 5.3.