<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Special Discrete Probability Distributions - Complete Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .definition-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .distribution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .distribution-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .distribution-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .distribution-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .parameter-list {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Special Discrete Probability Distributions</h1>
            <div class="subtitle">A Complete Step-by-Step Tutorial</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Introduction to Special Distributions</strong> - Why these distributions are important</li>
                <li><strong>Bernoulli Distribution</strong> - The simplest random variable with two outcomes</li>
                <li><strong>Geometric Distribution</strong> - Waiting for the first success</li>
                <li><strong>Binomial Distribution</strong> - Counting successes in fixed trials</li>
                <li><strong>Negative Binomial (Pascal) Distribution</strong> - Waiting for multiple successes</li>
                <li><strong>Hypergeometric Distribution</strong> - Sampling without replacement</li>
                <li><strong>Poisson Distribution</strong> - Counting rare events</li>
                <li><strong>Solved Problems and Applications</strong> - Real-world examples and practice</li>
            </ul>
        </div>

        <h2>🎯 Introduction to Special Distributions</h2>
        
        <div class="concept-box">
            <p><strong>Why do we study special distributions?</strong></p>
            <p>In probability theory, certain random experiments appear repeatedly in real-world applications. Rather than deriving their probability mass functions (PMFs) from scratch each time, mathematicians have identified these common patterns and given them special names.</p>
            <p style="text-align: center; font-size: 1.1em; color: #2c3e50; font-style: italic;">
                "Understanding the random experiment behind each distribution is more important than memorizing formulas."
            </p>
        </div>

        <div class="highlight">
            <p><strong>Key Learning Strategy:</strong> Focus on understanding the underlying random experiment for each distribution. If you understand the experiment, you can derive the PMF when needed!</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="300" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Special Distributions Overview
                </text>
                
                <!-- Distribution boxes -->
                <rect x="50" y="60" width="100" height="60" fill="#3498db" rx="5" opacity="0.8"/>
                <text x="100" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Bernoulli</text>
                <text x="100" y="100" text-anchor="middle" font-size="10" fill="white">Success/Failure</text>
                
                <rect x="170" y="60" width="100" height="60" fill="#e74c3c" rx="5" opacity="0.8"/>
                <text x="220" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Geometric</text>
                <text x="220" y="100" text-anchor="middle" font-size="10" fill="white">First Success</text>
                
                <rect x="290" y="60" width="100" height="60" fill="#f39c12" rx="5" opacity="0.8"/>
                <text x="340" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Binomial</text>
                <text x="340" y="100" text-anchor="middle" font-size="10" fill="white">Fixed Trials</text>
                
                <rect x="410" y="60" width="100" height="60" fill="#2ecc71" rx="5" opacity="0.8"/>
                <text x="460" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Pascal</text>
                <text x="460" y="100" text-anchor="middle" font-size="10" fill="white">Multiple Success</text>
                
                <rect x="530" y="60" width="100" height="60" fill="#9b59b6" rx="5" opacity="0.8"/>
                <text x="580" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Hypergeometric</text>
                <text x="580" y="100" text-anchor="middle" font-size="10" fill="white">No Replacement</text>
                
                <rect x="650" y="60" width="100" height="60" fill="#1abc9c" rx="5" opacity="0.8"/>
                <text x="700" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Poisson</text>
                <text x="700" y="100" text-anchor="middle" font-size="10" fill="white">Rare Events</text>
                
                <!-- Arrows showing relationships -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                
                <!-- Bernoulli to Geometric -->
                <line x1="150" y1="90" x2="170" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="160" y="85" text-anchor="middle" font-size="8" fill="#2c3e50">repeat</text>
                
                <!-- Bernoulli to Binomial -->
                <path d="M 150 90 Q 200 140 290 90" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                <text x="220" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">sum n trials</text>
                
                <!-- Geometric to Pascal -->
                <line x1="320" y1="90" x2="410" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="365" y="85" text-anchor="middle" font-size="8" fill="#2c3e50">generalize</text>
                
                <!-- Binomial to Poisson -->
                <path d="M 390 90 Q 520 140 650 90" stroke="#2c3e50" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                <text x="520" y="150" text-anchor="middle" font-size="8" fill="#2c3e50">n→∞, p→0</text>
                
                <!-- Real-world applications -->
                <text x="400" y="200" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Real-World Applications
                </text>
                <text x="100" y="220" text-anchor="middle" font-size="10" fill="#34495e">Pass/Fail Exam</text>
                <text x="220" y="220" text-anchor="middle" font-size="10" fill="#34495e">Coin Tosses</text>
                <text x="340" y="220" text-anchor="middle" font-size="10" fill="#34495e">Quality Control</text>
                <text x="460" y="220" text-anchor="middle" font-size="10" fill="#34495e">Customer Arrivals</text>
                <text x="580" y="220" text-anchor="middle" font-size="10" fill="#34495e">Card Drawing</text>
                <text x="700" y="220" text-anchor="middle" font-size="10" fill="#34495e">Email Arrivals</text>
            </svg>
        </div>

        <h2>🎲 Bernoulli Distribution</h2>
        
        <div class="concept-box">
            <p><strong>The Random Experiment:</strong> A single trial with exactly two possible outcomes - "success" and "failure".</p>
        </div>

        <div class="example-box">
            <p><strong>Real-World Examples:</strong></p>
            <ul>
                <li>Taking a pass-fail exam (pass = 1, fail = 0)</li>
                <li>Tossing a coin (heads = 1, tails = 0)</li>
                <li>Birth of a child (male = 1, female = 0)</li>
                <li>Testing a product (defective = 1, good = 0)</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition 3.4: Bernoulli Distribution</h3>
            <p>A random variable $X$ is said to be a <strong>Bernoulli random variable</strong> with parameter $p$, shown as $X \sim \text{Bernoulli}(p)$, if its PMF is given by:</p>
            <div class="formula-box">
                $$P_X(x) = \begin{cases}
                p & \text{for } x = 1 \\
                1-p & \text{for } x = 0 \\
                0 & \text{otherwise}
                \end{cases}$$
                <p style="margin-top: 10px;">where $0 < p < 1$</p>
            </div>
        </div>

        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Background -->
                <rect width="600" height="300" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                    Bernoulli(p) PMF Visualization
                </text>

                <!-- Axes -->
                <line x1="80" y1="250" x2="520" y2="250" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="250" x2="80" y2="50" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="200" y="270" text-anchor="middle" font-size="14" fill="#2c3e50">0</text>
                <text x="400" y="270" text-anchor="middle" font-size="14" fill="#2c3e50">1</text>
                <text x="300" y="290" text-anchor="middle" font-size="12" fill="#2c3e50">x</text>

                <!-- Y-axis labels -->
                <text x="70" y="255" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="70" y="200" text-anchor="end" font-size="12" fill="#2c3e50">0.3</text>
                <text x="70" y="150" text-anchor="end" font-size="12" fill="#2c3e50">0.6</text>
                <text x="70" y="100" text-anchor="end" font-size="12" fill="#2c3e50">0.9</text>
                <text x="40" y="150" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90 40 150)">P(X = x)</text>

                <!-- Probability bars for p = 0.3 -->
                <rect x="180" y="133" width="40" height="117" fill="#e74c3c" opacity="0.8"/>
                <rect x="380" y="200" width="40" height="50" fill="#3498db" opacity="0.8"/>

                <!-- Probability values -->
                <text x="200" y="125" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">1-p = 0.7</text>
                <text x="400" y="190" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">p = 0.3</text>

                <!-- Parameter box -->
                <rect x="450" y="60" width="130" height="80" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="515" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Parameters:</text>
                <text x="515" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">p = 0.3</text>
                <text x="515" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Range: {0, 1}</text>
                <text x="515" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">Mean: p</text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Indicator Random Variable:</strong> The Bernoulli distribution is also called the <em>indicator random variable</em> for an event $A$:</p>
            $$I_A = \begin{cases}
            1 & \text{if event } A \text{ occurs} \\
            0 & \text{otherwise}
            \end{cases}$$
            <p>We have $I_A \sim \text{Bernoulli}(P(A))$</p>
        </div>

        <h2>📈 Geometric Distribution</h2>

        <div class="concept-box">
            <p><strong>The Random Experiment:</strong> Toss a coin with $P(H) = p$ until you observe the first heads. Count the total number of tosses.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "How many trials until the first success?"
            </p>
        </div>

        <div class="example-box">
            <p><strong>Real-World Examples:</strong></p>
            <ul>
                <li>Number of job interviews until you get hired</li>
                <li>Number of lottery tickets bought until you win</li>
                <li>Number of attempts to start a car on a cold morning</li>
                <li>Number of customers served until finding a complaint</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition 3.5: Geometric Distribution</h3>
            <p>A random variable $X$ is said to be a <strong>geometric random variable</strong> with parameter $p$, shown as $X \sim \text{Geometric}(p)$, if its PMF is given by:</p>
            <div class="formula-box">
                $$P_X(k) = \begin{cases}
                p(1-p)^{k-1} & \text{for } k = 1,2,3,\ldots \\
                0 & \text{otherwise}
                \end{cases}$$
                <p style="margin-top: 10px;">where $0 < p < 1$</p>
            </div>
        </div>

        <div class="svg-container">
            <svg width="700" height="350" viewBox="0 0 700 350">
                <!-- Background -->
                <rect width="700" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                    Geometric(0.3) PMF Visualization
                </text>

                <!-- Axes -->
                <line x1="80" y1="300" x2="620" y2="300" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="300" x2="80" y2="50" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="120" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                <text x="160" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                <text x="200" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                <text x="240" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">4</text>
                <text x="280" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">5</text>
                <text x="320" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">6</text>
                <text x="360" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">7</text>
                <text x="400" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">8</text>
                <text x="350" y="340" text-anchor="middle" font-size="12" fill="#2c3e50">k (number of trials)</text>

                <!-- Y-axis labels -->
                <text x="70" y="305" text-anchor="end" font-size="10" fill="#2c3e50">0</text>
                <text x="70" y="250" text-anchor="end" font-size="10" fill="#2c3e50">0.1</text>
                <text x="70" y="200" text-anchor="end" font-size="10" fill="#2c3e50">0.2</text>
                <text x="70" y="150" text-anchor="end" font-size="10" fill="#2c3e50">0.3</text>
                <text x="40" y="175" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90 40 175)">P(X = k)</text>

                <!-- Probability bars (p=0.3, so probabilities: 0.3, 0.21, 0.147, 0.103, 0.072, 0.050, 0.035, 0.025) -->
                <rect x="110" y="150" width="20" height="150" fill="#e74c3c" opacity="0.8"/>
                <rect x="150" y="195" width="20" height="105" fill="#e74c3c" opacity="0.8"/>
                <rect x="190" y="226" width="20" height="74" fill="#e74c3c" opacity="0.8"/>
                <rect x="230" y="248" width="20" height="52" fill="#e74c3c" opacity="0.8"/>
                <rect x="270" y="264" width="20" height="36" fill="#e74c3c" opacity="0.8"/>
                <rect x="310" y="275" width="20" height="25" fill="#e74c3c" opacity="0.8"/>
                <rect x="350" y="282" width="20" height="18" fill="#e74c3c" opacity="0.8"/>
                <rect x="390" y="287" width="20" height="13" fill="#e74c3c" opacity="0.8"/>

                <!-- Probability values -->
                <text x="120" y="140" text-anchor="middle" font-size="9" fill="#2c3e50">0.30</text>
                <text x="160" y="185" text-anchor="middle" font-size="9" fill="#2c3e50">0.21</text>
                <text x="200" y="216" text-anchor="middle" font-size="9" fill="#2c3e50">0.15</text>

                <!-- Formula illustration -->
                <rect x="450" y="60" width="220" height="120" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="560" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Geometric Distribution</text>
                <text x="560" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">P(X = k) = p(1-p)^(k-1)</text>
                <text x="560" y="120" text-anchor="middle" font-size="11" fill="#2c3e50">p = 0.3</text>
                <text x="560" y="135" text-anchor="middle" font-size="11" fill="#2c3e50">Range: {1, 2, 3, ...}</text>
                <text x="560" y="150" text-anchor="middle" font-size="11" fill="#2c3e50">Mean: 1/p = 3.33</text>
                <text x="560" y="165" text-anchor="middle" font-size="11" fill="#2c3e50">Variance: (1-p)/p² = 7.78</text>
            </svg>
        </div>

        <div class="warning">
            <p><strong>Alternative Definition:</strong> Some textbooks define the geometric distribution as the number of <em>failures</em> before the first success, giving range $\{0, 1, 2, \ldots\}$ and PMF $P(X = k) = p(1-p)^k$. Always check which definition is being used!</p>
        </div>

        <h2>🎯 Binomial Distribution</h2>

        <div class="concept-box">
            <p><strong>The Random Experiment:</strong> Toss a coin with $P(H) = p$ exactly $n$ times. Count the total number of heads observed.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "How many successes in a fixed number of trials?"
            </p>
        </div>

        <div class="example-box">
            <p><strong>Real-World Examples:</strong></p>
            <ul>
                <li>Number of defective items in a batch of 100 products</li>
                <li>Number of students who pass out of 50 taking an exam</li>
                <li>Number of successful free throws out of 10 attempts</li>
                <li>Number of correct answers on a 20-question multiple choice test</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition 3.6: Binomial Distribution</h3>
            <p>A random variable $X$ is said to be a <strong>binomial random variable</strong> with parameters $n$ and $p$, shown as $X \sim \text{Binomial}(n,p)$, if its PMF is given by:</p>
            <div class="formula-box">
                $$P_X(k) = \begin{cases}
                \binom{n}{k} p^k (1-p)^{n-k} & \text{for } k = 0,1,2,\ldots,n \\
                0 & \text{otherwise}
                \end{cases}$$
                <p style="margin-top: 10px;">where $0 < p < 1$ and $n \in \mathbb{N}$</p>
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                    Binomial Distribution Comparison
                </text>

                <!-- Left plot: Binomial(10, 0.3) -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Binomial(10, 0.3)
                </text>

                <!-- Left axes -->
                <line x1="50" y1="350" x2="350" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="50" y1="350" x2="50" y2="100" stroke="#2c3e50" stroke-width="2"/>

                <!-- Left X-axis labels -->
                <text x="80" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="110" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                <text x="140" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">4</text>
                <text x="170" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">6</text>
                <text x="200" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">8</text>
                <text x="230" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">10</text>

                <!-- Left probability bars -->
                <rect x="75" y="320" width="10" height="30" fill="#3498db" opacity="0.8"/>
                <rect x="90" y="280" width="10" height="70" fill="#3498db" opacity="0.8"/>
                <rect x="105" y="220" width="10" height="130" fill="#3498db" opacity="0.8"/>
                <rect x="120" y="180" width="10" height="170" fill="#3498db" opacity="0.8"/>
                <rect x="135" y="200" width="10" height="150" fill="#3498db" opacity="0.8"/>
                <rect x="150" y="240" width="10" height="110" fill="#3498db" opacity="0.8"/>
                <rect x="165" y="280" width="10" height="70" fill="#3498db" opacity="0.8"/>
                <rect x="180" y="310" width="10" height="40" fill="#3498db" opacity="0.8"/>
                <rect x="195" y="330" width="10" height="20" fill="#3498db" opacity="0.8"/>
                <rect x="210" y="340" width="10" height="10" fill="#3498db" opacity="0.8"/>
                <rect x="225" y="345" width="10" height="5" fill="#3498db" opacity="0.8"/>

                <!-- Right plot: Binomial(20, 0.6) -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Binomial(20, 0.6)
                </text>

                <!-- Right axes -->
                <line x1="450" y1="350" x2="750" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="450" y1="350" x2="450" y2="100" stroke="#2c3e50" stroke-width="2"/>

                <!-- Right X-axis labels -->
                <text x="480" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="520" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">5</text>
                <text x="560" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">10</text>
                <text x="600" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">15</text>
                <text x="640" y="370" text-anchor="middle" font-size="10" fill="#2c3e50">20</text>

                <!-- Right probability bars (bell-shaped, centered around 12) -->
                <rect x="475" y="345" width="8" height="5" fill="#e74c3c" opacity="0.8"/>
                <rect x="490" y="340" width="8" height="10" fill="#e74c3c" opacity="0.8"/>
                <rect x="505" y="330" width="8" height="20" fill="#e74c3c" opacity="0.8"/>
                <rect x="520" y="310" width="8" height="40" fill="#e74c3c" opacity="0.8"/>
                <rect x="535" y="280" width="8" height="70" fill="#e74c3c" opacity="0.8"/>
                <rect x="550" y="240" width="8" height="110" fill="#e74c3c" opacity="0.8"/>
                <rect x="565" y="200" width="8" height="150" fill="#e74c3c" opacity="0.8"/>
                <rect x="580" y="160" width="8" height="190" fill="#e74c3c" opacity="0.8"/>
                <rect x="595" y="140" width="8" height="210" fill="#e74c3c" opacity="0.8"/>
                <rect x="610" y="160" width="8" height="190" fill="#e74c3c" opacity="0.8"/>
                <rect x="625" y="200" width="8" height="150" fill="#e74c3c" opacity="0.8"/>
                <rect x="640" y="240" width="8" height="110" fill="#e74c3c" opacity="0.8"/>
                <rect x="655" y="280" width="8" height="70" fill="#e74c3c" opacity="0.8"/>
                <rect x="670" y="310" width="8" height="40" fill="#e74c3c" opacity="0.8"/>
                <rect x="685" y="330" width="8" height="20" fill="#e74c3c" opacity="0.8"/>
                <rect x="700" y="340" width="8" height="10" fill="#e74c3c" opacity="0.8"/>
                <rect x="715" y="345" width="8" height="5" fill="#e74c3c" opacity="0.8"/>

                <!-- Key properties -->
                <rect x="50" y="80" width="150" height="60" fill="white" stroke="#3498db" stroke-width="1" rx="3"/>
                <text x="125" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">Mean = np = 3</text>
                <text x="125" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">Var = np(1-p) = 2.1</text>
                <text x="125" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">Skewed right</text>

                <rect x="600" y="80" width="150" height="60" fill="white" stroke="#e74c3c" stroke-width="1" rx="3"/>
                <text x="675" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">Mean = np = 12</text>
                <text x="675" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">Var = np(1-p) = 4.8</text>
                <text x="675" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">Nearly symmetric</text>
            </svg>
        </div>

        <h3><span class="step-number">1</span>Binomial as Sum of Bernoulli Variables</h3>

        <div class="concept-box">
            <p><strong>Key Insight:</strong> A binomial random variable can be viewed as the sum of independent Bernoulli random variables:</p>
            <div class="formula-box">
                $$\text{If } X_1, X_2, \ldots, X_n \text{ are independent Bernoulli}(p) \text{ random variables, then}$$
                $$X = X_1 + X_2 + \cdots + X_n \sim \text{Binomial}(n,p)$$
            </div>
        </div>

        <div class="example-box">
            <h4>Example 3.7: Sum of Independent Binomial Variables</h4>
            <p><strong>Problem:</strong> Let $X \sim \text{Binomial}(n,p)$ and $Y \sim \text{Binomial}(m,p)$ be independent. Find the PMF of $Z = X + Y$.</p>
            <p><strong>Solution:</strong> Since both $X$ and $Y$ count successes with the same probability $p$, their sum $Z$ counts successes in $n + m$ total trials. Therefore:</p>
            <div class="formula-box">
                $$Z \sim \text{Binomial}(n+m, p)$$
                $$P_Z(k) = \binom{n+m}{k} p^k (1-p)^{n+m-k}$$
            </div>
        </div>

        <h2>🔄 Negative Binomial (Pascal) Distribution</h2>

        <div class="concept-box">
            <p><strong>The Random Experiment:</strong> Toss a coin with $P(H) = p$ until you observe exactly $m$ heads. Count the total number of tosses.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "How many trials until the $m$-th success?"
            </p>
        </div>

        <div class="example-box">
            <p><strong>Real-World Examples:</strong></p>
            <ul>
                <li>Number of sales calls until you make 5 sales</li>
                <li>Number of patients examined until finding 3 with a rare disease</li>
                <li>Number of components tested until finding 10 defective ones</li>
                <li>Number of days until a restaurant serves 100 customers</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition 3.7: Pascal Distribution</h3>
            <p>A random variable $X$ is said to be a <strong>Pascal random variable</strong> with parameters $m$ and $p$, shown as $X \sim \text{Pascal}(m,p)$, if its PMF is given by:</p>
            <div class="formula-box">
                $$P_X(k) = \begin{cases}
                \binom{k-1}{m-1} p^m (1-p)^{k-m} & \text{for } k = m, m+1, m+2, \ldots \\
                0 & \text{otherwise}
                \end{cases}$$
                <p style="margin-top: 10px;">where $0 < p < 1$ and $m \in \mathbb{N}$</p>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Special Case:</strong> When $m = 1$, we get $\text{Pascal}(1,p) = \text{Geometric}(p)$</p>
        </div>

        <div class="svg-container">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Background -->
                <rect width="600" height="300" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                    Pascal(3, 0.5) PMF Visualization
                </text>

                <!-- Axes -->
                <line x1="80" y1="250" x2="520" y2="250" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="250" x2="80" y2="50" stroke="#2c3e50" stroke-width="2"/>

                <!-- X-axis labels -->
                <text x="120" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">3</text>
                <text x="150" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">4</text>
                <text x="180" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">5</text>
                <text x="210" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">6</text>
                <text x="240" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">7</text>
                <text x="270" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">8</text>
                <text x="300" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">9</text>
                <text x="330" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">10</text>
                <text x="300" y="290" text-anchor="middle" font-size="12" fill="#2c3e50">k (number of trials)</text>

                <!-- Y-axis labels -->
                <text x="70" y="255" text-anchor="end" font-size="10" fill="#2c3e50">0</text>
                <text x="70" y="200" text-anchor="end" font-size="10" fill="#2c3e50">0.1</text>
                <text x="70" y="150" text-anchor="end" font-size="10" fill="#2c3e50">0.2</text>
                <text x="40" y="150" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90 40 150)">P(X = k)</text>

                <!-- Probability bars -->
                <rect x="115" y="175" width="10" height="75" fill="#9b59b6" opacity="0.8"/>
                <rect x="145" y="150" width="10" height="100" fill="#9b59b6" opacity="0.8"/>
                <rect x="175" y="140" width="10" height="110" fill="#9b59b6" opacity="0.8"/>
                <rect x="205" y="155" width="10" height="95" fill="#9b59b6" opacity="0.8"/>
                <rect x="235" y="175" width="10" height="75" fill="#9b59b6" opacity="0.8"/>
                <rect x="265" y="190" width="10" height="60" fill="#9b59b6" opacity="0.8"/>
                <rect x="295" y="205" width="10" height="45" fill="#9b59b6" opacity="0.8"/>
                <rect x="325" y="215" width="10" height="35" fill="#9b59b6" opacity="0.8"/>

                <!-- Parameter box -->
                <rect x="400" y="60" width="180" height="100" fill="white" stroke="#9b59b6" stroke-width="2" rx="5"/>
                <text x="490" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Pascal Distribution</text>
                <text x="490" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">m = 3, p = 0.5</text>
                <text x="490" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Range: {3, 4, 5, ...}</text>
                <text x="490" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">Mean: m/p = 6</text>
                <text x="490" y="145" text-anchor="middle" font-size="11" fill="#2c3e50">Variance: m(1-p)/p² = 6</text>
            </svg>
        </div>

        <h2>🎴 Hypergeometric Distribution</h2>

        <div class="concept-box">
            <p><strong>The Random Experiment:</strong> You have a bag with $b$ blue marbles and $r$ red marbles. Draw $k$ marbles <em>without replacement</em>. Count the number of blue marbles drawn.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Sampling without replacement from a finite population"
            </p>
        </div>

        <div class="example-box">
            <p><strong>Real-World Examples:</strong></p>
            <ul>
                <li>Drawing cards from a deck without replacement</li>
                <li>Quality control: testing items from a finite batch</li>
                <li>Survey sampling from a specific population</li>
                <li>Selecting committee members from a group</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition 3.8: Hypergeometric Distribution</h3>
            <p>A random variable $X$ is said to be a <strong>hypergeometric random variable</strong> with parameters $b$, $r$, and $k$, shown as $X \sim \text{Hypergeometric}(b,r,k)$, if its range is:</p>
            <div class="formula-box">
                $$R_X = \{\max(0, k-r), \max(0, k-r)+1, \ldots, \min(k,b)\}$$
            </div>
            <p>and its PMF is given by:</p>
            <div class="formula-box">
                $$P_X(x) = \begin{cases}
                \frac{\binom{b}{x}\binom{r}{k-x}}{\binom{b+r}{k}} & \text{for } x \in R_X \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>
        </div>

        <div class="svg-container">
            <svg width="700" height="350" viewBox="0 0 700 350">
                <!-- Background -->
                <rect width="700" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                    Hypergeometric Distribution Illustration
                </text>

                <!-- Bag illustration -->
                <ellipse cx="150" cy="150" rx="80" ry="60" fill="#8e44ad" opacity="0.3" stroke="#8e44ad" stroke-width="3"/>
                <text x="150" y="100" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Population</text>

                <!-- Blue marbles in bag -->
                <circle cx="120" cy="130" r="8" fill="#3498db"/>
                <circle cx="140" cy="140" r="8" fill="#3498db"/>
                <circle cx="160" cy="135" r="8" fill="#3498db"/>
                <circle cx="180" cy="145" r="8" fill="#3498db"/>
                <circle cx="130" cy="160" r="8" fill="#3498db"/>
                <circle cx="170" cy="165" r="8" fill="#3498db"/>

                <!-- Red marbles in bag -->
                <circle cx="125" cy="175" r="8" fill="#e74c3c"/>
                <circle cx="145" cy="170" r="8" fill="#e74c3c"/>
                <circle cx="165" cy="175" r="8" fill="#e74c3c"/>
                <circle cx="175" cy="155" r="8" fill="#e74c3c"/>

                <!-- Arrow -->
                <path d="M 230 150 Q 280 120 330 150" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                <text x="280" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">Draw k marbles</text>
                <text x="280" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">without replacement</text>

                <!-- Sample illustration -->
                <rect x="350" y="120" width="120" height="60" fill="#f8f9fa" stroke="#2c3e50" stroke-width="2" rx="5"/>
                <text x="410" y="110" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Sample</text>

                <!-- Sample marbles -->
                <circle cx="370" cy="140" r="8" fill="#3498db"/>
                <circle cx="390" cy="140" r="8" fill="#e74c3c"/>
                <circle cx="410" cy="140" r="8" fill="#3498db"/>
                <circle cx="430" cy="140" r="8" fill="#3498db"/>
                <circle cx="450" cy="140" r="8" fill="#e74c3c"/>

                <circle cx="380" cy="160" r="8" fill="#3498db"/>
                <circle cx="400" cy="160" r="8" fill="#e74c3c"/>
                <circle cx="420" cy="160" r="8" fill="#3498db"/>
                <circle cx="440" cy="160" r="8" fill="#3498db"/>

                <!-- Parameters -->
                <rect x="500" y="80" width="180" height="180" fill="white" stroke="#8e44ad" stroke-width="2" rx="5"/>
                <text x="590" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Hypergeometric Parameters</text>

                <text x="590" y="125" text-anchor="middle" font-size="11" fill="#2c3e50">b = 6 (blue marbles)</text>
                <text x="590" y="140" text-anchor="middle" font-size="11" fill="#2c3e50">r = 4 (red marbles)</text>
                <text x="590" y="155" text-anchor="middle" font-size="11" fill="#2c3e50">k = 9 (sample size)</text>

                <text x="590" y="180" text-anchor="middle" font-size="11" fill="#2c3e50">X = number of blue in sample</text>
                <text x="590" y="195" text-anchor="middle" font-size="11" fill="#2c3e50">Range: {5, 6}</text>

                <text x="590" y="220" text-anchor="middle" font-size="11" fill="#2c3e50">Mean = k·b/(b+r) = 5.4</text>
                <text x="590" y="235" text-anchor="middle" font-size="11" fill="#2c3e50">Variance = k·(b/(b+r))·(r/(b+r))·((b+r-k)/(b+r-1))</text>

                <!-- Count illustration -->
                <text x="410" y="200" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">X = 6 blue marbles</text>
            </svg>
        </div>

        <div class="warning">
            <p><strong>Key Difference from Binomial:</strong> In hypergeometric distribution, we sample <em>without replacement</em>, so the probability of success changes with each draw. In binomial distribution, we sample <em>with replacement</em> (or from an infinite population), so the probability remains constant.</p>
        </div>

        <h2>⚡ Poisson Distribution</h2>

        <div class="concept-box">
            <p><strong>The Random Experiment:</strong> Count the number of rare events occurring in a fixed interval of time or space.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Counting rare events in continuous time or space"
            </p>
        </div>

        <div class="example-box">
            <p><strong>Real-World Examples:</strong></p>
            <ul>
                <li>Number of customers arriving at a store per hour</li>
                <li>Number of emails received per day</li>
                <li>Number of car accidents at an intersection per month</li>
                <li>Number of radioactive particles detected per minute</li>
                <li>Number of typos per page in a book</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Definition 3.9: Poisson Distribution</h3>
            <p>A random variable $X$ is said to be a <strong>Poisson random variable</strong> with parameter $\lambda$, shown as $X \sim \text{Poisson}(\lambda)$, if its range is $R_X = \{0, 1, 2, 3, \ldots\}$ and its PMF is given by:</p>
            <div class="formula-box">
                $$P_X(k) = \begin{cases}
                \frac{e^{-\lambda}\lambda^k}{k!} & \text{for } k \in R_X \\
                0 & \text{otherwise}
                \end{cases}$$
                <p style="margin-top: 10px;">where $\lambda > 0$</p>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Verification that this is a valid PMF:</strong></p>
            <p>We need to check that $\sum_{k=0}^{\infty} P_X(k) = 1$. Using the Taylor series $e^x = \sum_{k=0}^{\infty} \frac{x^k}{k!}$:</p>
            <div class="formula-box">
                $$\sum_{k=0}^{\infty} \frac{e^{-\lambda}\lambda^k}{k!} = e^{-\lambda} \sum_{k=0}^{\infty} \frac{\lambda^k}{k!} = e^{-\lambda} \cdot e^{\lambda} = 1 \checkmark$$
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                    Poisson Distribution for Different λ Values
                </text>

                <!-- Plot 1: λ = 1 -->
                <text x="130" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">λ = 1</text>
                <line x1="50" y1="350" x2="210" y2="350" stroke="#2c3e50" stroke-width="1"/>
                <line x1="50" y1="350" x2="50" y2="100" stroke="#2c3e50" stroke-width="1"/>

                <!-- Bars for λ = 1 -->
                <rect x="60" y="230" width="12" height="120" fill="#e74c3c" opacity="0.8"/>
                <rect x="75" y="230" width="12" height="120" fill="#e74c3c" opacity="0.8"/>
                <rect x="90" y="280" width="12" height="70" fill="#e74c3c" opacity="0.8"/>
                <rect x="105" y="315" width="12" height="35" fill="#e74c3c" opacity="0.8"/>
                <rect x="120" y="335" width="12" height="15" fill="#e74c3c" opacity="0.8"/>
                <rect x="135" y="345" width="12" height="5" fill="#e74c3c" opacity="0.8"/>

                <text x="66" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">0</text>
                <text x="81" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">1</text>
                <text x="96" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">2</text>
                <text x="111" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">3</text>
                <text x="126" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">4</text>
                <text x="141" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">5</text>

                <!-- Plot 2: λ = 5 -->
                <text x="400" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">λ = 5</text>
                <line x1="300" y1="350" x2="500" y2="350" stroke="#2c3e50" stroke-width="1"/>
                <line x1="300" y1="350" x2="300" y2="100" stroke="#2c3e50" stroke-width="1"/>

                <!-- Bars for λ = 5 (bell-shaped, centered around 5) -->
                <rect x="310" y="345" width="12" height="5" fill="#3498db" opacity="0.8"/>
                <rect x="325" y="335" width="12" height="15" fill="#3498db" opacity="0.8"/>
                <rect x="340" y="315" width="12" height="35" fill="#3498db" opacity="0.8"/>
                <rect x="355" y="280" width="12" height="70" fill="#3498db" opacity="0.8"/>
                <rect x="370" y="230" width="12" height="120" fill="#3498db" opacity="0.8"/>
                <rect x="385" y="200" width="12" height="150" fill="#3498db" opacity="0.8"/>
                <rect x="400" y="230" width="12" height="120" fill="#3498db" opacity="0.8"/>
                <rect x="415" y="280" width="12" height="70" fill="#3498db" opacity="0.8"/>
                <rect x="430" y="315" width="12" height="35" fill="#3498db" opacity="0.8"/>
                <rect x="445" y="335" width="12" height="15" fill="#3498db" opacity="0.8"/>
                <rect x="460" y="345" width="12" height="5" fill="#3498db" opacity="0.8"/>

                <text x="316" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">0</text>
                <text x="346" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">2</text>
                <text x="376" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">4</text>
                <text x="406" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">6</text>
                <text x="436" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">8</text>
                <text x="466" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">10</text>

                <!-- Plot 3: λ = 10 -->
                <text x="650" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">λ = 10</text>
                <line x1="550" y1="350" x2="750" y2="350" stroke="#2c3e50" stroke-width="1"/>
                <line x1="550" y1="350" x2="550" y2="100" stroke="#2c3e50" stroke-width="1"/>

                <!-- Bars for λ = 10 (more symmetric, centered around 10) -->
                <rect x="560" y="340" width="10" height="10" fill="#2ecc71" opacity="0.8"/>
                <rect x="575" y="330" width="10" height="20" fill="#2ecc71" opacity="0.8"/>
                <rect x="590" y="310" width="10" height="40" fill="#2ecc71" opacity="0.8"/>
                <rect x="605" y="280" width="10" height="70" fill="#2ecc71" opacity="0.8"/>
                <rect x="620" y="240" width="10" height="110" fill="#2ecc71" opacity="0.8"/>
                <rect x="635" y="200" width="10" height="150" fill="#2ecc71" opacity="0.8"/>
                <rect x="650" y="180" width="10" height="170" fill="#2ecc71" opacity="0.8"/>
                <rect x="665" y="200" width="10" height="150" fill="#2ecc71" opacity="0.8"/>
                <rect x="680" y="240" width="10" height="110" fill="#2ecc71" opacity="0.8"/>
                <rect x="695" y="280" width="10" height="70" fill="#2ecc71" opacity="0.8"/>
                <rect x="710" y="310" width="10" height="40" fill="#2ecc71" opacity="0.8"/>
                <rect x="725" y="330" width="10" height="20" fill="#2ecc71" opacity="0.8"/>

                <text x="565" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">5</text>
                <text x="595" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">7</text>
                <text x="625" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">9</text>
                <text x="655" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">11</text>
                <text x="685" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">13</text>
                <text x="715" y="370" text-anchor="middle" font-size="9" fill="#2c3e50">15</text>

                <!-- Properties -->
                <rect x="50" y="80" width="150" height="50" fill="white" stroke="#e74c3c" stroke-width="1" rx="3"/>
                <text x="125" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">Mean = Var = 1</text>
                <text x="125" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">Highly skewed</text>

                <rect x="325" y="80" width="150" height="50" fill="white" stroke="#3498db" stroke-width="1" rx="3"/>
                <text x="400" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">Mean = Var = 5</text>
                <text x="400" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">Moderately skewed</text>

                <rect x="600" y="80" width="150" height="50" fill="white" stroke="#2ecc71" stroke-width="1" rx="3"/>
                <text x="675" y="100" text-anchor="middle" font-size="10" fill="#2c3e50">Mean = Var = 10</text>
                <text x="675" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">Nearly symmetric</text>
            </svg>
        </div>

        <h3><span class="step-number">1</span>Poisson as Approximation to Binomial</h3>

        <div class="concept-box">
            <p><strong>Theorem 3.1:</strong> The Poisson distribution can be viewed as the limit of a binomial distribution when $n \to \infty$, $p \to 0$, but $np = \lambda$ remains constant.</p>
            <div class="formula-box">
                $$\text{If } X \sim \text{Binomial}(n, p = \frac{\lambda}{n}), \text{ then } \lim_{n \to \infty} P_X(k) = \frac{e^{-\lambda}\lambda^k}{k!}$$
            </div>
        </div>

        <div class="example-box">
            <h4>Example 3.8: Email Arrivals</h4>
            <p><strong>Problem:</strong> The number of emails I receive follows a Poisson distribution with an average of 0.2 emails per minute.</p>
            <ol>
                <li>What is the probability that I get no emails in 5 minutes?</li>
                <li>What is the probability that I get more than 3 emails in 10 minutes?</li>
            </ol>

            <p><strong>Solution:</strong></p>
            <p><strong>Part 1:</strong> In 5 minutes, $\lambda = 5 \times 0.2 = 1$. So $X \sim \text{Poisson}(1)$.</p>
            <div class="formula-box">
                $$P(X = 0) = \frac{e^{-1} \cdot 1^0}{0!} = e^{-1} \approx 0.3679$$
            </div>

            <p><strong>Part 2:</strong> In 10 minutes, $\lambda = 10 \times 0.2 = 2$. So $Y \sim \text{Poisson}(2)$.</p>
            <div class="formula-box">
                $$P(Y > 3) = 1 - P(Y \leq 3) = 1 - \left(\frac{e^{-2} \cdot 2^0}{0!} + \frac{e^{-2} \cdot 2^1}{1!} + \frac{e^{-2} \cdot 2^2}{2!} + \frac{e^{-2} \cdot 2^3}{3!}\right)$$
                $$= 1 - e^{-2}(1 + 2 + 2 + \frac{8}{6}) = 1 - \frac{19e^{-2}}{3} \approx 0.1429$$
            </div>
        </div>

        <h2>📊 Distribution Comparison Summary</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Distribution</th>
                    <th>Random Experiment</th>
                    <th>Parameters</th>
                    <th>Range</th>
                    <th>PMF</th>
                    <th>Mean</th>
                    <th>Variance</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Bernoulli(p)</strong></td>
                    <td>Single trial, success/failure</td>
                    <td>p (success probability)</td>
                    <td>{0, 1}</td>
                    <td>$p^x(1-p)^{1-x}$</td>
                    <td>p</td>
                    <td>p(1-p)</td>
                </tr>
                <tr>
                    <td><strong>Geometric(p)</strong></td>
                    <td>Trials until first success</td>
                    <td>p (success probability)</td>
                    <td>{1, 2, 3, ...}</td>
                    <td>$p(1-p)^{k-1}$</td>
                    <td>1/p</td>
                    <td>(1-p)/p²</td>
                </tr>
                <tr>
                    <td><strong>Binomial(n,p)</strong></td>
                    <td>Successes in n fixed trials</td>
                    <td>n (trials), p (success prob.)</td>
                    <td>{0, 1, ..., n}</td>
                    <td>$\binom{n}{k}p^k(1-p)^{n-k}$</td>
                    <td>np</td>
                    <td>np(1-p)</td>
                </tr>
                <tr>
                    <td><strong>Pascal(m,p)</strong></td>
                    <td>Trials until m-th success</td>
                    <td>m (successes), p (success prob.)</td>
                    <td>{m, m+1, ...}</td>
                    <td>$\binom{k-1}{m-1}p^m(1-p)^{k-m}$</td>
                    <td>m/p</td>
                    <td>m(1-p)/p²</td>
                </tr>
                <tr>
                    <td><strong>Hypergeometric(b,r,k)</strong></td>
                    <td>Sampling without replacement</td>
                    <td>b (blue), r (red), k (sample size)</td>
                    <td>{max(0,k-r), ..., min(k,b)}</td>
                    <td>$\frac{\binom{b}{x}\binom{r}{k-x}}{\binom{b+r}{k}}$</td>
                    <td>k·b/(b+r)</td>
                    <td>Complex formula</td>
                </tr>
                <tr>
                    <td><strong>Poisson(λ)</strong></td>
                    <td>Rare events in time/space</td>
                    <td>λ (rate parameter)</td>
                    <td>{0, 1, 2, ...}</td>
                    <td>$\frac{e^{-\lambda}\lambda^k}{k!}$</td>
                    <td>λ</td>
                    <td>λ</td>
                </tr>
            </tbody>
        </table>

        <h2>🎓 Key Learning Points</h2>

        <div class="distribution-grid">
            <div class="distribution-card">
                <div class="distribution-title">🎯 Focus on Understanding</div>
                <p>Don't memorize formulas! Understand the random experiment behind each distribution. The PMF follows naturally from the experiment setup.</p>
            </div>

            <div class="distribution-card">
                <div class="distribution-title">🔗 Relationships Matter</div>
                <p>Many distributions are related:
                <ul>
                    <li>Geometric is Pascal with m=1</li>
                    <li>Binomial is sum of Bernoulli</li>
                    <li>Poisson approximates Binomial</li>
                </ul>
                </p>
            </div>

            <div class="distribution-card">
                <div class="distribution-title">📈 Parameters Tell the Story</div>
                <p>Each parameter has meaning:
                <ul>
                    <li>p = probability of success</li>
                    <li>n = number of trials</li>
                    <li>λ = rate of occurrence</li>
                </ul>
                </p>
            </div>

            <div class="distribution-card">
                <div class="distribution-title">🎲 Practice with Examples</div>
                <p>The best way to master these distributions is through solving problems. Each distribution models specific real-world scenarios.</p>
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="250" viewBox="0 0 800 250">
                <!-- Background -->
                <rect width="800" height="250" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Distribution Selection Guide
                </text>

                <!-- Decision tree -->
                <rect x="350" y="50" width="100" height="40" fill="#3498db" rx="5"/>
                <text x="400" y="75" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Start Here</text>

                <!-- First level questions -->
                <rect x="150" y="120" width="120" height="30" fill="#e74c3c" rx="3"/>
                <text x="210" y="140" text-anchor="middle" font-size="10" fill="white">Fixed # of trials?</text>

                <rect x="530" y="120" width="120" height="30" fill="#e74c3c" rx="3"/>
                <text x="590" y="140" text-anchor="middle" font-size="10" fill="white">Counting events?</text>

                <!-- Second level -->
                <rect x="50" y="180" width="80" height="25" fill="#2ecc71" rx="3"/>
                <text x="90" y="197" text-anchor="middle" font-size="9" fill="white">Binomial</text>

                <rect x="150" y="180" width="80" height="25" fill="#2ecc71" rx="3"/>
                <text x="190" y="197" text-anchor="middle" font-size="9" fill="white">Hypergeometric</text>

                <rect x="250" y="180" width="80" height="25" fill="#2ecc71" rx="3"/>
                <text x="290" y="197" text-anchor="middle" font-size="9" fill="white">Geometric</text>

                <rect x="350" y="180" width="80" height="25" fill="#2ecc71" rx="3"/>
                <text x="390" y="197" text-anchor="middle" font-size="9" fill="white">Pascal</text>

                <rect x="470" y="180" width="80" height="25" fill="#2ecc71" rx="3"/>
                <text x="510" y="197" text-anchor="middle" font-size="9" fill="white">Poisson</text>

                <rect x="570" y="180" width="80" height="25" fill="#2ecc71" rx="3"/>
                <text x="610" y="197" text-anchor="middle" font-size="9" fill="white">Bernoulli</text>

                <!-- Arrows -->
                <line x1="380" y1="90" x2="230" y2="120" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="420" y1="90" x2="570" y2="120" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                <line x1="180" y1="150" x2="90" y2="180" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="200" y1="150" x2="190" y2="180" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="220" y1="150" x2="290" y2="180" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="240" y1="150" x2="390" y2="180" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>

                <line x1="570" y1="150" x2="510" y2="180" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="610" y1="150" x2="610" y2="180" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>

                <!-- Labels -->
                <text x="90" y="220" text-anchor="middle" font-size="8" fill="#2c3e50">With replacement</text>
                <text x="190" y="220" text-anchor="middle" font-size="8" fill="#2c3e50">Without replacement</text>
                <text x="290" y="220" text-anchor="middle" font-size="8" fill="#2c3e50">Until 1st success</text>
                <text x="390" y="220" text-anchor="middle" font-size="8" fill="#2c3e50">Until m-th success</text>
                <text x="510" y="220" text-anchor="middle" font-size="8" fill="#2c3e50">Rare events</text>
                <text x="610" y="220" text-anchor="middle" font-size="8" fill="#2c3e50">Single trial</text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Final Advice:</strong> These special distributions are powerful tools for modeling real-world phenomena. The key to mastering them is understanding when and why to use each one. Practice identifying the underlying random experiment in word problems, and the appropriate distribution will become clear!</p>
        </div>

    </div>
</body>
</html>
