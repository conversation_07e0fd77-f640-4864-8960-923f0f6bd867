5.2.3 Conditioning and Independence
 Here, we will discuss conditioning for continuous random variables. In particular, we
 will discuss the conditional PDF, conditional CDF, and conditional expectation. We
 have discussed conditional probability for discrete random variables before. The ideas
 behind conditional probability for continuous random variables are very similar to the
 discrete case. The difference lies in the fact that we need to work with probability
 density in the case of continuous random variables. Nevertheless, we would like to
 emphasize again that there is only one main formula regarding conditional probability
 which is
 P(A∩B)
 P(B)
 P(A|B) = , when P(B)>0.
 Any other formula regarding conditional probability can be derived from the above
 formula. In fact, for some problems we only need to apply the above formula. You have
 already used this in Example 5.17. As another example, if you have two random
 variables 
X
 and 
Y
 , you can write
 P(X ∈ C|Y ∈ D)=
 P(X ∈ C,Y ∈D)
 P(Y ∈ D)
 , where C,D ⊂ R.
 However, sometimes we need to use the concepts of conditional PDFs and CDFs. The
 formulas for conditional PDFs and CDFs of continuous random variables are very
 similar to those of discrete random variables. Since there are no new fundamental
 ideas in this section, we usually provide the main formulas and guidelines, and then
 work on examples. Specifically, we do not spend much time deriving formulas.
 Nevertheless, to give you the basic idea of how to derive these formulas, we start by
 deriving a formula for the conditional CDF and PDF of a random variable 
X
 given that 
X∈I =[a,b]
 . Consider a continuous random variable 
X
 . Suppose that we know that
 the event 
X ∈ I = [a,b]
 has occurred. Call this event 
A
 . The conditional CDF of 
X
 given 
A
 , denoted by 
FX|A(x)
 or 
FX|a≤X≤b(x)
 , is
 FX|A(x) = P(X ≤x|A)
 =P(X≤x|a≤X≤b)
 =
 P(X ≤x,a≤X≤b)
 P(A)
 .
Now if 
x<a
 , then 
FX|A(x)=0
 . On the other hand, if 
a≤x≤b
 , we have
 FX|A(x)=
 =
 = .
 Finally, if 
x>b
 , then 
FX|A(x)=1
 . Thus, we obtain
 FX|A(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 1 x>b
 a≤x<b
 0 otherwise
 Note that since 
X
 is a continuous random variable, we do not need to be careful about
 end points, i.e., changing 
x>b
 to 
x≥b
 does not make a difference in the above
 formula. To obtain the conditional PDF of 
X
 , denoted by 
fX|A(x)
 , we can differentiate 
FX|A(x)
 . We obtain
 fX|A(x)=
 ⎧ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎩
 a≤x<b
 0 otherwise
 It is insightful if we derive the above formula for 
fX|A(x)
 directly from the definition of
 the PDF for continuous random variables. Recall that the PDF of 
X
 can be defined as
 fX(x)= limΔ→0+ .
 Now, the conditional PDF of 
X
 given 
A
 , denoted by 
fX|A(x)
 , is
 fX|A(x)= limΔ→0+
 = limΔ→0+
 = limΔ→0+ .
 Now consider two cases. If 
a≤x<b
 , then
 P(X≤x,a≤X≤b)
 P(A)
 P(a≤X≤x)
 P(A)
 FX(x)−FX(a)
 FX(b)−FX(a)
 FX(x)−FX(a)
 FX(b)−FX(a)
 fX(x)
 P(A)
 P(x<X≤x+Δ)
 Δ
 P(x<X≤x+Δ|A)
 Δ
 P(x<X≤x+Δ,A)
 ΔP(A)
 P(x<X≤x+Δ,a≤X≤b)
 ΔP(A)
fX|A(x) = lim
 Δ→0+
 1
 P(A)
 P(x <X≤x+Δ,a≤X≤b)
 ΔP(A)
 = limΔ→0+
 fX(x)
 P(A)
 = .
 On the other hand, if 
x < a
 or 
x ≥ b
 , then
 P(x <X≤x+Δ)
 Δ
 fX|A(x) = lim
 Δ→0+
 =0.
 P(x <X≤x+Δ,a≤X≤b)
 ΔP(A)
 If 
X
 is a continuous random variable, and 
A
 is the event that 
a <X<b
 (where possibly 
b = ∞
 or 
a = −∞
 ), then
 1
 ⎧
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
FX|A(x) =
 ⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪⎩
 x >b
 FX(x)−FX(a)
 FX(b)−FX(a)
 a ≤x<b
 0
 ⎧
 ⎪ 
⎪ 
fX|A(x) =
 ⎪
 ⎨
 ⎪ 
⎪ 
⎩
 ⎪
 x <a
 fX(x)
 P(A)
 0
 a ≤x<b
 otherwise
 The conditional expectation and variance are defined by replacing the PDF by
 conditional PDF in the definitions of expectation and variance. In general, for a random
 variable 
X
 and an event 
A
 , we have the following:
E[X|A]=∫
 ∞
 −∞
 xfX|A(x)dx,
 E[g(X)|A]=∫
 ∞
 −∞
 g(x)fX|A(x)dx,
 Var(X|A)=E[X2|A]−(E[X|A])2
 
Example 5.20
 Let 
X∼Exponential(1)
 .
 a. Find the conditional PDF and CDF of 
X
 given 
X>1
 .
 b. Find 
E[X|X>1]
 .
 c. Find Var
 (X|X>1)
 .
 Solution
 a. Let 
A
 be the event that 
X>1
 . Then
 P(A)=∫
 ∞
 1
 e−x
 dx
 = .
 Thus,
 fX|X>1(x)=⎧ ⎪ ⎨ ⎪ ⎩
 e−x+1 x>1
 0 otherwise
 For 
x>1
 , we have
 FX|A(A)=
 =1−e−x+1.
 Thus,
 FX|A(x)=⎧ ⎪ ⎨ ⎪ ⎩
 1−e−x+1 x>1
 0 otherwise
 1
 e
 FX(x)−FX(1)
 P(A)
b. We have
 ∫ 
∞
 E[X|X >1]=
 =
 =e
 =e
 =e
 =2.
 c. We have
 E[X2|X >1] =∫ 
∞
 1
 =
 ∫ 
∞
 1
 =e
 =e
 ∫ 
∞
 1
 xfX|X>1(x)dx
 1
 ∫ 
∞
 1
 xe−x+1dx
 ∫ 
∞
 1
 xe−x
 dx
 [ −e−x−xe−x
 ]
 ∞
 1
 2
 e
 x
 2
 x
 2
 fX|X>1(x)dx
 e−x+1dx
 x
 2
 e−x
 dx
 [ −2e−x −2xe−x−x2e−x
 ]
 ∞
 1
 =e
 5
 e
 =5.
 Thus,
 Var(X|X > 1) =E[X2|X >1]−(E[X|X >1])2
 =5−4=1.
 Conditioning by Another Random Variable:
 If 
X
 and 
Y
 are two jointly continuous random variables, and we obtain some
 information regarding 
Y
 , we should update the PDF and CDF of 
X
 based on the new
 information. In particular, if we get to observe the value of the random variable 
Y
 , then
 how do we need to update the PDF and CDF of 
X
 ? Remember for the discrete case,
 the conditional PMF of 
X
 given 
Y = y
 is given by
.
 PX|Y (xi|yj) =
 PXY(xi,yj)
 PY(yj)
 Now, if 
X
 and 
Y
 are jointly continuous, the conditional PDF of 
X
 given 
Y
 is given by
 fX|Y (x|y) = .
 fXY (x,y)
 fY (y)
 This means that if we get to observe 
Y = y
 , then we need to use the above conditional
 density for the random variable 
X
 . To get an intuition about the formula, note that by
 definition, for small 
Δx
 and 
Δy
 we should have
 fX|Y (x|y) ≈
 =
 ≈
 P(x ≤X≤x+Δx|y≤Y ≤y+Δy)
 Δx
 P(x ≤X≤x+Δx,y≤Y ≤y+Δy)
 P(y ≤Y ≤y+Δy)Δx
 fXY (x,y)ΔxΔy
 fY (y)ΔyΔx
 fXY (x,y)
 fY (y)
 = .
 (definition of PDF)
 Similarly, we can write the conditional PDF of 
Y
 , given 
X = x
 , as
 fY|X(y|x) = .
 fXY (x,y)
 fX(x)
For two jointly continuous random variables 
X
 and 
Y
 , we can
 define the following conditional concepts:
 1. The conditional PDF of 
X
 given 
Y=y
 :
 fX|Y(x|y)=
 2. The conditional probability that 
X∈A
 given 
Y=y
 :
 P(X∈A|Y=y)=∫A
 fX|Y(x|y)dx
 3. The conditional CDF of 
X
 given 
Y=y
 :
 FX|Y(x|y)=P(X≤x|Y=y)=∫
 x
 −∞
 fX|Y(x|y)dx
 Example 5.21 Let 
X
 and 
Y
 be two jointly continuous random variables with joint PDF
 fXY(x,y)=
 ⎧ ⎪ ⎪ ⎨ ⎪ ⎪⎩
 + + 0≤x≤1,0≤y≤2
 0 otherwise
 For 
0≤y≤2
 , find
 a. the conditional PDF of 
X
 given 
Y=y
 ;
 b. 
P(X< |Y=y)
 .
 Solution
 a. Let us first find the marginal PDF of 
Y
 . We have
 fY(y)=∫
 1
 0
 + + dx
 = , for 0≤y≤2.
 Thus, for 
0≤y≤2
 , we obtain
 fXY(x,y)
 fY(y)
 x2
 4
 y2
 4
 xy
 6
 1
 2
 x2
 4
 y2
 4
 xy
 6
 3y2+y+1
 12
fX|Y (x|y) =
 fXY (x,y)
 fY (y)
 3x2 +3y2 +2xy
 3y2 +y+1
 = , for 0≤x≤1.
 Thus, for 
0 ≤ y ≤ 2
 , we have
 fX|Y (x|y) =
 b. We have
 P
 1
 2
 ⎧
 ⎪ 
⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪⎩
 3x2+3y2+2xy
 3y2+y+1
 0
 2
 0 ≤x≤1
 otherwise
 1
 (X< |Y =y
 )=∫0
 dx
 =
 3x2 +3y2 +2xy
 3y2 +y+1
 1
 3y2 +y+1
 1
 2
 [ x3 +yx2 +3y2x
 ]0
 3
 2
 y
 y2 + +
 4
 1
 8
 = .
 3y2 +y+1
 1
 Note that, as we expect, 
P (X < |Y = y )
 depends on 
y
 .
 2
 Conditional expectation and variance are similarly defined. Given 
Y = y
 , we need to
 replace 
fX(x)
 by 
fX|Y (x|y)
 in the formulas for expectation:
For two jointly continuous random variables 
X
 and 
Y
 , we have:
 1. Expected value of 
X
 given 
Y=y
 :
 E[X|Y=y]=∫
 ∞
 −∞
 xfX|Y(x|y)dx
 2. Conditional LOTUS:
 E[g(X)|Y=y]=∫
 ∞
 −∞
 g(x)fX|Y(x|y)dx
 3. Conditional variance of 
X
 given 
Y=y
 :
 Var(X|Y=y)=E[X2|Y=y]−(E[X|Y=y])2
 Example 5.22
 Let 
X
 and 
Y
 be as in Example 5.21. Find 
E[X|Y=1]
 and Var
 (X|Y=1)
 .
 Solution
 E[X|Y=1]=∫
 ∞
 −∞
 xfX|Y(x|1)dx
 =∫
 1
 0
 x |y=1 dx
 =∫
 1
 0
 x dx (y=1)
 = ∫
 1
 0
 3x3+2x2+3xdx
 = ,
 E[X2|Y=1]=∫
 ∞
 −∞
 x2fX|Y(x|1)dx
 = ∫
 1
 0
 3x4+2x3+3x2 dx
 = .
 So we have
 3x2+3y2+2xy
 3y2+y+1
 3x2+3+2x
 3+1+1
 1
 5
 7
 12
 1
 5
 21
 50
Var(X|Y=1)=E[X2|Y=1]−(E[X|Y=1])2
 = −( )
 2
 = .
 
Independent Random Variables:
 When two jointly continuous random variables are independent, we must have
 fX|Y(x|y)=fX(x).
 That is, knowing the value of 
Y
 does not change the PDF of 
X
 . Since 
fX|Y(x|y)=
 , we conclude that for two independent continuous random
 variables we must have
 fXY(x,y)=fX(x)fY(y).
 Two continuous random variables 
X
 and 
Y
 are independent if
 fXY(x,y)=fX(x)fY(y), for all x,y.
 Equivalently, 
X
 and 
Y
 are independent if
 FXY(x,y)=FX(x)FY(y), for all x,y.
 If 
X
 and 
Y
 are independent, we have
 E[XY]=EXEY,
 E[g(X)h(Y)]=E[g(X)]E[h(Y)].
 
Suppose that we are given the joint PDF 
fXY(x,y)
 of two random variables 
X
 and 
Y
 . If
 we can write
 fXY(x,y)=f1(x)f2(y),
 then 
X
 and 
Y
 are independent.
 21
 50
 7
 12
 287
 3600
 fXY(x,y)
 fY(y)
Example 5.23
 
Determine whether 
X
 and 
Y
 are independent:
 a. 
fXY (x,y) = ⎧
 ⎪
 ⎨
 ⎩
 ⎪
 b. 
fXY (x,y) = ⎧
 ⎪
 ⎨
 ⎩
 ⎪
 Solution
 a. We can write
 2e−x−2y
 0
 8xy
 0
 x,y >0
 otherwise
 0 <x<y<1
 otherwise
 fXY (x,y) = [e−x
 u(x)][2e−2y
 u(y)],
 where 
u(x)
 is the unit step function:
 u(x) =
 {1 x≥1
 0
 otherwise
 Thus, we conclude that 
X
 and 
Y
 are independent.
 b. For this case, it does not seem that we can write 
fXY (x,y)
 as a product of some 
f1(x)
 and 
f2(y)
 . Note that the given region 
0 < x < y < 1
 enforces that 
x < y
 .
 That is, we always have 
X < Y
 . Thus, we conclude that 
X
 and 
Y
 are not
 independent. To show this, we can obtain the marginal PDFs of 
X
 and 
Y
 and
 show that 
fXY (x,y) ≠ fX(x)fY (y), for some x,y
 . We have, for 
0 ≤ x ≤ 1
 ,
 1
 fX(x) = ∫
 x
 8xydy
 =4x(1−x2).
 Thus,
 Similarly, we obtain
 ⎧
 fX(x) =
 ⎪
 ⎨
 ⎩
 ⎪
 4x(1 −x2)
 0
 ⎧
 fY (y) =
 ⎪
 ⎨
 ⎩
 ⎪
 4y3
 0
 0 <x<1
 otherwise
 0 <y<1
 otherwise
As we see, 
fXY (x,y) ≠ fX(x)fY (y)
 , thus 
X
 and 
Y
 are NOT independent.
 Example 5.24
 
Consider the unit disc
 D={(x,y)|x2 +y2 ≤ 1}.
 Suppose that we choose a point 
(X,Y)
 uniformly at random in 
D
 . That is, the joint
 PDF of 
X
 and 
Y
 is given by
 ⎧
 fXY (x,y) =
 ⎪
 ⎨
 ⎩
 ⎪
 c
 0
 (x,y) ∈ D
 otherwise
 a. Find the constant 
c
 .
 b. Find the marginal PDFs 
fX(x)
 and 
fY (y)
 .
 c. Find the conditional PDF of 
X
 given 
Y = y
 , where 
−1 ≤ y ≤ 1
 .
 d. Are 
X
 and 
Y
 independent?
 Solution
 a. We have
 Thus, 
c = 
.
 b. For 
−1 ≤ x ≤ 1
 , we have
 1 =
 =
 ∫ 
∞
 −∞
 ∬
 fXY (x,y)dxdy
 ∫ 
∞
 −∞
 c dxdy
 D
 =c(area of D)
 =c(π).
 1
 π
 fX(x) =
 =
 ∫ 
∞
 −∞
 ∫ 
√
 −√
 2
 π
 fXY (x,y)dy
 1−x2
 1−x2
 1
 π
 1 −x2.
 dy
 = √
Thus,
 fX(x)=⎧ ⎪ ⎨ ⎪⎩
 √1−x2 −1≤x≤1
 0 otherwise
 Similarly,
 fY(y)=⎧ ⎪ ⎨ ⎪⎩
 √1−y2 −1≤y≤1
 0 otherwise
 c. We have
 fX|Y(x|y)=
 =
 ⎧ ⎪ ⎪ ⎨ ⎪ ⎪ ⎩
 −√1−y2≤x≤√1−y2
 0 otherwise
 Note that the above equation indicates that, given 
Y=y
 , 
X
 is uniformly
 distributed on 
[−√1−y2,√1−y2]
 . We write
 X|Y=y ∼ Uniform(−√1−y2,√1−y2).
 d. Are 
X
 and 
Y
 independent? No, because 
fXY(x,y)≠fX(x)fY(y)
 .
 
Law of Total Probability:
 Now, we'll discuss the law of total probability for continuous random variables. This is
 completely analogous to the discrete case. In particular, the law of total probability, the
 law of total expectation (law of iterated expectations), and the law of total variance can
 be stated as follows:
 2
 π
 2
 π
 fXY(x,y)
 fY(y)
 1
 2√1−y2
Law of Total Probability:
 P(A)=∫
 ∞
 −∞
 P(A|X=x)fX(x) dx (5.16)
 
Law of Total Expectation:
 E[Y]=∫
 ∞
 −∞
 E[Y|X=x]fX(x) dx (5.17)
 =E[E[Y|X]]
 
Law of Total Variance:
 Var(Y)=E[Var(Y|X)]+Var(E[Y|X]) (5.18)
 Let's look at some examples.
 Example 5.25
 Let 
X
 and 
Y
 be two independent 
Uniform(0,1)
 random variables. Find 
P(X3+Y>1) .
 Solution
 Using the law of total probability (Equation 5.16), we can write
P(X3 +Y >1)=∫ 
∞
 P(X3 +Y >1|X=x)fX(x) dx
 −∞
 =
 =
 =
 ∫ 
1
 0 
P(x3 +Y >1|X =x) dx
 ∫ 
1
 0 
P(Y >1−x3) dx
 ∫ 
1
 0 
x3 dx
 1
 = .
 4
 (since X and Y are independent)
 (since Y ∼ Uniform(0,1)) 
Example 5.26
 
Suppose 
X ∼ Uniform(1,2)
 and given 
X = x
 , 
Y
 is an exponential random variable
 with parameter 
λ = x
 , so we can write
 Y|X =x ∼ Exponential(x).
 We sometimes write this as
 a. Find 
EY
 .
 b. Find 
Var(Y)
 .
 Solution
 Y|X ∼ Exponential(X).
 a. We use the law of total expectation (Equation 5.17) to find 
EY
 . Remember that if
 Y ∼Exponential(λ)
 , then 
EY = 
. Thus we conclude
 1
 λ
 1
 E[Y|X =x]= .
 x
 Using the law of total expectation, we have
 EY =
 =
 =
 ∫ 
∞
 −∞
 E[Y|X =x]fX(x)dx
 ∫ 
2
 1 
E[Y|X =x]⋅1dx
 1
 ∫ 
2
 1 
dx
 =ln2.
 x
Another way to write the above calculation is
 EY =E[E[Y|X]]
 =E
 1
 X
 (law of total expectation)
 [ ] (since E[Y|X]= )
 1
 X
 =
 1
 x
 ∫ 
2
 1 
dx
 =ln2.
 b. To find 
V ar(Y)
 , we can write
 Var(Y) = E[Y 2]−(E[Y])2
 =E[Y 2]−(ln2)2
 =E[E[Y 2|X]] −(ln2)2
 =E
 =
 [ ] −(ln2)2
 2
 X2
 2
 x2
 (law of total expectation)
 (since Y|X ∼ Exponential(X))
 ∫ 
2
 1
 dx−(ln2)2
 =1−(ln2)2.
 Another way to find 
Var(Y)
 is to apply the law of total variance:
 Var(Y) = E[Var(Y|X)]+Var(E[Y|X]).
 Since 
Y|X ∼ Exponential(X)
 , we conclude
 1
 E[Y|X] = ,
 X
 Var(Y|X) = .
 1
 X2
 Therefore
 Var(Y) = E
 1
 X2
 [ ] +Var ( )
 1
 X
 =E
 =E
 1
 X2
 [ ] +E
 [ ] −(E
 [ ])
 2
 2
 X2
 1
 X2
 [ ] −(ln2)2
 =1−(ln2)2