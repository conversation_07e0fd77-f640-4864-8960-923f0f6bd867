<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistical Association Tests in Trajectory Analysis</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #4299e1;
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .method-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .method-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .method-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .method-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Statistical Association Tests in Trajectory Analysis</h1>
            <div class="subtitle">Understanding Wald Tests for Single-Cell RNA Sequencing Data</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Introduction to Association Testing</strong> - What are we testing and why?</li>
                <li><strong>Core Statistical Concepts</strong> - Wald tests, contrast matrices, and thresholds</li>
                <li><strong>Test Types</strong> - Global vs. lineage-specific approaches</li>
                <li><strong>Mathematical Framework</strong> - The statistical theory behind the tests</li>
                <li><strong>Implementation Details</strong> - How the R code works</li>
                <li><strong>Practical Applications</strong> - When and how to use these tests</li>
            </ul>
        </div>

        <h2>🎯 Introduction to Association Testing</h2>
        
        <div class="concept-box">
            <p><strong>Association testing</strong> in trajectory analysis addresses a fundamental question in single-cell biology:</p>
            <p style="text-align: center; font-size: 1.2em; color: #2c3e50; font-style: italic;">
                "Does gene expression change significantly along a developmental trajectory?"
            </p>
            <p>This R code implements sophisticated statistical tests to answer this question using <strong>Wald tests</strong> with customizable contrast matrices.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="300" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Association Testing Workflow
                </text>
                
                <!-- Trajectory curve -->
                <path d="M 50 200 Q 200 100 350 150 Q 500 200 750 120" 
                      stroke="#3498db" stroke-width="4" fill="none"/>
                
                <!-- Cells along trajectory -->
                <circle cx="80" cy="190" r="8" fill="#e74c3c" opacity="0.7"/>
                <circle cx="150" cy="140" r="8" fill="#f39c12" opacity="0.7"/>
                <circle cx="250" cy="120" r="8" fill="#f1c40f" opacity="0.7"/>
                <circle cx="350" cy="150" r="8" fill="#2ecc71" opacity="0.7"/>
                <circle cx="450" cy="180" r="8" fill="#3498db" opacity="0.7"/>
                <circle cx="550" cy="160" r="8" fill="#9b59b6" opacity="0.7"/>
                <circle cx="650" cy="140" r="8" fill="#e67e22" opacity="0.7"/>
                <circle cx="720" cy="125" r="8" fill="#95a5a6" opacity="0.7"/>
                
                <!-- Labels -->
                <text x="80" y="220" text-anchor="middle" font-size="12" fill="#2c3e50">Start</text>
                <text x="720" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">End</text>
                
                <!-- Gene expression indicator -->
                <text x="400" y="260" text-anchor="middle" font-size="14" fill="#34495e">
                    Gene Expression Changes Along Pseudotime
                </text>
                
                <!-- Arrow -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                <line x1="100" y1="240" x2="700" y2="240" stroke="#2c3e50" 
                      stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="400" y="255" text-anchor="middle" font-size="12" fill="#2c3e50">Pseudotime</text>
            </svg>
        </div>

        <h2>🔬 Core Statistical Concepts</h2>
        
        <h3><span class="step-number">1</span>The Wald Test</h3>
        
        <div class="concept-box">
            <p>The <strong>Wald test</strong> is the cornerstone of association testing. It tests whether a linear combination of parameters significantly differs from zero (or a specified threshold).</p>
        </div>

        <div class="formula-box">
            <p><strong>Wald Test Statistic:</strong></p>
            $$W = (L\beta - \theta)^T [L \Sigma L^T]^{-1} (L\beta - \theta)$$
            <p style="margin-top: 15px; font-size: 0.9em;">
                Where: $L$ = contrast matrix, $\beta$ = parameter estimates, $\Sigma$ = covariance matrix, $\theta$ = threshold
            </p>
        </div>

        <div class="highlight">
            <p><strong>Key Insight:</strong> Under the null hypothesis, $W$ follows a chi-squared distribution with degrees of freedom equal to the rank of $L$.</p>
        </div>

        <h3><span class="step-number">2</span>Contrast Matrices</h3>

        <div class="concept-box">
            <p>Contrast matrices ($L$) define <em>what</em> we're testing. They specify linear combinations of model parameters that represent biologically meaningful comparisons.</p>
        </div>

        <div class="method-comparison">
            <div class="method-card">
                <div class="method-title">🎯 "Start" Contrasts</div>
                <p>Compare each point along the trajectory to the starting point:</p>
                <div class="formula-box">
                    $$L_{start} = X_{point} - X_{start}$$
                </div>
                <p><strong>Use case:</strong> Detecting genes that change from the beginning of development.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🏁 "End" Contrasts</div>
                <p>Compare each point along the trajectory to the endpoint:</p>
                <div class="formula-box">
                    $$L_{end} = X_{point} - X_{end}$$
                </div>
                <p><strong>Use case:</strong> Identifying genes that differ from the final state.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🔄 "Consecutive" Contrasts</div>
                <p>Compare adjacent points along the trajectory:</p>
                <div class="formula-box">
                    $$L_{consec} = X_{point+1} - X_{point}$$
                </div>
                <p><strong>Use case:</strong> Finding genes with local changes between time points.</p>
            </div>
        </div>

        <h3><span class="step-number">3</span>Log2 Fold Change Thresholds</h3>

        <div class="concept-box">
            <p>The parameter <code>l2fc</code> allows testing against a <strong>minimum effect size</strong> rather than just testing for any change:</p>
            <ul>
                <li><code>l2fc = 0</code>: Test if expression changes at all</li>
                <li><code>l2fc > 0</code>: Test if expression changes by at least this amount</li>
            </ul>
        </div>

        <div class="warning">
            <p><strong>Important:</strong> When <code>l2fc > 0</code>, the null hypothesis becomes: "The fold change is less than or equal to the threshold" rather than "There is no change."</p>
        </div>

        <h2>🧮 Mathematical Framework</h2>

        <h3>Generalized Additive Models (GAMs)</h3>

        <div class="concept-box">
            <p>The association tests operate on fitted GAMs that model gene expression as smooth functions of pseudotime:</p>
        </div>

        <div class="formula-box">
            <p><strong>GAM Structure:</strong></p>
            $$\log(\mu_{gi}) = \beta_0 + \sum_{j=1}^{J} s_j(t_{ij}) \cdot l_{ij} + \epsilon_i$$
            <p style="margin-top: 15px; font-size: 0.9em;">
                Where: $\mu_{gi}$ = expected expression for gene $g$ in cell $i$, $s_j(t)$ = smooth function for lineage $j$, $l_{ij}$ = lineage assignment
            </p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    GAM Components in Association Testing
                </text>

                <!-- Smooth function curve -->
                <path d="M 100 300 Q 200 200 300 250 Q 400 300 500 200 Q 600 150 700 180"
                      stroke="#e74c3c" stroke-width="3" fill="none"/>

                <!-- Data points -->
                <circle cx="120" cy="290" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="180" cy="220" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="240" cy="240" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="300" cy="250" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="360" cy="280" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="420" cy="270" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="480" cy="210" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="540" cy="180" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="600" cy="160" r="4" fill="#3498db" opacity="0.8"/>
                <circle cx="660" cy="175" r="4" fill="#3498db" opacity="0.8"/>

                <!-- Knot points -->
                <circle cx="200" cy="200" r="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                <circle cx="400" cy="300" r="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                <circle cx="600" cy="150" r="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>

                <!-- Labels -->
                <text x="100" y="330" text-anchor="middle" font-size="12" fill="#2c3e50">Pseudotime</text>
                <text x="50" y="200" text-anchor="middle" font-size="12" fill="#2c3e50" transform="rotate(-90 50 200)">Expression</text>

                <!-- Legend -->
                <circle cx="150" cy="60" r="4" fill="#3498db"/>
                <text x="165" y="65" font-size="12" fill="#2c3e50">Observed Data</text>

                <line x1="250" y1="60" x2="280" y2="60" stroke="#e74c3c" stroke-width="3"/>
                <text x="290" y="65" font-size="12" fill="#2c3e50">Smooth Function s(t)</text>

                <circle cx="450" cy="60" r="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                <text x="465" y="65" font-size="12" fill="#2c3e50">Knot Points</text>

                <!-- Contrast illustration -->
                <line x1="200" y1="200" x2="600" y2="150" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,5"/>
                <text x="400" y="140" text-anchor="middle" font-size="12" fill="#9b59b6">Contrast: End - Start</text>
            </svg>
        </div>

        <h2>🔍 Test Types</h2>

        <h3><span class="step-number">1</span>Global Tests</h3>

        <div class="concept-box">
            <p>Global tests (<code>global = TRUE</code>) assess whether a gene shows <strong>any significant association</strong> with pseudotime across all lineages simultaneously.</p>
        </div>

        <div class="formula-box">
            <p><strong>Global Test Hypothesis:</strong></p>
            $$H_0: L\beta = \theta \quad \text{vs} \quad H_1: L\beta \neq \theta$$
            <p style="margin-top: 10px; font-size: 0.9em;">
                Tests all contrasts jointly using a single Wald statistic
            </p>
        </div>

        <div class="code-snippet">
# Global test in R code (simplified)
waldResultsOmnibus <- lapply(seq_len(nrow(models)), function(ii){
    beta <- t(betaAll[ii,])
    Sigma <- SigmaAll[[ii]]
    if (any(is.na(beta))) return(c(NA,NA, NA))
    waldTestFC(beta, Sigma, L, l2fc, inverse)
})
        </div>

        <h3><span class="step-number">2</span>Lineage-specific Tests</h3>

        <div class="concept-box">
            <p>Lineage-specific tests (<code>lineages = TRUE</code>) examine each developmental trajectory <strong>independently</strong>, allowing detection of lineage-specific expression patterns.</p>
        </div>

        <div class="method-comparison">
            <div class="method-card">
                <div class="method-title">🌐 Global Approach</div>
                <ul>
                    <li>Single test per gene</li>
                    <li>Higher statistical power</li>
                    <li>Detects overall association</li>
                    <li>May miss lineage-specific effects</li>
                </ul>
            </div>

            <div class="method-card">
                <div class="method-title">🎯 Lineage-specific Approach</div>
                <ul>
                    <li>Separate test per lineage</li>
                    <li>Lower power per test</li>
                    <li>Identifies which lineages show effects</li>
                    <li>Better for complex trajectories</li>
                </ul>
            </div>
        </div>

        <h2>⚙️ Implementation Details</h2>

        <h3>Function Structure</h3>

        <div class="concept-box">
            <p>The R code implements three main functions:</p>
            <ol>
                <li><code>.associationTest()</code> - Standard trajectory analysis</li>
                <li><code>.associationTest_conditions()</code> - Analysis with experimental conditions</li>
                <li><code>.associationTest_original()</code> - Legacy implementation using knots</li>
            </ol>
        </div>

        <div class="svg-container">
            <svg width="800" height="500" viewBox="0 0 800 500">
                <!-- Background -->
                <rect width="800" height="500" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Association Test Implementation Flow
                </text>

                <!-- Input box -->
                <rect x="50" y="60" width="120" height="60" fill="#3498db" rx="5"/>
                <text x="110" y="85" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Input:</text>
                <text x="110" y="100" text-anchor="middle" font-size="10" fill="white">Fitted GAMs</text>
                <text x="110" y="115" text-anchor="middle" font-size="10" fill="white">Parameters</text>

                <!-- Decision diamond -->
                <polygon points="250,70 320,90 250,110 180,90" fill="#f39c12"/>
                <text x="250" y="95" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Conditions?</text>

                <!-- Function boxes -->
                <rect x="100" y="160" width="140" height="50" fill="#e74c3c" rx="5"/>
                <text x="170" y="180" text-anchor="middle" font-size="11" fill="white" font-weight="bold">.associationTest()</text>
                <text x="170" y="195" text-anchor="middle" font-size="9" fill="white">Standard analysis</text>

                <rect x="300" y="160" width="160" height="50" fill="#e74c3c" rx="5"/>
                <text x="380" y="180" text-anchor="middle" font-size="11" fill="white" font-weight="bold">.associationTest_conditions()</text>
                <text x="380" y="195" text-anchor="middle" font-size="9" fill="white">With experimental conditions</text>

                <!-- Process boxes -->
                <rect x="50" y="250" width="120" height="40" fill="#2ecc71" rx="5"/>
                <text x="110" y="275" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Build Contrast Matrix</text>

                <rect x="200" y="250" width="120" height="40" fill="#2ecc71" rx="5"/>
                <text x="260" y="275" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Compute Wald Stats</text>

                <rect x="350" y="250" width="120" height="40" fill="#2ecc71" rx="5"/>
                <text x="410" y="275" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Calculate p-values</text>

                <rect x="500" y="250" width="120" height="40" fill="#2ecc71" rx="5"/>
                <text x="560" y="275" text-anchor="middle" font-size="10" fill="white" font-weight="bold">Fold Changes</text>

                <!-- Output box -->
                <rect x="200" y="350" width="200" height="80" fill="#9b59b6" rx="5"/>
                <text x="300" y="375" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Output:</text>
                <text x="300" y="390" text-anchor="middle" font-size="10" fill="white">Wald Statistics</text>
                <text x="300" y="405" text-anchor="middle" font-size="10" fill="white">p-values</text>
                <text x="300" y="420" text-anchor="middle" font-size="10" fill="white">Mean Log Fold Changes</text>

                <!-- Arrows -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7"
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>

                <!-- Flow arrows -->
                <line x1="170" y1="90" x2="180" y2="90" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                <line x1="250" y1="110" x2="170" y2="160" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                <line x1="250" y1="110" x2="380" y2="160" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>

                <line x1="110" y1="290" x2="110" y2="320" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                <line x1="170" y1="320" x2="200" y2="320" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                <line x1="320" y1="320" x2="350" y2="320" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>
                <line x1="470" y1="320" x2="500" y2="320" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>

                <line x1="300" y1="330" x2="300" y2="350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrow)"/>

                <!-- Labels for decision paths -->
                <text x="200" y="140" text-anchor="middle" font-size="9" fill="#2c3e50">No</text>
                <text x="320" y="140" text-anchor="middle" font-size="9" fill="#2c3e50">Yes</text>
            </svg>
        </div>

        <h3>Key Parameters</h3>

        <div class="method-comparison">
            <div class="method-card">
                <div class="method-title">📊 nPoints</div>
                <p><strong>Default:</strong> <code>2 * nknots</code></p>
                <p>Number of points along each lineage used to construct contrasts. More points = finer resolution but potentially more noise.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🔄 contrastType</div>
                <p><strong>Options:</strong> "start", "end", "consecutive"</p>
                <p>Determines the reference point for comparisons. Choose based on biological question.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🧮 inverse</div>
                <p><strong>Options:</strong> "Chol", "eigen", "QR", "generalized"</p>
                <p>Method for matrix inversion. "Chol" is default for l2fc=0, "eigen" for l2fc>0.</p>
            </div>
        </div>

        <h2>🎯 Practical Applications</h2>

        <h3>When to Use Each Test Type</h3>

        <div class="concept-box">
            <p><strong>Use Global Tests when:</strong></p>
            <ul>
                <li>You want to identify genes that change along <em>any</em> trajectory</li>
                <li>You have limited statistical power (fewer samples)</li>
                <li>You're doing initial screening for trajectory-associated genes</li>
            </ul>
        </div>

        <div class="concept-box">
            <p><strong>Use Lineage-specific Tests when:</strong></p>
            <ul>
                <li>You want to know <em>which</em> lineages show expression changes</li>
                <li>You suspect lineage-specific effects</li>
                <li>You have sufficient power for multiple testing</li>
            </ul>
        </div>

        <h3>Interpreting Results</h3>

        <div class="formula-box">
            <p><strong>Output Interpretation:</strong></p>
            <ul style="text-align: left; margin: 10px 0;">
                <li><strong>waldStat:</strong> Test statistic value (higher = stronger evidence)</li>
                <li><strong>df:</strong> Degrees of freedom (rank of contrast matrix)</li>
                <li><strong>pvalue:</strong> Probability of observing data under null hypothesis</li>
                <li><strong>meanLogFC:</strong> Average absolute log2 fold change</li>
            </ul>
        </div>

        <div class="highlight">
            <p><strong>Statistical Significance:</strong> Genes with low p-values (typically < 0.05 after multiple testing correction) show significant association with pseudotime.</p>
        </div>

        <h3>Example Usage Scenarios</h3>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Common Analysis Scenarios
                </text>

                <!-- Scenario 1 -->
                <rect x="50" y="60" width="200" height="120" fill="#3498db" rx="8" opacity="0.8"/>
                <text x="150" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">Early Development</text>
                <text x="150" y="105" text-anchor="middle" font-size="11" fill="white">contrastType = "start"</text>
                <text x="150" y="120" text-anchor="middle" font-size="11" fill="white">l2fc = 1</text>
                <text x="150" y="135" text-anchor="middle" font-size="10" fill="white">Find genes that change</text>
                <text x="150" y="150" text-anchor="middle" font-size="10" fill="white">significantly from start</text>
                <text x="150" y="165" text-anchor="middle" font-size="10" fill="white">by at least 2-fold</text>

                <!-- Scenario 2 -->
                <rect x="300" y="60" width="200" height="120" fill="#e74c3c" rx="8" opacity="0.8"/>
                <text x="400" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">Terminal Differentiation</text>
                <text x="400" y="105" text-anchor="middle" font-size="11" fill="white">contrastType = "end"</text>
                <text x="400" y="120" text-anchor="middle" font-size="11" fill="white">lineages = TRUE</text>
                <text x="400" y="135" text-anchor="middle" font-size="10" fill="white">Identify lineage-specific</text>
                <text x="400" y="150" text-anchor="middle" font-size="10" fill="white">terminal markers</text>
                <text x="400" y="165" text-anchor="middle" font-size="10" fill="white">relative to endpoint</text>

                <!-- Scenario 3 -->
                <rect x="550" y="60" width="200" height="120" fill="#2ecc71" rx="8" opacity="0.8"/>
                <text x="650" y="85" text-anchor="middle" font-size="14" fill="white" font-weight="bold">Dynamic Changes</text>
                <text x="650" y="105" text-anchor="middle" font-size="11" fill="white">contrastType = "consecutive"</text>
                <text x="650" y="120" text-anchor="middle" font-size="11" fill="white">nPoints = high</text>
                <text x="650" y="135" text-anchor="middle" font-size="10" fill="white">Detect genes with</text>
                <text x="650" y="150" text-anchor="middle" font-size="10" fill="white">rapid local changes</text>
                <text x="650" y="165" text-anchor="middle" font-size="10" fill="white">between time points</text>

                <!-- Best practices box -->
                <rect x="100" y="220" width="600" height="100" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="8"/>
                <text x="400" y="245" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">Best Practices</text>

                <text x="120" y="270" font-size="12" fill="#2c3e50">• Always apply multiple testing correction (FDR/Bonferroni)</text>
                <text x="120" y="285" font-size="12" fill="#2c3e50">• Consider biological effect size, not just statistical significance</text>
                <text x="120" y="300" font-size="12" fill="#2c3e50">• Validate results with independent datasets or experimental validation</text>
            </svg>
        </div>

        <h2>🔧 Advanced Features</h2>

        <h3>Handling Experimental Conditions</h3>

        <div class="concept-box">
            <p>The <code>.associationTest_conditions()</code> function extends the basic framework to handle experimental conditions (e.g., treatment vs. control):</p>
        </div>

        <div class="code-snippet">
# Example: Testing with conditions
# Each lineage × condition combination gets separate contrasts
for (jj in seq_len(nCurves)) {
    for(kk in seq_len(nlevels(conditions))){
        # Build contrast matrix for lineage jj, condition kk
        assign(paste0("L", jj, kk), C)
    }
}
        </div>

        <h3>Matrix Inversion Methods</h3>

        <div class="method-comparison">
            <div class="method-card">
                <div class="method-title">🔹 Cholesky ("Chol")</div>
                <p>Fast and stable for positive definite matrices. Default when l2fc=0.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🔸 Eigendecomposition ("eigen")</div>
                <p>More robust for near-singular matrices. Default when l2fc>0.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🔺 QR Decomposition ("QR")</div>
                <p>Good numerical stability for rank-deficient matrices.</p>
            </div>

            <div class="method-card">
                <div class="method-title">🔻 Generalized Inverse ("generalized")</div>
                <p>Handles singular matrices using Moore-Penrose pseudoinverse.</p>
            </div>
        </div>

        <div class="warning">
            <p><strong>Computational Note:</strong> The choice of inversion method can affect numerical stability, especially with high-dimensional data or near-singular covariance matrices.</p>
        </div>

        <h2>📝 Summary</h2>

        <div class="highlight">
            <p><strong>Key Takeaways:</strong></p>
            <ul>
                <li>Association tests use <strong>Wald statistics</strong> to test for trajectory-associated expression changes</li>
                <li><strong>Contrast matrices</strong> define what comparisons are made (start, end, or consecutive points)</li>
                <li><strong>Global tests</strong> detect any association; <strong>lineage tests</strong> identify which trajectories show effects</li>
                <li><strong>Log2 fold change thresholds</strong> allow testing for biologically meaningful effect sizes</li>
                <li>The implementation handles complex experimental designs with multiple conditions</li>
            </ul>
        </div>

        <div class="concept-box">
            <p style="text-align: center; font-size: 1.1em; color: #2c3e50; font-style: italic;">
                "These statistical tests provide a rigorous framework for identifying genes that drive developmental trajectories in single-cell data."
            </p>
        </div>

    </div>
</body>
</html>

