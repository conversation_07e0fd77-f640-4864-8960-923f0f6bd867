21.1 The Wald Test
When we are testing a simple null hypothesis against a possibly composite alternative, the
NP test is no longer applicable and a general alternative is to use the <PERSON><PERSON> test.
We are interested in testing the hypotheses in a parametric model:
H0 : θ = θ0
H1 : θ 6= θ0.
The Wald test most generally is based on an asymptotically normal estimator, i.e. we suppose
that we have access to an estimator θb which under the null satisfies the property that:
θb
d
→ N(θ0, σ2
0
),
where σ
2
0
is the variance of the estimator under the null. The canonical example is when θb
is taken to be the MLE.
In this case, we could consider the statistic:
Tn =
θb− θ0
σ0
,
or if σ0 is not known we can plug-in an estimate to obtain the statistic,
Tn =
θb− θ0
σb0
.
Under the null Tn
d
→ N(0, 1), so we simply reject the null if: |Tn| ≥ zα/2. This controls
the Type-I error only asymptotically (i.e. only if n → ∞) but this is relatively standard in
applications.
21-1
21-2 Lecture 21: October 16
Example: Suppose we considered the problem of testing the parameter of a Bernoulli, i.e.
we observe X1, . . . , Xn ∼ Ber(p), and the null is that p = p0. Defining pb =
1
n
Pn
i=1 Xi
. A
Wald test could be constructed based on the statistic:
Tn = q
pb− p0
p0(1−p0)
n
,
which has an asymptotic N(0, 1) distribution. An alternative would be to use a slightly
different estimated standard deviation, i.e. to define,
Tn = q
pb− p0
pb(1−pb)
n
.
Observe that this alternative test statistic also has an asymptotically standard normal distribution under the null. Its behaviour under the alternate is a bit more pleasant as we will
see.
21.1.1 Power of the Wald Test
To get some idea of what happens under the alternate, suppose we are in some situation
where the MLE has “standard asymptotics”, i.e. θb− θ
d
→ N(0, 1/(nI1(θ))). Suppose that
we use the statistic:
Tn =
q
nI1(θb)(θb− θ0),
and that the true value of the parameter is θ1 6= θ0. Let us define:
∆ = p
nI1(θ1)(θ0 − θ1),
then the probability that the Wald test rejects the null hypothesis is asymptotically:
1 − Φ

∆ + zα/2

+ Φ
∆ − zα/2

.
You will prove this on your HW (it is some simple re-arrangement, similar to what we have
done previously when computing the power function in a Gaussian model). There are some
aspects to notice:
1. If the difference between θ0 and θ1 is very small the power will tend to α, i.e. if ∆ ≈ 0
then the test will have trivial power.
2. As n → ∞ the two Φ terms will approach either 0 or 1, and so the power will approach
1.
3. As a rule of thumb the Wald test will have non-trivial power if |θ0 − θ1|  √
1
nI1(θ