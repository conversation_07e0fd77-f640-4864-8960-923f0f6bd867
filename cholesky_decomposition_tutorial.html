<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cholesky Decomposition Tutorial</title>
    
    <!-- MathJax 3 with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        header {
            background: linear-gradient(135deg, #2c3e50 0%, #8e44ad 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        nav {
            background: #34495e;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #8e44ad;
        }
        
        main {
            padding: 2rem;
        }
        
        .section {
            margin-bottom: 3rem;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            border-left: 5px solid #8e44ad;
        }
        
        h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid #8e44ad;
            padding-bottom: 0.5rem;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .formula-box {
            background: #fff;
            border: 2px solid #8e44ad;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }
        
        .definition-box {
            background: #e8f4f8;
            border-left: 4px solid #3498db;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .theorem-box {
            background: #f0e6ff;
            border-left: 4px solid #8e44ad;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .example-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .step-box {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
        
        .visualization {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .calculation-box {
            background: #e8f4f8;
            border: 1px solid #3498db;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .result-box {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
        }
        
        .algorithm-box {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .code-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        
        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .property-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #8e44ad;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .property-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            header {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            main {
                padding: 1rem;
            }
            
            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Cholesky Decomposition</h1>
            <p class="subtitle">The Matrix Square Root - Complete Tutorial</p>
            <p>From theory to implementation: Understanding one of the most efficient matrix factorizations</p>
        </header>
        
        <nav>
            <div class="nav-links">
                <a href="#introduction">Introduction</a>
                <a href="#positive-definite">Positive Definite</a>
                <a href="#mathematical-foundation">Foundation</a>
                <a href="#algorithm">Algorithm</a>
                <a href="#computational">Computational</a>
                <a href="#applications">Applications</a>
                <a href="#implementation">Implementation</a>
            </div>
        </nav>
        
        <main>
            <!-- Introduction -->
            <section id="introduction" class="section">
                <h2>1. Introduction to Cholesky Decomposition</h2>
                
                <div class="highlight-box">
                    <h3>🎯 What is Cholesky Decomposition?</h3>
                    <p>The <strong>Cholesky decomposition</strong> is a very efficient way of finding the "square root" of a positive definite matrix. It factors a positive definite matrix D into the product of an upper triangular matrix R and its transpose.</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Cholesky Decomposition:</strong></p>
                    $$\mathbf{D} = \mathbf{R}^T \mathbf{R}$$
                    <p>where <strong>D</strong> is positive definite and <strong>R</strong> is upper triangular</p>
                </div>
                
                <div class="visualization">
                    <h4>Cholesky Decomposition Structure</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
                            </linearGradient>
                        </defs>
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="30" text-anchor="middle" font-size="18" fill="#2c3e50" font-weight="bold">D = R^T R Decomposition</text>
                        
                        <!-- Original matrix D -->
                        <text x="100" y="80" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">Positive Definite Matrix D</text>
                        <rect x="50" y="100" width="100" height="100" stroke="#3498db" stroke-width="3" fill="#3498db" opacity="0.2"/>
                        
                        <!-- Fill pattern for symmetric -->
                        <path d="M 50 100 L 150 100 L 150 200 Z" fill="#3498db" opacity="0.4"/>
                        <path d="M 50 100 L 50 200 L 150 200 Z" fill="#3498db" opacity="0.6"/>
                        
                        <text x="100" y="220" text-anchor="middle" font-size="12" fill="#3498db">Symmetric</text>
                        <text x="100" y="235" text-anchor="middle" font-size="12" fill="#3498db">Positive Definite</text>
                        
                        <!-- Equals sign -->
                        <text x="200" y="150" font-size="24" fill="#2c3e50">=</text>
                        
                        <!-- R^T matrix -->
                        <text x="300" y="80" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">R^T (Lower Triangular)</text>
                        <rect x="250" y="100" width="100" height="100" stroke="#e74c3c" stroke-width="3" fill="#e74c3c" opacity="0.2"/>
                        
                        <!-- Lower triangular pattern -->
                        <path d="M 250 100 L 250 200 L 350 200 Z" fill="#e74c3c" opacity="0.5"/>
                        
                        <text x="300" y="220" text-anchor="middle" font-size="12" fill="#e74c3c">Lower Triangular</text>
                        <text x="300" y="235" text-anchor="middle" font-size="12" fill="#e74c3c">R^T</text>
                        
                        <!-- Multiplication sign -->
                        <text x="400" y="150" font-size="24" fill="#2c3e50">×</text>
                        
                        <!-- R matrix -->
                        <text x="500" y="80" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">R (Upper Triangular)</text>
                        <rect x="450" y="100" width="100" height="100" stroke="#27ae60" stroke-width="3" fill="#27ae60" opacity="0.2"/>
                        
                        <!-- Upper triangular pattern -->
                        <path d="M 450 100 L 550 100 L 550 200 Z" fill="#27ae60" opacity="0.5"/>
                        
                        <text x="500" y="220" text-anchor="middle" font-size="12" fill="#27ae60">Upper Triangular</text>
                        <text x="500" y="235" text-anchor="middle" font-size="12" fill="#27ae60">R</text>
                        
                        <!-- Benefits box -->
                        <rect x="580" y="100" width="200" height="120" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="5"/>
                        <text x="680" y="125" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Key Benefits</text>
                        <text x="590" y="145" font-size="12" fill="#2c3e50">• Efficient: O(n³/3) operations</text>
                        <text x="590" y="160" font-size="12" fill="#2c3e50">• Numerically stable</text>
                        <text x="590" y="175" font-size="12" fill="#2c3e50">• Unique factorization</text>
                        <text x="590" y="190" font-size="12" fill="#2c3e50">• Easy linear system solving</text>
                        <text x="590" y="205" font-size="12" fill="#2c3e50">• Memory efficient</text>
                        
                        <!-- Arrow showing "square root" concept -->
                        <path d="M 100 260 Q 150 280 200 260" stroke="#8e44ad" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                        <text x="150" y="290" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">"Square Root" of Matrix</text>
                        
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#8e44ad" />
                            </marker>
                        </defs>
                        
                        <!-- Intuition -->
                        <text x="400" y="340" text-anchor="middle" font-size="14" fill="#8e44ad" font-weight="bold">Just like a² = a × a, we have D = R^T × R</text>
                        <text x="400" y="360" text-anchor="middle" font-size="12" fill="#2c3e50">R is like the "square root" of the positive definite matrix D</text>
                    </svg>
                </div>
                
                <h3>Why "Square Root" of a Matrix?</h3>
                
                <div class="definition-box">
                    <h4>📚 Analogy with Real Numbers</h4>
                    <p>For a positive real number a, we have a = √a × √a. Similarly, for a positive definite matrix D, we have:</p>
                    <p style="text-align: center; margin: 1rem 0;">
                        <strong>D = R^T R</strong>
                    </p>
                    <p>where R can be thought of as a "matrix square root" of D.</p>
                </div>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>🔢 Efficiency</h4>
                        <p>Only ~n³/3 operations vs n³/2 for LU decomposition</p>
                    </div>
                    <div class="property-card">
                        <h4>🎯 Uniqueness</h4>
                        <p>For positive definite matrices, R is uniquely determined</p>
                    </div>
                    <div class="property-card">
                        <h4>💾 Memory</h4>
                        <p>Can overwrite original matrix during computation</p>
                    </div>
                    <div class="property-card">
                        <h4>⚡ Applications</h4>
                        <p>Linear systems, optimization, statistics, simulation</p>
                    </div>
                </div>
            </section>
            
            <!-- Positive Definite Matrices -->
            <section id="positive-definite" class="section">
                <h2>2. Positive Definite Matrices</h2>
                
                <h3>2.1 Definition and Properties</h3>
                
                <div class="definition-box">
                    <h4>📐 Definition: Positive Definite Matrix</h4>
                    <p>A matrix D is <strong>positive definite</strong> if:</p>
                    <p style="text-align: center; font-size: 1.2em; margin: 1rem 0;">
                        <strong>x^T D x > 0</strong>
                    </p>
                    <p>for any non-zero vector x (of appropriate dimension)</p>
                </div>
                
                <div class="theorem-box">
                    <h4>🔍 Key Properties of Positive Definite Matrices</h4>
                    <ul>
                        <li><strong>Symmetric:</strong> D = D^T</li>
                        <li><strong>Strictly positive eigenvalues:</strong> All λᵢ > 0</li>
                        <li><strong>Invertible:</strong> det(D) > 0</li>
                        <li><strong>Cholesky decomposition exists and is unique</strong></li>
                        <li><strong>All leading principal minors are positive</strong></li>
                    </ul>
                </div>
                
                <div class="visualization">
                    <h4>Geometric Interpretation of Positive Definiteness</h4>
                    <svg width="800" height="500" viewBox="0 0 800 500">
                        <!-- Background -->
                        <rect width="800" height="500" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">Quadratic Forms: x^T D x</text>
                        
                        <!-- Left side: Positive definite (ellipse) -->
                        <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Positive Definite</text>
                        <text x="200" y="80" text-anchor="middle" font-size="12" fill="#27ae60">x^T D x > 0 (elliptical contours)</text>
                        
                        <!-- Coordinate system -->
                        <line x1="100" y1="200" x2="300" y2="200" stroke="#666" stroke-width="1"/>
                        <line x1="200" y1="100" x2="200" y2="300" stroke="#666" stroke-width="1"/>
                        
                        <!-- Elliptical contours (positive definite) -->
                        <ellipse cx="200" cy="200" rx="60" ry="40" stroke="#27ae60" stroke-width="3" fill="none"/>
                        <ellipse cx="200" cy="200" rx="40" ry="25" stroke="#27ae60" stroke-width="2" fill="none"/>
                        <ellipse cx="200" cy="200" rx="20" ry="12" stroke="#27ae60" stroke-width="1" fill="none"/>
                        
                        <!-- Sample vectors -->
                        <line x1="200" y1="200" x2="240" y2="180" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="200" y1="200" x2="170" y2="230" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="210" y="340" text-anchor="middle" font-size="12" fill="#27ae60">All x^T D x > 0</text>
                        
                        <!-- Middle: Indefinite (hyperbola) -->
                        <text x="400" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Indefinite</text>
                        <text x="400" y="80" text-anchor="middle" font-size="12" fill="#e74c3c">x^T D x can be > 0 or < 0</text>
                        
                        <!-- Coordinate system -->
                        <line x1="300" y1="200" x2="500" y2="200" stroke="#666" stroke-width="1"/>
                        <line x1="400" y1="100" x2="400" y2="300" stroke="#666" stroke-width="1"/>
                        
                        <!-- Hyperbolic contours (indefinite) -->
                        <path d="M 350 140 Q 380 160 420 140 Q 450 160 480 140" stroke="#e74c3c" stroke-width="2" fill="none"/>
                        <path d="M 350 260 Q 380 240 420 260 Q 450 240 480 260" stroke="#e74c3c" stroke-width="2" fill="none"/>
                        <path d="M 340 150 Q 370 170 410 150 Q 440 170 470 150" stroke="#e74c3c" stroke-width="1" fill="none"/>
                        <path d="M 340 250 Q 370 230 410 250 Q 440 230 470 250" stroke="#e74c3c" stroke-width="1" fill="none"/>
                        
                        <text x="410" y="340" text-anchor="middle" font-size="12" fill="#e74c3c">Mixed signs</text>
                        
                        <!-- Right side: Negative definite -->
                        <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Negative Definite</text>
                        <text x="600" y="80" text-anchor="middle" font-size="12" fill="#8e44ad">x^T D x < 0 (elliptical, inverted)</text>
                        
                        <!-- Coordinate system -->
                        <line x1="500" y1="200" x2="700" y2="200" stroke="#666" stroke-width="1"/>
                        <line x1="600" y1="100" x2="600" y2="300" stroke="#666" stroke-width="1"/>
                        
                        <!-- Elliptical contours (negative definite) -->
                        <ellipse cx="600" cy="200" rx="60" ry="40" stroke="#8e44ad" stroke-width="3" fill="none" stroke-dasharray="5,5"/>
                        <ellipse cx="600" cy="200" rx="40" ry="25" stroke="#8e44ad" stroke-width="2" fill="none" stroke-dasharray="3,3"/>
                        <ellipse cx="600" cy="200" rx="20" ry="12" stroke="#8e44ad" stroke-width="1" fill="none" stroke-dasharray="2,2"/>
                        
                        <text x="610" y="340" text-anchor="middle" font-size="12" fill="#8e44ad">All x^T D x < 0</text>
                        
                        <!-- Matrix examples -->
                        <rect x="50" y="380" width="700" height="100" fill="#f8f9fa" stroke="#dee2e6" rx="5"/>
                        <text x="400" y="405" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Example Matrices</text>
                        
                        <text x="70" y="430" font-size="12" fill="#27ae60" font-weight="bold">Positive Definite:</text>
                        <text x="70" y="445" font-size="12" fill="#2c3e50">[2  1]    Eigenvalues: λ₁ = 3, λ₂ = 1</text>
                        <text x="70" y="460" font-size="12" fill="#2c3e50">[1  2]</text>
                        
                        <text x="270" y="430" font-size="12" fill="#e74c3c" font-weight="bold">Indefinite:</text>
                        <text x="270" y="445" font-size="12" fill="#2c3e50">[1  2]    Eigenvalues: λ₁ = 3, λ₂ = -1</text>
                        <text x="270" y="460" font-size="12" fill="#2c3e50">[2  1]</text>
                        
                        <text x="470" y="430" font-size="12" fill="#8e44ad" font-weight="bold">Negative Definite:</text>
                        <text x="470" y="445" font-size="12" fill="#2c3e50">[-2  -1]   Eigenvalues: λ₁ = -3, λ₂ = -1</text>
                        <text x="470" y="460" font-size="12" fill="#2c3e50">[-1  -2]</text>
                    </svg>
                </div>
                
                <h3>2.2 Testing for Positive Definiteness</h3>
                
                <div class="algorithm-box">
                    <h4>🔧 Methods to Check Positive Definiteness</h4>
                    <ol>
                        <li><strong>Eigenvalue Test:</strong> All eigenvalues > 0</li>
                        <li><strong>Leading Principal Minors:</strong> All > 0</li>
                        <li><strong>Cholesky Test:</strong> If decomposition succeeds without pivoting</li>
                        <li><strong>Quadratic Form:</strong> x^T D x > 0 for all x ≠ 0</li>
                    </ol>
                </div>
                
                <div class="example-box">
                    <h4>💡 Example: Testing a 2×2 Matrix</h4>
                    <p>For matrix D = [a c; c b], it's positive definite if:</p>
                    <ul>
                        <li>a > 0 (first leading principal minor)</li>
                        <li>det(D) = ab - c² > 0 (second leading principal minor)</li>
                    </ul>
                </div>
            </section>
            
            <!-- Mathematical Foundation -->
            <section id="mathematical-foundation" class="section">
                <h2>3. Mathematical Foundation</h2>
                
                <h3>3.1 The Decomposition Theorem</h3>
                
                <div class="theorem-box">
                    <h4>🎯 Cholesky Decomposition Theorem</h4>
                    <p><strong>Every positive definite matrix D has a unique decomposition:</strong></p>
                    <p style="text-align: center; font-size: 1.3em; margin: 1rem 0;">
                        <strong>D = R^T R</strong>
                    </p>
                    <p>where R is an upper triangular matrix with positive diagonal entries.</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Matrix Structure:</strong></p>
                    $$\begin{bmatrix}
                    d_{11} & d_{12} & d_{13} \\
                    d_{12} & d_{22} & d_{23} \\
                    d_{13} & d_{23} & d_{33}
                    \end{bmatrix} = 
                    \begin{bmatrix}
                    r_{11} & 0 & 0 \\
                    r_{12} & r_{22} & 0 \\
                    r_{13} & r_{23} & r_{33}
                    \end{bmatrix}
                    \begin{bmatrix}
                    r_{11} & r_{12} & r_{13} \\
                    0 & r_{22} & r_{23} \\
                    0 & 0 & r_{33}
                    \end{bmatrix}$$
                </div>
                
                <h3>3.2 Relationship to Other Factorizations</h3>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>🔗 LU Decomposition</h4>
                        <p>For positive definite D: D = LU where L = R^T and U = R</p>
                    </div>
                    <div class="property-card">
                        <h4>🔗 QR Decomposition</h4>
                        <p>If A = QR, then A^T A = R^T Q^T Q R = R^T R</p>
                    </div>
                    <div class="property-card">
                        <h4>🔗 Eigendecomposition</h4>
                        <p>D = Q Λ Q^T = Q √Λ √Λ Q^T, but Cholesky is more efficient</p>
                    </div>
                    <div class="property-card">
                        <h4>🔗 SVD</h4>
                        <p>For symmetric positive definite: SVD = eigendecomposition</p>
                    </div>
                </div>
            </section>
            
            <!-- Algorithm -->
            <section id="algorithm" class="section">
                <h2>4. The Cholesky Algorithm</h2>
                
                <h3>4.1 The Core Formulas</h3>
                
                <div class="highlight-box">
                    <h3>📊 Cholesky Algorithm Formulas</h3>
                    <p>Using the convention that $\sum_{k=1}^{0} x_i \equiv 0$, we have:</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Diagonal Elements:</strong></p>
                    $$R_{ii} = \sqrt{D_{ii} - \sum_{k=1}^{i-1} R_{ki}^2}$$
                    
                    <p><strong>Off-diagonal Elements (j > i):</strong></p>
                    $$R_{ij} = \frac{D_{ij} - \sum_{k=1}^{i-1} R_{ki}R_{kj}}{R_{ii}}$$
                </div>
                
                <div class="algorithm-box">
                    <h4>🔄 Step-by-Step Algorithm</h4>
                    <p><strong>Input:</strong> Positive definite matrix D (n×n)</p>
                    <p><strong>Output:</strong> Upper triangular matrix R such that D = R^T R</p>
                    
                    <div class="step-box">
                        <span class="step-number">1</span>
                        <strong>For i = 1 to n:</strong>
                        <ul style="margin-left: 3rem; margin-top: 0.5rem;">
                            <li>Compute diagonal element R_{ii}</li>
                            <li>For j = i+1 to n: Compute R_{ij}</li>
                        </ul>
                    </div>
                    
                    <div class="step-box">
                        <span class="step-number">2</span>
                        <strong>Row-by-row processing ensures all required values are available</strong>
                    </div>
                </div>
                
                <h3>4.2 Detailed Example: 3×3 Matrix</h3>
                
                <div class="example-box">
                    <h4>🧮 Complete Walkthrough</h4>
                    <p>Let's decompose the matrix:</p>
                    $$\mathbf{D} = \begin{bmatrix} 4 & 2 & 1 \\ 2 & 5 & 3 \\ 1 & 3 & 6 \end{bmatrix}$$
                </div>
                
                <div class="visualization">
                    <h4>Step-by-Step Cholesky Decomposition</h4>
                    <svg width="900" height="600" viewBox="0 0 900 600">
                        <!-- Background -->
                        <rect width="900" height="600" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="450" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Cholesky Decomposition: D = R^T R
                        </text>
                        
                        <!-- Original matrix D -->
                        <text x="150" y="70" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Matrix D</text>
                        <rect x="100" y="80" width="100" height="100" stroke="#3498db" stroke-width="2" fill="none"/>
                        
                        <!-- D matrix elements -->
                        <text x="125" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">4</text>
                        <text x="150" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                        <text x="175" y="105" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                        <text x="125" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                        <text x="150" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">5</text>
                        <text x="175" y="130" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                        <text x="125" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                        <text x="150" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                        <text x="175" y="155" text-anchor="middle" font-size="12" fill="#2c3e50">6</text>
                        
                        <!-- Step 1: R11 -->
                        <text x="150" y="220" text-anchor="middle" font-size="14" fill="#e74c3c" font-weight="bold">Step 1: Calculate R₁₁</text>
                        <rect x="120" y="230" width="60" height="30" stroke="#e74c3c" stroke-width="2" fill="#e74c3c" opacity="0.1"/>
                        <text x="150" y="250" text-anchor="middle" font-size="12" fill="#2c3e50">R₁₁ = √D₁₁ = √4 = 2</text>
                        
                        <!-- Step 2: R12, R13 -->
                        <text x="350" y="220" text-anchor="middle" font-size="14" fill="#27ae60" font-weight="bold">Step 2: Calculate R₁₂, R₁₃</text>
                        <text x="350" y="240" text-anchor="middle" font-size="12" fill="#2c3e50">R₁₂ = D₁₂/R₁₁ = 2/2 = 1</text>
                        <text x="350" y="255" text-anchor="middle" font-size="12" fill="#2c3e50">R₁₃ = D₁₃/R₁₁ = 1/2 = 0.5</text>
                        
                        <!-- First row of R -->
                        <text x="150" y="300" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">After Row 1:</text>
                        <rect x="100" y="310" width="100" height="100" stroke="#8e44ad" stroke-width="2" fill="none"/>
                        <text x="125" y="335" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">2</text>
                        <text x="150" y="335" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">1</text>
                        <text x="175" y="335" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">0.5</text>
                        <text x="125" y="360" text-anchor="middle" font-size="12" fill="#ddd">0</text>
                        <text x="150" y="360" text-anchor="middle" font-size="12" fill="#ddd">?</text>
                        <text x="175" y="360" text-anchor="middle" font-size="12" fill="#ddd">?</text>
                        <text x="125" y="385" text-anchor="middle" font-size="12" fill="#ddd">0</text>
                        <text x="150" y="385" text-anchor="middle" font-size="12" fill="#ddd">0</text>
                        <text x="175" y="385" text-anchor="middle" font-size="12" fill="#ddd">?</text>
                        
                        <!-- Step 3: R22 -->
                        <text x="350" y="300" text-anchor="middle" font-size="14" fill="#e74c3c" font-weight="bold">Step 3: Calculate R₂₂</text>
                        <text x="350" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">R₂₂ = √(D₂₂ - R₁₂²)</text>
                        <text x="350" y="335" text-anchor="middle" font-size="12" fill="#2c3e50">= √(5 - 1²) = √4 = 2</text>
                        
                        <!-- Step 4: R23 -->
                        <text x="550" y="300" text-anchor="middle" font-size="14" fill="#27ae60" font-weight="bold">Step 4: Calculate R₂₃</text>
                        <text x="550" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">R₂₃ = (D₂₃ - R₁₂R₁₃)/R₂₂</text>
                        <text x="550" y="335" text-anchor="middle" font-size="12" fill="#2c3e50">= (3 - 1×0.5)/2 = 2.5/2 = 1.25</text>
                        
                        <!-- After row 2 -->
                        <text x="350" y="380" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">After Row 2:</text>
                        <rect x="300" y="390" width="100" height="100" stroke="#8e44ad" stroke-width="2" fill="none"/>
                        <text x="325" y="415" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">2</text>
                        <text x="350" y="415" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">1</text>
                        <text x="375" y="415" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">0.5</text>
                        <text x="325" y="440" text-anchor="middle" font-size="12" fill="#ddd">0</text>
                        <text x="350" y="440" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">2</text>
                        <text x="375" y="440" text-anchor="middle" font-size="12" fill="#8e44ad" font-weight="bold">1.25</text>
                        <text x="325" y="465" text-anchor="middle" font-size="12" fill="#ddd">0</text>
                        <text x="350" y="465" text-anchor="middle" font-size="12" fill="#ddd">0</text>
                        <text x="375" y="465" text-anchor="middle" font-size="12" fill="#ddd">?</text>
                        
                        <!-- Step 5: R33 -->
                        <text x="550" y="380" text-anchor="middle" font-size="14" fill="#e74c3c" font-weight="bold">Step 5: Calculate R₃₃</text>
                        <text x="550" y="400" text-anchor="middle" font-size="12" fill="#2c3e50">R₃₃ = √(D₃₃ - R₁₃² - R₂₃²)</text>
                        <text x="550" y="415" text-anchor="middle" font-size="12" fill="#2c3e50">= √(6 - 0.5² - 1.25²)</text>
                        <text x="550" y="430" text-anchor="middle" font-size="12" fill="#2c3e50">= √(6 - 0.25 - 1.5625)</text>
                        <text x="550" y="445" text-anchor="middle" font-size="12" fill="#2c3e50">= √4.1875 ≈ 2.046</text>
                        
                        <!-- Final R matrix -->
                        <text x="750" y="300" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Final R Matrix</text>
                        <rect x="700" y="310" width="100" height="100" stroke="#27ae60" stroke-width="3" fill="#27ae60" opacity="0.1"/>
                        <text x="725" y="335" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">2</text>
                        <text x="750" y="335" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">1</text>
                        <text x="775" y="335" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">0.5</text>
                        <text x="725" y="360" text-anchor="middle" font-size="12" fill="#999">0</text>
                        <text x="750" y="360" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">2</text>
                        <text x="775" y="360" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">1.25</text>
                        <text x="725" y="385" text-anchor="middle" font-size="12" fill="#999">0</text>
                        <text x="750" y="385" text-anchor="middle" font-size="12" fill="#999">0</text>
                        <text x="775" y="385" text-anchor="middle" font-size="12" fill="#27ae60" font-weight="bold">2.046</text>
                        
                        <!-- Verification -->
                        <rect x="50" y="520" width="800" height="60" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="450" y="545" text-anchor="middle" font-size="14" fill="#155724" font-weight="bold">
                            Verification: Compute R^T R and check it equals original D
                        </text>
                        <text x="450" y="565" text-anchor="middle" font-size="12" fill="#155724">
                            This confirms our decomposition is correct!
                        </text>
                    </svg>
                </div>
                
                <div class="calculation-box">
                    <h4>📊 Detailed Calculations</h4>
                    
                    <h5>Step 1: First diagonal element</h5>
                    <p>$R_{11} = \sqrt{D_{11}} = \sqrt{4} = 2$</p>
                    
                    <h5>Step 2: First row off-diagonal elements</h5>
                    <p>$R_{12} = \frac{D_{12}}{R_{11}} = \frac{2}{2} = 1$</p>
                    <p>$R_{13} = \frac{D_{13}}{R_{11}} = \frac{1}{2} = 0.5$</p>
                    
                    <h5>Step 3: Second diagonal element</h5>
                    <p>$R_{22} = \sqrt{D_{22} - R_{12}^2} = \sqrt{5 - 1^2} = \sqrt{4} = 2$</p>
                    
                    <h5>Step 4: Second row off-diagonal element</h5>
                    <p>$R_{23} = \frac{D_{23} - R_{12}R_{13}}{R_{22}} = \frac{3 - 1 \times 0.5}{2} = \frac{2.5}{2} = 1.25$</p>
                    
                    <h5>Step 5: Third diagonal element</h5>
                    <p>$R_{33} = \sqrt{D_{33} - R_{13}^2 - R_{23}^2} = \sqrt{6 - 0.5^2 - 1.25^2} = \sqrt{4.1875} \approx 2.046$</p>
                </div>
                
                <div class="result-box">
                    <h4>✅ Final Result</h4>
                    <p>The Cholesky factor R is:</p>
                    $$\mathbf{R} = \begin{bmatrix} 2 & 1 & 0.5 \\ 0 & 2 & 1.25 \\ 0 & 0 & 2.046 \end{bmatrix}$$
                    <p>And we can verify: $\mathbf{D} = \mathbf{R}^T \mathbf{R}$</p>
                </div>
            </section>
            
            <!-- Computational Aspects -->
            <section id="computational" class="section">
                <h2>5. Computational Aspects</h2>
                
                <h3>5.1 Operation Count</h3>
                
                <div class="highlight-box">
                    <h3>⚡ Efficiency Analysis</h3>
                    <p>The Cholesky decomposition costs around <strong>n³/3 operations</strong> for an n×n matrix, making it a rather efficient way of solving linear systems involving positive definite matrices.</p>
                </div>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>📊 Operation Count</h4>
                        <p><strong>Cholesky:</strong> ~n³/3 flops</p>
                        <p><strong>LU:</strong> ~2n³/3 flops</p>
                        <p><strong>QR:</strong> ~4n³/3 flops</p>
                    </div>
                    <div class="property-card">
                        <h4>💾 Memory Usage</h4>
                        <p>Can overwrite input matrix</p>
                        <p>Only upper triangle needed</p>
                        <p>Memory efficient: ~n²/2 storage</p>
                    </div>
                    <div class="property-card">
                        <h4>🎯 Numerical Stability</h4>
                        <p>No pivoting needed</p>
                        <p>Stable for well-conditioned matrices</p>
                        <p>Breakdown indicates non-positive definite</p>
                    </div>
                    <div class="property-card">
                        <h4>⚡ Performance Benefits</h4>
                        <p>Half the work of general LU</p>
                        <p>Exploits symmetry</p>
                        <p>Cache-friendly algorithms</p>
                    </div>
                </div>
                
                <div class="visualization">
                    <h4>Operation Count Comparison</h4>
                    <svg width="800" height="400" viewBox="0 0 800 400">
                        <!-- Background -->
                        <rect width="800" height="400" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Operation Count vs Matrix Size
                        </text>
                        
                        <!-- Axes -->
                        <line x1="80" y1="350" x2="720" y2="350" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="80" y1="350" x2="80" y2="50" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- X-axis labels -->
                        <text x="80" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">0</text>
                        <text x="200" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">500</text>
                        <text x="320" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">1000</text>
                        <text x="440" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">1500</text>
                        <text x="560" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">2000</text>
                        <text x="680" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">2500</text>
                        <text x="400" y="390" text-anchor="middle" font-size="14" fill="#2c3e50">Matrix Size (n)</text>
                        
                        <!-- Y-axis labels -->
                        <text x="70" y="355" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                        <text x="70" y="290" text-anchor="end" font-size="12" fill="#2c3e50">1B</text>
                        <text x="70" y="225" text-anchor="end" font-size="12" fill="#2c3e50">2B</text>
                        <text x="70" y="160" text-anchor="end" font-size="12" fill="#2c3e50">3B</text>
                        <text x="70" y="95" text-anchor="end" font-size="12" fill="#2c3e50">4B</text>
                        <text x="40" y="200" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 40 200)">Operations (flops)</text>
                        
                        <!-- Grid lines -->
                        <g stroke="#ddd" stroke-width="1" opacity="0.5">
                            <line x1="80" y1="290" x2="720" y2="290"/>
                            <line x1="80" y1="225" x2="720" y2="225"/>
                            <line x1="80" y1="160" x2="720" y2="160"/>
                            <line x1="80" y1="95" x2="720" y2="95"/>
                            <line x1="200" y1="350" x2="200" y2="50"/>
                            <line x1="320" y1="350" x2="320" y2="50"/>
                            <line x1="440" y1="350" x2="440" y2="50"/>
                            <line x1="560" y1="350" x2="560" y2="50"/>
                            <line x1="680" y1="350" x2="680" y2="50"/>
                        </g>
                        
                        <!-- Cholesky curve (n³/3) -->
                        <path d="M 80 350 Q 200 320 320 250 Q 440 180 560 120 Q 640 80 720 60" 
                              stroke="#27ae60" stroke-width="4" fill="none"/>
                        
                        <!-- LU curve (2n³/3) -->
                        <path d="M 80 350 Q 200 290 320 190 Q 440 120 560 80 Q 640 60 720 50" 
                              stroke="#e74c3c" stroke-width="4" fill="none"/>
                        
                        <!-- QR curve (4n³/3) -->
                        <path d="M 80 350 Q 200 260 320 150 Q 440 90 560 70 Q 640 55 720 45" 
                              stroke="#8e44ad" stroke-width="4" fill="none"/>
                        
                        <!-- Legend -->
                        <rect x="500" y="70" width="200" height="100" fill="white" stroke="#ddd" rx="5"/>
                        <text x="600" y="90" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Operation Count</text>
                        
                        <line x1="510" y1="105" x2="540" y2="105" stroke="#27ae60" stroke-width="3"/>
                        <text x="545" y="110" font-size="11" fill="#2c3e50">Cholesky (~n³/3)</text>
                        
                        <line x1="510" y1="125" x2="540" y2="125" stroke="#e74c3c" stroke-width="3"/>
                        <text x="545" y="130" font-size="11" fill="#2c3e50">LU (~2n³/3)</text>
                        
                        <line x1="510" y1="145" x2="540" y2="145" stroke="#8e44ad" stroke-width="3"/>
                        <text x="545" y="150" font-size="11" fill="#2c3e50">QR (~4n³/3)</text>
                    </svg>
                </div>
                
                <h3>5.2 Derivatives of Cholesky Factor</h3>
                
                <div class="theorem-box">
                    <h4>🧮 Chain Rule Application</h4>
                    <p>Occasionally it is useful to have the derivative of the Cholesky factor. By applying the chain rule:</p>
                </div>
                
                <div class="formula-box">
                    <p><strong>Derivatives with respect to parameter ρ:</strong></p>
                    $$\frac{\partial R_{ii}}{\partial \rho} = \frac{1}{2R_{ii}^{-1}} B_{ii}$$
                    
                    $$\frac{\partial R_{ij}}{\partial \rho} = R_{ii}^{-1} B_{ij} - R_{ij} \frac{\partial R_{ii}}{\partial \rho}$$
                    
                    <p>where:</p>
                    $$B_{ij} = \frac{\partial D_{ij}}{\partial \rho} - \sum_{k=1}^{i-1} \left( \frac{\partial R_{ki}}{\partial \rho} R_{kj} + R_{ki} \frac{\partial R_{kj}}{\partial \rho} \right)$$
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Numerical Considerations</h4>
                    <ul>
                        <li><strong>Breakdown:</strong> If any diagonal element becomes non-positive, the matrix is not positive definite</li>
                        <li><strong>Pivoting:</strong> Standard Cholesky doesn't use pivoting (unlike LU)</li>
                        <li><strong>Condition Number:</strong> Large condition numbers can cause numerical instability</li>
                        <li><strong>Modified Cholesky:</strong> Variants exist for near-singular matrices</li>
                    </ul>
                </div>
            </section>
            
            <!-- Applications -->
            <section id="applications" class="section">
                <h2>6. Applications and Use Cases</h2>
                
                <h3>6.1 Solving Linear Systems</h3>
                
                <div class="highlight-box">
                    <h3>🎯 Primary Application: Linear System Solving</h3>
                    <p>For solving <strong>Dx = y</strong> where D is positive definite, use the Cholesky factorization for maximum efficiency.</p>
                </div>
                
                <div class="algorithm-box">
                    <h4>📝 Two-Step Solution Process</h4>
                    <p>Given: D = R^T R and we want to solve Dx = y</p>
                    
                    <div class="step-box">
                        <span class="step-number">1</span>
                        <strong>Forward substitution:</strong> Solve R^T z = y for z
                        <p style="margin-left: 3rem;">Since R^T is lower triangular, this is efficient</p>
                    </div>
                    
                    <div class="step-box">
                        <span class="step-number">2</span>
                        <strong>Back substitution:</strong> Solve Rx = z for x  
                        <p style="margin-left: 3rem;">Since R is upper triangular, this is also efficient</p>
                    </div>
                </div>
                
                <div class="visualization">
                    <h4>Linear System Solution Process</h4>
                    <svg width="800" height="300" viewBox="0 0 800 300">
                        <!-- Background -->
                        <rect width="800" height="300" fill="url(#bg)" />
                        
                        <!-- Title -->
                        <text x="400" y="25" text-anchor="middle" font-size="16" fill="#2c3e50" font-weight="bold">
                            Solving Dx = y using Cholesky: D = R^T R
                        </text>
                        
                        <!-- Step 1: Original system -->
                        <rect x="20" y="50" width="120" height="80" fill="#3498db" opacity="0.2" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="80" y="75" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Original System</text>
                        <text x="80" y="95" text-anchor="middle" font-size="12" fill="#2c3e50">Dx = y</text>
                        <text x="80" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">↓</text>
                        <text x="80" y="125" text-anchor="middle" font-size="12" fill="#2c3e50">R^T R x = y</text>
                        
                        <!-- Arrow 1 -->
                        <path d="M 150 90 L 190 90" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead)"/>
                        
                        <!-- Step 2: Forward substitution -->
                        <rect x="200" y="50" width="150" height="80" fill="#e74c3c" opacity="0.2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="275" y="75" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Step 1: Forward Sub</text>
                        <text x="275" y="95" text-anchor="middle" font-size="12" fill="#2c3e50">R^T z = y</text>
                        <text x="275" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">Solve for z</text>
                        <text x="275" y="125" text-anchor="middle" font-size="12" fill="#e74c3c">O(n²) operations</text>
                        
                        <!-- Arrow 2 -->
                        <path d="M 360 90 L 400 90" stroke="#27ae60" stroke-width="3" marker-end="url(#arrowhead)"/>
                        
                        <!-- Step 3: Back substitution -->
                        <rect x="410" y="50" width="150" height="80" fill="#27ae60" opacity="0.2" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="485" y="75" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Step 2: Back Sub</text>
                        <text x="485" y="95" text-anchor="middle" font-size="12" fill="#2c3e50">R x = z</text>
                        <text x="485" y="110" text-anchor="middle" font-size="12" fill="#2c3e50">Solve for x</text>
                        <text x="485" y="125" text-anchor="middle" font-size="12" fill="#27ae60">O(n²) operations</text>
                        
                        <!-- Arrow 3 -->
                        <path d="M 570 90 L 610 90" stroke="#8e44ad" stroke-width="3" marker-end="url(#arrowhead)"/>
                        
                        <!-- Final result -->
                        <rect x="620" y="50" width="120" height="80" fill="#8e44ad" opacity="0.2" stroke="#8e44ad" stroke-width="2" rx="5"/>
                        <text x="680" y="75" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Solution</text>
                        <text x="680" y="95" text-anchor="middle" font-size="12" fill="#2c3e50">x = answer</text>
                        <text x="680" y="110" text-anchor="middle" font-size="12" fill="#8e44ad">Total: O(n²)</text>
                        <text x="680" y="125" text-anchor="middle" font-size="12" fill="#8e44ad">after O(n³/3) setup</text>
                        
                        <!-- Efficiency note -->
                        <rect x="50" y="160" width="700" height="100" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="5"/>
                        <text x="400" y="185" text-anchor="middle" font-size="14" fill="#155724" font-weight="bold">Efficiency Advantage</text>
                        <text x="400" y="205" text-anchor="middle" font-size="12" fill="#155724">
                            One-time Cholesky decomposition: O(n³/3) operations
                        </text>
                        <text x="400" y="220" text-anchor="middle" font-size="12" fill="#155724">
                            Each linear solve: Only O(n²) operations using triangular structure
                        </text>
                        <text x="400" y="235" text-anchor="middle" font-size="12" fill="#155724">
                            Multiple right-hand sides: Reuse decomposition for great efficiency!
                        </text>
                        <text x="400" y="250" text-anchor="middle" font-size="12" fill="#155724">
                            Compare to Gaussian elimination: O(n³) for each solve
                        </text>
                    </svg>
                </div>
                
                <h3>6.2 Other Important Applications</h3>
                
                <div class="properties-grid">
                    <div class="property-card">
                        <h4>📊 Statistics</h4>
                        <ul>
                            <li>Multivariate normal sampling</li>
                            <li>Covariance matrix decomposition</li>
                            <li>Maximum likelihood estimation</li>
                            <li>Kalman filtering</li>
                        </ul>
                    </div>
                    <div class="property-card">
                        <h4>🎯 Optimization</h4>
                        <ul>
                            <li>Newton's method (Hessian factorization)</li>
                            <li>Quadratic programming</li>
                            <li>Trust region methods</li>
                            <li>Interior point methods</li>
                        </ul>
                    </div>
                    <div class="property-card">
                        <h4>🔬 Scientific Computing</h4>
                        <ul>
                            <li>Finite element methods</li>
                            <li>Partial differential equations</li>
                            <li>Monte Carlo simulation</li>
                            <li>Signal processing</li>
                        </ul>
                    </div>
                    <div class="property-card">
                        <h4>🤖 Machine Learning</h4>
                        <ul>
                            <li>Gaussian processes</li>
                            <li>Ridge regression</li>
                            <li>Kernel methods</li>
                            <li>Bayesian inference</li>
                        </ul>
                    </div>
                </div>
            </section>
            
            <!-- Implementation -->
            <section id="implementation" class="section">
                <h2>7. Practical Implementation</h2>
                
                <h3>7.1 R Implementation Example</h3>
                
                <div class="code-box">
<pre><code># Cholesky decomposition example in R
n <- 1000
D <- matrix(runif(n*n), n, n)
D <- D %*% t(D)  # create a positive definite matrix
y <- runif(n)    # and a right-hand side vector y

## Solve Dx = y efficiently using Cholesky factor of D
R <- chol(D)                              # Compute Cholesky factor
x <- backsolve(R, y, transpose=TRUE)      # Forward substitution: R^T z = y
x <- backsolve(R, x)                      # Back substitution: R x = z

# Verify the solution
max(abs(D %*% x - y))  # Should be very close to zero</code></pre>
                </div>
                
                <h3>7.2 Algorithm Implementation Details</h3>
                
                <div class="algorithm-box">
                    <h4>🔧 Pseudocode for Cholesky Decomposition</h4>
                    <div class="code-box">
<pre><code>function cholesky(D)
    n = size(D, 1)
    R = zeros(n, n)
    
    for i = 1 to n
        # Diagonal element
        sum_squares = 0
        for k = 1 to i-1
            sum_squares += R[k,i]^2
        end
        R[i,i] = sqrt(D[i,i] - sum_squares)
        
        # Off-diagonal elements in row i
        for j = i+1 to n
            sum_products = 0
            for k = 1 to i-1
                sum_products += R[k,i] * R[k,j]
            end
            R[i,j] = (D[i,j] - sum_products) / R[i,i]
        end
    end
    
    return R
end</code></pre>
                    </div>
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Implementation Considerations</h4>
                    <ul>
                        <li><strong>Square root failure:</strong> Check if argument is positive before taking sqrt</li>
                        <li><strong>Division by zero:</strong> Ensure diagonal elements are not zero</li>
                        <li><strong>Memory layout:</strong> Consider row-major vs column-major storage</li>
                        <li><strong>Pivoting variants:</strong> Modified Cholesky for indefinite matrices</li>
                        <li><strong>Block algorithms:</strong> More cache-efficient for large matrices</li>
                    </ul>
                </div>
                
                <h3>7.3 Performance Comparison</h3>
                
                <div class="result-box">
                    <h4>🚀 Timing Comparison (1000×1000 matrix)</h4>
                    <ul>
                        <li><strong>Cholesky + 2 triangular solves:</strong> ~0.3 seconds</li>
                        <li><strong>LU decomposition + solve:</strong> ~0.6 seconds</li>
                        <li><strong>QR decomposition + solve:</strong> ~1.2 seconds</li>
                        <li><strong>Matrix inversion:</strong> ~1.5 seconds (avoid!)</li>
                    </ul>
                    <p><em>Times are approximate and system-dependent</em></p>
                </div>
                
                <div class="highlight-box">
                    <h3>🏆 Key Takeaways</h3>
                    <ul>
                        <li><strong>Efficiency:</strong> Cholesky is about 2× faster than general LU for positive definite systems</li>
                        <li><strong>Stability:</strong> No pivoting needed for positive definite matrices</li>
                        <li><strong>Memory:</strong> Can overwrite input matrix to save space</li>
                        <li><strong>Reusability:</strong> Factor once, solve many times with different right-hand sides</li>
                        <li><strong>Applications:</strong> Essential for statistics, optimization, and scientific computing</li>
                    </ul>
                </div>
            </section>
        
        </main>
        
        <footer>
            <p>&copy; 2024 Cholesky Decomposition Tutorial. Created for educational purposes.</p>
            <p>From matrices to applications: Understanding efficient linear algebra.</p>
        </footer>
    </div>
</body>
</html> 