<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discrete Random Variables: CDF, Expectation, and Variance</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .definition-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .property-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .property-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .property-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .property-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .lotus-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .lotus-title {
            color: #d63031;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Discrete Random Variables</h1>
            <div class="subtitle">CDF, Expectation, Variance, and Functions</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Cumulative Distribution Function (CDF)</strong> - Alternative way to describe distributions</li>
                <li><strong>Expectation (Expected Value)</strong> - The average value of a random variable</li>
                <li><strong>Functions of Random Variables</strong> - How to work with transformations</li>
                <li><strong>Variance and Standard Deviation</strong> - Measuring spread and variability</li>
                <li><strong>Key Theorems and Properties</strong> - Linearity and computational formulas</li>
                <li><strong>Solved Problems</strong> - Practical applications and examples</li>
            </ul>
        </div>

        <h2>📈 Cumulative Distribution Function (CDF)</h2>
        
        <div class="concept-box">
            <p><strong>Why do we need the CDF?</strong></p>
            <p>While the PMF works well for discrete random variables, it cannot be defined for continuous random variables. The CDF provides a unified way to describe <em>any</em> type of random variable (discrete, continuous, or mixed).</p>
        </div>

        <div class="definition-box">
            <h3>Definition 3.10: Cumulative Distribution Function</h3>
            <p>The <strong>cumulative distribution function (CDF)</strong> of random variable $X$ is defined as:</p>
            <div class="formula-box">
                $$F_X(x) = P(X \leq x), \quad \text{for all } x \in \mathbb{R}$$
            </div>
            <p><strong>Key Point:</strong> The CDF is defined for <em>all real numbers</em>, not just values in the range of $X$.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    CDF Example: Two Coin Tosses (X = number of heads)
                </text>
                
                <!-- Axes -->
                <line x1="80" y1="350" x2="720" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="50" stroke="#2c3e50" stroke-width="2"/>
                
                <!-- X-axis labels -->
                <text x="150" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">-1</text>
                <text x="250" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">0</text>
                <text x="350" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">1</text>
                <text x="450" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">2</text>
                <text x="550" y="370" text-anchor="middle" font-size="12" fill="#2c3e50">3</text>
                <text x="400" y="390" text-anchor="middle" font-size="14" fill="#2c3e50">x</text>
                
                <!-- Y-axis labels -->
                <text x="70" y="355" text-anchor="end" font-size="12" fill="#2c3e50">0</text>
                <text x="70" y="290" text-anchor="end" font-size="12" fill="#2c3e50">0.25</text>
                <text x="70" y="225" text-anchor="end" font-size="12" fill="#2c3e50">0.5</text>
                <text x="70" y="160" text-anchor="end" font-size="12" fill="#2c3e50">0.75</text>
                <text x="70" y="95" text-anchor="end" font-size="12" fill="#2c3e50">1</text>
                <text x="40" y="200" text-anchor="middle" font-size="14" fill="#2c3e50" transform="rotate(-90 40 200)">F_X(x)</text>
                
                <!-- CDF step function -->
                <!-- F(x) = 0 for x < 0 -->
                <line x1="80" y1="350" x2="250" y2="350" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- F(x) = 0.25 for 0 ≤ x < 1 -->
                <line x1="250" y1="290" x2="350" y2="290" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- F(x) = 0.75 for 1 ≤ x < 2 -->
                <line x1="350" y1="160" x2="450" y2="160" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- F(x) = 1 for x ≥ 2 -->
                <line x1="450" y1="95" x2="720" y2="95" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Vertical jumps -->
                <line x1="250" y1="350" x2="250" y2="290" stroke="#e74c3c" stroke-width="3" stroke-dasharray="5,5"/>
                <line x1="350" y1="290" x2="350" y2="160" stroke="#e74c3c" stroke-width="3" stroke-dasharray="5,5"/>
                <line x1="450" y1="160" x2="450" y2="95" stroke="#e74c3c" stroke-width="3" stroke-dasharray="5,5"/>
                
                <!-- Jump points (filled circles) -->
                <circle cx="250" cy="290" r="4" fill="#e74c3c"/>
                <circle cx="350" cy="160" r="4" fill="#e74c3c"/>
                <circle cx="450" cy="95" r="4" fill="#e74c3c"/>
                
                <!-- Open circles at discontinuities -->
                <circle cx="250" cy="350" r="4" fill="white" stroke="#e74c3c" stroke-width="2"/>
                <circle cx="350" cy="290" r="4" fill="white" stroke="#e74c3c" stroke-width="2"/>
                <circle cx="450" cy="160" r="4" fill="white" stroke="#e74c3c" stroke-width="2"/>
                
                <!-- PMF values -->
                <text x="300" y="320" text-anchor="middle" font-size="11" fill="#2c3e50">P(X=0) = 1/4</text>
                <text x="400" y="225" text-anchor="middle" font-size="11" fill="#2c3e50">P(X=1) = 1/2</text>
                <text x="500" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">P(X=2) = 1/4</text>
                
                <!-- CDF values -->
                <rect x="580" y="60" width="200" height="120" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="680" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">CDF Values:</text>
                <text x="680" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">F(x) = 0 for x &lt; 0</text>
                <text x="680" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">F(x) = 1/4 for 0 ≤ x &lt; 1</text>
                <text x="680" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">F(x) = 3/4 for 1 ≤ x &lt; 2</text>
                <text x="680" y="145" text-anchor="middle" font-size="11" fill="#2c3e50">F(x) = 1 for x ≥ 2</text>
                <text x="680" y="165" text-anchor="middle" font-size="11" fill="#2c3e50">Jump size = PMF value</text>
            </svg>
        </div>

        <div class="property-grid">
            <div class="property-card">
                <div class="property-title">🔢 Key Properties of CDF</div>
                <ul>
                    <li><strong>Non-decreasing:</strong> If $y \geq x$, then $F_X(y) \geq F_X(x)$</li>
                    <li><strong>Right-continuous:</strong> $\lim_{x \to a^+} F_X(x) = F_X(a)$</li>
                    <li><strong>Limits:</strong> $\lim_{x \to -\infty} F_X(x) = 0$ and $\lim_{x \to \infty} F_X(x) = 1$</li>
                </ul>
            </div>
            
            <div class="property-card">
                <div class="property-title">📊 For Discrete Variables</div>
                <ul>
                    <li><strong>Step function:</strong> Flat between range values</li>
                    <li><strong>Jumps:</strong> At each value in the range</li>
                    <li><strong>Jump size:</strong> Equals the PMF value at that point</li>
                    <li><strong>Formula:</strong> $F_X(x) = \sum_{x_k \leq x} P_X(x_k)$</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Useful Formula:</strong> For any $a \leq b$:</p>
            <div class="formula-box">
                $$P(a < X \leq b) = F_X(b) - F_X(a)$$
            </div>
            <p>This formula works for all types of random variables!</p>
        </div>

        <h2>🎯 Expectation (Expected Value)</h2>

        <div class="concept-box">
            <p><strong>Intuitive Understanding:</strong> If you have a collection of numbers $a_1, a_2, \ldots, a_N$, their average is a single number that describes the whole collection. Similarly, the expected value is the "average" of a random variable.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "The expected value is the weighted average of all possible values."
            </p>
        </div>

        <div class="definition-box">
            <h3>Definition 3.11: Expected Value</h3>
            <p>Let $X$ be a discrete random variable with range $R_X = \{x_1, x_2, x_3, \ldots\}$. The <strong>expected value</strong> of $X$, denoted by $EX$, is defined as:</p>
            <div class="formula-box">
                $$EX = \sum_{x_k \in R_X} x_k P(X = x_k) = \sum_{x_k \in R_X} x_k P_X(x_k)$$
            </div>
            <p><strong>Alternative notations:</strong> $EX = E[X] = E(X) = \mu_X$</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Expected Value as Weighted Average
                </text>

                <!-- Balance beam illustration -->
                <line x1="100" y1="200" x2="700" y2="200" stroke="#34495e" stroke-width="8"/>
                <polygon points="400,200 390,220 410,220" fill="#34495e"/>

                <!-- Weights representing probabilities -->
                <rect x="180" y="150" width="40" height="50" fill="#e74c3c" opacity="0.8"/>
                <text x="200" y="140" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">P(X=1)</text>
                <text x="200" y="130" text-anchor="middle" font-size="11" fill="#2c3e50">= 0.3</text>
                <text x="200" y="225" text-anchor="middle" font-size="12" fill="#2c3e50">x = 1</text>

                <rect x="280" y="120" width="40" height="80" fill="#3498db" opacity="0.8"/>
                <text x="300" y="110" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">P(X=2)</text>
                <text x="300" y="100" text-anchor="middle" font-size="11" fill="#2c3e50">= 0.5</text>
                <text x="300" y="225" text-anchor="middle" font-size="12" fill="#2c3e50">x = 2</text>

                <rect x="480" y="160" width="40" height="40" fill="#2ecc71" opacity="0.8"/>
                <text x="500" y="150" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">P(X=4)</text>
                <text x="500" y="140" text-anchor="middle" font-size="11" fill="#2c3e50">= 0.2</text>
                <text x="500" y="225" text-anchor="middle" font-size="12" fill="#2c3e50">x = 4</text>

                <!-- Balance point (expected value) -->
                <line x1="400" y1="180" x2="400" y2="240" stroke="#f39c12" stroke-width="4"/>
                <text x="400" y="260" text-anchor="middle" font-size="14" fill="#f39c12" font-weight="bold">E[X] = 2.1</text>

                <!-- Calculation -->
                <rect x="50" y="280" width="700" height="50" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="400" y="300" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    E[X] = 1×0.3 + 2×0.5 + 4×0.2 = 0.3 + 1.0 + 0.8 = 2.1
                </text>
                <text x="400" y="320" text-anchor="middle" font-size="12" fill="#2c3e50">
                    The balance point represents the expected value!
                </text>
            </svg>
        </div>

        <div class="example-box">
            <h4>Example 3.11: Bernoulli Distribution</h4>
            <p><strong>Problem:</strong> Let $X \sim \text{Bernoulli}(p)$. Find $EX$.</p>
            <p><strong>Solution:</strong> For Bernoulli, $R_X = \{0, 1\}$ with $P_X(1) = p$ and $P_X(0) = 1-p$.</p>
            <div class="formula-box">
                $$EX = 0 \cdot P_X(0) + 1 \cdot P_X(1) = 0 \cdot (1-p) + 1 \cdot p = p$$
            </div>
            <p><strong>Interpretation:</strong> The expected value of a Bernoulli random variable is simply the probability of success!</p>
        </div>

        <div class="example-box">
            <h4>Example 3.12: Geometric Distribution</h4>
            <p><strong>Problem:</strong> Let $X \sim \text{Geometric}(p)$. Find $EX$.</p>
            <p><strong>Solution:</strong> For geometric, $R_X = \{1, 2, 3, \ldots\}$ and $P_X(k) = p(1-p)^{k-1}$.</p>
            <div class="formula-box">
                $$EX = \sum_{k=1}^{\infty} k \cdot p(1-p)^{k-1} = p \sum_{k=1}^{\infty} k(1-p)^{k-1}$$
            </div>
            <p>Using the derivative of geometric series: $\sum_{k=1}^{\infty} kx^{k-1} = \frac{1}{(1-x)^2}$ for $|x| < 1$:</p>
            <div class="formula-box">
                $$EX = p \cdot \frac{1}{(1-(1-p))^2} = p \cdot \frac{1}{p^2} = \frac{1}{p}$$
            </div>
            <p><strong>Interpretation:</strong> On average, you need $\frac{1}{p}$ trials to get the first success.</p>
        </div>

        <div class="theorem-box">
            <h3>Theorem 3.2: Linearity of Expectation</h3>
            <p>Expectation is a <strong>linear operator</strong>:</p>
            <div class="formula-box">
                $$E[aX + b] = aEX + b \quad \text{for all } a, b \in \mathbb{R}$$
                $$E[X_1 + X_2 + \cdots + X_n] = EX_1 + EX_2 + \cdots + EX_n$$
            </div>
            <p><strong>Important:</strong> The second property holds for <em>any</em> random variables, not just independent ones!</p>
        </div>

        <div class="example-box">
            <h4>Example 3.14: Binomial Distribution (Using Linearity)</h4>
            <p><strong>Problem:</strong> Let $X \sim \text{Binomial}(n,p)$. Find $EX$.</p>
            <p><strong>Solution:</strong> We can write $X = X_1 + X_2 + \cdots + X_n$ where each $X_i \sim \text{Bernoulli}(p)$.</p>
            <div class="formula-box">
                $$EX = E[X_1 + X_2 + \cdots + X_n] = EX_1 + EX_2 + \cdots + EX_n = p + p + \cdots + p = np$$
            </div>
            <p><strong>Power of Linearity:</strong> This is much easier than computing $\sum_{k=0}^n k \binom{n}{k} p^k (1-p)^{n-k}$ directly!</p>
        </div>

        <h2>🔄 Functions of Random Variables</h2>

        <div class="concept-box">
            <p>If $X$ is a random variable and $Y = g(X)$, then $Y$ is also a random variable. We need to understand how to find the PMF and expected value of $Y$.</p>
        </div>

        <div class="formula-box">
            <p><strong>Range of Y:</strong> $R_Y = \{g(x) | x \in R_X\}$</p>
            <p><strong>PMF of Y:</strong> $P_Y(y) = P(Y = y) = P(g(X) = y) = \sum_{x: g(x) = y} P_X(x)$</p>
        </div>

        <div class="lotus-box">
            <div class="lotus-title">🪷 Law of the Unconscious Statistician (LOTUS)</div>
            <p>To find $E[g(X)]$, you don't need to find the PMF of $Y = g(X)$ first!</p>
            <div class="formula-box">
                $$E[g(X)] = \sum_{x_k \in R_X} g(x_k) P_X(x_k)$$
            </div>
            <p><strong>Why "unconscious"?</strong> Because you can compute the expectation of $g(X)$ using the distribution of $X$ directly, without thinking about the distribution of $g(X)$.</p>
        </div>

        <div class="example-box">
            <h4>Example 3.17: LOTUS in Action</h4>
            <p><strong>Problem:</strong> Let $X$ have range $R_X = \{0, \frac{\pi}{4}, \frac{\pi}{2}, \frac{3\pi}{4}, \pi\}$ with equal probabilities $\frac{1}{5}$. Find $E[\sin(X)]$.</p>
            <p><strong>Solution:</strong> Using LOTUS:</p>
            <div class="formula-box">
                $$E[\sin(X)] = \sin(0) \cdot \frac{1}{5} + \sin\left(\frac{\pi}{4}\right) \cdot \frac{1}{5} + \sin\left(\frac{\pi}{2}\right) \cdot \frac{1}{5} + \sin\left(\frac{3\pi}{4}\right) \cdot \frac{1}{5} + \sin(\pi) \cdot \frac{1}{5}$$
                $$= \frac{1}{5}\left(0 + \frac{\sqrt{2}}{2} + 1 + \frac{\sqrt{2}}{2} + 0\right) = \frac{\sqrt{2} + 1}{5}$$
            </div>
        </div>

        <h2>📊 Variance and Standard Deviation</h2>

        <div class="concept-box">
            <p><strong>Motivation:</strong> Consider two random variables with the same mean but different "spread":</p>
            <ul>
                <li>$X$: Takes values $-100, 100$ with probabilities $0.5, 0.5$ → $EX = 0$</li>
                <li>$Y$: Takes value $0$ with probability $1$ → $EY = 0$</li>
            </ul>
            <p>Both have the same mean, but $X$ is much more "spread out" than $Y$. <strong>Variance</strong> measures this spread.</p>
        </div>

        <div class="definition-box">
            <h3>Definition: Variance and Standard Deviation</h3>
            <p>The <strong>variance</strong> of a random variable $X$ with mean $EX = \mu_X$ is:</p>
            <div class="formula-box">
                $$\text{Var}(X) = E[(X - \mu_X)^2]$$
            </div>
            <p>The <strong>standard deviation</strong> is:</p>
            <div class="formula-box">
                $$\text{SD}(X) = \sigma_X = \sqrt{\text{Var}(X)}$$
            </div>
            <p><strong>Units:</strong> Variance has units of $X^2$, while standard deviation has the same units as $X$.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <rect width="800" height="300" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Variance: Measuring Spread Around the Mean
                </text>

                <!-- Low variance distribution -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">Low Variance</text>
                <rect x="150" y="80" width="100" height="120" fill="#e1ecf4" stroke="#3498db" stroke-width="2" rx="5"/>

                <!-- Bars for low variance -->
                <rect x="190" y="120" width="20" height="60" fill="#3498db" opacity="0.8"/>
                <text x="200" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">0.6</text>
                <text x="200" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">μ</text>

                <rect x="170" y="150" width="15" height="30" fill="#3498db" opacity="0.6"/>
                <rect x="215" y="150" width="15" height="30" fill="#3498db" opacity="0.6"/>
                <text x="177" y="145" text-anchor="middle" font-size="9" fill="#2c3e50">0.2</text>
                <text x="222" y="145" text-anchor="middle" font-size="9" fill="#2c3e50">0.2</text>

                <!-- High variance distribution -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">High Variance</text>
                <rect x="450" y="80" width="300" height="120" fill="#f8d7da" stroke="#e74c3c" stroke-width="2" rx="5"/>

                <!-- Bars for high variance -->
                <rect x="490" y="150" width="20" height="30" fill="#e74c3c" opacity="0.8"/>
                <rect x="590" y="120" width="20" height="60" fill="#e74c3c" opacity="0.8"/>
                <rect x="690" y="150" width="20" height="30" fill="#e74c3c" opacity="0.8"/>

                <text x="500" y="145" text-anchor="middle" font-size="10" fill="#2c3e50">0.25</text>
                <text x="600" y="115" text-anchor="middle" font-size="10" fill="#2c3e50">0.5</text>
                <text x="700" y="145" text-anchor="middle" font-size="10" fill="#2c3e50">0.25</text>

                <text x="600" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">μ</text>

                <!-- Mean lines -->
                <line x1="200" y1="80" x2="200" y2="200" stroke="#f39c12" stroke-width="3"/>
                <line x1="600" y1="80" x2="600" y2="200" stroke="#f39c12" stroke-width="3"/>

                <!-- Variance illustration -->
                <text x="200" y="230" text-anchor="middle" font-size="12" fill="#2c3e50">Var(X) = Small</text>
                <text x="200" y="245" text-anchor="middle" font-size="11" fill="#2c3e50">Values close to mean</text>

                <text x="600" y="230" text-anchor="middle" font-size="12" fill="#2c3e50">Var(Y) = Large</text>
                <text x="600" y="245" text-anchor="middle" font-size="11" fill="#2c3e50">Values spread from mean</text>

                <!-- Arrows showing spread -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7"
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                <line x1="180" y1="210" x2="220" y2="210" stroke="#2c3e50" stroke-width="2" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
                <line x1="480" y1="210" x2="720" y2="210" stroke="#2c3e50" stroke-width="2" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
            </svg>
        </div>

        <div class="theorem-box">
            <h3>Computational Formula for Variance</h3>
            <p>Instead of computing $E[(X - \mu_X)^2]$ directly, use this easier formula:</p>
            <div class="formula-box">
                $$\text{Var}(X) = E[X^2] - (EX)^2$$
            </div>
            <p><strong>Proof:</strong></p>
            <div class="formula-box">
                $$\text{Var}(X) = E[(X - \mu_X)^2] = E[X^2 - 2\mu_X X + \mu_X^2]$$
                $$= E[X^2] - 2\mu_X E[X] + \mu_X^2 = E[X^2] - 2\mu_X^2 + \mu_X^2 = E[X^2] - \mu_X^2$$
            </div>
        </div>

        <div class="example-box">
            <h4>Example 3.19: Fair Die</h4>
            <p><strong>Problem:</strong> Roll a fair die. Let $X$ be the result. Find $EX$, $\text{Var}(X)$, and $\sigma_X$.</p>
            <p><strong>Solution:</strong> $R_X = \{1,2,3,4,5,6\}$ with $P_X(k) = \frac{1}{6}$ for each $k$.</p>
            <div class="formula-box">
                $$EX = \frac{1}{6}(1 + 2 + 3 + 4 + 5 + 6) = \frac{21}{6} = 3.5$$
                $$E[X^2] = \frac{1}{6}(1 + 4 + 9 + 16 + 25 + 36) = \frac{91}{6}$$
                $$\text{Var}(X) = \frac{91}{6} - \left(\frac{7}{2}\right)^2 = \frac{91}{6} - \frac{49}{4} = \frac{35}{12} \approx 2.92$$
                $$\sigma_X = \sqrt{\frac{35}{12}} \approx 1.71$$
            </div>
        </div>

        <div class="theorem-box">
            <h3>Theorem 3.3: Variance Properties</h3>
            <p><strong>Linear Transformation:</strong></p>
            <div class="formula-box">
                $$\text{Var}(aX + b) = a^2 \text{Var}(X)$$
                $$\text{SD}(aX + b) = |a| \text{SD}(X)$$
            </div>
            <p><strong>Sum of Independent Variables:</strong></p>
            <div class="formula-box">
                $$\text{If } X_1, X_2, \ldots, X_n \text{ are independent, then}$$
                $$\text{Var}(X_1 + X_2 + \cdots + X_n) = \text{Var}(X_1) + \text{Var}(X_2) + \cdots + \text{Var}(X_n)$$
            </div>
            <p><strong>Key Point:</strong> Unlike expectation, variance is NOT always linear!</p>
        </div>

        <div class="example-box">
            <h4>Example 3.20: Binomial Variance</h4>
            <p><strong>Problem:</strong> If $X \sim \text{Binomial}(n,p)$, find $\text{Var}(X)$.</p>
            <p><strong>Solution:</strong> Write $X = X_1 + X_2 + \cdots + X_n$ where $X_i \sim \text{Bernoulli}(p)$.</p>
            <p>For Bernoulli: $\text{Var}(X_i) = E[X_i^2] - (EX_i)^2 = p - p^2 = p(1-p)$</p>
            <div class="formula-box">
                $$\text{Var}(X) = \text{Var}(X_1) + \text{Var}(X_2) + \cdots + \text{Var}(X_n) = np(1-p)$$
            </div>
        </div>

        <h2>🎓 Key Takeaways</h2>

        <div class="property-grid">
            <div class="property-card">
                <div class="property-title">📈 CDF</div>
                <ul>
                    <li>Works for all types of random variables</li>
                    <li>Step function for discrete variables</li>
                    <li>Jump sizes equal PMF values</li>
                    <li>$P(a < X \leq b) = F_X(b) - F_X(a)$</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">🎯 Expectation</div>
                <ul>
                    <li>Weighted average of possible values</li>
                    <li>Linear operator: $E[aX + b] = aEX + b$</li>
                    <li>Use LOTUS for functions: $E[g(X)] = \sum g(x_k)P_X(x_k)$</li>
                    <li>Linearity makes calculations easier</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">📊 Variance</div>
                <ul>
                    <li>Measures spread around the mean</li>
                    <li>$\text{Var}(X) = E[X^2] - (EX)^2$</li>
                    <li>$\text{Var}(aX + b) = a^2\text{Var}(X)$</li>
                    <li>Additive for independent variables</li>
                </ul>
            </div>

            <div class="property-card">
                <div class="property-title">🔄 Functions</div>
                <ul>
                    <li>If $Y = g(X)$, then $Y$ is also a random variable</li>
                    <li>$P_Y(y) = \sum_{x: g(x)=y} P_X(x)$</li>
                    <li>LOTUS: $E[g(X)] = \sum g(x_k)P_X(x_k)$</li>
                    <li>No need to find PMF of $Y$ first!</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Study Strategy:</strong> Focus on understanding the concepts rather than memorizing formulas. The key insights are:</p>
            <ul>
                <li>CDF provides a universal way to describe any random variable</li>
                <li>Expectation is linear, which makes many calculations easier</li>
                <li>LOTUS lets you compute expectations of functions directly</li>
                <li>Variance measures spread and has special properties for independent variables</li>
            </ul>
        </div>

    </div>
</body>
</html>
