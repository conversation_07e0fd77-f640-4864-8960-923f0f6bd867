<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Taylor Series Expansions</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #eaecee;
            --text-color: #333;
            --link-color: #2980b9;
            --theorem-bg: #eaf2f8;
            --example-bg: #eafaf1;
            --note-bg: #fef9e7;
            --definition-bg: #f4ecf7;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1100px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        h1, h2, h3, h4 {
            color: var(--primary-color);
            margin-top: 1.5em;
        }
        
        h1 {
            color: var(--primary-color);
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 0.5em;
            border-bottom: 3px solid var(--secondary-color);
            padding-bottom: 0.3em;
        }
        
        h2 {
            color: var(--primary-color);
            border-bottom: 1px solid var(--secondary-color);
            padding-bottom: 0.2em;
        }
        
        h3 {
            color: var(--primary-color);
        }
        
        .theorem, .example, .note, .definition {
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .theorem {
            background-color: var(--theorem-bg);
            border-left: 5px solid var(--secondary-color);
        }
        
        .example {
            background-color: var(--example-bg);
            border-left: 5px solid #2ecc71;
        }
        
        .note {
            background-color: var(--note-bg);
            border-left: 5px solid #f1c40f;
        }
        
        .definition {
            background-color: var(--definition-bg);
            border-left: 5px solid #9b59b6;
        }
        
        .visualization {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            overflow-x: auto;
            margin: 20px 0;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }
        
        .toc {
            background-color: var(--dark-bg);
            padding: 20px;
            border-radius: 5px;
            margin: 30px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .toc h2 {
            margin-top: 0;
            border-bottom: 1px solid var(--secondary-color);
            padding-bottom: 10px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .toc a {
            color: var(--link-color);
            text-decoration: none;
            line-height: 1.8;
        }
        
        .toc a:hover {
            text-decoration: underline;
            color: var(--accent-color);
        }
        
        .step {
            counter-increment: step-counter;
            margin-bottom: 25px;
            padding: 10px;
            border-radius: 5px;
            background-color: var(--light-bg);
        }
        
        .step::before {
            content: "Step " counter(step-counter) ": ";
            font-weight: bold;
            color: var(--secondary-color);
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        
        .section-title::before {
            content: "";
            flex-grow: 0;
            width: 30px;
            height: 3px;
            background-color: var(--secondary-color);
            margin-right: 15px;
        }
        
        .section-title::after {
            content: "";
            flex-grow: 1;
            height: 1px;
            background-color: var(--secondary-color);
            margin-left: 15px;
        }
        
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: var(--secondary-color);
            color: white;
        }
        
        tr:nth-child(even) {
            background-color: var(--light-bg);
        }
        
        .highlight {
            background-color: #ffffcc;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            flex: 1 1 300px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            background-color: white;
        }
    </style>
    <!-- MathJax Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                tags: 'ams'
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <!-- Load MathJax -->
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
</head>
<body>
    <h1>Understanding Taylor Series Expansions</h1>
    
    <div class="definition">
        <p>A <strong>Taylor series expansion</strong> is a powerful mathematical technique that allows us to approximate complicated functions using polynomials. This tutorial will guide you through the various forms and applications of Taylor's theorem.</p>
    </div>

    <!-- Table of Contents will be added here -->
    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#introduction">1. Introduction to Taylor Series</a></li>
            <li><a href="#single-variable">2. Single Variable Taylor Expansions</a>
                <ul>
                    <li><a href="#basic-form">2.1 Basic Form</a></li>
                    <li><a href="#lagrange-form">2.2 Lagrange Form</a></li>
                    <li><a href="#remainder-bounds">2.3 Bounds on the Remainder</a></li>
                    <li><a href="#other-forms">2.4 Other Forms</a></li>
                </ul>
            </li>
            <li><a href="#multivariable">3. Multivariable Taylor Expansions</a>
                <ul>
                    <li><a href="#mv-basic-form">3.1 Basic Form</a></li>
                    <li><a href="#mv-lagrange-form">3.2 Lagrange Form</a></li>
                    <li><a href="#third-order">3.3 Third Order Expansions</a></li>
                    <li><a href="#vector-valued">3.4 Vector-Valued Functions</a></li>
                </ul>
            </li>
            <li><a href="#applications">4. Applications and Examples</a>
                <ul>
                    <li><a href="#statistics">4.1 Statistical Applications</a></li>
                    <li><a href="#visual-examples">4.2 Visual Examples</a></li>
                </ul>
            </li>
            <li><a href="#summary">5. Summary and Key Points</a></li>
        </ul>
    </div>

    <!-- Content sections will be added here -->
    <section id="introduction">
        <h2 class="section-title">1. Introduction to Taylor Series</h2>
        
        <p>The Taylor series expansion is a widely used method for approximating a complicated function by a polynomial. The idea is to use the function's derivatives at a specific point to construct a polynomial that matches the function's behavior near that point.</p>
        
        <div class="step">
            <p>Taylor series are named after the British mathematician Brook Taylor (1685-1731), although similar ideas were developed by other mathematicians including James Gregory and Colin Maclaurin.</p>
            
            <p>The key insight of Taylor series is that if we know a function's value and all its derivatives at a single point, we can reconstruct the function's behavior in a neighborhood around that point.</p>
        </div>
        
        <div class="step">
            <p>A Taylor series approximation becomes more accurate as we include more terms (higher-order derivatives). The approximation is exact at the expansion point and becomes less accurate as we move away from it.</p>
        </div>
        
        <div class="visualization">
            <svg width="650" height="400" viewBox="0 0 650 400">
                <!-- Coordinate system -->
                <line x1="50" y1="200" x2="600" y1="200" x2="200" stroke="black" stroke-width="1.5"/>
                <line x1="325" y1="50" x2="325" y1="350" x2="350" stroke="black" stroke-width="1.5"/>
                
                <!-- Axes labels -->
                <text x="610" y="200" font-family="Arial" font-size="16">x</text>
                <text x="330" y="40" font-family="Arial" font-size="16">f(x)</text>
                
                <!-- Original function: cos(x) -->
                <path d="M 50,100 C 100,80 150,120 200,200 C 250,280 300,320 325,200 C 350,80 400,40 450,200 C 500,360 550,320 600,200" 
                      fill="none" stroke="#2c3e50" stroke-width="3"/>
                
                <!-- Taylor polynomials -->
                <!-- 0th order (constant) -->
                <line x1="200" y1="200" x2="450" y1="200" x2="200" stroke="#e74c3c" stroke-width="2" stroke-dasharray="6,3"/>
                
                <!-- 1st order (linear) -->
                <line x1="200" y1="200" x2="450" y1="80" x2="80" stroke="#3498db" stroke-width="2" stroke-dasharray="4,2"/>
                
                <!-- 2nd order (quadratic) -->
                <path d="M 200,200 Q 262.5,80 325,200 Q 387.5,320 450,200" 
                      fill="none" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="2,1"/>
                
                <!-- Expansion point -->
                <circle cx="325" cy="200" r="6" fill="#9b59b6"/>
                <text x="315" y="225" font-family="Arial" font-size="14">x₀</text>
                
                <!-- Legend -->
                <rect x="450" y="80" width="180" height="120" fill="white" stroke="#95a5a6" stroke-width="1"/>
                
                <line x1="460" y1="100" x2="480" y1="100" x2="100" stroke="#2c3e50" stroke-width="3"/>
                <text x="490" y="105" font-family="Arial" font-size="12">Original Function</text>
                
                <line x1="460" y1="125" x2="480" y1="125" x2="125" stroke="#e74c3c" stroke-width="2" stroke-dasharray="6,3"/>
                <text x="490" y="130" font-family="Arial" font-size="12">0th Order (Constant)</text>
                
                <line x1="460" y1="150" x2="480" y1="150" x2="150" stroke="#3498db" stroke-width="2" stroke-dasharray="4,2"/>
                <text x="490" y="155" font-family="Arial" font-size="12">1st Order (Linear)</text>
                
                <line x1="460" y1="175" x2="480" y1="175" x2="175" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="2,1"/>
                <text x="490" y="180" font-family="Arial" font-size="12">2nd Order (Quadratic)</text>
                
                <!-- Annotations -->
                <text x="100" y="60" font-family="Arial" font-size="14" fill="#2c3e50">As we include more terms,</text>
                <text x="100" y="80" font-family="Arial" font-size="14" fill="#2c3e50">the approximation improves</text>
                
                <text x="450" y="300" font-family="Arial" font-size="14" fill="#e74c3c">Error increases</text>
                <text x="450" y="320" font-family="Arial" font-size="14" fill="#e74c3c">as we move away</text>
                <text x="450" y="340" font-family="Arial" font-size="14" fill="#e74c3c">from x₀</text>
            </svg>
        </div>
        
        <div class="step">
            <p>Taylor's theorem provides several different forms for expressing the approximation and quantifying the error (remainder term). The form you choose depends on:</p>
            <ul>
                <li>The smoothness of the function (how many derivatives exist)</li>
                <li>The region over which you need the approximation to be valid</li>
                <li>How precisely you need to bound the error</li>
            </ul>
        </div>
        
        <div class="note">
            <p>Throughout this tutorial, we'll explore different forms of Taylor's theorem, starting with the single variable case and then extending to multivariable functions. We'll also discuss applications, particularly in statistics.</p>
        </div>
    </section>

    <section id="single-variable">
        <h2 class="section-title">2. Single Variable Taylor Expansions</h2>
        
        <p>We'll start by exploring Taylor's theorem for functions of a single variable. This is the foundation for understanding more complex cases.</p>
        
        <section id="basic-form">
            <h3>2.1 Basic Form</h3>
            
            <div class="step">
                <p>The most basic statement of Taylor's theorem provides a way to approximate a function using its derivatives at a specific point.</p>
                
                <div class="theorem">
                    <h4>Theorem (Taylor - Basic Form)</h4>
                    <p>Suppose $n$ is a positive integer and $f: \mathbb{R} \rightarrow \mathbb{R}$ is $n$ times differentiable at a point $x_0$. Then:</p>
                    
                    <div class="formula-box">
                        $$f(x) = \sum_{k=0}^{n} \frac{f^{(k)}(x_0)}{k!}(x-x_0)^k + R_n(x,x_0)$$
                    </div>
                    
                    <p>where the remainder term $R_n$ satisfies:</p>
                    
                    <div class="formula-box">
                        $$R_n(x,x_0) = o(|x-x_0|^n) \text{ as } x \rightarrow x_0$$
                    </div>
                    
                    <p>This little-o notation means that the error term approaches zero faster than $|x-x_0|^n$ as $x$ approaches $x_0$.</p>
                </div>
            </div>
            
            <div class="step">
                <p>Let's break down what this means:</p>
                <ul>
                    <li>The first term ($k=0$) is just the function value at $x_0$: $f(x_0)$</li>
                    <li>The second term ($k=1$) involves the first derivative: $f'(x_0)(x-x_0)$</li>
                    <li>The third term ($k=2$) involves the second derivative: $\frac{f''(x_0)}{2}(x-x_0)^2$</li>
                    <li>And so on for higher derivatives</li>
                </ul>
                <p>The remainder term $R_n$ represents the error in our approximation.</p>
            </div>
            
            <div class="example">
                <h4>Example: Taylor Expansion of $e^x$ at $x_0 = 0$</h4>
                <p>Let's find the Taylor expansion of $f(x) = e^x$ centered at $x_0 = 0$:</p>
                <ul>
                    <li>$f(0) = e^0 = 1$</li>
                    <li>$f'(x) = e^x$ so $f'(0) = e^0 = 1$</li>
                    <li>$f''(x) = e^x$ so $f''(0) = e^0 = 1$</li>
                    <li>$f^{(3)}(x) = e^x$ so $f^{(3)}(0) = e^0 = 1$</li>
                    <li>And so on...</li>
                </ul>
                <p>Substituting into the formula:</p>
                <div class="formula-box">
                    \begin{align}
                    e^x &= \sum_{k=0}^{n} \frac{f^{(k)}(0)}{k!}x^k + R_n(x,0) \\
                    &= \frac{1}{0!} + \frac{1}{1!}x + \frac{1}{2!}x^2 + \frac{1}{3!}x^3 + \ldots + \frac{1}{n!}x^n + R_n(x,0) \\
                    &= 1 + x + \frac{x^2}{2} + \frac{x^3}{6} + \ldots + \frac{x^n}{n!} + R_n(x,0)
                    \end{align}
                </div>
                <p>As $n$ approaches infinity, the remainder term approaches zero for all $x$, giving us the familiar infinite series for $e^x$.</p>
            </div>
        </section>
        
        <section id="lagrange-form">
            <h3>2.2 Lagrange Form</h3>
            
            <div class="step">
                <p>The Lagrange form of Taylor's theorem gives us a more explicit expression for the remainder term.</p>
                
                <div class="theorem">
                    <h4>Theorem (Taylor - Lagrange Form)</h4>
                    <p>If $f^{(n+1)}$ exists over an open interval containing $(x,x_0)$, then there exists a point $\bar{x}$ between $x$ and $x_0$ such that:</p>
                    
                    <div class="formula-box">
                        $$R_n(x,x_0) = \frac{f^{(n+1)}(\bar{x})}{(n+1)!}(x-x_0)^{n+1}$$
                    </div>
                    
                    <p>This is also known as the "mean-value form" because it relies on the mean value theorem in its proof.</p>
                </div>
            </div>
            
            <div class="note">
                <h4>Important Note on Conditions</h4>
                <p>The conditions for the Lagrange form are sometimes presented differently:</p>
                <ul>
                    <li>Some sources require $f^{(n+1)}$ to be continuous, not just exist</li>
                    <li>Some specify that $f^{(n+1)}$ need not exist at the boundary points $x$ and $x_0$</li>
                    <li>Some require $f^{(n)}$ to be continuous on the closed interval between $x$ and $x_0$</li>
                </ul>
                <p>The form presented here is simpler but sufficient for most applications.</p>
            </div>
            
            <div class="step">
                <p>Let's compare the Basic and Lagrange forms for a second-order expansion:</p>
                
                <div class="formula-box">
                    \begin{align}
                    \text{(Basic) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(x_0)(x-x_0)^2 + o(|x-x_0|^2) \\
                    \text{(Lagrange) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(\bar{x})(x-x_0)^2
                    \end{align}
                </div>
                
                <p>In the Lagrange form, we have a simpler expression for the remainder, but it requires $f''$ to exist along the entire interval from $x$ to $x_0$, not just at $x_0$. Also, the second derivative is evaluated at some intermediate point $\bar{x}$ which generally cannot be determined exactly.</p>
            </div>
        </section>
        
        <section id="remainder-bounds">
            <h3>2.3 Bounds on the Remainder</h3>
            
            <div class="step">
                <p>If we can bound the derivative over the interval, we can bound the remainder term.</p>
                
                <div class="theorem">
                    <h4>Theorem (Lagrange Error Bound)</h4>
                    <p>If $f^{(n+1)}$ is continuous over an open interval containing $(x,x_0)$ and there exists $M$ such that $|f^{(n+1)}(a)| \leq M$ for all $a \in (x,x_0)$, then:</p>
                    
                    <div class="formula-box">
                        $$|R_n(x,x_0)| \leq \frac{M}{(n+1)!}|x-x_0|^{n+1}$$
                    </div>
                </div>
                
                <p>This allows us to put a concrete upper bound on the error of our approximation.</p>
            </div>
            
            <div class="step">
                <p>Using this bound, Taylor's theorem can be expressed using big-O notation:</p>
                
                <div class="formula-box">
                    \begin{align}
                    \text{(Basic) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(x_0)(x-x_0)^2 + o(|x-x_0|^2) \\
                    \text{(Big O) } f(x) &= f(x_0) + f'(x_0)(x-x_0) + \frac{1}{2}f''(x_0)(x-x_0)^2 + O(|x-x_0|^3)
                    \end{align}
                </div>
                
                <p>The big-O notation indicates that the error term is at most a constant times $|x-x_0|^3$ for $x$ close enough to $x_0$.</p>
            </div>
            
            <div class="example">
                <h4>Example: Error Bound for $\sin(x)$ at $x_0 = 0$</h4>
                <p>Let's find the error bound for the second-order Taylor approximation of $\sin(x)$ at $x_0 = 0$.</p>
                <p>For $f(x) = \sin(x)$:</p>
                <ul>
                    <li>$f(0) = \sin(0) = 0$</li>
                    <li>$f'(x) = \cos(x)$ so $f'(0) = \cos(0) = 1$</li>
                    <li>$f''(x) = -\sin(x)$ so $f''(0) = -\sin(0) = 0$</li>
                    <li>$f^{(3)}(x) = -\cos(x)$ so $f^{(3)}(a)$ is bounded by 1 for all $a$</li>
                </ul>
                <p>The second-order Taylor approximation is:</p>
                <div class="formula-box">
                    $$\sin(x) \approx 0 + 1 \cdot x + \frac{0}{2}x^2 = x$$
                </div>
                <p>And the error bound is:</p>
                <div class="formula-box">
                    $$|R_2(x,0)| \leq \frac{1}{3!}|x|^3 = \frac{|x|^3}{6}$$
                </div>
                <p>So for $x = 0.1$, the error is at most $\frac{0.1^3}{6} \approx 0.000167$.</p>
            </div>
        </section>
        
        <section id="other-forms">
            <h3>2.4 Other Forms</h3>
            
            <div class="step">
                <p>There are several other ways to express the remainder term in Taylor's theorem.</p>
                
                <div class="theorem">
                    <h4>Theorem (Integral Form)</h4>
                    <p>If $f^{(n+1)}$ is continuous over an open interval containing $(x,x_0)$, then:</p>
                    
                    <div class="formula-box">
                        $$R_n(x,x_0) = \int_{x_0}^{x} \frac{f^{(n+1)}(t)}{n!}(x-t)^n dt$$
                    </div>
                </div>
                
                <p>This form is particularly useful in certain theoretical analyses and when working with specific types of functions.</p>
            </div>
            
            <div class="note">
                <p>Other forms of the remainder include:</p>
                <ul>
                    <li><strong>Cauchy form</strong>: Expresses the remainder using a parameter $\theta \in (0,1)$</li>
                    <li><strong>Roche-Schlömilch form</strong>: A generalization that's useful in certain advanced applications</li>
                </ul>
                <p>These forms are less commonly used but can be valuable in specific contexts.</p>
            </div>
        </section>
    </section>

    <div class="visualization">
        <svg width="650" height="450" viewBox="0 0 650 450">
            <!-- Coordinate system -->
            <line x1="50" y1="225" x2="600" y1="225" x2="225" stroke="black" stroke-width="1.5"/>
            <line x1="325" y1="50" x2="325" y1="400" x2="400" stroke="black" stroke-width="1.5"/>
            
            <!-- Grid lines (light) -->
            <g stroke="#ddd" stroke-width="0.5">
                <!-- Vertical grid lines -->
                <line x1="125" y1="50" x2="125" y1="400"/>
                <line x1="225" y1="50" x2="225" y1="400"/>
                <line x1="425" y1="50" x2="425" y1="400"/>
                <line x1="525" y1="50" x2="525" y1="400"/>
                
                <!-- Horizontal grid lines -->
                <line x1="50" y1="125" x2="600" y1="125"/>
                <line x1="50" y1="325" x2="600" y1="325"/>
            </g>
            
            <!-- Axes labels -->
            <text x="610" y="225" font-family="Arial" font-size="16">x</text>
            <text x="330" y="40" font-family="Arial" font-size="16">f(x)</text>
            <text x="315" y="245" font-family="Arial" font-size="12">0</text>
            
            <!-- Function: sin(x) -->
            <path d="M 50,225 Q 100,125 150,225 Q 200,325 250,225 Q 300,125 350,225 Q 400,325 450,225 Q 500,125 550,225 Q 600,325 650,225" 
                  fill="none" stroke="#2c3e50" stroke-width="3"/>
            
            <!-- Taylor approximations at x₀ = 0 -->
            <!-- 1st order: sin(x) ≈ x -->
            <path d="M 125,325 L 525,125" 
                  fill="none" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,3"/>
            
            <!-- 3rd order: sin(x) ≈ x - x³/6 -->
            <path d="M 125,325 Q 225,125 325,225 Q 425,325 525,125" 
                  fill="none" stroke="#3498db" stroke-width="2.5" stroke-dasharray="3,2"/>
            
            <!-- 5th order: sin(x) ≈ x - x³/6 + x⁵/120 -->
            <path d="M 125,325 C 175,200 275,150 325,225 C 375,300 475,250 525,125" 
                  fill="none" stroke="#2ecc71" stroke-width="2" stroke-dasharray="2,1"/>
            
            <!-- Error visualization for 1st order -->
            <path d="M 400,325 L 400,270" stroke="#e74c3c" stroke-width="1.5" stroke-dasharray="2,1"/>
            <text x="405" y="300" font-family="Arial" font-size="12" fill="#e74c3c">Error</text>
            
            <!-- Expansion point -->
            <circle cx="325" cy="225" r="5" fill="#9b59b6"/>
            <text x="335" y="215" font-family="Arial" font-size="14">x₀</text>
            
            <!-- Legend -->
            <rect x="425" y="50" width="170" height="110" fill="white" stroke="#95a5a6" stroke-width="1"/>
            
            <line x1="435" y1="70" x2="455" y1="70" x2="70" stroke="#2c3e50" stroke-width="3"/>
            <text x="465" y="75" font-family="Arial" font-size="12">sin(x)</text>
            
            <line x1="435" y1="95" x2="455" y1="95" x2="95" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,3"/>
            <text x="465" y="100" font-family="Arial" font-size="12">1st order: x</text>
            
            <line x1="435" y1="120" x2="455" y1="120" x2="120" stroke="#3498db" stroke-width="2.5" stroke-dasharray="3,2"/>
            <text x="465" y="125" font-family="Arial" font-size="12">3rd order: x - x³/6</text>
            
            <line x1="435" y1="145" x2="455" y1="145" x2="145" stroke="#2ecc71" stroke-width="2" stroke-dasharray="2,1"/>
            <text x="465" y="150" font-family="Arial" font-size="12">5th order</text>
            
            <!-- Annotations -->
            <text x="100" y="70" font-family="Arial" font-size="14" fill="#2c3e50">Higher-order approximations</text>
            <text x="100" y="90" font-family="Arial" font-size="14" fill="#2c3e50">match the function better</text>
            <text x="100" y="110" font-family="Arial" font-size="14" fill="#2c3e50">over larger intervals</text>
            
            <!-- Remainder visualization -->
            <g transform="translate(500, 350)">
                <rect x="0" y="0" width="100" height="50" fill="white" stroke="#95a5a6" stroke-width="1"/>
                <text x="10" y="20" font-family="Arial" font-size="12">Remainder term</text>
                <text x="10" y="40" font-family="Arial" font-size="12">decreases with order</text>
            </g>
            
            <!-- Arrow pointing to error -->
            <path d="M 500,350 L 450,300" stroke="#e74c3c" stroke-width="1" fill="none" marker-end="url(#arrowhead)"/>
            
            <!-- Arrow marker definition -->
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                </marker>
            </defs>
        </svg>
    </div>
    
    <div class="note">
        <p>The visualization above shows how Taylor approximations of different orders approximate the sine function around $x_0 = 0$. Notice how:</p>
        <ul>
            <li>The first-order approximation (linear) is accurate near $x_0$ but quickly diverges</li>
            <li>The third-order approximation provides much better accuracy over a wider range</li>
            <li>The fifth-order approximation is nearly indistinguishable from the original function in this range</li>
        </ul>
        <p>This illustrates the key principle: higher-order Taylor approximations provide better accuracy over larger intervals.</p>
    </div>

    <section id="multivariable">
        <h2 class="section-title">3. Multivariable Taylor Expansions</h2>
        
        <p>Now we'll extend Taylor's theorem to functions of multiple variables. The core concepts remain the same, but the notation becomes more complex as we need to work with vectors, gradients, and matrices.</p>
        
        <section id="mv-basic-form">
            <h3>3.1 Basic Form</h3>
            
            <div class="step">
                <p>For a function of multiple variables, the basic form of Taylor's theorem uses vector and matrix notation.</p>
                
                <div class="theorem">
                    <h4>Theorem (First-Order Taylor Approximation)</h4>
                    <p>Suppose $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is differentiable at a point $x_0$. Then:</p>
                    
                    <div class="formula-box">
                        $$f(x) = f(x_0) + \nabla f(x_0)^\top (x-x_0) + o(\|x-x_0\|)$$
                    </div>
                    
                    <p>where $\nabla f(x_0)$ is the gradient of $f$ at $x_0$, and $\|x-x_0\|$ denotes the Euclidean norm of the vector $x-x_0$.</p>
                </div>
            </div>
            
            <div class="step">
                <p>For the second-order approximation, we incorporate the Hessian matrix:</p>
                
                <div class="theorem">
                    <h4>Theorem (Second-Order Taylor Approximation)</h4>
                    <p>Suppose $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is twice differentiable at a point $x_0$. Then:</p>
                    
                    <div class="formula-box">
                        $$f(x) = f(x_0) + \nabla f(x_0)^\top (x-x_0) + \frac{1}{2}(x-x_0)^\top \nabla^2 f(x_0) (x-x_0) + o(\|x-x_0\|^2)$$
                    </div>
                    
                    <p>where $\nabla^2 f(x_0)$ is the Hessian matrix of $f$ at $x_0$ (the matrix of second partial derivatives).</p>
                </div>
            </div>
            
            <div class="example">
                <h4>Example: Quadratic Approximation</h4>
                <p>Consider the function $f(x,y) = e^{x+y}$ at the point $(0,0)$.</p>
                <p>We compute:</p>
                <ul>
                    <li>$f(0,0) = e^0 = 1$</li>
                    <li>$\frac{\partial f}{\partial x} = e^{x+y}$ so $\frac{\partial f}{\partial x}(0,0) = 1$</li>
                    <li>$\frac{\partial f}{\partial y} = e^{x+y}$ so $\frac{\partial f}{\partial y}(0,0) = 1$</li>
                    <li>$\frac{\partial^2 f}{\partial x^2} = e^{x+y}$ so $\frac{\partial^2 f}{\partial x^2}(0,0) = 1$</li>
                    <li>$\frac{\partial^2 f}{\partial y^2} = e^{x+y}$ so $\frac{\partial^2 f}{\partial y^2}(0,0) = 1$</li>
                    <li>$\frac{\partial^2 f}{\partial x \partial y} = e^{x+y}$ so $\frac{\partial^2 f}{\partial x \partial y}(0,0) = 1$</li>
                </ul>
                <p>The gradient at $(0,0)$ is $\nabla f(0,0) = (1,1)^\top$, and the Hessian matrix is:</p>
                <div class="formula-box">
                    $$\nabla^2 f(0,0) = \begin{pmatrix} 1 & 1 \\ 1 & 1 \end{pmatrix}$$
                </div>
                <p>So the second-order Taylor approximation is:</p>
                <div class="formula-box">
                    \begin{align}
                    f(x,y) &\approx 1 + (1,1) \begin{pmatrix} x \\ y \end{pmatrix} + \frac{1}{2} \begin{pmatrix} x & y \end{pmatrix} \begin{pmatrix} 1 & 1 \\ 1 & 1 \end{pmatrix} \begin{pmatrix} x \\ y \end{pmatrix} \\
                    &= 1 + x + y + \frac{1}{2}(x^2 + 2xy + y^2) \\
                    &= 1 + x + y + \frac{1}{2}(x+y)^2
                    \end{align}
                </div>
                <p>This approximation is quite accurate near $(0,0)$.</p>
            </div>
        </section>
        
        <section id="mv-lagrange-form">
            <h3>3.2 Lagrange Form</h3>
            
            <div class="step">
                <p>The Lagrange form also extends to multivariable functions.</p>
                
                <div class="note">
                    <p>For the following theorems, "$\bar{x}$ on the line segment connecting $x$ and $x_0$" means there exists $w \in [0,1]$ such that $\bar{x} = wx + (1-w)x_0$. We'll also use the notation $N_r(x_0)$ to denote a neighborhood of $x_0$ with radius $r$.</p>
                </div>
                
                <div class="theorem">
                    <h4>Theorem (First-Order Lagrange Form)</h4>
                    <p>Suppose $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is differentiable on $N_r(x_0)$. Then for any $x \in N_r(x_0)$, there exists $\bar{x}$ on the line segment connecting $x$ and $x_0$ such that:</p>
                    
                    <div class="formula-box">
                        $$f(x) = f(x_0) + \nabla f(\bar{x})^\top (x-x_0)$$
                    </div>
                </div>
            </div>
            
            <div class="step">
                <p>For the second-order case:</p>
                
                <div class="theorem">
                    <h4>Theorem (Second-Order Lagrange Form)</h4>
                    <p>Suppose $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is twice differentiable on $N_r(x_0)$. Then for any $x \in N_r(x_0)$, there exists $\bar{x}$ on the line segment connecting $x$ and $x_0$ such that:</p>
                    
                    <div class="formula-box">
                        $$f(x) = f(x_0) + \nabla f(x_0)^\top (x-x_0) + \frac{1}{2}(x-x_0)^\top \nabla^2 f(\bar{x}) (x-x_0)$$
                    </div>
                </div>
                
                <p>Notice that in this form, the gradient is still evaluated at $x_0$, but the Hessian is evaluated at some intermediate point $\bar{x}$.</p>
            </div>
        </section>
        
        <section id="third-order">
            <h3>3.3 Third Order Expansions</h3>
            
            <div class="step">
                <p>Higher-order expansions for multivariable functions become increasingly complex in notation.</p>
                
                <div class="theorem">
                    <h4>Theorem (Third-Order Taylor Expansion)</h4>
                    <p>Suppose $f: \mathbb{R}^d \rightarrow \mathbb{R}$ is three times differentiable on $N_r(x_0)$. Then for any $x \in N_r(x_0)$, there exists $\bar{x}$ on the line segment connecting $x$ and $x_0$ such that:</p>
                    
                    <div class="formula-box">
                        \begin{align}
                        f(x) = f(x_0) &+ \sum_{j=1}^{d}\frac{\partial f(x_0)}{\partial x_j}(x_j-x_{0j}) \\
                        &+ \frac{1}{2}\sum_{j=1}^{d}\sum_{k=1}^{d}\frac{\partial^2 f(x_0)}{\partial x_j \partial x_k}(x_j-x_{0j})(x_k-x_{0k}) \\
                        &+ \frac{1}{6}\sum_{j=1}^{d}\sum_{k=1}^{d}\sum_{\ell=1}^{d}\frac{\partial^3 f(\bar{x})}{\partial x_j \partial x_k \partial x_\ell}(x_j-x_{0j})(x_k-x_{0k})(x_\ell-x_{0\ell})
                        \end{align}
                    </div>
                    
                    <p>where $\frac{\partial f(x_0)}{\partial x_j}$ is shorthand for $\frac{\partial f(x)}{\partial x_j}$ evaluated at $x_0$.</p>
                </div>
                
                <p>This expansion becomes quite cumbersome for higher orders, which is why we typically use tensor notation or other compact forms for higher-order terms.</p>
            </div>
        </section>
        
        <section id="vector-valued">
            <h3>3.4 Vector-Valued Functions</h3>
            
            <div class="step">
                <p>For vector-valued functions (functions that output vectors), Taylor's theorem needs further adaptation.</p>
                
                <div class="note">
                    <p>An important limitation: Lagrange-type results do not hold for vector-valued functions in the same way they do for scalar functions. In other words, it is not true that there exists a single point $\bar{x}$ such that:</p>
                    <div class="formula-box">
                        $$f(x) = f(x_0) + \nabla f(\bar{x})^\top (x-x_0)$$
                    </div>
                    <p>Such a point exists for each component function separately, but these points will generally be different.</p>
                </div>
            </div>
            
            <div class="step">
                <p>However, big-O versions of Taylor's theorem still apply to vector-valued functions:</p>
                
                <div class="theorem">
                    <h4>Theorem (Taylor for Vector-Valued Functions)</h4>
                    <p>Suppose $f: \mathbb{R}^d \rightarrow \mathbb{R}^k$ is twice differentiable on $N_r(x_0)$, and that $\nabla^2 f$ is bounded on $N_r(x_0)$. Then for any $x \in N_r(x_0)$:</p>
                    
                    <div class="formula-box">
                        $$f(x) = f(x_0) + [\nabla f(x_0) + O(\|x-x_0\|)]^\top (x-x_0)$$
                    </div>
                    
                    <p>where $O(\cdot)$ applies to each element of the $d \times k$ matrix.</p>
                </div>
            </div>
            
            <div class="example">
                <h4>Proof Sketch</h4>
                <p>For any individual component $j$ of $f$, there exists $\bar{x}_j$ on the line segment connecting $x$ and $x_0$ such that:</p>
                
                <div class="formula-box">
                    \begin{align}
                    f_j(x) &= f_j(x_0) + \nabla f_j(x_0)^\top(x-x_0) + \frac{1}{2}(x-x_0)^\top \nabla^2 f_j(\bar{x}_j)(x-x_0) \\
                    &= f_j(x_0) + \left[\nabla f_j(x_0) + \frac{1}{2}\nabla^2 f_j(\bar{x}_j)(x-x_0)\right]^\top(x-x_0) \\
                    &= f_j(x_0) + [\nabla f_j(x_0) + O(1)(x-x_0)]^\top(x-x_0)
                    \end{align}
                </div>
                
                <p>because $\nabla^2 f$ is bounded. Stacking these individual equations into a system of equations, we obtain the result stated in the theorem.</p>
            </div>
        </section>
    </section>

    <div class="visualization">
        <svg width="650" height="450" viewBox="0 0 650 450">
            <!-- 3D Coordinate system -->
            <g transform="translate(325, 225)">
                <!-- Axes -->
                <line x1="-275" y1="0" x2="275" y1="0" x2="0" stroke="black" stroke-width="1.5"/>
                <line x1="0" y1="-175" x2="0" y1="175" x2="175" stroke="black" stroke-width="1.5"/>
                <line x1="0" y1="0" x2="150" y1="100" x2="100" stroke="black" stroke-width="1.5" stroke-dasharray="4,2"/>
                
                <!-- Axes labels -->
                <text x="285" y="0" font-family="Arial" font-size="16">x</text>
                <text x="0" y="-185" font-family="Arial" font-size="16">z</text>
                <text x="160" y="110" font-family="Arial" font-size="16">y</text>
                <text x="10" y="20" font-family="Arial" font-size="12">(0,0,0)</text>
                
                <!-- Grid (subtle) -->
                <g stroke="#ddd" stroke-width="0.5">
                    <!-- x-y plane grid -->
                    <line x1="-200" y1="50" x2="100" y1="50" x2="50"/>
                    <line x1="-200" y1="100" x2="50" y1="100" x2="100"/>
                    <line x1="-100" y1="0" x2="-50" y1="100" x2="100"/>
                    <line x1="100" y1="0" x2="150" y1="100" x2="100"/>
                </g>
                
                <!-- Original function surface (simplified representation) -->
                <!-- Representing f(x,y) = x^2 + y^2 as a paraboloid -->
                <path d="M -200,0 Q -150,-100 -100,0 Q -50,100 0,0 Q 50,-100 100,0 Q 150,100 200,0" 
                      fill="none" stroke="#2c3e50" stroke-width="3" transform="translate(0, 100)"/>
                <path d="M -200,0 Q -150,-100 -100,0 Q -50,100 0,0 Q 50,-100 100,0 Q 150,100 200,0" 
                      fill="none" stroke="#2c3e50" stroke-width="3" transform="translate(0, 50)"/>
                <path d="M -200,0 Q -150,-100 -100,0 Q -50,100 0,0 Q 50,-100 100,0 Q 150,100 200,0" 
                      fill="none" stroke="#2c3e50" stroke-width="3"/>
                <path d="M -200,0 Q -150,-100 -100,0 Q -50,100 0,0 Q 50,-100 100,0 Q 150,100 200,0" 
                      fill="none" stroke="#2c3e50" stroke-width="3" transform="translate(0, -50)"/>
                <path d="M -200,0 Q -150,-100 -100,0 Q -50,100 0,0 Q 50,-100 100,0 Q 150,100 200,0" 
                      fill="none" stroke="#2c3e50" stroke-width="3" transform="translate(0, -100)"/>
                
                <!-- Expansion point -->
                <circle cx="50" cy="50" r="6" fill="#9b59b6"/>
                <text x="60" y="40" font-family="Arial" font-size="14">x₀</text>
                
                <!-- First-order approximation (tangent plane) -->
                <polygon points="0,0 100,0 100,100 0,100" 
                         fill="#3498db" fill-opacity="0.2" stroke="#3498db" stroke-width="1.5"
                         transform="translate(0, -50) rotate(20, 50, 50)"/>
                
                <!-- Second-order approximation (quadratic surface) -->
                <path d="M 0,0 Q 25,-25 50,0 Q 75,25 100,0" 
                      fill="none" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="3,2"
                      transform="translate(0, -50) rotate(20, 50, 50)"/>
                <path d="M 0,25 Q 25,0 50,25 Q 75,50 100,25" 
                      fill="none" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="3,2"
                      transform="translate(0, -50) rotate(20, 50, 50)"/>
                <path d="M 0,50 Q 25,25 50,50 Q 75,75 100,50" 
                      fill="none" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="3,2"
                      transform="translate(0, -50) rotate(20, 50, 50)"/>
                <path d="M 0,75 Q 25,50 50,75 Q 75,100 100,75" 
                      fill="none" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="3,2"
                      transform="translate(0, -50) rotate(20, 50, 50)"/>
                <path d="M 0,100 Q 25,75 50,100 Q 75,125 100,100" 
                      fill="none" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="3,2"
                      transform="translate(0, -50) rotate(20, 50, 50)"/>
                
                <!-- Legend -->
                <rect x="125" y="-175" width="150" height="100" fill="white" stroke="#95a5a6" stroke-width="1"/>
                
                <line x1="135" y1="-155" x2="155" y1="-155" x2="-155" stroke="#2c3e50" stroke-width="3"/>
                <text x="165" y="-150" font-family="Arial" font-size="12">Original Function</text>
                
                <rect x="135" y1="-130" width="20" height="10" fill="#3498db" fill-opacity="0.2" stroke="#3498db" stroke-width="1.5"/>
                <text x="165" y="-125" font-family="Arial" font-size="12">First-order (Plane)</text>
                
                <line x1="135" y1="-105" x2="155" y1="-105" x2="-105" stroke="#2ecc71" stroke-width="2.5" stroke-dasharray="3,2"/>
                <text x="165" y="-100" font-family="Arial" font-size="12">Second-order (Quadratic)</text>
            </g>
        </svg>
    </div>
    
    <div class="note">
        <p>The visualization above shows a multivariable function (represented as a surface in 3D space) and its Taylor approximations:</p>
        <ul>
            <li>The <strong>first-order approximation</strong> is a tangent plane at the expansion point $x_0$</li>
            <li>The <strong>second-order approximation</strong> is a quadratic surface that matches the function's curvature at $x_0$</li>
        </ul>
        <p>Just as in the single-variable case, higher-order approximations provide better accuracy near the expansion point.</p>
    </div>

    <section id="applications">
        <h2 class="section-title">4. Applications and Examples</h2>
        
        <p>Taylor series expansions have numerous applications across mathematics, physics, engineering, and statistics. Let's explore some key applications, with a focus on statistical examples.</p>
        
        <section id="statistics">
            <h3>4.1 Statistical Applications</h3>
            
            <div class="step">
                <p>In statistics, Taylor expansions are frequently used in asymptotic theory, particularly when analyzing estimators.</p>
                
                <div class="theorem">
                    <h4>Theorem (Taylor Expansion for Statistical Estimators)</h4>
                    <p>Suppose regularity conditions (A)-(C) are met. Then for any consistent estimator $\hat{\theta}$, we have:</p>
                    
                    <div class="formula-box">
                        $$\frac{1}{n}u(\hat{\theta}) = \frac{1}{n}u(\theta^*) - \{I(\theta^*) + o_p(1)\}\sqrt{n}(\hat{\theta} - \theta^*)$$
                    </div>
                    
                    <p>If $\hat{\theta}$ is $\sqrt{n}$-consistent, then:</p>
                    
                    <div class="formula-box">
                        $$\frac{1}{n}u(\hat{\theta}) = \frac{1}{n}u(\theta^*) - I(\theta^*)\sqrt{n}(\hat{\theta} - \theta^*) + o_p(1)$$
                    </div>
                    
                    <p>where $u$ is the score function and $I$ is the Fisher information.</p>
                </div>
            </div>
            
            <div class="step">
                <p>Let's see how this result is derived using Taylor expansion:</p>
                
                <div class="example">
                    <h4>Proof Sketch</h4>
                    <p>Taking Taylor series expansions of the contributions to the score vector, we have:</p>
                    
                    <div class="formula-box">
                        $$u_i(\hat{\theta}) = u_i(\theta^*) - [I_i(\theta^*) + M(x_i)O(\|\hat{\theta}-\theta^*\|)]^\top (\hat{\theta}-\theta^*)$$
                    </div>
                    
                    <p>Summing these contributions and dividing by $n$:</p>
                    
                    <div class="formula-box">
                        $$\frac{1}{n}u(\hat{\theta}) = \frac{1}{n}u(\theta^*) - \left[\frac{1}{n}I_n(\theta^*) + \left\{\frac{1}{n}\sum_{i=1}^{n}M(x_i)\right\}O(\|\hat{\theta}-\theta^*\|)\right]^\top \sqrt{n}(\hat{\theta}-\theta^*)$$
                    </div>
                    
                    <p>Finally, note that:</p>
                    <ul>
                        <li>$\frac{1}{n}I_n(\theta^*) \xrightarrow{p} I(\theta^*)$ by the Fisher information theorem</li>
                        <li>$\frac{1}{n}\sum M(x_i) = O_p(1)$ by condition C(iii)</li>
                        <li>$\|\hat{\theta}-\theta^*\| = o_p(1)$ because $\hat{\theta}$ is consistent</li>
                    </ul>
                    
                    <p>Thus, the rules of O-notation tell us that the entire term inside the square brackets is converging to $I(\theta^*)$ in probability.</p>
                    
                    <p>Finally, if $\hat{\theta}$ is $\sqrt{n}$-consistent, then $\sqrt{n}(\hat{\theta}-\theta^*)$ is $O_p(1)$ and $o_p(1)\sqrt{n}(\hat{\theta}-\theta^*) = o_p(1)$.</p>
                </div>
            </div>
            
            <div class="note">
                <h4>Corollary</h4>
                <p>Similarly, for any two consistent estimators $\hat{\theta}_1$ and $\hat{\theta}_2$, we have:</p>
                
                <div class="formula-box">
                    $$\frac{1}{n}u(\hat{\theta}_1) = \frac{1}{n}u(\hat{\theta}_2) - \{I(\theta^*) + o_p(1)\}\sqrt{n}(\hat{\theta}_1 - \hat{\theta}_2)$$
                </div>
                
                <p>If both estimators are $\sqrt{n}$-consistent, this simplifies further.</p>
            </div>
        </section>
        
        <section id="visual-examples">
            <h3>4.2 Visual Examples</h3>
            
            <div class="step">
                <p>Taylor series are widely used in many practical applications:</p>
                
                <div class="container">
                    <div class="card">
                        <h4>Numerical Methods</h4>
                        <ul>
                            <li>Approximating complex functions</li>
                            <li>Solving differential equations</li>
                            <li>Numerical integration techniques</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Physics & Engineering</h4>
                        <ul>
                            <li>Approximating physical laws near equilibrium</li>
                            <li>Small-signal analysis in electronics</li>
                            <li>Perturbation theory in quantum mechanics</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h4>Machine Learning</h4>
                        <ul>
                            <li>Optimization algorithms (gradient descent)</li>
                            <li>Understanding neural network behavior</li>
                            <li>Regularization techniques</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="visualization">
                <svg width="650" height="300" viewBox="0 0 650 300">
                    <!-- Title -->
                    <text x="325" y="30" font-family="Arial" font-size="18" text-anchor="middle" font-weight="bold">Common Functions and Their Taylor Approximations</text>
                    
                    <!-- Function 1: e^x -->
                    <g transform="translate(100, 150)">
                        <text x="0" y="-80" font-family="Arial" font-size="16" text-anchor="middle">e^x ≈ 1 + x + x²/2 + x³/6 + ...</text>
                        
                        <!-- Axes -->
                        <line x1="-80" y1="0" x2="80" y1="0" x2="0" stroke="black" stroke-width="1"/>
                        <line x1="0" y1="80" x2="0" y1="-80" x2="-80" stroke="black" stroke-width="1"/>
                        
                        <!-- Original function -->
                        <path d="M -80,75 C -60,50 -40,30 -20,15 C -10,7 0,0 10,-7 C 30,-25 50,-50 80,-80" 
                              fill="none" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Approximations -->
                        <line x1="-80" y1="0" x2="80" y1="0" x2="0" stroke="#e74c3c" stroke-width="1.5" stroke-dasharray="5,2"/>
                        <path d="M -80,80 L 80,-80" fill="none" stroke="#3498db" stroke-width="1.5" stroke-dasharray="3,2"/>
                        <path d="M -80,40 Q 0,-40 80,-80" fill="none" stroke="#2ecc71" stroke-width="1.5" stroke-dasharray="2,1"/>
                    </g>
                    
                    <!-- Function 2: sin(x) -->
                    <g transform="translate(325, 150)">
                        <text x="0" y="-80" font-family="Arial" font-size="16" text-anchor="middle">sin(x) ≈ x - x³/6 + x⁵/120 - ...</text>
                        
                        <!-- Axes -->
                        <line x1="-80" y1="0" x2="80" y1="0" x2="0" stroke="black" stroke-width="1"/>
                        <line x1="0" y1="80" x2="0" y1="-80" x2="-80" stroke="black" stroke-width="1"/>
                        
                        <!-- Original function -->
                        <path d="M -80,0 C -60,-50 -40,-50 -20,0 C 0,50 20,50 40,0 C 60,-50 80,-50 100,0" 
                              fill="none" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Approximations -->
                        <line x1="-80" y1="0" x2="80" y1="0" x2="0" stroke="#e74c3c" stroke-width="1.5" stroke-dasharray="5,2"/>
                        <path d="M -80,-80 L 80,80" fill="none" stroke="#3498db" stroke-width="1.5" stroke-dasharray="3,2"/>
                        <path d="M -80,-40 Q -40,80 0,0 Q 40,-80 80,40" fill="none" stroke="#2ecc71" stroke-width="1.5" stroke-dasharray="2,1"/>
                    </g>
                    
                    <!-- Function 3: ln(1+x) -->
                    <g transform="translate(550, 150)">
                        <text x="0" y="-80" font-family="Arial" font-size="16" text-anchor="middle">ln(1+x) ≈ x - x²/2 + x³/3 - ...</text>
                        
                        <!-- Axes -->
                        <line x1="-80" y1="0" x2="80" y1="0" x2="0" stroke="black" stroke-width="1"/>
                        <line x1="0" y1="80" x2="0" y1="-80" x2="-80" stroke="black" stroke-width="1"/>
                        
                        <!-- Original function -->
                        <path d="M -80,-80 C -70,-60 -60,-40 -50,-30 C -40,-20 -30,-15 -20,-10 C -10,-5 0,0 10,5 C 30,10 50,20 80,30" 
                              fill="none" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Approximations -->
                        <line x1="-80" y1="0" x2="80" y1="0" x2="0" stroke="#e74c3c" stroke-width="1.5" stroke-dasharray="5,2"/>
                        <path d="M -80,-80 L 80,80" fill="none" stroke="#3498db" stroke-width="1.5" stroke-dasharray="3,2"/>
                        <path d="M -80,-80 Q -40,0 0,0 Q 40,0 80,40" fill="none" stroke="#2ecc71" stroke-width="1.5" stroke-dasharray="2,1"/>
                    </g>
                    
                    <!-- Legend -->
                    <rect x="225" y="230" width="200" height="60" fill="white" stroke="#95a5a6" stroke-width="1"/>
                    
                    <line x1="235" y1="245" x2="255" y1="245" x2="245" stroke="#2c3e50" stroke-width="2"/>
                    <text x="265" y="250" font-family="Arial" font-size="12">Original Function</text>
                    
                    <line x1="235" y1="265" x2="255" y1="265" x2="265" stroke="#3498db" stroke-width="1.5" stroke-dasharray="3,2"/>
                    <text x="265" y="270" font-family="Arial" font-size="12">First-order Approximation</text>
                    
                    <line x1="235" y1="285" x2="255" y1="285" x2="285" stroke="#2ecc71" stroke-width="1.5" stroke-dasharray="2,1"/>
                    <text x="265" y="290" font-family="Arial" font-size="12">Higher-order Approximation</text>
                </svg>
            </div>
        </section>
    </section>

    <section id="summary">
        <h2 class="section-title">5. Summary and Key Points</h2>
        
        <div class="step">
            <p>Let's recap the key concepts we've covered about Taylor series expansions:</p>
            
            <ul>
                <li><strong>Basic Form:</strong> Taylor's theorem provides a way to approximate functions using its derivatives at a specific point.</li>
                <li><strong>Remainder Terms:</strong> Different forms (Peano, Lagrange, Integral) provide different ways to express and bound the approximation error.</li>
                <li><strong>Multivariable Extensions:</strong> The concepts extend to functions of multiple variables using gradients and Hessians.</li>
                <li><strong>Applications:</strong> Taylor series are widely used in numerical methods, physics, engineering, and statistics.</li>
            </ul>
        </div>
        
        <div class="note">
            <h4>When to Use Taylor Expansions</h4>
            <p>Taylor expansions are most useful when:</p>
            <ul>
                <li>You need to approximate a complex function near a specific point</li>
                <li>You're analyzing the behavior of a system near an equilibrium or reference state</li>
                <li>You need to understand how small changes in input affect the output</li>
                <li>You're developing numerical algorithms for function evaluation or optimization</li>
                <li>You're studying the asymptotic behavior of statistical estimators</li>
            </ul>
        </div>
        
        <div class="step">
            <p>The power of Taylor series lies in their ability to approximate complicated functions using simpler polynomial expressions. By understanding the different forms and their conditions, you can apply these approximations effectively across various domains.</p>
        </div>
    </section>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid var(--secondary-color); text-align: center; color: #777;">
        <p>This tutorial was created to provide a comprehensive understanding of Taylor series expansions.</p>
        <p>© 2023 Mathematics Tutorials</p>
    </footer>

</body>
</html> 