 This appendix covers the general theory of likelihood and quasi-likelihood used for
 inference with mixed models, GLMs, GAMs, etc. In particular the results invoked in
 sections 2.2 and 3.1 are derived here. The emphasis is on explaining the key ideas as
 simply as possible, so some of the results are proved only for the simple case of a
 single parameter and i.i.d. data, with the generalizations merely stated. To emphasize
 that the results given here apply much more widely than GLMs, the parameter that
 is the object of inference is denoted by θ in this section: for GLMs this will usually
 be β. Good references on the topics covered here are <PERSON> and <PERSON> (1974) and
 <PERSON><PERSON><PERSON> (1970), which are followed quite closely below.
 Proofs are only givenin outline and two general statistical results are used repeat
edly: the ‘law of large numbers’ (LOLN) and the ‘central limit theorem’ (CLT). In
 the i.i.d. context these are as follows. Let X1,X2,...,Xn be i.i.d. random variables
 with mean µ and variance σ2 (both of which are finite). The LOLN states that as
 n →∞, ¯ X →µ(inprobability).∗ The CLT states that as n → ∞ the distribution of
 ¯
 X tends to N(µ,σ2/n) whatever the distribution of the Xi. Both results generalize
 to multivariate and non-i.i.d. settings.
 A.1 Invariance
 Consider an observation, y = [y1,y2,...,yn]T, of a vector of random variables with
 joint p.m.f. or p.d.f. f(y,θ), where θ is a parameter with MLE ˆ θ. If γ is a parameter
 such that γ = g(θ), where g is any function, then the maximum likelihood estimate
 of γ is ˆ γ = g(ˆ θ), and this property is known as invariance.
 Invariance holds for any g, but a proof is easiest for the case in which g is a one
 to one function, so that g−1 is well defined. In this case θ = g−1(γ) and maximum
 likelihood estimation would proceed by maximizing the likelihood
 L(γ) = f(y,g−1(γ))
 w.r.t. γ. But we know that the maximum of f occurs at f(y, ˆ θ), by definition of ˆ θ, so
 ∗Tending to a limit in probability basically means that the probability of being further than any positive
 constant ǫ from the limit tends to zero.
 405
406
 MAXIMUMLIKELIHOODESTIMATION
 it must be the case that L’s maximum w.r.t. γ occurs when ˆ θ= g−1(ˆ γ), i.e.,
 ˆ
 γ =g(ˆ θ)
 is the MLE of γ. So, when working with maximum likelihood estimation, we can
 adopt whatever parameterization is most convenient for performing calculations, and
 simply transform back to the most interpretable parameterization at the end. Note
 that invariance holds for vector parameters as well.
 A.2 Properties of the expected log-likelihood
 The key to proving and understanding the large sample properties of maximum
 likelihood estimators lies in obtaining some results for the expectation of the log
likelihood, and then using the convergence in probability of the log-likelihood to its
 expected value, which results from the law of large numbers. In this section, some
 simple properties of the expected log likelihood are derived.
 Let y1,y2,...,yn be independent observations from a p.d.f. f(y,θ), where θ is
 an unknownparameter with true value θ0. Treating θ as unknown, the log-likelihood
 for θ is
 n
 l(θ) =
 i=1
 n
 log{f(yi,θ)} =
 i=1
 li(θ),
 where li is the log-likelihood, given only the single observation yi. Treating l as a
 function of random variables, Y1,Y2,...,Yn, means that l is itself a random variable
 (and the li are independent random variables). Hence we can consider expectations
 of l and its derivatives.
 Result 1:
 E0
 ∂l
 ∂θ θ0
 =0.
 (A.1)
 Thesubscript onthe expectationis to emphasizethat theexpectationis w.r.t. f(y,θ0).
 The proof is as follows (where it is to be taken that all derivatives are evaluated at θ0,
 and there is sufficient regularity that the order of differentiation and integration can
 be exchanged).
 E0
 ∂li
 ∂θ
 That the same holds for l follows immediately.
 Result 1 has the following obvious consequence:
 Result 2:
 var ∂l
 ∂f
 = E0
 ∂
 ∂θ log{f(Y,θ)} = 
1
 f(y,θ0)
 = ∂f
 ∂θ dy = ∂
 ∂θ fdy = ∂1
 ∂θ =0.
 
 
 ∂θ θ0
 =E0
 
 2
 
 
 ∂l
 
 .
 ∂θ f(y,θ0)dy
 (A.2)
 ∂θ θ0
PROPERTIESOFTHEEXPECTEDLOG-LIKELIHOOD 407
 Itcanfurtherbeshownthat
 Result3:
 I≡E0
 
 
 
 ∂l
 ∂θ θ0
 2
 
 
 =−E0
 ∂2l
 ∂θ2
 θ0
 (A.3)
 whereIisreferredtoastheinformationaboutθcontainedinthedata.Theterminol
ogyreferstothefact that, ifthedatatiedownθveryclosely(andaccurately),then
 thelog-likelihoodwillbesharplypeakedinthevicinityθ0 (i.e.,highI),whereas
 datacontaininglittleinformationaboutθwill leadtoanalmostflat likelihood,and
 lowI.
 Theproofofresult3issimple.Forasingleobservation,result1saysthat
 ∂log(f)
 ∂θ fdy=0.
 Differentiatingagainw.r.t.θyields
 ∂2log(f)
 ∂θ2
 f+∂log(f)
 ∂θ
 ∂f
 ∂θ dy=0
 but
 ∂log(f)
 ∂θ = 1
 f
 ∂f
 ∂θ
 andso
 ∂2log(f)
 ∂θ2
 fdy=− ∂log(f)
 ∂θ
 2
 fdy,
 whichis
 E0
 ∂2li
 ∂θ2 θ0
 =−E0
 
 
 
 ∂li
 ∂θ θ0
 2
 
 
 .
 Theresultfollowsveryeasily(giventheindependenceoftheli).
 Notethatbyresult1theexpectedloglikelihoodhasaturningpointatθ0,and
 sinceI ispositive, result3indicatesthat this turningpoint isamaximum.Sothe
 expectedloglikelihoodhasamaximumat thetrueparametervalue.Unfortunately
 results1and3don’testablishthatthismaximumisaglobalmaximum,butaslightly
 moreinvolvedproofshowsthatthisisinfactthecase.
 Result4:
 E0{l(θ0)}≥E0{l(θ)}∀θ. (A.4)
 TheproofisbasedonJensen’sinequality,whichsaysthatifcisaconcavefunction
 (i.e.,hasnegativesecondderivative)andY isarandomvariable,then
 E{c(Y)}≤c{E(Y)}.
 TheinequalityisalmostastatementoftheobviousasfigureA.1illustrates.Nowcon
408
 MAXIMUMLIKELIHOODESTIMATION
 C[E(Y)]
 E(C[Y])
 E(Y)
 Figure A.1 Schematic illustration of Jensen’s inequality which says that if c is a concave
 function then E{c(Y )} ≤ c{E(Y)}. The curve shows a concave function, while the lines
 connect the values of a discrete uniform random variable Y on the horizontal axis to the values
 of the discrete random variable c(Y ) on the vertical axis. It is immediately clear that the way
 that c spreads out c(Y ) values corresponding to low Y values, while bunching together c(Y )
 values corresponding to high Y values, implies Jensen’s inequality. Further reflection suggests
 (correctly) that Jensen’s inequality holds for any distribution.
 sider the concave function, log, and the random variable, f(Y,θ)/f(Y,θ0). Jensen’s
 inequality implies that
 E0 log f(Y,θ)
 f(Y,θ0)
 ≤log E0
 f(Y,θ)
 f(Y,θ0)
 Consider the right hand side of the inequality.
 E0
 f(Y,θ)
 f(Y,θ0) = f(y,θ)
 .
 f(y,θ0) f(y,θ0)dy = f(y,θ)dy = 1.
 So, since log(1) = 0 the inequality becomes
 E0 log f(Y,θ)
 f(Y,θ0)
 ≤0
 ⇒E0[log{f(Y,θ)}] ≤ E0[log{f(Y,θ0)}]
 from which the result follows immediately.
 Theaboveresults were derivedfor continuousY , but also hold for discrete Y : the
 proofs are almost identical, but with all yi 
replacing dy. Note also that, although
 the results presented here were derived assuming that the data were independent
 observations from the same distribution, this is in fact much more restrictive than is
 necessary, and the results hold more generally.
 Similarly, the results generalize to vector parameters. Let u be the vector such
 that ui = ∂l/∂θi, and H be the Hessian matrix of the log-likelihood w.r.t. the pa
rameters so that Hij = ∂2l/∂θi∂θj. Then, in particular,
CONSISTENCY
 409
 Result 1 (vector parameter)
 and
 Result 3 (vector parameter)
 E0(u) = 0
 I ≡E0(uuT) =−E0(H).
 A.3 Consistency
 (A.5)
 (A.6)
 Maximum likelihood estimators are often not unbiased, but under quite mild regu
larity conditions they are consistent. This means that as the sample size, on which
 the estimate is based, tends to infinity, the maximum likelihood estimator tends in
 probability to the true parameter value. Consistency therefore implies asymptotic†
 unbiasedness, but it actually implies slightly more than this — for example that the
 variance of the estimator is decreasing with sample size.
 Formally, if θ0 is the true value of parameter θ, and ˆ θn is its MLE based on n
 observations, y1, y2,...,yn, then consistency means that
 Pr(|ˆ θn − θ0| < ǫ) → 1
 as n →∞foranypositive ǫ.
 To see whyMLEsareconsistent,consider an outline prooffor the case of a single
 parameter, θ, estimated from independent observations y1,y2,...,yn on a random
 variable with p.m.f. or p.d.f. f(y,θ0). The log-likelihood in this case will be
 l(θ) ∝ 1
 n 
n
 i=1
 log{f(yi,θ)}
 where the factor of 1/n is introduced purely for later convenience. We need to
 show that in the large sample limit l(θ) achieves its maximum at the true parame
ter value θ0, but in the previous section it was shown that the expected value of the
 log likelihood for a single observation attains its maximum at θ0. The law of large
 numbers tells us that as n → ∞, n
 i=1log{f(Yi,θ)}/n tends (in probability) to
 E0[log{f(Y,θ)}]. So in the large sample limit we have that
 l(θ0) ≥ l(θ),
 i.e., that ˆ
 θ is θ0.
 To show that ˆ θ → θ0 in some well-ordered manner as n → ∞ requires that we
 assume some regularity (for example, at minimum, we need to be able to assume
 that if θ1 and θ2 are ‘close’ then so are l(θ1) and l(θ2)), but in the vast majority
 of practical situations such conditions hold. Figure A.2 can help illustrate how the
 †
 ‘Asymptotic’ here meaning ‘as sample size tends to infinity’.
410
 MAXIMUMLIKELIHOODESTIMATION
 E0
 (log(f(Y, θ)))
 1
 n
 n
 ∑
 i=1
 log(f(yi, θ))
 θ ^
 θ0
 Figure A.2 Illustration of the idea behind the derivation of consistency of MLEs. The dashed
 curve is the log-likelihood divided by the sample size, n, while the solid curve is the expec
tation of the log-likelihood divided by n (equivalently, the expected log-likelihood given one
 observation). The solid curve has a maximum at the true parameter value, and as sample size
 tends to infinity the dashed curve tends to the solid curve by the LOLN. Hence, in the large
 sample limit, the log likelihood has a maximum at the true parameter value, and we expect
 ˆ
 θn →θ0 asn →∞.
 argument works: as the sample size tends to infinity the dashed curve, proportional
 to the log likelihood, tends in probability to the solid curve, E0[log{f(Y,θ)}], which
 has its maximum at θ0, hence ˆ θ→ θ0.
 For simplicity of presentation, the above argument dealt only with a single pa
rameter and data that were independent observations of a random variable from one
 distribution. In fact consistency holds in much more general circumstances: for vec
tor parameters and non-independent data that do not necessarily all come from the
 same distribution.
 A.4 Largesample distribution of ˆ θ
 To obtain the large sample distribution of the MLE, ˆ θ, we make a Taylor expansion
 of the derivative of the log likelihood around the true parameter, θ0, and evaluate this
 at ˆ θ.
 ∂l
 ∂θ ˆ θ 
≃ ∂l
 ∂θ θ0 
+ ˆ θ−θ0
 ∂2l
 ∂θ2 θ0
 and from the definition of the MLE the left hand side must be zero, so we have that
 ˆ
 θ −θ0 ≃ ∂l/∂θ|θ0
 −∂2l/∂θ2|θ0 
,
THEGENERALIZEDLIKELIHOODRATIOTEST(GLRT)
 411
 with equality in the large sample limit (by consistency of ˆ θ). Now the top of this
 fraction has expected value zero and variance I (see results (A.1) and (A.2)), but it is
 also made up of a sum of i.i.d. random variables, li = log{f(Yi,θ)}, so that by the
 central limit theorem, as n → ∞, its distribution will tend to N(0,I). By the law of
 large numbers we also have that, as n → ∞, − ∂2l/∂θ2
 θ0 
→ I (in probability).So
 in the large sample limit (ˆ θ−θ0) is distributed as an N(0,I) r.v. divided by I. So in
 the limit as n → ∞,
 ˆ
 θ −θ0 ∼N(0,I−1).
 The result generalizes to vector parameters:
 ˆ
 θ ∼N(θ0,I−1)
 (A.7)
 in the large sample limit. Again the result holds generally and not just for the some
what restricted form of the likelihood which we have assumed here.
 Usually I will notbeknown,anymorethanθ is, andwill haveto beestimatedby
 plugging ˆ θ into the expression for I, or by the negative of the Hessian (−H) of the
 log-likelihood evaluated at the MLE For reasonable sample sizes, the empirical in
formation matrix, ˆ I = −H, is usually an adequate approximationto the information
 matrix I itself (this follows from the law of large numbers).
 A.5 Thegeneralized likelihood ratio test (GLRT)
 Consider an observation, y, on a random vector of dimension n with p.d.f. (or p.m.f.)
 f(y,θ), where θ is a parameter vector. Suppose that we want to test:
 H0 : R(θ) = 0 vs. H1 : R(θ)=0,
 where R is a vector valued function of θ, such that H0 imposes r restrictions on the
 parameter vector. If H0 is true then in the limit as n → ∞
 2λ =2{l(ˆ θH1
 ) − l(ˆ θH0
 )} ∼ χ2
 r,
 (A.8)
 where l is the log-likelihood function and ˆ θH1 
is the MLE of θ. ˆ θH0 
is the value of
 θ satisfying R(θ) = 0, which maximizes the likelihood (i.e., the restricted MLE).
 This result is used to calculate approximate p-values for the test.
 Tests performed in this way are known as ‘generalized likelihood ratio tests’,
 since λ is the log of the ratio of the maximized likelihoods under each hypothesis.
 In the context of GLMs, the null hypothesis is usually that the correct model is a
 simplified version of the model under the alternative hypothesis