For very large datasets, gradient descent iterations can be too slow. The cost of each iteration is $$O(nd)$$, where $$n$$ is the number of samples, and $$d$$ is the number of features. There are two common strategies for yielding even cheaper iterations:

1. Coordinate descent

2. Stochastic gradient descent

# Coordinate descent



![](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=YWQ3ZjFjMmZhMjA5NThjOTc0ZDUyOTQzNDU1NDU3ZTNfcE9OYkI3R3J0WEF0V0l4M0UySHpRSVl6V3RvWXpuU01fVG9rZW46RnJyTWJid2hCbzlUbDZ4em9LdmNnY21zbndnXzE3NTEzNDkxMDM6MTc1MTM1MjcwM19WNA)

In coordinate descent, each iteration only updates one variable. For example, on iteration $$k$$, we select a variable $$j_k$$ and set:

$$w_{j_k}^{k+1} = w_{j_k}^k - \alpha_k\nabla_{j_k}f(w^k)$$,

and other $$w_j$$ stay the same.

Theoretically, coordinate descent is a provably bad algorithm. Its convergence rate is slower than gradient descent. However, coordinate descent can be faster than gradient descent if iterations are $$d$$ times cheaper.

# Stochastic gradient descent

In machine learning, the loss or likelihood function is usually computed over a number of samples. As a result, the optimization problem in gradient descent can be rewritten as:

$$w^* = \argmin_{w \in \mathbb{R}^d}f(w) = \argmin_{w \in \mathbb{R}^d}\frac{1}n \sum_{i=1}^n f_i(w)$$

where $$f_i(w)$$ indicates the objective computed on each sample, and $$f(w) = \frac{1}n\sum_{i=1}^n f_i(w)$$ is our objective function.

As discussed before, the standard gradient descent method would perform the following iterations:

$$w^{(t+1)} = w^{(t)} - \frac{\alpha}n \sum_{i=1}^n\nabla f_i(w^{(t)})$$

![The searching path of standard gradient descent](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=Y2JhM2RjZTYzMGRmNDQ5YTVmODA1NzQzMDMwMmQ5ZDFfcmRIZ3BHMndUbDNzOUxxQ0NGRVJhVFpNdzI3V0VSSllfVG9rZW46Q1o0dWIza0N4b1JqMVN4ODB0aGNZd05UbkJoXzE3NTEzNDkxMDM6MTc1MTM1MjcwM19WNA)

The iteration cost in standard gradient descent is linear in $$n$$– the number of samples.

In stochastic gradient descent, the true gradient of $$f(w)$$ is approximated by a gradient at a single sample:

$$w^{(t+1)} = w^{(t)} - \alpha \nabla f_{i_k}(w^{(t)})$$

where $$i_k$$ is randomly selected from $$\{1,2,...,n\}$$.

![The searching path of stochastic gradient descent](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=MWI5N2ZmNGUwMjZlNDhiOWMwMTlkYzQ5OWY3OTk2NjVfV2Y0cVZZS3FWN2s0VkszWUNqd2ptMXJta3I5aHdRV2JfVG9rZW46R3dkMmJvWDVCb0plTWt4WDM4dGNLcXdwbkhiXzE3NTEzNDkxMDM6MTc1MTM1MjcwM19WNA)

# Subgradient method

**Subderivatives** or **subgradient&#x20;**&#x67;eneralizes the derivative to convex functions which are not necessarily differentiable. The set of subderivatives at a point is called the **subdifferential** at that point.

> Definition (Subderivative). A subderivative of a convex function $$f: I \rightarrow \mathbb{R}$$ at a point $$x_0$$ in the open interval $$I$$ is a real number $$c$$ such that
>
> $$f(x) - f(x_0) \ge c(x - x_0)$$
>
> for all $$x \in I$$.&#x20;

Consider the function $$f(x) = |x|$$ which is convex. Then, the subdifferential at the origin is the interval $$[−1,1]$$. The subdifferential at any point $$x_0 < 0$$ is the singleton set $$\{-1\}$$, while the subdifferential at any point $$x_0 > 0$$ is the singleton set $$\{1\}$$. This is similar to the sign function, but is not single-valued at 0, instead including all possible subderivatives.

> Algorithm (Subgradient descent). Let $$f: \mathbb{R}^n \rightarrow \mathbb{R}$$ be a convex function. A classical subgradient method iterates:
>
> $$w^{(t+1)} = w^{(t)} - \alpha g^{(t)}$$
>
> where $$g^{(t)}$$ denotes any subgradient of $$f$$ at $$w^{(t)}$$. If $$f$$ is differentiable, then its only subgradient is the gradient vector $$\nabla f$$ itself.

Just like gradient descent, subgradient descent can also be performed in a stochastic way.

> Algorithm (Stochastic subgradient descent). Let $$f: \mathbb{R}^n \rightarrow \mathbb{R}$$ be a convex function. A classical subgradient method iterates:
>
> $$w^{(t+1)} = w^{(t)} - \alpha g_{i_k}^{(t)}$$
>
> where $$g_{i_k}^{(t)}$$ denotes any subgradient of $$f_{i_k}$$ at $$w^{(t)}$$.

