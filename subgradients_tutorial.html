<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Subgradients</title>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #3498db;
            padding-bottom: 5px;
        }
        .definition, .theorem, .example, .note, .property {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .definition {
            background-color: #e8f4f8;
            border-left: 5px solid #3498db;
        }
        .theorem {
            background-color: #eaf7ea;
            border-left: 5px solid #27ae60;
        }
        .example {
            background-color: #fcf3cf;
            border-left: 5px solid #f1c40f;
        }
        .note {
            background-color: #f8e5e5;
            border-left: 5px solid #e74c3c;
        }
        .property {
            background-color: #f0e6f6;
            border-left: 5px solid #9b59b6;
        }
        .math-block {
            overflow-x: auto;
            margin: 15px 0;
            padding: 10px;
        }
        code {
            font-family: 'Courier New', Courier, monospace;
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .diagram {
            text-align: center;
            margin: 20px 0;
        }
        .section {
            margin-bottom: 40px;
        }
        .proof {
            margin: 15px 0;
            padding: 10px 15px;
            background-color: #f5f5f5;
            border-left: 3px solid #95a5a6;
        }
        .proof:before {
            content: "Proof:";
            font-style: italic;
            font-weight: bold;
        }
        .proof:after {
            content: "□";
            float: right;
        }
    </style>
</head>
<body>
    <header>
        <h1>Understanding Subgradients</h1>
        <p>A comprehensive guide to subgradient theory in convex analysis</p>
    </header>

    <div class="section" id="introduction">
        <h2>1. Introduction</h2>
        <p>Subgradients are a powerful generalization of gradients that allow us to work with non-differentiable convex functions. While the gradient of a function provides the slope at a point where the function is differentiable, subgradients extend this concept to points where traditional derivatives don't exist. This makes them an essential tool in optimization theory, especially for handling non-smooth convex functions.</p>
        
        <p>In this tutorial, we'll explore the concept of subgradients from first principles, examine their properties, and see how they're used in optimization problems. We'll also look at various examples to build intuition and understand practical applications.</p>
    </div>

    <div class="section" id="definition">
        <h2>2. Definition of Subgradients</h2>
        
        <div class="definition">
            <h3>Definition: Subgradient</h3>
            <p>Given a convex function \(f: \mathbb{R}^n \rightarrow \mathbb{R}\) and a point \(x \in \text{dom } f\), a vector \(g \in \mathbb{R}^n\) is called a <em>subgradient</em> of \(f\) at \(x\) if:</p>
            <div class="math-block">
                \begin{align}
                f(z) \geq f(x) + g^T(z - x) \quad \text{for all } z \in \text{dom } f
                \end{align}
            </div>
            <p>The set of all subgradients of \(f\) at the point \(x\) is called the <em>subdifferential</em> of \(f\) at \(x\), denoted by \(\partial f(x)\).</p>
        </div>
        
        <p>There are several ways to interpret what a subgradient means:</p>
        
        <ul>
            <li>A subgradient \(g\) at point \(x\) defines a linear function \(f(x) + g^T(z - x)\) that is a global underestimator of \(f\).</li>
            <li>Geometrically, \(g\) is a subgradient if the vector \((g, -1)\) supports the epigraph of \(f\) at the point \((x, f(x))\).</li>
            <li>The subgradient provides a generalized notion of "slope" at points where the function may not have a unique derivative.</li>
        </ul>
        
        <div class="diagram">
            <svg width="600" height="300" viewBox="0 0 600 300">
                <!-- Coordinate axes -->
                <line x1="50" y1="250" x2="550" y2="250" stroke="#333" stroke-width="2"/>
                <line x1="100" y1="50" x2="100" y2="280" stroke="#333" stroke-width="2"/>
                
                <!-- Function with non-differentiable point -->
                <path d="M100,200 L300,100 L500,200" fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Non-differentiable point -->
                <circle cx="300" cy="100" r="5" fill="#e74c3c"/>
                
                <!-- Subgradients -->
                <line x1="100" y1="200" x2="500" y2="0" stroke="#3498db" stroke-width="2" stroke-dasharray="5,3"/>
                <line x1="100" y1="260" x2="500" y2="140" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,3"/>
                
                <!-- Labels -->
                <text x="560" y="250" font-size="14">x</text>
                <text x="100" y="40" font-size="14">f(x)</text>
                <text x="300" y="85" font-size="12">Non-differentiable point</text>
                <text x="380" y="50" font-size="12" fill="#3498db">Subgradient 1</text>
                <text x="380" y="180" font-size="12" fill="#27ae60">Subgradient 2</text>
                <text x="300" y="280" font-size="12" text-anchor="middle">At non-differentiable points, there can be multiple subgradients</text>
            </svg>
            <p><em>Figure 1: Multiple subgradients at a non-differentiable point of a convex function</em></p>
        </div>
        
        <div class="example">
            <h3>Example: Absolute Value Function</h3>
            <p>Consider the absolute value function \(f(z) = |z|\). Let's find its subdifferential:</p>
            <ul>
                <li>For \(x < 0\), the function is differentiable with derivative \(-1\), so \(\partial f(x) = \{-1\}\).</li>
                <li>For \(x > 0\), the function is differentiable with derivative \(1\), so \(\partial f(x) = \{1\}\).</li>
                <li>At \(x = 0\), the function is not differentiable. A vector \(g\) is a subgradient at \(x = 0\) if \(|z| \geq gz\) for all \(z\). This is satisfied if and only if \(g \in [-1, 1]\). Therefore, \(\partial f(0) = [-1, 1]\).</li>
            </ul>
        </div>
        
        <div class="diagram">
            <svg width="500" height="300" viewBox="0 0 500 300">
                <!-- Coordinate axes -->
                <line x1="50" y1="150" x2="450" y2="150" stroke="#333" stroke-width="2"/>
                <line x1="250" y1="50" x2="250" y2="250" stroke="#333" stroke-width="2"/>
                
                <!-- Absolute value function -->
                <path d="M50,350 L250,150 L450,350" fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Subdifferentials visualization -->
                <line x1="100" y1="150" x2="100" y2="200" stroke="#3498db" stroke-width="3"/>
                <circle cx="100" cy="200" r="4" fill="#3498db"/>
                <text x="85" y="220" font-size="12" fill="#3498db">-1</text>
                
                <line x1="250" y1="150" x2="250" y2="200" stroke="#3498db" stroke-width="3"/>
                <rect x="225" y="200" width="50" height="6" fill="#3498db"/>
                <text x="220" y="220" font-size="12" fill="#3498db">[-1,1]</text>
                
                <line x1="400" y1="150" x2="400" y2="200" stroke="#3498db" stroke-width="3"/>
                <circle cx="400" cy="200" r="4" fill="#3498db"/>
                <text x="395" y="220" font-size="12" fill="#3498db">1</text>
                
                <!-- Labels -->
                <text x="460" y="150" font-size="14">x</text>
                <text x="235" y="40" font-size="14">∂f(x)</text>
                <text x="250" y="280" font-size="16" text-anchor="middle">Subdifferential of f(x) = |x|</text>
            </svg>
            <p><em>Figure 2: The subdifferential of the absolute value function</em></p>
        </div>
    </div>

    <div class="section" id="basic-properties">
        <h2>3. Basic Properties of Subgradients</h2>
        
        <p>The subdifferential \(\partial f(x)\) of a function at a point has several important properties that make it useful for analysis and optimization. Let's explore these properties.</p>
        
        <div class="property">
            <h3>Property 1: Closed and Convex Set</h3>
            <p>The subdifferential \(\partial f(x)\) is always a closed and convex set, even if \(f\) itself is not convex.</p>
            <p>This follows from the definition of the subdifferential as an intersection of halfspaces:</p>
            <div class="math-block">
                \begin{align}
                \partial f(x) = \bigcap_{z \in \text{dom } f} \{g \mid f(z) \geq f(x) + g^T(z - x)\}
                \end{align}
            </div>
            <p>Since each constraint defines a closed halfspace in \(\mathbb{R}^n\), and the intersection of any collection of closed convex sets is itself closed and convex, \(\partial f(x)\) must be a closed convex set.</p>
        </div>
        
        <div class="property">
            <h3>Property 2: Boundedness</h3>
            <p>If \(f\) is continuous at \(x\), then the subdifferential \(\partial f(x)\) is bounded.</p>
        </div>
        
        <div class="proof">
            <p>To prove this, let's assume that \(f\) is continuous at \(x\). This means we can find some \(\epsilon > 0\) such that \(f\) is bounded in the \(\epsilon\)-neighborhood of \(x\). Specifically, there exist constants \(f_{\min}\) and \(f_{\max}\) such that \(f_{\min} \leq f(y) \leq f_{\max}\) for all points \(y\) with \(\|y - x\|_2 \leq \epsilon\).</p>
            
            <p>Now, suppose for contradiction that \(\partial f(x)\) is unbounded. Then there exists a sequence of subgradients \(g_n \in \partial f(x)\) such that \(\|g_n\|_2 \to \infty\) as \(n \to \infty\).</p>
            
            <p>For each \(g_n\), define the point \(y_n = x + \epsilon \cdot \frac{g_n}{\|g_n\|_2}\). Note that \(\|y_n - x\|_2 = \epsilon\), so \(y_n\) is on the boundary of our neighborhood.</p>
            
            <p>Since \(g_n \in \partial f(x)\), we have:</p>
            <div class="math-block">
                \begin{align}
                f(y_n) \geq f(x) + g_n^T(y_n - x) = f(x) + g_n^T\left(\epsilon \cdot \frac{g_n}{\|g_n\|_2}\right) = f(x) + \epsilon\|g_n\|_2
                \end{align}
            </div>
            
            <p>But \(\|g_n\|_2 \to \infty\), so \(f(y_n) \to \infty\), which contradicts our assumption that \(f\) is bounded in the \(\epsilon\)-neighborhood of \(x\).</p>
            
            <p>Therefore, \(\partial f(x)\) must be bounded.</p>
        </div>
        
        <h3>3.1 Existence of Subgradients</h3>
        
        <div class="property">
            <h3>Property 3: Existence</h3>
            <p>If \(f\) is convex and \(x \in \text{int dom } f\) (i.e., \(x\) is in the interior of the domain of \(f\)), then \(\partial f(x)\) is non-empty and bounded.</p>
        </div>
        
        <div class="proof">
            <p>We can prove this using the supporting hyperplane theorem. At the point \((x, f(x))\) on the boundary of the epigraph of \(f\), there exists a non-vertical supporting hyperplane.</p>
            
            <p>Specifically, there exist \(a \in \mathbb{R}^n\) and \(b \in \mathbb{R}\), not both zero, such that:</p>
            <div class="math-block">
                \begin{align}
                a^T(z - x) + b(t - f(x)) \leq 0 \quad \text{for all } (z, t) \in \text{epi } f
                \end{align}
            </div>
            
            <p>Since \(x \in \text{int dom } f\), the supporting hyperplane cannot be vertical, meaning \(b \neq 0\). In fact, \(b < 0\) (since the epigraph extends upward).</p>
            
            <p>Rearranging the inequality and setting \(t = f(z)\):</p>
            <div class="math-block">
                \begin{align}
                a^T(z - x) + b(f(z) - f(x)) \leq 0 \\
                f(z) \geq f(x) - \frac{a^T(z-x)}{b} \\
                f(z) \geq f(x) + \left(-\frac{a}{b}\right)^T(z-x)
                \end{align}
            </div>
            
            <p>This shows that \(g = -\frac{a}{b}\) is a subgradient of \(f\) at \(x\), proving that \(\partial f(x)\) is non-empty.</p>
        </div>
        
        <div class="note">
            <h3>Important Note</h3>
            <p>While there are pathological convex functions that may not have subgradients at some points on the boundary of their domain, for most practical purposes, we can assume that convex functions are subdifferentiable at every point in their domain.</p>
        </div>
        
        <h3>3.2 Subgradients of Differentiable Functions</h3>
        
        <div class="property">
            <h3>Property 4: Differentiable Case</h3>
            <p>If \(f\) is convex and differentiable at \(x\), then the subdifferential contains exactly one element, which is the gradient:</p>
            <div class="math-block">
                \begin{align}
                \partial f(x) = \{\nabla f(x)\}
                \end{align}
            </div>
            <p>Conversely, if \(f\) is convex and \(\partial f(x) = \{g\}\) contains exactly one element, then \(f\) is differentiable at \(x\) with \(\nabla f(x) = g\).</p>
        </div>

        <div class="diagram">
            <svg width="500" height="300" viewBox="0 0 500 300">
                <!-- Coordinate axes -->
                <line x1="50" y1="250" x2="450" y2="250" stroke="#333" stroke-width="2"/>
                <line x1="100" y1="50" x2="100" y2="280" stroke="#333" stroke-width="2"/>
                
                <!-- Smooth convex function -->
                <path d="M100,250 Q150,200 200,170 T300,120 T400,100" fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Gradient at a point -->
                <circle cx="250" cy="140" r="5" fill="#3498db"/>
                <line x1="250" y1="140" x2="350" y2="90" stroke="#3498db" stroke-width="2" stroke-dasharray="5,3"/>
                <text x="270" y="110" font-size="14" fill="#3498db">∇f(x)</text>
                
                <!-- Supporting line -->
                <line x1="100" y1="290" x2="400" y2="40" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,3"/>
                
                <!-- Labels -->
                <text x="460" y="250" font-size="14">x</text>
                <text x="90" y="40" font-size="14">f(x)</text>
                <text x="250" y="280" font-size="14" text-anchor="middle">For differentiable functions, ∂f(x) = {∇f(x)}</text>
            </svg>
            <p><em>Figure 3: For a differentiable convex function, the subdifferential at any point contains only the gradient</em></p>
        </div>
        
        <h3>3.3 Characterization of Minimizers</h3>
        
        <div class="theorem">
            <h3>Theorem: Optimality Condition</h3>
            <p>A point \(x^*\) is a minimizer of a (not necessarily convex) function \(f\) if and only if \(f\) is subdifferentiable at \(x^*\) and:</p>
            <div class="math-block">
                \begin{align}
                0 \in \partial f(x^*)
                \end{align}
            </div>
            <p>That is, the subdifferential at \(x^*\) contains the zero vector.</p>
        </div>
        
        <div class="proof">
            <p>First, suppose \(0 \in \partial f(x^*)\). By definition of subgradient:</p>
            <div class="math-block">
                \begin{align}
                f(x) \geq f(x^*) + 0^T(x - x^*) = f(x^*)
                \end{align}
            </div>
            <p>for all \(x \in \text{dom } f\). This means \(x^*\) is a global minimizer of \(f\).</p>
            
            <p>Conversely, if \(x^*\) is a minimizer of \(f\), then for all \(x \in \text{dom } f\), we have \(f(x) \geq f(x^*)\). This is equivalent to:</p>
            <div class="math-block">
                \begin{align}
                f(x) \geq f(x^*) + 0^T(x - x^*)
                \end{align}
            </div>
            <p>Thus, \(0\) is a subgradient of \(f\) at \(x^*\), i.e., \(0 \in \partial f(x^*)\).</p>
        </div>
        
        <div class="note">
            <h3>Note on Non-Convex Functions</h3>
            <p>While this characterization of minimizers via subgradients holds for nonconvex functions, finding the subdifferential of a nonconvex function can be challenging in practice. For convex functions, this optimality condition is particularly powerful because it's both necessary and sufficient, and subgradients are generally easier to compute.</p>
        </div>
    </div>

    <div class="section" id="directional-derivatives">
        <h2>4. Directional Derivatives and Subgradients</h2>
        
        <p>Directional derivatives provide another way to understand subgradients and are particularly useful in optimization algorithms. For convex functions, directional derivatives always exist, even at points where the function is not differentiable.</p>
        
        <div class="definition">
            <h3>Definition: Directional Derivative</h3>
            <p>For a convex function \(f: \mathbb{R}^n \rightarrow \mathbb{R}\), the directional derivative of \(f\) at the point \(x \in \mathbb{R}^n\) in the direction \(v\) is defined as:</p>
            <div class="math-block">
                \begin{align}
                f'(x; v) = \lim_{t \downarrow 0} \frac{f(x + tv) - f(x)}{t}
                \end{align}
            </div>
            <p>where the notation \(t \downarrow 0\) means \(t\) approaches 0 from above.</p>
        </div>
        
        <p>For convex functions, this limit always exists (though it may be \(+\infty\) or \(-\infty\)). To see why, we can show that the ratio \(\frac{f(x+tv) - f(x)}{t}\) is non-decreasing as \(t\) decreases.</p>
        
        <div class="property">
            <h3>Key Properties of Directional Derivatives</h3>
            <ul>
                <li>The directional derivative \(f'(x; v)\) is convex in the direction \(v\)</li>
                <li>It exists for any convex function \(f\) that is finite in a neighborhood of \(x\)</li>
                <li>It is positively homogeneous in \(v\): \(f'(x; \alpha v) = \alpha f'(x; v)\) for \(\alpha > 0\)</li>
                <li>A function \(f\) is differentiable at \(x\) if and only if \(f'(x; v) = \nabla f(x)^T v\) for all directions \(v\)</li>
            </ul>
        </div>
        
        <h3>4.1 Relationship with Subgradients</h3>
        
        <p>There's a fundamental connection between directional derivatives and subgradients for convex functions:</p>
        
        <div class="theorem">
            <h3>Theorem: Directional Derivative as Support Function</h3>
            <p>For a convex function \(f\) and any direction \(v\), the directional derivative \(f'(x; v)\) can be expressed in terms of the subdifferential as:</p>
            <div class="math-block">
                \begin{align}
                f'(x; v) = \sup_{g \in \partial f(x)} g^T v
                \end{align}
            </div>
            <p>In other words, the directional derivative is the support function of the subdifferential evaluated at \(v\).</p>
        </div>
        
        <div class="proof">
            <p>First, let's show that \(f'(x; v) \geq \sup_{g \in \partial f(x)} g^T v\).</p>
            
            <p>By definition of a subgradient, for any \(g \in \partial f(x)\) and \(t > 0\):</p>
            <div class="math-block">
                \begin{align}
                f(x + tv) \geq f(x) + g^T(tv) = f(x) + tg^T v
                \end{align}
            </div>
            
            <p>Rearranging:</p>
            <div class="math-block">
                \begin{align}
                \frac{f(x + tv) - f(x)}{t} \geq g^T v
                \end{align}
            </div>
            
            <p>Taking the limit as \(t \downarrow 0\), we get \(f'(x; v) \geq g^T v\) for all \(g \in \partial f(x)\), which means \(f'(x; v) \geq \sup_{g \in \partial f(x)} g^T v\).</p>
            
            <p>The reverse direction is more involved and requires using the fact that any convex function can be expressed as the supremum of its affine minorants. The key insight is that the affine functions that support the directional derivative must correspond to subgradients of the original function.</p>
        </div>
        
        <div class="diagram">
            <svg width="500" height="400" viewBox="0 0 500 400">
                <!-- Coordinate axes -->
                <line x1="50" y1="300" x2="450" y2="300" stroke="#333" stroke-width="2"/>
                <line x1="100" y1="50" x2="100" y2="350" stroke="#333" stroke-width="2"/>
                
                <!-- Convex function -->
                <path d="M100,300 Q150,280 200,250 L300,150 Q350,120 400,100" fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Point x and x+tv -->
                <circle cx="200" cy="250" r="5" fill="#3498db"/>
                <text x="185" y="270" font-size="14">x</text>
                
                <circle cx="250" cy="200" r="5" fill="#3498db"/>
                <text x="255" y="195" font-size="14">x+tv</text>
                
                <!-- Direction vector -->
                <line x1="200" y1="250" x2="250" y2="200" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead1)"/>
                <text x="215" y="215" font-size="14" fill="#27ae60">tv</text>
                
                <!-- Tangent line at x -->
                <line x1="150" y1="280" x2="340" y2="100" stroke="#9b59b6" stroke-width="2" stroke-dasharray="5,3"/>
                
                <!-- Subgradient -->
                <line x1="200" y1="250" x2="250" y2="200" stroke="#f39c12" stroke-width="3" marker-end="url(#arrowhead2)" opacity="0.5"/>
                <text x="240" y="240" font-size="14" fill="#f39c12">g</text>
                
                <defs>
                    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                    </marker>
                    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
                    </marker>
                </defs>
                
                <!-- Labels -->
                <text x="460" y="300" font-size="14">x</text>
                <text x="90" y="40" font-size="14">f(x)</text>
                <text x="250" y="370" font-size="16" text-anchor="middle">Directional derivative f'(x;v) = sup{g^T v | g ∈ ∂f(x)}</text>
            </svg>
            <p><em>Figure 4: The directional derivative represents the rate of increase of the function in a given direction, and is related to the subdifferential via the support function</em></p>
        </div>
        
        <div class="example">
            <h3>Example: Directional Derivative of the Absolute Value Function</h3>
            <p>Let's compute the directional derivatives of \(f(x) = |x|\) at \(x = 0\) in the directions \(v = 1\) and \(v = -1\).</p>
            
            <p>We know that \(\partial f(0) = [-1, 1]\). Using the formula \(f'(x; v) = \sup_{g \in \partial f(x)} g^T v\):</p>
            
            <p>For \(v = 1\):</p>
            <div class="math-block">
                \begin{align}
                f'(0; 1) &= \sup_{g \in [-1, 1]} g \cdot 1 \\
                &= \sup_{g \in [-1, 1]} g \\
                &= 1
                \end{align}
            </div>
            
            <p>For \(v = -1\):</p>
            <div class="math-block">
                \begin{align}
                f'(0; -1) &= \sup_{g \in [-1, 1]} g \cdot (-1) \\
                &= \sup_{g \in [-1, 1]} -g \\
                &= -\inf_{g \in [-1, 1]} g \\
                &= -(-1) = 1
                \end{align}
            </div>
            
            <p>So, \(f'(0; 1) = f'(0; -1) = 1\), which makes sense because the absolute value function increases at the same rate in both the positive and negative directions from zero.</p>
        </div>
    </div>

    <div class="section" id="constrained-minimization">
        <h2>5. Constrained Minimization with Subgradients</h2>
        
        <p>We've seen that for unconstrained minimization of a function \(f\), the condition \(0 \in \partial f(x^*)\) characterizes the minimizer \(x^*\). For constrained minimization, the story is a bit more complex but equally elegant.</p>
        
        <div class="theorem">
            <h3>Theorem: Constrained Minimization</h3>
            <p>Consider the problem of minimizing a subdifferentiable function \(f\) over a closed convex set \(X\). A point \(x^* \in X\) is a minimizer of \(f\) over \(X\) if and only if there exists a subgradient \(g \in \partial f(x^*)\) such that:</p>
            <div class="math-block">
                \begin{align}
                g^T(y - x^*) \geq 0 \quad \text{for all } y \in X
                \end{align}
            </div>
            <p>In other words, there must be a subgradient at \(x^*\) that makes an acute angle with all feasible directions from \(x^*\).</p>
        </div>
        
        <div class="proof">
            <p>First, suppose there exists \(g \in \partial f(x^*)\) such that \(g^T(y - x^*) \geq 0\) for all \(y \in X\).</p>
            
            <p>By definition of subgradient:</p>
            <div class="math-block">
                \begin{align}
                f(y) &\geq f(x^*) + g^T(y - x^*) \\
                &\geq f(x^*) \quad \text{(since } g^T(y - x^*) \geq 0 \text{)}
                \end{align}
            </div>
            
            <p>So \(f(y) \geq f(x^*)\) for all \(y \in X\), meaning \(x^*\) is a minimizer.</p>
            
            <p>For the converse, assuming \(x^* \in \text{int dom }f\), we consider the directional derivatives. If \(x^*\) minimizes \(f\) over \(X\), then for any feasible direction \(\Delta = y - x^*\) (where \(y \in X\)), the directional derivative must be non-negative:</p>
            <div class="math-block">
                \begin{align}
                f'(x^*; y - x^*) \geq 0
                \end{align}
            </div>
            
            <p>Using the relationship between directional derivatives and subgradients:</p>
            <div class="math-block">
                \begin{align}
                f'(x^*; y - x^*) &= \sup_{g \in \partial f(x^*)} g^T(y - x^*) \geq 0 \\
                \end{align}
            </div>
            
            <p>Through minimax theory, we can swap the infimum over \(y \in X\) and the supremum over \(g \in \partial f(x^*)\), concluding that there must exist some \(g \in \partial f(x^*)\) such that \(g^T(y - x^*) \geq 0\) for all \(y \in X\).</p>
        </div>
        
        <div class="diagram">
            <svg width="500" height="400" viewBox="0 0 500 400">
                <!-- Feasible region X -->
                <path d="M150,300 L300,150 L400,200 L350,300 Z" fill="#eaf7ea" fill-opacity="0.5" stroke="#27ae60" stroke-width="2"/>
                <text x="290" y="230" font-size="16" fill="#27ae60">X</text>
                
                <!-- Contour lines of function f -->
                <ellipse cx="250" cy="250" rx="150" ry="100" fill="none" stroke="#e74c3c" stroke-width="1" stroke-dasharray="3,3"/>
                <ellipse cx="250" cy="250" rx="100" ry="66.7" fill="none" stroke="#e74c3c" stroke-width="1" stroke-dasharray="3,3"/>
                <ellipse cx="250" cy="250" rx="50" ry="33.3" fill="none" stroke="#e74c3c" stroke-width="1" stroke-dasharray="3,3"/>
                
                <!-- Optimal point -->
                <circle cx="300" cy="150" r="5" fill="#3498db"/>
                <text x="310" y="140" font-size="14" fill="#3498db">x*</text>
                
                <!-- Subgradient at optimal point -->
                <line x1="300" y1="150" x2="360" y2="90" stroke="#9b59b6" stroke-width="2" marker-end="url(#arrowhead3)"/>
                <text x="350" y="80" font-size="14" fill="#9b59b6">g</text>
                
                <!-- Non-optimal point -->
                <circle cx="200" cy="200" r="5" fill="#f39c12"/>
                <text x="180" y="200" font-size="14" fill="#f39c12">y</text>
                
                <!-- Feasible direction -->
                <line x1="300" y1="150" x2="200" y2="200" stroke="#34495e" stroke-width="2" stroke-dasharray="5,3"/>
                <text x="240" y="160" font-size="14" fill="#34495e">y-x*</text>
                
                <defs>
                    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6"/>
                    </marker>
                </defs>
                
                <!-- Labels -->
                <text x="250" y="370" font-size="16" text-anchor="middle">At optimal point, subgradient makes acute angles with all feasible directions</text>
            </svg>
            <p><em>Figure 5: At the optimal point on the boundary of a constrained set, there exists a subgradient that points "outward" from the feasible region</em></p>
        </div>
        
        <div class="note">
            <h3>Relationship to KKT Conditions</h3>
            <p>This optimality condition for constrained minimization is closely related to the Karush-Kuhn-Tucker (KKT) conditions in constrained optimization. In fact, the condition \(g^T(y - x^*) \geq 0\) for all \(y \in X\) is a generalization of the complementary slackness and dual feasibility conditions in the KKT framework.</p>
        </div>
        
        <p>This characterization of constrained minimizers is powerful because it provides a clear geometric interpretation: at an optimal point, there must be a subgradient that points "outward" from the feasible region. This insight is the foundation for many projected subgradient methods in constrained optimization.</p>
    </div>

    <div class="section" id="calculus">
        <h2>6. Calculus of Subgradients</h2>
        
        <p>Just as there is a calculus of derivatives to calculate derivatives of complex functions from simpler ones, there is a calculus of subgradients. This provides rules for constructing subgradients of functions that result from operations like scaling, addition, composition, and taking the maximum of convex functions.</p>
        
        <p>We'll distinguish between two levels of subdifferential calculus:</p>
        <ul>
            <li><strong>Weak calculus:</strong> Rules that produce at least one subgradient at a point, which is sufficient for many optimization algorithms.</li>
            <li><strong>Strong calculus:</strong> Rules that characterize the complete subdifferential set, which is useful for theoretical analysis and optimality conditions.</li>
        </ul>
        
        <h3>6.1 Basic Operations</h3>
        
        <div class="property">
            <h3>Rule 1: Nonnegative Scaling</h3>
            <p>For any convex function \(f\) and scalar \(\alpha \geq 0\):</p>
            <div class="math-block">
                \begin{align}
                \partial(\alpha f)(x) = \alpha \partial f(x)
                \end{align}
            </div>
            <p>That is, the subdifferential of a scaled function is the scaled subdifferential of the original function.</p>
        </div>
        
        <div class="property">
            <h3>Rule 2: Sum of Functions</h3>
            <p>For convex functions \(f_1, f_2, \ldots, f_m\):</p>
            <div class="math-block">
                \begin{align}
                \partial(f_1 + f_2 + \cdots + f_m)(x) = \partial f_1(x) + \partial f_2(x) + \cdots + \partial f_m(x)
                \end{align}
            </div>
            <p>where the sum of sets on the right means the Minkowski sum: \(A + B = \{a + b \mid a \in A, b \in B\}\).</p>
            <p>This property extends to infinite sums, integrals, and expectations (provided they exist).</p>
        </div>
        
        <div class="property">
            <h3>Rule 3: Affine Transformations</h3>
            <p>For a convex function \(f\) and function \(h(x) = f(Ax + b)\) where \(A\) is a matrix and \(b\) is a vector:</p>
            <div class="math-block">
                \begin{align}
                \partial h(x) = A^T \partial f(Ax + b)
                \end{align}
            </div>
            <p>That is, if \(g\) is a subgradient of \(f\) at the point \(Ax + b\), then \(A^T g\) is a subgradient of \(h\) at \(x\).</p>
        </div>
        
        <h3>6.2 Pointwise Maximum</h3>
        
        <div class="property">
            <h3>Rule 4: Pointwise Maximum</h3>
            <p>For convex functions \(f_1, f_2, \ldots, f_m\) and \(f(x) = \max_{i=1,\ldots,m} f_i(x)\):</p>
            <div class="math-block">
                \begin{align}
                \partial f(x) = \text{Conv} \left( \bigcup_{i \in I(x)} \partial f_i(x) \right)
                \end{align}
            </div>
            <p>where \(I(x) = \{i \mid f_i(x) = f(x)\}\) is the set of "active" functions at \(x\), and \(\text{Conv}\) denotes the convex hull.</p>
        </div>
        
        <div class="example">
            <h3>Example: Maximum of Differentiable Functions</h3>
            <p>Consider \(f(x) = \max\{x^2, 2x\}\). Let's find the subdifferential at different points:</p>
            
            <p>For \(x < 0\): \(2x < 0 < x^2\), so \(f(x) = x^2\) and \(\partial f(x) = \{2x\}\).</p>
            
            <p>For \(x > 1\): \(x^2 > 2x\), so \(f(x) = x^2\) and \(\partial f(x) = \{2x\}\).</p>
            
            <p>For \(0 < x < 1\): \(2x > x^2\), so \(f(x) = 2x\) and \(\partial f(x) = \{2\}\).</p>
            
            <p>At \(x = 0\): Both functions are active since \(f_1(0) = f_2(0) = 0\). We have \(\partial f_1(0) = \{0\}\) and \(\partial f_2(0) = \{2\}\), so \(\partial f(0) = \text{Conv}(\{0, 2\}) = [0, 2]\).</p>
            
            <p>At \(x = 1\): Both functions are active since \(f_1(1) = f_2(1) = 1\). We have \(\partial f_1(1) = \{2 \cdot 1\} = \{2\}\) and \(\partial f_2(1) = \{2\}\), so \(\partial f(1) = \{2\}\).</p>
        </div>
        
        <div class="diagram">
            <svg width="500" height="400" viewBox="0 0 500 400">
                <!-- Coordinate axes -->
                <line x1="50" y1="250" x2="450" y2="250" stroke="#333" stroke-width="2"/>
                <line x1="250" y1="50" x2="250" y2="350" stroke="#333" stroke-width="2"/>
                
                <!-- Parabola x^2 -->
                <path d="M50,450 Q150,350 250,250 T450,50" fill="none" stroke="#e74c3c" stroke-width="2"/>
                <text x="400" y="100" font-size="14" fill="#e74c3c">x²</text>
                
                <!-- Line 2x -->
                <line x1="150" y1="250" x2="350" y2="50" stroke="#3498db" stroke-width="2"/>
                <text x="360" y="50" font-size="14" fill="#3498db">2x</text>
                
                <!-- Maximum function -->
                <path d="M50,450 Q150,350 250,250 L300,150 L350,50" fill="none" stroke="#27ae60" stroke-width="3"/>
                <text x="280" y="120" font-size="14" fill="#27ae60">f(x) = max{x², 2x}</text>
                
                <!-- Points of interest -->
                <circle cx="250" cy="250" r="4" fill="#9b59b6"/>
                <text x="255" y="240" font-size="12" fill="#9b59b6">x = 0</text>
                
                <circle cx="300" cy="150" r="4" fill="#9b59b6"/>
                <text x="305" y="140" font-size="12" fill="#9b59b6">x = 1</text>
                
                <!-- Labels -->
                <text x="460" y="250" font-size="14">x</text>
                <text x="250" y="40" font-size="14">f(x)</text>
                <text x="250" y="370" font-size="16" text-anchor="middle">Subdifferential of max{x², 2x}</text>
                
                <!-- Subdifferential visualization -->
                <line x1="100" y1="320" x2="400" y2="320" stroke="#333" stroke-width="2"/>
                <text x="410" y="320" font-size="14">x</text>
                
                <!-- Subgradient at different points -->
                <line x1="150" y1="320" x2="150" y2="300" stroke="#f39c12" stroke-width="3"/>
                <circle cx="150" cy="300" r="4" fill="#f39c12"/>
                <text x="130" y="290" font-size="12" fill="#f39c12">2x</text>
                
                <line x1="250" y1="320" x2="250" y2="300" stroke="#f39c12" stroke-width="3"/>
                <line x1="235" y1="300" x2="265" y2="300" stroke="#f39c12" stroke-width="3"/>
                <text x="270" y="300" font-size="12" fill="#f39c12">[0,2]</text>
                
                <line x1="300" y1="320" x2="300" y2="300" stroke="#f39c12" stroke-width="3"/>
                <circle cx="300" cy="300" r="4" fill="#f39c12"/>
                <text x="305" y="300" font-size="12" fill="#f39c12">2</text>
                
                <line x1="350" y1="320" x2="350" y2="300" stroke="#f39c12" stroke-width="3"/>
                <circle cx="350" cy="300" r="4" fill="#f39c12"/>
                <text x="355" y="300" font-size="12" fill="#f39c12">2x</text>
            </svg>
            <p><em>Figure 6: Subdifferential of the pointwise maximum of two functions</em></p>
        </div>
        
        <div class="example">
            <h3>Example: L1-Norm</h3>
            <p>The L1-norm \(\|x\|_1 = |x_1| + \dots + |x_n|\) is a sum of absolute values. Using the sum rule and our knowledge of the subdifferential of the absolute value function:</p>
            <div class="math-block">
                \begin{align}
                \partial \|x\|_1 &= \partial |x_1| + \dots + \partial |x_n| \\
                &= \left\{g \in \mathbb{R}^n \mid g_i \in 
                \begin{cases}
                \{-1\} & \text{if } x_i < 0 \\
                [-1, 1] & \text{if } x_i = 0 \\
                \{1\} & \text{if } x_i > 0
                \end{cases}
                \right\}
                \end{align}
            </div>
        </div>
        
        <h3>6.3 Supremum Operation</h3>
        
        <div class="property">
            <h3>Rule 5: Supremum Over a Set</h3>
            <p>For a function \(f(x) = \sup_{\alpha \in A} f_\alpha(x)\), where \(f_\alpha\) are convex functions and the supremum is attained:</p>
            <div class="math-block">
                \begin{align}
                \partial f(x) = \text{Conv} \left( \bigcup_{\alpha \in A(x)} \partial f_\alpha(x) \right)
                \end{align}
            </div>
            <p>where \(A(x) = \{\alpha \in A \mid f_\alpha(x) = f(x)\}\) is the set of "active" functions at \(x\).</p>
        </div>
        
        <div class="example">
            <h3>Example: Maximum Eigenvalue Function</h3>
            <p>Consider the function \(f(X) = \lambda_{max}(X)\), which returns the maximum eigenvalue of a symmetric matrix \(X\). This can be expressed as:</p>
            <div class="math-block">
                \begin{align}
                f(X) = \lambda_{max}(X) = \max_{\|y\|_2 = 1} y^T X y
                \end{align}
            </div>
            
            <p>Each function \(f_y(X) = y^T X y\) is linear in \(X\) with gradient \(\nabla f_y(X) = yy^T\).</p>
            
            <p>The set of active indices at \(X\) corresponds to the set of normalized eigenvectors associated with the maximum eigenvalue of \(X\). Therefore:</p>
            <div class="math-block">
                \begin{align}
                \partial f(X) = \text{Conv} \{yy^T \mid Xy = \lambda_{max}(X)y, \|y\|_2 = 1\}
                \end{align}
            </div>
        </div>
        
        <h3>6.4 Minimization Over Variables</h3>
        
        <div class="property">
            <h3>Rule 6: Minimization Over Some Variables</h3>
            <p>For a function \(f(x) = \inf_y F(x, y)\) where \(F(x, y)\) is jointly convex in \(x\) and \(y\), and the infimum is attained at \(y \in Y_x\):</p>
            <div class="math-block">
                \begin{align}
                \partial f(x) = \{g_x \mid \exists y \in Y_x \text{ such that } (g_x, 0) \in \partial F(x, y)\}
                \end{align}
            </div>
            <p>In other words, \(g\) is a subgradient of \(f\) at \(x\) if and only if there exists some \(y \in Y_x\) such that \((g, 0)\) is a subgradient of \(F\) at \((x, y)\).</p>
        </div>
        
        <div class="example">
            <h3>Example: Optimal Value Function</h3>
            <p>Consider the optimal value function of a standard form convex optimization problem with \(x\) and \(y\) as parameters:</p>
            <div class="math-block">
                \begin{align}
                f(x, y) = \inf_z \{f_0(z) \mid f_i(z) \leq x_i, i = 1, \ldots, m, Az = y\}
                \end{align}
            </div>
            
            <p>Under appropriate conditions (e.g., strong duality), we can relate subgradients of \(f\) to the dual solution of the problem. Specifically, if \(\lambda^*\) and \(\nu^*\) are optimal dual variables, then \(-(\lambda^*, \nu^*)\) is a subgradient of \(f\) at \((x, y)\).</p>
        </div>
        
        <div class="note">
            <h3>Importance in Optimization</h3>
            <p>The calculus of subgradients is crucial for many optimization algorithms, including:</p>
            <ul>
                <li><strong>Subgradient methods:</strong> Iterative methods that use subgradients to minimize non-differentiable convex functions</li>
                <li><strong>Cutting-plane methods:</strong> Algorithms that use subgradients to build piecewise linear approximations of convex functions</li>
                <li><strong>Bundle methods:</strong> Methods that maintain and update a "bundle" of subgradients to better approximate the subdifferential</li>
            </ul>
            <p>These methods rely on our ability to compute at least one subgradient at each iteration, which is why the "weak" calculus of subgradients is often sufficient in practice.</p>
        </div>
    </div>

    <div class="section" id="clarke-subdifferential">
        <h2>7. Clarke Subdifferential for Non-Convex Functions</h2>
        
        <p>So far, we have focused on subgradients for convex functions. However, many practical optimization problems involve non-convex functions. In this section, we'll explore how the concept of subgradients can be generalized to non-convex functions using the Clarke subdifferential.</p>
        
        <h3>7.1 Locally Lipschitz Functions</h3>
        
        <p>The Clarke subdifferential is defined for locally Lipschitz functions, which are a broader class of functions than convex functions.</p>
        
        <div class="definition">
            <h3>Definition: Locally Lipschitz Function</h3>
            <p>A function \(f: \mathbb{R}^n \rightarrow \mathbb{R}\) is locally Lipschitz if for any bounded set \(S \subset \mathbb{R}^n\), there exists a constant \(L > 0\) such that:</p>
            <div class="math-block">
                \begin{align}
                |f(x) - f(y)| \leq L \|x - y\|_2 \quad \text{for all } x, y \in S
                \end{align}
            </div>
        </div>
        
        <p>A key result from Rademacher's theorem states that a locally Lipschitz function is differentiable almost everywhere. This means that for almost all points in the domain, the gradient exists.</p>
        
        <div class="definition">
            <h3>Definition: Clarke Subdifferential</h3>
            <p>For a locally Lipschitz function \(f: \mathbb{R}^n \rightarrow \mathbb{R}\), the Clarke subdifferential at a point \(x\) is defined as:</p>
            <div class="math-block">
                \begin{align}
                \partial_C f(x) = \text{Conv} \left\{s \in \mathbb{R}^n : \exists x^k \to x, \nabla f(x^k) \text{ exists, and } \nabla f(x^k) \to s \right\}
                \end{align}
            </div>
            <p>In other words, it is the convex hull of all possible limit points of gradients at nearby differentiable points.</p>
        </div>
        
        <div class="example">
            <h3>Example: Absolute Value Function</h3>
            <p>For \(f(x) = |x|\), the Clarke subdifferential is:</p>
            <div class="math-block">
                \begin{align}
                \partial_C f(x) = 
                \begin{cases}
                \{-1\} & \text{if } x < 0 \\
                [-1, 1] & \text{if } x = 0 \\
                \{1\} & \text{if } x > 0
                \end{cases}
                \end{align}
            </div>
            <p>Note that for this convex function, the Clarke subdifferential coincides with the regular subdifferential we defined earlier.</p>
        </div>
        
        <h3>7.2 Clarke Directional Derivative</h3>
        
        <p>Related to the Clarke subdifferential is the Clarke directional derivative, which generalizes the concept of directional derivatives to non-convex functions.</p>
        
        <div class="definition">
            <h3>Definition: Clarke Directional Derivative</h3>
            <p>For a locally Lipschitz function \(f: \mathbb{R}^n \rightarrow \mathbb{R}\), the Clarke directional derivative at \(x\) in the direction \(d\) is defined as:</p>
            <div class="math-block">
                \begin{align}
                f^\circ(x; d) = \limsup_{y \to x, t \downarrow 0} \frac{f(y + td) - f(y)}{t}
                \end{align}
            </div>
        </div>
        
        <p>The Clarke directional derivative captures the behavior of the function not just along a ray emanating from \(x\) (as the ordinary directional derivative does), but in a neighborhood of \(x\).</p>
        
        <div class="property">
            <h3>Property: Support Function Representation</h3>
            <p>The Clarke directional derivative can be expressed in terms of the Clarke subdifferential:</p>
            <div class="math-block">
                \begin{align}
                f^\circ(x; d) = \max_{s \in \partial_C f(x)} s^T d
                \end{align}
            </div>
            <p>This is analogous to the relationship between the ordinary directional derivative and the subdifferential for convex functions.</p>
        </div>
        
        <div class="diagram">
            <svg width="500" height="300" viewBox="0 0 500 300">
                <!-- Function -->
                <path d="M50,250 L150,150 C200,100 250,200 300,150 L450,150" fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Point of non-differentiability -->
                <circle cx="150" cy="150" r="5" fill="#3498db"/>
                <text x="140" y="135" font-size="14">x</text>
                
                <!-- Gradients at nearby points -->
                <line x1="100" y1="200" x2="140" y2="160" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead4)"/>
                <text x="90" y="210" font-size="12" fill="#27ae60">∇f(x₁)</text>
                
                <line x1="200" y1="100" x2="160" y2="140" stroke="#27ae60" stroke-width="2" marker-end="url(#arrowhead5)"/>
                <text x="210" y="90" font-size="12" fill="#27ae60">∇f(x₂)</text>
                
                <!-- Clarke subdifferential -->
                <path d="M140,160 L160,140 L140,120" fill="#f39c12" fill-opacity="0.3" stroke="#f39c12" stroke-width="2"/>
                <text x="160" y="170" font-size="12" fill="#f39c12">∂ₖf(x)</text>
                
                <defs>
                    <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                    </marker>
                    <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                    </marker>
                </defs>
                
                <!-- Labels -->
                <text x="250" y="270" font-size="16" text-anchor="middle">Clarke subdifferential as the convex hull of limiting gradients</text>
            </svg>
            <p><em>Figure 7: The Clarke subdifferential at a non-differentiable point of a non-convex function</em></p>
        </div>
        
        <h3>7.3 Properties of the Clarke Subdifferential</h3>
        
        <div class="property">
            <h3>Key Properties</h3>
            <ul>
                <li>For a convex function, the Clarke subdifferential coincides with the regular subdifferential.</li>
                <li>For a differentiable function, the Clarke subdifferential at \(x\) is the singleton \(\{\nabla f(x)\}\).</li>
                <li>The Clarke subdifferential is always a non-empty, convex, and compact set.</li>
                <li>If \(x\) is a local minimum or maximum of \(f\), then \(0 \in \partial_C f(x)\). However, the converse is not necessarily true.</li>
            </ul>
        </div>
        
        <div class="note">
            <h3>Limits of the Clarke Subdifferential</h3>
            <p>While the Clarke subdifferential generalizes the concept of subgradients to non-convex functions, it has some limitations:</p>
            <ul>
                <li>The sum rule \(\partial_C(f_1 + f_2) = \partial_C f_1 + \partial_C f_2\) does not hold in general. Instead, we have the weaker inclusion \(\partial_C(f_1 + f_2) \subset \partial_C f_1 + \partial_C f_2\).</li>
                <li>The condition \(0 \in \partial_C f(x)\) is necessary but not sufficient for \(x\) to be a local minimizer.</li>
                <li>Computing the Clarke subdifferential can be challenging for complex non-convex functions.</li>
            </ul>
        </div>
        
        <div class="example">
            <h3>Example: Non-convex Function</h3>
            <p>Consider the function \(f(x) = \min\{x, 0\} + \max\{x, 0\}\). Let's compute \(\partial_C f_1(0)\), \(\partial_C f_2(0)\), and \(\partial_C f(0)\) where \(f_1(x) = \min\{x, 0\}\) and \(f_2(x) = \max\{x, 0\}\).</p>
            
            <p>We have \(\partial_C f_1(0) = [0, 1]\), \(\partial_C f_2(0) = [0, 1]\), but \(\partial_C f(0) = \{1\}\).</p>
            
            <p>Note that \(\partial_C f(0) \subsetneq \partial_C f_1(0) + \partial_C f_2(0) = [0, 2]\), showing that the sum rule does not hold with equality.</p>
        </div>
        
        <p>The Clarke subdifferential is a powerful tool for analyzing non-convex optimization problems, particularly when dealing with locally Lipschitz functions. It provides a way to characterize stationary points and develop algorithms for non-convex optimization.</p>
    </div>

    <div class="section" id="applications">
        <h2>8. Practical Applications</h2>
        
        <p>Subgradient theory has numerous practical applications in optimization and machine learning. Here, we highlight some key areas where subgradients play a crucial role.</p>
        
        <h3>8.1 Subgradient Methods</h3>
        
        <p>Subgradient methods are a class of algorithms for minimizing non-differentiable convex functions. The basic subgradient method is similar to gradient descent, but uses a subgradient in place of the gradient:</p>
        
        <div class="math-block">
            \begin{align}
            x^{k+1} = x^k - \alpha_k g^k
            \end{align}
        </div>
        
        <p>where \(g^k \in \partial f(x^k)\) is any subgradient of \(f\) at \(x^k\), and \(\alpha_k > 0\) is the step size.</p>
        
        <p>Unlike gradient descent, the function value may not decrease at each iteration. Common step size rules include:</p>
        <ul>
            <li>Constant step size: \(\alpha_k = \alpha\)</li>
            <li>Diminishing step size: \(\alpha_k = \frac{\alpha}{\sqrt{k}}\) or \(\alpha_k = \frac{\alpha}{k}\)</li>
            <li>Polyak step size: \(\alpha_k = \frac{f(x^k) - f^*}{\|g^k\|^2}\) (where \(f^*\) is the optimal value)</li>
        </ul>
        
        <div class="example">
            <h3>Example: L1-Regularized Least Squares</h3>
            <p>Consider the problem of minimizing \(f(x) = \frac{1}{2}\|Ax - b\|_2^2 + \lambda\|x\|_1\), which appears in sparse regression problems like Lasso.</p>
            
            <p>The function is non-differentiable due to the L1-norm term. A subgradient at \(x\) is:</p>
            <div class="math-block">
                \begin{align}
                g = A^T(Ax - b) + \lambda s
                \end{align}
            </div>
            <p>where \(s_i = \text{sign}(x_i)\) if \(x_i \neq 0\), and \(s_i \in [-1, 1]\) if \(x_i = 0\).</p>
        </div>
        
        <h3>8.2 Non-Smooth Convex Optimization in Machine Learning</h3>
        
        <p>Many machine learning problems involve non-smooth convex optimization:</p>
        <ul>
            <li><strong>Support Vector Machines:</strong> The hinge loss function is non-differentiable.</li>
            <li><strong>Sparse Models:</strong> L1-regularization induces sparsity but introduces non-differentiability.</li>
            <li><strong>Robust Optimization:</strong> Max-type loss functions for robust learning are non-differentiable.</li>
            <li><strong>Deep Learning:</strong> ReLU and other activation functions are non-differentiable.</li>
        </ul>
        
        <p>Subgradient methods and their variants (e.g., stochastic subgradient methods) are widely used in these contexts.</p>
        
        <h3>8.3 Convex Programming and Duality</h3>
        
        <p>Subgradients are intimately connected to duality in convex optimization. Specifically:</p>
        <ul>
            <li>The optimal dual variables of a convex program provide subgradients of the optimal value function with respect to the constraints.</li>
            <li>The relationship between primal and dual solutions can often be expressed in terms of subgradient conditions.</li>
            <li>The KKT conditions, which characterize optimality in constrained optimization, can be formulated using subgradients for non-differentiable objectives.</li>
        </ul>
        
        <h3>8.4 Non-Convex Optimization</h3>
        
        <p>The Clarke subdifferential extends subgradient theory to non-convex settings, enabling applications such as:</p>
        <ul>
            <li><strong>Non-convex regularization:</strong> Functions like SCAD or MCP that provide better statistical properties than L1-regularization.</li>
            <li><strong>Deep Learning with Non-differentiable Activation Functions:</strong> Analysis of networks with ReLU, LeakyReLU, etc.</li>
            <li><strong>Game Theory:</strong> Analysis of non-smooth equilibrium problems.</li>
            <li><strong>Control Theory:</strong> Stability analysis of non-smooth dynamical systems.</li>
        </ul>
    </div>

    <div class="section" id="conclusion">
        <h2>9. Conclusion</h2>
        
        <p>Subgradients provide a powerful framework for generalizing derivatives to non-differentiable convex functions. They enable the development of optimization algorithms for a wide range of practical problems where traditional calculus-based methods cannot be applied.</p>
        
        <p>In this tutorial, we've covered:</p>
        <ul>
            <li>The definition of subgradients and subdifferentials</li>
            <li>Basic properties of subdifferentials, including convexity and boundedness</li>
            <li>The connection between subgradients and directional derivatives</li>
            <li>Optimality conditions for unconstrained and constrained optimization</li>
            <li>The calculus of subgradients, with rules for various operations</li>
            <li>The Clarke subdifferential for non-convex functions</li>
            <li>Practical applications in optimization and machine learning</li>
        </ul>
        
        <p>Understanding subgradients is crucial for anyone working in modern optimization, machine learning, and data science, as many of the most powerful models and algorithms rely on non-differentiable functions and constraints.</p>
        
        <p>As computational methods continue to advance, the theory of subgradients will play an increasingly important role in solving complex optimization problems across a wide range of disciplines.</p>
    </div>

    <div class="section" id="references">
        <h2>10. References</h2>
        
        <ol>
            <li>Boyd, S., Vandenberghe, L. (2004). <em>Convex Optimization</em>. Cambridge University Press.</li>
            <li>Clarke, F. H. (1990). <em>Optimization and Nonsmooth Analysis</em>. SIAM.</li>
            <li>Hiriart-Urruty, J., Lemaréchal, C. (1993). <em>Convex Analysis and Minimization Algorithms I & II</em>. Springer.</li>
            <li>Hiriart-Urruty, J., Lemaréchal, C. (2001). <em>Fundamentals of Convex Analysis</em>. Springer.</li>
            <li>Li, J., So, A. M., Ma, W. (2020). <em>Understanding notions of stationarity in nonsmooth optimization</em>. IEEE Signal Processing Magazine, 37(5):18–31.</li>
            <li>Rockafellar, R. T., Wets, J-B. (2009). <em>Variational analysis</em>, volume 317. Springer Science & Business Media.</li>
        </ol>
    </div>
</body>
</html> 