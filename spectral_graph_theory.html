<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Understanding Spectral Graph Theory: A Step-by-Step Guide</title>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
        }
        h1 {
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        .container {
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .formula {
            background-color: #f5f7fa;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
            overflow-x: auto;
        }
        .highlight {
            color: #3498db;
            font-weight: bold;
        }
        .note {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #2980b9;
        }
        .example {
            background-color: #fef9e7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #f39c12;
        }
        .definition {
            background-color: #eaf7ea;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #27ae60;
        }
        .theorem {
            background-color: #f8e5f2;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #8e44ad;
        }
        .proof {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #95a5a6;
        }
        figure {
            text-align: center;
            margin: 20px 0;
        }
        figcaption {
            font-style: italic;
            margin-top: 10px;
            color: #666;
        }
        .section {
            margin-bottom: 40px;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 15px;
        }
        .toc li {
            margin: 8px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .matrix {
            text-align: center;
        }
        .graph-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }
        .graph {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Understanding Spectral Graph Theory</h1>
        <p class="subtitle" style="text-align: center; font-style: italic; margin-top: -20px;">A Step-by-Step Guide to Graph Analysis Through Eigenvalues</p>

        <div class="toc">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#introduction">1. Introduction to Spectral Graph Theory</a></li>
                <li><a href="#background">2. Background and Fundamental Concepts</a>
                    <ul>
                        <li><a href="#graph-basics">2.1 Graph Theory Basics</a></li>
                        <li><a href="#matrices">2.2 Graph Matrices: Adjacency and Laplacian</a></li>
                    </ul>
                </li>
                <li><a href="#laplacian">3. The Laplacian Matrix and Its Properties</a>
                    <ul>
                        <li><a href="#laplacian-def">3.1 Alternative Definition of the Laplacian</a></li>
                        <li><a href="#basic-properties">3.2 Basic Properties of the Laplacian</a></li>
                        <li><a href="#eigenvalues">3.3 Eigenvalues and Connectivity</a></li>
                    </ul>
                </li>
                <li><a href="#fundamental-graphs">4. Eigenvalues and Eigenvectors of Fundamental Graphs</a>
                    <ul>
                        <li><a href="#complete">4.1 Complete Graphs</a></li>
                        <li><a href="#path">4.2 Path Graphs</a></li>
                        <li><a href="#cycle">4.3 Cycle Graphs</a></li>
                    </ul>
                </li>
                <li><a href="#bounding">5. Bounding Eigenvalues</a>
                    <ul>
                        <li><a href="#max-eigenvalue">5.1 Bounds for the Largest Eigenvalue</a></li>
                        <li><a href="#second-eigenvalue">5.2 Bounds for the Second Smallest Eigenvalue</a></li>
                    </ul>
                </li>
                <li><a href="#paths">6. Applications: Counting Paths in Graphs</a>
                    <ul>
                        <li><a href="#walks">6.1 Counting Walks Using Adjacency Matrix</a></li>
                        <li><a href="#simple-paths">6.2 Counting Simple Paths</a></li>
                    </ul>
                </li>
                <li><a href="#applications">7. Advanced Applications and Extensions</a></li>
                <li><a href="#conclusion">8. Conclusion and Further Reading</a></li>
            </ul>
        </div>

        <div class="section" id="introduction">
            <h2>1. Introduction to Spectral Graph Theory</h2>
            
            <p>Spectral graph theory is a fascinating branch of mathematics that combines linear algebra with graph theory. It studies the properties of graphs through the eigenvalues and eigenvectors of matrices associated with these graphs - primarily the adjacency matrix and the Laplacian matrix.</p>
            
            <p>This approach provides powerful tools for analyzing the structure and properties of graphs, with applications ranging from network analysis and machine learning to computer vision and quantum chemistry.</p>
            
            <div class="note">
                <p>The term "spectral" refers to the spectrum of a matrix - the set of its eigenvalues. Just as the spectrum of light can reveal the composition of distant stars, the spectrum of a graph can reveal its fundamental structural properties.</p>
            </div>
            
            <p>In this guide, we'll explore spectral graph theory step by step, focusing particularly on:</p>
            <ul>
                <li>The connection between eigenvalues of the Laplacian matrix and graph connectivity</li>
                <li>How to analyze fundamental graph structures through their spectral properties</li>
                <li>Methods for bounding important eigenvalues</li>
                <li>Practical applications such as counting paths within graphs</li>
            </ul>
            
            <p>We'll begin with basic concepts and gradually progress to more advanced topics, providing clear explanations, examples, and visualizations throughout.</p>
        </div>

        <div class="section" id="background">
            <h2>2. Background and Fundamental Concepts</h2>
            
            <p>Before diving into spectral graph theory, let's establish the fundamental concepts and definitions that will form the foundation of our understanding. This section introduces the basics of graph theory and the matrices that are essential for spectral analysis.</p>

            <div class="section" id="graph-basics">
                <h3>2.1 Graph Theory Basics</h3>

                <div class="definition">
                    <h4>Definition: Graph</h4>
                    <p>A graph is an ordered pair \(G = (V, E)\) of sets, where \(V\) is the set of vertices (or nodes) and \(E\) is the set of edges connecting pairs of vertices.</p>
                    <p>For an undirected graph, each edge is an unordered pair \(\{x, y\}\) where \(x, y \in V\) and \(x \neq y\).</p>
                    <p>Formally: \(E \subset \{\{x, y\} | x, y \in V, x \neq y\}\)</p>
                </div>

                <div class="graph-container">
                    <svg width="300" height="200" viewBox="0 0 300 200">
                        <!-- Simple undirected graph -->
                        <circle cx="50" cy="50" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="150" cy="50" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="250" cy="50" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="100" cy="150" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="200" cy="150" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        
                        <line x1="70" y1="50" x2="130" y2="50" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="170" y1="50" x2="230" y2="50" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="120" y1="150" x2="180" y2="150" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="58" y1="68" x2="92" y2="132" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="142" y1="68" x2="108" y2="132" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="242" y1="68" x2="208" y2="132" stroke="#2c3e50" stroke-width="2"></line>
                        
                        <text x="50" y="55" text-anchor="middle" fill="white" font-weight="bold">1</text>
                        <text x="150" y="55" text-anchor="middle" fill="white" font-weight="bold">2</text>
                        <text x="250" y="55" text-anchor="middle" fill="white" font-weight="bold">3</text>
                        <text x="100" y="155" text-anchor="middle" fill="white" font-weight="bold">4</text>
                        <text x="200" y="155" text-anchor="middle" fill="white" font-weight="bold">5</text>
                    </svg>
                    <figcaption>Example of an undirected graph with 5 vertices and 6 edges</figcaption>
                </div>

                <div class="definition">
                    <h4>Definition: Adjacency</h4>
                    <p>Two vertices \(x_i, x_j \in V\) are said to be <span class="highlight">adjacent</span> or <span class="highlight">neighbors</span> if \(\{x_i, x_j\} \in E\), meaning there is an edge connecting them.</p>
                </div>

                <div class="definition">
                    <h4>Definition: Degree</h4>
                    <p>The <span class="highlight">degree</span> of a vertex \(v\), denoted as \(d(v)\), is the number of vertices in the graph that are adjacent to \(v\), or equivalently, the number of edges incident to \(v\).</p>
                </div>

                <div class="definition">
                    <h4>Definition: Complete Graph</h4>
                    <p>A <span class="highlight">complete graph</span> is one where all pairs of vertices are connected by an edge. A complete graph with \(n\) vertices is denoted as \(K_n\).</p>
                </div>

                <div class="graph-container">
                    <svg width="300" height="200" viewBox="0 0 300 200">
                        <!-- Complete graph K5 -->
                        <circle cx="150" cy="40" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="80" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="220" cy="80" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="110" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="190" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        
                        <!-- All edges connecting each vertex to every other vertex -->
                        <line x1="150" y1="60" x2="80" y2="80" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="150" y1="60" x2="220" y2="80" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="150" y1="60" x2="110" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="150" y1="60" x2="190" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="80" y1="80" x2="220" y2="80" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="80" y1="80" x2="110" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="80" y1="80" x2="190" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="220" y1="80" x2="110" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="220" y1="80" x2="190" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="110" y1="160" x2="190" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        
                        <text x="150" y="45" text-anchor="middle" fill="white" font-weight="bold">1</text>
                        <text x="80" y="85" text-anchor="middle" fill="white" font-weight="bold">2</text>
                        <text x="220" y="85" text-anchor="middle" fill="white" font-weight="bold">3</text>
                        <text x="110" y="165" text-anchor="middle" fill="white" font-weight="bold">4</text>
                        <text x="190" y="165" text-anchor="middle" fill="white" font-weight="bold">5</text>
                    </svg>
                    <figcaption>A complete graph \(K_5\) with 5 vertices. Every vertex is connected to every other vertex.</figcaption>
                </div>

                <div class="definition">
                    <h4>Definition: Path and Cycle Graphs</h4>
                    <p>A <span class="highlight">path graph</span> \(P_n\) is a graph with \(n\) vertices where the vertices can be ordered such that there is an edge between each consecutive pair of vertices, but no other edges.</p>
                    <p>A <span class="highlight">cycle graph</span> \(C_n\) is a path graph with an additional edge connecting the first and last vertices, forming a closed loop.</p>
                </div>

                <div class="graph-container" style="display: flex; justify-content: space-around;">
                    <div>
                        <svg width="300" height="80" viewBox="0 0 300 80">
                            <!-- Path graph P5 -->
                            <circle cx="40" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="100" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="160" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="220" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="280" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            
                            <line x1="55" y1="40" x2="85" y2="40" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="115" y1="40" x2="145" y2="40" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="175" y1="40" x2="205" y2="40" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="235" y1="40" x2="265" y2="40" stroke="#2c3e50" stroke-width="2"></line>
                            
                            <text x="40" y="45" text-anchor="middle" fill="white" font-weight="bold">1</text>
                            <text x="100" y="45" text-anchor="middle" fill="white" font-weight="bold">2</text>
                            <text x="160" y="45" text-anchor="middle" fill="white" font-weight="bold">3</text>
                            <text x="220" y="45" text-anchor="middle" fill="white" font-weight="bold">4</text>
                            <text x="280" y="45" text-anchor="middle" fill="white" font-weight="bold">5</text>
                        </svg>
                        <figcaption>Path Graph \(P_5\)</figcaption>
                    </div>
                </div>
                
                <div class="graph-container">
                    <svg width="300" height="200" viewBox="0 0 300 200">
                        <!-- Cycle graph C5 -->
                        <circle cx="150" cy="40" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="80" cy="90" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="220" cy="90" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="110" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        <circle cx="190" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                        
                        <line x1="150" y1="60" x2="100" y2="90" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="150" y1="60" x2="200" y2="90" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="80" y1="110" x2="110" y2="140" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="220" y1="110" x2="190" y2="140" stroke="#2c3e50" stroke-width="2"></line>
                        <line x1="130" y1="160" x2="170" y2="160" stroke="#2c3e50" stroke-width="2"></line>
                        
                        <text x="150" y="45" text-anchor="middle" fill="white" font-weight="bold">1</text>
                        <text x="80" y="95" text-anchor="middle" fill="white" font-weight="bold">2</text>
                        <text x="220" y="95" text-anchor="middle" fill="white" font-weight="bold">3</text>
                        <text x="110" y="165" text-anchor="middle" fill="white" font-weight="bold">4</text>
                        <text x="190" y="165" text-anchor="middle" fill="white" font-weight="bold">5</text>
                    </svg>
                    <figcaption>Cycle Graph \(C_5\)</figcaption>
                </div>

                <div class="definition">
                    <h4>Definition: Walk, Path, and Connected Graph</h4>
                    <p>A <span class="highlight">walk</span> in a graph is an alternating sequence of vertices and edges, beginning and ending with a vertex, where each edge connects the vertices immediately preceding and following it.</p>
                    <p>A <span class="highlight">path</span> is a walk with no repeated vertices.</p>
                    <p>A graph is <span class="highlight">connected</span> if there exists a path between any two vertices in the graph.</p>
                </div>

                <div class="example">
                    <h4>Example: Walks vs. Paths</h4>
                    <p>Consider the graph shown above with vertices numbered 1 through 5:</p>
                    <ul>
                        <li>The sequence 1-2-3-5 is a path (and also a walk) because it traverses from vertex 1 to 5 without repeating any vertex.</li>
                        <li>The sequence 1-2-3-2-5 is a walk but not a path because vertex 2 appears twice.</li>
                    </ul>
                </div>
            </div>

            <div class="section" id="matrices">
                <h3>2.2 Graph Matrices: Adjacency and Laplacian</h3>
                
                <p>Two matrices play a central role in spectral graph theory: the <span class="highlight">adjacency matrix</span> and the <span class="highlight">Laplacian matrix</span>. These matrices capture the structure of a graph in algebraic form, allowing us to apply tools from linear algebra to analyze graph properties.</p>

                <div class="definition">
                    <h4>Definition: Adjacency Matrix</h4>
                    <p>For a graph \(G = (V, E)\) with \(V = \{1, 2, \ldots, n\}\), the adjacency matrix \(A_G\) is an \(n \times n\) matrix where the entries \(a_{i,j}\) are given by:</p>
                    <div class="formula">
                        \[a_{i,j} = \begin{cases}
                            1 & \text{if } \{i, j\} \in E, \\
                            0 & \text{otherwise}.
                        \end{cases}\]
                    </div>
                </div>

                <div class="definition">
                    <h4>Definition: Laplacian Matrix</h4>
                    <p>For a graph \(G = (V, E)\) with \(V = \{1, 2, \ldots, n\}\), the Laplacian matrix \(L_G\) is an \(n \times n\) matrix where the entries \(l_{i,j}\) are given by:</p>
                    <div class="formula">
                        \[l_{i,j} = \begin{cases}
                            d(i) & \text{if } i = j, \\
                            -1 & \text{if } \{i, j\} \in E, \\
                            0 & \text{otherwise}.
                        \end{cases}\]
                    </div>
                    <p>where \(d(i)\) is the degree of vertex \(i\).</p>
                </div>

                <div class="example">
                    <h4>Example: Matrices for a Simple Graph</h4>
                    <p>Consider a simple path graph \(P_3\) with 3 vertices connected in sequence:</p>
                    <div style="display: flex; justify-content: center; align-items: center; margin: 15px 0;">
                        <svg width="200" height="60" viewBox="0 0 200 60">
                            <circle cx="50" cy="30" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="100" cy="30" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="150" cy="30" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            
                            <line x1="65" y1="30" x2="85" y2="30" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="115" y1="30" x2="135" y2="30" stroke="#2c3e50" stroke-width="2"></line>
                            
                            <text x="50" y="35" text-anchor="middle" fill="white" font-weight="bold">1</text>
                            <text x="100" y="35" text-anchor="middle" fill="white" font-weight="bold">2</text>
                            <text x="150" y="35" text-anchor="middle" fill="white" font-weight="bold">3</text>
                        </svg>
                    </div>
                    
                    <p>The adjacency matrix \(A_{P_3}\) is:</p>
                    <div class="formula matrix">
                        \[A_{P_3} = \begin{pmatrix}
                            0 & 1 & 0 \\
                            1 & 0 & 1 \\
                            0 & 1 & 0
                        \end{pmatrix}\]
                    </div>
                    
                    <p>The Laplacian matrix \(L_{P_3}\) is:</p>
                    <div class="formula matrix">
                        \[L_{P_3} = \begin{pmatrix}
                            1 & -1 & 0 \\
                            -1 & 2 & -1 \\
                            0 & -1 & 1
                        \end{pmatrix}\]
                    </div>
                    
                    <p>Note how the Laplacian matrix has the degrees of vertices on the diagonal and -1 for adjacent vertices.</p>
                </div>

                <div class="note">
                    <p>Some important properties to observe:</p>
                    <ul>
                        <li>The adjacency matrix of an undirected graph is symmetric (\(a_{i,j} = a_{j,i}\)).</li>
                        <li>The Laplacian matrix is also symmetric.</li>
                        <li>The sum of each row (or column) in the Laplacian matrix is 0.</li>
                        <li>The Laplacian can also be expressed as \(L_G = D_G - A_G\), where \(D_G\) is the diagonal degree matrix with \(d_{i,i} = d(i)\).</li>
                    </ul>
                </div>

                <p>These matrices form the foundation for spectral graph theory. In the next section, we'll explore the properties of the Laplacian matrix and how its eigenvalues relate to fundamental properties of the graph.</p>
            </div>
        </div>

        <div class="section" id="laplacian">
            <h2>3. The Laplacian Matrix and Its Properties</h2>
            
            <p>The Laplacian matrix is of fundamental importance to spectral graph theory. In this section, we'll explore its properties and understand why it's so valuable for analyzing graph structures.</p>

            <div class="section" id="laplacian-alternative">
                <h3>3.1 Alternative Definition of the Laplacian</h3>
                
                <p>We've already defined the Laplacian matrix using its entries. However, there is an alternative and often more convenient way to define it in terms of other matrices.</p>
                
                <div class="definition">
                    <h4>Definition: Laplacian as Matrix Difference</h4>
                    <p>The Laplacian matrix can be defined as:</p>
                    <div class="formula">
                        \[L_G = D_G - A_G\]
                    </div>
                    <p>where \(D_G\) is the diagonal degree matrix with entries \(d_{i,i} = d(i)\) (the degree of vertex \(i\)) and \(A_G\) is the adjacency matrix.</p>
                </div>
                
                <div class="example">
                    <h4>Example: Alternative Calculation of Laplacian</h4>
                    <p>For our path graph \(P_3\) from the previous section, we can compute the Laplacian as follows:</p>
                    
                    <p>The degree matrix \(D_{P_3}\) is:</p>
                    <div class="formula matrix">
                        \[D_{P_3} = \begin{pmatrix}
                            1 & 0 & 0 \\
                            0 & 2 & 0 \\
                            0 & 0 & 1
                        \end{pmatrix}\]
                    </div>
                    
                    <p>The adjacency matrix \(A_{P_3}\) is:</p>
                    <div class="formula matrix">
                        \[A_{P_3} = \begin{pmatrix}
                            0 & 1 & 0 \\
                            1 & 0 & 1 \\
                            0 & 1 & 0
                        \end{pmatrix}\]
                    </div>
                    
                    <p>Therefore, the Laplacian matrix \(L_{P_3} = D_{P_3} - A_{P_3}\) is:</p>
                    <div class="formula matrix">
                        \[L_{P_3} = \begin{pmatrix}
                            1 & 0 & 0 \\
                            0 & 2 & 0 \\
                            0 & 0 & 1
                        \end{pmatrix} - \begin{pmatrix}
                            0 & 1 & 0 \\
                            1 & 0 & 1 \\
                            0 & 1 & 0
                        \end{pmatrix} = \begin{pmatrix}
                            1 & -1 & 0 \\
                            -1 & 2 & -1 \\
                            0 & -1 & 1
                        \end{pmatrix}\]
                    </div>
                </div>

                <div class="definition">
                    <h4>Definition: Normalized Laplacian</h4>
                    <p>In some applications, the <span class="highlight">normalized Laplacian</span> matrix \(\mathcal{L}_G\) is used, defined as:</p>
                    <div class="formula">
                        \[\mathcal{L}_G = D_G^{-1/2} L_G D_G^{-1/2} = I - D_G^{-1/2} A_G D_G^{-1/2}\]
                    </div>
                    <p>where \(I\) is the identity matrix and \(D_G^{-1/2}\) is the diagonal matrix with entries \(1/\sqrt{d(i)}\).</p>
                </div>
                
                <p>The normalized Laplacian has entries:</p>
                <div class="formula">
                    \[\mathcal{L}_{i,j} = \begin{cases}
                        1 & \text{if } i = j \text{ and } d(i) \neq 0, \\
                        -\frac{1}{\sqrt{d(i)d(j)}} & \text{if } i \text{ and } j \text{ are adjacent}, \\
                        0 & \text{otherwise}.
                    \end{cases}\]
                </div>
                
                <div class="note">
                    <p>The normalized Laplacian has the advantage that its eigenvalues are always between 0 and 2, making comparisons between different graphs easier. However, in this tutorial, we'll primarily focus on the standard (unnormalized) Laplacian.</p>
                </div>
            </div>

            <div class="section" id="laplacian-properties">
                <h3>3.2 Basic Properties of the Laplacian</h3>
                
                <p>The Laplacian matrix has several important properties that make it valuable for spectral graph theory:</p>
                
                <div class="theorem">
                    <h4>Theorem: Properties of the Laplacian Matrix</h4>
                    <p>For a graph \(G\) with \(n\) vertices, the Laplacian matrix \(L_G\) has the following properties:</p>
                    <ol>
                        <li>\(L_G\) is symmetric and positive semi-definite.</li>
                        <li>The sum of entries in each row (and column) is zero: \(\sum_{j} l_{i,j} = 0\) for all \(i\).</li>
                        <li>The smallest eigenvalue of \(L_G\) is 0, and the all-ones vector \(\mathbf{1}\) is a corresponding eigenvector.</li>
                        <li>The multiplicity of the eigenvalue 0 equals the number of connected components in \(G\).</li>
                    </ol>
                </div>
                
                <div class="proof-sketch">
                    <h4>Proof Sketch</h4>
                    <p><strong>Property 1:</strong> Symmetry follows directly from the symmetry of both \(D_G\) and \(A_G\). For positive semi-definiteness, for any vector \(\mathbf{x} \in \mathbb{R}^n\), we can show that:</p>
                    <div class="formula">
                        \[\mathbf{x}^T L_G \mathbf{x} = \sum_{\{i,j\} \in E} (x_i - x_j)^2 \geq 0\]
                    </div>
                    
                    <p><strong>Property 2:</strong> For any row \(i\), we have \(l_{i,i} = d(i)\) (the degree), and \(l_{i,j} = -1\) for each of the \(d(i)\) neighbors \(j\) of vertex \(i\). All other entries are 0, so the sum is \(d(i) + d(i) \cdot (-1) = 0\).</p>
                    
                    <p><strong>Property 3:</strong> From property 2, we can verify that \(L_G \mathbf{1} = \mathbf{0}\), proving that \(\mathbf{1}\) is an eigenvector with eigenvalue 0.</p>
                    
                    <p><strong>Property 4:</strong> This requires a more involved proof, which we'll provide intuition for in the next subsection.</p>
                </div>
                
                <div class="formula">
                    <h4>Quadratic Form of the Laplacian</h4>
                    <p>For any vector \(\mathbf{x} \in \mathbb{R}^n\), the quadratic form of the Laplacian has a beautiful interpretation:</p>
                    <div class="formula">
                        \[\mathbf{x}^T L_G \mathbf{x} = \sum_{\{i,j\} \in E} (x_i - x_j)^2\]
                    </div>
                    <p>This shows that the Laplacian measures the "smoothness" of a function \(\mathbf{x}\) on the vertices. If adjacent vertices have similar values (small \(x_i - x_j\)), then \(\mathbf{x}^T L_G \mathbf{x}\) is small.</p>
                </div>
            </div>

            <div class="section" id="eigenvalues-connectivity">
                <h3>3.3 Eigenvalues and Connectivity</h3>
                
                <p>The eigenvalues of the Laplacian matrix, also called the <span class="highlight">spectrum</span> of the graph, provide significant information about the graph structure. We usually order them as:</p>
                <div class="formula">
                    \[0 = \lambda_1 \leq \lambda_2 \leq \lambda_3 \leq \ldots \leq \lambda_n\]
                </div>
                
                <div class="theorem">
                    <h4>Theorem: Connectivity and Eigenvalues</h4>
                    <p>For a graph \(G\) with \(n\) vertices:</p>
                    <ol>
                        <li>The graph \(G\) is connected if and only if \(\lambda_2 > 0\).</li>
                        <li>The multiplicity of the eigenvalue 0 equals the number of connected components in \(G\).</li>
                    </ol>
                </div>
                
                <div class="proof-intuition">
                    <h4>Intuition for the Proof</h4>
                    <p>If a graph has \(k\) connected components, we can construct \(k\) linearly independent eigenvectors with eigenvalue 0. Each eigenvector has constant values within a connected component and zero elsewhere.</p>
                    
                    <p>For instance, if the graph has two connected components \(C_1\) and \(C_2\), then we can define two eigenvectors:</p>
                    <ul>
                        <li>\(\mathbf{v}_1\) with 1's for vertices in \(C_1\) and 0's elsewhere</li>
                        <li>\(\mathbf{v}_2\) with 1's for vertices in \(C_2\) and 0's elsewhere</li>
                    </ul>
                    <p>Both vectors satisfy \(L_G \mathbf{v} = \mathbf{0}\), as the difference across any edge is zero.</p>
                </div>
                
                <div class="example">
                    <h4>Example: Disconnected Graph Spectrum</h4>
                    <p>Consider a disconnected graph consisting of two separate triangles:</p>
                    
                    <div style="display: flex; justify-content: center; align-items: center; margin: 15px 0;">
                        <svg width="300" height="120" viewBox="0 0 300 120">
                            <!-- First triangle -->
                            <circle cx="50" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="100" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="75" cy="85" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            
                            <line x1="50" y1="40" x2="100" y2="40" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="50" y1="40" x2="75" y2="85" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="100" y1="40" x2="75" y2="85" stroke="#2c3e50" stroke-width="2"></line>
                            
                            <text x="50" y="45" text-anchor="middle" fill="white" font-weight="bold">1</text>
                            <text x="100" y="45" text-anchor="middle" fill="white" font-weight="bold">2</text>
                            <text x="75" y="90" text-anchor="middle" fill="white" font-weight="bold">3</text>
                            
                            <!-- Second triangle -->
                            <circle cx="200" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="250" cy="40" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            <circle cx="225" cy="85" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="2"></circle>
                            
                            <line x1="200" y1="40" x2="250" y2="40" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="200" y1="40" x2="225" y2="85" stroke="#2c3e50" stroke-width="2"></line>
                            <line x1="250" y1="40" x2="225" y2="85" stroke="#2c3e50" stroke-width="2"></line>
                            
                            <text x="200" y="45" text-anchor="middle" fill="white" font-weight="bold">4</text>
                            <text x="250" y="45" text-anchor="middle" fill="white" font-weight="bold">5</text>
                            <text x="225" y="90" text-anchor="middle" fill="white" font-weight="bold">6</text>
                        </svg>
                    </div>
                    
                    <p>The Laplacian matrix for this graph will have eigenvalue 0 with multiplicity 2. The corresponding eigenvectors are:</p>
                    <ul>
                        <li>\(\mathbf{v}_1 = (1, 1, 1, 0, 0, 0)^T\) (representing the first component)</li>
                        <li>\(\mathbf{v}_2 = (0, 0, 0, 1, 1, 1)^T\) (representing the second component)</li>
                    </ul>
                </div>
                
                <div class="definition">
                    <h4>Definition: Algebraic Connectivity</h4>
                    <p>The second smallest eigenvalue of the Laplacian matrix, \(\lambda_2\), is known as the <span class="highlight">algebraic connectivity</span> or <span class="highlight">Fiedler value</span> of the graph (named after Miroslav Fiedler).</p>
                </div>
                
                <div class="note">
                    <p>The algebraic connectivity \(\lambda_2\) is a measure of how well-connected the graph is. A larger value indicates a more connected graph. This eigenvalue has numerous applications, including in graph partitioning and network robustness analysis.</p>
                </div>
                
                <div class="theorem">
                    <h4>Theorem: Bounds on Algebraic Connectivity</h4>
                    <p>For a connected graph \(G\) with \(n\) vertices:</p>
                    <div class="formula">
                        \[\frac{4}{n \cdot \text{diam}(G)} \leq \lambda_2 \leq \frac{n}{n-1} \cdot \kappa(G)\]
                    </div>
                    <p>where \(\text{diam}(G)\) is the diameter of the graph (the maximum distance between any two vertices) and \(\kappa(G)\) is the vertex connectivity (the minimum number of vertices whose removal disconnects the graph).</p>
                </div>
                
                <p>This relationship between algebraic connectivity and graph connectivity is one of the core reasons why spectral graph theory is so powerful - it connects algebraic properties (eigenvalues) with fundamental graph theoretic properties.</p>
            </div>
        </div>

        <!-- This is just the outline. The complete HTML document will be built chunk by chunk with detailed content for each section. -->
    </div>
</body>
</html> 