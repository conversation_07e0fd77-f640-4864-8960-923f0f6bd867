In the field of numerical optimization, the primary goal is to find the values of variables that minimize or maximize a given function. One common formulation of an optimization problem is to find a point $$w^*$$ in a $$d$$-dimensional space that minimizes a function $$f$$:

$$w^* = \argmin_{w \in \mathbb{R}^d}f(w)$$

Here, $$w^*$$ is called the optimal point, and $$f(w)$$ is the objective function which we aim to minimize over the possible values of $$w$$ in the real vector space $$\mathbb{R}^d$$.

# **Gradient descent method**

![](https://nwkd3p3k202.feishu.cn/space/api/box/stream/download/asynccode/?code=MjM4YmU2YTA1N2VkOWMwY2E3ZWI5MDA4ZmUxMGYzYmJfUXdDZ1RxS0dtY3dvdVZ1N0ViMm1tZHFwWUZNdG5JV2pfVG9rZW46Slpaa2JRUUROb0MxQnp4THhtS2NyNWFRbmxnXzE3NTEyODA3Mjc6MTc1MTI4NDMyN19WNA)

One of the most widely used methods to solve such optimization problems is the gradient descent algorithm. This method is particularly favored due to its simplicity and effectiveness in handling large-scale problems. The gradient descent algorithm iteratively updates the point $$w$$ by moving it in the direction that locally decreases $$f$$. This direction is determined by the negative of the gradient $$\nabla f(w)$$ at $$w$$, as the gradient points in the direction of the steepest ascent of $$f$$ (read more about [ Derivatives and gradients](https://nwkd3p3k202.feishu.cn/wiki/Iskkw7IsYiZrR3kmguYcPqkonLe)). The update rule can be expressed as:

$$w^{(t+1)} = w^{(t)} - \alpha \nabla f(w^{(t)})$$

Where:

* $$w^{(t)}$$ is the value of $$w$$ at iteration $$t$$,

* $$\alpha$$ is a positive scalar known as the *learning rate* or step size,

* $$\nabla f(w^{(t)})$$ is the gradient of $$f$$ at $$w^{(t)}$$.

# **Convergence analysis**

In this section we will learn why gradient descent can help us find the optimal solution.

> Definition (**Lipschitz continuous**). A function $$f: \mathbb{R}^d \rightarrow \mathbb{R}^k$$ is called Lipschitz continuous with Lipschitz constant $$L > 0$$, if for all $$w, v \in \mathbb{R}^d$$,
>
> $$||f(w) - f(v)|| \le L ||w - v||$$

> Definition (**Smooth**). A function $$f: \mathbb{R}^d \rightarrow \mathbb{R}$$ is called $$L$$-smooth for some $$L> 0$$, if the gradient is Lipschitz continuous:
>
> $$||\nabla f(w) - \nabla f(v)|| \le L ||w - v||$$
>
> For all $$w, v \in \mathbb{R}^d$$.

> Lemma. If $$f$$ is $$L$$-smooth then for all $$x \in \mathbb{R}^d$$, then $$\nabla^2f(x) \preceq L\mathbf{I}$$, where $$\mathbf{I}$$ is the identity matrix.

Proof: Let $$\mathbf{u}$$ be a unit vector and $$t \ne 0$$ is a small scalar. If $$f$$ is twice continuously differentiable, the first-order Taylor expansion of $$\nabla f(x + t\mathbf{u})$$ around $$x$$ gives:

$$\nabla f(x + t\mathbf{u}) = \nabla f(x) + \nabla^2 f(x)(t\mathbf{u}) + o(\|t\mathbf{u}\|) \text{ as } \|t\mathbf{u}\| \to 0.$$

Taking norms and rearranging, we have:

$$\|\nabla f(x + t\mathbf{u}) - \nabla f(x)\| = \|\nabla^2 f(x)(t\mathbf{u}) + o(\|t\mathbf{u}\|)\|$$.

Dividing both sides by $$\|t\mathbf{u}\|$$ and taking the limit as $$t\to 0$$, we find:

$$\lim_{t \to 0} \frac{\|\nabla f(x + t\mathbf{u}) - \nabla f(x)\|}{\|t\mathbf{u}\|} = \frac{\|\nabla^2 f(x)(t\mathbf{u})\|}{\|t\mathbf{u}\|} = \frac{\|\nabla^2 f(x)\mathbf{u}\|}{\|\mathbf{u}\|} = \|\nabla^2 f(x)\mathbf{u}\|$$.

The gradient $$\nabla f$$ being Lipschitz continuous with constant $$L$$ (because $$f$$ is $$L$$-smooth) means that,

$$\frac{\|\nabla f(x + t\mathbf{u}) - \nabla f(x)\|}{\|t\mathbf{u}\|} \leq L$$,

And hence:

$$\|\nabla^2 f(x)\mathbf{u}\| \le L$$.

Given the definition of the operator norm of $$\nabla^2 f(x)$$: $$\|\nabla^2 f(x)\| = \sup_{\|h\|=1} \|\nabla^2 f(x) h\|$$, and taking the supremum in the above gives:

$$\|\nabla^2 f(x)\| \le L$$.

For any vector $$\mathbf{z}$$,

$$\mathbf{z}^T(L\mathbf{I} - \nabla^2 f(x)) \mathbf{z} = L\|\mathbf{z}\|^2 - \mathbf{z}^T \nabla^2 f(x) \mathbf{z}$$

Note the last term $$\mathbf{z}^T \nabla^2 f(x) \mathbf{z} \le L\|\mathbf{z}\|^2$$, given that $$\|\nabla^2 f(x)\| \le L$$. We therefore arrive:

$$\mathbf{z}^T(L\mathbf{I} - \nabla^2 f(x)) \mathbf{z} \succeq L\|\mathbf{z}\|^2 - L\|\mathbf{z}\|^2 = 0 \Rightarrow \nabla^2f(x) \preceq L\mathbf{I}$$.

> Lemma. If $$f$$ is $$L$$-smooth then for all $$x \in \mathbb{R}^d$$, the following holds:
>
> 1. $$f(x - \frac{1}L \nabla f(x)) - f(x) \le - \frac{1}{2L}||\nabla f(x)||^2_2$$;
>
> 2. $$f(x^*) - f(x) \le -\frac{1}{2L}||\nabla f(x)||^2_2$$.

Proof of (1): Since $$f$$ is smooth,



> Theorem (**Convergence of gradient descent**). Suppose the function $$f: \mathbb{R}^n \rightarrow \mathbb{R}$$ is convex and differentiable, and that its gradient is Lipschitz continuous with constant $$L > 0$$. Then if we run gradient descent for $$k$$ iterations with a fixed learning rate $$0 < \alpha\le \frac{1}L$$, it will yield a solution $$f^{(k)}$$ which satisfies:
>
> $$f(w^{(k)}) - f(w^*) \le \frac{||w^{(0)} - w^*||^2}{2\alpha k}$$,
>
> where $$f(w^*)$$ is the optimal value. Intuitively, the theorem means that gradient descent is guaranteed to converge and that it converges with rate $$O(1/k)$$.

Proof:&#x20;



The learning rate $$\alpha$$ is a crucial parameter in the gradient descent algorithm. If it is too small, the algorithm will converge slowly, requiring many iterations to reach the vicinity of $$w^*$$. Conversely, if $$\alpha$$ is too large, the algorithm may overshoot the minimum or even diverge, failing to converge to an optimal value.

# Example – logistic regression

Let $$\mathcal{D} = \{(\mathbf{X}^{(1)}, y^{(1)}),...,(\mathbf{X}^{(n)}, y^{(n)})\}$$ be the dataset. The log-likelihood of logistic regression model is:

$$\log \mathcal{L} = \sum_{i=1}^n [y^{(i)} \log \sigma(\mathbf{X}^{(i)}\beta) + (1-y^{(i)}) \log(1 - \sigma(\mathbf{X}^{(i)}\beta))]$$

&#x20;Here is the partial derivative of log-likelihood with respect to each parameter $$\beta_j$$:

$$\frac{\partial(\log\mathcal{L})}{\partial\beta_j} = \sum_{i=1}^n [y^{(i)} - \sigma(\mathbf{X}^{(i)}\beta)]x_j^{(i)}$$&#x20;

Parameter update:

$$\beta_j^{\text{new}} = \beta_j^{\text{old}} - \alpha \sum_{i=1}^n [y^{(i)} - \sigma(\mathbf{X}^{(i)}\beta)]x_j^{(i)}$$

Here is a code example:

```python
import numpy as np

def sigmoid(z):
    """Calculate the sigmoid function."""
    return 1 / (1 + np.exp(-z))

def compute_log_likelihood(X, y, beta):
    """Compute the log-likelihood of the logistic regression model."""
    z = np.dot(X, beta)
    return np.sum(y * np.log(sigmoid(z)) + (1 - y) * np.log(1 - sigmoid(z)))

def compute_gradients(X, y, beta):
    """Compute the gradient of the log-likelihood."""
    z = np.dot(X, beta)
    errors = y - sigmoid(z)
    return np.dot(X.T, errors)

def logistic_regression(X, y, alpha):
    """Perform logistic regression using gradient ascent."""
    beta = np.zeros(X.shape[1])  # Initialize parameters
    likelihood = compute_log_likelihood(X, y, beta)

    # Iterate to perform gradient ascent
    delta = float('inf')
    while delta > 0.0001:
        print(f'likelihood: {likelihood}')
        gradients = compute_gradients(X, y, beta)
        beta += alpha * gradients  # Update the parameters
        new = compute_log_likelihood(X, y, beta)
        delta = new - likelihood
        likelihood = new

    return beta

# Example usage
np.random.seed(0)
X = np.random.randn(100, 10)  # 100 samples, 10 features
y = np.random.randint(0, 2, 100)  # Binary target variable

# Training the logistic regression model
alpha = 0.01  # Learning rate
beta_final = logistic_regression(X, y, alpha)
print("Final beta coefficients:", beta_final)
```

#
> Definition (**Derivative of a vector-valued function**). Assume that $$S$$ is an open subset of $$\mathbb{R}^n$$. Given a function $$\mathbf{f}: S \rightarrow \mathbb{R}^m$$, we say that $$\mathbf{f}$$ is differentiable at a point $$\mathbf{a} \in S$$ if there exists an $$m \times n $$ matrix $$M$$ such that
>
> $$\mathbf{f(a + h)} = \mathbf{f(a)} + M\mathbf{h} + \mathbf{E(h)}$$, where $$\lim_{\mathbf{h}\rightarrow 0}\frac{\mathbf{E(h)}}{|\mathbf{h}|} = 0 \in \mathbb{R}^m$$.
>
> When this holds, we say that $$M$$ is the derivative of $$\mathbf{f}$$ at $$\mathbf{a}$$, and we write $$M = D\mathbf{f(a)}$$.

> Definition (**Differentiable and Gradient**). Suppose that $$f$$ is a function $$S \rightarrow \mathbb{R}$$, where $$S$$ is an open subset of $$\mathbb{R}^n$$. We say that $$f$$ is differentiable at a point $$\mathbf{x} \in S$$ if there exists a vector $$\mathbf{m} \in \mathbb{R}^n$$ such that
>
> $$\lim_{\mathbf{h} \rightarrow 0} \frac{f(\mathbf{x} + \mathbf{h}) - f(\mathbf{x}) - \mathbf{mh}}{|\mathbf{h}|} = 0$$.
>
> When this holds, we say that the vector $$\mathbf{m}$$ is the gradient of $$f$$ at $$\mathbf{x}$$, and denoted by $$\nabla f(\mathbf{x})$$.

Gradients are defined for real-valued functions only.

> Definition (**Partial derivative**). If $$f$$ is a function defined on an open subset $$S \subset \mathbb{R}^n$$, then at a point $$\mathbf{x} \in S$$, we define
>
> $$\frac{\partial f}{\partial x_j}(\mathbf{x}) = \lim_{h \rightarrow 0}\frac{f(\mathbf{x} + h\mathbf{e}_j) - f(\mathbf{x})}{h}$$,
>
> where $$\mathbf{e}_j$$ is the unit vector in $$\mathbb{R}^n$$ in the $$j\text{th}$$ coordinate direction. This is called the $$j\text{th}$$ partial derivative of $$f$$, the partial derivative of $$f$$ in the $$x_j$$ direction, or the partial derivative of $$f$$ with respect to $$x_j$$.

> Theorem. Let $$f$$ be a function $$S \rightarrow \mathbb{R}$$, where $$S$$ is an open subset of $$\mathbb{R}^n$$. If $$f$$ is differentiable at a point $$\mathbf{x} \in S$$, then $$\frac{\partial f}{\partial x_j}$$ exists at $$\mathbf{x}$$ for all $$j = 1, \dots, n$$, and in addition,
>
> $$\nabla f(\mathbf{x}) = (\frac{\partial f}{\partial x_1}, \dots, \frac{\partial f}{\partial x_n})(\mathbf{x})$$.
