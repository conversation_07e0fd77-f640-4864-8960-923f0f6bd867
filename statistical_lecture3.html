<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistical Concentration Inequalities - Lecture 3 Tutorial</title>
    
    <!-- MathJax Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
            src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            margin: -20px -20px 40px -20px;
            border-radius: 15px 15px 0 0;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .course-info {
            background: #f8f9fa;
            padding: 15px;
            border-left: 5px solid #007bff;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .section h3 {
            color: #e74c3c;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }
        
        .math-block {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
        }
        
        .example-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid #f39c12;
        }
        
        .example-box h4 {
            color: #d68910;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid #27ae60;
        }
        
        .theorem-box h4 {
            color: #27ae60;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .proof-box {
            background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 5px solid #95a5a6;
        }
        
        .proof-box h4 {
            color: #7f8c8d;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .key-insight {
            background: linear-gradient(135deg, #ebf3fd 0%, #c3e9ff 100%);
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .outline {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .outline h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .outline ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .outline li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .outline li:last-child {
            border-bottom: none;
        }
        
        .outline li::before {
            content: "▶ ";
            color: #007bff;
            font-weight: bold;
        }
        
        svg {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
        }
        
        .navigation {
            text-align: center;
            margin: 30px 0;
        }
        
        .nav-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 1em;
            transition: transform 0.2s;
        }
        
        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #28a745);
            width: 20%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Statistical Concentration Inequalities</h1>
            <div class="subtitle">Intermediate Statistics - Lecture 3 Tutorial</div>
        </header>
        
        <div class="course-info">
            <strong>Course:</strong> 36-705: Intermediate Statistics Fall 2019<br>
            <strong>Lecture:</strong> 3 - August 30<br>
            <strong>Lecturer:</strong> Siva Balakrishnan<br>
            <strong>Topic:</strong> Bounded Random Variables & Concentration Inequalities
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="outline">
            <h3>📚 Tutorial Outline</h3>
            <ul>
                <li><strong>Section 1:</strong> Introduction & Course Context</li>
                <li><strong>Section 2:</strong> Bounded Random Variables - Hoeffding's Bound</li>
                <li><strong>Section 3:</strong> Advanced Concentration Inequalities</li>
                <li><strong>Section 4:</strong> Chi-squared Tail Bounds</li>
                <li><strong>Section 5:</strong> Applications - Johnson-Lindenstrauss Lemma</li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction & Course Context -->
        <div class="section" id="section1">
            <h2>1. Introduction & Course Context</h2>
            
            <div class="key-insight">
                <strong>🎯 Learning Objectives:</strong>
                <ul>
                    <li>Understand what concentration inequalities tell us about random variables</li>
                    <li>Learn the connection between bounded random variables and sub-Gaussian behavior</li>
                    <li>Master the fundamental tools for analyzing tail probabilities</li>
                    <li>Apply these concepts to real-world statistical problems</li>
                </ul>
            </div>
            
            <h3>What are Concentration Inequalities?</h3>
            <p>Concentration inequalities are fundamental tools in probability theory and statistics that tell us how close a random variable is to its expected value. They provide <strong>exponential bounds</strong> on the probability that a random variable deviates significantly from its mean.</p>
            
            <div class="key-insight">
                <strong>🔑 Key Insight:</strong> While the Central Limit Theorem tells us about the asymptotic distribution of sums of random variables, concentration inequalities give us precise, non-asymptotic bounds that are useful for finite sample sizes.
            </div>
            
            <h3>Visual Overview: The Concentration Phenomenon</h3>
            <svg width="600" height="400" viewBox="0 0 600 400">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                    <linearGradient id="bellGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#007bff;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#0056b3;stop-opacity:0.9" />
                    </linearGradient>
                </defs>
                <rect width="600" height="400" fill="url(#bgGrad)" rx="10"/>
                
                <!-- Axes -->
                <line x1="50" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="300" y1="50" x2="300" y2="350" stroke="#333" stroke-width="2"/>
                
                <!-- Bell curve (approximation using path) -->
                <path d="M 100 350 Q 150 100 200 150 Q 250 80 300 100 Q 350 80 400 150 Q 450 100 500 350" 
                      fill="url(#bellGrad)" stroke="#0056b3" stroke-width="2" opacity="0.7"/>
                
                <!-- Concentration region -->
                <rect x="220" y="100" width="160" height="250" fill="rgba(40, 167, 69, 0.2)" stroke="#28a745" stroke-width="2" stroke-dasharray="5,5"/>
                
                <!-- Mean line -->
                <line x1="300" y1="50" x2="300" y2="350" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Labels -->
                <text x="300" y="40" text-anchor="middle" fill="#e74c3c" font-size="14" font-weight="bold">μ (mean)</text>
                <text x="140" y="330" text-anchor="middle" fill="#28a745" font-size="12">μ - ε</text>
                <text x="460" y="330" text-anchor="middle" fill="#28a745" font-size="12">μ + ε</text>
                <text x="300" y="380" text-anchor="middle" fill="#333" font-size="14" font-weight="bold">Value</text>
                <text x="25" y="200" text-anchor="middle" fill="#333" font-size="14" font-weight="bold" transform="rotate(-90 25 200)">Probability</text>
                
                <!-- Concentration annotation -->
                <text x="300" y="175" text-anchor="middle" fill="#28a745" font-size="12" font-weight="bold">High Concentration</text>
                <text x="300" y="190" text-anchor="middle" fill="#28a745" font-size="12">P(|X - μ| ≤ ε) is large</text>
                
                <!-- Tail regions -->
                <text x="120" y="280" text-anchor="middle" fill="#dc3545" font-size="10">Tail: Low probability</text>
                <text x="480" y="280" text-anchor="middle" fill="#dc3545" font-size="10">Tail: Low probability</text>
            </svg>
            
            <h3>Why Study Sub-Gaussian Random Variables?</h3>
            <p>In the previous lecture, we claimed that many classes of random variables are <strong>sub-Gaussian</strong>. This lecture focuses on proving this for an important special case: <strong>bounded random variables</strong>.</p>
            
            <div class="theorem-box">
                <h4>📝 Definition: Sub-Gaussian Random Variable</h4>
                <p>A random variable $X$ with mean $\mu = \mathbb{E}[X]$ is called $\sigma$-sub-Gaussian if its moment generating function satisfies:</p>
                <div class="math-block">
                    $$\mathbb{E}[\exp(t(X - \mu))] \leq \exp\left(\frac{\sigma^2 t^2}{2}\right)$$
                </div>
                <p>for all $t \in \mathbb{R}$.</p>
            </div>
            
            <div class="key-insight">
                <strong>🎯 Main Goal:</strong> We will show that if $X$ is bounded on $[a,b]$, then $X$ is $(b-a)$-sub-Gaussian, which immediately gives us Hoeffding's bound and other powerful concentration results.
            </div>
            
            <h3>Applications in Modern Statistics</h3>
            <p>These concentration inequalities are not just theoretical tools—they have practical applications in:</p>
            <ul>
                <li><strong>Machine Learning:</strong> Generalization bounds and sample complexity</li>
                <li><strong>High-dimensional Statistics:</strong> Random matrix theory and compressed sensing</li>
                <li><strong>Data Science:</strong> Confidence intervals and hypothesis testing</li>
                <li><strong>Algorithm Analysis:</strong> Randomized algorithms and probabilistic methods</li>
            </ul>
        </div>
        
        <!-- Section 2: Bounded Random Variables - Hoeffding's Bound -->
        <div class="section" id="section2">
            <h2>2. Bounded Random Variables - Hoeffding's Bound</h2>
            
            <p>We now prove that bounded random variables are sub-Gaussian, starting with the simplest case and building up to the general result.</p>
            
            <div class="example-box">
                <h4>📊 Example 1: Rademacher Random Variables</h4>
                <p>Let's consider the simplest case: <strong>Rademacher random variables</strong> that take values $\{+1, -1\}$ with equal probability $\frac{1}{2}$.</p>
                
                <h5>Step 1: Computing the Moment Generating Function</h5>
                <div class="math-block">
                    $$\mathbb{E}[\exp(tX)] = \frac{1}{2}[\exp(t) + \exp(-t)]$$
                </div>
                
                <h5>Step 2: Taylor Series Expansion</h5>
                <div class="math-block">
                    $$= \frac{1}{2}\left[\sum_{k=0}^{\infty} \frac{(-t)^k}{k!} + \sum_{k=0}^{\infty} \frac{t^k}{k!}\right]$$
                </div>
                
                <h5>Step 3: Combining Series (Only Even Terms Survive)</h5>
                <div class="math-block">
                    $$= \sum_{k=0}^{\infty} \frac{t^{2k}}{(2k)!} \leq \sum_{k=0}^{\infty} \frac{t^{2k}}{2^k k!} = \exp\left(\frac{t^2}{2}\right)$$
                </div>
                
                <div class="key-insight">
                    <strong>🎯 Conclusion:</strong> Rademacher random variables are <strong>1-sub-Gaussian</strong>!
                </div>
            </div>
            
            <div class="theorem-box">
                <h4>📋 Detour: Jensen's Inequality</h4>
                <p>Before proceeding to general bounded random variables, we need a key tool:</p>
                
                <p><strong>Jensen's Inequality:</strong> For a convex function $g : \mathbb{R} \to \mathbb{R}$:</p>
                <div class="math-block">
                    $$\mathbb{E}[g(X)] \geq g(\mathbb{E}[X])$$
                </div>
                <p>If $g$ is concave, the inequality reverses.</p>
            </div>
            
            <div class="proof-box">
                <h4>🔍 Proof of Jensen's Inequality</h4>
                <p>Let $\mu = \mathbb{E}[X]$ and let $L_\mu(x) = a + bx$ be the tangent line to $g$ at $\mu$.</p>
                <p>By convexity: $g(x) \geq L_\mu(x)$ for every point $x$.</p>
                <div class="math-block">
                    $$\mathbb{E}[g(X)] \geq \mathbb{E}[L_\mu(X)] = \mathbb{E}[a + bX] = a + b\mu = L_\mu(\mu) = g(\mu)$$
                </div>
            </div>
            
            <h3>Visual: Jensen's Inequality</h3>
            <svg width="500" height="350" viewBox="0 0 500 350">
                <defs>
                    <linearGradient id="convexGrad" x1="0%" y1="100%" x2="0%" y2="0%">
                        <stop offset="0%" style="stop-color:#3498db;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#2980b9;stop-opacity:0.8" />
                    </linearGradient>
                </defs>
                
                <!-- Background -->
                <rect width="500" height="350" fill="#f8f9fa" rx="5"/>
                
                <!-- Axes -->
                <line x1="50" y1="300" x2="450" y2="300" stroke="#333" stroke-width="2"/>
                <line x1="50" y1="50" x2="50" y2="300" stroke="#333" stroke-width="2"/>
                
                <!-- Convex function -->
                <path d="M 80 280 Q 150 120 220 140 Q 290 100 360 160 Q 420 200 450 240" 
                      fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- Tangent line -->
                <line x1="100" y1="260" x2="400" y2="180" stroke="#27ae60" stroke-width="2" stroke-dasharray="5,5"/>
                
                <!-- Expected value point -->
                <circle cx="250" cy="300" r="4" fill="#2c3e50"/>
                <line x1="250" y1="50" x2="250" y2="300" stroke="#2c3e50" stroke-width="1" stroke-dasharray="3,3"/>
                
                <!-- Function value at E[X] -->
                <circle cx="250" cy="125" r="4" fill="#e74c3c"/>
                
                <!-- Sample points -->
                <circle cx="150" cy="300" r="3" fill="#f39c12"/>
                <circle cx="350" cy="300" r="3" fill="#f39c12"/>
                <line x1="150" y1="50" x2="150" y2="300" stroke="#f39c12" stroke-width="1" stroke-dasharray="2,2"/>
                <line x1="350" y1="50" x2="350" y2="300" stroke="#f39c12" stroke-width="1" stroke-dasharray="2,2"/>
                
                <!-- Function values at sample points -->
                <circle cx="150" cy="180" r="3" fill="#e74c3c"/>
                <circle cx="350" cy="185" r="3" fill="#e74c3c"/>
                
                <!-- Expected function value -->
                <line x1="50" y1="182" x2="450" y2="182" stroke="#9b59b6" stroke-width="2" stroke-dasharray="8,4"/>
                
                <!-- Labels -->
                <text x="250" y="320" text-anchor="middle" fill="#2c3e50" font-size="12">E[X]</text>
                <text x="150" y="320" text-anchor="middle" fill="#f39c12" font-size="10">x₁</text>
                <text x="350" y="320" text-anchor="middle" fill="#f39c12" font-size="10">x₂</text>
                <text x="460" y="125" text-anchor="start" fill="#e74c3c" font-size="12">g(E[X])</text>
                <text x="460" y="182" text-anchor="start" fill="#9b59b6" font-size="12">E[g(X)]</text>
                <text x="250" y="30" text-anchor="middle" fill="#333" font-size="14" font-weight="bold">Jensen's Inequality: E[g(X)] ≥ g(E[X])</text>
            </svg>
            
            <div class="example-box">
                <h4>📊 Example 2: General Bounded Random Variables</h4>
                <p>Let $X$ be a random variable with zero mean and support on $[a, b]$.</p>
                
                <div class="key-insight">
                    <strong>💡 Note:</strong> The zero mean assumption is without loss of generality—we can always subtract the mean to create $Y = X - \mathbb{E}[X]$.
                </div>
                
                <h5>Step 1: Use Independent Copy Technique</h5>
                <p>Let $X'$ be an independent copy of $X$. Then:</p>
                <div class="math-block">
                    $$\mathbb{E}_X[\exp(tX)] = \mathbb{E}_X[\exp(t(X - \mathbb{E}[X']))] \leq \mathbb{E}_{X,X'}[\exp(t(X - X'))]$$
                </div>
                <p>using Jensen's inequality and convexity of $\exp(\cdot)$.</p>
                
                <h5>Step 2: Symmetrization with Rademacher Variables</h5>
                <p>Let $\epsilon$ be a Rademacher random variable. The distribution of $X - X'$ is identical to $\epsilon(X - X')$:</p>
                <div class="math-block">
                    $$\mathbb{E}_{X,X'}[\exp(t(X - X'))] = \mathbb{E}_{X,X'}[\mathbb{E}_\epsilon[\exp(t\epsilon(X - X'))]]$$
                </div>
                
                <h5>Step 3: Apply Rademacher Result</h5>
                <div class="math-block">
                    $$\leq \mathbb{E}_{X,X'}[\exp(t^2(X - X')^2/2)]$$
                </div>
                
                <h5>Step 4: Use Boundedness</h5>
                <p>Since $(X - X')$ is bounded by $(b - a)$:</p>
                <div class="math-block">
                    $$\mathbb{E}[exp(tX)] \leq \exp\left(\frac{t^2(b-a)^2}{2}\right)$$
                </div>
                
                <div class="key-insight">
                    <strong>🎯 Conclusion:</strong> Bounded random variables are <strong>$(b-a)$-sub-Gaussian</strong>!
                </div>
            </div>
            
            <div class="theorem-box">
                <h4>🎯 Hoeffding's Bound</h4>
                <p>If $X_1, \ldots, X_n$ are independent, identically distributed bounded random variables with $a \leq X_i \leq b$:</p>
                <div class="math-block">
                    $$P\left(\left|\frac{1}{n}\sum_{i=1}^n X_i - \mu\right| \geq t\right) \leq 2\exp\left(\frac{-nt^2}{2(b-a)^2}\right)$$
                </div>
                <p>An even tighter version exists:</p>
                <div class="math-block">
                    $$P\left(\left|\frac{1}{n}\sum_{i=1}^n X_i - \mu\right| \geq t\right) \leq 2\exp\left(\frac{-2nt^2}{(b-a)^2}\right)$$
                </div>
            </div>
            
            <h3>Visual: Hoeffding's Bound in Action</h3>
            <svg width="600" height="400" viewBox="0 0 600 400">
                <defs>
                    <linearGradient id="hoeffGrad" x1="0%" y1="100%" x2="0%" y2="0%">
                        <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Background -->
                <rect width="600" height="400" fill="#f8f9fa" rx="5"/>
                
                <!-- Axes -->
                <line x1="60" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="60" y1="50" x2="60" y2="350" stroke="#333" stroke-width="2"/>
                
                <!-- Exponential decay curve -->
                <path d="M 100 300 Q 200 200 300 150 Q 400 100 500 80" 
                      fill="none" stroke="#e74c3c" stroke-width="4"/>
                
                <!-- Sample size effect -->
                <path d="M 100 280 Q 200 160 300 110 Q 400 70 500 50" 
                      fill="none" stroke="#27ae60" stroke-width="3" stroke-dasharray="5,5"/>
                
                <!-- Bound visualization -->
                <rect x="80" y="60" width="460" height="280" fill="url(#hoeffGrad)" opacity="0.1"/>
                
                <!-- Labels -->
                <text x="300" y="30" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Hoeffding's Bound: Exponential Decay</text>
                <text x="570" y="350" text-anchor="middle" fill="#333" font-size="12">t (deviation)</text>
                <text x="30" y="200" text-anchor="middle" fill="#333" font-size="12" transform="rotate(-90 30 200)">P(|S̄ₙ - μ| ≥ t)</text>
                
                <!-- Bound curves -->
                <text x="520" y="90" text-anchor="start" fill="#e74c3c" font-size="11">Small n</text>
                <text x="520" y="60" text-anchor="start" fill="#27ae60" font-size="11">Large n</text>
                
                <!-- Key points -->
                <circle cx="200" cy="200" r="4" fill="#f39c12"/>
                <circle cx="300" cy="150" r="4" fill="#f39c12"/>
                <circle cx="400" cy="100" r="4" fill="#f39c12"/>
                
                <!-- Annotations -->
                <text x="200" y="220" text-anchor="middle" fill="#f39c12" font-size="10">2exp(-2nt²/(b-a)²)</text>
            </svg>
            
            <div class="theorem-box">
                <h4>🔧 Generalization: Non-Identical Distributions</h4>
                <p>The power of concentration inequalities is that they don't require identical distributions!</p>
                
                <p>If $X_1, \ldots, X_n$ are independent with each $X_i$ being $\sigma_i$-sub-Gaussian, then their average is $\sigma$-sub-Gaussian where:</p>
                <div class="math-block">
                    $$\sigma = \frac{1}{n}\sqrt{\sum_{i=1}^n \sigma_i^2}$$
                </div>
                
                <p>This gives the concentration inequality:</p>
                <div class="math-block">
                    $$P\left(\left|\frac{1}{n}\sum_{i=1}^n (X_i - \mathbb{E}[X_i])\right| \geq t\right) \leq \exp\left(\frac{-t^2}{2\sigma^2}\right)$$
                                 </div>
             </div>
         </div>
        
        <!-- Section 3: Advanced Concentration Inequalities -->
        <div class="section" id="section3">
            <h2>3. Advanced Concentration Inequalities</h2>
            
            <p>Beyond Hoeffding's bound, there are several other important concentration inequalities that apply in different settings and provide sharper bounds under additional assumptions.</p>
            
            <div class="theorem-box">
                <h4>📈 Bernstein's Inequality</h4>
                <p>While Hoeffding's bound depends only on the range $[a,b]$, Bernstein's inequality also incorporates the <strong>variance</strong> of the random variables, potentially giving much sharper bounds.</p>
                
                <p>If $X_1, \ldots, X_n$ are i.i.d. with mean $\mu$, bounded support $[a,b]$, and variance $\sigma^2$:</p>
                <div class="math-block">
                    $$P(|\hat{\mu} - \mu| \geq t) \leq 2\exp\left(\frac{-nt^2}{2(\sigma^2 + (b-a)t)}\right)$$
                </div>
                
                <div class="key-insight">
                    <strong>🎯 Key Insight:</strong> When $\sigma^2 \ll (b-a)^2$, Bernstein's bound can be much sharper than Hoeffding's bound!
                </div>
                
                <p>This roughly translates to:</p>
                <div class="math-block">
                    $$|\hat{\mu} - \mu| \leq 4\sigma\sqrt{\frac{\ln(2/\delta)}{n}} + \frac{4(b-a)\ln(2/\delta)}{n}$$
                </div>
                <p>with probability at least $1-\delta$.</p>
            </div>
            
            <h3>Visual: Bernstein vs Hoeffding Comparison</h3>
            <svg width="600" height="400" viewBox="0 0 600 400">
                <defs>
                    <linearGradient id="bernGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#27ae60;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Background -->
                <rect width="600" height="400" fill="#f8f9fa" rx="5"/>
                
                <!-- Axes -->
                <line x1="60" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="60" y1="50" x2="60" y2="350" stroke="#333" stroke-width="2"/>
                
                <!-- Hoeffding bound (looser) -->
                <path d="M 100 320 Q 200 250 300 200 Q 400 160 500 130" 
                      fill="none" stroke="#e74c3c" stroke-width="3" stroke-dasharray="8,4"/>
                
                <!-- Bernstein bound (tighter for small variance) -->
                <path d="M 100 300 Q 200 180 300 120 Q 400 90 500 70" 
                      fill="none" stroke="#27ae60" stroke-width="4"/>
                
                <!-- Crossover point -->
                <circle cx="350" cy="140" r="5" fill="#f39c12"/>
                <line x1="350" y1="50" x2="350" y2="350" stroke="#f39c12" stroke-width="1" stroke-dasharray="3,3"/>
                
                <!-- Labels -->
                <text x="300" y="30" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Bernstein vs Hoeffding: The Variance Effect</text>
                <text x="570" y="350" text-anchor="middle" fill="#333" font-size="12">t (deviation)</text>
                <text x="30" y="200" text-anchor="middle" fill="#333" font-size="12" transform="rotate(-90 30 200)">Bound Value</text>
                
                <!-- Legend -->
                <line x1="450" y1="80" x2="480" y2="80" stroke="#e74c3c" stroke-width="3" stroke-dasharray="8,4"/>
                <text x="485" y="85" text-anchor="start" fill="#e74c3c" font-size="12">Hoeffding</text>
                
                <line x1="450" y1="100" x2="480" y2="100" stroke="#27ae60" stroke-width="4"/>
                <text x="485" y="105" text-anchor="start" fill="#27ae60" font-size="12">Bernstein (small σ²)</text>
                
                <!-- Annotation -->
                <text x="360" y="130" text-anchor="start" fill="#f39c12" font-size="11">Bernstein better</text>
                <text x="360" y="145" text-anchor="start" fill="#f39c12" font-size="11">when σ² ≪ (b-a)²</text>
            </svg>
            
            <div class="theorem-box">
                <h4>🔧 McDiarmid's Inequality</h4>
                <p>McDiarmid's inequality extends concentration to general functions of independent random variables, not just averages.</p>
                
                <p><strong>Bounded Difference Condition:</strong> A function $f: \mathbb{R}^n \to \mathbb{R}$ satisfies this if:</p>
                <div class="math-block">
                    $$|f(x_1, \ldots, x_n) - f(x_1, \ldots, x_{k-1}, x'_k, x_{k+1}, \ldots, x_n)| \leq L_k$$
                </div>
                <p>for all $x, x'$ and each coordinate $k$.</p>
                
                <p><strong>McDiarmid's Bound:</strong> If $X_1, \ldots, X_n$ are independent:</p>
                <div class="math-block">
                    $$P(|f(X_1, \ldots, X_n) - \mathbb{E}[f(X_1, \ldots, X_n)]| \geq t) \leq 2\exp\left(\frac{-2t^2}{\sum_{k=1}^n L_k^2}\right)$$
                </div>
            </div>
            
            <div class="example-box">
                <h4>📊 Example: U-Statistics</h4>
                <p>A U-statistic with kernel $g: \mathbb{R}^2 \to \mathbb{R}$ is:</p>
                <div class="math-block">
                    $$U(X_1, \ldots, X_n) = \frac{1}{\binom{n}{2}} \sum_{j < k} g(X_j, X_k)$$
                </div>
                
                <p><strong>Examples of U-statistics:</strong></p>
                <ul>
                    <li><strong>Sample Variance:</strong> $g(X_j, X_k) = \frac{1}{2}(X_j - X_k)^2$</li>
                    <li><strong>Mean Absolute Deviation:</strong> $g(X_j, X_k) = |X_j - X_k|$</li>
                </ul>
                
                <p>For bounded $g$ with $|g(X_j, X_k)| \leq b$, each $X_i$ affects $(n-1)$ terms, so:</p>
                <div class="math-block">
                    $$L_i = \frac{(n-1) \cdot 2b}{\binom{n}{2}} = \frac{4b}{n}$$
                </div>
                
                <p>McDiarmid's inequality gives:</p>
                <div class="math-block">
                    $$P(|U - \mathbb{E}[U]| \geq t) \leq 2\exp\left(\frac{-nt^2}{8b^2}\right)$$
                </div>
            </div>
            
            <div class="theorem-box">
                <h4>🎯 Lévy's Inequality</h4>
                <p>For functions of <strong>Gaussian random variables</strong> that are Lipschitz continuous.</p>
                
                <p><strong>Lipschitz Condition:</strong> Function $f$ satisfies:</p>
                <div class="math-block">
                    $$|f(X_1, \ldots, X_n) - f(Y_1, \ldots, Y_n)| \leq L\sqrt{\sum_{i=1}^n (X_i - Y_i)^2}$$
                </div>
                
                <p><strong>Lévy's Bound:</strong> If $X_1, \ldots, X_n \sim N(0,1)$:</p>
                <div class="math-block">
                    $$P(|f(X_1, \ldots, X_n) - \mathbb{E}[f(X_1, \ldots, X_n)]| \geq t) \leq 2\exp\left(\frac{-t^2}{2L^2}\right)$$
                </div>
                
                <div class="key-insight">
                    <strong>🎯 Key Difference:</strong> Lévy's inequality uses the <strong>Euclidean norm</strong> rather than coordinate-wise bounded differences, making it natural for Gaussian settings.
                </div>
            </div>
            
            <h3>Comparison of Concentration Inequalities</h3>
            <svg width="700" height="500" viewBox="0 0 700 500">
                <!-- Background -->
                <rect width="700" height="500" fill="#f8f9fa" rx="5"/>
                
                <!-- Title -->
                <text x="350" y="30" text-anchor="middle" fill="#333" font-size="18" font-weight="bold">Concentration Inequalities: When to Use Which?</text>
                
                <!-- Hoeffding Box -->
                <rect x="50" y="60" width="180" height="120" fill="#ffe6e6" stroke="#e74c3c" stroke-width="2" rx="5"/>
                <text x="140" y="80" text-anchor="middle" fill="#c0392b" font-size="14" font-weight="bold">Hoeffding's Bound</text>
                <text x="60" y="100" fill="#333" font-size="11">• Bounded random variables</text>
                <text x="60" y="115" fill="#333" font-size="11">• Only needs range [a,b]</text>
                <text x="60" y="130" fill="#333" font-size="11">• General, but can be loose</text>
                <text x="60" y="145" fill="#333" font-size="11">• Rate: exp(-nt²/(b-a)²)</text>
                <text x="60" y="160" fill="#333" font-size="11">• Most widely applicable</text>
                
                <!-- Bernstein Box -->
                <rect x="260" y="60" width="180" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="5"/>
                <text x="350" y="80" text-anchor="middle" fill="#27ae60" font-size="14" font-weight="bold">Bernstein's Bound</text>
                <text x="270" y="100" fill="#333" font-size="11">• Bounded + small variance</text>
                <text x="270" y="115" fill="#333" font-size="11">• Uses variance σ²</text>
                <text x="270" y="130" fill="#333" font-size="11">• Sharper when σ² ≪ (b-a)²</text>
                <text x="270" y="145" fill="#333" font-size="11">• Rate: exp(-nt²/(σ²+(b-a)t))</text>
                <text x="270" y="160" fill="#333" font-size="11">• Best for low-variance case</text>
                
                <!-- McDiarmid Box -->
                <rect x="470" y="60" width="180" height="120" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="5"/>
                <text x="560" y="80" text-anchor="middle" fill="#d68910" font-size="14" font-weight="bold">McDiarmid's Bound</text>
                <text x="480" y="100" fill="#333" font-size="11">• General functions f(X₁,...,Xₙ)</text>
                <text x="480" y="115" fill="#333" font-size="11">• Bounded differences</text>
                <text x="480" y="130" fill="#333" font-size="11">• Beyond just averages</text>
                <text x="480" y="145" fill="#333" font-size="11">• Rate: exp(-2t²/ΣLₖ²)</text>
                <text x="480" y="160" fill="#333" font-size="11">• U-statistics, etc.</text>
                
                <!-- Levy Box -->
                <rect x="155" y="220" width="180" height="120" fill="#ebf3fd" stroke="#007bff" stroke-width="2" rx="5"/>
                <text x="245" y="240" text-anchor="middle" fill="#0056b3" font-size="14" font-weight="bold">Lévy's Bound</text>
                <text x="165" y="260" fill="#333" font-size="11">• Gaussian random variables</text>
                <text x="165" y="275" fill="#333" font-size="11">• Lipschitz functions</text>
                <text x="165" y="290" fill="#333" font-size="11">• Euclidean geometry</text>
                <text x="165" y="305" fill="#333" font-size="11">• Rate: exp(-t²/(2L²))</text>
                <text x="165" y="320" fill="#333" font-size="11">• Natural for Gaussian case</text>
                
                <!-- Chi-squared Box -->
                <rect x="365" y="220" width="180" height="120" fill="#f0f0f0" stroke="#6c757d" stroke-width="2" rx="5"/>
                <text x="455" y="240" text-anchor="middle" fill="#495057" font-size="14" font-weight="bold">χ² Tail Bounds</text>
                <text x="375" y="260" fill="#333" font-size="11">• Sum of squared Gaussians</text>
                <text x="375" y="275" fill="#333" font-size="11">• Sub-exponential behavior</text>
                <text x="375" y="290" fill="#333" font-size="11">• Random matrix theory</text>
                <text x="375" y="305" fill="#333" font-size="11">• Rate: exp(-nt²/8) for t < 1</text>
                <text x="375" y="320" fill="#333" font-size="11">• Johnson-Lindenstrauss</text>
                
                <!-- Arrows showing relationships -->
                <path d="M 140 180 Q 200 200 245 220" fill="none" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                <path d="M 350 180 Q 400 200 455 220" fill="none" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                    </marker>
                </defs>
                
                <!-- Decision tree -->
                <text x="350" y="380" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Decision Guide:</text>
                <text x="50" y="410" fill="#333" font-size="12">1. Are variables bounded? → Hoeffding or Bernstein</text>
                <text x="50" y="430" fill="#333" font-size="12">2. Is variance small? → Bernstein preferred</text>
                <text x="50" y="450" fill="#333" font-size="12">3. General function? → McDiarmid</text>
                                 <text x="50" y="470" fill="#333" font-size="12">4. Gaussian variables? → Lévy or χ² bounds</text>
             </svg>
         </div>
        
        <!-- Section 4: Chi-squared Tail Bounds -->
        <div class="section" id="section4">
            <h2>4. Chi-squared Tail Bounds</h2>
            
            <div class="theorem-box">
                <h4>📝 Definition: Chi-squared Random Variable</h4>
                <p>A chi-squared random variable with $n$ degrees of freedom, denoted $Y \sim \chi^2_n$, is:</p>
                <div class="math-block">
                    $$Y = \sum_{i=1}^n X_i^2$$
                </div>
                <p>where each $X_i \sim N(0,1)$ independently.</p>
                
                <p><strong>Key Properties:</strong></p>
                <ul>
                    <li>$\mathbb{E}[Y] = n$ (expected value)</li>
                    <li>$\text{Var}(Y) = 2n$ (variance)</li>
                    <li>$\mathbb{E}[X_i^2] = 1$ for each standard normal $X_i$</li>
                </ul>
            </div>
            
            <div class="theorem-box">
                <h4>🎯 Chi-squared Tail Bound</h4>
                <p>For $Z_1, \ldots, Z_n \sim N(0,1)$ independently:</p>
                <div class="math-block">
                    $$P\left(\left|\frac{1}{n}\sum_{k=1}^n Z_k^2 - 1\right| \geq t\right) \leq 2\exp\left(\frac{-nt^2}{8}\right)$$
                </div>
                <p>for all $t \in (0,1)$.</p>
                
                <div class="key-insight">
                    <strong>⚠️ Important Limitation:</strong> This bound only holds for <strong>small deviations</strong> $t < 1$. Chi-squared variables are <strong>sub-exponential</strong>, not sub-Gaussian!
                </div>
            </div>
            
            <h3>Visual: Chi-squared Distribution and Tail Behavior</h3>
            <svg width="600" height="400" viewBox="0 0 600 400">
                <defs>
                    <linearGradient id="chiGrad" x1="0%" y1="100%" x2="0%" y2="0%">
                        <stop offset="0%" style="stop-color:#6c757d;stop-opacity:0.8" />
                        <stop offset="100%" style="stop-color:#495057;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- Background -->
                <rect width="600" height="400" fill="#f8f9fa" rx="5"/>
                
                <!-- Axes -->
                <line x1="60" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="60" y1="50" x2="60" y2="350" stroke="#333" stroke-width="2"/>
                
                <!-- Chi-squared curves for different degrees of freedom -->
                <!-- df = 1 -->
                <path d="M 80 100 Q 120 200 160 260 Q 200 300 300 330 Q 400 340 500 345" 
                      fill="none" stroke="#e74c3c" stroke-width="3"/>
                
                <!-- df = 3 -->
                <path d="M 80 350 Q 120 250 160 200 Q 200 180 300 220 Q 400 280 500 320" 
                      fill="none" stroke="#27ae60" stroke-width="3"/>
                
                <!-- df = 5 -->
                <path d="M 80 350 Q 120 300 160 240 Q 200 200 300 180 Q 400 200 500 250" 
                      fill="none" stroke="#007bff" stroke-width="3"/>
                
                <!-- Mean lines -->
                <line x1="80" y1="60" x2="80" y2="350" stroke="#e74c3c" stroke-width="1" stroke-dasharray="3,3"/>
                <line x1="140" y1="60" x2="140" y2="350" stroke="#27ae60" stroke-width="1" stroke-dasharray="3,3"/>
                <line x1="180" y1="60" x2="180" y2="350" stroke="#007bff" stroke-width="1" stroke-dasharray="3,3"/>
                
                <!-- Labels -->
                <text x="300" y="30" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Chi-squared Distributions</text>
                <text x="570" y="350" text-anchor="middle" fill="#333" font-size="12">x</text>
                <text x="30" y="200" text-anchor="middle" fill="#333" font-size="12" transform="rotate(-90 30 200)">Density</text>
                
                <!-- Legend -->
                <line x1="420" y1="80" x2="450" y2="80" stroke="#e74c3c" stroke-width="3"/>
                <text x="455" y="85" text-anchor="start" fill="#e74c3c" font-size="12">χ²₁ (df=1)</text>
                
                <line x1="420" y1="100" x2="450" y2="100" stroke="#27ae60" stroke-width="3"/>
                <text x="455" y="105" text-anchor="start" fill="#27ae60" font-size="12">χ²₃ (df=3)</text>
                
                <line x1="420" y1="120" x2="450" y2="120" stroke="#007bff" stroke-width="3"/>
                <text x="455" y="125" text-anchor="start" fill="#007bff" font-size="12">χ²₅ (df=5)</text>
                
                <!-- Mean annotations -->
                <text x="85" y="370" text-anchor="middle" fill="#e74c3c" font-size="10">μ=1</text>
                <text x="145" y="370" text-anchor="middle" fill="#27ae60" font-size="10">μ=3</text>
                <text x="185" y="370" text-anchor="middle" fill="#007bff" font-size="10">μ=5</text>
            </svg>
            
            <div class="theorem-box">
                <h4>🔗 The Union Bound (Boole's Inequality)</h4>
                <p>A fundamental tool for handling multiple events simultaneously:</p>
                <div class="math-block">
                    $$P\left(\bigcup_{i=1}^n A_i\right) \leq \sum_{i=1}^n P(A_i)$$
                </div>
                
                <p><strong>Interpretation:</strong> The probability that <em>at least one</em> failure occurs is at most the sum of individual failure probabilities.</p>
                
                <div class="key-insight">
                    <strong>🎯 Key Application:</strong> This allows us to control the probability of any one of many events failing, which is crucial for the Johnson-Lindenstrauss lemma.
                </div>
            </div>
        </div>
        
        <!-- Section 5: Applications - Johnson-Lindenstrauss Lemma -->
        <div class="section" id="section5">
            <h2>5. Applications: The Johnson-Lindenstrauss Lemma</h2>
            
            <p>One of the most elegant applications of chi-squared tail bounds is proving the Johnson-Lindenstrauss lemma, a fundamental result in high-dimensional geometry and data science.</p>
            
            <div class="theorem-box">
                <h4>🎯 The Johnson-Lindenstrauss Lemma</h4>
                <p><strong>Problem:</strong> We have a dataset $X_1, \ldots, X_n \in \mathbb{R}^d$ where $d$ is very large. We want to reduce storage costs by projecting to $\mathbb{R}^m$ with $m \ll d$ while preserving distances.</p>
                
                <p><strong>Goal:</strong> Find a map $F: \mathbb{R}^d \to \mathbb{R}^m$ such that for every pair $(X_i, X_j)$:</p>
                <div class="math-block">
                    $$(1-\epsilon)\|X_i - X_j\|_2^2 \leq \|F(X_i) - F(X_j)\|_2^2 \leq (1+\epsilon)\|X_i - X_j\|_2^2$$
                </div>
                
                <p><strong>Amazing Result:</strong> A simple random construction works with high probability if:</p>
                <div class="math-block">
                    $$m \geq \frac{16\log(n/\delta)}{\epsilon^2}$$
                </div>
                
                <div class="key-insight">
                    <strong>🤯 Stunning Facts:</strong>
                    <ul>
                        <li>The required dimension $m$ is <strong>independent</strong> of the original dimension $d$!</li>
                        <li>It depends only <strong>logarithmically</strong> on the number of points $n$</li>
                        <li>Can achieve massive storage savings while preserving geometry</li>
                    </ul>
                </div>
            </div>
            
            <div class="example-box">
                <h4>🔧 The Construction</h4>
                <p><strong>Step 1:</strong> Create a random matrix $Z \in \mathbb{R}^{m \times d}$ where each entry $Z_{ij} \sim N(0,1)$ independently.</p>
                
                <p><strong>Step 2:</strong> Define the map:</p>
                <div class="math-block">
                    $$F(X_i) = \frac{ZX_i}{\sqrt{m}}$$
                </div>
                
                <p><strong>That's it!</strong> This incredibly simple construction preserves all pairwise distances with high probability.</p>
            </div>
            
            <h3>Visual: Johnson-Lindenstrauss in Action</h3>
            <svg width="700" height="450" viewBox="0 0 700 450">
                <!-- Background -->
                <rect width="700" height="450" fill="#f8f9fa" rx="5"/>
                
                <!-- Title -->
                <text x="350" y="25" text-anchor="middle" fill="#333" font-size="18" font-weight="bold">Johnson-Lindenstrauss: Dimension Reduction Magic</text>
                
                <!-- High-dimensional space -->
                <rect x="50" y="60" width="250" height="200" fill="#e8f5e8" stroke="#27ae60" stroke-width="3" rx="10"/>
                <text x="175" y="85" text-anchor="middle" fill="#27ae60" font-size="16" font-weight="bold">High-Dimensional Space ℝᵈ</text>
                <text x="175" y="105" text-anchor="middle" fill="#333" font-size="12">d = 10,000 (very large!)</text>
                
                <!-- Points in high-dimensional space -->
                <circle cx="120" cy="140" r="4" fill="#e74c3c"/>
                <circle cx="180" cy="160" r="4" fill="#e74c3c"/>
                <circle cx="150" cy="190" r="4" fill="#e74c3c"/>
                <circle cx="200" cy="130" r="4" fill="#e74c3c"/>
                <circle cx="110" cy="180" r="4" fill="#e74c3c"/>
                
                <!-- Distance lines -->
                <line x1="120" y1="140" x2="180" y2="160" stroke="#666" stroke-width="1" stroke-dasharray="2,2"/>
                <line x1="180" y1="160" x2="150" y2="190" stroke="#666" stroke-width="1" stroke-dasharray="2,2"/>
                <line x1="150" y1="190" x2="200" y2="130" stroke="#666" stroke-width="1" stroke-dasharray="2,2"/>
                
                <text x="175" y="220" text-anchor="middle" fill="#333" font-size="11">n data points</text>
                <text x="175" y="235" text-anchor="middle" fill="#333" font-size="11">O(n²) pairwise distances</text>
                
                <!-- Random projection arrow -->
                <path d="M 300 160 Q 350 140 400 160" fill="none" stroke="#007bff" stroke-width="4" marker-end="url(#arrow)"/>
                <text x="350" y="135" text-anchor="middle" fill="#007bff" font-size="14" font-weight="bold">Random</text>
                <text x="350" y="150" text-anchor="middle" fill="#007bff" font-size="14" font-weight="bold">Projection</text>
                <text x="350" y="180" text-anchor="middle" fill="#007bff" font-size="12">F(x) = Zx/√m</text>
                
                <!-- Low-dimensional space -->
                <rect x="400" y="60" width="250" height="200" fill="#ebf3fd" stroke="#007bff" stroke-width="3" rx="10"/>
                <text x="525" y="85" text-anchor="middle" fill="#007bff" font-size="16" font-weight="bold">Low-Dimensional Space ℝᵐ</text>
                <text x="525" y="105" text-anchor="middle" fill="#333" font-size="12">m = 16 log(n)/ε² (much smaller!)</text>
                
                <!-- Projected points -->
                <circle cx="470" cy="140" r="4" fill="#e74c3c"/>
                <circle cx="530" cy="160" r="4" fill="#e74c3c"/>
                <circle cx="500" cy="190" r="4" fill="#e74c3c"/>
                <circle cx="550" cy="130" r="4" fill="#e74c3c"/>
                <circle cx="460" cy="180" r="4" fill="#e74c3c"/>
                
                <!-- Preserved distance lines -->
                <line x1="470" y1="140" x2="530" y2="160" stroke="#27ae60" stroke-width="2"/>
                <line x1="530" y1="160" x2="500" y2="190" stroke="#27ae60" stroke-width="2"/>
                <line x1="500" y1="190" x2="550" y2="130" stroke="#27ae60" stroke-width="2"/>
                
                <text x="525" y="220" text-anchor="middle" fill="#333" font-size="11">Distances preserved:</text>
                <text x="525" y="235" text-anchor="middle" fill="#27ae60" font-size="11">(1-ε)||x-y||² ≤ ||F(x)-F(y)||² ≤ (1+ε)||x-y||²</text>
                
                <!-- Arrow marker -->
                <defs>
                    <marker id="arrow" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#007bff"/>
                    </marker>
                </defs>
                
                <!-- Key insight boxes -->
                <rect x="50" y="290" width="300" height="80" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="5"/>
                <text x="200" y="310" text-anchor="middle" fill="#d68910" font-size="14" font-weight="bold">Storage Savings</text>
                <text x="60" y="330" fill="#333" font-size="12">Original: O(nd) storage</text>
                <text x="60" y="345" fill="#333" font-size="12">Projected: O(nm) storage</text>
                <text x="60" y="360" fill="#333" font-size="12">Savings factor: d/m = d·ε²/(16log(n))</text>
                
                <rect x="370" y="290" width="280" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="5"/>
                <text x="510" y="310" text-anchor="middle" fill="#27ae60" font-size="14" font-weight="bold">Dimension Independence</text>
                <text x="380" y="330" fill="#333" font-size="12">Required dimension m independent of d!</text>
                <text x="380" y="345" fill="#333" font-size="12">Works for d = 10⁶, 10⁹, or even higher</text>
                <text x="380" y="360" fill="#333" font-size="12">Only depends logarithmically on n</text>
                
                <!-- Applications -->
                <text x="350" y="400" text-anchor="middle" fill="#333" font-size="16" font-weight="bold">Applications in Data Science</text>
                <text x="100" y="425" fill="#333" font-size="12">• Machine Learning: Dimensionality reduction before clustering/classification</text>
                <text x="100" y="440" fill="#333" font-size="12">• Computer Vision: Efficient nearest neighbor search in high-dimensional feature spaces</text>
            </svg>
            
            <div class="proof-box">
                <h4>🔍 Proof Sketch</h4>
                <p><strong>Step 1:</strong> Fix a pair $(X_j, X_k)$ and analyze the ratio:</p>
                <div class="math-block">
                    $$\frac{\|F(X_j) - F(X_k)\|_2^2}{\|X_j - X_k\|_2^2} = \frac{1}{m}\sum_{i=1}^m T_i$$
                </div>
                <p>where $T_i = \langle Z_i, \frac{X_j - X_k}{\|X_j - X_k\|_2}\rangle^2$ and $Z_i$ is the $i$-th row of $Z$.</p>
                
                <p><strong>Step 2:</strong> Each $T_i$ is an independent $\chi^2_1$ random variable (since $\langle Z_i, u\rangle \sim N(0,1)$ for unit vector $u$).</p>
                
                <p><strong>Step 3:</strong> Apply the chi-squared tail bound:</p>
                <div class="math-block">
                    $$P\left(\left|\frac{1}{m}\sum_{i=1}^m T_i - 1\right| \geq \epsilon\right) \leq 2\exp\left(\frac{-m\epsilon^2}{8}\right)$$
                </div>
                
                <p><strong>Step 4:</strong> Use the union bound over all $\binom{n}{2}$ pairs:</p>
                <div class="math-block">
                    $$P(\text{any pair fails}) \leq 2\binom{n}{2}\exp\left(\frac{-m\epsilon^2}{8}\right)$$
                </div>
                
                <p><strong>Step 5:</strong> Set $m \geq \frac{16\log(n/\delta)}{\epsilon^2}$ to make this probability $\leq \delta$.</p>
                
                <div class="key-insight">
                    <strong>🎯 The Magic:</strong> The exponential concentration is what makes $m$ scale only logarithmically with $n$, giving us this amazing dimension-independent result!
                </div>
            </div>
            
            <div class="key-insight">
                <strong>🌟 Summary: Why Concentration Inequalities Matter</strong>
                <ul>
                    <li><strong>Finite-sample guarantees:</strong> Work for any sample size, not just asymptotically</li>
                    <li><strong>Exponential rates:</strong> Provide very tight bounds with explicit constants</li>
                    <li><strong>Broad applicability:</strong> From basic statistics to advanced machine learning</li>
                    <li><strong>Theoretical foundation:</strong> Enable proofs of fundamental results like Johnson-Lindenstrauss</li>
                    <li><strong>Practical impact:</strong> Guide algorithm design and sample complexity analysis</li>
                </ul>
            </div>
        </div>
        
        <div class="navigation">
            <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); color: white; margin: 40px -20px -20px -20px; border-radius: 0 0 15px 15px;">
                <h3>🎓 Congratulations!</h3>
                <p>You've completed the tutorial on Statistical Concentration Inequalities.</p>
                <p>You now understand the fundamental tools for analyzing tail probabilities and their applications in modern data science.</p>
            </div>
        </div>
    </div>
</body>
</html> 