<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced RNN Architectures</title>
    <script>
    MathJax = {
        tex: {
            inlineMath: [['$', '$'], ['\\\\(', '\\\\)']],
            displayMath: [['$$', '$$'], ['\\\\[', '\\\\]']],
            processEscapes: true,
            processEnvironments: true
        },
        svg: {
            fontCache: 'global'
        }
    };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <style>
        :root {
            --primary-color: #4a6fa5;
            --secondary-color: #166088;
            --accent-color: #45b7d1;
            --light-accent: #e8f4f8;
            --dark-accent: #13334c;
            --text-color: #333;
            --light-gray: #f6f6f6;
            --success-color: #5cb85c;
            --warning-color: #f0ad4e;
            --error-color: #d9534f;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fcfcfc;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 30px 20px;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        h2 {
            color: var(--secondary-color);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
            margin-top: 40px;
            font-weight: 500;
        }
        
        h3 {
            color: var(--primary-color);
            margin-top: 30px;
            font-weight: 500;
        }
        
        p {
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .toc {
            background-color: var(--light-accent);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .toc h3 {
            margin-top: 0;
            border-bottom: 1px solid var(--accent-color);
            padding-bottom: 10px;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin-bottom: 10px;
        }
        
        .toc a {
            color: var(--secondary-color);
            text-decoration: none;
            transition: color 0.3s;
            display: inline-block;
            padding: 3px 0;
        }
        
        .toc a:hover {
            color: var(--accent-color);
            text-decoration: underline;
        }
        
        .visualization {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }
        
        .equation {
            margin: 20px 0;
            padding: 15px;
            background-color: var(--light-gray);
            border-left: 4px solid var(--accent-color);
            border-radius: 4px;
            overflow-x: auto;
        }
        
        .note {
            background-color: #fff3cd;
            border-left: 4px solid var(--warning-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .example {
            background-color: #d4edda;
            border-left: 4px solid var(--success-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .comparison {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            flex: 1;
            min-width: 300px;
            padding: 15px;
            background-color: var(--light-gray);
            border-radius: 4px;
        }
        
        .comparison-item h4 {
            margin-top: 0;
            color: var(--secondary-color);
        }
        
        .caption {
            font-style: italic;
            text-align: center;
            color: #666;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: var(--light-gray);
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }
            
            header {
                padding: 20px 15px;
            }
            
            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Advanced RNN Architectures</h1>
        <p>A comprehensive guide to encoder-decoder, deep recurrent, and recursive neural networks</p>
    </header>
    
    <div class="toc">
        <h3>Table of Contents</h3>
        <ul>
            <li><a href="#section1">1. Encoder-Decoder Sequence-to-Sequence Architectures</a></li>
            <li><a href="#section2">2. Deep Recurrent Networks</a></li>
            <li><a href="#section3">3. Recursive Neural Networks</a></li>
            <li><a href="#section4">4. The Challenge of Long-Term Dependencies</a></li>
        </ul>
    </div>
    
    <section id="section1">
        <h2>1. Encoder-Decoder Sequence-to-Sequence Architectures</h2>
        
        <p>
            Recurrent Neural Networks (RNNs) can be designed to map various types of input-output relationships:
        </p>
        
        <ul>
            <li>An input sequence to a fixed-size vector</li>
            <li>A fixed-size vector to a sequence</li>
            <li>An input sequence to an output sequence of the same length</li>
        </ul>
        
        <p>
            But what if we need to map an input sequence to an output sequence of a different length? This is where 
            encoder-decoder architectures come in, also known as sequence-to-sequence (seq2seq) models.
        </p>

        <div class="visualization">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="400" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold">Encoder-Decoder Architecture</text>
                
                <!-- Encoder Label -->
                <text x="200" y="70" text-anchor="middle" font-size="16" font-weight="bold" fill="#4a6fa5">Encoder</text>
                
                <!-- Decoder Label -->
                <text x="600" y="70" text-anchor="middle" font-size="16" font-weight="bold" fill="#166088">Decoder</text>
                
                <!-- Encoder-Decoder divider -->
                <line x1="400" y1="60" x2="400" y2="350" stroke="#ccc" stroke-width="2" stroke-dasharray="5,5" />
                
                <!-- Encoder inputs -->
                <g transform="translate(80, 120)">
                    <rect x="0" y="0" width="60" height="40" rx="5" ry="5" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                    <text x="30" y="25" text-anchor="middle" font-size="14">x(1)</text>
                </g>
                
                <g transform="translate(160, 120)">
                    <rect x="0" y="0" width="60" height="40" rx="5" ry="5" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                    <text x="30" y="25" text-anchor="middle" font-size="14">x(2)</text>
                </g>
                
                <g transform="translate(240, 120)">
                    <text x="30" y="25" text-anchor="middle" font-size="16">...</text>
                </g>
                
                <g transform="translate(320, 120)">
                    <rect x="0" y="0" width="60" height="40" rx="5" ry="5" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                    <text x="30" y="25" text-anchor="middle" font-size="14">x(nx)</text>
                </g>
                
                <!-- Encoder hidden states -->
                <g transform="translate(80, 200)">
                    <circle cx="30" cy="30" r="25" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="14">h(1)</text>
                </g>
                
                <g transform="translate(160, 200)">
                    <circle cx="30" cy="30" r="25" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="14">h(2)</text>
                </g>
                
                <g transform="translate(240, 200)">
                    <text x="30" y="35" text-anchor="middle" font-size="16">...</text>
                </g>
                
                <g transform="translate(320, 200)">
                    <circle cx="30" cy="30" r="25" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="14">h(nx)</text>
                </g>
                
                <!-- Context vector -->
                <g transform="translate(370, 200)">
                    <rect x="0" y="0" width="60" height="60" rx="5" ry="5" fill="#ffd8b1" stroke="#ff9800" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="16">C</text>
                </g>
                
                <!-- Decoder hidden states -->
                <g transform="translate(450, 200)">
                    <circle cx="30" cy="30" r="25" fill="#d4e6f1" stroke="#166088" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="14">h(1)</text>
                </g>
                
                <g transform="translate(530, 200)">
                    <circle cx="30" cy="30" r="25" fill="#d4e6f1" stroke="#166088" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="14">h(2)</text>
                </g>
                
                <g transform="translate(610, 200)">
                    <text x="30" y="35" text-anchor="middle" font-size="16">...</text>
                </g>
                
                <g transform="translate(690, 200)">
                    <circle cx="30" cy="30" r="25" fill="#d4e6f1" stroke="#166088" stroke-width="2" />
                    <text x="30" y="35" text-anchor="middle" font-size="14">h(ny)</text>
                </g>
                
                <!-- Decoder outputs -->
                <g transform="translate(450, 300)">
                    <rect x="0" y="0" width="60" height="40" rx="5" ry="5" fill="#d5f5e3" stroke="#166088" stroke-width="2" />
                    <text x="30" y="25" text-anchor="middle" font-size="14">y(1)</text>
                </g>
                
                <g transform="translate(530, 300)">
                    <rect x="0" y="0" width="60" height="40" rx="5" ry="5" fill="#d5f5e3" stroke="#166088" stroke-width="2" />
                    <text x="30" y="25" text-anchor="middle" font-size="14">y(2)</text>
                </g>
                
                <g transform="translate(610, 300)">
                    <text x="30" y="25" text-anchor="middle" font-size="16">...</text>
                </g>
                
                <g transform="translate(690, 300)">
                    <rect x="0" y="0" width="60" height="40" rx="5" ry="5" fill="#d5f5e3" stroke="#166088" stroke-width="2" />
                    <text x="30" y="25" text-anchor="middle" font-size="14">y(ny)</text>
                </g>
                
                <!-- Connections -->
                <!-- Encoder input to hidden -->
                <line x1="110" y1="160" x2="110" y2="175" stroke="#4a6fa5" stroke-width="1.5" />
                <line x1="190" y1="160" x2="190" y2="175" stroke="#4a6fa5" stroke-width="1.5" />
                <line x1="350" y1="160" x2="350" y2="175" stroke="#4a6fa5" stroke-width="1.5" />
                
                <!-- Encoder hidden to hidden -->
                <line x1="135" y1="230" x2="160" y2="230" stroke="#4a6fa5" stroke-width="1.5" />
                <line x1="215" y1="230" x2="240" y2="230" stroke="#4a6fa5" stroke-width="1.5" />
                <line x1="270" y1="230" x2="295" y2="230" stroke="#4a6fa5" stroke-width="1.5" />
                
                <!-- Last encoder hidden to context -->
                <line x1="375" y1="230" x2="370" y2="230" stroke="#ff9800" stroke-width="2" />
                
                <!-- Context to first decoder hidden -->
                <line x1="430" y1="230" x2="450" y2="230" stroke="#166088" stroke-width="2" />
                
                <!-- Decoder hidden to hidden -->
                <line x1="505" y1="230" x2="530" y2="230" stroke="#166088" stroke-width="1.5" />
                <line x1="585" y1="230" x2="610" y2="230" stroke="#166088" stroke-width="1.5" />
                <line x1="640" y1="230" x2="665" y2="230" stroke="#166088" stroke-width="1.5" />
                
                <!-- Decoder hidden to output -->
                <line x1="480" y1="255" x2="480" y2="300" stroke="#166088" stroke-width="1.5" />
                <line x1="560" y1="255" x2="560" y2="300" stroke="#166088" stroke-width="1.5" />
                <line x1="720" y1="255" x2="720" y2="300" stroke="#166088" stroke-width="1.5" />
                
                <!-- Arrows -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
                    </marker>
                </defs>
            </svg>
            <p class="caption">Figure 1: Encoder-Decoder (Sequence-to-Sequence) RNN architecture for mapping an input sequence 
            (x(1), x(2), ..., x(nx)) to an output sequence (y(1), y(2), ..., y(ny)) of possibly different lengths. The encoder 
            processes the input sequence, and the decoder generates the output sequence based on a context vector C derived from the 
            encoder's final state.</p>
        </div>
        
        <h3>1.1 Structure and Operation</h3>
        <p>
            The encoder-decoder architecture consists of two main components:
        </p>
        
        <ol>
            <li>
                <strong>Encoder (Reader or Input RNN):</strong> Processes the input sequence and compresses it into a fixed-length 
                context vector C, which is typically derived from the encoder's final hidden state.
            </li>
            <li>
                <strong>Decoder (Writer or Output RNN):</strong> Takes the context vector C and generates the output sequence.
            </li>
        </ol>
        
        <p>
            This architecture was first proposed independently by Cho et al. (2014) and Sutskever et al. (2014). The key innovation 
            is that it allows mapping between sequences of different lengths (nx and ny), which wasn't possible with the standard 
            RNN architectures discussed previously.
        </p>
        
        <h3>1.2 Training Objective</h3>
        <p>
            The encoder-decoder networks are trained jointly to maximize the average log probability:
        </p>
        
        <div class="equation">
            $$\log P(y^{(1)}, \ldots, y^{(n_y)} | x^{(1)}, \ldots, x^{(n_x)})$$
        </div>
        
        <p>
            over all the pairs of input and output sequences in the training set.
        </p>
        
        <h3>1.3 Context Vector</h3>
        <p>
            The context vector C is typically the last hidden state of the encoder RNN:
        </p>
        
        <div class="equation">
            $$C = h_{n_x}$$
        </div>
        
        <p>
            This context vector is then provided to the decoder RNN, which essentially functions as a vector-to-sequence RNN.
        </p>
        
        <div class="note">
            <p>
                <strong>Note:</strong> There are different ways to provide the context vector to the decoder:
            </p>
            <ul>
                <li>As the initial state of the decoder RNN</li>
                <li>As an additional input to the hidden units at each time step</li>
                <li>A combination of both approaches</li>
            </ul>
        </div>
        
        <h3>1.4 Advantages and Limitations</h3>
        
        <div class="comparison">
            <div class="comparison-item">
                <h4>Advantages</h4>
                <ul>
                    <li>Can map between sequences of different lengths</li>
                    <li>Handles variable-length inputs and outputs</li>
                    <li>End-to-end training is possible</li>
                    <li>Has shown great success in machine translation, speech recognition, and more</li>
                </ul>
            </div>
            
            <div class="comparison-item">
                <h4>Limitations</h4>
                <ul>
                    <li>The context vector can become a bottleneck for long sequences</li>
                    <li>May struggle to capture all relevant information from long input sequences</li>
                    <li>Fixed-size context vector may not be sufficient for complex relationships</li>
                </ul>
            </div>
        </div>
        
        <h3>1.5 Addressing the Bottleneck: Attention Mechanisms</h3>
        <p>
            Bahdanau et al. (2015) identified a limitation in the basic encoder-decoder model: when the context vector C is too 
            small to adequately summarize a long sequence. To address this, they proposed:
        </p>
        
        <ul>
            <li>Making C a variable-length sequence rather than a fixed-size vector</li>
            <li>Introducing an attention mechanism that learns to associate elements of the context sequence with elements of the output sequence</li>
        </ul>
        
        <p>
            Attention mechanisms have since become a critical component in many sequence-to-sequence models, allowing them to focus 
            on relevant parts of the input when generating each element of the output.
        </p>
        
        <h3>1.6 Applications</h3>
        <p>
            Encoder-decoder architectures have been successfully applied to a wide range of tasks, including:
        </p>
        
        <ul>
            <li><strong>Machine Translation:</strong> Translating text from one language to another</li>
            <li><strong>Speech Recognition:</strong> Converting audio to text transcriptions</li>
            <li><strong>Text Summarization:</strong> Creating concise summaries of longer documents</li>
            <li><strong>Question Answering:</strong> Generating answers to natural language questions</li>
            <li><strong>Image Captioning:</strong> When combined with CNNs for image encoding</li>
        </ul>
    </section>
    
    <section id="section2">
        <h2>2. Deep Recurrent Networks</h2>
        
        <p>
            The computations in standard recurrent neural networks can be decomposed into three main blocks:
        </p>
        
        <ol>
            <li>From the input to the hidden state</li>
            <li>From the previous hidden state to the next hidden state</li>
            <li>From the hidden state to the output</li>
        </ol>
        
        <p>
            In traditional RNNs, each of these blocks corresponds to a single weight matrix and represents a shallow transformation. 
            But what if we could introduce depth in each of these operations?
        </p>
        
        <div class="note">
            <p>
                <strong>Shallow Transformation:</strong> A transformation that would be represented by a single layer within a deep 
                MLP, typically consisting of an affine transformation followed by a fixed nonlinearity.
            </p>
        </div>
        
        <p>
            Experimental evidence from researchers like Graves et al. (2013) and Pascanu et al. (2014) strongly suggests that 
            adding depth to RNNs can be beneficial. This aligns with the general principle in deep learning that sufficient depth 
            is needed to perform complex mappings effectively.
        </p>
        
        <h3>2.1 Approaches to Creating Deep RNNs</h3>
        
        <div class="visualization">
            <svg width="800" height="600" viewBox="0 0 800 600">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="600" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold">Ways to Make RNNs Deep</text>
                
                <!-- (a) Hierarchical Hidden States -->
                <g transform="translate(130, 90)">
                    <text x="0" y="0" font-size="16" font-weight="bold">(a) Hierarchical Hidden States</text>
                    
                    <!-- Input -->
                    <rect x="40" y="40" width="60" height="40" rx="5" ry="5" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                    <text x="70" y="65" text-anchor="middle" font-size="14">x</text>
                    
                    <!-- Lower hidden layer -->
                    <rect x="40" y="120" width="60" height="40" rx="5" ry="5" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                    <text x="70" y="145" text-anchor="middle" font-size="14">h¹</text>
                    
                    <!-- Upper hidden layer -->
                    <rect x="40" y="200" width="60" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                    <text x="70" y="225" text-anchor="middle" font-size="14">h²</text>
                    
                    <!-- Output -->
                    <rect x="40" y="280" width="60" height="40" rx="5" ry="5" fill="#d5f5e3" stroke="#2ecc71" stroke-width="2" />
                    <text x="70" y="305" text-anchor="middle" font-size="14">y</text>
                    
                    <!-- Recurrent connections for lower layer -->
                    <path d="M 110 140 C 130 140, 130 100, 100 100 L 100 120" fill="none" stroke="#4a6fa5" stroke-width="2" />
                    <polygon points="100,120 95,110 105,110" fill="#4a6fa5" />
                    
                    <!-- Recurrent connections for upper layer -->
                    <path d="M 110 220 C 150 220, 150 180, 100 180 L 100 200" fill="none" stroke="#166088" stroke-width="2" />
                    <polygon points="100,200 95,190 105,190" fill="#166088" />
                    
                    <!-- Connections -->
                    <line x1="70" y1="80" x2="70" y2="120" stroke="#4a6fa5" stroke-width="1.5" />
                    <line x1="70" y1="160" x2="70" y2="200" stroke="#166088" stroke-width="1.5" />
                    <line x1="70" y1="240" x2="70" y2="280" stroke="#2ecc71" stroke-width="1.5" />
                </g>
                
                <!-- (b) Deep Transformations -->
                <g transform="translate(400, 90)">
                    <text x="0" y="0" font-size="16" font-weight="bold">(b) Deep Transformations</text>
                    
                    <!-- Input -->
                    <rect x="40" y="40" width="60" height="40" rx="5" ry="5" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                    <text x="70" y="65" text-anchor="middle" font-size="14">x</text>
                    
                    <!-- Input MLP -->
                    <rect x="10" y="110" width="120" height="50" rx="5" ry="5" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" stroke-dasharray="5,2" />
                    <text x="70" y="140" text-anchor="middle" font-size="14">Input MLP</text>
                    
                    <!-- Hidden state -->
                    <rect x="40" y="190" width="60" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                    <text x="70" y="215" text-anchor="middle" font-size="14">h</text>
                    
                    <!-- Recurrent MLP -->
                    <rect x="140" y="185" width="120" height="50" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" stroke-dasharray="5,2" />
                    <text x="200" y="215" text-anchor="middle" font-size="14">Recurrent MLP</text>
                    
                    <!-- Output MLP -->
                    <rect x="10" y="260" width="120" height="50" rx="5" ry="5" fill="#d5f5e3" stroke="#2ecc71" stroke-width="2" stroke-dasharray="5,2" />
                    <text x="70" y="290" text-anchor="middle" font-size="14">Output MLP</text>
                    
                    <!-- Output -->
                    <rect x="40" y="340" width="60" height="40" rx="5" ry="5" fill="#d5f5e3" stroke="#2ecc71" stroke-width="2" />
                    <text x="70" y="365" text-anchor="middle" font-size="14">y</text>
                    
                    <!-- Recurrent connection -->
                    <path d="M 100 210 L 140 210 M 260 210 C 280 210, 280 170, 260 170 L 100 170 L 100 190" fill="none" stroke="#166088" stroke-width="2" />
                    <polygon points="100,190 95,180 105,180" fill="#166088" />
                    
                    <!-- Connections -->
                    <line x1="70" y1="80" x2="70" y2="110" stroke="#4a6fa5" stroke-width="1.5" />
                    <line x1="70" y1="160" x2="70" y2="190" stroke="#166088" stroke-width="1.5" />
                    <line x1="70" y1="230" x2="70" y2="260" stroke="#2ecc71" stroke-width="1.5" />
                    <line x1="70" y1="310" x2="70" y2="340" stroke="#2ecc71" stroke-width="1.5" />
                </g>
                
                <!-- (c) Skip Connections -->
                <g transform="translate(670, 90)">
                    <text x="0" y="0" font-size="16" font-weight="bold">(c) Skip Connections</text>
                    
                    <!-- Input -->
                    <rect x="40" y="40" width="60" height="40" rx="5" ry="5" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                    <text x="70" y="65" text-anchor="middle" font-size="14">x</text>
                    
                    <!-- Input MLP -->
                    <rect x="10" y="110" width="120" height="50" rx="5" ry="5" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" stroke-dasharray="5,2" />
                    <text x="70" y="140" text-anchor="middle" font-size="14">Input MLP</text>
                    
                    <!-- Hidden state -->
                    <rect x="40" y="190" width="60" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                    <text x="70" y="215" text-anchor="middle" font-size="14">h</text>
                    
                    <!-- Recurrent MLP -->
                    <rect x="10" y="260" width="120" height="50" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" stroke-dasharray="5,2" />
                    <text x="70" y="290" text-anchor="middle" font-size="14">Recurrent MLP</text>
                    
                    <!-- Output -->
                    <rect x="40" y="340" width="60" height="40" rx="5" ry="5" fill="#d5f5e3" stroke="#2ecc71" stroke-width="2" />
                    <text x="70" y="365" text-anchor="middle" font-size="14">y</text>
                    
                    <!-- Skip connection -->
                    <path d="M -40 210 C -60 210, -60 290, 10 290" fill="none" stroke="#ff7043" stroke-width="2.5" stroke-dasharray="5,3" />
                    <text x="-30" y="250" text-anchor="middle" font-size="12" fill="#ff7043">Skip</text>
                    
                    <!-- Recurrent connection -->
                    <path d="M 40 230 L 40 260 M 40 310 C -20 310, -20 180, 40 180 L 40 190" fill="none" stroke="#166088" stroke-width="2" />
                    <polygon points="40,190 35,180 45,180" fill="#166088" />
                    
                    <!-- Connections -->
                    <line x1="70" y1="80" x2="70" y2="110" stroke="#4a6fa5" stroke-width="1.5" />
                    <line x1="70" y1="160" x2="70" y2="190" stroke="#166088" stroke-width="1.5" />
                    <line x1="70" y1="230" x2="70" y2="260" stroke="#166088" stroke-width="1.5" />
                    <line x1="70" y1="310" x2="70" y2="340" stroke="#2ecc71" stroke-width="1.5" />
                </g>
                
                <!-- Descriptions -->
                <g transform="translate(50, 430)">
                    <text x="120" y="0" text-anchor="middle" font-size="14">
                        Hidden state broken into 
                    </text>
                    <text x="120" y="20" text-anchor="middle" font-size="14">
                        hierarchical groups
                    </text>
                    
                    <text x="400" y="0" text-anchor="middle" font-size="14">
                        Deeper computation in each part 
                    </text>
                    <text x="400" y="20" text-anchor="middle" font-size="14">
                        (input-to-hidden, hidden-to-hidden, 
                    </text>
                    <text x="400" y="40" text-anchor="middle" font-size="14">
                        hidden-to-output)
                    </text>
                    
                    <text x="670" y="0" text-anchor="middle" font-size="14">
                        Skip connections mitigate 
                    </text>
                    <text x="670" y="20" text-anchor="middle" font-size="14">
                        path-lengthening effect
                    </text>
                </g>
                
                <!-- Path length explanation -->
                <g transform="translate(400, 490)">
                    <text x="0" y="0" font-size="14" font-weight="bold">Challenge with Deep RNNs:</text>
                    <text x="0" y="25" font-size="14">
                        Adding depth makes the path between time steps longer.
                    </text>
                    <text x="0" y="50" font-size="14">
                        This can make optimization more difficult.
                    </text>
                    <text x="0" y="75" font-size="14">
                        Skip connections help by providing shorter paths through the network.
                    </text>
                </g>
            </svg>
            <p class="caption">Figure 2: Different approaches to making recurrent neural networks deep. (a) Breaking the hidden state into 
            hierarchical groups, (b) Using deep computation (MLPs) for each of the main RNN transformations, (c) Adding skip connections 
            to mitigate the path-lengthening effect.</p>
        </div>
        
        <p>
            There are several ways to introduce depth in RNNs:
        </p>
        
        <h3>2.2 Hierarchical Hidden States</h3>
        <p>
            Graves et al. (2013) showed significant benefits from decomposing the hidden state of an RNN into multiple layers, 
            as illustrated in Figure 2(a). The lower layers in this hierarchy transform the raw input into a representation that 
            is more appropriate for processing at the higher levels of the hidden state.
        </p>
        
        <h3>2.3 Deep Transformations</h3>
        <p>
            Pascanu et al. (2014) proposed using a separate MLP (potentially deep) for each of the three main blocks in an RNN:
        </p>
        
        <ol>
            <li>From input to hidden state (input-to-hidden)</li>
            <li>From previous hidden state to next hidden state (hidden-to-hidden)</li>
            <li>From hidden state to output (hidden-to-output)</li>
        </ol>
        
        <p>
            This approach, illustrated in Figure 2(b), adds more representational capacity to each step of the RNN's operation.
        </p>
        
        <h3>2.4 The Path-Lengthening Challenge</h3>
        <p>
            While adding depth can increase representational capacity, it comes with challenges. It's generally easier to 
            optimize shallower architectures. Adding depth to an RNN's hidden-to-hidden transition makes the shortest path 
            from a variable in time step t to a variable in time step t+1 longer.
        </p>
        
        <p>
            For example, if an MLP with a single hidden layer is used for the state-to-state transition, the path length 
            between variables in different time steps is doubled compared to a standard RNN.
        </p>
        
        <h3>2.5 Skip Connections</h3>
        <p>
            To mitigate the path-lengthening effect, Pascanu et al. (2014) suggested introducing skip connections in the 
            hidden-to-hidden path, as shown in Figure 2(c). These connections provide shortcuts through the network, 
            allowing information to flow more directly between time steps.
        </p>
        
        <div class="note">
            <p>
                <strong>Skip Connections:</strong> Direct connections that bypass one or more layers in a neural network. They 
                help with the flow of gradients during backpropagation, making it easier to train very deep networks.
            </p>
        </div>
        
        <h3>2.6 Benefits of Deep RNNs</h3>
        <p>
            Despite the challenges, deep RNNs offer several advantages:
        </p>
        
        <ul>
            <li><strong>Increased Representational Capacity:</strong> More layers allow the network to learn more complex functions.</li>
            <li><strong>Hierarchical Feature Learning:</strong> Different layers can capture different levels of abstraction.</li>
            <li><strong>Improved Performance:</strong> Experimental evidence shows better results on various sequence modeling tasks.</li>
        </ul>
        
        <h3>2.7 Practical Considerations</h3>
        <p>
            When designing deep RNNs, consider the following:
        </p>
        
        <ul>
            <li><strong>Balancing Depth and Trainability:</strong> More depth increases capacity but can make training harder.</li>
            <li><strong>Use of Skip Connections:</strong> These can significantly help with training deeper architectures.</li>
            <li><strong>Initialization Schemes:</strong> Proper initialization becomes more critical in deeper networks.</li>
            <li><strong>Normalization Techniques:</strong> Layer normalization can help stabilize training in deep RNNs.</li>
        </ul>
        
        <p>
            Modern deep learning frameworks make it relatively easy to experiment with different deep RNN architectures, 
            allowing practitioners to find the right balance between depth and trainability for their specific tasks.
        </p>
    </section>
    
    <section id="section3">
        <h2>3. Recursive Neural Networks</h2>
        
        <p>
            Recursive Neural Networks represent another generalization of recurrent networks, but with a fundamentally 
            different computational structure. While recurrent networks have a chain-like computational graph, recursive 
            networks have a tree-structured computational graph.
        </p>
        
        <div class="note">
            <p>
                <strong>Disambiguation:</strong> To avoid confusion with Recurrent Neural Networks, we should not abbreviate 
                "Recursive Neural Network" as "RNN." Some researchers use "RecNN" or simply "recursive network" to distinguish them.
            </p>
        </div>
        
        <div class="visualization">
            <svg width="800" height="450" viewBox="0 0 800 450">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="450" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold">Recursive Neural Network Architecture</text>
                
                <!-- Tree structure -->
                <!-- Root -->
                <circle cx="400" cy="80" r="30" fill="#e8f4f8" stroke="#4a6fa5" stroke-width="2" />
                <text x="400" y="85" text-anchor="middle" font-size="14">o</text>
                
                <!-- Internal nodes level 1 -->
                <circle cx="300" cy="160" r="30" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                <text x="300" y="165" text-anchor="middle" font-size="14">h</text>
                
                <circle cx="500" cy="160" r="30" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                <text x="500" y="165" text-anchor="middle" font-size="14">h</text>
                
                <!-- Internal nodes level 2 (left branch) -->
                <circle cx="240" cy="240" r="30" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                <text x="240" y="245" text-anchor="middle" font-size="14">h</text>
                
                <circle cx="360" cy="240" r="30" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                <text x="360" y="245" text-anchor="middle" font-size="14">h</text>
                
                <!-- Internal nodes level 2 (right branch) -->
                <circle cx="500" cy="240" r="30" fill="#d1ecf1" stroke="#4a6fa5" stroke-width="2" />
                <text x="500" y="245" text-anchor="middle" font-size="14">h</text>
                
                <!-- Leaf nodes (inputs) -->
                <rect x="180" y="320" width="40" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                <text x="200" y="345" text-anchor="middle" font-size="14">x(1)</text>
                
                <rect x="260" y="320" width="40" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                <text x="280" y="345" text-anchor="middle" font-size="14">x(2)</text>
                
                <rect x="340" y="320" width="40" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                <text x="360" y="345" text-anchor="middle" font-size="14">x(3)</text>
                
                <rect x="480" y="320" width="40" height="40" rx="5" ry="5" fill="#aed6f1" stroke="#166088" stroke-width="2" />
                <text x="500" y="345" text-anchor="middle" font-size="14">x(4)</text>
                
                <!-- Connections -->
                <line x1="400" y1="110" x2="300" y2="130" stroke="#4a6fa5" stroke-width="2" />
                <line x1="400" y1="110" x2="500" y2="130" stroke="#4a6fa5" stroke-width="2" />
                
                <line x1="300" y1="190" x2="240" y2="210" stroke="#4a6fa5" stroke-width="2" />
                <line x1="300" y1="190" x2="360" y2="210" stroke="#4a6fa5" stroke-width="2" />
                
                <line x1="240" y1="270" x2="200" y2="320" stroke="#166088" stroke-width="2" />
                <line x1="240" y1="270" x2="280" y2="320" stroke="#166088" stroke-width="2" />
                
                <line x1="360" y1="270" x2="360" y2="320" stroke="#166088" stroke-width="2" />
                
                <line x1="500" y1="270" x2="500" y2="320" stroke="#166088" stroke-width="2" />
                
                <!-- Weight labels -->
                <text x="350" y="110" text-anchor="middle" font-size="12" fill="#166088">V</text>
                <text x="450" y="110" text-anchor="middle" font-size="12" fill="#166088">V</text>
                
                <text x="270" y="170" text-anchor="middle" font-size="12" fill="#166088">W</text>
                <text x="330" y="170" text-anchor="middle" font-size="12" fill="#166088">W</text>
                
                <text x="210" y="290" text-anchor="middle" font-size="12" fill="#166088">U</text>
                <text x="265" y="290" text-anchor="middle" font-size="12" fill="#166088">U</text>
                <text x="340" y="290" text-anchor="middle" font-size="12" fill="#166088">U</text>
                <text x="480" y="290" text-anchor="middle" font-size="12" fill="#166088">U</text>
                
                <!-- Output label -->
                <text x="430" y="70" text-anchor="middle" font-size="12" fill="#2ecc71">Output</text>
                
                <!-- Hidden nodes label -->
                <text x="555" y="160" text-anchor="middle" font-size="12" fill="#4a6fa5">Hidden states</text>
                
                <!-- Input label -->
                <text x="555" y="320" text-anchor="middle" font-size="12" fill="#166088">Input sequence</text>
                
                <!-- Explanation -->
                <text x="400" y="390" text-anchor="middle" font-size="14">A variable-size sequence x(1), x(2), ..., x(τ) can be mapped to a</text>
                <text x="400" y="410" text-anchor="middle" font-size="14">fixed-size representation with a fixed set of parameters (U, V, W)</text>
                <text x="400" y="430" text-anchor="middle" font-size="14">based on a tree-structured computational graph</text>
            </svg>
            <p class="caption">Figure 3: A recursive neural network processes inputs arranged in a tree structure. The same weights 
            (U for input-to-hidden, W for hidden-to-hidden, and V for hidden-to-output) are reused throughout the tree. This enables 
            processing of variable-length sequences with a fixed set of parameters while exploiting hierarchical structure.</p>
        </div>
        
        <h3>3.1 Historical Development</h3>
        <p>
            Recursive neural networks were introduced by Pollack (1990), and their potential for learning to reason was described 
            by Bottou (2011). They've been successfully applied in various domains:
        </p>
        
        <ul>
            <li>Processing data structures as input to neural networks (Frasconi et al., 1997, 1998)</li>
            <li>Natural language processing (Socher et al., 2011a, 2011c, 2013a)</li>
            <li>Computer vision (Socher et al., 2011b)</li>
        </ul>
        
        <h3>3.2 Computational Structure</h3>
        <p>
            The key feature of recursive networks is their tree-structured computational graph. This contrasts with the 
            chain-like structure of recurrent networks. In a recursive network:
        </p>
        
        <ul>
            <li>Each node processes inputs from its child nodes</li>
            <li>The same transformation is applied at each node of the tree</li>
            <li>Information flows from the leaves to the root</li>
        </ul>
        
        <p>
            This structure allows recursive networks to naturally model hierarchical data, such as parsed sentences in 
            natural language processing or hierarchical image segmentation in computer vision.
        </p>
        
        <h3>3.3 Advantages Over Recurrent Networks</h3>
        <p>
            One clear advantage of recursive networks over recurrent networks is the potential reduction in depth of the 
            computational graph. For a sequence of length τ:
        </p>
        
        <div class="comparison">
            <div class="comparison-item">
                <h4>Recurrent Network</h4>
                <p>Depth of computation: τ (linear in sequence length)</p>
                <p>Path length between elements at opposite ends of the sequence: τ steps</p>
            </div>
            
            <div class="comparison-item">
                <h4>Recursive Network (Balanced Tree)</h4>
                <p>Depth of computation: O(log τ) (logarithmic in sequence length)</p>
                <p>Path length between elements at opposite ends: at most 2 log τ steps</p>
            </div>
        </div>
        
        <p>
            This reduced depth can be beneficial for handling long-term dependencies, as the gradient needs to propagate 
            through fewer layers during training.
        </p>
        
        <h3>3.4 Tree Structure Considerations</h3>
        <p>
            An important question in recursive networks is how to structure the tree. There are several approaches:
        </p>
        
        <ol>
            <li>
                <strong>Fixed Tree Structure:</strong> Use a structure that doesn't depend on the data, such as a balanced binary tree.
            </li>
            <li>
                <strong>Domain-Specific Trees:</strong> In some applications, external methods can suggest the appropriate tree 
                structure. For example, in natural language processing, the tree structure can follow the parse tree of the 
                sentence provided by a natural language parser (Socher et al., 2011a, 2013a).
            </li>
            <li>
                <strong>Learned Tree Structure:</strong> Ideally, the learner itself would discover and infer the tree structure 
                that is appropriate for any given input, as suggested by Bottou (2011) and explored in subsequent research.
            </li>
        </ol>
        
        <h3>3.5 Variants and Extensions</h3>
        <p>
            Many variants of recursive networks have been proposed:
        </p>
        
        <ul>
            <li>
                <strong>Data-Associated Trees:</strong> Frasconi et al. (1997, 1998) associate data with a tree structure, 
                with inputs and targets linked to individual nodes of the tree.
            </li>
            <li>
                <strong>Advanced Node Computations:</strong> The computation at each node doesn't have to be the traditional 
                artificial neuron computation. For example, Socher et al. (2013a) proposed using tensor operations and bilinear 
                forms, which have been found useful for modeling relationships between concepts represented by continuous vectors.
            </li>
            <li>
                <strong>Tree-LSTMs:</strong> Extending the Long Short-Term Memory (LSTM) architecture to tree structures, 
                allowing for better handling of long-range dependencies in hierarchical data.
            </li>
        </ul>
        
        <h3>3.6 Applications in Detail</h3>
        
        <h4>3.6.1 Natural Language Processing</h4>
        <p>
            Recursive neural networks are particularly well-suited for natural language processing tasks, where sentences 
            naturally have a hierarchical structure that can be represented as a parse tree:
        </p>
        
        <ul>
            <li><strong>Sentiment Analysis:</strong> Capturing the compositional effects of negation and other linguistic phenomena</li>
            <li><strong>Paraphrase Detection:</strong> Comparing the semantic structure of two sentences</li>
            <li><strong>Relation Extraction:</strong> Identifying relationships between entities in text</li>
            <li><strong>Semantic Relatedness:</strong> Measuring how related two pieces of text are</li>
        </ul>
        
        <h4>3.6.2 Computer Vision</h4>
        <p>
            In computer vision, recursive networks can model hierarchical decompositions of images:
        </p>
        
        <ul>
            <li><strong>Scene Parsing:</strong> Breaking down images into semantically meaningful regions</li>
            <li><strong>Object Recognition:</strong> Identifying objects in images based on their parts and relationships</li>
            <li><strong>3D Object Classification:</strong> Processing 3D point clouds or meshes with hierarchical structure</li>
        </ul>
        
        <h3>3.7 Challenges and Limitations</h3>
        <p>
            Despite their advantages, recursive neural networks face several challenges:
        </p>
        
        <ul>
            <li><strong>Tree Structure Determination:</strong> Deciding on the optimal tree structure for a given task can be difficult</li>
            <li><strong>Computational Complexity:</strong> The recursive nature of the computation can make parallel processing more challenging</li>
            <li><strong>Training Issues:</strong> Like deep networks, they can suffer from vanishing/exploding gradients</li>
            <li><strong>Limited Adoption:</strong> They haven't been as widely adopted as recurrent or convolutional networks in many tasks</li>
        </ul>
        
        <p>
            Nevertheless, for tasks with inherent hierarchical structure, recursive neural networks remain a powerful and elegant approach.
        </p>
    </section>
    
    <section id="section4">
        <h2>4. The Challenge of Long-Term Dependencies</h2>
        
        <p>
            One of the most significant challenges in training recurrent neural networks is their ability to learn and 
            remember information over long sequences. This is known as the challenge of long-term dependencies, and it 
            remains one of the main challenges in deep learning.
        </p>
        
        <h3>4.1 Understanding the Problem</h3>
        
        <p>
            When RNNs process long sequences, they need to retain and use information from many steps back. However, the 
            basic RNN architecture struggles with this for fundamental mathematical reasons related to how gradients 
            propagate through the network during training.
        </p>
        
        <div class="visualization">
            <svg width="800" height="480" viewBox="0 0 800 480">
                <!-- Background -->
                <rect x="0" y="0" width="800" height="480" fill="#f9f9f9" rx="10" ry="10" stroke="#ddd" stroke-width="1" />
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold">Function Composition in RNNs and Gradient Flow</text>
                
                <!-- Function composition visualization -->
                <g transform="translate(50, 80)">
                    <!-- Axes -->
                    <line x1="0" y1="200" x2="700" y2="200" stroke="#333" stroke-width="2" />
                    <line x1="350" y1="50" x2="350" y2="350" stroke="#333" stroke-width="2" />
                    
                    <!-- Axis labels -->
                    <text x="350" y="380" text-anchor="middle" font-size="14">Input coordinate</text>
                    <text x="0" y="50" font-size="14" transform="rotate(-90, 0, 50)">Projection of output</text>
                    
                    <!-- Ticks for x-axis -->
                    <line x1="150" y1="195" x2="150" y2="205" stroke="#333" stroke-width="2" />
                    <line x1="250" y1="195" x2="250" y2="205" stroke="#333" stroke-width="2" />
                    <line x1="350" y1="195" x2="350" y2="205" stroke="#333" stroke-width="2" />
                    <line x1="450" y1="195" x2="450" y2="205" stroke="#333" stroke-width="2" />
                    <line x1="550" y1="195" x2="550" y2="205" stroke="#333" stroke-width="2" />
                    
                    <!-- Tick labels for x-axis -->
                    <text x="150" y="220" text-anchor="middle" font-size="12">-40</text>
                    <text x="250" y="220" text-anchor="middle" font-size="12">-20</text>
                    <text x="350" y="220" text-anchor="middle" font-size="12">0</text>
                    <text x="450" y="220" text-anchor="middle" font-size="12">20</text>
                    <text x="550" y="220" text-anchor="middle" font-size="12">40</text>
                    
                    <!-- Ticks for y-axis -->
                    <line x1="345" y1="100" x2="355" y2="100" stroke="#333" stroke-width="2" />
                    <line x1="345" y1="150" x2="355" y2="150" stroke="#333" stroke-width="2" />
                    <line x1="345" y1="200" x2="355" y2="200" stroke="#333" stroke-width="2" />
                    <line x1="345" y1="250" x2="355" y2="250" stroke="#333" stroke-width="2" />
                    <line x1="345" y1="300" x2="355" y2="300" stroke="#333" stroke-width="2" />
                    
                    <!-- Tick labels for y-axis -->
                    <text x="335" y="100" text-anchor="end" font-size="12">2</text>
                    <text x="335" y="150" text-anchor="end" font-size="12">1</text>
                    <text x="335" y="200" text-anchor="end" font-size="12">0</text>
                    <text x="335" y="250" text-anchor="end" font-size="12">-1</text>
                    <text x="335" y="300" text-anchor="end" font-size="12">-2</text>
                    
                    <!-- Plot lines for different time steps -->
                    <!-- t=1 -->
                    <path d="M 50,190 Q 150,170 250,195 T 450,205 T 650,190" fill="none" stroke="#4a6fa5" stroke-width="2" />
                    
                    <!-- t=2 -->
                    <path d="M 50,180 Q 100,160 200,210 T 350,170 T 500,220 T 650,180" fill="none" stroke="#45b7d1" stroke-width="2" />
                    
                    <!-- t=3 -->
                    <path d="M 50,170 Q 90,130 170,220 T 280,160 T 420,230 T 550,150 T 650,170" fill="none" stroke="#166088" stroke-width="2" />
                    
                    <!-- t=4 -->
                    <path d="M 50,160 Q 80,110 140,230 T 230,140 T 350,240 T 450,130 T 550,210 T 650,160" fill="none" stroke="#13334c" stroke-width="2" />
                    
                    <!-- t=5 -->
                    <path d="M 50,150 Q 70,90 120,240 T 200,120 T 300,250 T 400,110 T 500,230 T 600,130 T 650,150" fill="none" stroke="#ff7043" stroke-width="2" />
                    
                    <!-- Legend -->
                    <g transform="translate(500, 70)">
                        <text x="0" y="0" font-size="12" font-weight="bold">Time steps</text>
                        <line x1="0" y1="15" x2="20" y2="15" stroke="#4a6fa5" stroke-width="2" />
                        <text x="25" y="18" font-size="12">t=1</text>
                        <line x1="0" y1="35" x2="20" y2="35" stroke="#45b7d1" stroke-width="2" />
                        <text x="25" y="38" font-size="12">t=2</text>
                        <line x1="0" y1="55" x2="20" y2="55" stroke="#166088" stroke-width="2" />
                        <text x="25" y="58" font-size="12">t=3</text>
                        <line x1="0" y1="75" x2="20" y2="75" stroke="#13334c" stroke-width="2" />
                        <text x="25" y="78" font-size="12">t=4</text>
                        <line x1="0" y1="95" x2="20" y2="95" stroke="#ff7043" stroke-width="2" />
                        <text x="25" y="98" font-size="12">t=5</text>
                    </g>
                </g>
                
                <!-- Explanation -->
                <g transform="translate(400, 430)">
                    <text x="0" y="0" text-anchor="middle" font-size="14">
                        When composing many nonlinear functions (as in RNNs), the result becomes highly nonlinear
                    </text>
                    <text x="0" y="25" text-anchor="middle" font-size="14">
                        with most regions having tiny derivatives (flat regions) and some with large derivatives.
                    </text>
                </g>
            </svg>
            <p class="caption">Figure 4: Function composition in RNNs. As we compose the same nonlinear function multiple times 
            (increasing time steps), the result becomes increasingly nonlinear with most regions having vanishingly small gradients 
            and a few regions with very large gradients. This makes learning long-term dependencies difficult.</p>
        </div>
        
        <p>
            The problem arises from the repeated application of the same function during the forward pass of an RNN. When we 
            backpropagate through these repeated applications, we end up multiplying the same Jacobian matrices many times. 
            This leads to one of two problematic outcomes:
        </p>
        
        <div class="comparison">
            <div class="comparison-item">
                <h4>Vanishing Gradients</h4>
                <p>
                    If the largest eigenvalue of the Jacobian is less than 1, the gradients shrink exponentially with the 
                    number of time steps, making it nearly impossible to learn dependencies over many steps.
                </p>
            </div>
            
            <div class="comparison-item">
                <h4>Exploding Gradients</h4>
                <p>
                    If the largest eigenvalue of the Jacobian is greater than 1, the gradients grow exponentially with the 
                    number of time steps, potentially causing numerical instability during training.
                </p>
            </div>
        </div>
        
        <h3>4.2 A Simple Recurrence Example</h3>
        
        <p>
            To understand the problem better, consider a very simple recurrent neural network without a nonlinear activation 
            function and without inputs:
        </p>
        
        <div class="equation">
            $$h^{(t)} = W h^{(t-1)}$$
        </div>
        
        <p>
            This recurrence relation can be simplified to:
        </p>
        
        <div class="equation">
            $$h^{(t)} = W^t h^{(0)}$$
        </div>
        
        <p>
            If W admits an eigendecomposition W = QΛQ<sup>-1</sup> with orthogonal Q, the recurrence further simplifies to:
        </p>
        
        <div class="equation">
            $$h^{(t)} = Q \Lambda^t Q^{-1} h^{(0)}$$
        </div>
        
        <p>
            Here, the eigenvalues are raised to the power of t, causing:
        </p>
        
        <ul>
            <li>Eigenvalues with magnitude less than 1 to decay to zero</li>
            <li>Eigenvalues with magnitude greater than 1 to explode</li>
        </ul>
        
        <p>
            Any component of h<sup>(0)</sup> that is not aligned with the eigenvector corresponding to the largest eigenvalue 
            will eventually be discarded. This means information from the past gets lost exponentially fast.
        </p>
        
        <h3>4.3 Recurrent vs. Feedforward Networks</h3>
        
        <p>
            This problem is specific to recurrent networks. In a deep feedforward network, we can use different weights at 
            each layer, allowing us to control the variance of activations throughout the network. But in an RNN, we reuse 
            the same weights at each time step, making it impossible to independently adjust the impact of different time 
            steps on the final output.
        </p>
        
        <h3>4.4 Historical Context</h3>
        
        <p>
            The vanishing and exploding gradient problem for RNNs was independently discovered by researchers in the early 
            1990s (Hochreiter, 1991; Bengio et al., 1993, 1994). Their key insight was that even when RNNs can theoretically 
            represent long-term dependencies, gradient-based optimization methods struggle to learn them.
        </p>
        
        <p>
            Specifically, Bengio et al. (1994) showed that in order for an RNN to store memories in a way that is robust to 
            small perturbations, it must enter a parameter region where gradients vanish. In these regions, the gradient of 
            a long-term interaction has exponentially smaller magnitude than the gradient of a short-term interaction.
        </p>
        
        <p>
            Their experiments demonstrated that as the span of dependencies increases, the probability of successfully 
            training a traditional RNN via stochastic gradient descent rapidly approaches zero, even for relatively short 
            sequences of only 10 or 20 steps.
        </p>
        
        <h3>4.5 Approaches to Address the Problem</h3>
        
        <p>
            Several approaches have been developed to address the challenge of learning long-term dependencies:
        </p>
        
        <ul>
            <li>
                <strong>Specialized Architectures:</strong> Long Short-Term Memory (LSTM) networks, Gated Recurrent Units (GRUs), 
                and other gated architectures are specifically designed to mitigate the vanishing gradient problem.
            </li>
            <li>
                <strong>Skip Connections:</strong> Creating direct paths between distant time steps can help gradients flow more easily.
            </li>
            <li>
                <strong>Gradient Clipping:</strong> To address exploding gradients, gradients can be clipped to a maximum value.
            </li>
            <li>
                <strong>Careful Initialization:</strong> Proper initialization of weights can help maintain gradient magnitude.
            </li>
            <li>
                <strong>Orthogonal Initialization:</strong> Initializing recurrent weight matrices to be orthogonal helps preserve 
                gradient norms during backpropagation.
            </li>
            <li>
                <strong>Attention Mechanisms:</strong> Allow models to focus on specific parts of the input sequence regardless 
                of their distance in time.
            </li>
        </ul>
        
        <p>
            Despite these advancements, the problem of learning long-term dependencies remains one of the central challenges 
            in deep learning and continues to be an active area of research.
        </p>
    </section>
    
    <footer>
        <p>This tutorial was created as a comprehensive guide to advanced recurrent neural network architectures.</p>
        <p>It covers encoder-decoder sequence-to-sequence architectures, deep recurrent networks, recursive neural networks, and the challenge of learning long-term dependencies in RNNs.</p>
    </footer>
</body>
</html> 