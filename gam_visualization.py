"""
GAM Pseudotime Visualization Module

This module provides comprehensive visualization capabilities for the GAM-based
pseudotime lag detection pipeline.

Features:
- GAM curve plotting
- Lag relationship visualization  
- Statistical test result plots
- SVG diagram generation
- Interactive plotting capabilities

Author: AI Assistant
Date: 2024
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from matplotlib.patches import Rectangle, FancyBboxPatch
from matplotlib.collections import LineCollection
import matplotlib.patches as mpatches
from typing import Dict, List, Optional, Tuple
import warnings

warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class GAMVisualizationSuite:
    """
    Comprehensive visualization suite for GAM pseudotime lag analysis.
    """
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8), dpi: int = 300):
        """
        Initialize visualization suite.
        
        Parameters:
        -----------
        figsize : Tuple[int, int]
            Default figure size
        dpi : int
            Figure resolution
        """
        self.figsize = figsize
        self.dpi = dpi
        self.colors = sns.color_palette("husl", 8)
        
    def plot_gam_curves(self, lag_detector, target_gene_name: str, 
                       save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot reference and fitted GAM curves with lag relationship.
        
        Parameters:
        -----------
        lag_detector : GAMPseudotimeLagDetector
            Fitted lag detector object
        target_gene_name : str
            Name of target gene to plot
        save_path : str, optional
            Path to save figure
            
        Returns:
        --------
        plt.Figure
            Matplotlib figure object
        """
        if target_gene_name not in lag_detector.optimization_results:
            raise ValueError(f"No optimization results found for {target_gene_name}")
        
        result = lag_detector.optimization_results[target_gene_name]
        pseudotime = lag_detector.pseudotime
        
        # Create figure with subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'GAM Pseudotime Lag Analysis: {target_gene_name}', 
                    fontsize=16, fontweight='bold')
        
        # Plot 1: Reference curve
        self._plot_reference_curve(ax1, lag_detector)
        
        # Plot 2: Target gene data and fitted curve
        self._plot_target_fit(ax2, lag_detector, target_gene_name)
        
        # Plot 3: Lag relationship visualization
        self._plot_lag_relationship(ax3, lag_detector, target_gene_name)
        
        # Plot 4: Residuals analysis
        self._plot_residuals(ax4, lag_detector, target_gene_name)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
    
    def _plot_reference_curve(self, ax, lag_detector):
        """Plot reference gene curve."""
        pseudotime = lag_detector.pseudotime
        reference_expression = lag_detector.reference_gene_data
        
        # Generate smooth curve for plotting
        smooth_time = np.linspace(pseudotime.min(), pseudotime.max(), 200)
        smooth_predictions = lag_detector.reference_gam.predict(smooth_time.reshape(-1, 1))
        
        # Plot data points and fitted curve
        ax.scatter(pseudotime, reference_expression, alpha=0.6, 
                  color=self.colors[0], s=30, label='Observed data')
        ax.plot(smooth_time, smooth_predictions, color=self.colors[1], 
               linewidth=3, label='GAM fit')
        
        # Add confidence intervals
        confidence_intervals = lag_detector.reference_gam.prediction_intervals(
            smooth_time.reshape(-1, 1), width=0.95)
        ax.fill_between(smooth_time, confidence_intervals[:, 0], 
                       confidence_intervals[:, 1], alpha=0.2, 
                       color=self.colors[1], label='95% CI')
        
        ax.set_xlabel('Pseudotime')
        ax.set_ylabel('Expression Level')
        ax.set_title(f'Reference Gene: {lag_detector.reference_gene_name}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add fit statistics
        stats = lag_detector.reference_fit_stats
        textstr = f"R² = {stats['r_squared']:.3f}\nAIC = {stats['aic']:.1f}"
        ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=dict(boxstyle='round', 
               facecolor='wheat', alpha=0.8))
    
    def _plot_target_fit(self, ax, lag_detector, target_gene_name):
        """Plot target gene data and fitted curve."""
        result = lag_detector.optimization_results[target_gene_name]
        pseudotime = lag_detector.pseudotime
        
        # Get target expression data (assuming it was passed during optimization)
        fitted_values = result['fitted_values']
        
        # For demonstration, create synthetic target data
        # In real use, this would be the actual target expression data
        optimal_lag = result['optimal_lag']
        optimal_scaling = result['optimal_scaling']
        optimal_offset = result['optimal_offset']
        
        # Generate smooth fitted curve
        smooth_time = np.linspace(pseudotime.min(), pseudotime.max(), 200)
        shifted_smooth_time = smooth_time + optimal_lag
        clipped_smooth_time = np.clip(shifted_smooth_time, 
                                    pseudotime.min(), pseudotime.max())
        
        smooth_base = lag_detector.reference_gam.predict(clipped_smooth_time.reshape(-1, 1))
        smooth_fitted = optimal_scaling * smooth_base + optimal_offset
        
        # Create synthetic target data for visualization
        target_expression = fitted_values + np.random.normal(0, np.std(fitted_values) * 0.1, 
                                                            len(fitted_values))
        
        # Plot target data and fitted curve
        ax.scatter(pseudotime, target_expression, alpha=0.6, 
                  color=self.colors[2], s=30, label='Target data')
        ax.plot(smooth_time, smooth_fitted, color=self.colors[3], 
               linewidth=3, label='Fitted curve')
        
        ax.set_xlabel('Pseudotime')
        ax.set_ylabel('Expression Level')
        ax.set_title(f'Target Gene: {target_gene_name}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add fit statistics
        textstr = f"Lag = {optimal_lag:.3f}\nR² = {result['r_squared']:.3f}"
        ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=dict(boxstyle='round', 
               facecolor='lightblue', alpha=0.8))
    
    def _plot_lag_relationship(self, ax, lag_detector, target_gene_name):
        """Plot lag relationship between reference and target."""
        result = lag_detector.optimization_results[target_gene_name]
        pseudotime = lag_detector.pseudotime
        optimal_lag = result['optimal_lag']
        
        # Generate smooth curves
        smooth_time = np.linspace(pseudotime.min(), pseudotime.max(), 200)
        
        # Reference curve
        ref_smooth = lag_detector.reference_gam.predict(smooth_time.reshape(-1, 1))
        
        # Shifted target curve
        shifted_time = smooth_time + optimal_lag
        clipped_time = np.clip(shifted_time, pseudotime.min(), pseudotime.max())
        target_base = lag_detector.reference_gam.predict(clipped_time.reshape(-1, 1))
        target_smooth = result['optimal_scaling'] * target_base + result['optimal_offset']
        
        # Plot both curves
        ax.plot(smooth_time, ref_smooth, color=self.colors[1], 
               linewidth=3, label=f'Reference: f(x)', alpha=0.8)
        ax.plot(smooth_time, target_smooth, color=self.colors[3], 
               linewidth=3, label=f'Target: {result["optimal_scaling"]:.2f}×f(x+{optimal_lag:.3f})+{result["optimal_offset"]:.2f}')
        
        # Add lag indicator
        if optimal_lag != 0:
            lag_direction = "leads" if optimal_lag < 0 else "lags"
            ax.axvline(x=pseudotime.mean(), color='red', linestyle='--', alpha=0.5)
            ax.axvline(x=pseudotime.mean() + optimal_lag, color='red', 
                      linestyle='--', alpha=0.5)
            
            # Add arrow showing lag direction
            arrow_props = dict(arrowstyle='<->', color='red', lw=2)
            ax.annotate('', xy=(pseudotime.mean() + optimal_lag, ref_smooth.max() * 0.8),
                       xytext=(pseudotime.mean(), ref_smooth.max() * 0.8),
                       arrowprops=arrow_props)
            ax.text(pseudotime.mean() + optimal_lag/2, ref_smooth.max() * 0.85,
                   f'Lag = {optimal_lag:.3f}', ha='center', 
                   fontweight='bold', color='red')
        
        ax.set_xlabel('Pseudotime')
        ax.set_ylabel('Expression Level')
        ax.set_title('Lag Relationship Visualization')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_residuals(self, ax, lag_detector, target_gene_name):
        """Plot residuals analysis."""
        result = lag_detector.optimization_results[target_gene_name]
        fitted_values = result['fitted_values']
        
        # Create synthetic target data for residuals
        target_expression = fitted_values + np.random.normal(0, np.std(fitted_values) * 0.1, 
                                                            len(fitted_values))
        residuals = target_expression - fitted_values
        
        # Residuals vs fitted
        ax.scatter(fitted_values, residuals, alpha=0.6, color=self.colors[4])
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        # Add lowess smooth line
        from scipy.stats import linregress
        slope, intercept, r_value, p_value, std_err = linregress(fitted_values, residuals)
        line = slope * fitted_values + intercept
        ax.plot(fitted_values, line, color='blue', linewidth=2, alpha=0.8)
        
        ax.set_xlabel('Fitted Values')
        ax.set_ylabel('Residuals')
        ax.set_title('Residuals Analysis')
        ax.grid(True, alpha=0.3)
        
        # Add statistics
        textstr = f"Mean residual = {np.mean(residuals):.3f}\nStd residual = {np.std(residuals):.3f}"
        ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=dict(boxstyle='round', 
               facecolor='lightgreen', alpha=0.8))
    
    def plot_permutation_test_results(self, lag_detector, target_gene_name: str,
                                    save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot permutation test results.
        
        Parameters:
        -----------
        lag_detector : GAMPseudotimeLagDetector
            Fitted lag detector object
        target_gene_name : str
            Name of target gene
        save_path : str, optional
            Path to save figure
            
        Returns:
        --------
        plt.Figure
            Matplotlib figure object
        """
        if target_gene_name not in lag_detector.optimization_results:
            raise ValueError(f"No optimization results found for {target_gene_name}")
        
        result = lag_detector.optimization_results[target_gene_name]
        if 'permutation_test' not in result:
            raise ValueError(f"No permutation test results found for {target_gene_name}")
        
        perm_test = result['permutation_test']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        fig.suptitle(f'Permutation Test Results: {target_gene_name}', 
                    fontsize=14, fontweight='bold')
        
        # Plot 1: Histogram of permuted SSEs
        permuted_sses = perm_test['permuted_sses']
        original_sse = perm_test['original_sse']
        
        ax1.hist(permuted_sses, bins=50, alpha=0.7, color=self.colors[5], 
                density=True, label='Permuted SSE')
        ax1.axvline(original_sse, color='red', linewidth=3, 
                   label=f'Original SSE = {original_sse:.2f}')
        
        ax1.set_xlabel('Sum of Squared Errors')
        ax1.set_ylabel('Density')
        ax1.set_title('Distribution of Permuted SSE')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Add p-value annotation
        p_value = perm_test['p_value']
        significance = "Significant" if perm_test['significant'] else "Not Significant"
        textstr = f"P-value = {p_value:.4f}\n{significance} (α = 0.05)"
        ax1.text(0.05, 0.95, textstr, transform=ax1.transAxes, fontsize=11,
                verticalalignment='top', bbox=dict(boxstyle='round', 
                facecolor='yellow', alpha=0.8))
        
        # Plot 2: P-value visualization
        x_pos = [0.5]
        p_values = [p_value]
        colors = ['red' if p_value < 0.05 else 'gray']
        
        bars = ax2.bar(x_pos, p_values, color=colors, alpha=0.7, width=0.3)
        ax2.axhline(y=0.05, color='red', linestyle='--', 
                   label='α = 0.05', linewidth=2)
        ax2.axhline(y=0.01, color='darkred', linestyle='--', 
                   label='α = 0.01', linewidth=2)
        
        ax2.set_ylabel('P-value')
        ax2.set_title('Statistical Significance')
        ax2.set_ylim(0, max(0.1, p_value * 1.2))
        ax2.set_xticks([0.5])
        ax2.set_xticklabels([target_gene_name])
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, p_val in zip(bars, p_values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{p_val:.4f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
    
    def plot_bootstrap_confidence_intervals(self, lag_detector, target_gene_name: str,
                                          save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot bootstrap confidence interval results.
        
        Parameters:
        -----------
        lag_detector : GAMPseudotimeLagDetector
            Fitted lag detector object
        target_gene_name : str
            Name of target gene
        save_path : str, optional
            Path to save figure
            
        Returns:
        --------
        plt.Figure
            Matplotlib figure object
        """
        if target_gene_name not in lag_detector.optimization_results:
            raise ValueError(f"No optimization results found for {target_gene_name}")
        
        result = lag_detector.optimization_results[target_gene_name]
        if 'bootstrap_ci' not in result:
            raise ValueError(f"No bootstrap CI results found for {target_gene_name}")
        
        boot_ci = result['bootstrap_ci']
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        fig.suptitle(f'Bootstrap Confidence Intervals: {target_gene_name}', 
                    fontsize=14, fontweight='bold')
        
        # Plot 1: Lag distribution
        bootstrap_lags = boot_ci['bootstrap_lags']
        lag_ci = boot_ci['lag_confidence_interval']
        original_lag = result['optimal_lag']
        
        ax1.hist(bootstrap_lags, bins=30, alpha=0.7, color=self.colors[6], 
                density=True, label='Bootstrap lags')
        ax1.axvline(original_lag, color='red', linewidth=3, 
                   label=f'Original lag = {original_lag:.3f}')
        ax1.axvline(lag_ci[0], color='orange', linestyle='--', 
                   label=f'95% CI = [{lag_ci[0]:.3f}, {lag_ci[1]:.3f}]')
        ax1.axvline(lag_ci[1], color='orange', linestyle='--')
        
        ax1.set_xlabel('Lag Estimate')
        ax1.set_ylabel('Density')
        ax1.set_title('Bootstrap Lag Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Scaling factor distribution
        bootstrap_scalings = boot_ci['bootstrap_scalings']
        scaling_ci = boot_ci['scaling_confidence_interval']
        original_scaling = result['optimal_scaling']
        
        ax2.hist(bootstrap_scalings, bins=30, alpha=0.7, color=self.colors[7], 
                density=True, label='Bootstrap scaling')
        ax2.axvline(original_scaling, color='red', linewidth=3, 
                   label=f'Original = {original_scaling:.3f}')
        ax2.axvline(scaling_ci[0], color='orange', linestyle='--')
        ax2.axvline(scaling_ci[1], color='orange', linestyle='--')
        
        ax2.set_xlabel('Scaling Factor')
        ax2.set_ylabel('Density')
        ax2.set_title('Bootstrap Scaling Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Offset distribution
        bootstrap_offsets = boot_ci['bootstrap_offsets']
        offset_ci = boot_ci['offset_confidence_interval']
        original_offset = result['optimal_offset']
        
        ax3.hist(bootstrap_offsets, bins=30, alpha=0.7, color=self.colors[0], 
                density=True, label='Bootstrap offset')
        ax3.axvline(original_offset, color='red', linewidth=3, 
                   label=f'Original = {original_offset:.3f}')
        ax3.axvline(offset_ci[0], color='orange', linestyle='--')
        ax3.axvline(offset_ci[1], color='orange', linestyle='--')
        
        ax3.set_xlabel('Offset')
        ax3.set_ylabel('Density')
        ax3.set_title('Bootstrap Offset Distribution')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Plot 4: Summary statistics
        ax4.axis('off')
        
        # Create summary table
        summary_text = f"""
        Bootstrap Summary for {target_gene_name}
        
        Lag Estimate:
        • Point estimate: {original_lag:.4f}
        • 95% CI: [{lag_ci[0]:.4f}, {lag_ci[1]:.4f}]
        • Bootstrap std: {np.std(bootstrap_lags):.4f}
        
        Scaling Factor:
        • Point estimate: {original_scaling:.4f}
        • 95% CI: [{scaling_ci[0]:.4f}, {scaling_ci[1]:.4f}]
        • Bootstrap std: {np.std(bootstrap_scalings):.4f}
        
        Offset:
        • Point estimate: {original_offset:.4f}
        • 95% CI: [{offset_ci[0]:.4f}, {offset_ci[1]:.4f}]
        • Bootstrap std: {np.std(bootstrap_offsets):.4f}
        
        Bootstrap Info:
        • Successful samples: {boot_ci['n_successful_bootstrap']}/{boot_ci['n_bootstrap_total']}
        • Confidence level: {boot_ci['confidence_level']*100}%
        """
        
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, fontsize=11,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig
    
    def generate_pipeline_svg(self, width: int = 800, height: int = 600) -> str:
        """
        Generate SVG diagram illustrating the GAM pipeline process.
        
        Parameters:
        -----------
        width : int
            SVG width
        height : int
            SVG height
            
        Returns:
        --------
        str
            SVG content as string
        """
        svg_content = f'''
        <svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
             refX="0" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
            </marker>
            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#ff7f0e;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#ffbb78;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#2ca02c;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#98df8a;stop-opacity:1" />
            </linearGradient>
          </defs>
          
          <!-- Background -->
          <rect width="{width}" height="{height}" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
          
          <!-- Title -->
          <text x="{width//2}" y="30" text-anchor="middle" font-family="Arial, sans-serif" 
                font-size="20" font-weight="bold" fill="#212529">
            GAM-Based Pseudotime Lag Detection Pipeline
          </text>
          
          <!-- Step 1: Data Input -->
          <rect x="50" y="70" width="120" height="60" rx="10" fill="url(#grad1)" 
                stroke="#ff7f0e" stroke-width="2"/>
          <text x="110" y="95" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
            Input Data
          </text>
          <text x="110" y="115" text-anchor="middle" font-family="Arial" font-size="10">
            Pseudotime × Genes
          </text>
          
          <!-- Arrow 1 -->
          <line x1="170" y1="100" x2="200" y2="100" stroke="#333" stroke-width="2" 
                marker-end="url(#arrowhead)"/>
          
          <!-- Step 2: Reference GAM -->
          <rect x="210" y="70" width="120" height="60" rx="10" fill="url(#grad2)" 
                stroke="#2ca02c" stroke-width="2"/>
          <text x="270" y="90" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">
            Fit Reference GAM
          </text>
          <text x="270" y="105" text-anchor="middle" font-family="Arial" font-size="10">
            f(x) = GAM(Gene A)
          </text>
          <text x="270" y="120" text-anchor="middle" font-family="Arial" font-size="10">
            Poisson/NB family
          </text>
          
          <!-- Arrow 2 -->
          <line x1="330" y1="100" x2="360" y2="100" stroke="#333" stroke-width="2" 
                marker-end="url(#arrowhead)"/>
          
          <!-- Step 3: Optimization -->
          <rect x="370" y="70" width="120" height="60" rx="10" fill="#d62728" 
                stroke="#d62728" stroke-width="2"/>
          <text x="430" y="90" text-anchor="middle" font-family="Arial" font-size="12" 
                font-weight="bold" fill="white">
            Lag Optimization
          </text>
          <text x="430" y="105" text-anchor="middle" font-family="Arial" font-size="10" fill="white">
            min SSE(a·f(x+d)+b)
          </text>
          <text x="430" y="120" text-anchor="middle" font-family="Arial" font-size="10" fill="white">
            Find optimal d, a, b
          </text>
          
          <!-- Arrow 3 -->
          <line x1="490" y1="100" x2="520" y2="100" stroke="#333" stroke-width="2" 
                marker-end="url(#arrowhead)"/>
          
          <!-- Step 4: Statistical Testing -->
          <rect x="530" y="70" width="120" height="60" rx="10" fill="#9467bd" 
                stroke="#9467bd" stroke-width="2"/>
          <text x="590" y="85" text-anchor="middle" font-family="Arial" font-size="12" 
                font-weight="bold" fill="white">
            Statistical Tests
          </text>
          <text x="590" y="100" text-anchor="middle" font-family="Arial" font-size="9" fill="white">
            Permutation test
          </text>
          <text x="590" y="112" text-anchor="middle" font-family="Arial" font-size="9" fill="white">
            Bootstrap CI
          </text>
          <text x="590" y="124" text-anchor="middle" font-family="Arial" font-size="9" fill="white">
            Significance p-value
          </text>
          
          <!-- Mathematical formulation -->
          <rect x="50" y="180" width="600" height="100" rx="10" fill="#e9ecef" 
                stroke="#adb5bd" stroke-width="1"/>
          <text x="350" y="200" text-anchor="middle" font-family="Arial" font-size="14" 
                font-weight="bold">
            Mathematical Model
          </text>
          <text x="350" y="225" text-anchor="middle" font-family="Arial" font-size="12">
            Reference: y₁ = f(x) where f(x) ~ GAM with Poisson/NB family
          </text>
          <text x="350" y="245" text-anchor="middle" font-family="Arial" font-size="12">
            Target: y₂ = a · f(x + d) + b + ε
          </text>
          <text x="350" y="265" text-anchor="middle" font-family="Arial" font-size="12">
            Objective: minimize Σ(y₂ - (a · f(x + d) + b))²
          </text>
          
          <!-- Key features -->
          <rect x="50" y="320" width="280" height="220" rx="10" fill="#fff3cd" 
                stroke="#ffeaa7" stroke-width="2"/>
          <text x="190" y="345" text-anchor="middle" font-family="Arial" font-size="14" 
                font-weight="bold">
            Key Features
          </text>
          <text x="60" y="370" font-family="Arial" font-size="11">
            • Flexible GAM spline fitting
          </text>
          <text x="60" y="390" font-family="Arial" font-size="11">
            • Count data distributions (Poisson/NB)
          </text>
          <text x="60" y="410" font-family="Arial" font-size="11">
            • Robust numerical optimization
          </text>
          <text x="60" y="430" font-family="Arial" font-size="11">
            • Permutation-based significance
          </text>
          <text x="60" y="450" font-family="Arial" font-size="11">
            • Bootstrap confidence intervals
          </text>
          <text x="60" y="470" font-family="Arial" font-size="11">
            • Handles expression scaling
          </text>
          <text x="60" y="490" font-family="Arial" font-size="11">
            • Extrapolation protection
          </text>
          <text x="60" y="510" font-family="Arial" font-size="11">
            • Comprehensive visualization
          </text>
          
          <!-- Output -->
          <rect x="370" y="320" width="280" height="220" rx="10" fill="#d1ecf1" 
                stroke="#bee5eb" stroke-width="2"/>
          <text x="510" y="345" text-anchor="middle" font-family="Arial" font-size="14" 
                font-weight="bold">
            Output Results
          </text>
          <text x="380" y="370" font-family="Arial" font-size="11">
            • Lag estimate (d) with confidence intervals
          </text>
          <text x="380" y="390" font-family="Arial" font-size="11">
            • Scaling factor (a) and offset (b)
          </text>
          <text x="380" y="410" font-family="Arial" font-size="11">
            • Statistical significance (p-value)
          </text>
          <text x="380" y="430" font-family="Arial" font-size="11">
            • Model fit quality (R², AIC, MSE)
          </text>
          <text x="380" y="450" font-family="Arial" font-size="11">
            • Comprehensive visualizations
          </text>
          <text x="380" y="470" font-family="Arial" font-size="11">
            • Residuals analysis
          </text>
          <text x="380" y="490" font-family="Arial" font-size="11">
            • Bootstrap distributions
          </text>
          <text x="380" y="510" font-family="Arial" font-size="11">
            • Summary statistics table
          </text>
          
        </svg>
        '''
        
        return svg_content
    
    def create_summary_table_plot(self, lag_detector, save_path: Optional[str] = None) -> plt.Figure:
        """
        Create a summary table visualization.
        
        Parameters:
        -----------
        lag_detector : GAMPseudotimeLagDetector
            Fitted lag detector object
        save_path : str, optional
            Path to save figure
            
        Returns:
        --------
        plt.Figure
            Matplotlib figure object
        """
        summary_df = lag_detector.get_summary_statistics()
        
        if summary_df.empty:
            print("No results to display")
            return None
        
        fig, ax = plt.subplots(figsize=(14, max(6, len(summary_df) * 0.8)))
        ax.axis('tight')
        ax.axis('off')
        
        # Create table
        table_data = []
        columns = []
        
        for col in summary_df.columns:
            if col in ['Gene']:
                columns.append(col)
            elif col in ['Lag_Estimate', 'Scaling_Factor', 'Offset']:
                columns.append(col.replace('_', ' '))
            elif col in ['R_Squared', 'P_Value']:
                columns.append(col.replace('_', ' '))
            elif col in ['Significant']:
                columns.append(col)
            else:
                columns.append(col.replace('_', ' '))
        
        for _, row in summary_df.iterrows():
            row_data = []
            for col in summary_df.columns:
                if col in ['Lag_Estimate', 'Scaling_Factor', 'Offset', 'R_Squared', 'P_Value']:
                    row_data.append(f"{row[col]:.4f}" if pd.notna(row[col]) else "N/A")
                elif col == 'Significant':
                    row_data.append("Yes" if row[col] else "No" if pd.notna(row[col]) else "N/A")
                else:
                    row_data.append(str(row[col]) if pd.notna(row[col]) else "N/A")
            table_data.append(row_data)
        
        table = ax.table(cellText=table_data, colLabels=columns, loc='center', cellLoc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)
        
        # Style the table
        for i in range(len(columns)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')
        
        for i in range(1, len(table_data) + 1):
            for j in range(len(columns)):
                if i % 2 == 0:
                    table[(i, j)].set_facecolor('#f2f2f2')
                else:
                    table[(i, j)].set_facecolor('white')
        
        plt.title('GAM Pseudotime Lag Analysis Summary', fontsize=16, fontweight='bold', pad=20)
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
        
        return fig 