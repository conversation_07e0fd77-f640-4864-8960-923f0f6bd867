Appl. Statist. (2005)
54, Part 3, pp. 507–554

Generalized additive models for location, scale
and shape
<PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON>
London Metropolitan University, UK
[Read before The Royal Statistical Society on Tuesday, November 23rd, 2004, the President ,
Professor <PERSON><PERSON> <PERSON><PERSON>, in the Chair ]
Summary. A general class of statistical models for a univariate response variable is presented
which we call the generalized additive model for location, scale and shape (GAMLSS). The
model assumes independent observations of the response variable y given the parameters, the
explanatory variables and the values of the random effects. The distribution for the response
variable in the GAMLSS can be selected from a very general family of distributions including
highly skew or kurtotic continuous and discrete distributions. The systematic part of the model
is expanded to allow modelling not only of the mean (or location) but also of the other parameters of the distribution of y, as parametric and/or additive nonparametric (smooth) functions
of explanatory variables and/or random-effects terms. Maximum (penalized) likelihood estimation is used to fit the (non)parametric models. A Newton–<PERSON> or Fisher scoring algorithm is
used to maximize the (penalized) likelihood. The additive terms in the model are fitted by using a
backfitting algorithm. Censored data are easily incorporated into the framework. Five data sets
from different fields of application are analysed to emphasize the generality of the GAMLSS
class of models.
Keywords: Beta–binomial distribution; Box–Cox transformation; Centile estimation; Cubic
smoothing splines; Generalized linear mixed model; LMS method; Negative binomial
distribution; Non-normality; Nonparametric models; Overdispersion; Penalized likelihood;
Random effects; Skewness and kurtosis

1.

Introduction

The quantity of data collected and requiring statistical analysis has been increasing rapidly over
recent years, allowing the ﬁtting of more complex and potentially more realistic models. In this
paper we develop a very general regression-type model in which both the systematic and the
random parts of the model are highly ﬂexible and where the ﬁtting algorithm is sufﬁciently fast
to allow the rapid exploration of very large and complex data sets.
Within the framework of univariate regression modelling techniques the generalized linear
model (GLM) and generalized additive model (GAM) hold a prominent place (Nelder and
Wedderburn (1972) and Hastie and Tibshirani (1990) respectively). Both models assume an
exponential family distribution for the response variable y in which the mean µ of y is modelled
as a function of explanatory variables and the variance of y, given by V.y/ = φ v.µ/, depends
on a constant dispersion parameter φ and on the mean µ, through the variance function v.µ/.
Furthermore, for an exponential family distribution both the skewness and the kurtosis of y are,
in general, functions of µ and φ. Hence, in the GLM and GAM models, the variance, skewness

Address for correspondence: R. A. Rigby, Statistics, OR and Mathematics (STORM) Research Centre, London
Metropolitan University, 166–220 Holloway Road, London, N7 8DB, UK.
E-mail: <EMAIL>
©

2005 Royal Statistical Society

0035–9254/05/54507

508

R. A. Rigby and D. M. Stasinopoulos

and kurtosis are not modelled explicitly in terms of the explanatory variables but implicitly
through their dependence on µ.
Another important class of models, the linear mixed (random-effects) models, which provide
a very broad framework for modelling dependent data particularly associated with spatial, hierarchical and longitudinal sampling schemes, assume normality for the conditional distribution
of y given the random effects and therefore cannot model skewness and kurtosis explicitly.
The generalized linear mixed model (GLMM) combines the GLM and linear mixed model,
by introducing a (usually normal) random-effects term in the linear predictor for the mean of
a GLM. Bayesian procedures to ﬁt GLMMs by using the EM algorithm and Markov chain
Monte Carlo methods were described by McCulloch (1997) and Zeger and Karim (1991). Lin
and Zhang (1999) gave an example of a generalized additive mixed model (GAMM). Fahrmeir
and Lang (2001) discussed GAMM modelling using Bayesian inference. Fahrmeir and Tutz
(2001) discussed alternative estimation procedures for the GLMM and GAMM. The GLMM
and GAMM, although more ﬂexible than the GLM and GAM, also assume an exponential
family conditional distribution for y and rarely allow the modelling of parameters other than the
mean (or location) of the distribution of the response variable as functions of the explanatory
variables. Their ﬁtting often depends on Markov chain Monte Carlo or integrated (marginal distribution) likelihoods (e.g. Gaussian quadrature), making them highly computationally intensive and time consuming, at least at present, for large data sets where the model selection
requires the investigation of many alternative models. Various approximate procedures for ﬁtting a GLMM have been proposed (Breslow and Clayton, 1993; Breslow and Lin, 1995; Lee and
Nelder, 1996, 2001a, b). An alternative approach is to use nonparametric maximum likelihood
based on ﬁnite mixtures; Aitkin (1999).
In this paper we develop a general class of univariate regression models which we call the
generalized additive model for location, scale and shape (GAMLSS), where the exponential
family assumption is relaxed and replaced by a very general distribution family. Within this new
framework, the systematic part of the model is expanded to allow not only the mean (or location) but all the parameters of the conditional distribution of y to be modelled as parametric
and/or additive nonparametric (smooth) functions of explanatory variables and/or randomeffects terms. The model ﬁtting of a GAMLSS is achieved by either of two different algorithmic
procedures. The ﬁrst algorithm (RS) is based on the algorithm that was used for the ﬁtting of the
mean and dispersion additive models of Rigby and Stasinopoulos (1996a), whereas the second
(CG) is based on the Cole and Green (1992) algorithm.
Section 2 formally introduces the GAMLSS. Parametric terms in the linear predictors are considered in Section 3.1, and several speciﬁc forms of additive terms which can be incorporated
in the predictors are considered in Section 3.2. These include nonparametric smooth function
terms, using cubic splines or smoothness priors, random-walk terms and many random-effects
terms (including terms for simple overdispersion, longitudinal random effects, random-coefﬁcient models, multilevel hierarchical models and crossed and spatial random effects). A major
advantage of the GAMLSS framework is that any combinations of the above terms can be
incorporated easily in the model. This is discussed in Section 3.3.
Section 4 describes speciﬁc families of distributions for the dependent variable which have
been implemented in the GAMLSS. Incorporating censored data and centile estimation are
also discussed there. The RS and CG algorithms (based on the Newton–Raphson or Fisher
scoring algorithm) for maximizing the (penalized) likelihood of the data under a GAMLSS are
discussed in Section 5. The details and justiﬁcation of the algorithms are given in Appendices B
and C respectively. The inferential framework for the GAMLSS is considered in Appendix A,
where alternative inferential approaches are considered. Model selection, inference and residual

Generalized Additive Models

509

diagnostics are considered in Section 6. Section 7 gives ﬁve practical examples. Section 8 concludes the paper.
2.

The generalized additive model for location, scale and shape

2.1. Definition
The p parameters θT = .θ1 , θ2 , . . . , θp / of a population probability (density) function f.y|θ/ are
modelled here by using additive models. Speciﬁcally the model assumes that, for i = 1, 2, . . . , n,
observations yi are independent conditional on θi , with probability (density) function f.yi |θi /,
where θiT = .θi1 , θi2 , . . . , θip / is a vector of p parameters related to explanatory variables and
random effects. (If covariate values are stochastic or observations yi depend on their past values
then f.yi |θi / is understood to be conditional on these values.)
Let yT = .y1 , y2 , . . . , yn / be the vector of the response variable observations. Also, for k =
1, 2, . . . , p, let gk .·/ be a known monotonic link function relating θk to explanatory variables
and random effects through an additive model given by
gk .θk / = ηk = Xk βk +

Jk

j=1

Zjk γ jk

.1/

T
where θk and ηk are vectors of length n, e.g. θT
k = .θ1k , θ2k , . . . , θnk /, β k = .β1k , β2k , . . . , βJk k / is

a parameter vector of length Jk , Xk is a known design matrix of order n × Jk , Zjk is a ﬁxed
known n × qjk design matrix and γ jk is a qjk -dimensional random variable. We call model (1)
the GAMLSS.
The vectors γ jk for j = 1, 2, . . . , Jk could be combined into a single vector γ k with a single
design matrix Zk ; however, formulation (1) is preferred here as it is suited to the backﬁtting algorithm (see Appendix B) and allows combinations of different types of additive random-effects
terms to be incorporated easily in the model (see Section 3.3).
If, for k = 1, 2, . . . , p, Jk = 0 then model (1) reduces to a fully parametric model given by

gk .θk / = ηk = Xk βk :

.2/

If Zjk = In , where In is an n × n identity matrix, and γ jk = hjk = hjk .xjk / for all combinations
of j and k in model (1), this gives
gk .θk / = ηk = Xk βk +

Jk


hjk .xjk /

.3/

j=1

where xjk for j = 1, 2, . . . , Jk and k = 1, 2, . . . , p are vectors of length n. The function hjk is an
unknown function of the explanatory variable Xjk and hjk = hjk .xjk / is the vector which evaluates the function hjk at xjk . The explanatory vectors xjk are assumed to be known. We call the
model in equation (3) the semiparametric GAMLSS. Model (3) is an important special case of
model (1). If Zjk = In and γ jk = hjk = hjk .xjk / for speciﬁc combinations of j and k in model (1),
then the resulting model contains parametric, nonparametric and random-effects terms.
The ﬁrst two population parameters θ1 and θ2 in model (1) are usually characterized as location and scale parameters, denoted here by µ and σ, whereas the remaining parameter(s), if
any, are characterized as shape parameters, although the model may be applied more generally
to the parameters of any population distribution.
For many families of population distributions a maximum of two shape parameters ν .= θ3 /
and τ .= θ4 / sufﬁce, giving the model

510

R. A. Rigby and D. M. Stasinopoulos



g1 .µ/ = η1 = X1 β1 +
Zj1 γ j1 , 



j=1




J

2



Zj2 γ j2 , 
g2 .σ/ = η2 = X2 β2 +


j=1
J1





Zj3 γ j3 , 
g3 .ν/ = η3 = X3 β3 +



j=1




J
4



Zj4 γ j4 : 
g4 .τ / = η4 = X4 β4 +

J3


.4/

j=1

The GAMLSS model (1) is more general than the GLM, GAM, GLMM or GAMM in
that the distribution of the dependent variable is not limited to the exponential family and all
parameters (not just the mean) are modelled in terms of both ﬁxed and random effects.
2.2. Model estimation
Crucial to the way that additive components are ﬁtted within the GAMLSS framework is the
backﬁtting algorithm and the fact that quadratic penalties in the likelihood result from assuming a normally distributed random effect in the linear predictor. The resulting estimation uses
shrinking (smoothing) matrices within a backﬁtting algorithm, as shown below.
Assume in model (1) that the γ jk have independent (prior) normal distributions with γ jk ∼
−
−
Nqjk .0, Gjk
/, where Gjk
is the (generalized) inverse of a qjk × qjk symmetric matrix Gjk =
Gjk .λjk /, which may depend on a vector of hyperparameters λjk , and where if Gjk is singular then
γ jk is understood to have an improper prior density function proportional to exp.− 21 γ T
jk Gjk γ jk /.
Subsequently in the paper we refer to Gjk rather than to Gjk .λjk / for simplicity of notation,
although the dependence of Gjk on hyperparameters λjk remains throughout.
The assumption of independence between different random-effects vectors γ jk is essential
within the GAMLSS framework. However, if, for a particular k, two or more random-effect
vectors are not independent, they can be combined into a single random-effect vector and
their corresponding design matrices Zjk into a single design matrix, to satisfy the condition of
independence.
In Appendix A.1 it is shown, by using empirical Bayesian arguments, that posterior mode
estimation (or maximum a posteriori (MAP) estimation; see Berger (1985)) for the parameter
vectors βk and the random-effect terms γ jk (for ﬁxed values of the smoothing or hyperparameters λjk ), for j = 1, 2, . . . , Jk and k = 1, 2, . . . , p, is equivalent to penalized likelihood estimation.
Hence for ﬁxed λjk s the βk s and the γ jk s are estimated within the GAMLSS framework by
maximizing a penalized likelihood function lp given by
lp = l −

p 
Jk
1 
γ T Gjk γ jk
2 k=1 j=1 jk

.5/

where l = Σni=1 log{f.yi |θi /} is the log-likelihood function of the data given θi for i = 1, 2, . . . , n.
This is equivalent to maximizing the extended or hierarchical likelihood deﬁned by
lh = lp +

p 
Jk
1 
{log |Gjk | − qjk log.2π/}
2 k=1 j=1

(see Pawitan (2001), page 429, and Lee and Nelder (1996)).

Generalized Additive Models

511

It is shown in Appendix C that maximizing lp is achieved by the CG algorithm, which is
described in Appendix B. Appendix C shows that the maximization of lp leads to the shrinking
(smoothing) matrix Sjk , applied to partial residuals εjk to update the estimate of the additive
predictor Zjk γ jk within a backﬁtting algorithm, given by
−1 T
Sjk = Zjk .ZT
jk Wkk Zjk + Gjk / Zjk Wkk

.6/

for j = 1, 2, . . . , Jk and k = 1, 2, . . . , p, where Wkk is a diagonal matrix of iterative weights. Different forms of Zjk and Gjk correspond to different types of additive terms in the linear predictor
ηk for k = 1, 2, . . . , p. For random-effects terms Gjk is often a simple and/or low order matrix
whereas for a cubic smoothing spline term γ jk = hjk , Zjk = In and Gjk = λjk Kjk where Kjk is a
structured matrix. Either case allows easy updating of Zjk γ jk .
The hyperparameters λ can be ﬁxed or estimated. In Appendix A.2 we propose four alternative methods of estimation of λ which avoid integrating out the random effects.
2.3. Comparison of generalized additive models for location, scale and shape and
hierarchical generalized linear models
Lee and Nelder (1996, 2001a) developed hierarchical generalized linear models. In the notation
of the GAMLSS, they use, in general, extended quasi-likelihood to approximate the conditional distribution of y given θ = .µ, φ/, where µ and φ are mean and scale parameters respectively, and any conjugate distribution for the random effects γ (parameterized by λ). They
model predictors for µ, φ and λ in terms of explanatory variables, and the predictor for µ also
includes random-effects terms. Lee and Nelder (1996, 2001a) assumed independent random
effects, whereas Lee and Nelder (2001b) relaxed this assumption to allow correlated random
effects.
However, extended quasi-likelihood does not provide a proper distribution which integrates
or sums to 1 (and the integral or sum cannot be obtained explicitly, varies between cases and
depends on the parameters of the model). In large samples this has been found to lead to serious
inaccuracies in the ﬁtted global deviance, even for the gamma distribution (see Stasinopoulos
et al. (2000)), resulting potentially in a misleading comparison with a proper distribution. It is
also quite restrictive in the shape of distributions that are available for y given θ, particularly
for continuous distributions where it is unsuitable for negatively skew data, or for platykurtic
data or for leptokurtic data unless positively skewed. In addition, hierarchical generalized linear
models allow neither explanatory variables nor random effects in the predictors for the shape
parameters of f.y|θ/.
3.

The linear predictor

3.1. Parametric terms
In the GAMLSS (1) the linear predictors ηk , for k = 1, 2, . . . , p, comprise a parametric component Xk βk and additive components Zjk γ jk , for j = 1, . . . , Jk . The parametric component can
include linear and interaction terms for explanatory variables and factors, polynomials, fractional polynomials (Royston and Altman, 1994) and piecewise polynomials (with ﬁxed knots)
for variables (Smith, 1979; Stasinopoulos and Rigby, 1992).
Non-linear parameters can be incorporated into the GAMLSS (1) and ﬁtted by either of two
methods:
(a) the proﬁle or
(b) the derivative method.

512

R. A. Rigby and D. M. Stasinopoulos

In the proﬁle ﬁtting method, estimation of non-linear parameters is achieved by maximizing
their proﬁle likelihood. An example of the proﬁle method is given in Section 7.1 where the
age explanatory variable is transformed to x = ageξ where ξ is a non-linear parameter. In the
derivative ﬁtting method, the derivatives of a predictor ηk with respect to non-linear parameters
are included in the design matrix Xk in the ﬁtting algorithm; see, for example, Benjamin et al.
(2003). Lindsey (http://alpha.luc.ac.be/jlindsey/) has also considered modelling
parameters of a distribution as non-linear functions of explanatory variables.
3.2. Additive terms
The additive components Zjk γ jk in model (1) can model a variety of terms such as smoothing
and random-effect terms as well as terms that are useful for time series analysis (e.g. random
walks). Different additive terms that can be included in the GAMLSS will be discussed below.
For simplicity of exposition we shall drop the subscripts j and k in the vectors and matrices,
where appropriate.
3.2.1. Cubic smoothing splines terms
With cubic smoothing splines terms we assume in model (3) that the functions h.t/ are arbitrary twice continuously differentiable functions and
 ∞ we maximize a penalized log-likelihood,
given by l subject to penalty terms of the form λ −∞ h .t/2 dt. Following Reinsch (1967), the
maximizing functions h.t/ are all natural cubic splines and hence can be expressed as linear
combinations of their natural cubic spline basis functions Bi .t/ for i = 1, 2, . . . , n (de Boor,
1978; Schumaker, 1993), i.e. h.t/ = Σni=1 δi Bi .t/. Let h = h.x/ be the vector of evaluations of the
function h.t/ at the values x of the explanatory variable X (which is assumed to be distinct for
simplicity of exposition). Let N be an n × n non-singular matrix containing as its columns the
n-vectors of evaluations of functions Bi .t/, for i = 1, 2, . . . , n, at x. Then h can be expressed by
using coefﬁcient vector δ as a linear combination of the columns of N by h = Nδ. Let Ω be
the n × n matrix of inner products of the second derivatives of the natural cubic spline basis
functions, with .r, s/th entry given by

Ωrs = Br .t/ Bs .t/ dt:
The penalty is then given by the quadratic form
 ∞
Q.h/ = λ
h .t/2 dt = λδ T Ωδ = λhT N−T ΩN−1 h = λhT Kh,
−∞

K = N−T ΩN−1

where
is a known penalty matrix that depends only on the values of the explanatory vector x (Hastie and Tibshirani (1990), chapter 2). The precise form of the matrix K can
be found in Green and Silverman (1994), section 2.1.2.
The model can be formulated as a random-effects GAMLSS (1) by letting γ = h, Z = In ,
K = N−T ΩN−1 and G = λK, so that h ∼ Nn .0, λ−1 K− /, a partially improper prior (Silverman,
1985). This amounts to assuming complete prior uncertainty about the constant and linear
functions and decreasing uncertainty about higher order functions; see Verbyla et al. (1999).
3.2.2. Parameter-driven time series terms and smoothness priors
First assume that an explanatory variable X has equally spaced observations xi , i = 1, . . . , n,
sorted into the ordered sequence x.1/ < . . . < x.i/ < . . . < x.n/ deﬁning an equidistant grid on the

Generalized Additive Models

513

x-axis. Typically, for a parameter-driven time series term, X corresponds to time units as days,
weeks, months or years. First- and second-order random walks, denoted as rw(1) and rw(2), are
deﬁned respectively by h[x.i/ ] = h[x.i−1/ ] + "i and h[x.i/ ] = 2 h[x.i−1/ ] − h[x.i−2/ ] + "i with independent errors "i ∼ N.0, λ−1 / for i > 1 and i > 2 respectively, and with diffuse uniform priors for
h[x.1/ ] for rw(1) and, in addition, for h[x.2/ ] for rw(2). Let h = h.x/; then D1 h ∼ Nn−1 .0, λ−1 I/
and D2 h ∼ Nn−2 .0, λ−1 I/, where D1 and D2 are .n − 1/ × n and .n − 2/ × n matrices giving
ﬁrst and second differences respectively. The above terms can be included in the GAMLSS
framework (1) by letting Z = In and G = λK so that γ = h ∼ N.0, λ−1 K− /, where K has a strucT
tured form given by K = DT
1 D1 or K = D2 D2 for rw(1) or rw(2) respectively; see Fahrmeir and
T
Tutz (2001), pages 223–225 and 363–364. (The resulting quadratic penalty
 ∞λh Kh2for rw.2/ is
a discretized version of the corresponding cubic spline penalty term λ −∞ h .t/ dt.) Hence
many of the state space models of Harvey (1989) can be incorporated in the GAMLSS framework.
The more general case of a non-equally spaced variable X requires modiﬁcations to K
(Fahrmeir and Lang, 2001), where X is any continuous variable and the prior distribution
for h is called a smoothness prior.
3.2.3. Penalized splines terms
Smoothers in which the number of basis functions is less than the number of observations but in
which their regression coefﬁcients are penalized are referred to as penalized splines or P-splines;
see Eilers and Marx (1996) and Wood (2001). Eilers and Marx (1996) used a set of q B-spline
basis functions in the explanatory variable X (whose evaluations at the values x of X are the
columns of the n × q design matrix Z in equation (1)). They suggested the use of a moderately
large number of equal-spaced knots (i.e. between 20 and 40), at which the spline segments connect, to ensure enough ﬂexibility in the ﬁtted curves, but they imposed penalties on the B-spline
basis function parameters γ to guarantee sufﬁcient smoothness of the resulting ﬁtted curves.
In effect they assumed that Dr γ ∼ Nn−r .0, λ−1 I/ where Dr is a .q − r/ × q matrix giving rth
differences of the q-dimensional vector γ. (The same approach was used by Wood (2001) but he
used instead a cubic Hermite polynomial basis rather than a B-spine. He also provided a way
of estimating the hyperparameters by using generalized cross-validation (Wood, 2000).) Hence,
in the GAMLSS framework (1), this corresponds to letting G = λK so that γ ∼ N.0, λ−1 K− /
where K = DT
r Dr .
3.2.4. Other smoothers
Other smoothers can be used as additive terms, e.g. the R implementation of a GAMLSS allows
local regression smoothers, loess; Cleveland et al. (1993).
3.2.5. Varying-coefﬁcient terms
Varying-coefﬁcient models (Hastie and Tibshirani, 1993) allow a particular type of interaction
between smoothing additive terms and continuous variables or factors. They are of the form
r h.x/ where r and x are vectors of ﬁxed values of the explanatory variables R and X. It can be
shown that they can be incorporated easily in the GAMLSS ﬁtting algorithm by using a smoothing matrix in the form of equation (6) in the backﬁtting algorithm, with Z = In , K = N−T ΩN−1
and G = λK as in Section 3.2.1 above, but, assuming that the values of R are distinct, with the
diagonal matrix of iterative weights W multiplied by diag.r12 , r22 , . . . , rn2 / and the partial residuals
εi divided by ri for i = 1, 2, . . . , n.

514

R. A. Rigby and D. M. Stasinopoulos

3.2.6. Spatial (covariate) random-effect terms
Besag et al. (1991) and Besag and Higdon (1999) considered models for spatial random effects
with singular multivariate normal distributions, whereas Breslow and Clayton (1993), Lee and
Nelder (2001b) and Fahrmeir and Lang (2001) considered incorporating these spatial terms in
the predictor of the mean in GLMMs. In model (1) the spatial terms can be included in the
predictor of one or more of the location, scale and shape parameters. For example consider an
intrinsic autoregressive model (Besag et al., 1991), in which the vector of random effects for q
geographical regions γ = .γ1 , γ2 , . . . , γq /T has an improper prior density that is proportional to
exp.− 21 λγ T Kγ/, denoted γ ∼ Nq .0, λ−1 K− /, where the elements of the q × q matrix K are given
by kmm = nm where nm is the total number of regions adjacent to region m and kmt = −1 if regions
m and t are adjacent, and kmt = 0 otherwise, for m = 1, 2, . . . , q and t = 1, 2, . . . , q. This model has
−1
the attractive property that, conditional on λ and γ t for t = m, then γm ∼ N{Σ γt n−1
m , .λnm / }
where the summation is over all regions which are neighbours of region m. This is incorporated
in a GAMLSS by setting Z = Iq and G = λK.
3.2.7. Speciﬁc random-effects terms
Lee and Nelder (2001b) considered various random-effect terms in the predictor of the mean in
GLMMs. Many speciﬁc random-effects terms can be incorporated in the predictors in model
(1) including the following.
(a) An overdispersion term: in model (1) let Z = In and γ ∼ Nn .0, λ−1 In /; then this provides
an overdispersion term for each observation (i.e. case) in the predictor.
(b) A one-factor random-effect term: in model (1) let Z be an n × q incidence design matrix
(for a q-level factor) deﬁned by elements zit = 1 if the ith observation belongs to the tth
factor level, and otherwise zit = 0, and let γ ∼ Nq .0, λ−1 Iq /; then this provides a one-factor
random-effects model.
(c) A correlated random-effects term: in model (1), since γ ∼ N.0, G− /, correlated structures
can be applied to the random effects by a suitable choice of the matrix G, e.g. ﬁrstor second-order random walks, ﬁrst- or second-order autoregressive, (time-dependent)
exponential decaying and compound symmetry correlation models.
3.3. Combinations of terms
Any combinations of parametric and additive terms can be combined (in the predictors of one
or more of the location, scale or shape parameters) to produce more complex terms or models.
3.3.1. Combinations of random-effect terms
3.3.1.1. Two-level longitudinal repeated measurement design. Consider a two-level design
with subjects as the ﬁrst level, where yij for i = 1, 2, . . . , nj are repeated measurements at the
second level on subject j, for j = 1, 2, . . . , J. Let η be a vector of predictor values, partitioned
J
T
T
into values for each subject, i.e. ηT = .ηT
1 , η 2 , . . . , η J / of length n = Σj=1 nj . Let Zj be an n × qj
design matrix (for random effects γ j for subject j) having non-zero values for the nj rows corresponding to subject j, and assume that the γ j are all independent with γ j ∼ Nqj .0, Gj−1 /, for
j = 1, 2, . . . , J. (The Zj -matrices and random effects γ j for j = 1, 2, . . . , J could alternatively be
combined into a single design matrix Z and a single random vector γ.)
3.3.1.2. Repeated measures with correlated random-effects terms. In Section 3.3.1.1, set
qj = nj and set the non-zero submatrix of Zj to be the identity matrix Inj , for j = 1, 2, . . . , J.

Generalized Additive Models

515

This allows various covariance or correlation structures in the random effects of the repeated
measurements to be speciﬁed by a suitable choice of matrices Gj , as in point (c) in Section 3.2.7.
3.3.1.3. Random- (covariate) coefﬁcients terms. In Section 3.3.1.1 for j = 1, 2, . . . , J, set
qj = q and Gj = G, i.e. γ j ∼ Nq .0, G−1 /, and set the non-zero submatrix of the design matrices
Zj suitably by using the covariate(s). This allows the speciﬁcation of random (covariate) coefﬁcient models.
3.3.1.4. Multilevel (nested) hierarchical model terms. Let each level of the hierarchy be a
one-factor random-effect term as in point (b) in Section 3.2.7.
3.3.1.5. Crossed random-effect terms. Let each of the crossed factors be a one-factor
random-effect term as in point (b) in Section 3.2.7.
3.3.2. Combinations of random effects and spline terms
There are many useful combinations, e.g. combining random (covariate) coefﬁcients and cubic
smoothing spline terms in the same covariate.
3.3.3. Combinations of spline terms
For example, combining cubic smoothing spline terms in different covariates gives the additive
model; Hastie and Tibshirani (1990).
4.

Specific families of population distribution f (yjθ)

4.1. General comments
The population probability (density) function f.y|θ/ in model (1) is deliberately left general
with no explicit conditional distributional form for the response variable y. The only restriction
that the R implementation of a GAMLSS (Stasinopoulos et al., 2004) has for specifying the
distribution of y is that the function f.y|θ/ and its ﬁrst (and optionally expected second and
cross-) derivatives with respect to each of the parameters of θ must be computable. Explicit
derivatives are preferable but numerical derivatives can be used (resulting in reduced computational speed). Table 1 shows a variety of one-, two-, three- and four-parameter distributions that
the authors have successfully implemented in their software. Johnson et al. (1993, 1994, 1995)
are the classic references on distributions and cover most of the distributions in Table 1. More
information on those distributions which are not covered is provided in Section 4.2. Clearly
Table 1 provides a wide selection of distributions from which to choose, but to extend the list to
include other distributions is a relatively easy task. For some of the distributions that are shown
in Table 1 more that one parameterization has been implemented.
We shall use notation
y ∼ D{g1 .θ1 / = t1 , g2 .θ2 / = t2 , . . . , gp .θp / = tp }
to identify uniquely a GAMLSS, where D is the response variable distribution (as abbreviated in Table 1), .θ1 , . . . , θp / are the parameters of D, .g1 , . . . , gp / are the link functions and
.t1 , . . . , tp / are the model formulae for the explanatory terms and/or random effects in the predictors .η1 , . . . , ηp / respectively. For example
y ∼ TF{µ = cs.x, 3/, log.σ/ = x, log.ν/ = 1}

516

R. A. Rigby and D. M. Stasinopoulos
Table 1. Implemented GAMLSS distributions
Number of parameters
Discrete, one parameter

Discrete, two parameters

Discrete, three parameters
Continuous, one parameter

Continuous, two parameters

Continuous, three parameters

Continuous, four parameters

Distribution
Binomial
Geometric
Logarithmic
Poisson
Positive Poisson
Beta–binomial
Generalized Poisson
Negative binomial type I
Negative binomial type II
Poisson–inverse Gaussian
Sichel
Exponential
Double exponential
Pareto
Rayleigh
Gamma
Gumbel
Inverse Gaussian
Logistic
Log-logistic
Normal
Reverse Gumbel
Weibull
Weibull (proportional hazards)
Box–Cox normal (Cole and Green, 1992)
Generalized extreme family
Generalized gamma family (Box–Cox gamma)
Power exponential family
t-family
Box–Cox t
Box–Cox power exponential
Johnson–Su original
Reparameterized Johnson–Su

is a model where the response variable y has a t-distribution with the location parameter µ
modelled, using an identity link, as a cubic smoothing spline with three effective degrees of freedom in x on top of the linear term in x, i.e. cs(x,3), the scale parameter σ modelled by using a
log-linear model in x and the t-distribution degrees-of-freedom parameter ν modelled by using
a constant model denoted 1 (but on the log-scale).
Quantile residuals (Section 6.2) are obtained easily provided that the cumulative distribution
function (CDF) can be computed, and centile estimation is achieved easily provided that the
inverse CDF can be computed. This applies to the continuous distributions in Table 1 which
transform to simple standard distributions, whereas the CDF and inverse CDF of the discrete
distributions can be computed numerically, if necessary.
Censoring can be incorporated easily in a GAMLSS. For example, assume that an observation is randomly right censored at value y; then its contribution to the log-likelihood l is given by
log{1 − F.y|θ/}, where F.y|θ/ is the CDF of y. Hence, the incorporation of censoring requires
functions for computing F.y|θ/ and also its ﬁrst (and optionally expected second and cross-)
derivatives with respect to each of the parameters .θ1 , θ2 , . . . , θp / in the ﬁtting algorithm. This
has been found to be straightforward for the distributions in Table 1 for which an explicit form
for the CDF exists. Similarly, truncated distributions are easily incorporated in a GAMLSS.

Generalized Additive Models

517

4.2. Specific distributions
Many three- and four-parameter families of continuous distribution for y can be deﬁned by
assuming that a transformed variable z, obtained from y, has a simple well-known distribution.
The Box–Cox normal family for y > 0 which was used by Cole and Green (1992), denoted
by BCN.µ, σ, ν/, reparameterized from Box and Cox (1964), assumes that z has a standard
normal distribution N.0, 1/ with mean 0 and variance 1 where


1
y ν


−
1
,
if ν = 0,

σν
µ
z=
.7/
1
y


 log
,
if ν =0.
σ
µ
Cole and Green (1992) were the ﬁrst to model all three parameters of a distribution as nonparametric smooth functions of a single explanatory variable.
The generalized gamma family for y > 0, as parameterized by Lopatatzidis and Green (2000),
denoted by GG.µ, σ, ν/, assumes that z has a gamma GA.1, σ 2 ν 2 / distribution with mean 1 and
variance σ 2 ν 2 , where z = .y=µ/ν , for ν > 0.
The power exponential family for −∞ < y < ∞ which was used by Nelson (1991), denoted by
PE.µ, σ, ν/, a reparameterization of that of Box and Tiao (1973), assumes that z has a gamma
GA.1, ν/ distribution with mean 1 and variance ν, where


ν  y − µ ν
,
z = 
2 σ c.ν/ 
and the function
c.ν/ = 2−2=ν

Γ.1=ν/
Γ.3=ν/

1=2
,

from Nelson (1991), where ν > 0. For this parameterization µ and σ are the mean and standard
deviation of y respectively.
The Student t-family for −∞ < y < ∞ (e.g. Lange et al. (1989)), denoted by TF.µ, σ, ν/,
assumes that z has a standard t-distribution with ν degrees of freedom, where z = .y − µ/=σ.
The four-parameter Box–Cox t-family for y > 0, denoted by BCT.µ, σ, ν, τ /, is deﬁned by
assuming that z given by expression (7) has a standard t-distribution with τ degrees of freedom;
Rigby and Stasinopoulos (2004a).
The Box–Cox power exponential family for y > 0, denoted BCPE.µ, σ, ν, τ /, is deﬁned by
assuming that z given by expression (7) has a standard power exponential distribution; Rigby
and Stasinopoulos (2004b). This distribution is useful for modelling (positive or negative) skewness combined with (lepto or platy) kurtosis in continuous data.
The Johnson–Su family for −∞ < y < ∞, denoted by JSU0 .µ, σ, ν, τ / (Johnson, 1949), is
deﬁned by assuming that z = ν + τ sinh−1 {.y − µ/=σ} has a standard normal distribution. The
reparameterized Johnson–Su family, denoted by JSU.µ, σ, ν, τ /, has mean µ and standard deviation σ for all values of ν and τ .
5.
