8. Orthogonality
In Section 5.3 we introduced the dot product in R
n
and extended the basic geometric notions of length and
distance. A set {f1, f2, ..., fm} of nonzero vectors in R
n was called an orthogonal set if fi
·fj = 0 for
all i 6= j, and it was proved that every orthogonal set is independent. In particular, it was observed that
the expansion of a vector as a linear combination of orthogonal basis vectors is easy to obtain because
formulas exist for the coefficients. Hence the orthogonal bases are the “nice” bases, and much of this
chapter is devoted to extending results about bases to orthogonal bases. This leads to some very powerful
methods and theorems. Our first task is to show that every subspace of R
n has an orthogonal basis.
8.1 Orthogonal Complements and Projections
If {v1, ..., vm} is linearly independent in a general vector space, and if vm+1 is not in span {v1, ..., vm},
then {v1, ..., vm, vm+1} is independent (Lemma 6.4.1). Here is the analog for orthogonal sets in R
n
.
Lemma 8.1.1: Orthogonal Lemma
Let {f1, f2, ..., fm} be an orthogonal set in R
n
. Given x in R
n
, write
fm+1 = x−
x·f1
kf1k
2
f1 −
x·f2
kf2k
2
f2 −··· −
x·fm
kfmk
2
fm
Then:
1. fm+1 ·fk = 0 for k = 1, 2, ..., m.
2. If x is not in span {f1, ..., fm}, then fm+1 6= 0 and {f1, ..., fm, fm+1} is an orthogonal set.
Proof. For convenience, write ti = (x ·fi)/kfik
2
for each i. Given 1 ≤ k ≤ m:
fm+1 ·fk = (x−t1f1 −··· −tk
fk −··· −tmfm)·fk
= x ·fk −t1(f1 ·fk)−··· −tk(fk
·fk)−··· −tm(fm ·fk)
= x ·fk −tkkfkk
2
= 0
This proves (1), and (2) follows because fm+1 6= 0 if x is not in span {f1, ..., fm}.
The orthogonal lemma has three important consequences for R
n
. The first is an extension for orthogonal sets of the fundamental fact that any independent set is part of a basis (Theorem 6.4.1).
415
416 Orthogonality
Theorem 8.1.1
Let U be a subspace of R
n
.
1. Every orthogonal subset {f1, ..., fm} in U is a subset of an orthogonal basis of U.
2. U has an orthogonal basis.
Proof.
1. If span {f1, ..., fm} =U, it is already a basis. Otherwise, there exists x inU outside span {f1, ..., fm}.
If fm+1 is as given in the orthogonal lemma, then fm+1 is in U and {f1, ..., fm, fm+1} is orthogonal.
If span {f1, ..., fm, fm+1} = U, we are done. Otherwise, the process continues to create larger and
larger orthogonal subsets of U. They are all independent by Theorem 5.3.5, so we have a basis when
we reach a subset containing dim U vectors.
2. If U = {0}, the empty basis is orthogonal. Otherwise, if f 6= 0 is in U, then {f} is orthogonal, so (2)
follows from (1).
We can improve upon (2) of Theorem 8.1.1. In fact, the second consequence of the orthogonal lemma
is a procedure by which any basis {x1, ..., xm} of a subspace U of R
n
can be systematically modified to
yield an orthogonal basis {f1, ..., fm} of U. The fi are constructed one at a time from the xi
.
To start the process, take f1 = x1. Then x2 is not in span {f1} because {x1, x2} is independent, so take
f2 = x2 −
x2·f1
kf1k
2
f1
Thus {f1, f2} is orthogonal by Lemma 8.1.1. Moreover, span {f1, f2} = span {x1, x2} (verify), so x3 is
not in span {f1, f2}. Hence {f1, f2, f3} is orthogonal where
f3 = x3 −
x3·f1
kf1k
2
f1 −
x3·f2
kf2k
2
f2
Again, span {f1, f2, f3} = span {x1, x2, x3}, so x4 is not in span {f1, f2, f3} and the process continues.
At the mth iteration we construct an orthogonal set {f1, ..., fm} such that
span {f1, f2, ..., fm} = span {x1, x2, ..., xm} = U
Hence {f1, f2, ..., fm} is the desired orthogonal basis of U. The procedure can be summarized as follows.
8.1. Orthogonal Complements and Projections 417
0
x3
f2
f1
span {f1, f2}
Gram-Schmidt
0
f3
f2
f1
span {f1, f2}
Theorem 8.1.2: Gram-Schmidt Orthogonalization Algorithm1
If {x1, x2, ..., xm} is any basis of a subspace U of R
n
, construct
f1, f2, ..., fm in U successively as follows:
f1 = x1
f2 = x2 −
x2·f1
kf1k
2
f1
f3 = x3 −
x3·f1
kf1k
2
f1 −
x3·f2
kf2k
2
f2
.
.
.
fk = xk −
xk
·f1
kf1k
2
f1 −
xk
·f2
kf2k
2
f2 −··· −
xk
·fk−1
kfk−1k
2
fk−1
for each k = 2, 3, ..., m. Then
1. {f1, f2, ..., fm} is an orthogonal basis of U.
2. span {f1, f2, ..., fk} = span {x1, x2, ..., xk} for each
k = 1, 2, ..., m.
The process (for k = 3) is depicted in the diagrams. Of course, the algorithm converts any basis of R
n
itself into an orthogonal basis.
Example 8.1.1
Find an orthogonal basis of the row space of A =


1 1 −1 −1
3 2 0 1
1 0 1 0

.
Solution. Let x1, x2, x3 denote the rows of A and observe that {x1, x2, x3} is linearly independent.
Take f1 = x1. The algorithm gives
f2 = x2 −
x2·f1
kf1k
2
f1 = (3, 2, 0, 1)−
4
4
(1, 1, −1, −1) = (2, 1, 1, 2)
f3 = x3 −
x3·f1
kf1k
2
f1 −
x3·f2
kf2k
2
f2 = x3 −
0
4
f1 −
3
10 f2 =
1
10 (4, −3, 7, −6)
Hence {(1, 1, −1, −1), (2, 1, 1, 2),
1
10 (4, −3, 7, −6)} is the orthogonal basis provided by the
algorithm. In hand calculations it may be convenient to eliminate fractions (see the Remark
below), so {(1, 1, −1, −1), (2, 1, 1, 2), (4, −3, 7, −6)} is also an orthogonal basis for row A.
1Erhardt Schmidt (1876–1959) was a German mathematician who studied under the great David Hilbert and later developed
the theory of Hilbert spaces. He first described the present algorithm in 1907. Jörgen Pederson Gram (1850–1916) was a Danish
actuary.
418 Orthogonality
Remark
Observe that the vector x·fi
kfik
2
fi
is unchanged if a nonzero scalar multiple of fi
is used in place of fi
. Hence,
if a newly constructed fi
is multiplied by a nonzero scalar at some stage of the Gram-Schmidt algorithm,
the subsequent fs will be unchanged. This is useful in actual calculations.
Projections
x
p
x−p
0
U
Suppose a point x and a plane U through the origin in R
3
are given, and
we want to find the point p in the plane that is closest to x. Our geometric
intuition assures us that such a point p exists. In fact (see the diagram), p
must be chosen in such a way that x−p is perpendicular to the plane.
Now we make two observations: first, the plane U is a subspace of R
3
(because U contains the origin); and second, that the condition that x−p
is perpendicular to the plane U means that x − p is orthogonal to every vector in U. In these terms the
whole discussion makes sense in R
n
. Furthermore, the orthogonal lemma provides exactly what is needed
to find p in this more general setting.
Definition 8.1 Orthogonal Complement of a Subspace of R
n
If U is a subspace of R
n
, define the orthogonal complement U
⊥ of U (pronounced “U-perp”) by
U
⊥ = {x in R
n
| x · y = 0 for all y in U}
The following lemma collects some useful properties of the orthogonal complement; the proof of (1)
and (2) is left as Exercise 8.1.6.
Lemma 8.1.2
Let U be a subspace of R
n
.
1. U
⊥ is a subspace of R
n
.
2. {0}
⊥ = R
n
and (R
n
)
⊥ = {0}.
3. If U = span {x1, x2, ..., xk}, then U
⊥ = {x in R
n
| x · xi = 0 for i = 1, 2, ..., k}.
Proof.
3. Let U = span {x1, x2, ..., xk}; we must show that U
⊥ = {x | x · xi = 0 for each i}. If x is in U
⊥
then x · xi = 0 for all i because each xi
is in U. Conversely, suppose that x · xi = 0 for all i; we must
show that x is in U
⊥, that is, x·y = 0 for each y in U. Write y = r1x1 +r2x2 +···+rkxk
, where each
ri
is in R. Then, using Theorem 5.3.1,
x · y = r1(x · x1) +r2(x · x2) +···+rk(x · xk) = r10+r20+···+rk0 = 0
as required.
8.1. Orthogonal Complements and Projections 419
Example 8.1.2
Find U
⊥ if U = span {(1, −1, 2, 0), (1, 0, −2, 3)} in R
4
.
Solution. By Lemma 8.1.2, x = (x, y, z, w) is in U
⊥ if and only if it is orthogonal to both
(1, −1, 2, 0) and (1, 0, −2, 3); that is,
x − y + 2z = 0
x − 2z + 3w = 0
Gaussian elimination gives U
⊥ = span {(2, 4, 1, 0), (3, 3, 0, −1)}.
x
0
p
d
U
Now consider vectors x and d 6= 0 in R
3
. The projection p = projd
x
of x on d was defined in Section 4.2 as in the diagram.
The following formula for p was derived in Theorem 4.2.4
p = projd
x =

x·d
kdk
2

d
where it is shown that x−p is orthogonal to d. Now observe that the line
U = Rd = {td | t ∈ R} is a subspace of R
3
, that {d} is an orthogonal basis
of U, and that p ∈ U and x−p ∈ U
⊥ (by Theorem 4.2.4).
In this form, this makes sense for any vector x in R
n
and any subspace U of R
n
, so we generalize it
as follows. If {f1, f2, ..., fm} is an orthogonal basis of U, we define the projection p of x on U by the
formula
p =

x·f1
kf1k
2

f1 +

x·f2
kf2k
2

f2 +···+

x·fm
kfmk
2

fm (8.1)
Then p ∈ U and (by the orthogonal lemma) x − p ∈ U
⊥, so it looks like we have a generalization of
Theorem 4.2.4.
However there is a potential problem: the formula (8.1) for p must be shown to be independent of the
choice of the orthogonal basis {f1, f2, ..., fm}. To verify this, suppose that {f
′
1
, f
′
2
, ..., f
′
m} is another
orthogonal basis of U, and write
p
′ =

x·f
′
1
kf
′
1
k
2

f
′
1 +

x·f
′
2
kf
′
2
k
2

f
′
2 +···+

x·f
′
m
kf
′
mk
2

f
′
m
As before, p
′ ∈ U and x−p
′ ∈ U
⊥, and we must show that p
′ = p. To see this, write the vector p−p
′
as
follows:
p−p
′ = (x−p
′
)−(x−p)
This vector is in U (because p and p
′
are in U) and it is in U
⊥ (because x−p
′
and x−p are in U
⊥), and
so it must be zero (it is orthogonal to itself!). This means p
′ = p as desired.
Hence, the vector p in equation (8.1) depends only on x and the subspace U, and not on the choice
of orthogonal basis {f1, ..., fm} of U used to compute it. Thus, we are entitled to make the following
definition:
420 Orthogonality
Definition 8.2 Projection onto a Subspace of R
n
Let U be a subspace of R
n with orthogonal basis {f1, f2, ..., fm}. If x is in R
n
, the vector
projU x =
x·f1
kf1k
2
f1 +
x·f2
kf2k
2
f2 +···+
x·fm
kfmk
2
fm
is called the orthogonal projection of x on U. For the zero subspace U = {0}, we define
proj{0}
x = 0
The preceding discussion proves (1) of the following theorem.
Theorem 8.1.3: Projection Theorem
If U is a subspace of R
n
and x is in R
n
, write p = projU x. Then:
1. p is in U and x−p is in U
⊥.
2. p is the vector in U closest to x in the sense that
kx−pk < kx−yk for all y ∈ U, y 6= p
Proof.
1. This is proved in the preceding discussion (it is clear if U = {0}).
2. Write x−y = (x−p) + (p−y). Then p−y is in U and so is orthogonal to x−p by (1). Hence, the
Pythagorean theorem gives
kx−yk
2 = kx−pk
2 +kp−yk
2 > kx−pk
2
because p−y 6= 0. This gives (2).
Example 8.1.3
Let U = span {x1, x2} in R
4 where x1 = (1, 1, 0, 1) and x2 = (0, 1, 1, 2). If x = (3, −1, 0, 2),
find the vector in U closest to x and express x as the sum of a vector in U and a vector orthogonal
to U.
Solution. {x1, x2} is independent but not orthogonal. The Gram-Schmidt process gives an
orthogonal basis {f1, f2} of U where f1 = x1 = (1, 1, 0, 1) and
f2 = x2 −
x2·f1
kf1k
2
f1 = x2 −
3
3
f1 = (−1, 0, 1, 1)
Hence, we can compute the projection using {f1, f2}:
p = projU x =
x·f1
kf1k
2
f1 +
x·f2
kf2k
2
f2 =
4
3
f1 +
−1
3
f2 =
1
3

5 4 −1 3
8.1. Orthogonal Complements and Projections 421
Thus, p is the vector in U closest to x, and x−p =
1
3
(4, −7, 1, 3) is orthogonal to every vector in
U. (This can be verified by checking that it is orthogonal to the generators x1 and x2 of U.) The
required decomposition of x is thus
x = p+ (x−p) = 1
3
(5, 4, −1, 3) + 1
3
(4, −7, 1, 3)
Example 8.1.4
Find the point in the plane with equation 2x+y−z = 0 that is closest to the point (2, −1, −3).
Solution. We write R
3
as rows. The plane is the subspace U whose points (x, y, z) satisfy
z = 2x+y. Hence
U = {(s, t, 2s+t) | s, t in R} = span {(0, 1, 1), (1, 0, 2)}
The Gram-Schmidt process produces an orthogonal basis {f1, f2} of U where f1 = (0, 1, 1) and
f2 = (1, −1, 1). Hence, the vector in U closest to x = (2, −1, −3) is
projU x =
x·f1
kf1k
2
f1 +
x·f2
kf2k
2
f2 = −2f1 +0f2 = (0, −2, −2)
Thus, the point in U closest to (2, −1, −3) is (0, −2, −2).
The next theorem shows that projection on a subspace of R
n
is actually a linear operator R
n → R
n
.
Theorem 8.1.4
Let U be a fixed subspace of R
n
. If we define T : R
n → R
n by
T(x) = projU x for all x in R
n
1. T is a linear operator.
2. im T = U and ker T = U
⊥.
3. dim U + dim U
⊥ = n.
Proof. If U = {0}, then U
⊥ = R
n
, and so T(x) = proj{0}
x = 0 for all x. Thus T = 0 is the zero (linear)
operator, so (1), (2), and (3) hold. Hence assume that U 6= {0}.
1. If {f1, f2, ..., fm} is an orthonormal basis of U, then
T(x) = (x ·f1)f1 + (x ·f2)f2 +···+ (x ·fm)fm for all x in R
n
(8.2)
by the definition of the projection. Thus T is linear because
(x+y)·fi = x ·fi +y ·fi and (rx)·fi = r(x ·fi) for each i
422 Orthogonality
2. We have im T ⊆ U by (8.2) because each fi
is in U. But if x is in U, then x = T(x) by (8.2) and the
expansion theorem applied to the space U. This shows that U ⊆ im T, so im T = U.
Now suppose that x is in U
⊥. Then x ·fi = 0 for each i (again because each fi
is in U) so x is in
ker T by (8.2). Hence U
⊥ ⊆ ker T. On the other hand, Theorem 8.1.3 shows that x−T(x) is in U
⊥
for all x in R
n
, and it follows that ker T ⊆ U
⊥. Hence ker T = U
⊥, proving (2).
3. This follows from (1), (2), and the dimension theorem (Theorem 7.2.4).
Exercises for 8.1
Exercise 8.1.1 In each case, use the Gram-Schmidt algorithm to convert the given basis B of V into an orthogonal basis.
a. V = R
2
, B = {(1, −1), (2, 1)}
b. V = R
2
, B = {(2, 1), (1, 2)}
c. V = R
3
, B = {(1, −1, 1), (1, 0, 1), (1, 1, 2)}
d. V = R
3
, B = {(0, 1, 1), (1, 1, 1), (1, −2, 2)}
Exercise 8.1.2 In each case, write x as the sum of a
vector in U and a vector in U
⊥.
a. x = (1, 5, 7), U = span {(1, −2, 3), (−1, 1, 1)}
b. x = (2, 1, 6), U = span {(3, −1, 2), (2, 0, −3)}
c. x = (3, 1, 5, 9),
U = span {(1, 0, 1, 1), (0, 1, −1, 1), (−2, 0, 1, 1)}
d. x = (2, 0, 1, 6),
U = span {(1, 1, 1, 1), (1, 1, −1, −1), (1, −1, 1, −1)}
e. x = (a, b, c, d),
U = span {(1, 0, 0, 0), (0, 1, 0, 0), (0, 0, 1, 0)}
f. x = (a, b, c, d),
U = span {(1, −1, 2, 0), (−1, 1, 1, 1)}
Exercise 8.1.3 Let x = (1, −2, 1, 6) in R
4
, and let
U = span {(2, 1, 3, −4), (1, 2, 0, 1)}.
a. Compute projU x.
b. Show that {(1, 0, 2, −3), (4, 7, 1, 2)} is another
orthogonal basis of U.
c. Use the basis in part (b) to compute projU x.
Exercise 8.1.4 In each case, use the Gram-Schmidt algorithm to find an orthogonal basis of the subspace U,
and find the vector in U closest to x.
a. U = span {(1, 1, 1), (0, 1, 1)}, x = (−1, 2, 1)
b. U = span {(1, −1, 0), (−1, 0, 1)}, x = (2, 1, 0)
c. U = span {(1, 0, 1, 0), (1, 1, 1, 0), (1, 1, 0, 0)},
x = (2, 0, −1, 3)
d. U = span {(1, −1, 0, 1), (1, 1, 0, 0), (1, 1, 0, 1)},
x = (2, 0, 3, 1)
Exercise 8.1.5 Let U = span {v1, v2, ..., vk}, vi
in R
n
,
and let A be the k×n matrix with the vi as rows.
a. Show that U
⊥ = {x | x in R
n
, Ax
T = 0}.
b. Use part (a) to find U
⊥ if
U = span {(1, −1, 2, 1), (1, 0, −1, 1)}.
Exercise 8.1.6
a. Prove part 1 of Lemma 8.1.2.
b. Prove part 2 of Lemma 8.1.2.
8.1. Orthogonal Complements and Projections 423
Exercise 8.1.7 Let U be a subspace of R
n
. If x in R
n
can be written in any way at all as x = p+q with p in U
and q in U
⊥, show that necessarily p = projU x.
Exercise 8.1.8 Let U be a subspace of R
n
and let x be
a vector in R
n
. Using Exercise 8.1.7, or otherwise, show
that x is in U if and only if x = projU x.
Exercise 8.1.9 Let U be a subspace of R
n
.
a. Show that U
⊥ = R
n
if and only if U = {0}.
b. Show that U
⊥ = {0} if and only if U = R
n
.
Exercise 8.1.10 If U is a subspace of R
n
, show that
projU x = x for all x in U.
Exercise 8.1.11 If U is a subspace of R
n
, show that
x = projU x+ projU⊥ x for all x in R
n
.
Exercise 8.1.12 If {f1, ..., fn} is an orthogonal basis of
R
n
and U = span {f1, ..., fm}, show that
U
⊥ = span {fm+1, ..., fn}.
Exercise 8.1.13 If U is a subspace of R
n
, show that
U
⊥⊥ = U. [Hint: Show that U ⊆ U
⊥⊥, then use Theorem 8.1.4 (3) twice.]
Exercise 8.1.14 If U is a subspace of R
n
, show how to
find an n×n matrix A such that U = {x | Ax = 0}. [Hint:
Exercise 8.1.13.]
Exercise 8.1.15 Write R
n
as rows. If A is an n×n matrix, write its null space as null A = {x in R
n
| Ax
T = 0}.
Show that:
null A = (row A)
⊥ a. null ; A
T = (col A)
⊥ b. .
Exercise 8.1.16 If U and W are subspaces, show that
(U +W)
⊥ = U
⊥ ∩W⊥. [See Exercise 5.1.22.]
Exercise 8.1.17 Think of R
n
as consisting of rows.
a. Let E be an n×n matrix, and let
U = {xE | x in R
n}. Show that the following are
equivalent.
i. E
2 = E = E
T
(E is a projection matrix).
ii. (x−xE)·(yE) = 0 for all x and y in R
n
.
iii. projU x = xE for all x in R
n
.
[Hint: For (ii) implies (iii): Write x = xE +
(x − xE) and use the uniqueness argument
preceding the definition of projU x. For (iii)
implies (ii): x−xE is in U
⊥ for all x in R
n
.]
b. If E is a projection matrix, show that I −E is also
a projection matrix.
c. If EF = 0 = FE and E and F are projection matrices, show that E +F is also a projection matrix.
d. If A is m × n and AAT
is invertible, show that
E = A
T
(AAT
)
−1A is a projection matrix.
Exercise 8.1.18 Let A be an n×n matrix of rank r. Show
that there is an invertible n×n matrix U such that UA is a
row-echelon matrix with the property that the first r rows
are orthogonal. [Hint: Let R be the row-echelon form
of A, and use the Gram-Schmidt process on the nonzero
rows of R from the bottom up. Use Lemma 2.4.1.]
Exercise 8.1.19 Let A be an (n−1)×n matrix with rows
x1, x2, ..., xn−1 and let Ai denote the
(n−1)×(n−1) matrix obtained from A by deleting column i. Define the vector y in R
n by
y =

det A1 − det A2 det A3 ··· (−1)
n+1 det An

Show that:
a. xi
· y = 0 for all i = 1, 2, ..., n− 1. [Hint: Write
Bi =

xi
A

and show that det Bi = 0.]
b. y 6= 0 if and only if {x1, x2, ..., xn−1} is linearly
independent. [Hint: If some det Ai 6= 0, the rows
of Ai are linearly independent. Conversely, if the
xi are independent, consider A =UR where R is in
reduced row-echelon form.]
c. If {x1, x2, ..., xn−1} is linearly independent, use
Theorem 8.1.3(3) to show that all solutions to the
system of n−1 homogeneous equations
Ax
T = 0
are given by ty, t a parameter.
424 Orthogonality
8.2 Orthogonal Diagonalization
Recall (Theorem 5.5.3) that an n×n matrix A is diagonalizable if and only if it has n linearly independent
eigenvectors. Moreover, the matrix P with these eigenvectors as columns is a diagonalizing matrix for A,
that is
P
−1AP is diagonal.
As we have seen, the really nice bases of R
n
are the orthogonal ones, so a natural question is: which n×n
matrices have an orthogonal basis of eigenvectors? These turn out to be precisely the symmetric matrices,
and this is the main result of this section.
Before proceeding, recall that an orthogonal set of vectors is called orthonormal if kvk = 1 for each
vector v in the set, and that any orthogonal set {v1, v2, ..., vk} can be “normalized”, that is converted into
an orthonormal set {
1
kv1k
v1,
1
kv2k
v2, ...,
1
kvkk
vk}. In particular, if a matrix A has n orthogonal eigenvectors,
they can (by normalizing) be taken to be orthonormal. The corresponding diagonalizing matrix P has
orthonormal columns, and such matrices are very easy to invert.
Theorem 8.2.1
The following conditions are equivalent for an n×n matrix P.
1. P is invertible and P
−1 = P
T
.
2. The rows of P are orthonormal.
3. The columns of P are orthonormal.
Proof. First recall that condition (1) is equivalent to PPT = I by Corollary 2.4.1 of Theorem 2.4.5. Let
x1, x2, ..., xn denote the rows of P. Then x
T
j
is the jth column of P
T
, so the (i, j)-entry of PPT
is xi
· xj
.
Thus PPT = I means that xi
· xj = 0 if i 6= j and xi
· xj = 1 if i = j. Hence condition (1) is equivalent to
(2). The proof of the equivalence of (1) and (3) is similar.
Definition 8.3 Orthogonal Matrices
An n×n matrix P is called an orthogonal matrix
2
if it satisfies one (and hence all) of the
conditions in Theorem 8.2.1.
Example 8.2.1
The rotation matrix 
cosθ −sinθ
sinθ cosθ

is orthogonal for any angle θ.
These orthogonal matrices have the virtue that they are easy to invert—simply take the transpose. But
they have many other important properties as well. If T : R
n → R
n
is a linear operator, we will prove
2
In view of (2) and (3) of Theorem 8.2.1, orthonormal matrix might be a better name. But orthogonal matrix is standard.