36-705: Intermediate Statistics Fall 2019
Lecture 19: October 11
Lecturer: <PERSON><PERSON>
Today we will try to address the question of what is the asymptotic distribution of the MLE.
This is analogous to the CLT which gave the asymptotic distribution of averages.
In some cases, we can do this directly. For instance, if X1, . . . , Xn ∼ Ber(p) then the MLE
is just the average:
pb =
1
n
Xn
i=1
Xi
,
and so we know by the CLT:
√
n
pb− p
p
p(1 − p)
d
→ N(0, 1),
which tells us the asymptotic distribution of the MLE.
More generally, however the MLE need not be a simple average of i.i.d. terms, but the main
take-away is that asymptotically it often behaves like one.
19.1 Reminder
Just to remind you - in the lecture on the Cram´er-Rao bound, we defined the score,
s(θ) = Xn
i=1
∇ log(p(Xi
; θ)),
which is the gradient of the log-likelihood, and the Fisher Information,
I(θ) = E[s(θ)s(θ)
T
].
We showed that s(θ) has mean 0, so I(θ) = Var(s(θ)). The Fisher information is alternatively
the expected Hessian of the log-likelihood:
In(θ) = E
"Xn
i=1
∇2
log p(Xi
; θ)
#
.
It is worth remembering that the score is data-dependent, while the Fisher Information is
not (it is an expectation over the data so does not depend on the values of X1, . . . , Xn).
19-1
19-2 Lecture 19: October 11
Let θb denote the MLE. The rough goal for most of today is to show that (under enough
regularity conditions),
√
n(θb− θ)
d
→ N(0, [I1(θ)]−1
).
19.2 Counterexample
The usual counterexample to the above convergence in distribution is the MLE for the
uniform distribution.
For the uniform distribution most regularity conditions fail. Formally, we observe X1, . . . , Xn ∼
U[0, θ] and want to estimate θ. The log-likelihood:
LL(θ) = log 
1
θ
n
I(θ ≥
n
max
i=1
Xi)

.
The MLE is just θb = maxn
i=1 Xi
. Firstly, you should observe that the log-likelihood is not
differentiable at the MLE, so the Fisher information is not defined at the MLE.
Another thing that we used frequently in defining the equivalent forms of the Fisher information was to exchange derivatives (with respect to θ) and integrals (with respect to X).
This in general does not work if the domain of integration depends on the parameter with
respect to which we are taking the derivative. For the uniform distribution the domain of
density depends on the parameter.
On the other hand, things are usually nice for exponential families. They will automatically
satisfy all the regularity conditions (provided it is identifiable, i.e. say full-rank and minimal)
and the MLE is extremely well-behaved in such models.
Returning to the uniform case, we can directly analyze the distribution of the MLE. In
Lecture 4, we showed the following:
n(θb− θ)
d
→ −Exp(1/θ)
(we did this when θ = 1 but you can work out the general case). So it should be clear that,
√
n(θb− θ)
d
→ δ0, where δ0 is a point mass at 0 and it does not have a Gaussian limit.
19.3 MLE asymptotics
We will only attempt a heuristic calculation here. If you are curious to see a rigorous proof
with minimal regularity assumptions you should look at Van der Vaart’s book on Asymptotic
Statistics. Here is a list of some sufficient regularity conditions:
Lecture 19: October 11 19-3
1. The dimension of the parameter space does not change with n, i.e. θ ∈ R
d and d is
fixed. We have seen that if d grows the MLE need not even be consistent.
2. p(x; θ) is a smooth (thrice differentiable) function of θ,
3. We can interchange differentiation with respect to θ and integration over X. This
in turn requires that the range of X does not depend on θ, and some integrability
conditions on p(x; θ).
4. The parameter θ is identifiable.
5. If the parameter space is restricted, i.e. θ ∈ Θ for some set Θ then θ is in the interior
of the set Θ (i.e. cannot be on its boundary).
We will focus on the case when the parameter is one-dimensional, although everything carries
over almost exactly in the general (fixed) d case.
Theorem 19.1 Under the regularity conditions above,
√
n(θb− θ)
d
→ N(0, 1/I(θ)).
We note that under the conditions of the theorem one can verify that the MLE is consistent,
i.e. that θb
p→ θ. The basic idea is to verify that under the differentiability assumptions on
the density, we can effectively treat the parameter space as compact, then derive a uniform
law of large numbers, and then apply the proof from the previous lecture notes. This is
a complicated technical proof but you can look it up by searching for Wald’s proof of the
consistency of the MLE.
The proof will use all the facts about scores and the Fisher information that we derived
earlier.
Proof: This is not a complete proof. I will try to point out why the various regularity
conditions are needed.
To begin with let us note the following fact: if θb
p→ θ, then
Eθ[−∇2
θ
log p(X; θb)] p→ Eθ[−∇2
θ
log p(X; θ)] = I(θ).
Roughly, this is saying that as θb gets close to θ the Hessian of the log-likelihood at θb gets
close to the Hessian of log-likelihood at θ. This is just a smoothness assumption on the
Hessian, which is why we assumed that p(X; θ) is thrice differentiable.
Since θb maximizes the log-likelihood we know that the derivative of the log-likelihood at θb
must be 0, i.e.
LL0
(θb) = 0.
19-4 Lecture 19: October 11
Formally you need to know that θb is not on the boundary of the parameter space. To prove
this you will need to use the fact that θ is not on the boundary and that θb
p→ θ.
By a Taylor expansion of the derivative of the log-likelihood we obtain that,
0 = LL0
(θb) = LL0
(θ) + (θb− θ)LL00(θe),
where θe is some point in between θb and θ. This in turn gives us that,
(θb− θ) = LL0
(θ)
−LL00(θe)
,
so that,
√
n(θb− θ) =
LL0
(θ)
√
n
−
LL00(θe)
n
.
We will look at the numerator and denominator separately. The denominator is:
−
LL00(θe)
n
=
1
n
Xn
i=1
−∇2
θ
log p(Xi
; θe)
p→ Eθ[−∇2
θ
log p(X; θe)] p→ Eθ[−∇2
θ
log p(X; θ)]
where the last step uses the fact that θe
p→ θ.
The numerator is just the score function, i.e.
1
√
n
LL0
(θ) = 1
√
n
Xn
i=1
∇θ log p(Xi
; θ) = √
n ×
1
n
Xn
i=1
[∇θ log p(Xi
; θ) − E[∇θ log p(X; θ)]]
d
→ N(0, Var(∇θ log p(X; θ))) d
→ N(0, I(θ)),
where we used the facts that the score has mean 0, that the variance of the score is the Fisher
information and that by the CLT √
n times an average of i.i.d. terms minus its expectation
converges in distribution to a normal.
Putting the pieces together via Slutsky’s theorem we obtain that,
√
n(θb− θ)
d
→
1
I(θ)
N(0, I(θ)) d
→ N(0, 1/I(θ)),
which is what we wanted to prove.
Example: Suppose that X1, . . . , Xn ∼ Exp(θ), then the log-likelihood,
LL(θ) = n log θ − θ
Xn
i=1
Xi
.
Lecture 19: October 11 19-5
The score function:
s(θ) = n
θ
−
Xn
i=1
Xi
,
and the Fisher information,
I(θ) = n
θ
2
.
The MLE is θb =
1
X
. So we can use the above result to conclude that,
θb− θ
d
→ N

0,
θ
2
n

.
19.4 Influence Functions and Regular Asymptotically
Linear Estimators
We could have followed a similar proof as above to conclude that the MLE can be written
as:
θb = θ +
1
n
Xn
i=1
∇θ log p(Xi
; θ)
I(θ)
+ Remainder,
where the remainder is small (roughly proportional to the previous term multiplied by [I(θe)−
I(θ)] → 0).
The term,
ψ(x) = ∇θ log p(x; θ)
I(θ)
,
is called the influence function, i.e. as you can see above it measures the influence each single
observation has on the estimator θb, i.e.
θb ≈ θ +
1
n
Xn
i=1
ψ(Xi).
An estimator is often called robust if the function ψ is bounded, i.e. each observation can
exert a limited influence on the estimator. Almost every estimator we have seen so far is
non-robust in this sense.
For instance, for X1, . . . , Xn ∼ N(θ, σ2
) with σ known say, it is easy to check that the MLE
satisfies,
θb = θ +
1
n
Xn
i=1
(Xi − θ),
19-6 Lecture 19: October 11
so that the influence of any point on the MLE is Xi − θ which is certainly unbounded. This
means that if I corrupted a single point Xi then the MLE could be arbitrarily bad.
Thinking of a complex predictor like a deep neural network, one can try to obtain some
information about the predictor by trying to compute the influence function of training
images on the final predictor. A paper that did this (and quite a bit more) won ICML’s best
paper a few years ago.
Returning to the expression:
θb ≈ θ +
1
n
Xn
i=1
ψ(Xi).
Estimators that satisfy this type of expansion are called asymptotically linear estimators
(many non-MLE estimators also satisfy expansions of this form). There is a classical result
due to Le Cam that any sufficiently well-behaved (regular) estimator is asymptotically linear.
It is not easy to prove (see Van Der Vaart’s book). This together with the Cram´er-Rao lower
bound implies that the MLE is the “best regular asymptotically linear estimator”.
19.5 Asymptotic Relative Efficiency
Once you restrict attention to asymptotically linear estimators, comparing estimators in
terms of their MSE boils down to comparing their variances. Specifically, if
√
n(Wn − τ (θ)) N(0, σ2
W )
√
n(Vn − τ (θ)) N(0, σ2
V
)
then the asymptotic relative efficiency (ARE) is
ARE(Vn, Wn) = σ
2
W
σ
2
V
.
Example 19.2 Let X1, . . . , Xn ∼ Poisson(λ). The mle of λ is X. Let
τ = P(Xi = 0).
So τ = e
−λ
. Define Yi = I(Xi = 0). This suggests the estimator
Wn =
1
n
Xn
i=1
Yi
.
Another estimator is the mle
Vn = e
−λˆ
.
Lecture 19: October 11 19-7
The delta method gives
Var(Vn) ≈
λe−2λ
n
.
We have
√
n(Wn − τ ) N(0, e−λ
(1 − e
−λ
))
√
n(Vn − τ ) N(0, λe−2λ
).
So
ARE(Wn, Vn) = λ
e
λ − 1
≤ 1.
Since the mle is efficient, we know that, in general, ARE(Wn, mle) ≤ 1.
19.6 Multivariate Case
Now let θ = (θ1, . . . , θk). In this case we have
√
n(
ˆθ − θ) N(0, I−1
(θ))
where I
−1
(θ) is the inverse of the Fisher information matrix. The approximate standard
error of ˆθj
is q
I
−1
jj /n. If τ = g(θ) with g : R
k → R then by the delta method,
√
n(ˆτ − τ ) N(0,(g
0
)
T
I
−1
g
0
)
where g
0
is the gradient of g evaluated at θ