Variable Elimination
Next, we turn our attention to the problem of inference in graphical models. Given a probabilistic model (such as a Bayes net or a MRF), we are interested in using it to answer useful questions, e.g., determining the probability that a given email is spam. More formally, we focus on two types of questions:

Marginal inference: what is the probability of a given variable in our model after we sum everything else out (e.g., probability of spam vs. non-spam)?
p
(
y
=
1
)
=
∑
x
1
  
∑
x
2
 
⋯
∑
x
n
 
p
(
y
=
1
,
x
1
,
x
2
,
…
,
x
n
)
.
Maximum a posteriori (MAP) inference: what is the most likely assignment to the variables in the model (possibly conditioned on evidence)?
max
x
1
,
…
,
x
n
 
p
(
y
=
1
,
x
1
,
…
,
x
n
)
It turns out that inference is a challenging task. For many probabilities of interest, it is NP-hard to answer any of these questions exactly. Crucially, whether inference is tractable depends on the structure of the graph that describes that probability. If a problem is intractable, we are still able to obtain useful answers via approximate inference methods.

This chapter covers the first exact inference algorithm, variable elimination. We discuss approximate inference in later chapters.

We will assume for the rest of the chapter that 
x
i
 are discrete variables taking 
k
 possible values each.

An illustrative example
Consider first the problem of marginal inference. Suppose for simplicity that we are given a chain Bayesian network, i.e., a probability of the form

p
(
x
1
,
…
,
x
n
)
=
p
(
x
1
)
n
∏
i
=
2
 
p
(
x
i
∣
x
i
−
1
)
.
We are interested in computing the marginal probability 
p
(
x
n
)
. The naive way of calculating this is to sum the probability over all 
k
n
−
1
 assignments to 
x
1
,
…
,
x
n
−
1
:

p
(
x
n
)
=
∑
x
1
 
⋯
∑
x
n
−
1
 
p
(
x
1
,
…
,
x
n
)
.
However, we can do much better by leveraging the factorization of our probability distribution. We may rewrite the sum in a way that “pushes in” certain variables deeper into the product.

p
(
x
n
)
=
∑
x
1
 
⋯
∑
x
n
−
1
 
p
(
x
1
)
n
∏
i
=
2
 
p
(
x
i
∣
x
i
−
1
)
=
∑
x
n
−
1
 
p
(
x
n
∣
x
n
−
1
)
∑
x
n
−
2
 
p
(
x
n
−
1
∣
x
n
−
2
)
⋯
∑
x
1
 
p
(
x
2
∣
x
1
)
p
(
x
1
)
We sum the inner terms first, starting from 
x
1
 and ending with 
x
n
−
1
. Concretely, we start by computing an intermediary factor 
τ
(
x
2
)
=
∑
x
1
p
(
x
2
∣
x
1
)
p
(
x
1
)
 by summing out 
x
1
. This takes 
O
(
k
2
)
 time because we must sum over 
x
1
 for each assignment to 
x
2
. The resulting factor 
τ
(
x
2
)
 can be thought of as a table of 
k
 values (though not necessarily probabilities), with one entry for each assignment to 
x
2
 (just as factor 
p
(
x
1
)
 can be represented as a table). We may then rewrite the marginal probability using 
τ
 as

p
(
x
n
)
=
∑
x
n
−
1
 
p
(
x
n
∣
x
n
−
1
)
∑
x
n
−
2
 
p
(
x
n
−
1
∣
x
n
−
2
)
⋯
∑
x
2
 
p
(
x
3
∣
x
2
)
τ
(
x
2
)
.
Note that this has the same form as the initial expression, except that we are summing over one fewer variable. We may therefore compute another factor 
τ
(
x
3
)
=
∑
x
2
p
(
x
3
∣
x
2
)
τ
(
x
2
)
, and repeat the process until we are only left with 
x
n
. Since each step takes 
O
(
k
2
)
 time, and we perform 
O
(
n
)
 steps, inference now takes 
O
(
n
k
2
)
 time, which is much better than our naive 
O
(
k
n
)
 solution.

Also, at each time, we are eliminating a variable, which gives the algorithm its name.

Eliminating Variables
Having established some intuitions, with a special case, we now introduce the variable elimination algorithm in its general form.

Factors
We assume that we are given a graphical model as a product of factors

p
(
x
1
,
…
,
x
n
)
=
∏
c
∈
C
 
ϕ
c
(
x
c
)
.
Recall that we can view a factor as a multi-dimensional table assigning a value to each assignment of a set of variables 
x
c
. In a Bayesian network, the factors correspond to conditional probability distributions. In a Markov Random Field, the factors encode an unnormalized distribution; to compute marginals, we first calculate the partition function (also using variable elimination), then we compute marginals using the unnormalized distribution, and finally we divide the result by the partition constant to construct a valid marginal probability.

Factor Operations
The variable elimination algorithm repeatedly performs two factor operations: product and marginalization. We have been implicitly performing these operations in our chain example.

The factor product operation simply defines the product 
ϕ
3
:=
ϕ
1
×
ϕ
2
 of two factors 
ϕ
1
,
ϕ
2
 as

ϕ
3
(
x
c
)
=
ϕ
1
(
x
(
1
)
c
)
×
ϕ
2
(
x
(
2
)
c
)
.
The scope of 
ϕ
3
 is defined as the union of the variables in the scopes of 
ϕ
1
,
ϕ
2
; also 
x
(
i
)
c
 denotes an assignment to the variables in the scope of 
ϕ
i
 defined by the restriction of 
x
c
 to that scope. For example, we define 
ϕ
3
(
a
,
b
,
c
)
:=
ϕ
1
(
a
,
b
)
×
ϕ
2
(
b
,
c
)
.

Next, the marginalization operation “locally” eliminates a set of variables from a factor. If we have a factor 
ϕ
(
X
,
Y
)
 over two sets of variables 
X
,
Y
, marginalizing 
Y
 produces a new factor

τ
(
x
)
=
∑
y
 
ϕ
(
x
,
y
)
,
where the sum is over all joint assignments to the set of variables 
Y
.⊕

We use 
τ
 to refer to the marginalized factor. It is important to understand that this factor does not necessarily correspond to a probability distribution, even if 
ϕ
 was a CPD.

Orderings
Finally, the variable elimination algorithm requires an ordering over the variables according to which variables will be “eliminated.” In our chain example, we took the ordering implied by the DAG. It is important to note that:

Different orderings may dramatically alter the running time of the variable elimination algorithm.
It is NP-hard to find the best ordering.
We will come back to these complications later, but for now let the ordering be fixed.

The variable elimination algorithm
We are now ready to formally define the variable elimination (VE) algorithm. Essentially, we loop over the variables as ordered by 
O
 and eliminate them in that ordering. Intuitively, this corresponds to choosing a sum and “pushing it in” as far as possible inside the product of the factors, as we did in the chain example.

More formally, for each variable 
X
i
 (ordered according to 
O
),

Multiply all factors 
Φ
i
 containing 
X
i
Marginalize out 
X
i
 to obtain a new factor 
τ
Replace the factors 
Φ
i
 with 
τ
A former CS228 student has created an interactive web simulation for visualizing the variable elimination algorithm. Feel free to play around with it and, if you do, please submit any feedback or bugs through the Feedback button on the web app.

Examples
Let’s try to understand what these steps correspond to in our chain example. In that case, the chosen ordering was 
x
1
,
x
2
,
…
,
x
n
−
1
. Starting with 
x
1
, we collected all the factors involving 
x
1
, which were 
p
(
x
1
)
 and 
p
(
x
2
∣
x
1
)
. We then used them to construct a new factor 
τ
(
x
2
)
=
∑
x
1
p
(
x
2
∣
x
1
)
p
(
x
1
)
. This can be seen as the results of steps 1 and 2 of the VE algorithm: first we form a large factor 
σ
(
x
2
,
x
1
)
=
p
(
x
2
∣
x
1
)
p
(
x
1
)
; then we eliminate 
x
1
 from that factor to produce 
τ
. Then, we repeat the same procedure for 
x
2
, except that the factors are now 
p
(
x
3
∣
x
2
)
,
τ
(
x
2
)
.

For a slightly more complex example, recall the graphical model of a student’s grade that we introduced earlier.⊕ The probability specified by the model is of the form

p
(
l
,
g
,
i
,
d
,
s
)
=
p
(
l
∣
g
)
p
(
s
∣
i
)
p
(
i
)
p
(
g
∣
i
,
d
)
p
(
d
)
.
Let’s suppose that we are computing 
p
(
l
)
 and are eliminating variables in their topological ordering in the graph. First, we eliminate 
d
, which corresponds to creating a new factor 
τ
1
(
g
,
i
)
=
∑
d
p
(
g
∣
i
,
d
)
p
(
d
)
. Next, we eliminate 
i
 to produce a factor 
τ
2
(
g
,
s
)
=
∑
i
τ
1
(
g
,
i
)
p
(
i
)
p
(
s
∣
i
)
; then we eliminate 
s
 yielding 
τ
3
(
g
)
=
∑
s
τ
2
(
g
,
s
)
, and so on. Note that these operations are equivalent to summing out the factored probability distribution as follows:

p
(
l
)
=
∑
g
 
p
(
l
∣
g
)
∑
s
  
∑
i
 
p
(
s
∣
i
)
p
(
i
)
∑
d
 
p
(
g
∣
i
,
d
)
p
(
d
)
.
Note that this example requires computing at most 
k
3
 operations per step, since each factor is at most over 2 variables, and one variable is summed out at each step (the dimensionality 
k
 in this example is either 2 or 3).

Introducing evidence
A closely related and equally important problem is computing conditional probabilities of the form

P
(
Y
∣
E
=
e
)
=
P
(
Y
,
E
=
e
)
P
(
E
=
e
)
where 
P
(
X
,
Y
,
E
)
 is a probability distribution, over sets of query variables 
Y
, observed evidence variables 
E
, and unobserved variables 
X
.

We can compute this probability by performing variable elimination once on 
P
(
Y
,
E
=
e
)
 and then once more on 
P
(
E
=
e
)
.

To compute 
P
(
Y
,
E
=
e
)
, we simply take every factor 
ϕ
(
X
′
,
Y
′
,
E
′
)
 which has scope over variables 
E
′
⊆
E
 that are also found in 
E
, and we set their values as specified by 
e
. Then we perform standard variable elimination over 
X
 to obtain a factor over only 
Y
.

Running Time of Variable Elimination
It is very important to understand that the running time of Variable Elimination depends heavily on the structure of the graph.

In the previous example, suppose we eliminated 
g
 first. Then, we would have had to transform the factors 
p
(
g
∣
i
,
d
)
,
ϕ
(
l
∣
g
)
 into a big factor 
τ
(
d
,
i
,
l
)
 over 3 variables, which would require 
O
(
k
4
)
 time to compute: 
k
 times for each of three conditional variables, and 
k
 times for each value of 
g
. If we had a factor 
S
→
G
, then we would have had to eliminate 
p
(
g
∣
s
)
 as well, producing a single giant factor 
τ
(
d
,
i
,
l
,
s
)
 in 
O
(
k
5
)
 time. Then, eliminating any variable from this factor would require almost as much work as if we had started with the original distribution, since all the variables have become coupled.

Clearly some orderings are more efficient than others. In fact, the running time of Variable Elimination is 
O
(
n
k
M
+
1
)
, where 
M
 is the maximum size of any factor 
τ
 formed during the elimination process and 
n
 is the number of variables.

Choosing variable elimination orderings
Unfortunately, choosing the optimal VE ordering is an NP-hard problem. However, in practice, we may resort to the following heuristics:

Min-neighbors: Choose a variable with the fewest dependent variables.
Min-weight: Choose variables to minimize the product of the cardinalities of its dependent variables.
Min-fill: Choose vertices to minimize the size of the factor that will be added to the graph.
These methods often result in reasonably good performance in many interesting settings.

