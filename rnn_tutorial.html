<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recurrent Neural Networks Tutorial</title>
    
    <!-- MathJax configuration for rendering equations -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        h1, h2, h3 {
            color: #2c3e50;
            margin-top: 2em;
        }
        
        h1 {
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 1.5em;
        }
        
        .container {
            background-color: white;
            padding: 2em;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .equation {
            padding: 1em;
            margin: 1em 0;
            background-color: #f5f7f9;
            border-left: 4px solid #3498db;
            overflow-x: auto;
        }
        
        .note {
            padding: 1em;
            margin: 1em 0;
            background-color: #e8f4f8;
            border-left: 4px solid #2980b9;
            border-radius: 4px;
        }
        
        .highlight {
            background-color: #fffacd;
            padding: 0 3px;
            border-radius: 2px;
        }
        
        .figure {
            text-align: center;
            margin: 2em 0;
        }
        
        .figure svg {
            max-width: 100%;
            height: auto;
        }
        
        .figure-caption {
            margin-top: 10px;
            font-style: italic;
            color: #555;
        }
        
        code {
            background-color: #f5f7f9;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', Courier, monospace;
        }
        
        .algorithm {
            background-color: #f8f5ff;
            border-left: 4px solid #9b59b6;
            padding: 1em;
            margin: 1em 0;
            border-radius: 4px;
        }
        
        .toc {
            background-color: #f5f7f9;
            padding: 1.5em;
            border-radius: 4px;
            margin-bottom: 2em;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .toc li {
            margin-bottom: 0.5em;
        }
        
        .toc a {
            text-decoration: none;
            color: #2c3e50;
        }
        
        .toc a:hover {
            color: #3498db;
        }

        .example {
            background-color: #f0f7ff;
            border-left: 4px solid #5698c3;
            padding: 1em;
            margin: 1em 0;
            border-radius: 4px;
        }

        .comparison {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin: 2em 0;
        }

        .comparison-item {
            flex: 1 1 45%;
            min-width: 300px;
            padding: 1em;
            margin: 0.5em;
            background-color: #f9f9f9;
            border-radius: 4px;
        }

        .comparison-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 0.8em;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .comparison {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sequence Modeling: Recurrent and Recursive Neural Networks</h1>
        
        <div class="toc">
            <h3>Table of Contents</h3>
            <ul>
                <li><a href="#introduction">1. Introduction to Sequence Modeling</a></li>
                <li><a href="#unfolding">2. Unfolding Computational Graphs</a></li>
                <li><a href="#architecture">3. Recurrent Neural Networks Architecture</a></li>
                <li><a href="#computation">4. RNN Computational Properties</a></li>
                <li><a href="#training">5. Training RNNs</a></li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction -->
        <section id="introduction">
            <h2>1. Introduction to Sequence Modeling</h2>
            
            <p>Traditional feedforward neural networks have significant limitations when working with sequential data. Unlike humans who can understand sequences naturally, standard neural networks:</p>
            
            <ul>
                <li>Cannot process inputs of variable length</li>
                <li>Don't share parameters across different positions in time</li>
                <li>Cannot leverage statistical strength across sequence elements</li>
            </ul>
            
            <div class="example">
                <p><strong>Example:</strong> Consider these two sentences:</p>
                <p>"I went to Nepal in 2009" and "In 2009, I went to Nepal."</p>
                <p>If we want a machine learning model to extract the year when the narrator went to Nepal, it should recognize "2009" regardless of its position in the sentence (sixth word or second word).</p>
            </div>
            
            <div class="comparison">
                <div class="comparison-item">
                    <div class="comparison-title">Traditional Feedforward Network</div>
                    <p>Has separate parameters for each input feature</p>
                    <p>Needs to learn language rules separately for each position</p>
                    <p>Cannot handle variable-length sequences naturally</p>
                </div>
                
                <div class="comparison-item">
                    <div class="comparison-title">Recurrent Neural Network</div>
                    <p>Shares weights across time steps</p>
                    <p>Learns patterns independent of position</p>
                    <p>Naturally handles variable-length sequences</p>
                </div>
            </div>
            
            <h3>Parameter Sharing and Convolutional Approaches</h3>
            
            <p>A related idea to RNNs is the use of convolution across a 1-D temporal sequence. This convolutional approach forms the foundation for <strong>time-delay neural networks</strong> (Lang and Hinton, 1988; Waibel et al., 1989; Lang et al., 1990).</p>
            
            <div class="figure">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Convolutional approach -->
                    <rect x="50" y="50" width="250" height="80" fill="#e1f5fe" stroke="#01579b" stroke-width="2" rx="5" />
                    <text x="175" y="30" text-anchor="middle" font-size="16" font-weight="bold">Convolutional Approach</text>
                    <text x="175" y="90" text-anchor="middle">Output is a function of small</text>
                    <text x="175" y="110" text-anchor="middle">neighborhood of input elements</text>
                    
                    <!-- Visualization of convolution -->
                    <circle cx="70" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="120" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="170" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="220" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="270" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    
                    <rect x="70" y="160" width="100" height="40" fill="none" stroke="#f44336" stroke-width="2" rx="5" />
                    <rect x="120" y="160" width="100" height="40" fill="none" stroke="#f44336" stroke-width="2" rx="5" opacity="0.6" />
                    <rect x="170" y="160" width="100" height="40" fill="none" stroke="#f44336" stroke-width="2" rx="5" opacity="0.3" />
                    
                    <circle cx="120" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    <circle cx="170" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    <circle cx="220" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    
                    <line x1="70" y1="195" x2="120" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="120" y1="195" x2="120" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="170" y1="195" x2="120" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    
                    <line x1="120" y1="195" x2="170" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="170" y1="195" x2="170" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="220" y1="195" x2="170" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    
                    <line x1="170" y1="195" x2="220" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="220" y1="195" x2="220" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="270" y1="195" x2="220" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    
                    <text x="100" y="280" text-anchor="middle" font-style="italic">Shared kernel</text>
                    <text x="100" y="300" text-anchor="middle" font-style="italic">applied at each position</text>
                    
                    <!-- RNN approach -->
                    <rect x="400" y="50" width="250" height="80" fill="#f3e5f5" stroke="#4a148c" stroke-width="2" rx="5" />
                    <text x="525" y="30" text-anchor="middle" font-size="16" font-weight="bold">Recurrent Approach</text>
                    <text x="525" y="90" text-anchor="middle">Output is a function of the</text>
                    <text x="525" y="110" text-anchor="middle">previous outputs</text>
                    
                    <!-- Visualization of recurrence -->
                    <circle cx="420" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="470" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="520" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="570" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <circle cx="620" cy="180" r="15" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    
                    <circle cx="420" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    <circle cx="470" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    <circle cx="520" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    <circle cx="570" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    <circle cx="620" cy="240" r="15" fill="#b39ddb" stroke="#4527a0" stroke-width="2" />
                    
                    <line x1="420" y1="195" x2="420" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="470" y1="195" x2="470" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="520" y1="195" x2="520" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="570" y1="195" x2="570" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="620" y1="195" x2="620" y2="225" stroke="#1565c0" stroke-width="1.5" />
                    
                    <path d="M 435 240 A 20 20 0 0 0 455 240" fill="none" stroke="#4527a0" stroke-width="1.5" />
                    <path d="M 485 240 A 20 20 0 0 0 505 240" fill="none" stroke="#4527a0" stroke-width="1.5" />
                    <path d="M 535 240 A 20 20 0 0 0 555 240" fill="none" stroke="#4527a0" stroke-width="1.5" />
                    <path d="M 585 240 A 20 20 0 0 0 605 240" fill="none" stroke="#4527a0" stroke-width="1.5" />
                    
                    <text x="525" y="280" text-anchor="middle" font-style="italic">Hidden state passes information</text>
                    <text x="525" y="300" text-anchor="middle" font-style="italic">through the entire sequence</text>
                </svg>
                <div class="figure-caption">Figure 1: Comparison between convolutional and recurrent approaches for sequence modeling</div>
            </div>
            
            <p>While both approaches share parameters across time, they differ in important ways:</p>
            
            <table border="1" style="width: 100%; border-collapse: collapse; margin: 2em 0;">
                <tr style="background-color: #e8f4f8;">
                    <th style="padding: 10px; text-align: left;">Convolutional Approach</th>
                    <th style="padding: 10px; text-align: left;">Recurrent Approach</th>
                </tr>
                <tr>
                    <td style="padding: 10px;">
                        <ul>
                            <li>Applies the same convolution kernel at each time step</li>
                            <li>Output at each time step depends on a small neighborhood of input elements</li>
                            <li>Parameter sharing is shallow</li>
                        </ul>
                    </td>
                    <td style="padding: 10px;">
                        <ul>
                            <li>Each output is a function of previous outputs</li>
                            <li>Uses the same update rule applied to previous outputs</li>
                            <li>Results in parameter sharing through a very deep computational graph</li>
                        </ul>
                    </td>
                </tr>
            </table>
            
            <h3>RNN Notation and Application</h3>
            
            <p>For simplicity, we refer to RNNs as operating on a sequence containing vectors $x^{(t)}$ with the time step index $t$ ranging from 1 to $\tau$. In practice:</p>
            
            <ul>
                <li>Recurrent networks usually operate on minibatches of sequences</li>
                <li>Different sequences in a minibatch may have different lengths $\tau$</li>
                <li>The time step index need not literally refer to time in the real world—sometimes it refers only to position in a sequence</li>
                <li>RNNs may also be applied in two dimensions across spatial data such as images</li>
                <li>RNNs can have connections that go backward in time (bidirectional RNNs), provided the entire sequence is observed before processing</li>
            </ul>
            
            <div class="note">
                <p>RNNs extend the concept of computational graphs to include cycles. These cycles represent the influence of the present value of a variable on its own value at a future time step, allowing us to define recurrent neural networks.</p>
            </div>
        </section>
        
        <!-- Section 2: Unfolding Computational Graphs -->
        <section id="unfolding">
            <h2>2. Unfolding Computational Graphs</h2>
            
            <p>A computational graph is a way to formalize the structure of a set of computations, such as those involved in mapping inputs and parameters to outputs and loss. In this section, we explain the idea of unfolding a recursive or recurrent computation into a computational graph with a repetitive structure, typically corresponding to a chain of events.</p>
            
            <div class="note">
                <p>Unfolding a graph results in the sharing of parameters across a deep network structure, which is a key advantage of recurrent neural networks.</p>
            </div>
            
            <h3>Classical Dynamical Systems</h3>
            
            <p>Consider the classical form of a dynamical system:</p>
            
            <div class="equation">
                $$s^{(t)} = f(s^{(t-1)}; \theta)$$
            </div>
            
            <p>where $s^{(t)}$ is called the state of the system at time $t$.</p>
            
            <p>This equation is recurrent because the definition of $s$ at time $t$ refers back to the same definition at time $t-1$.</p>
            
            <h3>The Unfolding Process</h3>
            
            <p>For a finite number of time steps $\tau$, the graph can be unfolded by applying the definition $\tau-1$ times. For example, if we unfold the equation for $\tau = 3$ time steps, we obtain:</p>
            
            <div class="equation">
                \begin{align}
                s^{(3)} &= f(s^{(2)}; \theta)\\
                &= f(f(s^{(1)}; \theta); \theta)
                \end{align}
            </div>
            
            <p>Unfolding the equation by repeatedly applying the definition in this way has yielded an expression that does not involve recurrence. Such an expression can now be represented by a traditional directed acyclic computational graph.</p>
            
            <div class="figure">
                <svg width="700" height="280" viewBox="0 0 700 280">
                    <!-- Original recurrent form -->
                    <rect x="50" y="30" width="200" height="100" fill="#e8f5e9" stroke="#2e7d32" stroke-width="2" rx="5" />
                    <text x="150" y="15" text-anchor="middle" font-size="16" font-weight="bold">Recurrent Form</text>
                    
                    <circle cx="150" cy="60" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="150" y="65" text-anchor="middle" font-weight="bold">s</text>
                    
                    <circle cx="150" cy="140" r="15" fill="#a5d6a7" stroke="#2e7d32" stroke-width="2" />
                    <text x="150" y="145" text-anchor="middle" font-weight="bold">f</text>
                    
                    <line x1="150" y1="80" x2="150" y2="125" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <!-- Loop back -->
                    <path d="M 165 140 C 200 140 200 60 170 60" fill="none" stroke="#2e7d32" stroke-width="1.5" />
                    <polygon points="170,55 170,65 160,60" fill="#2e7d32" />
                    
                    <!-- Black box for delay -->
                    <rect x="185" y="70" width="10" height="10" fill="black" />
                    
                    <!-- Unfolding arrow -->
                    <text x="350" y="80" text-anchor="middle" font-size="16" font-weight="bold">Unfold</text>
                    <line x1="300" y1="80" x2="400" y2="80" stroke="#333" stroke-width="2" />
                    <polygon points="400,75 400,85 410,80" fill="#333" />
                    
                    <!-- Unfolded form -->
                    <rect x="450" y="30" width="200" height="220" fill="#e8f5e9" stroke="#2e7d32" stroke-width="2" rx="5" />
                    <text x="550" y="15" text-anchor="middle" font-size="16" font-weight="bold">Unfolded Computation Graph</text>
                    
                    <!-- First time step -->
                    <circle cx="500" cy="60" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="500" y="65" text-anchor="middle" font-weight="bold">s⁽¹⁾</text>
                    
                    <circle cx="500" cy="120" r="15" fill="#a5d6a7" stroke="#2e7d32" stroke-width="2" />
                    <text x="500" y="125" text-anchor="middle" font-weight="bold">f</text>
                    
                    <line x1="500" y1="80" x2="500" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <!-- Second time step -->
                    <circle cx="550" cy="160" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="550" y="165" text-anchor="middle" font-weight="bold">s⁽²⁾</text>
                    
                    <circle cx="550" cy="220" r="15" fill="#a5d6a7" stroke="#2e7d32" stroke-width="2" />
                    <text x="550" y="225" text-anchor="middle" font-weight="bold">f</text>
                    
                    <line x1="550" y1="180" x2="550" y2="205" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <!-- Third time step -->
                    <circle cx="600" cy="260" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="600" y="265" text-anchor="middle" font-weight="bold">s⁽³⁾</text>
                    
                    <!-- Connections between time steps -->
                    <line x1="500" y1="135" x2="550" y2="140" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="550" y1="235" x2="600" y2="240" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <!-- Parameter sharing indication -->
                    <text x="470" y="120" text-anchor="end" font-size="12">θ</text>
                    <text x="520" y="220" text-anchor="end" font-size="12">θ</text>
                    <line x1="470" y1="125" x2="520" y2="225" stroke="#ff7043" stroke-width="1.5" stroke-dasharray="5,3" />
                    <text x="495" y="175" text-anchor="middle" font-size="12" fill="#ff7043" font-style="italic">Shared parameters</text>
                </svg>
                <div class="figure-caption">Figure 2: The classical dynamical system, illustrated as an unfolded computational graph. Each node represents the state at some time t and the function f maps the state at t to the state at t+1. The same parameters (θ) are used for all time steps.</div>
            </div>
            
            <h3>Dynamical Systems with External Input</h3>
            
            <p>Let's consider a dynamical system driven by an external signal $x^{(t)}$:</p>
            
            <div class="equation">
                $$s^{(t)} = f(s^{(t-1)}, x^{(t)}; \theta)$$
            </div>
            
            <p>Here we see that the state now contains information about the whole past sequence.</p>
            
            <h3>Recurrent Neural Networks and Hidden States</h3>
            
            <p>To indicate that the state is the hidden units of the network, we rewrite the equation using the variable $h$ to represent the state:</p>
            
            <div class="equation">
                $$h^{(t)} = f(h^{(t-1)}, x^{(t)}; \theta)$$
            </div>
            
            <div class="figure">
                <svg width="700" height="350" viewBox="0 0 700 350">
                    <!-- Recurrent form -->
                    <rect x="50" y="50" width="220" height="150" fill="#e8f5e9" stroke="#2e7d32" stroke-width="2" rx="5" />
                    <text x="160" y="30" text-anchor="middle" font-size="16" font-weight="bold">Recurrent Network</text>
                    
                    <!-- Hidden state h -->
                    <circle cx="160" cy="90" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="160" y="95" text-anchor="middle" font-weight="bold">h</text>
                    
                    <!-- Input x -->
                    <circle cx="110" cy="160" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="110" y="165" text-anchor="middle" font-weight="bold">x</text>
                    
                    <!-- Function f -->
                    <circle cx="160" cy="160" r="15" fill="#a5d6a7" stroke="#2e7d32" stroke-width="2" />
                    <text x="160" y="165" text-anchor="middle" font-weight="bold">f</text>
                    
                    <!-- Connections -->
                    <line x1="160" y1="110" x2="160" y2="145" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="130" y1="160" x2="145" y2="160" stroke="#1565c0" stroke-width="1.5" />
                    
                    <!-- Loop back -->
                    <path d="M 175 160 C 220 160 220 90 180 90" fill="none" stroke="#2e7d32" stroke-width="1.5" />
                    <polygon points="180,85 180,95 170,90" fill="#2e7d32" />
                    
                    <!-- Black box for delay -->
                    <rect x="210" y="100" width="10" height="10" fill="black" />
                    
                    <!-- Unfolding arrow -->
                    <text x="350" y="120" text-anchor="middle" font-size="16" font-weight="bold">Unfold</text>
                    <line x1="300" y1="120" x2="400" y2="120" stroke="#333" stroke-width="2" />
                    <polygon points="400,115 400,125 410,120" fill="#333" />
                    
                    <!-- Unfolded form -->
                    <rect x="440" y="30" width="240" height="300" fill="#e8f5e9" stroke="#2e7d32" stroke-width="2" rx="5" />
                    <text x="560" y="15" text-anchor="middle" font-size="16" font-weight="bold">Unfolded Computation Graph</text>
                    
                    <!-- Time step t-1 -->
                    <circle cx="480" cy="80" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="480" y="85" text-anchor="middle" font-size="12">h⁽ᵗ⁻¹⁾</text>
                    
                    <circle cx="480" cy="140" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="480" y="145" text-anchor="middle" font-size="12">x⁽ᵗ⁻¹⁾</text>
                    
                    <!-- Time step t -->
                    <circle cx="560" cy="80" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="560" y="85" text-anchor="middle" font-size="12">h⁽ᵗ⁾</text>
                    
                    <circle cx="560" cy="140" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="560" y="145" text-anchor="middle" font-size="12">x⁽ᵗ⁾</text>
                    
                    <!-- Time step t+1 -->
                    <circle cx="640" cy="80" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="640" y="85" text-anchor="middle" font-size="12">h⁽ᵗ⁺¹⁾</text>
                    
                    <circle cx="640" cy="140" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="640" y="145" text-anchor="middle" font-size="12">x⁽ᵗ⁺¹⁾</text>
                    
                    <!-- Functions f -->
                    <circle cx="520" cy="180" r="15" fill="#a5d6a7" stroke="#2e7d32" stroke-width="2" />
                    <text x="520" y="185" text-anchor="middle" font-weight="bold">f</text>
                    
                    <circle cx="600" cy="180" r="15" fill="#a5d6a7" stroke="#2e7d32" stroke-width="2" />
                    <text x="600" y="185" text-anchor="middle" font-weight="bold">f</text>
                    
                    <!-- Connections -->
                    <line x1="480" y1="100" x2="520" y2="165" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="480" y1="160" x2="505" y2="180" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="520" y1="195" x2="560" y2="100" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <line x1="560" y1="100" x2="600" y2="165" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="560" y1="160" x2="585" y2="180" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="600" y1="195" x2="640" y2="100" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <!-- Ellipsis for sequence continuation -->
                    <text x="480" y="230" text-anchor="middle" font-size="24">...</text>
                    <text x="560" y="230" text-anchor="middle" font-size="24">...</text>
                    <text x="640" y="230" text-anchor="middle" font-size="24">...</text>
                    
                    <!-- Parameter sharing indication -->
                    <text x="520" y="250" text-anchor="middle" font-size="14" fill="#ff7043" font-style="italic">Shared parameters θ</text>
                    <line x1="520" y1="195" x2="520" y2="230" stroke="#ff7043" stroke-width="1.5" stroke-dasharray="5,3" />
                    <line x1="600" y1="195" x2="600" y2="230" stroke="#ff7043" stroke-width="1.5" stroke-dasharray="5,3" />
                    <line x1="520" y1="230" x2="600" y2="230" stroke="#ff7043" stroke-width="1.5" stroke-dasharray="5,3" />
                    
                    <!-- Time instances explanation -->
                    <text x="560" y="270" text-anchor="middle" font-size="14">Each node is associated with</text>
                    <text x="560" y="290" text-anchor="middle" font-size="14">one particular time instance</text>
                </svg>
                <div class="figure-caption">Figure 3: A recurrent network with inputs. This network processes information from the input x by incorporating it into the state h that is passed forward through time.</div>
            </div>
            
            <p>Typical RNNs will add extra architectural features such as output layers that read information out of the state h to make predictions.</p>
            
            <h3>RNN as a Lossy Summary</h3>
            
            <p>When a recurrent network is trained to perform a task that requires predicting the future from the past, the network typically learns to use $h^{(t)}$ as a kind of lossy summary of the task-relevant aspects of the past sequence of inputs up to t.</p>
            
            <div class="note">
                <p>This summary is necessarily lossy, since it maps an arbitrary length sequence $(x^{(t)}, x^{(t-1)}, x^{(t-2)}, \ldots, x^{(2)}, x^{(1)})$ to a fixed-length vector $h^{(t)}$.</p>
            </div>
            
            <p>Depending on the training criterion, this summary might selectively keep some aspects of the past sequence with more precision than other aspects:</p>
            <ul>
                <li>For statistical language modeling (predicting the next word), it may store only enough information to predict the rest of the sentence</li>
                <li>In autoencoder frameworks, $h^{(t)}$ might need to be rich enough to approximately recover the entire input sequence</li>
            </ul>
            
            <h3>Mathematical Representation of Unfolded Recurrence</h3>
            
            <p>We can represent the unfolded recurrence after t steps with a function $g^{(t)}$:</p>
            
            <div class="equation">
                \begin{align}
                h^{(t)} &= g^{(t)}(x^{(t)}, x^{(t-1)}, x^{(t-2)}, \ldots, x^{(2)}, x^{(1)})\\
                &= f(h^{(t-1)}, x^{(t)}; \theta)
                \end{align}
            </div>
            
            <p>The function $g^{(t)}$ takes the whole past sequence as input and produces the current state, but the unfolded recurrent structure allows us to factorize $g^{(t)}$ into repeated application of a function $f$.</p>
            
            <h3>Key Advantages of Unfolding</h3>
            
            <p>The unfolding process introduces two major advantages:</p>
            
            <ol>
                <li><strong>Consistent Input Size:</strong> Regardless of the sequence length, the learned model always has the same input size, because it is specified in terms of transition from one state to another state, rather than specified in terms of a variable-length history of states.</li>
                <li><strong>Parameter Sharing:</strong> It is possible to use the <em>same</em> transition function $f$ with the same parameters at every time step.</li>
            </ol>
            
            <p>These factors make it possible to learn a single model $f$ that operates on all time steps and all sequence lengths, rather than needing to learn a separate model $g^{(t)}$ for all possible time steps. This allows:</p>
            
            <ul>
                <li>Generalization to sequence lengths that did not appear in the training set</li>
                <li>Model estimation with far fewer training examples than would be required without parameter sharing</li>
            </ul>
            
            <div class="note">
                <p>Both the recurrent graph and the unrolled graph have their uses. The recurrent graph is succinct. The unfolded graph provides an explicit description of which computations to perform and helps to illustrate the flow of information forward in time (computing outputs and losses) and backward in time (computing gradients).</p>
            </div>
        </section>
        
        <!-- Section 3: Recurrent Neural Networks Architecture -->
        <section id="architecture">
            <h2>3. Recurrent Neural Networks Architecture</h2>
            
            <p>Armed with the graph unfolding and parameter sharing ideas, we can design a wide variety of recurrent neural networks. In this section, we explore common RNN architectures and their properties.</p>
            
            <h3>Common RNN Design Patterns</h3>
            
            <p>Some examples of important design patterns for recurrent neural networks include:</p>
            
            <ol>
                <li>RNNs that produce an output at each time step and have recurrent connections between hidden units</li>
                <li>RNNs that produce an output at each time step and have recurrent connections only from the output at one time step to the hidden units at the next time step</li>
                <li>RNNs with recurrent connections between hidden units that read an entire sequence and then produce a single output</li>
            </ol>
            
            <h3>Standard RNN with Outputs at Each Time Step</h3>
            
            <div class="figure">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Recurrent form (left side) -->
                    <rect x="50" y="80" width="220" height="270" fill="#e3f2fd" stroke="#1565c0" stroke-width="2" rx="5" />
                    <text x="160" y="60" text-anchor="middle" font-size="16" font-weight="bold">Recurrent Network</text>
                    
                    <!-- Hidden state h -->
                    <circle cx="160" cy="90" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="160" y="95" text-anchor="middle" font-weight="bold">h</text>
                    
                    <!-- Input x -->
                    <circle cx="110" cy="160" r="20" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="110" y="165" text-anchor="middle" font-weight="bold">x</text>
                    
                    <!-- Output o -->
                    <circle cx="230" cy="120" r="20" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="230" y="125" text-anchor="middle" font-weight="bold">o</text>
                    
                    <!-- Target y -->
                    <circle cx="230" cy="60" r="15" fill="#ffecb3" stroke="#ff8f00" stroke-width="2" />
                    <text x="230" y="65" text-anchor="middle" font-weight="bold">y</text>
                    
                    <!-- Loss L -->
                    <rect x="200" y="30" width="60" height="20" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="5" />
                    <text x="230" y="45" text-anchor="middle" font-weight="bold">L</text>
                    
                    <!-- Connections -->
                    <line x1="230" y1="100" x2="230" y2="80" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="230" y1="60" x2="230" y2="50" stroke="#ff8f00" stroke-width="1.5" />
                    <line x1="160" y1="145" x2="215" y2="120" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="90" y1="260" x2="145" y2="185" stroke="#8e24aa" stroke-width="1.5" />
                    
                    <!-- U, V, W matrices -->
                    <text x="120" y="235" text-anchor="middle" font-weight="bold" fill="#8e24aa">U</text>
                    <text x="190" y="140" text-anchor="middle" font-weight="bold" fill="#1565c0">V</text>
                    <text x="160" y="220" text-anchor="middle" font-weight="bold" fill="#1565c0">W</text>
                    
                    <!-- Loop back for W -->
                    <path d="M 185 170 C 250 170 250 210 160 210 C 70 210 70 170 135 170" fill="none" stroke="#1565c0" stroke-width="2" />
                    <polygon points="135,165 135,175 150,170" fill="#1565c0" />
                    
                    <!-- Black box for delay -->
                    <rect x="175" y="200" width="10" height="10" fill="black" />
                    
                    <!-- Unfolding arrow -->
                    <text x="350" y="200" text-anchor="middle" font-size="16" font-weight="bold">Unfold</text>
                    <line x1="300" y1="200" x2="400" y2="200" stroke="#333" stroke-width="2" />
                    <polygon points="400,195 400,205 410,200" fill="#333" />
                    
                    <!-- Unfolded form (right side) -->
                    <rect x="430" y="80" width="240" height="270" fill="#e3f2fd" stroke="#1565c0" stroke-width="2" rx="5" />
                    <text x="550" y="60" text-anchor="middle" font-size="16" font-weight="bold">Unfolded Computation Graph</text>
                    
                    <!-- Time steps -->
                    <text x="475" y="110" text-anchor="middle" font-size="14" font-style="italic">t-1</text>
                    <text x="550" y="110" text-anchor="middle" font-size="14" font-style="italic">t</text>
                    <text x="625" y="110" text-anchor="middle" font-size="14" font-style="italic">t+1</text>
                    
                    <!-- Hidden states for each time step -->
                    <circle cx="475" cy="170" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="475" y="175" text-anchor="middle" font-size="12">h⁽ᵗ⁻¹⁾</text>
                    
                    <circle cx="550" cy="170" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="550" y="175" text-anchor="middle" font-size="12">h⁽ᵗ⁾</text>
                    
                    <circle cx="625" cy="170" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="625" y="175" text-anchor="middle" font-size="12">h⁽ᵗ⁺¹⁾</text>
                    
                    <!-- Inputs for each time step -->
                    <circle cx="475" cy="280" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="475" y="285" text-anchor="middle" font-size="12">x⁽ᵗ⁻¹⁾</text>
                    
                    <circle cx="550" cy="280" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="550" y="285" text-anchor="middle" font-size="12">x⁽ᵗ⁾</text>
                    
                    <circle cx="625" cy="280" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="625" y="285" text-anchor="middle" font-size="12">x⁽ᵗ⁺¹⁾</text>
                    
                    <!-- Outputs for each time step -->
                    <circle cx="475" cy="130" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="475" y="135" text-anchor="middle" font-size="12">o⁽ᵗ⁻¹⁾</text>
                    
                    <circle cx="550" cy="130" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="550" y="135" text-anchor="middle" font-size="12">o⁽ᵗ⁾</text>
                    
                    <circle cx="625" cy="130" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="625" y="135" text-anchor="middle" font-size="12">o⁽ᵗ⁺¹⁾</text>
                    
                    <!-- Loss functions -->
                    <rect x="462" y="90" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="475" y="103" text-anchor="middle" font-size="10">L⁽ᵗ⁻¹⁾</text>
                    
                    <rect x="537" y="90" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="550" y="103" text-anchor="middle" font-size="10">L⁽ᵗ⁾</text>
                    
                    <rect x="612" y="90" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="625" y="103" text-anchor="middle" font-size="10">L⁽ᵗ⁺¹⁾</text>
                    
                    <!-- Targets y -->
                    <circle cx="475" cy="70" r="12" fill="#ffecb3" stroke="#ff8f00" stroke-width="2" />
                    <text x="475" y="74" text-anchor="middle" font-size="10">y⁽ᵗ⁻¹⁾</text>
                    
                    <circle cx="550" cy="70" r="12" fill="#ffecb3" stroke="#ff8f00" stroke-width="2" />
                    <text x="550" y="74" text-anchor="middle" font-size="10">y⁽ᵗ⁾</text>
                    
                    <circle cx="625" cy="70" r="12" fill="#ffecb3" stroke="#ff8f00" stroke-width="2" />
                    <text x="625" y="74" text-anchor="middle" font-size="10">y⁽ᵗ⁺¹⁾</text>
                    
                    <!-- Connections between nodes -->
                    <!-- For t-1 -->
                    <line x1="475" y1="130" x2="475" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="475" y1="70" x2="475" y2="90" stroke="#ff8f00" stroke-width="1.5" />
                    <line x1="475" y1="150" x2="475" y2="130" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="475" y1="265" x2="475" y2="190" stroke="#8e24aa" stroke-width="1.5" />
                    
                    <!-- For t -->
                    <line x1="550" y1="130" x2="550" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="550" y1="70" x2="550" y2="90" stroke="#ff8f00" stroke-width="1.5" />
                    <line x1="550" y1="150" x2="550" y2="130" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="550" y1="265" x2="550" y2="190" stroke="#8e24aa" stroke-width="1.5" />
                    
                    <!-- For t+1 -->
                    <line x1="625" y1="130" x2="625" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="625" y1="70" x2="625" y2="90" stroke="#ff8f00" stroke-width="1.5" />
                    <line x1="625" y1="150" x2="625" y2="130" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="625" y1="265" x2="625" y2="190" stroke="#8e24aa" stroke-width="1.5" />
                    
                    <!-- Recurrent connections between time steps -->
                    <line x1="495" y1="170" x2="530" y2="170" stroke="#1565c0" stroke-width="2" />
                    <line x1="570" y1="170" x2="605" y2="170" stroke="#1565c0" stroke-width="2" />
                    <polygon points="530,165 530,175 540,170" fill="#1565c0" />
                    <polygon points="605,165 605,175 615,170" fill="#1565c0" />
                    
                    <!-- Parameter sharing labels -->
                    <text x="475" y="240" text-anchor="middle" font-size="12" fill="#8e24aa">U</text>
                    <text x="550" y="240" text-anchor="middle" font-size="12" fill="#8e24aa">U</text>
                    <text x="625" y="240" text-anchor="middle" font-size="12" fill="#8e24aa">U</text>
                    
                    <text x="505" y="160" text-anchor="middle" font-size="12" fill="#1565c0">W</text>
                    <text x="580" y="160" text-anchor="middle" font-size="12" fill="#1565c0">W</text>
                    
                    <text x="450" y="140" text-anchor="middle" font-size="12" fill="#2e7d32">V</text>
                    <text x="525" y="140" text-anchor="middle" font-size="12" fill="#2e7d32">V</text>
                    <text x="600" y="140" text-anchor="middle" font-size="12" fill="#2e7d32">V</text>
                </svg>
                <div class="figure-caption">Figure 4: The computational graph to compute the training loss of a recurrent network that maps an input sequence of x values to a corresponding sequence of output o values. A loss L measures how far each o is from the corresponding training target y.</div>
            </div>
            
            <h3>Forward Propagation in RNNs</h3>
            
            <p>We now develop the forward propagation equations for the RNN depicted above. We assume the hyperbolic tangent activation function for the hidden units, and a softmax output for discrete prediction tasks (e.g., predicting words or characters).</p>
            
            <p>Forward propagation begins with a specification of the initial state $h^{(0)}$. Then, for each time step from $t = 1$ to $t = \tau$, we apply the following update equations:</p>
            
            <div class="equation">
                \begin{align}
                a^{(t)} &= b + Wh^{(t-1)} + Ux^{(t)}\\
                h^{(t)} &= \tanh(a^{(t)})\\
                o^{(t)} &= c + Vh^{(t)}\\
                \hat{y}^{(t)} &= \text{softmax}(o^{(t)})
                \end{align}
            </div>
            
            <p>where the parameters are the bias vectors $b$ and $c$ along with the weight matrices $U$, $V$ and $W$, respectively for input-to-hidden, hidden-to-output and hidden-to-hidden connections.</p>
            
            <div class="note">
                <p>This is an example of a recurrent network that maps an input sequence to an output sequence of the same length. Each output $\hat{y}^{(t)}$ is a vector of normalized probabilities over the possible output values at time step $t$.</p>
            </div>
            
            <h3>Loss Function for Sequence Data</h3>
            
            <p>The total loss for a given sequence of $x$ values paired with a sequence of $y$ values would be the sum of the losses over all the time steps. For example, if $L^{(t)}$ is the negative log-likelihood of $y^{(t)}$ given $x^{(1)}, \ldots, x^{(t)}$, then:</p>
            
            <div class="equation">
                \begin{align}
                L(\{x^{(1)}, \ldots, x^{(\tau)}\}, \{y^{(1)}, \ldots, y^{(\tau)}\}) &= \sum_t L^{(t)}\\
                &= -\sum_t \log p_{\text{model}}(y^{(t)} | \{x^{(1)}, \ldots, x^{(t)}\})
                \end{align}
            </div>
            
            <p>where $p_{\text{model}}(y^{(t)} | \{x^{(1)}, \ldots, x^{(t)}\})$ is given by reading the entry for $y^{(t)}$ from the model's output vector $\hat{y}^{(t)}$.</p>
        </section>
        
        <!-- Section 4: RNN Computational Properties -->
        <section id="computation">
            <h2>4. RNN Computational Properties</h2>
            
            <h3>Turing Completeness of RNNs</h3>
            
            <p>The recurrent neural network described in the previous section is universal in the sense that any function computable by a Turing machine can be computed by such a recurrent network of a finite size.</p>
            
            <div class="note">
                <p>The output can be read from the RNN after a number of time steps that is asymptotically linear in the number of time steps used by the Turing machine and asymptotically linear in the length of the input.</p>
            </div>
            
            <p>Important theoretical results on the computational power of RNNs include:</p>
            
            <ul>
                <li>Siegelmann and Sontag (1991, 1995) proved RNNs can simulate Turing machines</li>
                <li>The functions computable by a Turing machine are discrete, so these results regard exact implementation of the function, not approximations</li>
                <li>When used as a Turing machine, an RNN takes a binary sequence as input and its outputs must be discretized to provide a binary output</li>
                <li>It is possible to compute all functions in this setting using a single specific RNN of finite size (Siegelmann and Sontag, 1995, used 886 units)</li>
            </ul>
            
            <div class="figure">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="18" font-weight="bold">RNNs and Turing Completeness</text>
                    
                    <!-- Turing Machine -->
                    <rect x="100" y="70" width="200" height="100" fill="#f8f9fa" stroke="#343a40" stroke-width="2" rx="5" />
                    <text x="200" y="60" text-anchor="middle" font-size="16" font-weight="bold">Turing Machine</text>
                    
                    <!-- Tape representation -->
                    <rect x="50" y="100" width="300" height="40" fill="#e9ecef" stroke="#343a40" stroke-width="1" />
                    <line x1="80" y1="100" x2="80" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="110" y1="100" x2="110" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="140" y1="100" x2="140" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="170" y1="100" x2="170" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="200" y1="100" x2="200" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="230" y1="100" x2="230" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="260" y1="100" x2="260" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="290" y1="100" x2="290" y2="140" stroke="#343a40" stroke-width="1" />
                    <line x1="320" y1="100" x2="320" y2="140" stroke="#343a40" stroke-width="1" />
                    
                    <!-- Head -->
                    <polygon points="200,90 210,80 190,80" fill="#fd7e14" />
                    
                    <!-- State -->
                    <circle cx="200" cy="170" r="15" fill="#e9ecef" stroke="#343a40" stroke-width="1" />
                    <text x="200" y="175" text-anchor="middle" font-size="14">q</text>
                    
                    <!-- RNN -->
                    <rect x="400" y="70" width="200" height="100" fill="#e3f2fd" stroke="#1565c0" stroke-width="2" rx="5" />
                    <text x="500" y="60" text-anchor="middle" font-size="16" font-weight="bold">Recurrent Neural Network</text>
                    
                    <!-- RNN internal representation -->
                    <circle cx="450" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="450" y="125" text-anchor="middle" font-size="16">h</text>
                    
                    <circle cx="520" cy="90" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="520" y="95" text-anchor="middle" font-size="14">o</text>
                    
                    <circle cx="520" cy="150" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="520" y="155" text-anchor="middle" font-size="14">x</text>
                    
                    <!-- Connections -->
                    <line x1="450" y1="100" x2="505" y2="90" stroke="#1565c0" stroke-width="1.5" />
                    <line x1="520" y1="135" x2="520" y2="105" stroke="#8e24aa" stroke-width="1.5" />
                    
                    <!-- Loop back -->
                    <path d="M 470 120 C 490 160 410 160 430 120" fill="none" stroke="#1565c0" stroke-width="1.5" />
                    <polygon points="430,120 430,130 440,125" fill="#1565c0" />
                    
                    <!-- Equivalence arrows -->
                    <line x1="320 140 L 380 140" fill="none" stroke="#333" stroke-width="1.5" />
                    <polygon points="380,135 380,145 390,140" fill="#333" />
                    <text x="350" y="130" text-anchor="middle" font-size="12">Simpler</text>
                    
                    <!-- Features comparison -->
                    <text x="350" y="220" text-anchor="middle" font-size="14" font-weight="bold">Common Features</text>
                    <text x="350" y="240" text-anchor="middle" font-size="12">Both address vanishing gradients with gating mechanisms</text>
                </svg>
                <div class="figure-caption">Figure 5: Illustration of the relationship between RNNs and Turing machines. Any computation possible on a Turing machine can be performed by a sufficiently powerful RNN.</div>
            </div>
            
            <p>The "input" of the Turing machine is a specification of the function to be computed, so the same network that simulates this Turing machine is sufficient for all problems. The theoretical RNN used for the proof can simulate an unbounded stack by representing its activations and weights with rational numbers of unbounded precision.</p>
            
            <h3>Computational Complexity of RNNs</h3>
            
            <p>Computing the gradient of the loss function for an RNN is an expensive operation:</p>
            
            <ul>
                <li>The gradient computation involves performing a forward propagation pass moving left to right through the unrolled graph, followed by a backward propagation pass moving right to left</li>
                <li>The runtime is O(τ) where τ is the length of the sequence, and cannot be reduced by parallelization because the forward propagation graph is inherently sequential</li>
                <li>Each time step may only be computed after the previous one</li>
                <li>States computed in the forward pass must be stored until they are reused during the backward pass, so the memory cost is also O(τ)</li>
            </ul>
            
            <div class="note">
                <p>The back-propagation algorithm applied to the unrolled graph with O(τ) cost is called <strong>back-propagation through time</strong> or <strong>BPTT</strong>. The network with recurrence between hidden units is thus very powerful but also expensive to train.</p>
            </div>
        </section>
        
        <!-- Section 5: Training RNNs -->
        <section id="training">
            <h2>5. Training RNNs</h2>
            
            <h3>Back-Propagation Through Time (BPTT)</h3>
            
            <p>Training recurrent neural networks involves computing gradients through the unfolded computational graph using an algorithm called <strong>Back-Propagation Through Time (BPTT)</strong>.</p>
            
            <div class="figure">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="18" font-weight="bold">Back-Propagation Through Time</text>
                    
                    <!-- Unfolded network -->
                    <rect x="50" y="50" width="600" height="200" fill="#f8f9fa" stroke="#343a40" stroke-width="2" rx="5" />
                    
                    <!-- Time steps -->
                    <text x="125" y="70" text-anchor="middle" font-size="14" font-style="italic">t-2</text>
                    <text x="225" y="70" text-anchor="middle" font-size="14" font-style="italic">t-1</text>
                    <text x="325" y="70" text-anchor="middle" font-size="14" font-style="italic">t</text>
                    <text x="425" y="70" text-anchor="middle" font-size="14" font-style="italic">t+1</text>
                    <text x="525" y="70" text-anchor="middle" font-size="14" font-style="italic">t+2</text>
                    <text x="625" y="70" text-anchor="middle" font-size="14" font-style="italic">t+3</text>
                    
                    <!-- Hidden states -->
                    <circle cx="125" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="125" y="125" text-anchor="middle" font-size="12">h</text>
                    
                    <circle cx="225" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="225" y="125" text-anchor="middle" font-size="12">h</text>
                    
                    <circle cx="325" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="325" y="125" text-anchor="middle" font-size="12">h</text>
                    
                    <circle cx="425" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="425" y="125" text-anchor="middle" font-size="12">h</text>
                    
                    <circle cx="525" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="525" y="125" text-anchor="middle" font-size="12">h</text>
                    
                    <circle cx="625" cy="120" r="20" fill="#bbdefb" stroke="#1565c0" stroke-width="2" />
                    <text x="625" y="125" text-anchor="middle" font-size="12">h</text>
                    
                    <!-- Forward connections -->
                    <line x1="145" y1="120" x2="205" y2="120" stroke="#1565c0" stroke-width="2" />
                    <line x1="245" y1="120" x2="305" y2="120" stroke="#1565c0" stroke-width="2" />
                    <line x1="345" y1="120" x2="405" y2="120" stroke="#1565c0" stroke-width="2" />
                    <line x1="445" y1="120" x2="505" y2="120" stroke="#1565c0" stroke-width="2" />
                    <line x1="545" y1="120" x2="605" y2="120" stroke="#1565c0" stroke-width="2" />
                    
                    <!-- Outputs -->
                    <circle cx="125" cy="70" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="125" y="74" text-anchor="middle" font-size="12">o</text>
                    
                    <circle cx="225" cy="70" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="225" y="74" text-anchor="middle" font-size="12">o</text>
                    
                    <circle cx="325" cy="70" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="325" y="74" text-anchor="middle" font-size="12">o</text>
                    
                    <circle cx="425" cy="70" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="425" y="74" text-anchor="middle" font-size="12">o</text>
                    
                    <circle cx="525" cy="70" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="525" y="74" text-anchor="middle" font-size="12">o</text>
                    
                    <circle cx="625" cy="70" r="15" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" />
                    <text x="625" y="74" text-anchor="middle" font-size="12">o</text>
                    
                    <!-- Loss functions -->
                    <rect x="112" y="30" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="125" y="43" text-anchor="middle" font-size="10">L</text>
                    
                    <rect x="212" y="30" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="225" y="43" text-anchor="middle" font-size="10">L</text>
                    
                    <rect x="312" y="30" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="325" y="43" text-anchor="middle" font-size="10">L</text>
                    
                    <rect x="412" y="30" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="425" y="43" text-anchor="middle" font-size="10">L</text>
                    
                    <rect x="512" y="30" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="525" y="43" text-anchor="middle" font-size="10">L</text>
                    
                    <rect x="612" y="30" width="26" height="16" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3" />
                    <text x="625" y="43" text-anchor="middle" font-size="10">L</text>
                    
                    <!-- Inputs -->
                    <circle cx="125" cy="170" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="125" y="174" text-anchor="middle" font-size="12">x</text>
                    
                    <circle cx="225" cy="170" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="225" y="174" text-anchor="middle" font-size="12">x</text>
                    
                    <circle cx="325" cy="170" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="325" y="174" text-anchor="middle" font-size="12">x</text>
                    
                    <circle cx="425" cy="170" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="425" y="174" text-anchor="middle" font-size="12">x</text>
                    
                    <circle cx="525" cy="170" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="525" y="174" text-anchor="middle" font-size="12">x</text>
                    
                    <circle cx="625" cy="170" r="15" fill="#e1bee7" stroke="#8e24aa" stroke-width="2" />
                    <text x="625" y="174" text-anchor="middle" font-size="12">x</text>
                    
                    <!-- Forward and backward arrows -->
                    <path d="M 350 220 L 100 220 L 100 190" fill="none" stroke="#1565c0" stroke-width="2" stroke-dasharray="5,3" />
                    <polygon points="350,215 350,225 360,220" fill="#1565c0" />
                    <text x="225" y="210" text-anchor="middle" font-size="14" fill="#1565c0">Forward Pass</text>
                    
                    <path d="M 350 250 L 650 250 L 650 190" fill="none" stroke="#d32f2f" stroke-width="2" stroke-dasharray="5,3" />
                    <polygon points="350,245 350,255 340,250" fill="#d32f2f" />
                    <text x="500" y="240" text-anchor="middle" font-size="14" fill="#d32f2f">Backward Pass</text>
                    
                    <!-- Vertical connections -->
                    <line x1="125" y1="85" x2="125" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="225" y1="85" x2="225" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="325" y1="85" x2="325" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="425" y1="85" x2="425" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="525" y1="85" x2="525" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    <line x1="625" y1="85" x2="625" y2="105" stroke="#2e7d32" stroke-width="1.5" />
                    
                    <line x1="125" y1="155" x2="125" y2="135" stroke="#8e24aa" stroke-width="1.5" />
                    <line x1="225" y1="155" x2="225" y2="135" stroke="#8e24aa" stroke-width="1.5" />
                    <line x1="325" y1="155" x2="325" y2="135" stroke="#8e24aa" stroke-width="1.5" />
                    <line x1="425" y1="155" x2="425" y2="135" stroke="#8e24aa" stroke-width="1.5" />
                    <line x1="525" y1="155" x2="525" y2="135" stroke="#8e24aa" stroke-width="1.5" />
                    <line x1="625" y1="155" x2="625" y2="135" stroke="#8e24aa" stroke-width="1.5" />
                    
                    <line x1="125" y1="30" x2="125" y2="55" stroke="#d32f2f" stroke-width="1.5" />
                    <line x1="225" y1="30" x2="225" y2="55" stroke="#d32f2f" stroke-width="1.5" />
                    <line x1="325" y1="30" x2="325" y2="55" stroke="#d32f2f" stroke-width="1.5" />
                    <line x1="425" y1="30" x2="425" y2="55" stroke="#d32f2f" stroke-width="1.5" />
                    <line x1="525" y1="30" x2="525" y2="55" stroke="#d32f2f" stroke-width="1.5" />
                    <line x1="625" y1="30" x2="625" y2="55" stroke="#d32f2f" stroke-width="1.5" />
                </svg>
                <div class="figure-caption">Figure 6: Back-propagation through time (BPTT) algorithm - forward pass computes the network states and outputs, while the backward pass computes gradients flowing backwards through time.</div>
            </div>
            
            <p>The BPTT algorithm works in two phases:</p>
            
            <ol>
                <li><strong>Forward Pass:</strong> Compute and store all states, outputs, and losses for each time step</li>
                <li><strong>Backward Pass:</strong> Propagate error gradients back through time, computing parameter gradients at each step</li>
            </ol>
            
            <h3>Training Challenges</h3>
            
            <p>Training RNNs presents several unique challenges:</p>
            
            <ul>
                <li><strong>Computational Expense:</strong> As discussed, the computational cost is O(τ) in both time and memory, which makes training on very long sequences challenging</li>
                <li><strong>Vanishing/Exploding Gradients:</strong> RNNs often suffer from vanishing or exploding gradients when trained on long sequences, as errors get propagated through many time steps</li>
                <li><strong>Difficulty in Capturing Long-Term Dependencies:</strong> Due to the vanishing gradient problem, standard RNNs struggle to learn dependencies between temporally distant inputs</li>
            </ul>
            
            <h3>Truncated BPTT</h3>
            
            <p>To address the computational expense of full BPTT, practitioners often use a technique called <strong>Truncated BPTT</strong>. This approach:</p>
            
            <ul>
                <li>Limits the number of time steps to backpropagate through</li>
                <li>Runs forward for k₁ steps, then backpropagates for k₂ steps (where k₂ ≤ k₁)</li>
                <li>Reduces computational cost but may prevent learning of long-term dependencies</li>
            </ul>
            
            <div class="note">
                <p>When using truncated BPTT, hidden states are typically detached from the computational graph after k₂ steps, which prevents gradients from flowing further back in time.</p>
            </div>
            
            <h3>Advanced RNN Architectures</h3>
            
            <p>To address the challenges associated with training basic RNNs, researchers have developed several advanced architectures:</p>
            
            <ul>
                <li><strong>Long Short-Term Memory (LSTM):</strong> Introduces specialized gates to control information flow, helping to mitigate the vanishing gradient problem</li>
                <li><strong>Gated Recurrent Unit (GRU):</strong> A simplified gating mechanism compared to LSTM but with similar benefits</li>
                <li><strong>Bidirectional RNNs:</strong> Process sequences in both forward and backward directions to capture context from both past and future</li>
                <li><strong>Attention Mechanisms:</strong> Allow the model to focus on different parts of the input sequence when producing each output element</li>
            </ul>
            
            <div class="figure">
                <svg width="700" height="250" viewBox="0 0 700 250">
                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="18" font-weight="bold">Advanced RNN Architectures</text>
                    
                    <!-- LSTM box -->
                    <rect x="100" y="60" width="200" height="160" fill="#e3f2fd" stroke="#1565c0" stroke-width="2" rx="5" />
                    <text x="200" y="80" text-anchor="middle" font-size="16" font-weight="bold">LSTM</text>
                    
                    <!-- LSTM components -->
                    <rect x="130" y="100" width="140" height="100" fill="#bbdefb" stroke="#1565c0" stroke-width="1" rx="3" />
                    <line x1="130" y1="130" x2="270" y2="130" stroke="#1565c0" stroke-width="1" />
                    <line x1="130" y1="160" x2="270" y2="160" stroke="#1565c0" stroke-width="1" />
                    <line x1="200" y1="100" x2="200" y2="200" stroke="#1565c0" stroke-width="1" />
                    
                    <text x="165" y="120" text-anchor="middle" font-size="12">Forget Gate</text>
                    <text x="235" y="120" text-anchor="middle" font-size="12">Input Gate</text>
                    <text x="165" y="150" text-anchor="middle" font-size="12">Cell State</text>
                    <text x="235" y="150" text-anchor="middle" font-size="12">Output Gate</text>
                    <text x="165" y="180" text-anchor="middle" font-size="12">Input</text>
                    <text x="235" y="180" text-anchor="middle" font-size="12">Output</text>
                    
                    <!-- GRU box -->
                    <rect x="400" y="60" width="200" height="160" fill="#f3e5f5" stroke="#8e24aa" stroke-width="2" rx="5" />
                    <text x="500" y="80" text-anchor="middle" font-size="16" font-weight="bold">GRU</text>
                    
                    <!-- GRU components -->
                    <rect x="430" y="100" width="140" height="100" fill="#e1bee7" stroke="#8e24aa" stroke-width="1" rx="3" />
                    <line x1="430" y1="150" x2="570" y2="150" stroke="#8e24aa" stroke-width="1" />
                    <line x1="500" y1="100" x2="500" y2="200" stroke="#8e24aa" stroke-width="1" />
                    
                    <text x="465" y="130" text-anchor="middle" font-size="12">Update Gate</text>
                    <text x="535" y="130" text-anchor="middle" font-size="12">Reset Gate</text>
                    <text x="465" y="175" text-anchor="middle" font-size="12">Input</text>
                    <text x="535" y="175" text-anchor="middle" font-size="12">Output</text>
                    
                    <!-- Arrows -->
                    <path d="M 320 140 L 380 140" fill="none" stroke="#333" stroke-width="1.5" />
                    <polygon points="380,135 380,145 390,140" fill="#333" />
                    <text x="350" y="130" text-anchor="middle" font-size="12">Simpler</text>
                    
                    <!-- Features comparison -->
                    <text x="350" y="220" text-anchor="middle" font-size="14" font-weight="bold">Common Features</text>
                    <text x="350" y="240" text-anchor="middle" font-size="12">Both address vanishing gradients with gating mechanisms</text>
                </svg>
                <div class="figure-caption">Figure 7: Comparison of LSTM and GRU architectures, two advanced RNN variants designed to better handle long-term dependencies.</div>
            </div>
            
            <h3>Conclusion</h3>
            
            <p>Recurrent Neural Networks provide a powerful framework for modeling sequential data by sharing parameters across time steps and maintaining internal state. While the basic RNN architecture faces challenges with long sequences, advanced variants like LSTMs and GRUs, along with specialized training techniques, have made RNNs highly effective for a wide range of sequence modeling tasks including:</p>
            
            <ul>
                <li>Natural language processing</li>
                <li>Speech recognition</li>
                <li>Time series prediction</li>
                <li>Music generation</li>
                <li>Video analysis</li>
            </ul>
            
            <p>For more comprehensive information on recurrent neural networks, refer to the textbook by Graves (2012), which provides detailed explanations of various RNN architectures and training methodologies.</p>
        </section>
    </div>
</body>
</html> 