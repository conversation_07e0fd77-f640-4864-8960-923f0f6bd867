in this video i'm going to give a primer  on variational calculus which is an  extremely powerful  and elegant technique similar to  differential calculus  only instead of finding a point that  minimizes a function  we now want to find a function that  minimizes a function  first and in order to understand things  a little bit more clearly in terms of  the chronological order i thought i  would give a brief history  round about 1670 calculus was invented  by  either newton or <PERSON><PERSON><PERSON><PERSON> depending on  who you believe  but it's now thought that they invented  it simultaneously but completely  separately from one another  in 1686 newton publishes his principia  mathematica  which is the first text on classical  mechanics this is where he first  publishes his three laws  and as a result classical or newtonian  mechanics is born  in 1696 johann be<PERSON><PERSON><PERSON> the brother of  jaco<PERSON> be<PERSON><PERSON><PERSON>  posed the brokista crone problem we'll  talk about that a little bit more later  in this video  his brother jaco<PERSON> also spent some time  working on this problem  but it wasn't until 1733 that euler  first elaborated on the brachistochrone  problem  oil in case you weren't aware was quite  the mathematician of his time  then in 1743 the allen burr  guy with the principle he reformulates  in essence  newton's second law as the sum of forces  equal to zero  it's a little bit more involved than  that but that's the basic idea  and then in 1755 a guy named <PERSON><PERSON><PERSON> at  the age of 19  is studying a problem known as the  tortocrone problem  which is a variation of the  brachistochrone problem he is greatly  influenced by euler  and writes to him and at the same time  publishes a paper  explaining a method of analyzing such a  problem  euler is so impressed that he drops his  current method of doing it which  involves geometry  and switches to lagrange's method which  is purely analytical bear in mind  that euler is a man of about 48 49 at  the time and really distinguished  his well-known euler formula had been  published a few years before that  and lagrange is a 19 year old teenager  and yet euler changes everything based  on lagrange's research  he even goes ahead and delays publishing  a paper of his on the subject  to allow lagrange to catch up and start  publishing his own work  because he realized that lagrange has it  right  and just a year later in 1756 euler  publishes a paper  on the calculus of variations as he now  calls it  and this has taken lagrange's equations  and has tried to  extend it and generalize it a little bit  for a much wider range of problems  let's put a box around that since that's  the central point of this  and then in 1788 lagrange publishes his  mechanic analytic  a classical mechanics paper which is the  seminal paper on the subject that has  been published  since newton and lagrange has  fundamentally put forth a different  method of analysis  than newton whereby the equations of  motion can be derived  by considering certain mechanics  problems as minimization problems  more about that later in this video and  then finally several years later in 1834  hamilton is examining specifically  lagrange's form of the problem  where he's minimizing a lagrangian and  he extends it  to all sorts of problems and it has  applications way beyond the classical  mechanics problems  to problems in electromagnetism and  quantum theory  okay enough about the history let's get  down to some business variational  calculus a primer  first of all what is our motivation here  well variational calculus addresses a  wide array of minimization type problems  but for the purposes of our discussion  we're going to reduce this to path  minimization type problems  so consider you have two points we'll  name it a and b  and point a is at a coordinate x 1 y 1  and point b  is at a coordinate x 2 y 2. now  if i said find the minimum length path  between those  you would know immediately that that's a  straight line and that would be the  optimum path  if i was trying to minimize the distance  but what if i was trying to minimize  something else  for example what if i had a chain what  if a chain was hanging  between a and b what shape would that  take  does anyone know well let me ask you  this why  fired a chain like this why does a chain  hang down and not up  it hangs down and nut up because that  minimizes its potential energy so the  chain will hang in a shape that  minimizes its potential energy which is  kind of obvious it would be a u-shape if  it's a symmetric problem but what about  the case of this problem  we might think well that would be a  viable shape  if the path we were choosing was to  minimize the potential energy  as opposed to minimizing the distance we  would expect a chain to hang in a  similar sort of shape to that shown  well what about another sort of problem  the brachistochrome problem suggests  that if i've got say a marble or a ball  rolling down a slope from a to b  what is the shape of the slope that will  minimize the time taken from a to b  assuming it's just falling under gravity  and the answer to that is not  immediately obvious in fact many smart  people have spent a long long time  studying this problem and it was this  problem that gave rise to  the calculus of variations but you might  think  maybe it's a path like the red one that  would be the shortest path  if what we're trying to minimize is the  time taken from a to b  and maybe yet for another problem that  we're trying to minimize this blue path  from a to b might be the minimum path  so the idea of variational calculus is  we're trying to find a path  that minimizes some sort of a function  but what is a path  a path is just a function so in fact  we're trying to find a function that  minimizes a function  and this is the difference between  variational calculus and differential  calculus  in the case of differential calculus  we're finding a point that minimizes a  function  in the case of variational calculus  we're trying to find a path  or a function that minimizes another  function so in general we're dealing  with minimizing path type of problems  so what's one example well the green  path minimizing the distance as an  example  how would we proceed with something like  this in each case we're going to define  an  integral that we're trying to minimize  we'll call this integral i  in all cases and the integral in this  case is just  the integral from x1 to x2  of ds where ds is the incremental path  length  and let's just draw a quick diagram to  remind ourselves if if ds is the  incremental path length  we know from pythagoras that ds squared  is dx squared plus dy squared  we can take a dx squared out and write  it as dx squared times 1  plus y over dx squared and then taking  the square root of both sides gives us  an expression for ds  plugging that in gives us the integral  from x1 to x2  of square root 1 plus y prime for  shorthand squared dx  let's give these some numbers number one  and two a second example  would be as we said to minimize the time  taken from a to b  assuming this is falling under gravity  otherwise known as the brachistochrone  problem  in this case we might define the  integral i as the integral from x1 to x2  of ds divided by the velocity  since the time taken is just the  distance divided by the velocity  and then this can be rewritten as the  integral from x1 to x2  of root 1 plus y prime squared divided  by  root 2g times y1 minus y  dx where the velocity is found by using  the potential and kinetic energies  we'll number that three yet another  minimization type problem might be to  minimize the potential energy  as we mentioned before the hanging rope  problem also commonly known as the  catenary problem  and that is if i hang a rope of a  specific length between those two points  what is the exact shape that it hangs in  and we said it before what we need to  minimize is the potential energy  so our integral in this case i would be  defined as the integral from x1 to x2  of mgh m in this case is row a  and h is y the height ds  we can bring row a out it's row a  integral from x1 to x2  of y root 1 plus y prime squared  dx number that 4 and this is subject to  the constraint that the length of the  rope  is a known given constant so l is equal  to the integral from x1 to  x2 of the incremental path length which  is just 1 plus  y prime squared square root dx  and let's just remind you that this is a  constraint equation  but in principle it's still the same  idea we minimize it the same way  and then finally the fourth example  would be minimizing the lagrangian  and this is what yields lagrange's  equations and we've seen this in  previous videos that we have this  quantity  l called the lagrangian which is the  difference between the kinetic and  potential energy of the system  and it was shown by lagrange and later  expanded upon by hamilton  that in moving from one time to another  a system will follow a path of  stationary points  with respect to the lagrangian that is  to say at any given time it will follow  a path that minimizes the lagrangian  in this case the integral is just equal  to the integral from x1 to x2 of the  lagrangian  which is a function we've seen  previously it's the kinetic minus the  potential energy and in general  will be a function of x y and y prime  dx i remind you that i is a functional  because it is a function of a function i  is a function of y  and its derivative which are functions  okay so now that all the talk is over  let's get down to a little bit of  mathematics  what is the basic idea behind the  calculus of variations  well assuming we have a point x one y y1  and  the second point x2 y2 let's assume  i'm going to draw in black this is the  optimal path from x1 to x2  we're minimizing some integral that this  path is the solution it's the optimal  path  and we're going to call that y of x and  then what we want to do because we want  to consider all other paths is we come  up with some  purely arbitrary path which i'll draw in  red and we call that path eta of x eta  of x  is some random function that goes from  point one to point two  and it's completely arbitrary other than  it must start at point one and it must  end at point two  then we can scale that by a factor we  call epsilon which is some small number  and we call this difference epsilon  times eta and this could be thought of  as the variation  of y so y is the perfect path  and epsilon times eta is the variation  of y  eta is really a shape function an  arbitrary shape function  and epsilon is the magnitude and in this  way  we are in effect parameterizing a whole  family of possible curves  that could be the correct solution and a  reminder here is that the function eta  is twice differentiable  so i'm going to write that y bar  which is just an arbitrary path is the  sum of y which is the perfect  optimum path plus epsilon times eight of  x  so again i've parameterized an entire  family of curves  based on the fact that i get to pick a y  of x which is the ideal part so that  must remain fixed  and then i arbitrarily pick an eta of x  and by shifting the value of epsilon i  can account for a whole family of  arbitrary curves  okay so in effect i'm varying y by  adding this variational bit called  epsilon eta  and this is not all that different to  where we consider differential slices of  x and at the other end x plus dx it's a  little bit like that  dx in that case is considered the  differential  of x epsilon eight is considered the  variation so the variation is like the  differential  but for functionals instead of functions  let's give this a number we're up to  number five  so i'm just going to remind you again  that eta is arbitrary it's the arbitrary  variation of y  but it must satisfy the two boundary  conditions  such that 8x1 and ada at x2 are zero  and you can see this in the figure that  the variation ada has got to be zero at  each of the boundary conditions  because these are geometric boundary  conditions in which case they specified  there can be no variation at the  boundaries as a result of this  and just for good measure because i know  i'm going to need it later i'm going to  take the derivative of equation 5  which says simply that y bar prime this  is the derivative with respect to x  that y bar prime is equal to y prime  plus  epsilon eta prime let's give these  numbers  six and seven okay so based on the  problems i showed in the previous couple  of slides we showed that each of these  integrals can be written  the integral from x1 to x2 of a  functional some  function of in general x y and or y  prime and if you look at the previous  four examples i gave  you'll see that each one of them is a  function of one of these functions or  derivatives  we need a dx and equation eight  all right so here is the crux of it here  is the foundational principle  of the calculus of variations and that  is  if this path y is an optimum path and in  the language of calculus we call it an  extremal  if it's an extremal then it's got to be  a stationary point in other words the  derivative of this function  must be zero just like we would do in  the case of differential calculus we set  the derivative equal to zero so we say  the derivative of i with respect to  epsilon and let me just remind you  that as epsilon goes towards zero let's  look here at equation five as  epsilon goes towards zero y bar goes to  the exact solution  so we expect that in the neighborhood of  epsilon equal to zero  this integral i must have a stationary  point  so let me say that again in order to  minimize  and i'm going to blow the punch line  here these are all minimization problems  but you need to show that through the  second derivative or the second  variation  but in order to find an extremal we need  to set the variation of i  with respect to epsilon to zero and let  me just remind you that when i make this  substitution for y  y of x this is constant because it's the  optimum path  it's an extremal and it doesn't change  this is arbitrary but once we pick it  for the problem remains constant so the  only thing that gets to be varied is  epsilon so this is actually a whole  derivative a complete derivative it's  not a partial derivative  and i'm making a point here it seems  like such a minor detail but  all the explanations i see for this  really gloss over this part of it and  this is really the fundamental concept  of all of this  is that in order to find this minimum  path  we need to set the derivative of this  function i  this integral equal to 0 around that  extremal point  of epsilon equals to zero let me write  it out and it will become a little more  clear  so this is just the derivative with  respect to epsilon  of the integral in equation eight from  x1 to x2  of the functional fdx and that must be  set  equal to zero this is the foundation of  variational calculus  we'll call that equation nine and then  what we can do is we can move this  derivative  inside the integral all i've done is  i've moved this derivative here  and this can be done it's actually  leibniz's rule  and you know when leibniz says something  i mean this is the guy that invented  calculus so  we need to take it seriously according  to leibniz's rule we can bring this  derivative inside the integral  because we have constant integration  points let's call this number 10.  okay so no great mystery here all we've  done is we've said we need to take the  derivative of i  with respect to epsilon and i'll remind  you  that the functional f is now a function  of  y bar and y bar prime because i've gone  ahead in theory  and made this substitution in equation  five  okay i don't want to confuse you too  much now let's go on with it and then  i'm going to come back and review the  whole thing quickly  so turning the page let's copy over  equation 10.  so this is equal to the integral from x1  to x2 of  and in order to take the derivative we  need to take the derivative with respect  to each coordinate so it's partial f  partial x  times partial x partial epsilon but x  is not a function of epsilon so that is  zero  plus partial f partial y bar times  partial y  bar partial epsilon plus partial f  partial y bar prime times partial y bar  prime  partial epsilon at epsilon equals zero  dx equals zero we'll call this equation  eleven  and then from equation five this was  equation five that y bar is y of x  plus epsilon eta if we take the  derivative of y bar with respect to  epsilon  the answer is eta call that number 12  put a box around it  and then similarly from equation 7 if we  take the derivative of y bar prime with  respect to epsilon the answer is eta  prime  call that 13 put a box around it now by  substituting 12 and 13 in here  we can rewrite this as the integral from  x1 to x2  of partial f partial y bar times eta  plus  partial f partial y bar prime times eta  prime  at epsilon equals 0 dx and that must all  equal 0 number 14. so i remind you again  that we need to go ahead and evaluate  this at epsilon equals zero  and most people do not explain this this  is the crux  of what's going on here is that as  epsilon approaches zero  so this variation on the solution  approaches  the exact solution  so let's write that mathematically at  epsilon equals zero  y bar is exactly y and y bar prime is y  prime in other words the solution  approaches the exact solution as epsilon  goes to zero therefore we can rewrite  equation 14 from the previous page  as such where the y bars have now become  ys  and y primes this equation represents  the first variation of  f and is also known to be its weak form  the reason it's its weak form  is because we've got an eta prime here  and not an eta  in order to get this into its strong  form what we need to do  is integrate this term here by parts and  i remind you that by integrating by  parts we can take the derivative of the  eta  and apply it to the d f d y prime  just a little reminder on the side note  that the integral from a to b  of u dv is equal to v times u  evaluated at points a and b minus  don't forget that's a minus sign the  integral from a to b  of v d u so in effect what i've done is  i've transferred the derivative from the  v  to the u over here and in order to do  that i needed to flip the sign  and i needed to get this boundary term  so let's apply that to the problem at  hand this implies that d  f d y prime eta evaluated at x one and x  two and this is zero in both cases  because remember in equation  six we defined it that way we said that  eta at x one and at x two was zero  plus the integral from x1 to x2 of  this part here partial f partial y i'll  take the eta out  minus and we're going to take the  derivative now of this part so d by dx  of partial f partial y prime  times eta times dx and that's going to  be equal to zero  this now appears in its strong form  because we're only multiplying eighteen  we've got rid of the eta prime  and now this is one of the core pieces  of calculus of variations  that because ada is arbitrary let me  just write this the fundamental lemma of  the calculus of variations  but because the eta is arbitrary  therefore  all of that must equal to zero so let's  write that out  partial f partial y minus d by dx  of partial f partial y prime is equal to  zero  this is equation fifteen and that is the  euler lagrange equation  it's considered by many to be one of the  most beautiful equations in all of  classical mechanics  in fact hamilton described it as a  scientific poem  and this formula this equation has  applications  way beyond what it was originally  intended for which was lagrangian  mechanics  and to those of you who have seen the  videos where we've used lagrange's  equations it should be pretty easy to  see  that if we replace our x by time t  and we replace our y's and y primes by  q's and q  dots then this is exactly lagrange's  equations  of course lagrange didn't derive it this  way lagrange got to the same result but  derived it  using the allen bear's principle but as  a result of deriving it  euler was then able to expand on this  whole concept and this is what they came  up with  truly one of the most beautiful  equations in all of mechanics  okay and that's it we're done now i know  for many of you who are perhaps seeing  this just for the first time this has  probably just set your mind spinning  so i'm going to go back through it from  the beginning so that it can flow and we  can look at what the important concepts  are here  going back to the start of the math we  discovered that in many cases we would  like to minimize an integral and i  showed various examples  of minimization problems in particular  we have a special affinity for those  problems that minimize the lagrangian  and we've seen in previous videos  examples of how powerful that can be  so what we did is we assumed that y of x  was the exact solution was the optimal  path or the extremal  we decided to add a slight variation to  that which we called  epsilon times eta we recognize that the  functional that we're dealing with in  general was a function of  x y and y prime in order to find the  extremal  or the optimal path we set the  derivative of i with respect to epsilon  equal to zero in order to take the  derivative  of a function or a functional that is a  function  of x and y-bar and y-bar prime  we took the derivative with respect to  each component individually  we recognized that this one cancelled  out because there was no variance of  x in other words x did not vary with  epsilon at all  and then we further recognize that we  could reduce these derivatives to eta  and eta prime respectively  substituting that into the equation then  gave us the weak  form of the euler lagrange equation  which was this  in order to get it to the strong form we  integrated this term by parts  we applied the boundary conditions here  and i wanted to show you i remind you  that the reason that those boundary  conditions are zero is because we set  them equal to zero  we said that there was no variation at  the boundaries in other words  in order for us to consider all paths we  needed to consider only those paths that  began at  x1 and ended at x2 because those were  geometric boundary conditions therefore  they were prescribed  therefore there's no variation on them  anyway all of that was applied to this  equation  and we found that in the end because eta  was arbitrary which is the fundamental  lemma of calculus of variations  we then found that everything else  multiplying eta  must be equal to zero at all times and  this yield  the euler lagrange equations anyway  that's all i want to say about this  video i hope you found something  interesting in it  if you did please go ahead and smash  those like buttons so that others can  get to see it too  if you have any questions comments  criticisms i'd love to hear from you in  the comment section below  if you would like to be notified of any  future video releases  please hit those subscribe buttons and  click on the bells  thank you very much for watching and  i'll catch up with you in the next video