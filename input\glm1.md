 Generalized linear models∗ (<PERSON><PERSON><PERSON> and <PERSON>, 1972) allow for response dis
tributions other than normal, and for a degree of non-linearity in the model structure.
 AGLM hasthebasic structure
 g(µi) = Xiβ,
 where µi ≡ E(Yi), g is a smooth monotonic ‘link function’, Xi is the ith row of
 a model matrix, X, and β is a vector of unknown parameters. In addition, a GLM
 usually makes the distributional assumptions that the Yi are independent and
 Yi ∼ someexponential family distribution.
 The exponential family of distributions includes many distributions that are useful
 for practical modelling, such as the Poisson, binomial, gamma and normal distri
butions. The comprehensive reference for GLMs is <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (1989),
 while <PERSON><PERSON> and <PERSON> (2008) provides a thorough introduction.
 A generalized linear mixed model (GLMM) follows from a linear mixed model
 in the same way as a GLM follows from a linear model. We now have
 g(µi) = Xiβ +Zib, b ∼N(0,ψ)
 but otherwise the model for Yi is as above. For now, concentrate on the GLM case.
 Because generalized linear models are specified in terms of the ‘linear predictor’,
 η ≡ Xβ, many of the general ideas and concepts of linear modelling carry over,
 with a little modification, to generalized linear modelling. Basic model formulation
 is much the same as for linear models, except that a link function and distribution
 must be chosen. Of course, if the identity function is chosen as the link, along with
 the normal distribution, then ordinary linear models are recovered as a special case.
 The generalization comes at some cost: model fitting now has to be done itera
tively, and distributional results, used for inference, are now approximate and justi
f
 iedbylargesample limiting results, rather than being exact. But beforegoing further
 into these issues, consider a couple of simple examples.
 Example 1: In the early stages of a disease epidemic, the rate at which new cases
 ∗Note that there is a distinction between ‘generalized’ and ‘general’ linear models — the latter term
 being sometimes used to refer to all linear models other than simple straight lines.
 101
102
 GENERALIZEDLINEARMODELS
 occur can often increase exponentially through time. Hence, if µi is the expected
 number of new cases on day ti, a model of the form
 µi = γexp(δti)
 might be appropriate, where γ and δ are unknown parameters. Such a model can be
 turned into GLM form by using a log link so that
 log(µi) = log(γ) + δti = β0 +tiβ1
 (by definition of β0 = log(γ) and β1 = δ). Note that the right hand side of the model
 is now linear in the parameters. The response variable is the number of new cases
 per day and, since this is a count, the Poisson distribution is probably a reasonable
 distribution to try. So the GLM for this situation uses a Poisson response distribution,
 log link, and linear predictor β0 + tiβ1.
 Example 2: The rate of capture of prey items, yi, by a hunting animal, tends to
 increase with increasing density of prey, xi, but to eventually level off, when the
 predator is catching as much as it can cope with. A suitable model for this situation
 might be
 µi = αxi
 h+xi
 ,
 where α is an unknown parameter, representing the maximum capture rate, and h
 is an unknown parameter, representing the prey density at which the capture rate is
 half the maximum rate. Obviously this model is non-linear in its parameters, but, by
 using a reciprocal link, the right hand side can be made linear in the parameters:
 1
 µi 
= 1
 α + h
 α 
1
 xi 
= β0 + 1
 xi
 β1
 (here β0 ≡ 1/α and β1 ≡ h/α). In this case the standard deviation of prey capture
 rate might be approximately proportional to the mean rate, suggesting the use of a
 gammadistribution for the response, and completing the model specification.
 Of course we are not restricted to the simple straight line forms of the examples,
 but can have anystructure for the linear predictor that was possible for linear models.
  3.1 Thetheory of GLMs
 Estimation and inference with GLMs is based on the theory of maximum likelihood
 estimation, although the maximization of the likelihood turns out to require an it
erative least squares approach, related to the method of section 1.8.8 (p. 54). This
 section begins by introducing the exponential family of distributions, which allows a
 general method to be developed for maximizing the likelihood of a GLM. Inference
 for GLMs is then discussed, based on general results of likelihood theory (see sec
tion 2.2, p.74, and appendix A for derivations). In this section it is sometimes useful
 to distinguish between the response data, y, and the random variable of which it is
 an observation, Y , so they are distinguished notationally: this has not been done for
 estimates and estimators.
GLMTHEORY
 103
 3.1.1 The exponential family of distributions
 The response variable in a GLM can have any distribution from the exponential fam
ily. A distribution belongs to the exponential family of distributions if its probability
 density function, or probability mass function, can be written as
 fθ(y) = exp[{yθ −b(θ)}/a(φ) + c(y,φ)],
 where b, a and c are arbitrary functions, φ an arbitrary ‘scale’ parameter, and θ is
 known as the ‘canonical parameter’ of the distribution (in the GLM context, θ will
 completely depend on the model parameters β, but it is not necessary to make this
 explicit yet).
 For example, the normal distribution is a member of the exponential family since
 fµ(y) =
 1
 σ√2π exp −(y−µ)2
 2σ2
 = exp −y2+2yµ−µ2
 2σ2
 = exp yµ−µ2/2
 σ2
 − y2
 −log(σ
 √
 2π)
 2σ2 
−log(σ
 √
 2π) ,
 which is of exponential form, with θ = µ, b(θ) = θ2/2 ≡ µ2/2, a(φ) = φ = σ2 and
 c(φ, y) = −y2/(2φ) − log(√φ2π) ≡ −y2/(2σ2) − log(σ√2π). Table 3.1 gives a
 similar breakdown for the distributions implemented for GLMs in R.
 It is possible to obtain general expressions for the mean and variance of expo
nential family distributions, in terms of a, b and φ. The log likelihood of θ, given a
 particular y, is simply log{fθ(y)} considered as a function of θ. That is
 l(θ) = {yθ −b(θ)}/a(φ) +c(y,φ),
 and so
 ∂l
 ∂θ ={y−b′(θ)}/a(φ).
 Treating l as a random variable, by replacing the particular observation y by the
 random variable Y , enables the expected value of ∂l/∂θ to be evaluated:
 E ∂l
 ∂θ ={E(Y)−b′(θ)}/a(φ).
 Using the general result that E(∂l/∂θ) = 0 (see (A.1) in section A.2) and re
arranging implies that
 E(Y ) = b′(θ),
 (3.1)
 i.e., the mean of any exponential family random variable is given by the first deriva
tive of b w.r.t. θ, where the form of b depends on the particular distribution. This
 equation is the key to linking the GLM model parameters,β, to the canonicalparam
eters of the exponential family. In a GLM, β determines the mean of the response
104 GENERALIZEDLINEARMODELS
 Normal Poisson Binomial Gamma InverseGaussian
 f(y) 1
 σ√2π
 exp −(y−µ)2
 2σ2
 µyexp(−µ)
 y!
 n
 y
 µ
 n
 y 1−µ
 n
 n−y 1
 Γ(ν)
 ν
 µ
 ν
 yν−1exp −νy
 µ
 γ
 2πy3
 exp −γ(y−µ)2
 2µ2y
 Range −∞<y<∞ y=0,1,2,... y=0,1,...,n y>0 y>0
 θ µ log(µ) log µ
 n−µ
 −1
 µ
 −1
 2µ2
 φ σ2 1 1 1
 ν
 1
 γ
 a(φ) φ(=σ2) φ(=1) φ(=1) φ=1
 ν
 φ =1
 γ
 b(θ) θ2
 2
 exp(θ) nlog 1+eθ −log(−θ) −√−2θ
 c(y,φ) −1
 2
 y2
 φ
 +log(2πφ) −log(y!) log n
 y
 νlog(νy)−log{yΓ(ν)} −1
 2
 log(2πy3φ)+ 1
 φy
 V(µ) 1 µ µ(1−µ/n) µ2 µ3
 gc(µ) µ log(µ) log µ
 n−µ
 1
 µ
 1
 µ2
 D(y,ˆ µ) (y−ˆ µ)2 2ylog y
 ˆ µ − 2 ylog y
 ˆ µ + 2 y−ˆ µ
 ˆ µ −log y
 ˆ µ
 (y−ˆ µ)2
 ˆ µ2y
 2(y−ˆ µ) (n−y)log n−y
 n−ˆ µ
 Table3.1 Someexponentialfamilydistributions.Notethatwheny=0,ylog(y/ˆ µ)istakentobezero(itslimitasy→0).
GLMTHEORY
 105
 variable and, via (3.1), thereby determines the canonical parameter for each response
 observation.
 Differentiating the likelihood once more yields
 ∂2l
 ∂θ2 
= −b′′(θ)/a(φ),
 and plugging this into the general result, E(∂2l/∂θ2) = −E{(∂l/∂θ)2} (the deriva
tives are evaluated at the true θ value, see result (A.3), section A.2), gives
 b′′(θ)/a(φ) = E {Y −b′(θ)}2 /a(φ)2,
 which re-arranges to the second useful general result:
 var(Y ) = b′′(θ)a(φ).
 a could in principle be any function of φ, and when working with GLMs there is no
 difficulty in handling any form of a, if φ is known. However, when φ is unknown
 matters become awkward, unless we can write a(φ) = φ/ω, where ω is a known
 constant. This restricted form in fact covers all the cases of practical interest here
 (see, e.g., table 3.1). For example, a(φ) = φ/ω allows the possibility of unequal
 variances in models based on the normal distribution, but in most cases ω is simply
 1. Hence we now have
 var(Y ) = b′′(θ)φ/ω.
 (3.2)
 In subsequent sections it is convenient to write var(Y ) as a function of µ ≡ E(Y ),
 and, since µ and θ are linked via (3.1), we can always define a function V(µ) =
 b′′(θ)/ω, such that var(Y ) = V (µ)φ. Several such functions are listed in