3.2.1 Cumulative Distribution Function
 The PMF is one way to describe the distribution of a discrete random variable. As we
 will see later on, PMF cannot be defined for continuous random variables. The
 cumulative distribution function (CDF) of a random variable is another method to
 describe the distribution of random variables. The advantage of the CDF is that it can
 be defined for any kind of random variable (discrete, continuous, and mixed).
 Definition 3.10
 
The cumulative distribution function (CDF) of random variable 
X
 is defined as
 FX(x) = P(X ≤x), for all x ∈ R.
 Note that the subscript 
X
 indicates that this is the CDF of the random variable 
X
 . Also,
 note that the CDF is defined for all 
x ∈ R
 . Let us look at an example.
 Example 3.9
 
I toss a coin twice. Let 
X
 be the number of observed heads. Find the CDF of 
X
 .
 Solution
 1
 2
 Note that here 
X ∼ Binomial(2, )
 . The range of 
X
 is 
RX = {0,1,2}
 and its PMF is
 given by
 1
 PX(0) = P(X =0)= ,
 4
 1
 PX(1) = P(X =1)= ,
 2
 1
 PX(2) = P(X =2)= .
 4
 To find the CDF, we argue as follows. First, note that if 
x < 0
 , then
 FX(x) = P(X ≤x)=0, for x <0.
Next, if 
x ≥ 2
 ,
 FX(x) = P(X ≤x)=1, for x ≥2.
 Next, if 
0 ≤ x < 1
 ,
 Finally, if 
1 ≤ x < 2
 ,
 1
 FX(x) = P(X ≤x)=P(X =0)= , for 0≤x<1.
 4
 1
 1
 3
 FX(x) = P(X ≤x)=P(X =0)+P(X=1)= + = , for 1≤x<2.
 4
 Thus, to summarize, we have
 FX(x) =
 ⎧
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪⎩
 0
 1
 4
 3
 4
 2
 for x < 0
 for 0 ≤ x <1
 for 1 ≤ x <2
 1
 1
 for x ≥ 2
 4
 Note that when you are asked to find the CDF of a random variable, you need to find
 the function for the entire real line. Also, for discrete random variables, we must be
 careful when to use "
 <
 " or "
 ≤
 ". Figure 3.3 shows the graph of 
FX(x)
 . Note that the
 CDF is flat between the points in 
RX
 and jumps at each value in the range. The size of
 the jump at each point is equal to the probability at that point. For, example, at point 
x =1
 , the CDF jumps from  to . The size of the jump here is 
− =
 which is
 equal to 
PX(1)
 . Also, note that the open and closed circles at point 
x = 1
 indicate that 
FX(1) =
 and not .
 3
 4
 3
 4
 1
 4
 4
 3
 4
 1
 4
 1
 2
 Fig.3.3 - CDF for Example 3.9.
In general, let 
X
 be a discrete random variable with range 
RX = {x1,x2,x3,...}
 , such
 that 
x1 < x2 < x3 <...
 Here, for simplicity, we assume that the range 
RX
 is bounded
 from below, i.e., 
x1
 is the smallest value in 
RX
 . If this is not the case then 
FX(x)
 approaches zero as 
x → −∞
 rather than hitting zero. Figure 3.4 shows the general
 form of the CDF, 
FX(x)
 , for such a random variable. We see that the CDF is in the
 form of a staircase. In particular, note that the CDF starts at 
0
 ; i.e.,
 FX(−∞) = 0
 . Then,
 it jumps at each point in the range. In particular, the CDF stays flat between 
xk
 and 
xk+1
 , so we can write
 FX(x) = FX(xk), for xk ≤ x < xk+1.
 The CDF jumps at each 
xk
 . In particular, we can write
 FX(xk) −FX(xk −ϵ) = PX(xk), For ϵ > 0 small enough.
 Thus, the CDF is always a non-decreasing function, i.e., if 
y ≥ x
 then 
FX(y) ≥ FX(x)
 .
 Finally, the CDF approaches 
1
 as 
x
 becomes large. We can write
 lim
 x→∞
 FX(x) = 1.
 Fig.3.4 - CDF of a discrete random variable.
 Note that the CDF completely describes the distribution of a discrete random variable.
 In particular, we can find the PMF values by looking at the values of the jumps in the
 CDF function. Also, if we have the PMF, we can find the CDF from it. In particular, if 
RX ={x1,x2,x3,...}
 , we can write
 FX(x) = ∑
 xk≤x
 PX(xk).
 Now, let us prove a useful formula.
For all 
a ≤ b
 , we have
 P(a <X≤b)=FX(b)−FX(a)
 To see this, note that for 
a ≤ b
 we have
 P(X ≤b)=P(x≤a)+P(a<X≤b).
 Thus,
 FX(b) = FX(a)+P(a <X ≤b).
 (3.1)
 Again, pay attention to the use of "
 <
 " and "
 ≤
 " as they could make a difference in the
 case of discrete random variables. We will see later that Equation 3.1 is true for all
 types of random variables (discrete, continuous, and mixed). Note that the CDF gives
 us 
P(X ≤ x)
 . To find 
P(X < x)
 , for a discrete random variable, we can simply write
 P(X <x)=P(X≤x)−P(X=x)=FX(x)−PX(x).
 Example 3.10
 
Let 
X
 be a discrete random variable with range 
RX = {1,2,3,...}
 . Suppose the PMF
 of 
X
 is given by
 1
 2k
 PX(k) = for k=1,2,3,...
 a. Find and plot the CDF of 
X
 , 
FX(x)
 .
 b. Find 
P(2 < X ≤ 5)
 .
 c. Find 
P(X > 4)
 .
 Solution
 First, note that this is a valid PMF. In particular,
 ∞
 ∑
 k=1
 a. To find the CDF, note that
 For x <1,
 ∞
 PX(k) =
 ∑
 k=1
 1
 2k
 =1 (geometric sum)
 FX(x) = 0
 .
 For 1 ≤x<2,FX(x) =PX(1) = 
.
 1
 2
For 2≤x<3,FX(x)=PX(1)+PX(2)= + =
 .
 
In general we have
 For 0<k≤x<k+1,
 FX(x)=PX(1)+PX(2)+...+PX(k)
 = + +...+ = .
 Figure 3.5 shows the CDF of 
X
 .
 Fig.3.5 - CDF of random variable given in Example 3.10.
 
b. To find 
P(2<X≤5)
 , we can write
 P(2<X≤5)=FX(5)−FX(2)= − = .
 Or equivalently, we can write
 P(2<X≤5)=PX(3)+PX(4)+PX(5)= + + = ,
 which gives the same answer.
 
c. To find 
P(X>4)
 , we can write
 P(X>4)=1−P(X≤4)=1−FX(4)=1− = .
 1
 2
 1
 4
 3
 4
 1
 2
 1
 4
 1
 2k
 2k
 −1
 2k
 31
 32
 3
 4
 7
 32
 1
 8
 1
 16
 1
 32
 7
 32
 15
 16
 1
 16

3.2.2 Expectation
 If you have a collection of numbers 
a1,a2,...,aN
 , their average is a single number that
 describes the whole collection. Now, consider a random variable 
X
 . We would like to
 define its average, or as it is called in probability, its expected value or mean. The
 expected value is defined as the weighted average of the values in the range.
 Expected value (= mean=average):
 
Definition 3.11
 
Let 
X
 be a discrete random variable with range 
RX = {x1,x2,x3,...}
 (finite or
 countably infinite). The expected value of 
X
 , denoted by 
EX
 is defined as
 EX= ∑
 xk∈RX
 xkP(X =xk) = ∑
 xk∈RX
 xkPX(xk).
 To understand the concept behind 
EX
 , consider a discrete random variable with range
 RX ={x1,x2,x3,...}
 . This random variable is a result of random experiment. Suppose
 that we repeat this experiment a very large number of times 
N
 , and that the trials are
 independent. Let 
N1
 be the number of times we observe 
x1
 , 
N2
 be the number of
 times we observe 
x2
 , ...., 
Nk
 be the number of times we observe 
xk
 , and so on. Since 
P(X =xk)=PX(xk)
 , we expect that
 N1
 PX(x1) ≈ ,
 N
 N2
 PX(x2) ≈ ,
 N
 .
 .
 Nk
 .
 PX(xk) ≈ ,
 N
 .
 .
 .
 In other words, we have 
Nk ≈ NPX(xk)
 . Now, if we take the average of the observed
 values of 
X
 , we obtain
 Average =
 ≈
 N1x1+N2x2+N3x3+...
 N
 x1NPX(x1)+x2NPX(x2)+x3NPX(x3)+...
 N
=x1PX(x1)+x2PX(x2)+x3PX(x3)+...
 =EX.
 Thus, the intuition behind 
EX
 is that if you repeat the random experiment
 independently 
N
 times and take the average of the observed data, the average gets
 closer and closer to 
EX
 as 
N
 gets larger and larger. We sometimes denote 
EX
 by 
μX
 .
 Different notations for expected value of 
X
 : 
EX = E[X] = E(X) = μX
 .
 Let's compute the expected values of some well-known distributions.
 Example 3.11
 
Let 
X ∼ Bernoulli(p)
 . Find 
EX
 .
 Solution
 For the Bernoulli distribution, the range of 
X
 is 
RX = {0,1}
 , and 
PX(1) = p
 and 
PX(0) = 1−p
 . Thus,
 EX=0⋅PX(0)+1⋅PX(1)
 =0⋅(1−p)+1⋅p
 =p
 .
 For a Bernoulli random variable, finding the expectation 
EX
 was easy. However, for
 some random variables, to find the expectation sum, you might need a little algebra.
 Let's look at another example.
 Example 3.12
 
Let 
X ∼ Geometric(p)
 . Find 
EX
 .
 Solution
 For the geometric distribution, the range is 
RX = {1,2,3,...}
 and the PMF is given by
PX(k) = qk−1p,
 for k = 1,2,...
 where, 
0 < p < 1
 and 
q = p−1
 . Thus, we can write
 EX=∑xk∈RX 
xkPX(xk)
 =∑∞
 k=1 kqk−1p
 =p∑∞
 k=1 kqk−1
 .
 Now, we already know the geometric sum formula
 ∞
 ∑
 k=0
 But we need to find a sum 
∑∞
 xk = , for |x|<1.
 1
 1 −x
 k=1 kqk−1
 . Luckily, we can convert the geometric sum to
 the form we want by taking derivative with respect to 
x
 , i.e.,
 ∞
 d
 dx
 Thus, we have
 ∞
 ∑
 k=0
 ∑
 xk = , for |x|<1.
 d
 dx
 1
 1 −x
 kxk−1 = , for |x|<1.
 k=0
 1
 (1 −x)2
 To finish finding the expectation, we can write
 EX=p∑∞
 k=1kqk−1
 =p
 =p
 = 
.
 1
 (1−q)2
 1
 p2
 1
 p
 1
 p
 So, for 
X ∼ Geometric(p)
 , 
EX = 
. Note that this makes sense intuitively. The
 random experiment behind the geometric distribution was that we tossed a coin until
 we observed the first heads, where 
P(H) = p
 . Here, we found out that on average you
 need to toss the coin 
1
 p
 unlikely), then 
1
 p
 times in this experiment. In particular, if 
p
 is small (heads are
 is large, so you need to toss the coin a large number of times before
 you observe a heads. Conversely, for large 
p
 a few coin tosses usually suffices.
 Example 3.13
 
Let 
X ∼ Poisson(λ)
 . Find 
EX
 .
Solution
 Before doing the math, we suggest that you try to guess what the expected value
 would be. It might be a good idea to think about the examples where the Poisson
 distribution is used. For the Poisson distribution, the range is 
RX = {0,1,2,⋯}
 and the
 PMF is given by
 e−λλk
 k!
 PX(k) = , for k=0,1,2,...
 Thus, we can write
 EX=∑xk∈RX 
xkPX(xk)
 =∑∞
 k=0 k
 =e−λ∑∞
 k=1
 e−λλk
 k!
 λk
 (k−1)!
 =e−λ∑∞
 j=0 ( by letting j =k−1)
 =λe−λ ∑∞
 j=0
 λ(j+1)
 j!
 λj
 j!
 =λe−λeλ
 =λ
 .
 ( Taylor series for eλ)
 So the expected value is 
λ
 . Remember, when we first talked about the Poisson
 distribution, we introduced its parameter 
λ
 as the average number of events. So it is
 not surprising that the expected value is 
EX = λ
 .
 Before looking at more examples, we would like to talk about an important property of
 expectation, which is linearity. Note that if 
X
 is a random variable, any function of 
X
 is
 also a random variable, so we can talk about its expected value. For example, if 
Y =aX+b
 , we can talk about 
EY = E[aX +b]
 . Or if you define 
Y =X1+X2+⋯+Xn
 , where 
Xi
 's are random variables, we can talk about
 EY =E[X1 +X2+⋯+Xn]
 . The following theorem states that expectation is linear,
 which makes it easier to calculate the expected value of linear functions of random
 variables.
Expectation is linear:
 
Theorem 3.2
 
We have
 E[aX+b] =aEX+b
 , for all 
a,b ∈ R
 ;
 E[X1 +X2 +⋯+Xn]=EX1+EX2+⋯+EXn
 , for any set of random
 variables 
X1,X2,⋯,Xn
 .
 We will prove this theorem later on in Chapter 5, but here we would like to emphasize
 its importance with an example.
 Example 3.14
 
Let 
X ∼ Binomial(n,p)
 . Find 
EX
 .
 Solution
 We provide two ways to solve this problem. One way is as before: we do the math and
 calculate 
EX = ∑xk∈RX 
xkPX(xk)
 which will be a little tedious. A much faster way
 would be to use linearity of expectation. In particular, remember that if 
X1,X2,...,Xn
 are independent 
Bernoulli(p)
 random variables, then the random variable 
X
 defined
 by 
X =X1 +X2+...+Xn
 has a 
Binomial(n,p)
 distribution. Thus, we can write
 
EX=E[X1 +X2+⋯+Xn]
 =EX1+EX2+⋯+EXn by linearity of expectation
 =p+p+⋯+p
 =np
 .
 We will provide the direct calculation of 
EX = ∑xk∈RX xkPX(xk)
 in the Solved
 Problems section and as you will see it needs a lot more algebra than above. The
 bottom line is that linearity of expectation can sometimes make our calculations much
 easier. Let's look at another example.
 Example 3.15
 
Let 
X ∼ Pascal(m,p)
 . Find 
EX
 . (Hint: Try to write 
X = X1 +X2 +⋯ +Xm
 , such that
 you already know 
EXi
 .)
 Solution
 We claim that if the 
Xi
 's are independent and 
Xi ∼ Geometric(p)
 , for 
i = 1
 , 
2
 , 
⋯
 , 
m
 ,
 then the random variable 
X
 defined by 
X = X1 +X2 +⋯ +Xm
 has 
Pascal(m,p)
 . To
 see this, you can look at 
Problem 5 in Section 3.1.6 and the discussion there. Now,
 1
 p
 1
 p
 1
 since we already know 
EXi = 
, we conclude
 EX=E[X1 +X2+⋯+Xm]
 =EX1+EX2+⋯+EXm by linearity of expectation
 = + +⋯+
 = 
.
 1
 p
 m
 p
 p
 Again, you can try to find 
EX
 directly and as you will see, you need much more
 algebra compared to using the linearity of expectation.
3.2.3 Functions of Random Variables
 If 
X
 is a random variable and 
Y = g(X)
 , then 
Y
 itself is a random variable. Thus, we
 can talk about its PMF, CDF, and expected value. First, note that the range of 
Y
 can be
 written as
 RY ={g(x)|x ∈ RX}.
 If we already know the PMF of 
X
 , to find the PMF of 
Y = g(X)
 , we can write
 PY(y) = P(Y =y)
 =P(g(X) =y)
 = ∑
 PX(x)
 x:g(x)=y
 Let's look at an example.
 Example 3.16
 
1
 5
 Let 
X
 be a discrete random variable with 
PX(k) =
 for 
k = −1,0,1,2,3
 . Let 
Y = 2|X|
 .
 Find the range and PMF of 
Y
 .
 Solution
 First, note that the range of 
Y
 is
 RY ={2|x| where x ∈ RX}
 ={0,2,4,6}
 .
 To find 
PY (y)
 , we need to find 
P(Y = y)
 for 
y = 0,2,4,6
 . We have
 
1
 5
 PY(0) = P(Y =0) =P(2|X|=0)
 =P(X=0)= 
;
 PY(2) = P(Y =2) =P(2|X|=2)
 =P((X =−1) or (X =1))
 =PX(−1)+PX(1) = + = 
;
 1
 5
 1
 5
 2
 5
5
 PY(4) = P(Y =4) =P(2|X|=4)
 =P(X=2)+P(X=−2)= 
;
 PY(6) = P(Y =6) =P(2|X|=6)
 =P(X=3)+P(X=−3)= 
.
 1
 1
 5
 So, to summarize,
 ⎧
 ⎪ 
⎪ 
PY(k) =
 ⎪
 ⎨
 ⎪ 
⎪ 
⎩
 ⎪
 1
 5
 2
 5
 0
 for k = 0,4,6
 for k = 2
 otherwise
 Expected Value of a Function of a Random Variable (LOTUS)
 
Let 
X
 be a discrete random variable with PMF 
PX(x)
 , and let 
Y = g(X)
 . Suppose that
 we are interested in finding 
EY
 . One way to find 
EY
 is to first find the PMF of 
Y
 and
 then use the expectation formula 
EY = E[g(X)] = ∑y∈RY 
yPY(y)
 . But there is another
 way which is usually easier. It is called the law of the unconscious statistician
 (LOTUS).
 Law of the unconscious statistician (LOTUS) for discrete random variables:
 
E[g(X)] = ∑
 g(xk)PX(xk)
 xk∈RX
 (3.2)
 You can prove this by writing 
EY = E[g(X)] = ∑y∈RY yPY(y)
 in terms of 
PX(x)
 . In
 practice it is usually easier to use LOTUS than direct definition when we need 
E[g(X)]
 .
 Example 3.17
 
π
 π
 π
 π
 Let 
X
 be a discrete random variable with range 
RX = {0, , , ,π}
 , such that 
PX(0) = PX( )=PX( )=PX( )=PX(π)= 
. Find 
E[sin(X)]
 .
 4
 4
 Solution
 2
 3π
 4
 1
 5
 2
 3π
 4
 Using LOTUS, we have
 
E[g(X)]=∑xk∈RX
 g(xk)PX(xk)
 =sin(0)⋅ +sin( )⋅ +sin( )⋅ +sin( )⋅ +sin(π)⋅
 =0⋅ + ⋅ +1⋅ + ⋅ +0⋅
 =
 .
 
 
Example 3.18
 Prove 
E[aX+b]=aEX+b
 (linearity of expectation).
 Solution
 Here 
g(X)=aX+b
 , so using LOTUS we have
  
E[aX+b]=∑xk∈RX(axk+b)PX(xk)
 =∑xk∈RXaxkPX(xk)+∑xk∈RXbPX(xk)
 =a∑xk∈RXxkPX(xk)+b∑xk∈RXPX(xk)
 =aEX+b
 .
 
1
 5
 π
 4
 1
 5
 π
 2
 1
 5
 3π
 4
 1
 5
 1
 5
 1
 5
 √2
 2
 1
 5
 1
 5
 √2
 2
 1
 5
 1
 5
 √2+1
 5
3.2.4 Variance
 Consider two random variables 
X
 and 
Y
 with the following PMFs.
 ⎧
 PX(x) =
 ⎪
 ⎨
 ⎩
 ⎪
 PY(y) =
 0.5
 0.5
 0
 0
 for x = −100
 for x = 100
 otherwise
 {1 for y=0
 otherwise
 (3.3)
 (3.4)
 Note that 
EX = EY = 0
 . Although both random variables have the same mean value,
 their distribution is completely different. 
Y
 is always equal to its mean of 
0
 , while 
X
 is
 either 
100
 or 
−100
 , quite far from its mean value. The variance is a measure of how
 spread out the distribution of a random variable is. Here, the variance of 
Y
 is quite
 small since its distribution is concentrated at a single value, while the variance of 
X
 will
 be larger since its distribution is more spread out.
 The variance of a random variable 
X
 , with mean 
EX = μX
 , is defined as
 Var(X) = E[(X−μX)2
 ].
 By definition, the variance of 
X
 is the average value of 
(X − μX)2
 . Since 
(X − μX)2 ≥ 0
 , the variance is always larger than or equal to zero. A large value of the variance
 means that 
(X −μX)2
 is often large, so 
X
 often takes values far from its mean. This
 means that the distribution is very spread out. On the other hand, a low variance
 means that the distribution is concentrated around its average.
 Note that if we did not square the difference between 
X
 and its mean, the result would
 be 
0
 . That is
 E[X−μX]=EX−E[μX]=μX−μX =0.
 X
 is sometimes below its average and sometimes above its average. Thus, 
X − μX
 is
 sometimes negative and sometimes positive, but on average it is zero.
To compute 
Var(X) = E[(X −μX)2]
 , note that we need to find the expected value of 
g(X) =(X−μX)2
 , so we can use LOTUS. In particular, we can write
 Var(X) = E[(X−μX)2
 ] = ∑
 xk∈RX
 (xk −μX)2PX(xk).
 For example, for 
X
 and 
Y
 defined in Equations 3.3 and 3.4, we have
 Var(X) = (−100−0)2(0.5)+(100−0)2(0.5) = 10,000
 Var(Y) = (0−0)2(1) = 0.
 As we expect, 
X
 has a very large variance while Var
 (Y) = 0
 .
 Note that Var
 (X)
 has a different unit than 
X
 . For example, if 
X
 is measured in 
meters
 then Var
 (X)
 is in 
meters2
 . To solve this issue, we define another measure, called the
 standard deviation, usually shown as 
σX
 , which is simply the square root of variance.
 The standard deviation of a random variable 
X
 is defined as
 SD(X) =σX =√
 Var(X).
 The standard deviation of 
X
 has the same unit as 
X
 . For 
X
 and 
Y
 defined in
 Equations 3.3 and 3.4, we have
 σX =√
 10,000 = 100
 σY =√
 0 =0
 .
 Here is a useful formula for computing the variance.
 Computational formula for the variance:
 Var(X) = E[X2]−[EX]2
 To prove it note that
 (3.5)
Var(X)=E[(X−μX)2]
 =E[X2−2μXX+μ2
 X]
 =E[X2]−2E[μXX]+E[μ2
 X] by linearity of expectation.
 
Note that for a given random variable 
X
 , 
μX
 is just a constant real number. Thus,
 E[μXX]=μXE[X]=μ2
 X
 , and 
E[μ2
 X]=μ2
 X
 , so we have
  
Var(X)=E[X2]−2μ2
 X+μ2
 X
 =E[X2]−μ2
 X.
 
quation 3.5 is usually easier to work with compared to 
Var(X)=E[(X−μX)2]
 . To use
 this equation, we can find 
E[X2]=EX2
 using LOTUS
 EX2=∑ xk∈RX
 x2
 kPX(xk),
 and then subtract 
μ2
 X
 to obtain the variance.
 
Example 3.19
 I roll a fair die and let 
X
 be the resulting number. Find 
EX
 , Var
 (X)
 , and 
σX
 .
 Solution
 We have 
RX={1,2,3,4,5,6}
 and 
PX(k)=
 for 
k=1,2,...,6
 . Thus, we have
 EX=1⋅ +2⋅ +3⋅ +4⋅ +5⋅ +6⋅ = ;
 EX2=1⋅ +4⋅ +9⋅ +16⋅ +25⋅ +36⋅ = .
 Thus
 Var(X)=E[X2
 ]−(EX)2
 = −( )
 2
 = − ≈2.92,
 σX=√Var(X)≈√2.92≈1.71
 1
 6
 1
 6
 1
 6
 1
 6
 1
 6
 1
 6
 1
 6
 7
 2
 1
 6
 1
 6
 1
 6
 1
 6
 1
 6
 1
 6
 91
 6
 91
 6
 7
 2
 91
 6
 49
 4
Note that variance is not a linear operator. In particular, we have the following theorem.
 Theorem 3.3
 
For a random variable 
X
 and real numbers 
a
 and 
b
 ,
 Var(aX +b) =a2Var(X)
 Proof
 
If 
Y = aX+b
 , 
EY =aEX+b
 . Thus,
 Var(Y) = E[(Y −EY)2]
 =E[(aX+b−aEX−b)2]
 =E[a2(X−μX)2]
 =a2E[(X−μX)2]
 =a2Var(X)
 (3.6)
 From Equation 3.6, we conclude that, for standard deviation, 
SD(aX + b) = |a|SD(X)
 .
 We mentioned that variance is NOT a linear operation. But there is a very important
 case, in which variance behaves like a linear operation and that is when we look at
 sum of independent random variables.
 Theorem 3.4
 
If 
X1,X2,⋯,Xn
 are independent random variables and 
X = X1 +X2 +⋯ +Xn
 ,
 then
 Var(X) = Var(X1)+Var(X2)+⋯+Var(Xn)
 (3.7)
 We will prove this theorem in Chapter 6, but for now we can look at an example to see
 how we can use it.
 Example 3.20
 
If 
X ∼ Binomial(n,p)
 find Var
 (X)
 .
Solution
 We know that we can write a 
Binomial(n,p)
 random variable as the sum of 
n
 independent 
Bernoulli(p)
 random variables, i.e., 
X = X1 +X2 +⋯ +Xn
 . Thus, we
 conclude
 Var(X) = Var(X1)+Var(X2)+⋯+Var(Xn).
 If 
Xi ∼ Bernoulli(p)
 , then its variance is
 Var(Xi) = E[X2
 i ]−(EXi)2 = 12 ⋅p+02 ⋅(1−p)−p2 = p(1−p).
 Thus,
 Var(X) = p(1−p)+p(1−p)+⋯+p(1−p)
 =np(1−p)
 .
 
3.2.5 Solved Problems:
 More about Discrete Random Variables
 Problem 1
 Let 
X
 be a discrete random variable with the following PMF
 PX(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0.3 for x=3
 0.2 for x=5
 0.3 for x=8
 0.2 for x=10
 0 otherwise
 Find and plot the CDF of 
X
 .
 Solution
 The CDF is defined by 
FX(x)=P(X≤x)
 . We have
 FX(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0 for x<3
 PX(3)=0.3 for 3≤x<5
 PX(3)+PX(5)=0.5 for 5≤x<8
 PX(3)+PX(5)+PX(8)=0.8 for 8≤x<10
 1 for x≥10
 
Problem 2
 Let 
X
 be a discrete random variable with the following PMF
 PX(k)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0.1 for k=0
 0.4 for k=1
 0.3 for k=2
 0.2 for k=3
 0 otherwise
 a. Find 
EX
 .
 b. Find Var
 (X)
 .
 c. If 
Y=(X−2)2
 , find 
EY
 .
Solution
 a. 
EX=∑xk∈RX 
xkPX(xk)
 =0(0.1) +1(0.4) +2(0.3)+3(0.2)
 =1.6
 b. We can use Var
 (X) = EX2 −(EX)2 = EX2 −(1.6)2
 . Thus we need to find 
EX2
 .
 Using LOTUS, we have
 EX2 =02(0.1)+12(0.4)+22(0.3)+32(0.2) = 3.4
 Thus, we have
 Var(X) = (3.4)−(1.6)2 = 0.84
 c. Again, using LOTUS, we have
 E(X−2)2 =(0−2)2(0.1)+(1−2)2(0.4)+(2−2)2(0.3)+(3−2)2(0.2) = 1.
 Problem 3
 
Let 
X
 be a discrete random variable with PMF
 ⎧
 ⎪ 
⎪ 
⎪ 
⎪ 
PX(k) =
 ⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪⎩
 0.2
 0.2
 0.3
 0.3
 0
 for k = 0
 for k = 1
 for k = 2
 for k = 3
 otherwise
 Define 
Y = X(X−1)(X−2)
 . Find the PMF of 
Y
 .
 Solution
 First, note that 
RY = {x(x −1)(x −2)|x ∈ {0,1,2,3}} = {0,6}
 . Thus,
 
PY(0) = P(Y =0) =P((X =0) or (X =1) or (X =2))
 =PX(0)+PX(1)+PX(2)
 =0.7
 ;
 PY(6) = P(X =3)=0.3
 
Thus,
 PY(k)=⎧ ⎪ ⎨ ⎪ ⎩
 0.7 for k=0
 0.3 for k=6
 0 otherwise
 
Problem 4
 
Let 
X∼Geometric(p)
 . Find 
E[ ]
 .
 Solution
 The PMF of 
X
 is given by
 PX(k)={pqk−1 for k=1,2,3,...
 0 otherwise
 where 
q=1−p
 . Thus,
 E[ ]=∑∞
 k=1 PX(k)
 =∑∞
 k=1 qk−1p
 = ∑∞
 k=1( )
 k−1
 =
 =
 .
 
 
Problem 5
 If 
X∼Hypergeometric(b,r,k)
 , find 
EX
 .
 Solution
 The PMF of 
X
 is given by
 1
 2X
 1
 2X
 1
 2k
 1
 2k
 p
 2
 q
 2
 p
 2
 1
 1−
 q
 2
 p
 1+p
⎧
 b
 for x ∈ RX
 x
 ( )( )
 r
 k−x
 PX(x) =
 ⎪
 ⎨
 ⎩
 ⎪
 b+r
 k
 ( )
 0
 otherwise
 where 
RX = {max(0,k−r),max(0,k−r)+1,max(0,k−r)+2,...,min(k,b)}
 . Finding 
EX
 directly seems to be very complicated. So let's try to see if we can find an easier
 way to find 
EX
 . In particular, a powerful tool that we have is linearity of expectation.
 Can we write 
X
 as the sum of simpler random variables 
Xi
 ? To do so, let's remember
 the random experiment behind the hypergeometric distribution. You have a bag that
 contains 
b
 blue marbles and 
r
 red marbles. You choose 
k ≤ b +r
 marbles at random
 (without replacement) and let 
X
 be the number of blue marbles in your sample. In
 particular, let's define the indicator random variables 
Xi
 as follows:
 Xi =
 Then, we can write
 Thus,
 0
 {1 if the ith chosen marble is blue
 otherwise
 X=X1+X2+⋯+Xk.
 EX=EX1+EX2+⋯+EXk.
 To find 
P(Xi = 1)
 , we note that for any particular 
Xi
 all marbles are equally likely to be
 chosen. This is because of symmetry: no marble is more likely to be chosen than the 
i
 th marble as any other marbles. Therefore,
 b
 b +r
 P(Xi =1) = for all i ∈ {1,2,⋯,k}.
 We conclude
 Thus, we have
 EXi =0⋅p(Xi =0)+1⋅P(Xi =1)
 = 
.
 b
 b+r
 EX= .
 kb
 b +r
 Problem 6
 
In 
Example 3.14 we showed that if 
X ∼ Binomial(n,p)
 , then 
EX = np
 . We found this
 by writing 
X
 as the sum of 
n
 
Bernoulli(p)
 random variables. Now, find 
EX
 directly
using 
EX = ∑xk∈RX 
xkPX(xk)
 . Hint: Use 
k( ) = n( )
 .
 n
 k
 Solution
 n
 k
 n−1
 k−1
 n−1
 k−1
 First note that we can prove 
k( ) = n( )
 by the following combinatorial interpretation:
 Suppose that from a group of 
n
 students we would like to choose a committee of 
k
 students, one of whom is chosen to be the committee chair. We can do this
 n
 k
 1. by choosing 
k
 people first (in 
( )
 ways), and then choosing one of them to be the
 chair (
 k
 ways), or
 2. by choosing the chair first (
 n
 possibilities and then choosing 
k − 1
 students from
 the remaining 
n −1
 students (in 
( )
 ways)).
 n−1
 k−1
 Thus, we conclude
 k
 n
 k
 ( ) =n
 ( ) .
 Now, let's find 
EX
 for 
X ∼ Binomial(n,p)
 .
 n −1
 k−1
 n
 k
 n
 k
 EX=∑n
 k=0k( )pkqn−k
 =∑n
 k=1 k( )pkqn−k
 =∑n
 k=1 n( )pkqn−k
 =np∑n
 k=1 ( )pk−1qn−k
 =np∑n−1
 n−1
 k−1
 n−1
 k−1
 n−1
 l
 l=0 ( )plq(n−1)−l
 =np
 .
 Note that the last line is true because the 
∑n−1
 n−1
 l
 l=0 ( )plq(n−1)−l
 is equal to 
∑n−1
 l=0 PY(l)
 for a random variable 
Y
 that has 
Binomial(n − 1,p)
 distribution, hence it is equal to 
1
 .
 Problem 7
 
Let 
X
 be a discrete random variable with 
RX ⊂ {0,1,2,...}
 . Prove
 ∞
 EX=
 Solution
 Note that
 ∑
 k=0
 P(X >k).
 P(X >0) =PX(1)+PX(2)+PX(3)+PX(4)+⋯
 ,
P(X >1) =PX(2)+PX(3)+PX(4)+⋯
 ,
 P(X >2) =PX(3)+PX(4)+PX(5)+⋯
 .
 Thus
 ∑∞
 k=0 P(X > k) =P(X >0)+P(X >1)+P(X >2)+...
 =PX(1)+2PX(2)+3PX(3)+4PX(4)+...
 =EX
 .
 Problem 8
 
If 
X ∼ Poisson(λ)
 , find Var
 (X)
 .
 Solution
 We already know 
EX = λ
 , thus Var
 (X) = EX2 −λ2
 . You can find 
EX2
 directly using
 LOTUS; however, it is a little easier to find 
E[X(X − 1)]
 first. In particular, using
 LOTUS we have
 
E[X(X−1)] =∑∞
 k=0k(k−1)PX(k)
 =∑∞
 k=0 k(k−1)e−λ
 =e−λ
 ∑∞
 k=2
 =e−λλ2 
∑∞
 k=2
 λk
 (k−2)!
 λk−2
 (k−2)!
 =e−λλ2eλ
 =λ2
 .
 λk
 k!
 So, we have 
λ2 = E[X(X −1)] = EX2 −EX =EX2 −λ
 . Thus, 
EX2 =λ2 +λ
 and we
 conclude
 Var(X) = EX2 −(EX)2
 =λ2 +λ−λ2
 =λ
 .
 Problem 9
 
Let 
X
 and 
Y
 be two independent random variables. Suppose that we know Var
 (2X −Y)=6
 and Var
 (X+2Y) =9
 . Find Var
 (X)
 and Var
 (Y)
 .
 Solution
 Let's first make sure we understand what Var
 (2X − Y)
 and Var
 (X +2Y)
 mean. They
 are Var
 (Z)
 and Var
 (W)
 , where the random variables 
Z
 and 
W
 are defined as 
Z =2X−Y
 and 
W =X+2Y
 . Since 
X
 and 
Y
 are independent random variables, then
 2X
 and 
−Y
 are independent random variables. Also, 
X
 and 
2Y
 are independent
 random variables. Thus, by using 
Equation 3.7, we can write
 Var(2X −Y) =Var(2X)+Var(-Y) =4Var(X)+Var(Y) =6,
 Var(X +2Y) =Var(X)+Var(2Y) =Var(X)+4Var(Y) =9.
 By solving for Var
 (X)
 and Var
 (Y)
 , we obtain Var
 (X) = 1
 and Var
 (Y) = 2
 .
3.3 End of Chapter Problems
 Problem 1
 
Let 
X
 be a discrete random variable with the following PMF:
 ⎧
 ⎪ 
⎪ 
⎪ 
⎪ 
PX(x) =
 ⎪ 
⎪
 ⎨
 ⎪ 
⎪ 
⎪ 
⎪ 
⎪ 
⎪⎩
 1
 2
 1
 3
 1
 6
 0
 for x = 0
 for x = 1
 for x = 2
 otherwise
 a. Find 
RX
 , the range of the random variable 
X
 .
 b. Find 
P(X ≥ 1.5)
 .
 c. Find 
P(0 < X < 2)
 .
 d. Find 
P(X = 0|X < 2)
 Problem 2
 
Let 
X
 be the number of the cars being repaired at a repair shop. We have the
 following information:
 At any time, there are at most 
3
 cars being repaired.
 The probability of having 
2
 cars at the shop is the same as the probability of
 having one car.
 The probability of having no car at the shop is the same as the probability of
 having 
3
 cars.
 The probability of having 
1
 or 
2
 cars is half of the probability of having 
0
 or 
3
 cars.
 Find the PMF of 
X
 .
 Problem 3
 
I roll two dice and observe two numbers 
X
 and 
Y
 . If 
Z = X − Y
 , find the range and
 PMF of 
Z
 .
 Problem 4
Let 
X
 and 
Y
 be two independent discrete random variables with the following PMFs:
 PX(k)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 for k=1
 for k=2
 for k=3
 for k=4
 0 otherwise
 and
 PY(k)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 for k=1
 for k=2
 for k=3
 for k=4
 0 otherwise
 a. Find 
P(X≤2 and Y≤2)
 .
 b. Find 
P(X>2 or Y>2)
 .
 c. Find 
P(X>2|Y>2)
 .
 d. Find 
P(X<Y)
 .
 
Problem 5
 
50
 students live in a dormitory. The parking lot has the capacity for 
30
 cars. If each
 student has a car with probability  (independently from other students), what is the
 probability that there won't be enough parking spaces for all the cars?
 
Problem 6 (The Matching Problem)
 
N
 guests arrive at a party. Each person is wearing a hat. We collect all the hats and
 then randomly redistribute the hats, giving each person one of the 
N
 hats randomly.
 Let 
XN
 be the number of people who receive their own hats. Find the PMF of 
XN
 .
 Hint: We previously found that (Problem 7 in Section 2.1.5)
 P(XN=0)= − + −⋯(−1)N
 ,  for  
N=1,2,⋯
 . Using this, find 
P(XN=k)
 for all 
k∈{0,1,⋯N}
 .
 1
 4
 1
 8
 1
 8
 1
 2
 1
 6
 1
 6
 1
 3
 1
 3
 1
 2
 1
 2!
 1
 3!
 1
 4!
 1
 N!
Problem 7
 
For each of the following random variables, find 
P(X > 5)
 , 
P(2 < X ≤ 6)
 and 
P(X >5|X <8)
 .
 1
 5
 1
 1
 3
 a. 
X ∼ Geometric( )
 b. 
X ∼ Binomial(10, )
 c. 
X ∼ Pascal(3, )
 d. 
X ∼ Hypergeometric(10,10,12)
 e. 
X ∼ Poisson(5)
 2
 Problem 8
 
Suppose you take a pass-fail test repeatedly. Let 
Sk
 be the event that you are
 successful in your 
kth
 try, and 
Fk
 be the event that you fail the test in your 
kth
 try. On
 your first try, you have a 
50
 percent chance of passing the test.
 1
 P(S1) = 1−P(F1) = .
 2
 Assume that as you take the test more often, your chance of failing the test goes
 down. In particular,
 1
 P(Fk) = ⋅P(Fk−1),
 2
 for k = 2,3,4,⋯
 However, the result of different exams are independent. Suppose you take the test
 repeatedly until you pass the test for the first time. Let 
X
 be the total number of tests
 you take, so 
Range(X) = {1,2,3,⋯}
 .
 a. Find 
P(X = 1),P(X = 2),P(X = 3)
 .
 b. Find a general formula for 
P(X = k)
 for 
k = 1,2,⋯
 .
 c. Find the probability that you take the test more than 
2
 times.
 d. Given that you take the test more than once, find the probability that you take the
 test exactly twice.
 Problem 9
 
In this problem, we would like to show that the geometric random variable is
 memoryless. Let 
X ∼ Geometric(p)
 . Show that
 P(X >m+l|X>m)=P(X>l), for m,l ∈ {1,2,3,⋯}.
We can interpret this in the following way: Remember that a geometric random
 variable can be obtained by tossing a coin repeatedly until observing the first heads. If
 we toss the coin several times, and do not observe a heads, from now on it is like we
 start all over again. In other words, the failed coin tosses do not impact the distribution
 of waiting time from this point forward. The reason for this is that the coin tosses are
 independent.
 
Problem 10
 An urn consists of 
20
 red balls and 
30
 green balls. We choose 
10
 balls at random from
 the urn. The sampling is done without replacement (repetition not allowed).
 a. What is the probability that there will be exactly 
4
 red balls among the chosen
 balls?
 b. Given that there are at least 
3
 red balls among the chosen balls, what is the
 probability that there are exactly 
4
 red balls?
 
Problem 11
 The number of emails that I get in a weekday (Monday through Friday) can be
 modeled by a Poisson distribution with an average of  emails per minute. The
 number of emails that I receive on weekends (Saturday and Sunday) can be modeled
 by a Poisson distribution with an average of  emails per minute.
 a. What is the probability that I get no emails in an interval of length 
4
 hours on a
 Sunday?
 b. A random day is chosen (all days of the week are equally likely to be selected),
 and a random interval of length one hour is selected on the chosen day. It is
 observed that I did not receive any emails in that interval. What is the probability
 that the chosen day is a weekday?
 
Problem 12
 Let 
X
 be a discrete random variable with the following PMF:
 1
 6
 1
 30
PX(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0.2 for x=−2
 0.3 for x=−1
 0.2 for x=0
 0.2 for x=1
 0.1 for x=2
 0 otherwise
 Find and plot the CDF of 
X
 .
 
Problem 13
 Let 
X
 be a discrete random variable with the following CDF:
 FX(x)=
 ⎧ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪ ⎪⎩
 0 for x<0
 for 0≤x<1
 for 1≤x<2
 for 2≤x<3
 1 for x≥3
 Find the range and PMF of 
X
 .
 
Problem 14
 Let 
X
 be a discrete random variable with the following PMF
 PX(k)=
 ⎧ ⎪ ⎪ ⎪ ⎨ ⎪ ⎪ ⎪⎩
 0.5 for k=1
 0.3 for k=2
 0.2 for k=3
 0 otherwise
 a. Find 
EX
 .
 b. Find Var
 (X)
 and 
SD(X)
 .
 c. If 
Y=
 , find 
EY
 .
 
Problem 15
 Let 
X∼Geometric( )
 , and let 
Y=|X−5|
 . Find the range and PMF of 
Y
 .
 
Problem 16
 Let 
X
 be a discrete random variable with the following PMF
 1
 6
 1
 2
 3
 4
 2
 X
 1
 3
PX(k)=
 ⎧ ⎪ ⎪ ⎨ ⎪ ⎪ ⎩
 for k∈{−10,−9,⋯,−1,0,1,⋯,9,10}
 0 otherwise
 The random variable 
Y=g(X)
 is defined as
 Y=g(X)=⎧ ⎪ ⎨ ⎪ ⎩
 0 if X≤0
 X if 0<X≤5
 5 otherwise
 Find the PMF of 
Y
 .
 
Problem 17
 Let 
X∼Geometric(p)
 . Find 
Var(X)
 .
 
Problem 18
 Let 
X∼Pascal(m,p)
 . Find 
Var(X)
 .
 
Problem 19
 Suppose that 
Y=−2X+3
 . If we know 
EY=1
 and 
EY2=9
 , find 
EX
 and 
Var(X)
 .
 
Problem 20
 There are 
1000
 households in a town. Specifically, there are 
100
 households with one
 member, 
200
 households with 2 members, 
300
 households with 
3
 members, 
200 households with 4 members, 
100
 households with 5 members, and 
100
 households
 with 
6
 members. Thus, the total number of people living in the town is
 N=100⋅1+200⋅2+300⋅3+200⋅4+100⋅5+100⋅6=3300.
 a. We pick a household at random, and define the random variable 
X
 as the
 number of people in the chosen household. Find the PMF and the expected
 value of 
X
 .
 b. We pick a person in the town at random, and define the random variable 
Y
 as
 the number of people in the household where the chosen person lives. Find the
 PMF and the expected value of 
Y
 .
 1
 21
Problem 21(
 Coupon collector's problem [8])
 Suppose that there are N different types of coupons. Each time you get a coupon, it is
 equally likely to be any of the N possible types. Let X be the number of coupons you
 will need to get before having observed each coupon at least once.
 N−i
 N
 a. Show that you can write 
X = X0 +X1 +⋯ +XN−1
 , where 
Xi ∼Geometric( )
 .
 b. Find EX.
 Problem 22 
(St. Petersburg Paradox [9])
 
Here is a famous problem called the St. Petersburg Paradox. Wikipedia states the
 problem as follows: "A casino offers a game of chance for a single player in which a
 fair coin is tossed at each stage. The pot starts at 
1
 dollar and is doubled every time a
 head appears. The first time a tail appears, the game ends and the player wins
 whatever is in the pot. Thus the player wins 1 dollar if a tail appears on the first toss, 2
 dollars if a head appears on the first toss and a tail on the second, 
4
 dollars if a head
 appears on the first two tosses and a tail on the third, 8 dollars if a head appears on
 the first three tosses and a tail on the fourth, and so on. In short, the player wins 
2k−1
 dollars if the coin is tossed 
k
 times until the first tail appears. What would be a fair
 price to pay the casino for entering the game?"
 a. Let 
X
 be the amount of money (in dollars) that the player wins. Find 
EX
 .
 b. What is the probability that the player wins more than 
65
 dollars?
 c. Now suppose that the casino only has a finite amount of money. Specifically,
 suppose that the maximum amount of the money that the casion will pay you is 
230
 dollars (around 1.07 billion dollars). That is, if you win more than 
230
 dollars,
 the casino is going to pay you only 
230
 dollars. Let 
Y
 be the money that the
 player wins in this case. Find 
EY
 .
 Problem 23
 
Let 
X
 be a random variable with mean 
EX = μ
 . Define the function 
f(α)
 as
 f(α) = E[(X−α)2].
 Find the value of 
α
 that minimizes 
f
 .
Problem 24
 
You are offered to play the following game. You roll a fair die once and observe the
 result which is shown by the random variable 
X
 . At this point, you can stop the game
 and win 
X
 dollars. You can also choose to roll the die for the second time to observe
 the value 
Y
 . In this case, you will win 
Y
 dollars. Let 
W
 be the value that you win in this
 game. What strategy do you use to maximize 
EW
 ? What is the maximum 
EW
 you
 can achieve using your strategy?
 Problem 25
 
The median of a random variable 
X
 is defined as any number 
m
 that satisfies both of
 the following conditions:
 1
 P(X ≥m)≥ and P(X≤m)≥
 2
 1
 2
 Note that the median of 
X
 is not necessarily unique. Find the median of 
X
 if
 a. The PMF of 
X
 is given by
 ⎧
 ⎪ 
⎪ 
PX(k) =
 ⎪
 ⎨
 ⎪ 
⎪ 
⎪⎩
 0.4
 0.3
 0.3
 0
 b. 
X
 is the result of a rolling of a fair die.
 c. 
X ∼ Geometric(p)
 , where 
0 < p < 1
 .
 for k = 1
 for k = 2
 for k = 3
 other