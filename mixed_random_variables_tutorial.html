<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mixed Random Variables and the Delta Function - Complete Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .definition-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .mixed-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .type-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .card-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .delta-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .delta-title {
            color: #d63031;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .property-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 3px solid #3498db;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mixed Random Variables</h1>
            <div class="subtitle">Bridging Discrete and Continuous with the Delta Function</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Introduction to Mixed Random Variables</strong> - Neither discrete nor continuous</li>
                <li><strong>Basic Example</strong> - Understanding mixed behavior through transformation</li>
                <li><strong>Properties and Calculations</strong> - Working with mixed CDFs and expectations</li>
                <li><strong>The Dirac Delta Function</strong> - Mathematical foundation for unified theory</li>
                <li><strong>Generalized PDFs</strong> - Unified approach for all random variable types</li>
                <li><strong>Practical Applications</strong> - Real-world examples and problem solving</li>
                <li><strong>Advanced Examples</strong> - Complex mixed distributions</li>
                <li><strong>Problem-Solving Techniques</strong> - Systematic approaches</li>
            </ul>
        </div>

        <h2>🌐 Introduction to Mixed Random Variables</h2>
        
        <div class="concept-box">
            <p><strong>What are Mixed Random Variables?</strong></p>
            <p>Mixed random variables are neither purely discrete nor purely continuous. They have both:</p>
            <ul>
                <li><strong>Continuous parts:</strong> Where the CDF increases smoothly</li>
                <li><strong>Discrete parts:</strong> Where the CDF has jumps (point masses)</li>
            </ul>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Mixed variables combine the best (and challenges) of both worlds."
            </p>
        </div>

        <div class="mixed-types">
            <div class="type-card">
                <div class="card-title">📊 Discrete Random Variables</div>
                <ul>
                    <li><strong>CDF:</strong> Staircase function with jumps</li>
                    <li><strong>PDF:</strong> Not defined (or uses delta functions)</li>
                    <li><strong>PMF:</strong> Gives probabilities at specific points</li>
                    <li><strong>Example:</strong> Number of coin flips</li>
                </ul>
            </div>
            
            <div class="type-card">
                <div class="card-title">📈 Continuous Random Variables</div>
                <ul>
                    <li><strong>CDF:</strong> Smooth, continuous function</li>
                    <li><strong>PDF:</strong> Well-defined everywhere</li>
                    <li><strong>PMF:</strong> Not applicable</li>
                    <li><strong>Example:</strong> Height, weight, temperature</li>
                </ul>
            </div>
            
            <div class="type-card">
                <div class="card-title">🔀 Mixed Random Variables</div>
                <ul>
                    <li><strong>CDF:</strong> Combination of smooth parts and jumps</li>
                    <li><strong>PDF:</strong> Requires delta functions</li>
                    <li><strong>PMF:</strong> Only for discrete components</li>
                    <li><strong>Example:</strong> Insurance claims, device lifetimes</li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <p><strong>Key Challenge:</strong> Traditional PDF and PMF concepts don't directly apply to mixed variables. We need new tools!</p>
        </div>

        <h2>📚 Example 4.14: Basic Mixed Random Variable</h2>

        <div class="example-box">
            <h4>Problem Setup</h4>
            <p>Let $X$ be a continuous random variable with PDF:</p>
            <div class="formula-box">
                $$f_X(x) = \begin{cases}
                2x & \text{for } 0 \leq x \leq 1 \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>

            <p>Define $Y = g(X)$ where:</p>
            <div class="formula-box">
                $$Y = g(X) = \begin{cases}
                X & \text{if } 0 \leq X \leq \frac{1}{2} \\
                \frac{1}{2} & \text{if } X > \frac{1}{2}
                \end{cases}$$
            </div>

            <p><strong>Goal:</strong> Find the CDF of $Y$</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Transformation Creating a Mixed Random Variable
                </text>

                <!-- Left: Original X -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Original X ~ Continuous
                </text>

                <!-- X axis -->
                <line x1="50" y1="180" x2="350" y2="180" stroke="#2c3e50" stroke-width="2"/>
                <text x="200" y="200" text-anchor="middle" font-size="12" fill="#2c3e50">x</text>

                <!-- X PDF -->
                <path d="M 50 180 L 200 80 L 350 180" stroke="#3498db" stroke-width="3" fill="none"/>
                <text x="200" y="100" text-anchor="middle" font-size="12" fill="#3498db" font-weight="bold">f_X(x) = 2x</text>

                <!-- X axis labels -->
                <text x="50" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="200" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">0.5</text>
                <text x="350" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>

                <!-- Vertical lines -->
                <line x1="200" y1="80" x2="200" y2="180" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>

                <!-- Right: Transformed Y -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Transformed Y ~ Mixed
                </text>

                <!-- Y axis -->
                <line x1="450" y1="180" x2="750" y2="180" stroke="#2c3e50" stroke-width="2"/>
                <text x="600" y="200" text-anchor="middle" font-size="12" fill="#2c3e50">y</text>

                <!-- Y transformation -->
                <path d="M 450 180 L 600 80" stroke="#3498db" stroke-width="3" fill="none"/>
                <circle cx="600" cy="80" r="8" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="620" y="85" font-size="10" fill="#e74c3c" font-weight="bold">Point mass at y=1/2</text>

                <!-- Y axis labels -->
                <text x="450" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="600" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">0.5</text>

                <!-- Transformation arrow -->
                <path d="M 350 130 Q 400 100 450 130" stroke="#f39c12" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                <text x="400" y="115" text-anchor="middle" font-size="12" fill="#f39c12" font-weight="bold">g(x)</text>

                <!-- Arrow marker -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7"
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12" />
                    </marker>
                </defs>

                <!-- CDF of Y -->
                <text x="400" y="250" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    CDF of Y (Mixed)
                </text>

                <!-- CDF axes -->
                <line x1="200" y1="350" x2="600" y2="350" stroke="#2c3e50" stroke-width="2"/>
                <line x1="200" y1="350" x2="200" y2="270" stroke="#2c3e50" stroke-width="2"/>

                <!-- CDF curve -->
                <path d="M 200 350 Q 300 320 400 290" stroke="#9b59b6" stroke-width="3" fill="none"/>
                <line x1="400" y1="290" x2="400" y2="280" stroke="#9b59b6" stroke-width="3"/>
                <line x1="400" y1="280" x2="600" y2="280" stroke="#9b59b6" stroke-width="3"/>

                <!-- Jump annotation -->
                <line x1="400" y1="290" x2="400" y2="280" stroke="#e74c3c" stroke-width="4"/>
                <text x="420" y="285" font-size="10" fill="#e74c3c" font-weight="bold">Jump = 3/4</text>

                <!-- CDF labels -->
                <text x="200" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="400" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">0.5</text>
                <text x="600" y="365" text-anchor="middle" font-size="10" fill="#2c3e50">y</text>
                <text x="185" y="280" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>
                <text x="185" y="350" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Key Insight:</strong> The transformation $g(X)$ maps all values $X > \frac{1}{2}$ to the single point $Y = \frac{1}{2}$, creating a point mass!</p>
        </div>

        <div class="example-box">
            <h4>Solution Steps</h4>

            <p><strong>Step 1:</strong> Determine the range of $Y$</p>
            <p>Since $X \in [0,1]$ and the transformation maps:</p>
            <ul>
                <li>$X \in [0, \frac{1}{2}] \rightarrow Y \in [0, \frac{1}{2}]$</li>
                <li>$X \in (\frac{1}{2}, 1] \rightarrow Y = \frac{1}{2}$</li>
            </ul>
            <p>Therefore, $R_Y = [0, \frac{1}{2}]$</p>

            <p><strong>Step 2:</strong> Find the point mass</p>
            <div class="formula-box">
                $$P\left(Y = \frac{1}{2}\right) = P\left(X > \frac{1}{2}\right) = \int_{1/2}^1 2x \, dx = \left[x^2\right]_{1/2}^1 = 1 - \frac{1}{4} = \frac{3}{4}$$
            </div>

            <p><strong>Step 3:</strong> Find CDF for continuous part</p>
            <p>For $0 < y < \frac{1}{2}$:</p>
            <div class="formula-box">
                $$F_Y(y) = P(Y \leq y) = P(X \leq y) = \int_0^y 2x \, dx = y^2$$
            </div>

            <p><strong>Step 4:</strong> Complete CDF</p>
            <div class="formula-box">
                $$F_Y(y) = \begin{cases}
                0 & \text{if } y < 0 \\
                y^2 & \text{if } 0 \leq y < \frac{1}{2} \\
                1 & \text{if } y \geq \frac{1}{2}
                \end{cases}$$
            </div>
        </div>

        <div class="concept-box">
            <p><strong>Decomposing the Mixed CDF:</strong></p>
            <p>We can write $F_Y(y) = C(y) + D(y)$ where:</p>

            <p><strong>Continuous part:</strong> $C(y) = \begin{cases} 0 & y < 0 \\ y^2 & 0 \leq y < \frac{1}{2} \\ \frac{1}{4} & y \geq \frac{1}{2} \end{cases}$</p>

            <p><strong>Discrete part:</strong> $D(y) = \begin{cases} 0 & y < \frac{1}{2} \\ \frac{3}{4} & y \geq \frac{1}{2} \end{cases}$</p>
        </div>

        <h2>🧮 Properties and Calculations</h2>

        <div class="example-box">
            <h4>Example 4.15: Calculations with Mixed Variables</h4>
            <p>Using the mixed random variable $Y$ from Example 4.14, find:</p>

            <p><strong>Part (a):</strong> $P\left(\frac{1}{4} \leq Y \leq \frac{3}{8}\right)$</p>
            <div class="formula-box">
                $$P\left(\frac{1}{4} \leq Y \leq \frac{3}{8}\right) = F_Y\left(\frac{3}{8}\right) - F_Y\left(\frac{1}{4}\right) + P\left(Y = \frac{1}{4}\right)$$
                $$= \left(\frac{3}{8}\right)^2 - \left(\frac{1}{4}\right)^2 + 0 = \frac{9}{64} - \frac{1}{16} = \frac{5}{64}$$
            </div>

            <p><strong>Part (b):</strong> $P\left(Y \geq \frac{1}{2}\right)$</p>
            <div class="formula-box">
                $$P\left(Y \geq \frac{1}{2}\right) = 1 - F_Y\left(\frac{1}{2}^-\right) + P\left(Y = \frac{1}{2}\right)$$
                $$= 1 - \left(\frac{1}{2}\right)^2 = 1 - \frac{1}{4} = \frac{3}{4}$$
            </div>

            <p><strong>Part (c):</strong> $E[Y]$</p>
            <p>For mixed variables, we split the expectation:</p>
            <div class="formula-box">
                $$E[Y] = \int_0^{1/2} y \cdot c(y) \, dy + \frac{1}{2} \cdot P\left(Y = \frac{1}{2}\right)$$
            </div>
            <p>where $c(y) = \frac{dC(y)}{dy} = 2y$ for $0 \leq y \leq \frac{1}{2}$</p>
            <div class="formula-box">
                $$E[Y] = \int_0^{1/2} y \cdot 2y \, dy + \frac{1}{2} \cdot \frac{3}{4} = \int_0^{1/2} 2y^2 \, dy + \frac{3}{8}$$
                $$= \left[\frac{2y^3}{3}\right]_0^{1/2} + \frac{3}{8} = \frac{2}{3} \cdot \frac{1}{8} + \frac{3}{8} = \frac{1}{12} + \frac{3}{8} = \frac{11}{24}$$
            </div>
        </div>

        <div class="highlight">
            <p><strong>General Formula for Mixed Variables:</strong></p>
            <div class="formula-box">
                $$E[Y] = \int_{-\infty}^{\infty} y \cdot c(y) \, dy + \sum_k y_k P(Y = y_k)$$
            </div>
            <p>where $c(y)$ is the derivative of the continuous part and $\{y_k\}$ are the jump points.</p>
        </div>

        <h2>⚡ The Dirac Delta Function</h2>

        <div class="concept-box">
            <p><strong>Motivation:</strong> We want to unify the theory of discrete, continuous, and mixed random variables. The key insight is to find a way to "differentiate" jump discontinuities in CDFs.</p>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "The delta function allows us to treat all random variables with the same mathematical framework."
            </p>
        </div>

        <div class="delta-box">
            <div class="delta-title">🔬 Building the Delta Function</div>

            <p><strong>Step 1:</strong> Start with the unit step function</p>
            <div class="formula-box">
                $$u(x) = \begin{cases}
                1 & \text{if } x \geq 0 \\
                0 & \text{otherwise}
                \end{cases}$$
            </div>

            <p><strong>Step 2:</strong> Create a smooth approximation $u_\alpha(x)$</p>
            <div class="formula-box">
                $$u_\alpha(x) = \begin{cases}
                0 & \text{if } x < -\frac{\alpha}{2} \\
                \frac{1}{\alpha}(x + \frac{\alpha}{2}) & \text{if } -\frac{\alpha}{2} \leq x \leq \frac{\alpha}{2} \\
                1 & \text{if } x > \frac{\alpha}{2}
                \end{cases}$$
            </div>

            <p><strong>Step 3:</strong> Define $\delta_\alpha(x) = \frac{du_\alpha(x)}{dx}$</p>
            <div class="formula-box">
                $$\delta_\alpha(x) = \begin{cases}
                \frac{1}{\alpha} & \text{if } |x| < \frac{\alpha}{2} \\
                0 & \text{if } |x| > \frac{\alpha}{2}
                \end{cases}$$
            </div>

            <p><strong>Step 4:</strong> Take the limit as $\alpha \to 0$</p>
            <div class="formula-box">
                $$\delta(x) = \lim_{\alpha \to 0} \delta_\alpha(x)$$
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="350" viewBox="0 0 800 350">
                <!-- Background -->
                <rect width="800" height="350" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Evolution of the Delta Function
                </text>

                <!-- Step function -->
                <text x="150" y="60" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Unit Step u(x)
                </text>

                <!-- Step function plot -->
                <line x1="50" y1="150" x2="250" y2="150" stroke="#2c3e50" stroke-width="1"/>
                <line x1="150" y1="80" x2="150" y2="220" stroke="#2c3e50" stroke-width="1"/>

                <line x1="50" y1="180" x2="150" y2="180" stroke="#3498db" stroke-width="3"/>
                <line x1="150" y1="120" x2="250" y2="120" stroke="#3498db" stroke-width="3"/>
                <line x1="150" y1="180" x2="150" y2="120" stroke="#e74c3c" stroke-width="3"/>

                <text x="150" y="240" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="100" y="185" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="200" y="125" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>

                <!-- Smooth approximation -->
                <text x="400" y="60" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Smooth u_α(x)
                </text>

                <!-- Smooth function plot -->
                <line x1="300" y1="150" x2="500" y2="150" stroke="#2c3e50" stroke-width="1"/>
                <line x1="400" y1="80" x2="400" y2="220" stroke="#2c3e50" stroke-width="1"/>

                <line x1="300" y1="180" x2="380" y2="180" stroke="#3498db" stroke-width="3"/>
                <path d="M 380 180 Q 400 150 420 120" stroke="#3498db" stroke-width="3" fill="none"/>
                <line x1="420" y1="120" x2="500" y2="120" stroke="#3498db" stroke-width="3"/>

                <text x="400" y="240" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="380" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">-α/2</text>
                <text x="420" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">α/2</text>

                <!-- Delta approximation -->
                <text x="650" y="60" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    δ_α(x) = du_α/dx
                </text>

                <!-- Delta function plot -->
                <line x1="550" y1="150" x2="750" y2="150" stroke="#2c3e50" stroke-width="1"/>
                <line x1="650" y1="80" x2="650" y2="220" stroke="#2c3e50" stroke-width="1"/>

                <rect x="630" y="100" width="40" height="80" fill="#e74c3c" opacity="0.7" stroke="#e74c3c" stroke-width="2"/>
                <text x="650" y="95" text-anchor="middle" font-size="10" fill="#e74c3c" font-weight="bold">1/α</text>

                <text x="650" y="240" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                <text x="630" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">-α/2</text>
                <text x="670" y="195" text-anchor="middle" font-size="10" fill="#2c3e50">α/2</text>

                <!-- Arrows showing progression -->
                <path d="M 260 150 Q 280 140 290 150" stroke="#f39c12" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                <path d="M 510 150 Q 530 140 540 150" stroke="#f39c12" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>

                <!-- Limit notation -->
                <text x="400" y="280" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    δ(x) = lim[α→0] δ_α(x)
                </text>

                <!-- Properties -->
                <rect x="50" y="300" width="700" height="40" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="400" y="320" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Key Properties: δ(x) = ∞ at x=0, δ(x) = 0 elsewhere, ∫δ(x)dx = 1
                </text>
            </svg>
        </div>

        <div class="definition-box">
            <h3>Definition 4.3: Properties of the Delta Function</h3>
            <div class="properties-grid">
                <div class="property-item">
                    <strong>1. Point behavior:</strong><br>
                    $\delta(x) = \begin{cases} \infty & x = 0 \\ 0 & \text{otherwise} \end{cases}$
                </div>
                <div class="property-item">
                    <strong>2. Derivative relation:</strong><br>
                    $\delta(x) = \frac{d}{dx}u(x)$
                </div>
                <div class="property-item">
                    <strong>3. Unit integral:</strong><br>
                    $\int_{-\epsilon}^{\epsilon} \delta(x) dx = 1$ for any $\epsilon > 0$
                </div>
                <div class="property-item">
                    <strong>4. Sifting property:</strong><br>
                    $\int_{-\infty}^{\infty} g(x)\delta(x-x_0) dx = g(x_0)$
                </div>
            </div>
        </div>

        <div class="theorem-box">
            <h3>Lemma 4.1: The Sifting Property</h3>
            <p><strong>Statement:</strong> For any continuous function $g: \mathbb{R} \mapsto \mathbb{R}$:</p>
            <div class="formula-box">
                $$\int_{-\infty}^{\infty} g(x)\delta(x-x_0) dx = g(x_0)$$
            </div>

            <p><strong>Proof Sketch:</strong></p>
            <ol>
                <li>Use the definition: $\int_{-\infty}^{\infty} g(x)\delta(x-x_0) dx = \lim_{\alpha \to 0} \int_{-\infty}^{\infty} g(x)\delta_\alpha(x-x_0) dx$</li>
                <li>Since $\delta_\alpha$ is non-zero only on $[x_0-\frac{\alpha}{2}, x_0+\frac{\alpha}{2}]$:</li>
                <li>$= \lim_{\alpha \to 0} \int_{x_0-\alpha/2}^{x_0+\alpha/2} g(x) \frac{1}{\alpha} dx$</li>
                <li>By mean value theorem: $= \lim_{\alpha \to 0} g(x_\alpha) = g(x_0)$ for some $x_\alpha$ in the interval</li>
            </ol>
        </div>

        <div class="example-box">
            <h4>Example: Using the Sifting Property</h4>
            <p>Evaluate $\int_{-\infty}^{\infty} (x^2 + 3x + 1)\delta(x-2) dx$</p>

            <p><strong>Solution:</strong> Using the sifting property with $g(x) = x^2 + 3x + 1$ and $x_0 = 2$:</p>
            <div class="formula-box">
                $$\int_{-\infty}^{\infty} (x^2 + 3x + 1)\delta(x-2) dx = g(2) = 2^2 + 3(2) + 1 = 11$$
            </div>
        </div>

        <h2>🔄 Generalized PDFs for All Random Variables</h2>

        <div class="concept-box">
            <p><strong>The Big Idea:</strong> Using delta functions, we can define PDFs for discrete and mixed random variables, creating a unified framework!</p>
        </div>

        <div class="definition-box">
            <h3>Generalized PDF for Discrete Random Variables</h3>
            <p>For a discrete random variable $X$ with range $R_X = \{x_1, x_2, x_3, \ldots\}$ and PMF $P_X(x_k)$:</p>
            <div class="formula-box">
                $$f_X(x) = \sum_{x_k \in R_X} P_X(x_k) \delta(x - x_k)$$
            </div>

            <p><strong>Derivation:</strong> Start with the CDF and differentiate</p>
            <div class="formula-box">
                $$F_X(x) = \sum_{x_k \in R_X} P_X(x_k) u(x - x_k)$$
                $$f_X(x) = \frac{dF_X(x)}{dx} = \sum_{x_k \in R_X} P_X(x_k) \frac{d}{dx}u(x - x_k) = \sum_{x_k \in R_X} P_X(x_k) \delta(x - x_k)$$
            </div>
        </div>

        <div class="definition-box">
            <h3>Generalized PDF for Mixed Random Variables</h3>
            <p>For a mixed random variable, the generalized PDF has the form:</p>
            <div class="formula-box">
                $$f_X(x) = \sum_k a_k \delta(x - x_k) + g(x)$$
            </div>
            <p>where:</p>
            <ul>
                <li>$a_k = P(X = x_k)$ are the point masses</li>
                <li>$g(x) \geq 0$ is the continuous part (no delta functions)</li>
                <li>$\sum_k a_k + \int_{-\infty}^{\infty} g(x) dx = 1$</li>
            </ul>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Unified Framework: Generalized PDFs
                </text>

                <!-- Discrete -->
                <text x="150" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Discrete: Pure Delta Functions
                </text>

                <line x1="50" y1="150" x2="250" y2="150" stroke="#2c3e50" stroke-width="1"/>
                <line x1="150" y1="80" x2="150" y2="220" stroke="#2c3e50" stroke-width="1"/>

                <!-- Delta spikes for discrete -->
                <line x1="100" y1="150" x2="100" y2="100" stroke="#e74c3c" stroke-width="4"/>
                <line x1="130" y1="150" x2="130" y2="120" stroke="#e74c3c" stroke-width="4"/>
                <line x1="170" y1="150" x2="170" y2="90" stroke="#e74c3c" stroke-width="4"/>
                <line x1="200" y1="150" x2="200" y2="110" stroke="#e74c3c" stroke-width="4"/>

                <!-- Arrow heads for discrete -->
                <polygon points="100,100 95,110 105,110" fill="#e74c3c"/>
                <polygon points="130,120 125,130 135,130" fill="#e74c3c"/>
                <polygon points="170,90 165,100 175,100" fill="#e74c3c"/>
                <polygon points="200,110 195,120 205,120" fill="#e74c3c"/>

                <text x="100" y="240" text-anchor="middle" font-size="9" fill="#2c3e50">x₁</text>
                <text x="130" y="240" text-anchor="middle" font-size="9" fill="#2c3e50">x₂</text>
                <text x="170" y="240" text-anchor="middle" font-size="9" fill="#2c3e50">x₃</text>
                <text x="200" y="240" text-anchor="middle" font-size="9" fill="#2c3e50">x₄</text>

                <!-- Continuous -->
                <text x="400" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Continuous: Smooth Function
                </text>

                <line x1="300" y1="150" x2="500" y2="150" stroke="#2c3e50" stroke-width="1"/>
                <line x1="400" y1="80" x2="400" y2="220" stroke="#2c3e50" stroke-width="1"/>

                <!-- Smooth curve for continuous -->
                <path d="M 320 150 Q 350 120 380 100 Q 420 90 450 110 Q 480 130 500 150"
                      stroke="#3498db" stroke-width="3" fill="none"/>

                <!-- Mixed -->
                <text x="650" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Mixed: Deltas + Continuous
                </text>

                <line x1="550" y1="150" x2="750" y2="150" stroke="#2c3e50" stroke-width="1"/>
                <line x1="650" y1="80" x2="650" y2="220" stroke="#2c3e50" stroke-width="1"/>

                <!-- Mixed: continuous part -->
                <path d="M 570 150 Q 600 130 620 120 Q 640 115 660 120 Q 680 125 700 140 Q 720 150 730 150"
                      stroke="#3498db" stroke-width="3" fill="none"/>

                <!-- Mixed: delta spikes -->
                <line x1="600" y1="150" x2="600" y2="100" stroke="#e74c3c" stroke-width="4"/>
                <line x1="700" y1="150" x2="700" y2="90" stroke="#e74c3c" stroke-width="4"/>
                <polygon points="600,100 595,110 605,110" fill="#e74c3c"/>
                <polygon points="700,90 695,100 705,100" fill="#e74c3c"/>

                <!-- Formulas -->
                <rect x="50" y="260" width="200" height="60" fill="white" stroke="#e74c3c" stroke-width="2" rx="5"/>
                <text x="150" y="280" text-anchor="middle" font-size="11" fill="#2c3e50" font-weight="bold">Discrete PDF:</text>
                <text x="150" y="295" text-anchor="middle" font-size="10" fill="#2c3e50">f_X(x) = Σ P(x_k)δ(x-x_k)</text>

                <rect x="300" y="260" width="200" height="60" fill="white" stroke="#3498db" stroke-width="2" rx="5"/>
                <text x="400" y="280" text-anchor="middle" font-size="11" fill="#2c3e50" font-weight="bold">Continuous PDF:</text>
                <text x="400" y="295" text-anchor="middle" font-size="10" fill="#2c3e50">f_X(x) = smooth function</text>

                <rect x="550" y="260" width="200" height="60" fill="white" stroke="#9b59b6" stroke-width="2" rx="5"/>
                <text x="650" y="280" text-anchor="middle" font-size="11" fill="#2c3e50" font-weight="bold">Mixed PDF:</text>
                <text x="650" y="295" text-anchor="middle" font-size="10" fill="#2c3e50">f_X(x) = Σ a_k δ(x-x_k) + g(x)</text>

                <!-- Unified formula -->
                <rect x="200" y="340" width="400" height="40" fill="#f8f9fa" stroke="#2c3e50" stroke-width="2" rx="5"/>
                <text x="400" y="365" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Unified: E[X] = ∫ x f_X(x) dx works for ALL types!
                </text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Unified Formulas:</strong> With generalized PDFs, the same formulas work for all random variable types!</p>
            <div class="formula-box">
                $$E[X] = \int_{-\infty}^{\infty} x f_X(x) dx$$
                $$\text{Var}(X) = \int_{-\infty}^{\infty} x^2 f_X(x) dx - \left(\int_{-\infty}^{\infty} x f_X(x) dx\right)^2$$
                $$P(a < X \leq b) = \int_a^b f_X(x) dx$$
            </div>
        </div>

        <div class="example-box">
            <h4>Example 4.16: Complete Mixed Variable Analysis</h4>
            <p>Let $X$ be a random variable with CDF:</p>
            <div class="formula-box">
                $$F_X(x) = \begin{cases}
                0 & \text{if } x < 0 \\
                \frac{1}{4} + \frac{1}{2}(1-e^{-x}) & \text{if } 0 \leq x < 1 \\
                \frac{1}{2} + \frac{1}{2}(1-e^{-x}) & \text{if } x \geq 1
                \end{cases}$$
            </div>

            <p><strong>Part (a):</strong> What type of random variable is $X$?</p>
            <p><strong>Solution:</strong> The CDF has jumps at $x = 0$ and $x = 1$ (each of size $\frac{1}{4}$), and increases continuously elsewhere. Therefore, $X$ is mixed.</p>

            <p><strong>Part (b):</strong> Find the generalized PDF</p>
            <p><strong>Solution:</strong> The PDF has delta functions at jump points plus the continuous part:</p>
            <div class="formula-box">
                $$f_X(x) = \frac{1}{4}\delta(x) + \frac{1}{4}\delta(x-1) + \frac{1}{2}e^{-x}u(x)$$
            </div>

            <p><strong>Part (c):</strong> Find $P(X > 0.5)$</p>
            <div class="formula-box">
                $$P(X > 0.5) = \int_{0.5}^{\infty} f_X(x) dx = 0 + \frac{1}{4} + \int_{0.5}^{\infty} \frac{1}{2}e^{-x} dx$$
                $$= \frac{1}{4} + \frac{1}{2}e^{-0.5} = \frac{1}{4} + \frac{1}{2}e^{-0.5} \approx 0.5533$$
            </div>

            <p><strong>Part (d):</strong> Find $E[X]$</p>
            <div class="formula-box">
                $$E[X] = \int_{-\infty}^{\infty} x f_X(x) dx$$
                $$= \frac{1}{4} \cdot 0 + \frac{1}{4} \cdot 1 + \int_0^{\infty} x \cdot \frac{1}{2}e^{-x} dx$$
                $$= \frac{1}{4} + \frac{1}{2} \cdot 1 = \frac{3}{4}$$
            </div>
            <p>(Note: $\int_0^{\infty} xe^{-x} dx = 1$ is the mean of an Exponential(1) distribution)</p>
        </div>

        <h2>🎯 Practical Applications and Problem-Solving</h2>

        <div class="example-box">
            <h4>Problem 1: Coin-Flip Construction of Mixed Variables</h4>
            <p><strong>Setup:</strong> Create a mixed random variable by combining discrete and continuous components.</p>
            <p>We have:</p>
            <ul>
                <li>Discrete random variable $X_d$ with PDF $f_d(x)$ and CDF $F_d(x)$</li>
                <li>Continuous random variable $X_c$ with PDF $f_c(x)$ and CDF $F_c(x)$</li>
                <li>Fair coin with $P(H) = p$</li>
            </ul>

            <p><strong>Construction:</strong> If coin shows heads, use $X_d$; if tails, use $X_c$.</p>

            <p><strong>Solution:</strong></p>
            <div class="formula-box">
                $$F_X(x) = pF_d(x) + (1-p)F_c(x)$$
                $$f_X(x) = pf_d(x) + (1-p)f_c(x)$$
                $$E[X] = pE[X_d] + (1-p)E[X_c]$$
                $$\text{Var}(X) = p\text{Var}(X_d) + (1-p)\text{Var}(X_c) + p(1-p)(E[X_d] - E[X_c])^2$$
            </div>
        </div>

        <div class="example-box">
            <h4>Problem 2: Device Lifetime Model</h4>
            <p><strong>Real-world scenario:</strong> A company manufactures devices where:</p>
            <ul>
                <li>2% are defective from start (lifetime = 0)</li>
                <li>98% have exponentially distributed lifetime with $\lambda = 2$ years</li>
            </ul>

            <p><strong>Solution:</strong></p>
            <p><strong>Generalized PDF:</strong></p>
            <div class="formula-box">
                $$f_X(x) = 0.02\delta(x) + 0.98 \cdot 2e^{-2x}u(x)$$
            </div>

            <p><strong>Probability device lasts ≥ 1 year:</strong></p>
            <div class="formula-box">
                $$P(X \geq 1) = \int_1^{\infty} f_X(x) dx = 0.98 \int_1^{\infty} 2e^{-2x} dx = 0.98e^{-2} \approx 0.133$$
            </div>

            <p><strong>Expected lifetime:</strong></p>
            <div class="formula-box">
                $$E[X] = 0.02 \cdot 0 + 0.98 \cdot \frac{1}{2} = 0.49 \text{ years}$$
            </div>
        </div>

        <div class="comparison-table">
            <thead>
                <tr>
                    <th>Random Variable Type</th>
                    <th>CDF Characteristics</th>
                    <th>PDF Form</th>
                    <th>Example</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Discrete</strong></td>
                    <td>Staircase with jumps</td>
                    <td>$\sum_k P(x_k)\delta(x-x_k)$</td>
                    <td>Coin flips, dice rolls</td>
                </tr>
                <tr>
                    <td><strong>Continuous</strong></td>
                    <td>Smooth, no jumps</td>
                    <td>$g(x) \geq 0$, no deltas</td>
                    <td>Height, temperature</td>
                </tr>
                <tr>
                    <td><strong>Mixed</strong></td>
                    <td>Smooth parts + jumps</td>
                    <td>$\sum_k a_k\delta(x-x_k) + g(x)$</td>
                    <td>Insurance claims, device lifetimes</td>
                </tr>
            </tbody>
        </table>

        <h2>📊 Summary and Key Takeaways</h2>

        <div class="mixed-types">
            <div class="type-card">
                <div class="card-title">🎯 Understanding Mixed Variables</div>
                <ul>
                    <li><strong>Definition:</strong> Neither purely discrete nor continuous</li>
                    <li><strong>CDF:</strong> Combination of smooth parts and jumps</li>
                    <li><strong>Components:</strong> Point masses + continuous density</li>
                    <li><strong>Real examples:</strong> Insurance claims, device failures</li>
                </ul>
            </div>

            <div class="type-card">
                <div class="card-title">⚡ The Delta Function</div>
                <ul>
                    <li><strong>Purpose:</strong> Unify discrete, continuous, and mixed</li>
                    <li><strong>Key property:</strong> Sifting property for integration</li>
                    <li><strong>Interpretation:</strong> "Derivative" of step function</li>
                    <li><strong>Benefit:</strong> Same formulas for all variable types</li>
                </ul>
            </div>

            <div class="type-card">
                <div class="card-title">🔧 Problem-Solving Strategy</div>
                <ul>
                    <li><strong>Step 1:</strong> Identify CDF structure (jumps + smooth)</li>
                    <li><strong>Step 2:</strong> Find point masses and continuous parts</li>
                    <li><strong>Step 3:</strong> Write generalized PDF with deltas</li>
                    <li><strong>Step 4:</strong> Use unified formulas for calculations</li>
                </ul>
            </div>

            <div class="type-card">
                <div class="card-title">📈 Practical Applications</div>
                <ul>
                    <li><strong>Insurance:</strong> Claims with deductibles</li>
                    <li><strong>Engineering:</strong> Component reliability</li>
                    <li><strong>Finance:</strong> Returns with discrete events</li>
                    <li><strong>Biology:</strong> Survival analysis with cures</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Master Formula for All Random Variables:</strong></p>
            <div class="formula-box">
                $$E[g(X)] = \int_{-\infty}^{\infty} g(x) f_X(x) dx$$
            </div>
            <p>where $f_X(x)$ is the generalized PDF (possibly containing delta functions)</p>
        </div>

        <div class="warning">
            <p><strong>Common Mistakes to Avoid:</strong></p>
            <ul>
                <li>Forgetting to include point masses when computing probabilities</li>
                <li>Incorrectly handling delta functions in integration</li>
                <li>Not recognizing when a transformation creates mixed variables</li>
                <li>Confusing left-continuous vs. right-continuous at jump points</li>
            </ul>
        </div>

        <div class="concept-box">
            <p><strong>The Big Picture:</strong></p>
            <p>Mixed random variables bridge the gap between discrete and continuous probability theory. The delta function provides the mathematical tool to treat all random variables uniformly, leading to elegant and powerful unified formulas. This framework is essential for advanced probability theory, stochastic processes, and real-world modeling where pure discrete or continuous models are inadequate.</p>
        </div>

        <div class="delta-box">
            <div class="delta-title">🎓 Study Tips</div>
            <ul>
                <li><strong>Practice identifying:</strong> Look at CDFs and identify jump points vs. smooth regions</li>
                <li><strong>Master the sifting property:</strong> This is the key to working with delta functions</li>
                <li><strong>Understand transformations:</strong> How continuous variables can become mixed</li>
                <li><strong>Use unified formulas:</strong> Same expectation/variance formulas work for all types</li>
                <li><strong>Connect to applications:</strong> Think about real-world scenarios with mixed behavior</li>
            </ul>
        </div>

    </div>
</body>
</html>
