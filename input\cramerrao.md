36-705: Intermediate Statistics Fall 2019
Lecture 15: October 2
Lecturer: <PERSON><PERSON>
We continue our discussion of point estimation.
15.1 MSE
Now we discuss the evaluation of estimators. The mean squared error (MSE) is
Eθ(θb− θ)
2 =
Z
· · · Z
(θb(x1, . . . , xn) − θ)
2
p(x1; θ)· · · p(xn; θ)dx1 . . . dxn.
The bias is
B = Eθ(θb) − θ
and the variance is
V = Varθ(θb).
Theorem 15.1 We have
MSE = B
2 + V.
Proof: Let m = Eθ(θb). Then
MSE = Eθ(θb− θ)
2 = Eθ(θb− m + m − θ)
2
= Eθ(θb− m)
2 + (m − θ)
2 + 2Eθ(θb− m)(m − θ)
= Eθ(θb− m)
2 + (m − θ)
2 = V + B
2
.
An estimator is unbiased if the bias is 0. In that case, the MSE = Variance. There is often
a tradeoff between bias and variance. So low bias can imply high variance and vice versa.
Example 15.2 Let X1, . . . , Xn ∼ N(µ, σ2
). Then
E(X) = µ, E(S
2
) = σ
2
.
The MSE’s are
E(X − µ)
2 =
σ
2
n
, E(S
2 − σ
2
)
2 =
2σ
4
n − 1
.
15-1
15-2 Lecture 15: October 2
It is worth thinking about how one defines the MSE when θ is multivariate (as in the example
above), and what the analogous bias-variance decomposition is.
We would like to choose an estimator with small MSE. However, the MSE is a function of
θ. Later, we shall discuss minimax estimators, that use the maximum of the MSE over θ as
a way to compare estimators.
15.2 Unbiased estimators - Fisher Information, Cram´erRao
Classically, an initial focus in the search for optimal estimators focused on unbiased estimators. Roughly, from a technical standpoint finding estimators which have lowest possible
MSE is quite difficult so one way to narrow the search space is to restrict our attention to
unbiased estimators. In this case, the goal is to find minimum variance unbiased estimators and classical textbooks discuss the question of existence and finding minimum variance
unbiased estimators in much detail.
More modern treatments do not often emphasize this point of view for two reasons: (1) there
are many known examples where a small amount of bias can result in large reductions in
variance so in general restricting attention to unbiased estimators can be bad. (2) for most
problems finding minimum variance unbiased estimators is challenging or impossible (there
are lots of interesting statistical quantities for which there are no unbiased estimators).
With that said there are still pieces of this classical theory that I personally find very useful.
One of the important pieces is the Cram´er-Rao bound which provides a lower bound on the
variance of an unbiased estimator. In many problems, this bound will provide some at least
heuristic guidelines into the difficulty of an estimation problem. Later on in the course we
will talk about other ways of proving lower bounds that do not restrict attention to unbiased
estimators (i.e. we will discuss what are called minimax lower bounds).
15.2.1 Fisher Information
We are in the setting where we observe X1, . . . , Xn ∼ p(X; θ). We will generally suppose
that θ ∈ R
d
. We can compute the log-likelihood function:
LL(θ) = Xn
i=1
log p(Xi
; θ).
We can also define the gradient of this function, which is called the score function:
s(θ) = ∇θLL(θ) = Xn
i=1
∇θ log p(Xi
; θ).
Lecture 15: October 2 15-3
This gradient is a d-dimensional vector. The Fisher Information matrix is the expected outer
product of the score, i.e.:
I(θ) = E[s(θ)s(θ)
T
].
The Fisher information matrix is a d × d matrix. Lets take a quick look at a couple of
examples:
Example 1: Suppose that X ∼ Ber(p), then the log-likelihood is given by:
LL(p) = X log(p) + (1 − X) log(1 − p),
and accordingly the score is:
s(p) = X
p
−
1 − X
1 − p
=
X − p
p(1 − p)
.
We can then compute the Fisher information:
I(p) = 1
p
2
(1 − p)
2
E

(X − p)
2

=
1
p(1 − p)
.
Example 2: Suppose that X ∼ N(µ, σ2
) where σ is known, then the log-likelihood is given
by:
LL(µ) = −
1
2σ
2
(X − µ)
2
,
so that the score is:
s(µ) = X − µ
σ
2
,
and the Fisher information is:
I(µ) = E

(X − µ)
2
σ
4

=
1
σ
2
.
Notice the connection between Fisher information and the variance.
An important property that we will use in the sequel is that the score function has mean
zero, i.e.
Ep(X1,...,Xn;θ)
[s(θ)] = 0.
15-4 Lecture 15: October 2
Proof: Notice that,
Ep(X1,...,Xn;θ)
[s(θ)] = Xn
i=1
Z
∇θ log p(xi
; θ)p(x1, . . . , xn; θ)dx1dx2 . . . dxn
=
Xn
i=1
Z
∇θ log p(xi
; θ)p(xi
; θ)dxi
= n
Z
∇θ log p(x1; θ)p(x1; θ)dx1,
using the i.i.d. assumption several times. Under some regularity conditions we can switch
the derivative and integral (essentially the dominated convergence theorem again but see the
Lehmann and Casella book for details) so we obtain,
Z
∇θ log p(x1; θ)p(x1; θ)dx1 =
Z
∇θp(x1; θ)
p(x1; θ)
p(x1; θ)dx1
= ∇θ
Z
p(x1; θ)dx1 = ∇θ1 = 0.
One simple consequence of this property is that we can interpret the Fisher information
matrix as the covariance matrix of the score, i.e.
I(θ) = E[(s(θ) − E(s(θ)))(s(θ) − E(s(θ)))T
].
So at a very rough level, we might expect that if the Fisher information is large then parameter estimation is easy since small changes in the parameter result in a very noticeable
change in the score function. We will make this more precise soon.
Suppose first that we only have 1 sample from the model. In this case, the Fisher information
can alternatively be defined as:
I1(θ) = −E

∇2
θ
log p(X; θ)

.
To see this observe that,
∇2
θ
log p(X; θ) = ∇θ
∇θp(X; θ)
p(X; θ)
=
∇2
θ
p(X; θ)
p(X; θ)
−
(∇θp(X; θ)∇θp(X; θ)
T
)
p(X; θ)
2
=
∇2
θ
p(X; θ)
p(X; θ)
− s(θ)s(θ)
T
.
Now, notice that,
E

∇2
θ
p(X; θ)
p(X; θ)

=
Z
∇2
θ
p(X; θ) = ∇2
θ
Z
p(X; θ) = ∇2
θ
1 = 0,
Lecture 15: October 2 15-5
which in turn yields the desired alternate definition of the Fisher information. In essence the
Fisher information is measuring the expected curvature of the log-likelihood function around
the point θ. As we will see in future lectures if the log-likelihood is more curved (i.e. I(θ) is
appropriately “large”) then θ is easier to estimate.
Another observation from the above representation is that if we instead had n i.i.d. samples,
then
I(θ) = −E

∇2
θ
log p(X1, . . . , Xn; θ)

= −
Xn
i=1
E

∇2
θ
log p(Xi
; θ)

= nI1(θ).
Example 3: The above in turn yields that if we observed X1, . . . , Xn ∼ Ber(p) the Fisher
information would be,
I(p) = n
p(1 − p)
.
As a more abstract example we can consider the case of general exponential families.
Example 4: For exponential families we have seen that the log-likelihood is given as:
LL(θ; X1, . . . , Xn) = Xs
i=1
θi
Xn
j=1
Ti(Xj ) − nA(θ),
so the Hessian is simply,
I(θ) = n∇2
θA(θ) = nE[(T(X) − E[T(X)])(T(X) − E[T(X)])T
],
i.e. the Fisher information matrix is simply given by (n times) the Hessian of the log-partition
function or alternatively it is the covariance matrix of the vector of sufficient statistics.
15.2.2 Cram´er-Rao Bound
Let us briefly consider again the Bernoulli example. We observe X1, . . . , Xn ∼ Ber(p) and
estimate pb =
1
n
Pn
i=1 Xi
. The estimator is unbiased and has variance p(1 − p)/n which is
precisely the inverse of the Fisher information.
This turns out to be a fairly general phenomenon. Indeed, the Cram´er-Rao bound assures
us that this estimator is unimprovable in a certain sense. We focus first on the univariate
case (when θ ∈ R) and then consider the multivariate extension.
15-6 Lecture 15: October 2
Cram´er-Rao Bound: Suppose that we observe X1, . . . , Xn ∼ p(X; θ) and that θb is an
unbiased estimator of θ, then:
Var(θb) ≥
1
nI1(θ)
.
Proof: Consider that,
cov(θ, s b (θ)) = E((θb− θ)s(θ))
= E(θsb (θ)),
since E[s(θ)] = 0. Furthermore,
E(θsb (θ)) = Z
θb(x1, . . . , xn)∇θ log p(x1, . . . , xn; θ)p(x1, . . . , xn; θ)dx1 . . . dxn
=
Z
θb(x1, . . . , xn)
∇θp(x1, . . . , xn; θ)
p(x1, . . . , xn; θ)
p(x1, . . . , xn; θ)dx1 . . . dxn
= ∇θθ = 1.
Notice that for any fixed ζ we can write:
Var(θb− ζs(θ)) = Var(θb) + ζ
2Var(s(θ)) − 2ζcov(θ, s b (θ)) = Var(θb) + ζ
2nI1(θ) − 2ζ.
Using the fact that variances are positive we can write:
Var(θb) ≥ 2ζ − ζ
2nI1(θ)
Take ζ = 1/(nI1(θ)) to obtain the Cram´er-Rao bound.
Multivariate Generalization: The Cram´er-Rao bound can be derived for a multivariate
parameter θ in a very similar fashion and in this case leads to the conclusion:
Var(θb)  I(θ)
−1 =
1
n
I1(θ)
−1
,
where we are comparing two positive semi-definite matrices so the ordering is the Loewner
ordering, i.e. for any vector v we have that,
v
TVar(θb)v ≥ v
T
I(θ)
−1
v.
Examples: In both the Gaussian and Bernoulli models, as a consequence of the Cram´erRao bound we can conclude that the MLE is the best unbiased estimator.
Important Note: The Cram´er-Rao bound holds, in an asymptotic sense, for substantially
more general settings (without the unbiasedness requirement). For example, see the book
of Van der Vaart (on Asymptotic Statistics) which shows that under appropriate conditions
(known as quadratic mean differentiability or local asymptotic normality) that no estimator
can have smaller mean squared error than Fisher information in any uniform sense. This is
a deep result and central to the optimality of the MLE in more general settings and is one
of Lucien Le Cam’s main contributions to the theory of statistics.
Lecture 15: October 2 15-7
15.3 Beyond unbiased estimators - decision theory
The central idea in decision theory is that we want to minimize our expected loss.
Let us first try to understand the decision theoretic setup. We observe data X1, . . . , Xn ∼
p(X; θ), with θ ∈ Θ, and we make a decision, i.e. we select an action a.
In point estimation, the decision is just our guess of the parameter. In hypothesis testing
situations our decision will instead be which of the hypotheses we believe to be true. Once
we take an action we suffer a loss. The loss function in point estimation is roughly something
that is large if a is far from θ and small if our guess is good, i.e., if a is close to θ.
Some very common loss functions are:
1. Squared loss: L(a, θ) = (a − θ)
2
.
2. Absolute loss: L(a, θ) = |a − θ|.
There are however many other loss functions. For instance, we sometimes consider losses
like:
L(a, θ) = (a − θ)
2
|θ| + 1
,
which penalizes errors in estimation more for small values of θ than for large values. We can
similarly design a loss function that penalizes errors more strongly for large values of θ.
Another important point is that there are cases when we do not really care about estimating
the parameter well but rather just the distribution p(X; θ). This is true when we care about
prediction in regression or in density estimation. In this case we could define the loss between
θ and a in terms of the distributions p(X; θ) and p(X; a). One canonical example:
Kullback-Leibler loss:
L(a, θ) = KL(p(X; θ), p(X; a)) = EX∼p(X;θ)
log 
p(X; θ)
p(X; a)

.
Once we have a loss function, and an estimator, we can assess the estimator via its expected
loss. This expected loss is called the risk of the estimator. Suppose we consider an estimator
θb(X). Then we define:
R(θ, θb(X)) = EθL(θb(X), θ).
In general, we do not a-priori know anything about the value of θ so we would like estimators
with low risk for all parameters θ ∈ Θ. So ideally, we would like to find an estimator θb such
that for any other estimator θ
0 we have that:
R(θ, θb(X)) ≤ R(θ, θ0
)
for all values θ. Such estimators will most often not exist – why not?