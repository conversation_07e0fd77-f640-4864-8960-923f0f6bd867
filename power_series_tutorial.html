<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Series Tutorial - Section 6.1</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .outline {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            color: white;
        }

        .outline h2 {
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .outline-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .outline-section {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .section h3 {
            color: #764ba2;
            margin: 20px 0 15px 0;
            font-size: 1.3em;
        }

        .definition-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .theorem-box {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .example-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .info-box {
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
            color: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }

        .svg-container {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .learning-objectives {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .learning-objectives h3 {
            margin-bottom: 15px;
            color: white;
        }

        .learning-objectives ul {
            list-style-type: none;
            padding-left: 0;
        }

        .learning-objectives li {
            margin: 10px 0;
            padding-left: 25px;
            position: relative;
        }

        .learning-objectives li:before {
            content: "📚";
            position: absolute;
            left: 0;
        }

        .math-display {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .outline-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Power Series Tutorial</h1>
            <p>Section 6.1: Introduction to Power Series and Convergence</p>
        </div>

        <div class="learning-objectives">
            <h3>Learning Objectives</h3>
            <ul>
                <li>Identify a power series and provide examples of them</li>
                <li>Determine the radius of convergence and interval of convergence of a power series</li>
                <li>Use a power series to represent a function</li>
            </ul>
        </div>

        <div class="outline">
            <h2>Tutorial Outline</h2>
            <div class="outline-grid">
                <div class="outline-section">
                    <h4>1. Introduction & Definitions</h4>
                    <p>What is a power series and why do we study them?</p>
                </div>
                <div class="outline-section">
                    <h4>2. Forms of Power Series</h4>
                    <p>Power series centered at x=0 and x=a</p>
                </div>
                <div class="outline-section">
                    <h4>3. Convergence Theory</h4>
                    <p>The three fundamental cases of convergence</p>
                </div>
                <div class="outline-section">
                    <h4>4. Radius & Interval</h4>
                    <p>Finding where power series converge</p>
                </div>
                <div class="outline-section">
                    <h4>5. The Ratio Test</h4>
                    <p>Practical method for finding convergence</p>
                </div>
                <div class="outline-section">
                    <h4>6. Function Representation</h4>
                    <p>Using power series to represent functions</p>
                </div>
            </div>
        </div>

        <!-- Section 1: Introduction & Definition -->
        <div class="section">
            <h2>1. Introduction & Definition of Power Series</h2>
            
            <p>A <strong>power series</strong> is a type of infinite series where each term involves a variable raised to increasing powers. Think of it as an "infinite polynomial" that can represent complex functions in a more manageable form.</p>

            <div class="info-box">
                <strong>Why Study Power Series?</strong><br>
                • They convert complex functions into polynomial-like expressions<br>
                • Polynomials are easy to differentiate, integrate, and evaluate<br>
                • They provide excellent approximations for function values<br>
                • They're fundamental in calculus, physics, and engineering
            </div>

            <div class="definition-box">
                <h3>Definition: Power Series</h3>
                <p>A series of the form:</p>
                <div class="math-display">
                    $$\sum_{n=0}^{\infty} c_n x^n = c_0 + c_1 x + c_2 x^2 + c_3 x^3 + \cdots$$
                </div>
                <p>where $x$ is a variable and the coefficients $c_n$ are constants, is called a <strong>power series</strong>.</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                        Power Series as "Infinite Polynomial"
                    </text>
                    
                    <!-- Polynomial terms visualization -->
                    <g transform="translate(50, 80)">
                        <!-- Finite polynomial -->
                        <text x="0" y="0" font-size="14" font-weight="bold" fill="#333">Finite Polynomial:</text>
                        <text x="0" y="25" font-size="12" fill="#666">P(x) = c₀ + c₁x + c₂x² + c₃x³ + c₄x⁴</text>
                        
                        <!-- Arrow -->
                        <path d="M 250 35 L 250 55" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="260" y="45" font-size="10" fill="#667eea">Extend to infinity</text>
                        
                        <!-- Power series -->
                        <text x="0" y="80" font-size="14" font-weight="bold" fill="#333">Power Series:</text>
                        <text x="0" y="105" font-size="12" fill="#666">f(x) = c₀ + c₁x + c₂x² + c₃x³ + c₄x⁴ + ⋯</text>
                        
                        <!-- Terms breakdown -->
                        <g transform="translate(0, 140)">
                            <rect x="0" y="0" width="80" height="30" fill="#4facfe" opacity="0.3" rx="5"/>
                            <text x="40" y="20" text-anchor="middle" font-size="11" fill="#333">Constant</text>
                            <text x="40" y="45" text-anchor="middle" font-size="10" fill="#666">c₀</text>
                            
                            <rect x="90" y="0" width="80" height="30" fill="#fa709a" opacity="0.3" rx="5"/>
                            <text x="130" y="20" text-anchor="middle" font-size="11" fill="#333">Linear</text>
                            <text x="130" y="45" text-anchor="middle" font-size="10" fill="#666">c₁x</text>
                            
                            <rect x="180" y="0" width="80" height="30" fill="#a8edea" opacity="0.3" rx="5"/>
                            <text x="220" y="20" text-anchor="middle" font-size="11" fill="#333">Quadratic</text>
                            <text x="220" y="45" text-anchor="middle" font-size="10" fill="#666">c₂x²</text>
                            
                            <rect x="270" y="0" width="80" height="30" fill="#d299c2" opacity="0.3" rx="5"/>
                            <text x="310" y="20" text-anchor="middle" font-size="11" fill="#333">Cubic</text>
                            <text x="310" y="45" text-anchor="middle" font-size="10" fill="#666">c₃x³</text>
                            
                            <text x="370" y="20" text-anchor="middle" font-size="16" fill="#333">⋯</text>
                        </g>
                    </g>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="example-box">
                <h3>Simple Example: Geometric Series</h3>
                <p>Consider the series:</p>
                <div class="math-display">
                    $$1 + x + x^2 + x^3 + \cdots = \sum_{n=0}^{\infty} x^n$$
                </div>
                <p>This is a power series where all coefficients $c_n = 1$. This particular series is called a <strong>geometric series</strong> with ratio $r = x$.</p>
                
                <div class="info-box">
                    <strong>Key Insight:</strong> This geometric series converges when $|x| < 1$ and diverges when $|x| \geq 1$. When it converges, its sum is $\frac{1}{1-x}$.
                </div>
            </div>
        </div>

        <!-- Section 2: Forms of Power Series -->
        <div class="section">
            <h2>2. Forms of Power Series</h2>
            
            <p>Power series can be centered at different points. Understanding these different forms is crucial for working with various functions and their representations.</p>

            <div class="grid-2">
                <div class="definition-box">
                    <h3>Power Series Centered at x = 0</h3>
                    <div class="math-display">
                        $$\sum_{n=0}^{\infty} c_n x^n = c_0 + c_1 x + c_2 x^2 + \cdots$$
                    </div>
                    <p>This is the standard form we've seen. The series is "centered" at the origin.</p>
                </div>

                <div class="definition-box">
                    <h3>Power Series Centered at x = a</h3>
                    <div class="math-display">
                        $$\sum_{n=0}^{\infty} c_n (x-a)^n = c_0 + c_1(x-a) + c_2(x-a)^2 + \cdots$$
                    </div>
                    <p>This series is centered at the point $x = a$. We substitute $(x-a)$ for $x$ in each term.</p>
                </div>
            </div>

            <div class="info-box">
                <strong>Important Convention:</strong> We define $x^0 = 1$ and $(x-a)^0 = 1$ even when $x = 0$ or $x = a$ respectively. This ensures our series has a well-defined constant term.
            </div>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                        Comparison: Power Series Centered at Different Points
                    </text>
                    
                    <!-- Centered at x = 0 -->
                    <g transform="translate(50, 70)">
                        <text x="100" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#fa709a">
                            Centered at x = 0
                        </text>
                        
                        <!-- Number line -->
                        <line x1="20" y1="40" x2="180" y2="40" stroke="#333" stroke-width="2"/>
                        
                        <!-- Tick marks -->
                        <g>
                            <line x1="20" y1="35" x2="20" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="20" y="60" text-anchor="middle" font-size="10" fill="#666">-2</text>
                            
                            <line x1="60" y1="35" x2="60" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="60" y="60" text-anchor="middle" font-size="10" fill="#666">-1</text>
                            
                            <line x1="100" y1="35" x2="100" y2="45" stroke="#fa709a" stroke-width="3"/>
                            <text x="100" y="60" text-anchor="middle" font-size="10" fill="#fa709a" font-weight="bold">0</text>
                            
                            <line x1="140" y1="35" x2="140" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="140" y="60" text-anchor="middle" font-size="10" fill="#666">1</text>
                            
                            <line x1="180" y1="35" x2="180" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="180" y="60" text-anchor="middle" font-size="10" fill="#666">2</text>
                        </g>
                        
                        <!-- Center point highlight -->
                        <circle cx="100" cy="40" r="4" fill="#fa709a"/>
                        <text x="100" y="85" text-anchor="middle" font-size="12" fill="#fa709a" font-weight="bold">Center: a = 0</text>
                        
                        <!-- Series formula -->
                        <text x="100" y="110" text-anchor="middle" font-size="11" fill="#333">
                            ∑ cₙ xⁿ
                        </text>
                    </g>
                    
                    <!-- Centered at x = a -->
                    <g transform="translate(400, 70)">
                        <text x="100" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#4facfe">
                            Centered at x = a
                        </text>
                        
                        <!-- Number line -->
                        <line x1="20" y1="40" x2="180" y2="40" stroke="#333" stroke-width="2"/>
                        
                        <!-- Tick marks -->
                        <g>
                            <line x1="20" y1="35" x2="20" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="20" y="60" text-anchor="middle" font-size="10" fill="#666">-1</text>
                            
                            <line x1="60" y1="35" x2="60" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="60" y="60" text-anchor="middle" font-size="10" fill="#666">0</text>
                            
                            <line x1="100" y1="35" x2="100" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="100" y="60" text-anchor="middle" font-size="10" fill="#666">1</text>
                            
                            <line x1="140" y1="35" x2="140" y2="45" stroke="#4facfe" stroke-width="3"/>
                            <text x="140" y="60" text-anchor="middle" font-size="10" fill="#4facfe" font-weight="bold">2</text>
                            
                            <line x1="180" y1="35" x2="180" y2="45" stroke="#333" stroke-width="1"/>
                            <text x="180" y="60" text-anchor="middle" font-size="10" fill="#666">3</text>
                        </g>
                        
                        <!-- Center point highlight -->
                        <circle cx="140" cy="40" r="4" fill="#4facfe"/>
                        <text x="140" y="85" text-anchor="middle" font-size="12" fill="#4facfe" font-weight="bold">Center: a = 2</text>
                        
                        <!-- Series formula -->
                        <text x="140" y="110" text-anchor="middle" font-size="11" fill="#333">
                            ∑ cₙ (x-2)ⁿ
                        </text>
                    </g>
                    
                    <!-- Examples section -->
                    <g transform="translate(50, 220)">
                        <text x="300" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">
                            Examples of Different Power Series
                        </text>
                        
                        <!-- Example 1 -->
                        <g transform="translate(0, 30)">
                            <rect x="0" y="0" width="180" height="80" fill="#a8edea" opacity="0.3" rx="8"/>
                            <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Exponential Series</text>
                            <text x="90" y="40" text-anchor="middle" font-size="10" fill="#666">Centered at x = 0</text>
                            <text x="90" y="60" text-anchor="middle" font-size="10" fill="#333">∑ xⁿ/n! = 1 + x + x²/2! + x³/3! + ⋯</text>
                        </g>
                        
                        <!-- Example 2 -->
                        <g transform="translate(200, 30)">
                            <rect x="0" y="0" width="180" height="80" fill="#d299c2" opacity="0.3" rx="8"/>
                            <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Factorial Series</text>
                            <text x="90" y="40" text-anchor="middle" font-size="10" fill="#666">Centered at x = 0</text>
                            <text x="90" y="60" text-anchor="middle" font-size="10" fill="#333">∑ n! xⁿ = 1 + x + 2!x² + 3!x³ + ⋯</text>
                        </g>
                        
                        <!-- Example 3 -->
                        <g transform="translate(400, 30)">
                            <rect x="0" y="0" width="180" height="80" fill="#fee140" opacity="0.3" rx="8"/>
                            <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Centered Series</text>
                            <text x="90" y="40" text-anchor="middle" font-size="10" fill="#666">Centered at x = 2</text>
                            <text x="90" y="55" text-anchor="middle" font-size="9" fill="#333">∑ (x-2)ⁿ/((n+1)3ⁿ)</text>
                            <text x="90" y="70" text-anchor="middle" font-size="9" fill="#333">= 1 + (x-2)/6 + (x-2)²/27 + ⋯</text>
                        </g>
                    </g>
                </svg>
            </div>

            <div class="example-box">
                <h3>Concrete Examples</h3>
                <div class="grid-2">
                    <div>
                        <h4>Series Centered at x = 0:</h4>
                        <div class="math-display">
                            $$\sum_{n=0}^{\infty} \frac{x^n}{n!} = 1 + x + \frac{x^2}{2!} + \frac{x^3}{3!} + \cdots$$
                        </div>
                        <p>This represents the exponential function $e^x$.</p>
                    </div>
                    
                    <div>
                        <h4>Series Centered at x = 2:</h4>
                        <div class="math-display">
                            $$\sum_{n=0}^{\infty} \frac{(x-2)^n}{(n+1)3^n} = 1 + \frac{x-2}{2 \cdot 3} + \frac{(x-2)^2}{3 \cdot 3^2} + \cdots$$
                        </div>
                        <p>This series is centered at the point $x = 2$.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 3: Convergence of Power Series -->
        <div class="section">
            <h2>3. Convergence of Power Series - The Three Fundamental Cases</h2>
            
            <p>The most important question about any power series is: <strong>For which values of x does the series converge?</strong> Unlike numerical series, power series involve a variable, so their convergence depends on the specific value of x we choose.</p>

            <div class="info-box">
                <strong>Key Insight:</strong> A power series always converges at its center point. For $\sum c_n(x-a)^n$, the series always converges at $x = a$ because all terms except the first become zero.
            </div>

            <div class="theorem-box">
                <h3>Theorem 6.1: Convergence of Power Series</h3>
                <p>For any power series $\sum_{n=0}^{\infty} c_n(x-a)^n$, exactly one of the following three cases occurs:</p>
                
                <div class="grid-2">
                    <div>
                        <h4>Case I: Point Convergence</h4>
                        <p>The series converges only at $x = a$ and diverges for all $x \neq a$.</p>
                        <p><strong>Radius:</strong> $R = 0$</p>
                    </div>
                    
                    <div>
                        <h4>Case II: Universal Convergence</h4>
                        <p>The series converges for all real numbers $x$.</p>
                        <p><strong>Radius:</strong> $R = \infty$</p>
                    </div>
                </div>
                
                <div style="margin-top: 15px;">
                    <h4>Case III: Interval Convergence</h4>
                    <p>There exists $R > 0$ such that:</p>
                    <ul>
                        <li>The series converges if $|x - a| < R$</li>
                        <li>The series diverges if $|x - a| > R$</li>
                        <li>At $|x - a| = R$, the series may converge or diverge</li>
                    </ul>
                </div>
            </div>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                        Three Cases of Power Series Convergence
                    </text>
                    
                    <!-- Case I: R = 0 -->
                    <g transform="translate(50, 70)">
                        <text x="100" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#fa709a">
                            Case I: R = 0
                        </text>
                        <text x="100" y="20" text-anchor="middle" font-size="12" fill="#666">
                            Converges only at center
                        </text>
                        
                        <!-- Number line -->
                        <line x1="20" y1="50" x2="180" y2="50" stroke="#333" stroke-width="2"/>
                        
                        <!-- Tick marks and labels -->
                        <g>
                            <line x1="20" y1="45" x2="20" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="20" y="70" text-anchor="middle" font-size="10" fill="#666">a-2</text>
                            <text x="20" y="85" text-anchor="middle" font-size="9" fill="#e74c3c">Diverges</text>
                            
                            <line x1="60" y1="45" x2="60" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="60" y="70" text-anchor="middle" font-size="10" fill="#666">a-1</text>
                            <text x="60" y="85" text-anchor="middle" font-size="9" fill="#e74c3c">Diverges</text>
                            
                            <line x1="100" y1="45" x2="100" y2="55" stroke="#fa709a" stroke-width="3"/>
                            <text x="100" y="70" text-anchor="middle" font-size="10" fill="#fa709a" font-weight="bold">a</text>
                            <text x="100" y="85" text-anchor="middle" font-size="9" fill="#27ae60" font-weight="bold">Converges</text>
                            
                            <line x1="140" y1="45" x2="140" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="140" y="70" text-anchor="middle" font-size="10" fill="#666">a+1</text>
                            <text x="140" y="85" text-anchor="middle" font-size="9" fill="#e74c3c">Diverges</text>
                            
                            <line x1="180" y1="45" x2="180" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="180" y="70" text-anchor="middle" font-size="10" fill="#666">a+2</text>
                            <text x="180" y="85" text-anchor="middle" font-size="9" fill="#e74c3c">Diverges</text>
                        </g>
                        
                        <!-- Center point -->
                        <circle cx="100" cy="50" r="3" fill="#27ae60"/>
                        
                        <!-- Example -->
                        <text x="100" y="110" text-anchor="middle" font-size="10" fill="#333" font-weight="bold">Example:</text>
                        <text x="100" y="125" text-anchor="middle" font-size="9" fill="#333">∑ n! xⁿ</text>
                    </g>
                    
                    <!-- Case II: R = ∞ -->
                    <g transform="translate(300, 70)">
                        <text x="100" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#4facfe">
                            Case II: R = ∞
                        </text>
                        <text x="100" y="20" text-anchor="middle" font-size="12" fill="#666">
                            Converges everywhere
                        </text>
                        
                        <!-- Number line -->
                        <line x1="20" y1="50" x2="180" y2="50" stroke="#27ae60" stroke-width="4"/>
                        
                        <!-- Tick marks and labels -->
                        <g>
                            <line x1="20" y1="45" x2="20" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="20" y="70" text-anchor="middle" font-size="10" fill="#666">a-2</text>
                            <text x="20" y="85" text-anchor="middle" font-size="9" fill="#27ae60">Converges</text>
                            
                            <line x1="60" y1="45" x2="60" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="60" y="70" text-anchor="middle" font-size="10" fill="#666">a-1</text>
                            <text x="60" y="85" text-anchor="middle" font-size="9" fill="#27ae60">Converges</text>
                            
                            <line x1="100" y1="45" x2="100" y2="55" stroke="#4facfe" stroke-width="3"/>
                            <text x="100" y="70" text-anchor="middle" font-size="10" fill="#4facfe" font-weight="bold">a</text>
                            <text x="100" y="85" text-anchor="middle" font-size="9" fill="#27ae60">Converges</text>
                            
                            <line x1="140" y1="45" x2="140" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="140" y="70" text-anchor="middle" font-size="10" fill="#666">a+1</text>
                            <text x="140" y="85" text-anchor="middle" font-size="9" fill="#27ae60">Converges</text>
                            
                            <line x1="180" y1="45" x2="180" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="180" y="70" text-anchor="middle" font-size="10" fill="#666">a+2</text>
                            <text x="180" y="85" text-anchor="middle" font-size="9" fill="#27ae60">Converges</text>
                        </g>
                        
                        <!-- Arrows indicating continuation -->
                        <path d="M 10 50 L 5 50" stroke="#27ae60" stroke-width="2" marker-end="url(#leftarrow)"/>
                        <path d="M 190 50 L 195 50" stroke="#27ae60" stroke-width="2" marker-end="url(#rightarrow)"/>
                        
                        <!-- Example -->
                        <text x="100" y="110" text-anchor="middle" font-size="10" fill="#333" font-weight="bold">Example:</text>
                        <text x="100" y="125" text-anchor="middle" font-size="9" fill="#333">∑ xⁿ/n!</text>
                    </g>
                    
                    <!-- Case III: Finite R -->
                    <g transform="translate(550, 70)">
                        <text x="100" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#a8edea">
                            Case III: 0 &lt; R &lt; ∞
                        </text>
                        <text x="100" y="20" text-anchor="middle" font-size="12" fill="#666">
                            Interval convergence
                        </text>
                        
                        <!-- Number line -->
                        <line x1="20" y1="50" x2="180" y2="50" stroke="#333" stroke-width="2"/>
                        
                        <!-- Convergence interval -->
                        <line x1="60" y1="50" x2="140" y2="50" stroke="#27ae60" stroke-width="4"/>
                        
                        <!-- Tick marks and labels -->
                        <g>
                            <line x1="20" y1="45" x2="20" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="20" y="70" text-anchor="middle" font-size="10" fill="#666">a-2</text>
                            <text x="20" y="85" text-anchor="middle" font-size="9" fill="#e74c3c">Diverges</text>
                            
                            <line x1="60" y1="45" x2="60" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="60" y="70" text-anchor="middle" font-size="10" fill="#666">a-R</text>
                            <text x="60" y="85" text-anchor="middle" font-size="9" fill="#f39c12">Maybe</text>
                            
                            <line x1="100" y1="45" x2="100" y2="55" stroke="#a8edea" stroke-width="3"/>
                            <text x="100" y="70" text-anchor="middle" font-size="10" fill="#a8edea" font-weight="bold">a</text>
                            <text x="100" y="85" text-anchor="middle" font-size="9" fill="#27ae60">Converges</text>
                            
                            <line x1="140" y1="45" x2="140" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="140" y="70" text-anchor="middle" font-size="10" fill="#666">a+R</text>
                            <text x="140" y="85" text-anchor="middle" font-size="9" fill="#f39c12">Maybe</text>
                            
                            <line x1="180" y1="45" x2="180" y2="55" stroke="#333" stroke-width="1"/>
                            <text x="180" y="70" text-anchor="middle" font-size="10" fill="#666">a+2</text>
                            <text x="180" y="85" text-anchor="middle" font-size="9" fill="#e74c3c">Diverges</text>
                        </g>
                        
                        <!-- Endpoint circles -->
                        <circle cx="60" cy="50" r="3" fill="#f39c12"/>
                        <circle cx="140" cy="50" r="3" fill="#f39c12"/>
                        
                        <!-- Example -->
                        <text x="100" y="110" text-anchor="middle" font-size="10" fill="#333" font-weight="bold">Example:</text>
                        <text x="100" y="125" text-anchor="middle" font-size="9" fill="#333">∑ xⁿ</text>
                    </g>
                    
                    <!-- Detailed explanation section -->
                    <g transform="translate(50, 220)">
                        <text x="350" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">
                            Understanding the Radius of Convergence
                        </text>
                        
                        <!-- Visual explanation -->
                        <g transform="translate(50, 30)">
                            <rect x="0" y="0" width="600" height="120" fill="#fff" stroke="#dee2e6" stroke-width="1" rx="8"/>
                            
                            <!-- Number line for detailed case III -->
                            <g transform="translate(50, 40)">
                                <text x="250" y="0" text-anchor="middle" font-size="12" fill="#333" font-weight="bold">
                                    Case III: Interval of Convergence |x - a| &lt; R
                                </text>
                                
                                <!-- Main line -->
                                <line x1="50" y1="30" x2="450" y2="30" stroke="#333" stroke-width="2"/>
                                
                                <!-- Convergence region -->
                                <line x1="150" y1="30" x2="350" y2="30" stroke="#27ae60" stroke-width="6" opacity="0.7"/>
                                
                                <!-- Divergence regions -->
                                <line x1="50" y1="30" x2="150" y2="30" stroke="#e74c3c" stroke-width="4" opacity="0.7"/>
                                <line x1="350" y1="30" x2="450" y2="30" stroke="#e74c3c" stroke-width="4" opacity="0.7"/>
                                
                                <!-- Key points -->
                                <circle cx="150" cy="30" r="4" fill="#f39c12"/>
                                <circle cx="250" cy="30" r="4" fill="#4facfe"/>
                                <circle cx="350" cy="30" r="4" fill="#f39c12"/>
                                
                                <!-- Labels -->
                                <text x="100" y="20" text-anchor="middle" font-size="11" fill="#e74c3c" font-weight="bold">Diverges</text>
                                <text x="100" y="50" text-anchor="middle" font-size="10" fill="#e74c3c">|x-a| &gt; R</text>
                                
                                <text x="250" y="50" text-anchor="middle" font-size="11" fill="#27ae60" font-weight="bold">Converges</text>
                                <text x="250" y="65" text-anchor="middle" font-size="10" fill="#27ae60">|x-a| &lt; R</text>
                                
                                <text x="400" y="20" text-anchor="middle" font-size="11" fill="#e74c3c" font-weight="bold">Diverges</text>
                                <text x="400" y="50" text-anchor="middle" font-size="10" fill="#e74c3c">|x-a| &gt; R</text>
                                
                                <!-- Point labels -->
                                <text x="150" y="80" text-anchor="middle" font-size="10" fill="#666">a - R</text>
                                <text x="250" y="80" text-anchor="middle" font-size="10" fill="#666">a</text>
                                <text x="350" y="80" text-anchor="middle" font-size="10" fill="#666">a + R</text>
                                
                                <!-- Endpoint uncertainty -->
                                <text x="150" y="95" text-anchor="middle" font-size="9" fill="#f39c12">Check separately</text>
                                <text x="350" y="95" text-anchor="middle" font-size="9" fill="#f39c12">Check separately</text>
                            </g>
                        </g>
                    </g>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="leftarrow" markerWidth="10" markerHeight="7" refX="1" refY="3.5" orient="auto">
                            <polygon points="10 0, 0 3.5, 10 7" fill="#27ae60"/>
                        </marker>
                        <marker id="rightarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="example-box">
                <h3>Examples of Each Case</h3>
                
                <div class="grid-2">
                    <div>
                        <h4>Case I: Point Convergence (R = 0)</h4>
                        <div class="math-display">
                            $$\sum_{n=0}^{\infty} n! x^n = 1 + x + 2!x^2 + 3!x^3 + \cdots$$
                        </div>
                        <p>This series converges only at $x = 0$ because the factorials grow so rapidly that the series diverges for any $x \neq 0$.</p>
                    </div>
                    
                    <div>
                        <h4>Case II: Universal Convergence (R = ∞)</h4>
                        <div class="math-display">
                            $$\sum_{n=0}^{\infty} \frac{x^n}{n!} = 1 + x + \frac{x^2}{2!} + \frac{x^3}{3!} + \cdots$$
                        </div>
                        <p>This series converges for all real numbers $x$. It represents the exponential function $e^x$.</p>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>Case III: Interval Convergence (0 < R < ∞)</h4>
                    <div class="math-display">
                        $$\sum_{n=0}^{\infty} x^n = 1 + x + x^2 + x^3 + \cdots$$
                    </div>
                    <p>This geometric series converges when $|x| < 1$ (so $R = 1$) and diverges when $|x| \geq 1$. At the endpoints $x = \pm 1$, we need to check separately.</p>
                </div>
            </div>

            <div class="info-box">
                <strong>Important Note:</strong> For Case III, the behavior at the endpoints $x = a \pm R$ must be checked individually. The series might converge at one endpoint, both endpoints, or neither endpoint.
            </div>
        </div>

        <!-- Section 4: Finding Radius and Interval of Convergence -->
        <div class="section">
            <h2>4. Finding Radius and Interval of Convergence Using the Ratio Test</h2>
            
            <p>Now that we understand the theory, let's learn the practical method for finding the radius and interval of convergence. The <strong>ratio test</strong> is our primary tool.</p>

            <div class="theorem-box">
                <h3>Method: Using the Ratio Test</h3>
                <p>For a power series $\sum_{n=0}^{\infty} c_n(x-a)^n$:</p>
                
                <ol>
                    <li><strong>Apply the ratio test:</strong> Compute $\lim_{n \to \infty} \left|\frac{c_{n+1}}{c_n}\right| = L$</li>
                    <li><strong>Find the radius:</strong> 
                        <ul>
                            <li>If $L = 0$, then $R = \infty$ (converges everywhere)</li>
                            <li>If $L = \infty$, then $R = 0$ (converges only at center)</li>
                            <li>If $0 < L < \infty$, then $R = \frac{1}{L}$</li>
                        </ul>
                    </li>
                    <li><strong>Check endpoints:</strong> Test convergence at $x = a \pm R$ separately</li>
                </ol>
            </div>

            <div class="example-box">
                <h3>Example 6.1: Finding Interval and Radius of Convergence</h3>
                
                <div style="margin-bottom: 30px;">
                    <h4>Part (a): $\sum_{n=0}^{\infty} \frac{x^n}{n!}$</h4>
                    <p><strong>Step 1:</strong> Apply the ratio test to the coefficients $c_n = \frac{1}{n!}$</p>
                    <div class="math-display">
                        $$\lim_{n \to \infty} \left|\frac{c_{n+1}}{c_n}\right| = \lim_{n \to \infty} \left|\frac{\frac{1}{(n+1)!}}{\frac{1}{n!}}\right| = \lim_{n \to \infty} \frac{n!}{(n+1)!} = \lim_{n \to \infty} \frac{1}{n+1} = 0$$
                    </div>
                    <p><strong>Step 2:</strong> Since $L = 0$, we have $R = \infty$</p>
                    <p><strong>Conclusion:</strong> The series converges for all real numbers $x$. Interval: $(-\infty, \infty)$</p>
                </div>

                <div style="margin-bottom: 30px;">
                    <h4>Part (b): $\sum_{n=0}^{\infty} n! x^n$</h4>
                    <p><strong>Step 1:</strong> Apply the ratio test to the coefficients $c_n = n!$</p>
                    <div class="math-display">
                        $$\lim_{n \to \infty} \left|\frac{c_{n+1}}{c_n}\right| = \lim_{n \to \infty} \left|\frac{(n+1)!}{n!}\right| = \lim_{n \to \infty} (n+1) = \infty$$
                    </div>
                    <p><strong>Step 2:</strong> Since $L = \infty$, we have $R = 0$</p>
                    <p><strong>Conclusion:</strong> The series converges only at $x = 0$. Interval: $\{0\}$</p>
                </div>

                <div>
                    <h4>Part (c): $\sum_{n=0}^{\infty} \frac{(x-2)^n}{(n+1)3^n}$</h4>
                    <p><strong>Step 1:</strong> Apply the ratio test to the coefficients $c_n = \frac{1}{(n+1)3^n}$</p>
                    <div class="math-display">
                        $$\lim_{n \to \infty} \left|\frac{c_{n+1}}{c_n}\right| = \lim_{n \to \infty} \left|\frac{\frac{1}{(n+2)3^{n+1}}}{\frac{1}{(n+1)3^n}}\right| = \lim_{n \to \infty} \frac{n+1}{(n+2) \cdot 3} = \frac{1}{3}$$
                    </div>
                    <p><strong>Step 2:</strong> Since $L = \frac{1}{3}$, we have $R = \frac{1}{L} = 3$</p>
                    <p><strong>Step 3:</strong> Check endpoints $x = 2 \pm 3 = -1, 5$</p>
                    <p>At $x = -1$: $\sum_{n=0}^{\infty} \frac{(-3)^n}{(n+1)3^n} = \sum_{n=0}^{\infty} \frac{(-1)^n}{n+1}$ (converges by alternating series test)</p>
                    <p>At $x = 5$: $\sum_{n=0}^{\infty} \frac{3^n}{(n+1)3^n} = \sum_{n=0}^{\infty} \frac{1}{n+1}$ (diverges as harmonic series)</p>
                    <p><strong>Conclusion:</strong> Interval of convergence is $[-1, 5)$</p>
                </div>
            </div>

            <div class="svg-container">
                <svg width="800" height="600" viewBox="0 0 800 600">
                    <!-- Background -->
                    <rect width="800" height="600" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                        Step-by-Step Process: Finding Convergence
                    </text>
                    
                    <!-- Process flowchart -->
                    <g transform="translate(100, 60)">
                        <!-- Step 1 -->
                        <g>
                            <rect x="0" y="0" width="200" height="80" fill="#4facfe" opacity="0.8" rx="10" stroke="#333" stroke-width="1"/>
                            <text x="100" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Step 1: Apply Ratio Test</text>
                            <text x="100" y="45" text-anchor="middle" font-size="10" fill="white">Compute:</text>
                            <text x="100" y="60" text-anchor="middle" font-size="11" fill="white">L = lim |c_{n+1}/c_n|</text>
                        </g>
                        
                        <!-- Arrow -->
                        <path d="M 100 90 L 100 110" stroke="#333" stroke-width="2" marker-end="url(#arrow2)"/>
                        
                        <!-- Step 2 -->
                        <g transform="translate(0, 120)">
                            <rect x="0" y="0" width="200" height="100" fill="#fa709a" opacity="0.8" rx="10" stroke="#333" stroke-width="1"/>
                            <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Step 2: Find Radius R</text>
                            <text x="100" y="40" text-anchor="middle" font-size="9" fill="white">If L = 0: R = ∞</text>
                            <text x="100" y="55" text-anchor="middle" font-size="9" fill="white">If L = ∞: R = 0</text>
                            <text x="100" y="70" text-anchor="middle" font-size="9" fill="white">If 0 &lt; L &lt; ∞: R = 1/L</text>
                            <text x="100" y="90" text-anchor="middle" font-size="10" fill="white">Series converges for |x-a| &lt; R</text>
                        </g>
                        
                        <!-- Arrow -->
                        <path d="M 100 230 L 100 250" stroke="#333" stroke-width="2" marker-end="url(#arrow2)"/>
                        
                        <!-- Step 3 -->
                        <g transform="translate(0, 260)">
                            <rect x="0" y="0" width="200" height="80" fill="#a8edea" opacity="0.8" rx="10" stroke="#333" stroke-width="1"/>
                            <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">Step 3: Check Endpoints</text>
                            <text x="100" y="40" text-anchor="middle" font-size="10" fill="#333">Test x = a - R and x = a + R</text>
                            <text x="100" y="55" text-anchor="middle" font-size="10" fill="#333">separately for convergence</text>
                        </g>
                    </g>
                    
                    <!-- Examples visualization -->
                    <g transform="translate(350, 60)">
                        <text x="200" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#333">
                            Visual Examples from Example 6.1
                        </text>
                        
                        <!-- Example (a) -->
                        <g transform="translate(50, 30)">
                            <rect x="0" y="0" width="300" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="8"/>
                            <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">∑ xⁿ/n! (Exponential Series)</text>
                            <text x="150" y="40" text-anchor="middle" font-size="10" fill="#666">L = 0 → R = ∞</text>
                            
                            <!-- Number line showing convergence everywhere -->
                            <g transform="translate(50, 50)">
                                <line x1="0" y1="10" x2="200" y2="10" stroke="#27ae60" stroke-width="4"/>
                                <text x="100" y="25" text-anchor="middle" font-size="9" fill="#27ae60">Converges for all x ∈ (-∞, ∞)</text>
                            </g>
                        </g>
                        
                        <!-- Example (b) -->
                        <g transform="translate(50, 130)">
                            <rect x="0" y="0" width="300" height="80" fill="#ffe8e8" stroke="#e74c3c" stroke-width="2" rx="8"/>
                            <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">∑ n! xⁿ (Factorial Series)</text>
                            <text x="150" y="40" text-anchor="middle" font-size="10" fill="#666">L = ∞ → R = 0</text>
                            
                            <!-- Number line showing convergence only at origin -->
                            <g transform="translate(50, 50)">
                                <line x1="0" y1="10" x2="200" y2="10" stroke="#e74c3c" stroke-width="2"/>
                                <circle cx="100" cy="10" r="3" fill="#27ae60"/>
                                <text x="100" y="25" text-anchor="middle" font-size="9" fill="#333">Converges only at x = 0</text>
                            </g>
                        </g>
                        
                        <!-- Example (c) -->
                        <g transform="translate(50, 230)">
                            <rect x="0" y="0" width="300" height="100" fill="#fff2e8" stroke="#f39c12" stroke-width="2" rx="8"/>
                            <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">∑ (x-2)ⁿ/((n+1)3ⁿ)</text>
                            <text x="150" y="40" text-anchor="middle" font-size="10" fill="#666">L = 1/3 → R = 3</text>
                            
                            <!-- Number line showing interval convergence -->
                            <g transform="translate(50, 55)">
                                <line x1="0" y1="10" x2="200" y2="10" stroke="#333" stroke-width="2"/>
                                
                                <!-- Divergence regions -->
                                <line x1="0" y1="10" x2="50" y2="10" stroke="#e74c3c" stroke-width="3"/>
                                <line x1="150" y1="10" x2="200" y2="10" stroke="#e74c3c" stroke-width="3"/>
                                
                                <!-- Convergence region -->
                                <line x1="50" y1="10" x2="150" y2="10" stroke="#27ae60" stroke-width="4"/>
                                
                                <!-- Points -->
                                <circle cx="50" cy="10" r="3" fill="#27ae60"/> <!-- x = -1 (included) -->
                                <circle cx="100" cy="10" r="3" fill="#4facfe"/> <!-- x = 2 (center) -->
                                <circle cx="150" cy="10" r="3" fill="#e74c3c"/> <!-- x = 5 (excluded) -->
                                
                                <!-- Labels -->
                                <text x="50" y="25" text-anchor="middle" font-size="8" fill="#666">-1</text>
                                <text x="100" y="25" text-anchor="middle" font-size="8" fill="#666">2</text>
                                <text x="150" y="25" text-anchor="middle" font-size="8" fill="#666">5</text>
                                
                                <text x="150" y="40" text-anchor="middle" font-size="9" fill="#333">Interval: [-1, 5)</text>
                            </g>
                        </g>
                    </g>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="info-box">
                <strong>Checkpoint 6.1:</strong> Find the interval and radius of convergence for $\sum_{n=1}^{\infty} \frac{x^n}{\sqrt{n}}$.
                <details style="margin-top: 10px;">
                    <summary>Click to see solution</summary>
                    <p style="margin-top: 10px;"><strong>Solution:</strong></p>
                    <p>$c_n = \frac{1}{\sqrt{n}}$, so $\lim_{n \to \infty} \left|\frac{c_{n+1}}{c_n}\right| = \lim_{n \to \infty} \frac{\sqrt{n}}{\sqrt{n+1}} = 1$</p>
                    <p>Therefore $R = 1$. Check endpoints:</p>
                    <p>At $x = 1$: $\sum_{n=1}^{\infty} \frac{1}{\sqrt{n}}$ diverges (p-series with $p = \frac{1}{2} < 1$)</p>
                    <p>At $x = -1$: $\sum_{n=1}^{\infty} \frac{(-1)^n}{\sqrt{n}}$ converges (alternating series test)</p>
                    <p><strong>Answer:</strong> Interval of convergence is $[-1, 1)$, radius is $R = 1$.</p>
                </details>
            </div>
        </div>

        <!-- Section 5: Representing Functions as Power Series -->
        <div class="section">
            <h2>5. Representing Functions as Power Series</h2>
            
            <p>One of the most powerful applications of power series is representing complicated functions as "infinite polynomials." This allows us to use the simple arithmetic operations of polynomials to analyze complex functions.</p>

            <div class="info-box">
                <strong>Why This Matters:</strong><br>
                • Polynomials are easy to differentiate and integrate<br>
                • We can approximate function values using partial sums<br>
                • Complex functions become manageable through their power series representations<br>
                • This forms the foundation for Taylor and Maclaurin series
            </div>

            <div class="theorem-box">
                <h3>Key Result: Geometric Series</h3>
                <p>The fundamental power series representation is:</p>
                <div class="math-display">
                    $$\frac{1}{1-x} = 1 + x + x^2 + x^3 + \cdots = \sum_{n=0}^{\infty} x^n \text{ for } |x| < 1$$
                </div>
                <p>This formula serves as the starting point for representing many other functions.</p>
            </div>

            <div class="example-box">
                <h3>Example 6.2: Graphing Functions and Their Power Series Approximations</h3>
                
                <p>Let's visualize how the partial sums $S_N(x) = \sum_{n=0}^N x^n$ approximate $f(x) = \frac{1}{1-x}$ as $N$ increases.</p>
                
                <div class="svg-container">
                    <svg width="700" height="400" viewBox="0 0 700 400">
                        <!-- Background -->
                        <rect width="700" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                        
                        <!-- Title -->
                        <text x="350" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#667eea">
                            Approximating f(x) = 1/(1-x) with Partial Sums
                        </text>
                        
                        <!-- Coordinate system -->
                        <g transform="translate(50, 50)">
                            <!-- Axes -->
                            <line x1="50" y1="300" x2="550" y2="300" stroke="#333" stroke-width="2"/>
                            <line x1="300" y1="50" x2="300" y2="320" stroke="#333" stroke-width="2"/>
                            
                            <!-- Grid lines -->
                            <g stroke="#ddd" stroke-width="1">
                                <line x1="50" y1="250" x2="550" y2="250"/>
                                <line x1="50" y1="200" x2="550" y2="200"/>
                                <line x1="50" y1="150" x2="550" y2="150"/>
                                <line x1="50" y1="100" x2="550" y2="100"/>
                                
                                <line x1="150" y1="50" x2="150" y2="320"/>
                                <line x1="200" y1="50" x2="200" y2="320"/>
                                <line x1="250" y1="50" x2="250" y2="320"/>
                                <line x1="350" y1="50" x2="350" y2="320"/>
                                <line x1="400" y1="50" x2="400" y2="320"/>
                                <line x1="450" y1="50" x2="450" y2="320"/>
                            </g>
                            
                            <!-- Axis labels -->
                            <text x="275" y="340" text-anchor="middle" font-size="10" fill="#666">-1</text>
                            <text x="300" y="340" text-anchor="middle" font-size="10" fill="#666">0</text>
                            <text x="350" y="340" text-anchor="middle" font-size="10" fill="#666">0.5</text>
                            <text x="400" y="340" text-anchor="middle" font-size="10" fill="#666">1</text>
                            
                            <text x="20" y="305" text-anchor="middle" font-size="10" fill="#666">0</text>
                            <text x="20" y="255" text-anchor="middle" font-size="10" fill="#666">1</text>
                            <text x="20" y="205" text-anchor="middle" font-size="10" fill="#666">2</text>
                            <text x="20" y="155" text-anchor="middle" font-size="10" fill="#666">3</text>
                            <text x="20" y="105" text-anchor="middle" font-size="10" fill="#666">4</text>
                            
                            <!-- True function f(x) = 1/(1-x) -->
                            <path d="M 50 250 Q 150 200, 250 150 Q 300 125, 350 100 Q 375 87, 395 75" 
                                  stroke="#e74c3c" stroke-width="3" fill="none"/>
                            
                            <!-- Partial sums -->
                            <!-- S_0(x) = 1 -->
                            <line x1="50" y1="250" x2="350" y2="250" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <!-- S_1(x) = 1 + x -->
                            <path d="M 50 275 L 350 225" stroke="#f39c12" stroke-width="2" stroke-dasharray="3,3"/>
                            
                            <!-- S_2(x) = 1 + x + x^2 (approximation) -->
                            <path d="M 50 275 Q 200 240, 350 180" stroke="#9b59b6" stroke-width="2" stroke-dasharray="2,2"/>
                            
                            <!-- Legend -->
                            <g transform="translate(450, 80)">
                                <rect x="0" y="0" width="140" height="100" fill="white" stroke="#ccc" stroke-width="1" rx="5"/>
                                <text x="70" y="15" text-anchor="middle" font-size="11" font-weight="bold">Legend</text>
                                
                                <line x1="10" y1="25" x2="30" y2="25" stroke="#e74c3c" stroke-width="3"/>
                                <text x="35" y="30" font-size="9" fill="#333">f(x) = 1/(1-x)</text>
                                
                                <line x1="10" y1="40" x2="30" y2="40" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                                <text x="35" y="45" font-size="9" fill="#333">S₀ = 1</text>
                                
                                <line x1="10" y1="55" x2="30" y2="55" stroke="#f39c12" stroke-width="2" stroke-dasharray="3,3"/>
                                <text x="35" y="60" font-size="9" fill="#333">S₁ = 1 + x</text>
                                
                                <line x1="10" y1="70" x2="30" y2="70" stroke="#9b59b6" stroke-width="2" stroke-dasharray="2,2"/>
                                <text x="35" y="75" font-size="9" fill="#333">S₂ = 1 + x + x²</text>
                                
                                <text x="70" y="90" text-anchor="middle" font-size="8" fill="#666">Better approximation</text>
                                <text x="70" y="100" text-anchor="middle" font-size="8" fill="#666">as N increases</text>
                            </g>
                        </g>
                    </svg>
                </div>
                
                <div class="info-box">
                    <strong>Observation:</strong> As we include more terms in the partial sum, the approximation gets better, especially near $x = 0$. However, all polynomial approximations fail to capture the vertical asymptote at $x = 1$.
                </div>
            </div>

            <div class="example-box">
                <h3>Example 6.3: Representing Functions with Power Series</h3>
                
                <div class="grid-2">
                    <div>
                        <h4>Part (a): $f(x) = \frac{1}{1+x^3}$</h4>
                        <p><strong>Method:</strong> Use substitution in the geometric series formula.</p>
                        <p>Start with: $\frac{1}{1-u} = \sum_{n=0}^{\infty} u^n$ for $|u| < 1$</p>
                        <p>Let $u = -x^3$:</p>
                        <div class="math-display">
                            $$\frac{1}{1+x^3} = \frac{1}{1-(-x^3)} = \sum_{n=0}^{\infty} (-x^3)^n = \sum_{n=0}^{\infty} (-1)^n x^{3n}$$
                        </div>
                        <p>This converges when $|-x^3| < 1$, i.e., when $|x| < 1$.</p>
                        <p><strong>Expanded form:</strong> $1 - x^3 + x^6 - x^9 + x^{12} - \cdots$</p>
                    </div>
                    
                    <div>
                        <h4>Part (b): $f(x) = \frac{x^2}{4-x^2}$</h4>
                        <p><strong>Method:</strong> Factor and use substitution.</p>
                        <p>Rewrite: $\frac{x^2}{4-x^2} = \frac{x^2}{4(1-\frac{x^2}{4})} = \frac{x^2}{4} \cdot \frac{1}{1-\frac{x^2}{4}}$</p>
                        <p>Use geometric series with $u = \frac{x^2}{4}$:</p>
                        <div class="math-display">
                            $$\frac{x^2}{4-x^2} = \frac{x^2}{4} \sum_{n=0}^{\infty} \left(\frac{x^2}{4}\right)^n = \sum_{n=0}^{\infty} \frac{x^{2n+2}}{4^{n+1}}$$
                        </div>
                        <p>This converges when $\left|\frac{x^2}{4}\right| < 1$, i.e., when $|x| < 2$.</p>
                        <p><strong>Expanded form:</strong> $\frac{x^2}{4} + \frac{x^4}{16} + \frac{x^6}{64} + \cdots$</p>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <svg width="800" height="300" viewBox="0 0 800 300">
                    <!-- Background -->
                    <rect width="800" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#667eea">
                        Strategy for Representing Functions as Power Series
                    </text>
                    
                    <!-- Strategy flowchart -->
                    <g transform="translate(50, 60)">
                        <!-- Step 1 -->
                        <g>
                            <rect x="0" y="0" width="150" height="80" fill="#4facfe" opacity="0.8" rx="8"/>
                            <text x="75" y="25" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Start with</text>
                            <text x="75" y="45" text-anchor="middle" font-size="11" fill="white">1/(1-x) = ∑ xⁿ</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="white">|x| &lt; 1</text>
                        </g>
                        
                        <!-- Arrow -->
                        <path d="M 160 40 L 180 40" stroke="#333" stroke-width="2" marker-end="url(#arrow3)"/>
                        
                        <!-- Step 2 -->
                        <g transform="translate(190, 0)">
                            <rect x="0" y="0" width="150" height="80" fill="#fa709a" opacity="0.8" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Manipulate your</text>
                            <text x="75" y="35" text-anchor="middle" font-size="11" fill="white">function to match</text>
                            <text x="75" y="50" text-anchor="middle" font-size="11" fill="white">the pattern</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="white">Factor, substitute</text>
                        </g>
                        
                        <!-- Arrow -->
                        <path d="M 350 40 L 370 40" stroke="#333" stroke-width="2" marker-end="url(#arrow3)"/>
                        
                        <!-- Step 3 -->
                        <g transform="translate(380, 0)">
                            <rect x="0" y="0" width="150" height="80" fill="#a8edea" opacity="0.8" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="#333">Apply substitution</text>
                            <text x="75" y="35" text-anchor="middle" font-size="11" fill="#333">Replace x with</text>
                            <text x="75" y="50" text-anchor="middle" font-size="11" fill="#333">your expression</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="#333">e.g., x → -x³</text>
                        </g>
                        
                        <!-- Arrow -->
                        <path d="M 540 40 L 560 40" stroke="#333" stroke-width="2" marker-end="url(#arrow3)"/>
                        
                        <!-- Step 4 -->
                        <g transform="translate(570, 0)">
                            <rect x="0" y="0" width="150" height="80" fill="#d299c2" opacity="0.8" rx="8"/>
                            <text x="75" y="20" text-anchor="middle" font-size="11" font-weight="bold" fill="#333">Find interval</text>
                            <text x="75" y="35" text-anchor="middle" font-size="11" fill="#333">of convergence</text>
                            <text x="75" y="50" text-anchor="middle" font-size="11" fill="#333">|substitution| &lt; 1</text>
                            <text x="75" y="65" text-anchor="middle" font-size="10" fill="#333">Check endpoints</text>
                        </g>
                    </g>
                    
                    <!-- Examples -->
                    <g transform="translate(50, 160)">
                        <text x="350" y="0" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">
                            Common Substitution Patterns
                        </text>
                        
                        <g transform="translate(0, 25)">
                            <!-- Pattern 1 -->
                            <rect x="0" y="0" width="220" height="60" fill="#e8f5e8" stroke="#27ae60" stroke-width="1" rx="5"/>
                            <text x="110" y="20" text-anchor="middle" font-size="10" font-weight="bold" fill="#333">1/(1+x) pattern</text>
                            <text x="110" y="35" text-anchor="middle" font-size="9" fill="#666">x → -x in geometric series</text>
                            <text x="110" y="50" text-anchor="middle" font-size="9" fill="#333">Result: ∑(-1)ⁿxⁿ</text>
                            
                            <!-- Pattern 2 -->
                            <g transform="translate(240, 0)">
                                <rect x="0" y="0" width="220" height="60" fill="#fff2e8" stroke="#f39c12" stroke-width="1" rx="5"/>
                                <text x="110" y="20" text-anchor="middle" font-size="10" font-weight="bold" fill="#333">1/(1-x²) pattern</text>
                                <text x="110" y="35" text-anchor="middle" font-size="9" fill="#666">x → x² in geometric series</text>
                                <text x="110" y="50" text-anchor="middle" font-size="9" fill="#333">Result: ∑x²ⁿ</text>
                            </g>
                            
                            <!-- Pattern 3 -->
                            <g transform="translate(480, 0)">
                                <rect x="0" y="0" width="220" height="60" fill="#f0e8ff" stroke="#9b59b6" stroke-width="1" rx="5"/>
                                <text x="110" y="20" text-anchor="middle" font-size="10" font-weight="bold" fill="#333">Multiply by x pattern</text>
                                <text x="110" y="35" text-anchor="middle" font-size="9" fill="#666">x/(1-x) = x·∑xⁿ</text>
                                <text x="110" y="50" text-anchor="middle" font-size="9" fill="#333">Result: ∑xⁿ⁺¹</text>
                            </g>
                        </g>
                    </g>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrow3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="info-box">
                <strong>Checkpoint 6.3:</strong> Represent $f(x) = \frac{x^3}{2-x}$ using a power series and find the interval of convergence.
                <details style="margin-top: 10px;">
                    <summary>Click to see solution</summary>
                    <p style="margin-top: 10px;"><strong>Solution:</strong></p>
                    <p>Rewrite: $\frac{x^3}{2-x} = \frac{x^3}{2(1-\frac{x}{2})} = \frac{x^3}{2} \cdot \frac{1}{1-\frac{x}{2}}$</p>
                    <p>Using geometric series: $\frac{x^3}{2-x} = \frac{x^3}{2} \sum_{n=0}^{\infty} \left(\frac{x}{2}\right)^n = \sum_{n=0}^{\infty} \frac{x^{n+3}}{2^{n+1}}$</p>
                    <p>Converges when $\left|\frac{x}{2}\right| < 1$, so $|x| < 2$.</p>
                    <p><strong>Answer:</strong> $\sum_{n=0}^{\infty} \frac{x^{n+3}}{2^{n+1}}$ with interval of convergence $(-2, 2)$.</p>
                </details>
            </div>
        </div>

        <!-- Section 6: Summary and Looking Ahead -->
        <div class="section">
            <h2>6. Summary and Looking Ahead</h2>
            
            <p>In this tutorial, we've explored the fundamental concepts of power series - a cornerstone topic in advanced calculus that bridges finite polynomials with infinite series.</p>

            <div class="theorem-box">
                <h3>Key Takeaways</h3>
                <div class="grid-2">
                    <div>
                        <h4>🎯 Power Series Definition</h4>
                        <p>Series of the form $\sum c_n(x-a)^n$ that represent functions as "infinite polynomials"</p>
                        
                        <h4>🎯 Three Convergence Cases</h4>
                        <p>Point convergence (R=0), universal convergence (R=∞), or interval convergence (0&lt;R&lt;∞)</p>
                    </div>
                    
                    <div>
                        <h4>🎯 Finding Convergence</h4>
                        <p>Use ratio test on coefficients, then check endpoints separately for complete interval</p>
                        
                        <h4>🎯 Function Representation</h4>
                        <p>Start with geometric series and use algebraic manipulation to represent complex functions</p>
                    </div>
                </div>
            </div>

            <div class="svg-container">
                <svg width="700" height="200" viewBox="0 0 700 200">
                    <!-- Background -->
                    <rect width="700" height="200" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="10"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#667eea">
                        Power Series: Foundation for Advanced Topics
                    </text>
                    
                    <!-- Central concept -->
                    <g transform="translate(300, 60)">
                        <circle cx="50" cy="50" r="40" fill="#667eea" opacity="0.8"/>
                        <text x="50" y="45" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Power</text>
                        <text x="50" y="60" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Series</text>
                    </g>
                    
                    <!-- Applications -->
                    <g transform="translate(50, 50)">
                        <!-- Taylor Series -->
                        <rect x="0" y="0" width="100" height="40" fill="#4facfe" opacity="0.7" rx="5"/>
                        <text x="50" y="15" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Taylor</text>
                        <text x="50" y="30" text-anchor="middle" font-size="10" fill="white">Series</text>
                        
                        <!-- Maclaurin Series -->
                        <rect x="0" y="60" width="100" height="40" fill="#fa709a" opacity="0.7" rx="5"/>
                        <text x="50" y="75" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Maclaurin</text>
                        <text x="50" y="90" text-anchor="middle" font-size="10" fill="white">Series</text>
                    </g>
                    
                    <g transform="translate(550, 50)">
                        <!-- Differential Equations -->
                        <rect x="0" y="0" width="100" height="40" fill="#a8edea" opacity="0.7" rx="5"/>
                        <text x="50" y="15" text-anchor="middle" font-size="9" font-weight="bold" fill="#333">Differential</text>
                        <text x="50" y="30" text-anchor="middle" font-size="9" fill="#333">Equations</text>
                        
                        <!-- Function Analysis -->
                        <rect x="0" y="60" width="100" height="40" fill="#d299c2" opacity="0.7" rx="5"/>
                        <text x="50" y="75" text-anchor="middle" font-size="9" font-weight="bold" fill="#333">Function</text>
                        <text x="50" y="90" text-anchor="middle" font-size="9" fill="#333">Analysis</text>
                    </g>
                    
                    <!-- Arrows -->
                    <path d="M 170 70 L 280 70" stroke="#333" stroke-width="2" marker-end="url(#arrow4)"/>
                    <path d="M 170 90 L 280 90" stroke="#333" stroke-width="2" marker-end="url(#arrow4)"/>
                    <path d="M 420 70 L 530 70" stroke="#333" stroke-width="2" marker-end="url(#arrow4)"/>
                    <path d="M 420 90 L 530 90" stroke="#333" stroke-width="2" marker-end="url(#arrow4)"/>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrow4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="example-box">
                <h3>What's Next?</h3>
                <p>Power series form the foundation for many advanced topics in calculus and beyond:</p>
                <ul>
                    <li><strong>Taylor Series:</strong> Represent any differentiable function around a point</li>
                    <li><strong>Maclaurin Series:</strong> Special case of Taylor series centered at origin</li>
                    <li><strong>Series Solutions to Differential Equations:</strong> Use power series to solve complex differential equations</li>
                    <li><strong>Complex Analysis:</strong> Power series extend naturally to complex numbers</li>
                    <li><strong>Numerical Methods:</strong> Use partial sums for function approximation and computation</li>
                </ul>
            </div>

            <div class="info-box">
                <strong>Practice Recommendation:</strong> Master the techniques in this section before moving to Taylor series. The algebraic manipulation skills you've learned here will be essential for more advanced applications.
            </div>
        </div>
    </div>
</body>
</html> 