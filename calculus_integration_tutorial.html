<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculus Integration Tutorial: Approximating Areas</title>
    
    <!-- MathJax 3 Script for HD rendering -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            margin: -20px -20px 40px -20px;
        }
        
        h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .learning-objectives {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            border-radius: 12px;
            background: #f8f9fa;
            border-left: 6px solid #007bff;
        }
        
        .section h2 {
            color: #007bff;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 12px;
            border-bottom: 3px solid #e9ecef;
        }
        
        .section h3 {
            color: #495057;
            font-size: 1.5em;
            margin: 30px 0 20px 0;
        }
        
        .math-block {
            background: #ffffff;
            padding: 25px;
            margin: 25px 0;
            border-radius: 10px;
            border: 1px solid #dee2e6;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #2196f3;
        }
        
        .warning-box {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ff9800;
        }
        
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4caf50;
        }
        
        .outline {
            background: #f1f3f4;
            padding: 30px;
            border-radius: 12px;
            margin: 35px 0;
            border: 2px solid #e8eaed;
        }
        
        .outline h3 {
            color: #1a73e8;
            margin-bottom: 20px;
        }
        
        .outline ol {
            padding-left: 30px;
        }
        
        .outline li {
            margin: 10px 0;
            font-weight: 500;
        }
        
        .outline li ol {
            margin-top: 8px;
        }
        
        .outline li ol li {
            font-weight: normal;
            color: #666;
            margin: 5px 0;
        }
        
        .example-box {
            background: #f0f4f8;
            border: 2px solid #718096;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .example-box h4 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .historical-note {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff8a65;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Calculus Integration Tutorial</h1>
            <div class="subtitle">Approximating Areas Under Curves</div>
            <div class="subtitle">From Archimedes to Riemann Sums</div>
            
            <div class="learning-objectives">
                <h3>🎯 Learning Objectives</h3>
                <ul style="text-align: left; max-width: 600px; margin: 0 auto; padding-left: 20px;">
                    <li>Use sigma (summation) notation to calculate sums and powers of integers</li>
                    <li>Use the sum of rectangular areas to approximate the area under a curve</li>
                    <li>Use Riemann sums to approximate area</li>
                    <li>Understand the limit process that leads to exact integration</li>
                </ul>
            </div>
        </div>
        
        <!-- Tutorial Outline -->
        <div class="outline">
            <h3>📚 Tutorial Outline</h3>
            <ol>
                <li><strong>Introduction & Historical Context</strong>
                    <ol type="a">
                        <li>Archimedes and the method of exhaustion</li>
                        <li>The problem of finding areas under curves</li>
                        <li>Overview of approximation techniques</li>
                    </ol>
                </li>
                <li><strong>Sigma (Summation) Notation</strong>
                    <ol type="a">
                        <li>Introduction to sigma notation</li>
                        <li>Properties of summation</li>
                        <li>Formulas for sums and powers of integers</li>
                        <li>Worked examples and exercises</li>
                    </ol>
                </li>
                <li><strong>Partitions and Rectangular Approximations</strong>
                    <ol type="a">
                        <li>Definition of partitions</li>
                        <li>Left-endpoint approximation</li>
                        <li>Right-endpoint approximation</li>
                        <li>Comparison and accuracy analysis</li>
                    </ol>
                </li>
                <li><strong>Riemann Sums</strong>
                    <ol type="a">
                        <li>General definition of Riemann sums</li>
                        <li>Upper and lower sums</li>
                        <li>Area under the curve definition</li>
                        <li>The limit process</li>
                    </ol>
                </li>
                <li><strong>Practical Examples and Applications</strong>
                    <ol type="a">
                        <li>Step-by-step calculations</li>
                        <li>Visual demonstrations</li>
                        <li>Convergence analysis</li>
                        <li>Real-world applications</li>
                    </ol>
                </li>
                <li><strong>Key Concepts Summary</strong>
                    <ol type="a">
                        <li>Formula reference</li>
                        <li>When to use different methods</li>
                        <li>Connection to definite integrals</li>
                    </ol>
                </li>
            </ol>
        </div>
        
        <!-- Section 1: Introduction & Historical Context -->
        <div class="section">
            <h2>1. Introduction & Historical Context</h2>
            
            <div class="historical-note">
                <h3>🏛️ Archimedes and the Method of Exhaustion</h3>
                <p>Over 2000 years ago, the brilliant Greek mathematician <strong>Archimedes</strong> (287-212 BCE) was fascinated with calculating the areas of various shapes. He developed what we now call the <em>method of exhaustion</em>, a revolutionary approach that used smaller and smaller shapes with known area formulas to fill irregular regions, obtaining closer and closer approximations to the total area.</p>
            </div>
            
            <p>In this section, we develop techniques to approximate the area between a curve, defined by a function $f(x)$, and the $x$-axis on a closed interval $[a,b]$. Like Archimedes, we first approximate the area under the curve using shapes of known area (namely, rectangles). By using smaller and smaller rectangles, we get closer and closer approximations to the area.</p>
            
            <div class="info-box">
                <strong>🎯 The Central Problem:</strong><br>
                Given a continuous, nonnegative function $f(x)$ on interval $[a,b]$, how do we find the exact area between the curve $y = f(x)$ and the $x$-axis?
            </div>
            
            <h3>Visual Understanding of the Problem</h3>
            
            <svg width="100%" height="400" viewBox="0 0 800 400" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">The Area Under a Curve Problem</text>
                
                <!-- Grid -->
                <defs>
                    <pattern id="gridIntro" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                    </pattern>
                </defs>
                <rect x="80" y="50" width="640" height="300" fill="url(#gridIntro)" />
                
                <!-- Axes -->
                <line x1="80" y1="350" x2="720" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                
                <!-- Axis labels -->
                <text x="400" y="380" text-anchor="middle" font-size="14" fill="#333">x</text>
                <text x="40" y="200" text-anchor="middle" font-size="14" fill="#333" transform="rotate(-90, 40, 200)">y</text>
                
                <!-- Function curve -->
                <path d="M 150 320 Q 250 200 350 150 Q 450 120 550 140 Q 650 180 700 220" stroke="#007bff" stroke-width="4" fill="none"/>
                
                <!-- Area under curve (shaded) -->
                <path d="M 150 350 L 150 320 Q 250 200 350 150 Q 450 120 550 140 Q 650 180 700 220 L 700 350 Z" fill="#007bff" opacity="0.2"/>
                
                <!-- Vertical lines at a and b -->
                <line x1="150" y1="50" x2="150" y2="350" stroke="#dc3545" stroke-width="2" stroke-dasharray="5,5"/>
                <line x1="700" y1="50" x2="700" y2="350" stroke="#dc3545" stroke-width="2" stroke-dasharray="5,5"/>
                
                <!-- Labels -->
                <text x="155" y="45" font-size="14" fill="#dc3545" font-weight="bold">x = a</text>
                <text x="705" y="45" font-size="14" fill="#dc3545" font-weight="bold">x = b</text>
                <text x="400" y="100" text-anchor="middle" font-size="14" fill="#007bff" font-weight="bold">y = f(x)</text>
                
                <!-- Area label -->
                <text x="425" y="250" text-anchor="middle" font-size="16" fill="#007bff" font-weight="bold">Area = ?</text>
                
                <!-- X-axis markings -->
                <line x1="150" y1="350" x2="150" y2="355" stroke="#333" stroke-width="1"/>
                <text x="150" y="370" text-anchor="middle" font-size="12">a</text>
                
                <line x1="700" y1="350" x2="700" y2="355" stroke="#333" stroke-width="1"/>
                <text x="700" y="370" text-anchor="middle" font-size="12">b</text>
            </svg>
            
            <h3>The Challenge</h3>
            <p>Unlike simple geometric shapes (rectangles, triangles, circles), we don't have a direct formula for the area under an arbitrary curve. However, we can use a brilliant insight:</p>
            
            <div class="highlight-box">
                <h3>💡 Key Insight: Approximation by Known Shapes</h3>
                <p>If we can approximate the curved region using rectangles (whose areas we can calculate exactly), and if we use enough rectangles that are thin enough, we can get arbitrarily close to the true area.</p>
            </div>
            
            <h3>The Journey Ahead</h3>
            <p>In this tutorial, we'll follow this logical progression:</p>
            
            <div class="success-box">
                <strong>🛤️ Our Path to Integration:</strong>
                <ol style="margin: 10px 0; padding-left: 25px;">
                    <li><strong>Learn notation:</strong> Sigma notation for efficient sum writing</li>
                    <li><strong>Approximate with rectangles:</strong> Left and right endpoint methods</li>
                    <li><strong>Generalize:</strong> Riemann sums with arbitrary evaluation points</li>
                    <li><strong>Take the limit:</strong> As the number of rectangles approaches infinity</li>
                    <li><strong>Arrive at integration:</strong> The exact area under the curve</li>
                </ol>
            </div>
            
            <h3>Why This Matters</h3>
            
            <div class="warning-box">
                <strong>🌍 Real-World Applications:</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>Physics:</strong> Distance from velocity, work from force</li>
                    <li><strong>Economics:</strong> Total cost from marginal cost</li>
                    <li><strong>Biology:</strong> Population growth from growth rate</li>
                    <li><strong>Engineering:</strong> Volume, center of mass, moment of inertia</li>
                    <li><strong>Statistics:</strong> Probability from probability density functions</li>
                </ul>
            </div>
            
            <p>By the end of this tutorial, you'll understand not just <em>how</em> to calculate areas under curves, but <em>why</em> the methods work and how they connect to the fundamental theorem of calculus.</p>
        </div>
        
        <!-- Section 2: Sigma (Summation) Notation -->
        <div class="section">
            <h2>2. Sigma (Summation) Notation</h2>
            
            <p>Before we dive into area approximation, we need an efficient way to write long sums. When approximating areas with rectangles, we'll often need to add up hundreds or even thousands of terms. <strong>Sigma notation</strong> (also called summation notation) provides a compact way to express these lengthy sums.</p>
            
            <h3>Introduction to Sigma Notation</h3>
            
            <p>The Greek capital letter $\Sigma$ (sigma) is used to express long sums. For example, instead of writing:</p>
            
            <div class="math-block">
                $$1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 + 12 + 13 + 14 + 15 + 16 + 17 + 18 + 19 + 20$$
            </div>
            
            <p>We can write this much more compactly as:</p>
            
            <div class="math-block">
                $$\sum_{i=1}^{20} i$$
            </div>
            
            <div class="info-box">
                <strong>🔍 Reading Sigma Notation:</strong><br>
                The general form is $\sum_{i=1}^{n} a_i$, where:
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>$i$ is the <strong>index</strong> (also called a dummy variable)</li>
                    <li>$1$ is the <strong>starting value</strong> of the index</li>
                    <li>$n$ is the <strong>ending value</strong> of the index</li>
                    <li>$a_i$ describes the <strong>terms to be added</strong></li>
                </ul>
            </div>
            
            <h3>Visual Understanding of Sigma Notation</h3>
            
            <svg width="100%" height="350" viewBox="0 0 800 350" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Sigma Notation Breakdown</text>
                
                <!-- Large Sigma -->
                <text x="200" y="180" text-anchor="middle" font-size="80" fill="#007bff" font-weight="bold">Σ</text>
                
                <!-- Index notation -->
                <text x="230" y="200" font-size="18" fill="#dc3545">i=1</text>
                <text x="230" y="130" font-size="18" fill="#dc3545">20</text>
                <text x="280" y="160" font-size="24" fill="#28a745">i</text>
                
                <!-- Arrows with explanations -->
                <g>
                    <!-- Starting value arrow -->
                    <path d="M 270 210 Q 320 220 370 210" stroke="#dc3545" stroke-width="2" fill="none" marker-end="url(#arrowhead1)"/>
                    <text x="380" y="220" font-size="12" fill="#dc3545">Starting value</text>
                    <text x="380" y="235" font-size="12" fill="#dc3545">of index i</text>
                    
                    <!-- Ending value arrow -->
                    <path d="M 270 120 Q 320 100 370 120" stroke="#dc3545" stroke-width="2" fill="none" marker-end="url(#arrowhead1)"/>
                    <text x="380" y="110" font-size="12" fill="#dc3545">Ending value</text>
                    <text x="380" y="125" font-size="12" fill="#dc3545">of index i</text>
                    
                    <!-- Expression arrow -->
                    <path d="M 320 150 Q 360 140 400 150" stroke="#28a745" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <text x="410" y="140" font-size="12" fill="#28a745">Expression to</text>
                    <text x="410" y="155" font-size="12" fill="#28a745">be summed</text>
                </g>
                
                <!-- Expansion example -->
                <g transform="translate(100, 270)">
                    <text x="0" y="0" font-size="14" fill="#333">This means:</text>
                    <text x="80" y="0" font-size="14" fill="#007bff">1 + 2 + 3 + 4 + ... + 19 + 20</text>
                    <text x="0" y="20" font-size="14" fill="#333">Result:</text>
                    <text x="60" y="20" font-size="14" fill="#007bff" font-weight="bold">210</text>
                </g>
                
                <!-- Arrow marker definitions -->
                <defs>
                    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#dc3545"/>
                    </marker>
                    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#28a745"/>
                    </marker>
                </defs>
            </svg>
            
            <h3>Examples of Sigma Notation</h3>
            
            <div class="example-box">
                <h4>Example 1: Powers of 3</h4>
                <p>Write and evaluate: $\sum_{i=1}^{5} 3^i$</p>
                
                <div class="math-block">
                    \begin{align}
                    \sum_{i=1}^{5} 3^i &= 3^1 + 3^2 + 3^3 + 3^4 + 3^5 \\
                    &= 3 + 9 + 27 + 81 + 243 \\
                    &= 363
                    \end{align}
                </div>
            </div>
            
            <div class="example-box">
                <h4>Example 2: Reciprocals of Squares</h4>
                <p>Write in sigma notation: $1 + \frac{1}{4} + \frac{1}{9} + \frac{1}{16} + \frac{1}{25}$</p>
                
                <div class="math-block">
                    $$\sum_{i=1}^{5} \frac{1}{i^2}$$
                </div>
                
                <p>Notice that each denominator is a perfect square!</p>
            </div>
            
            <h3>Properties of Sigma Notation</h3>
            
            <p>These properties make working with sigma notation much easier:</p>
            
            <div class="highlight-box">
                <h3>📐 Key Properties</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div>
                        <strong>Constant Factor:</strong>
                        $$\sum_{i=1}^{n} c \cdot a_i = c \sum_{i=1}^{n} a_i$$
                        
                        <strong>Sum/Difference:</strong>
                        $$\sum_{i=1}^{n} (a_i \pm b_i) = \sum_{i=1}^{n} a_i \pm \sum_{i=1}^{n} b_i$$
                    </div>
                    <div>
                        <strong>Constant Sum:</strong>
                        $$\sum_{i=1}^{n} c = nc$$
                        
                        <strong>Splitting Sums:</strong>
                        $$\sum_{i=1}^{n} a_i = \sum_{i=1}^{m} a_i + \sum_{i=m+1}^{n} a_i$$
                    </div>
                </div>
            </div>
            
            <h3>Essential Sum Formulas</h3>
            
            <p>These formulas are crucial for area calculations:</p>
            
            <div class="math-block">
                <strong>Sum of integers:</strong> \quad \sum_{i=1}^{n} i = \frac{n(n+1)}{2}
            </div>
            
            <div class="math-block">
                <strong>Sum of squares:</strong> \quad \sum_{i=1}^{n} i^2 = \frac{n(n+1)(2n+1)}{6}
            </div>
            
            <div class="math-block">
                <strong>Sum of cubes:</strong> \quad \sum_{i=1}^{n} i^3 = \frac{n^2(n+1)^2}{4}
            </div>
            
            <div class="warning-box">
                <strong>💡 Memory Tip:</strong> The sum of the first $n$ positive integers is just $\frac{n(n+1)}{2}$. For example, $1+2+3+4+5 = \frac{5 \cdot 6}{2} = 15$.
            </div>
            
            <h3>Practice Example</h3>
            
            <div class="example-box">
                <h4>Example 3: Complex Expression</h4>
                <p>Evaluate: $\sum_{i=1}^{6} (i^3 - i^2)$</p>
                
                <div class="math-block">
                    \begin{align}
                    \sum_{i=1}^{6} (i^3 - i^2) &= \sum_{i=1}^{6} i^3 - \sum_{i=1}^{6} i^2 \\
                    &= \frac{6^2(6+1)^2}{4} - \frac{6(6+1)(2 \cdot 6+1)}{6} \\
                    &= \frac{36 \cdot 49}{4} - \frac{6 \cdot 7 \cdot 13}{6} \\
                    &= 441 - 91 \\
                    &= 350
                    \end{align}
                </div>
            </div>
            
            <div class="success-box">
                <strong>🎯 Key Takeaway:</strong> Sigma notation transforms lengthy arithmetic into compact, manageable expressions. This will be essential when we start adding up hundreds of rectangular areas to approximate curves!
            </div>
        </div>
        
        <!-- Section 3: Partitions and Rectangular Approximations -->
        <div class="section">
            <h2>3. Partitions and Rectangular Approximations</h2>
            
            <p>Now we're ready to tackle the main problem: approximating the area under a curve using rectangles. The key insight is to divide the interval into smaller pieces and approximate the area of each piece with a rectangle.</p>
            
            <h3>Partitions: Dividing the Interval</h3>
            
            <div class="info-box">
                <strong>📐 Definition: Partition</strong><br>
                A <em>partition</em> of interval $[a,b]$ is a set of points $P = \{x_0, x_1, x_2, \ldots, x_n\}$ where:<br>
                $$a = x_0 < x_1 < x_2 < \ldots < x_{n-1} < x_n = b$$
                
                If all subintervals have equal width, it's called a <em>regular partition</em>.
            </div>
            
            <p>For a regular partition with $n$ subintervals:</p>
            
            <div class="math-block">
                $$\Delta x = \frac{b-a}{n} \quad \text{and} \quad x_i = a + i \cdot \Delta x$$
            </div>
            
            <h3>Visual Understanding of Partitions</h3>
            
            <svg width="100%" height="300" viewBox="0 0 800 300" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Regular Partition of Interval [a,b]</text>
                
                <!-- Main interval line -->
                <line x1="100" y1="150" x2="700" y2="150" stroke="#333" stroke-width="4"/>
                
                <!-- Partition points -->
                <g stroke="#dc3545" stroke-width="2">
                    <line x1="100" y1="140" x2="100" y2="160"/>
                    <line x1="200" y1="140" x2="200" y2="160"/>
                    <line x1="300" y1="140" x2="300" y2="160"/>
                    <line x1="400" y1="140" x2="400" y2="160"/>
                    <line x1="500" y1="140" x2="500" y2="160"/>
                    <line x1="600" y1="140" x2="600" y2="160"/>
                    <line x1="700" y1="140" x2="700" y2="160"/>
                </g>
                
                <!-- Labels -->
                <text x="100" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₀ = a</text>
                <text x="200" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₁</text>
                <text x="300" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₂</text>
                <text x="400" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₃</text>
                <text x="500" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₄</text>
                <text x="600" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₅</text>
                <text x="700" y="180" text-anchor="middle" font-size="14" fill="#dc3545">x₆ = b</text>
                
                <!-- Δx spacing -->
                <g>
                    <path d="M 100 120 L 200 120" stroke="#28a745" stroke-width="2" marker-start="url(#arrowhead3)" marker-end="url(#arrowhead3)"/>
                    <text x="150" y="110" text-anchor="middle" font-size="14" fill="#28a745">Δx</text>
                    
                    <path d="M 200 100 L 300 100" stroke="#28a745" stroke-width="2" marker-start="url(#arrowhead3)" marker-end="url(#arrowhead3)"/>
                    <text x="250" y="90" text-anchor="middle" font-size="14" fill="#28a745">Δx</text>
                </g>
                
                <!-- Subinterval labels -->
                <text x="150" y="210" text-anchor="middle" font-size="12" fill="#666">[x₀, x₁]</text>
                <text x="250" y="210" text-anchor="middle" font-size="12" fill="#666">[x₁, x₂]</text>
                <text x="350" y="210" text-anchor="middle" font-size="12" fill="#666">[x₂, x₃]</text>
                <text x="550" y="210" text-anchor="middle" font-size="12" fill="#666">...</text>
                
                <!-- Note -->
                <text x="400" y="250" text-anchor="middle" font-size="12" fill="#333">n = 6 subintervals, each of width Δx = (b-a)/6</text>
                
                <!-- Arrow marker -->
                <defs>
                    <marker id="arrowhead3" markerWidth="6" markerHeight="4" refX="3" refY="2" orient="auto">
                        <polygon points="0 0, 6 2, 0 4" fill="#28a745"/>
                    </marker>
                </defs>
            </svg>
            
            <h3>Left-Endpoint Approximation</h3>
            
            <p>The first method uses the <strong>left endpoint</strong> of each subinterval to determine rectangle height:</p>
            
            <div class="highlight-box">
                <h3>📏 Left-Endpoint Approximation</h3>
                $$A \approx L_n = \sum_{i=1}^{n} f(x_{i-1}) \Delta x$$
                <p style="margin-top: 10px;">where $x_{i-1}$ is the left endpoint of the $i$-th subinterval.</p>
            </div>
            
            <h3>Right-Endpoint Approximation</h3>
            
            <p>The second method uses the <strong>right endpoint</strong> of each subinterval:</p>
            
            <div class="highlight-box">
                <h3>📏 Right-Endpoint Approximation</h3>
                $$A \approx R_n = \sum_{i=1}^{n} f(x_i) \Delta x$$
                <p style="margin-top: 10px;">where $x_i$ is the right endpoint of the $i$-th subinterval.</p>
            </div>
            
            <h3>Visual Comparison: Left vs Right Endpoints</h3>
            
            <svg width="100%" height="500" viewBox="0 0 800 500" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Left vs Right Endpoint Approximations</text>
                
                <!-- Left endpoint approximation (top) -->
                <g transform="translate(0, 50)">
                    <text x="50" y="0" font-size="14" font-weight="bold" fill="#dc3545">Left-Endpoint (L₄)</text>
                    
                    <!-- Axes -->
                    <line x1="100" y1="180" x2="700" y2="180" stroke="#333" stroke-width="2"/>
                    <line x1="100" y1="180" x2="100" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Function curve f(x) = x²/2 -->
                    <path d="M 100 180 Q 250 140 400 100 Q 550 70 700 50" stroke="#007bff" stroke-width="3" fill="none"/>
                    
                    <!-- Left-endpoint rectangles -->
                    <g fill="#dc3545" opacity="0.4" stroke="#dc3545" stroke-width="1">
                        <rect x="100" y="180" width="150" height="0"/>     <!-- f(0) = 0 -->
                        <rect x="250" y="152.5" width="150" height="27.5"/> <!-- f(1) ≈ 0.5 -->
                        <rect x="400" y="110" width="150" height="70"/>     <!-- f(2) = 2 -->
                        <rect x="550" y="67.5" width="150" height="112.5"/> <!-- f(3) = 4.5 -->
                    </g>
                    
                    <!-- Grid and labels -->
                    <g stroke="#ccc" stroke-width="1">
                        <line x1="250" y1="50" x2="250" y2="180"/>
                        <line x1="400" y1="50" x2="400" y2="180"/>
                        <line x1="550" y1="50" x2="550" y2="180"/>
                    </g>
                    
                    <text x="100" y="200" text-anchor="middle" font-size="12">0</text>
                    <text x="250" y="200" text-anchor="middle" font-size="12">1</text>
                    <text x="400" y="200" text-anchor="middle" font-size="12">2</text>
                    <text x="550" y="200" text-anchor="middle" font-size="12">3</text>
                    <text x="700" y="200" text-anchor="middle" font-size="12">4</text>
                </g>
                
                <!-- Right endpoint approximation (bottom) -->
                <g transform="translate(0, 270)">
                    <text x="50" y="0" font-size="14" font-weight="bold" fill="#28a745">Right-Endpoint (R₄)</text>
                    
                    <!-- Axes -->
                    <line x1="100" y1="180" x2="700" y2="180" stroke="#333" stroke-width="2"/>
                    <line x1="100" y1="180" x2="100" y2="50" stroke="#333" stroke-width="2"/>
                    
                    <!-- Function curve f(x) = x²/2 -->
                    <path d="M 100 180 Q 250 140 400 100 Q 550 70 700 50" stroke="#007bff" stroke-width="3" fill="none"/>
                    
                    <!-- Right-endpoint rectangles -->
                    <g fill="#28a745" opacity="0.4" stroke="#28a745" stroke-width="1">
                        <rect x="100" y="152.5" width="150" height="27.5"/> <!-- f(1) = 0.5 -->
                        <rect x="250" y="110" width="150" height="70"/>     <!-- f(2) = 2 -->
                        <rect x="400" y="67.5" width="150" height="112.5"/> <!-- f(3) = 4.5 -->
                        <rect x="550" y="30" width="150" height="150"/>     <!-- f(4) = 8 -->
                    </g>
                    
                    <!-- Grid and labels -->
                    <g stroke="#ccc" stroke-width="1">
                        <line x1="250" y1="50" x2="250" y2="180"/>
                        <line x1="400" y1="50" x2="400" y2="180"/>
                        <line x1="550" y1="50" x2="550" y2="180"/>
                    </g>
                    
                    <text x="100" y="200" text-anchor="middle" font-size="12">0</text>
                    <text x="250" y="200" text-anchor="middle" font-size="12">1</text>
                    <text x="400" y="200" text-anchor="middle" font-size="12">2</text>
                    <text x="550" y="200" text-anchor="middle" font-size="12">3</text>
                    <text x="700" y="200" text-anchor="middle" font-size="12">4</text>
                </g>
            </svg>
            
            <h3>Worked Example: f(x) = x² on [0,2]</h3>
            
            <div class="example-box">
                <h4>Example: Left and Right Approximations with n=4</h4>
                <p>Let's approximate the area under $f(x) = x^2$ from $x = 0$ to $x = 2$ using $n = 4$ rectangles.</p>
                
                <p><strong>Step 1:</strong> Find $\Delta x$ and partition points</p>
                <div class="math-block">
                    $$\Delta x = \frac{2-0}{4} = 0.5$$
                    $$x_0 = 0, \quad x_1 = 0.5, \quad x_2 = 1, \quad x_3 = 1.5, \quad x_4 = 2$$
                </div>
                
                <p><strong>Step 2:</strong> Left-endpoint approximation</p>
                <div class="math-block">
                    \begin{align}
                    L_4 &= f(x_0) \Delta x + f(x_1) \Delta x + f(x_2) \Delta x + f(x_3) \Delta x \\
                    &= f(0) \cdot 0.5 + f(0.5) \cdot 0.5 + f(1) \cdot 0.5 + f(1.5) \cdot 0.5 \\
                    &= 0 \cdot 0.5 + 0.25 \cdot 0.5 + 1 \cdot 0.5 + 2.25 \cdot 0.5 \\
                    &= 0 + 0.125 + 0.5 + 1.125 \\
                    &= 1.75 \text{ square units}
                    \end{align}
                </div>
                
                <p><strong>Step 3:</strong> Right-endpoint approximation</p>
                <div class="math-block">
                    \begin{align}
                    R_4 &= f(x_1) \Delta x + f(x_2) \Delta x + f(x_3) \Delta x + f(x_4) \Delta x \\
                    &= f(0.5) \cdot 0.5 + f(1) \cdot 0.5 + f(1.5) \cdot 0.5 + f(2) \cdot 0.5 \\
                    &= 0.25 \cdot 0.5 + 1 \cdot 0.5 + 2.25 \cdot 0.5 + 4 \cdot 0.5 \\
                    &= 0.125 + 0.5 + 1.125 + 2 \\
                    &= 3.75 \text{ square units}
                    \end{align}
                </div>
            </div>
            
            <div class="warning-box">
                <strong>🤔 Notice the Difference:</strong> 
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Left approximation: $L_4 = 1.75$</li>
                    <li>Right approximation: $R_4 = 3.75$</li>
                    <li>The true area under $f(x) = x^2$ from 0 to 2 is $\frac{8}{3} \approx 2.67$</li>
                    <li>Left approximation underestimates (function is increasing)</li>
                    <li>Right approximation overestimates (function is increasing)</li>
                </ul>
            </div>
            
            <h3>Improving Accuracy: More Rectangles</h3>
            
            <p>The key insight is that as we use more rectangles (increase $n$), our approximations get better:</p>
            
            <div class="info-box">
                <strong>📈 Convergence Pattern:</strong>
                <table style="margin: 10px auto; border-collapse: collapse;">
                    <tr style="border-bottom: 2px solid #2196f3;">
                        <th style="padding: 8px; text-align: center;">n</th>
                        <th style="padding: 8px; text-align: center;">$L_n$</th>
                        <th style="padding: 8px; text-align: center;">$R_n$</th>
                        <th style="padding: 8px; text-align: center;">Average</th>
                    </tr>
                    <tr><td style="padding: 5px; text-align: center;">4</td><td style="padding: 5px; text-align: center;">1.75</td><td style="padding: 5px; text-align: center;">3.75</td><td style="padding: 5px; text-align: center;">2.75</td></tr>
                    <tr><td style="padding: 5px; text-align: center;">8</td><td style="padding: 5px; text-align: center;">2.21875</td><td style="padding: 5px; text-align: center;">3.21875</td><td style="padding: 5px; text-align: center;">2.71875</td></tr>
                    <tr><td style="padding: 5px; text-align: center;">16</td><td style="padding: 5px; text-align: center;">2.44</td><td style="padding: 5px; text-align: center;">2.94</td><td style="padding: 5px; text-align: center;">2.69</td></tr>
                    <tr><td style="padding: 5px; text-align: center;">∞</td><td style="padding: 5px; text-align: center; font-weight: bold;">8/3 ≈ 2.67</td><td style="padding: 5px; text-align: center; font-weight: bold;">8/3 ≈ 2.67</td><td style="padding: 5px; text-align: center; font-weight: bold;">8/3 ≈ 2.67</td></tr>
                </table>
            </div>
            
            <div class="success-box">
                <strong>🎯 Key Insight:</strong> Both left and right approximations converge to the same value as $n \to \infty$. This common limit is the exact area under the curve, which leads us to the definition of the definite integral!
            </div>
        </div>
        
        <!-- Section 4: Riemann Sums -->
        <div class="section">
            <h2>4. Riemann Sums</h2>
            
            <p>So far, we've restricted ourselves to evaluating functions at the left or right endpoints of subintervals. But there's no mathematical reason for this restriction! We can evaluate the function at <em>any</em> point within each subinterval. This leads us to the powerful concept of <strong>Riemann sums</strong>.</p>
            
            <h3>General Definition of Riemann Sums</h3>
            
            <div class="info-box">
                <strong>📐 Definition: Riemann Sum</strong><br>
                Let $f(x)$ be defined on interval $[a,b]$ and let $P$ be any partition of $[a,b]$. For each subinterval $[x_{i-1}, x_i]$, choose any point $x_i^*$ in that interval. Then a Riemann sum is:
                
                $$\sum_{i=1}^{n} f(x_i^*) \Delta x_i$$
                
                where $\Delta x_i$ is the width of the $i$-th subinterval.
            </div>
            
            <p>For regular partitions (equal subinterval widths), this becomes:</p>
            
            <div class="math-block">
                $$\sum_{i=1}^{n} f(x_i^*) \Delta x \quad \text{where } \Delta x = \frac{b-a}{n}$$
            </div>
            
            <h3>Visual Understanding: Freedom of Choice</h3>
            
            <svg width="100%" height="400" viewBox="0 0 800 400" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Riemann Sum with Arbitrary Evaluation Points</text>
                
                <!-- Axes -->
                <line x1="80" y1="350" x2="720" y2="350" stroke="#333" stroke-width="2"/>
                <line x1="80" y1="350" x2="80" y2="50" stroke="#333" stroke-width="2"/>
                
                <!-- Function curve -->
                <path d="M 150 320 Q 250 200 350 150 Q 450 120 550 140 Q 650 180 700 220" stroke="#007bff" stroke-width="4" fill="none"/>
                
                <!-- Partition lines -->
                <g stroke="#ccc" stroke-width="1">
                    <line x1="200" y1="50" x2="200" y2="350"/>
                    <line x1="300" y1="50" x2="300" y2="350"/>
                    <line x1="400" y1="50" x2="400" y2="350"/>
                    <line x1="500" y1="50" x2="500" y2="350"/>
                    <line x1="600" y1="50" x2="600" y2="350"/>
                </g>
                
                <!-- Evaluation points (arbitrary positions) -->
                <g fill="#dc3545" r="4">
                    <circle cx="170" cy="350" r="4"/>  <!-- x₁* in first interval -->
                    <circle cx="280" cy="350" r="4"/>  <!-- x₂* in second interval -->
                    <circle cx="350" cy="350" r="4"/>  <!-- x₃* in third interval -->
                    <circle cx="460" cy="350" r="4"/>  <!-- x₄* in fourth interval -->
                    <circle cx="580" cy="350" r="4"/>  <!-- x₅* in fifth interval -->
                    <circle cx="650" cy="350" r="4"/>  <!-- x₆* in sixth interval -->
                </g>
                
                <!-- Rectangles based on function values at evaluation points -->
                <g fill="#007bff" opacity="0.3" stroke="#007bff" stroke-width="1">
                    <rect x="150" y="295" width="50" height="55"/>   <!-- f(x₁*) -->
                    <rect x="200" y="175" width="100" height="175"/> <!-- f(x₂*) -->
                    <rect x="300" y="150" width="100" height="200"/> <!-- f(x₃*) -->
                    <rect x="400" y="125" width="100" height="225"/> <!-- f(x₄*) -->
                    <rect x="500" y="160" width="100" height="190"/> <!-- f(x₅*) -->
                    <rect x="600" y="190" width="100" height="160"/> <!-- f(x₆*) -->
                </g>
                
                <!-- Function value indicators -->
                <g stroke="#dc3545" stroke-width="2">
                    <line x1="170" y1="295" x2="170" y2="350"/>
                    <line x1="280" y1="175" x2="280" y2="350"/>
                    <line x1="350" y1="150" x2="350" y2="350"/>
                    <line x1="460" y1="125" x2="460" y2="350"/>
                    <line x1="580" y1="160" x2="580" y2="350"/>
                    <line x1="650" y1="190" x2="650" y2="350"/>
                </g>
                
                <!-- Labels -->
                <text x="170" y="370" text-anchor="middle" font-size="10" fill="#dc3545">x₁*</text>
                <text x="280" y="370" text-anchor="middle" font-size="10" fill="#dc3545">x₂*</text>
                <text x="350" y="370" text-anchor="middle" font-size="10" fill="#dc3545">x₃*</text>
                <text x="460" y="370" text-anchor="middle" font-size="10" fill="#dc3545">x₄*</text>
                <text x="580" y="370" text-anchor="middle" font-size="10" fill="#dc3545">x₅*</text>
                <text x="650" y="370" text-anchor="middle" font-size="10" fill="#dc3545">x₆*</text>
                
                <!-- Note -->
                <text x="400" y="45" text-anchor="middle" font-size="12" fill="#333">Each x᷈ᵢ* can be anywhere in its subinterval</text>
            </svg>
            
            <h3>Special Cases: Upper and Lower Sums</h3>
            
            <p>While we can choose evaluation points arbitrarily, sometimes we want to guarantee an overestimate or underestimate:</p>
            
            <div class="highlight-box">
                <h3>📊 Upper and Lower Sums</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                    <div>
                        <strong>Upper Sum (Overestimate):</strong><br>
                        Choose $x_i^*$ so that $f(x_i^*)$ is the <em>maximum</em> value of $f$ on $[x_{i-1}, x_i]$
                        
                        $$U_n = \sum_{i=1}^{n} \max_{[x_{i-1}, x_i]} f(x) \cdot \Delta x$$
                    </div>
                    <div>
                        <strong>Lower Sum (Underestimate):</strong><br>
                        Choose $x_i^*$ so that $f(x_i^*)$ is the <em>minimum</em> value of $f$ on $[x_{i-1}, x_i]$
                        
                        $$L_n = \sum_{i=1}^{n} \min_{[x_{i-1}, x_i]} f(x) \cdot \Delta x$$
                    </div>
                </div>
            </div>
            
            <h3>Worked Examples</h3>
            
            <div class="example-box">
                <h4>Example 1: Lower Sum for f(x) = 10 - x²</h4>
                <p>Find a lower sum for $f(x) = 10 - x^2$ on $[1, 2]$ with $n = 4$ subintervals.</p>
                
                <p><strong>Analysis:</strong> Since $f(x) = 10 - x^2$ is decreasing on $[1, 2]$, the minimum values occur at the right endpoints.</p>
                
                <div class="math-block">
                    \begin{align}
                    \Delta x &= \frac{2-1}{4} = 0.25 \\
                    \text{Intervals: } &[1, 1.25], [1.25, 1.5], [1.5, 1.75], [1.75, 2] \\
                    \text{Lower sum} &= f(1.25) \cdot 0.25 + f(1.5) \cdot 0.25 + f(1.75) \cdot 0.25 + f(2) \cdot 0.25 \\
                    &= 8.4375 \cdot 0.25 + 7.75 \cdot 0.25 + 6.9375 \cdot 0.25 + 6 \cdot 0.25 \\
                    &= 7.28 \text{ square units}
                    \end{align}
                </div>
            </div>
            
            <div class="example-box">
                <h4>Example 2: Lower Sum for f(x) = sin(x)</h4>
                <p>Find a lower sum for $f(x) = \sin(x)$ on $[0, \frac{\pi}{2}]$ with $n = 6$ subintervals.</p>
                
                <p><strong>Analysis:</strong> Since $f(x) = \sin(x)$ is increasing on $[0, \frac{\pi}{2}]$, the minimum values occur at the left endpoints.</p>
                
                <div class="math-block">
                    \begin{align}
                    \Delta x &= \frac{\pi/2 - 0}{6} = \frac{\pi}{12} \\
                    \text{Lower sum} &= \sin(0) \cdot \frac{\pi}{12} + \sin(\frac{\pi}{12}) \cdot \frac{\pi}{12} + \sin(\frac{\pi}{6}) \cdot \frac{\pi}{12} + \ldots \\
                    &\approx 0.863 \text{ square units}
                    \end{align}
                </div>
            </div>
            
            <h3>The Fundamental Definition: Area Under the Curve</h3>
            
            <p>Now we're ready for the crucial step that connects approximations to exact areas:</p>
            
            <div class="highlight-box">
                <h3>🎯 Definition: Area Under the Curve</h3>
                Let $f(x)$ be continuous and nonnegative on $[a,b]$. The area under the curve $y = f(x)$ is:
                
                $$A = \lim_{n \to \infty} \sum_{i=1}^{n} f(x_i^*) \Delta x$$
                
                <p style="margin-top: 15px;"><strong>Remarkable fact:</strong> This limit exists and is unique for continuous functions, regardless of how we choose the evaluation points $x_i^*$!</p>
            </div>
            
            <h3>Convergence Visualization</h3>
            
            <svg width="100%" height="600" viewBox="0 0 800 600" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="400" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#007bff">Convergence as n → ∞</text>
                
                <!-- n = 4 approximation -->
                <g transform="translate(50, 50)">
                    <text x="0" y="0" font-size="14" font-weight="bold">n = 4</text>
                    <rect x="0" y="10" width="300" height="120" fill="#f8f9fa" stroke="#ccc"/>
                    <!-- Simple rectangles representation -->
                    <g fill="#007bff" opacity="0.4">
                        <rect x="10" y="90" width="70" height="30"/>
                        <rect x="80" y="70" width="70" height="50"/>
                        <rect x="150" y="50" width="70" height="70"/>
                        <rect x="220" y="30" width="70" height="90"/>
                    </g>
                    <path d="M 10 120 Q 75 100 145 80 Q 215 60 290 40" stroke="#007bff" stroke-width="2" fill="none"/>
                </g>
                
                <!-- n = 8 approximation -->
                <g transform="translate(450, 50)">
                    <text x="0" y="0" font-size="14" font-weight="bold">n = 8</text>
                    <rect x="0" y="10" width="300" height="120" fill="#f8f9fa" stroke="#ccc"/>
                    <!-- More rectangles -->
                    <g fill="#007bff" opacity="0.4">
                        <rect x="10" y="105" width="35" height="15"/>
                        <rect x="45" y="95" width="35" height="25"/>
                        <rect x="80" y="85" width="35" height="35"/>
                        <rect x="115" y="75" width="35" height="45"/>
                        <rect x="150" y="65" width="35" height="55"/>
                        <rect x="185" y="55" width="35" height="65"/>
                        <rect x="220" y="45" width="35" height="75"/>
                        <rect x="255" y="35" width="35" height="85"/>
                    </g>
                    <path d="M 10 120 Q 75 100 145 80 Q 215 60 290 40" stroke="#007bff" stroke-width="2" fill="none"/>
                </g>
                
                <!-- n = 16 approximation -->
                <g transform="translate(50, 220)">
                    <text x="0" y="0" font-size="14" font-weight="bold">n = 16</text>
                    <rect x="0" y="10" width="300" height="120" fill="#f8f9fa" stroke="#ccc"/>
                    <!-- Even more rectangles -->
                    <g fill="#007bff" opacity="0.4">
                        <rect x="10" y="110" width="17.5" height="10"/>
                        <rect x="27.5" y="105" width="17.5" height="15"/>
                        <rect x="45" y="100" width="17.5" height="20"/>
                        <rect x="62.5" y="95" width="17.5" height="25"/>
                        <rect x="80" y="90" width="17.5" height="30"/>
                        <rect x="97.5" y="85" width="17.5" height="35"/>
                        <rect x="115" y="80" width="17.5" height="40"/>
                        <rect x="132.5" y="75" width="17.5" height="45"/>
                        <rect x="150" y="70" width="17.5" height="50"/>
                        <rect x="167.5" y="65" width="17.5" height="55"/>
                        <rect x="185" y="60" width="17.5" height="60"/>
                        <rect x="202.5" y="55" width="17.5" height="65"/>
                        <rect x="220" y="50" width="17.5" height="70"/>
                        <rect x="237.5" y="45" width="17.5" height="75"/>
                        <rect x="255" y="40" width="17.5" height="80"/>
                        <rect x="272.5" y="35" width="17.5" height="85"/>
                    </g>
                    <path d="M 10 120 Q 75 100 145 80 Q 215 60 290 40" stroke="#007bff" stroke-width="2" fill="none"/>
                </g>
                
                <!-- n = ∞ (exact area) -->
                <g transform="translate(450, 220)">
                    <text x="0" y="0" font-size="14" font-weight="bold">n = ∞ (Exact Area)</text>
                    <rect x="0" y="10" width="300" height="120" fill="#f8f9fa" stroke="#ccc"/>
                    <!-- Filled area under curve -->
                    <path d="M 10 120 Q 75 100 145 80 Q 215 60 290 40 L 290 120 Z" fill="#007bff" opacity="0.4"/>
                    <path d="M 10 120 Q 75 100 145 80 Q 215 60 290 40" stroke="#007bff" stroke-width="2" fill="none"/>
                </g>
                
                <!-- Convergence arrow -->
                <g transform="translate(400, 400)">
                    <text x="0" y="0" text-anchor="middle" font-size="24" fill="#28a745">↓</text>
                    <text x="0" y="30" text-anchor="middle" font-size="14" fill="#28a745">As n increases</text>
                    <text x="0" y="50" text-anchor="middle" font-size="16" fill="#007bff" font-weight="bold">Approximation → Exact Area</text>
                </g>
            </svg>
            
            <h3>Key Properties of the Limit</h3>
            
            <div class="info-box">
                <strong>🔬 Important Properties:</strong>
                <ol style="margin: 10px 0; padding-left: 25px;">
                    <li><strong>Existence:</strong> If $f$ is continuous on $[a,b]$, the limit $\lim_{n \to \infty} \sum_{i=1}^{n} f(x_i^*) \Delta x$ always exists.</li>
                    <li><strong>Uniqueness:</strong> The limit value doesn't depend on how we choose the evaluation points $x_i^*$.</li>
                    <li><strong>Independence:</strong> Left-endpoint, right-endpoint, midpoint, and any other choice of $x_i^*$ all give the same limit.</li>
                    <li><strong>Connection to Integration:</strong> This limit is precisely the definite integral $\int_a^b f(x) \, dx$.</li>
                </ol>
            </div>
            
            <h3>From Approximation to Integration</h3>
            
            <p>What we've developed is the foundation of integral calculus! The definite integral is defined as:</p>
            
            <div class="math-block">
                $$\int_a^b f(x) \, dx = \lim_{n \to \infty} \sum_{i=1}^{n} f(x_i^*) \Delta x$$
            </div>
            
            <div class="success-box">
                <strong>🎉 Congratulations!</strong> You've discovered the fundamental connection between:
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Geometric areas under curves</li>
                    <li>Algebraic sums and limits</li>
                    <li>The definite integral</li>
                </ul>
                This is one of the most profound discoveries in mathematics, connecting discrete approximations to continuous exact values!
            </div>
        </div>
        
        <!-- Section 5: Key Concepts Summary -->
        <div class="section">
            <h2>5. Key Concepts Summary</h2>
            
            <h3>📋 Essential Formulas Reference</h3>
            
            <div class="highlight-box">
                <h3>🔧 Core Formulas</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
                    <div>
                        <strong>Sigma Notation Properties:</strong>
                        $$\sum_{i=1}^{n} c = nc$$
                        $$\sum_{i=1}^{n} c \cdot a_i = c \sum_{i=1}^{n} a_i$$
                        $$\sum_{i=1}^{n} (a_i \pm b_i) = \sum_{i=1}^{n} a_i \pm \sum_{i=1}^{n} b_i$$
                        
                        <strong>Power Sums:</strong>
                        $$\sum_{i=1}^{n} i = \frac{n(n+1)}{2}$$
                        $$\sum_{i=1}^{n} i^2 = \frac{n(n+1)(2n+1)}{6}$$
                        $$\sum_{i=1}^{n} i^3 = \frac{n^2(n+1)^2}{4}$$
                    </div>
                    <div>
                        <strong>Approximation Methods:</strong>
                        $$L_n = \sum_{i=1}^{n} f(x_{i-1}) \Delta x$$
                        $$R_n = \sum_{i=1}^{n} f(x_i) \Delta x$$
                        
                        <strong>General Riemann Sum:</strong>
                        $$\sum_{i=1}^{n} f(x_i^*) \Delta x$$
                        
                        <strong>Definite Integral:</strong>
                        $$\int_a^b f(x) \, dx = \lim_{n \to \infty} \sum_{i=1}^{n} f(x_i^*) \Delta x$$
                    </div>
                </div>
            </div>
            
            <h3>🎯 When to Use Each Method</h3>
            
            <div class="info-box">
                <strong>📊 Method Selection Guide:</strong>
                <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                    <tr style="background-color: #f8f9fa; border-bottom: 2px solid #007bff;">
                        <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Method</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Best Used When</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Accuracy</th>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>Left-Endpoint</strong></td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Function is decreasing (gives underestimate)</td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Good for many intervals</td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>Right-Endpoint</strong></td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Function is increasing (gives overestimate)</td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Good for many intervals</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>Upper Sum</strong></td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Need guaranteed overestimate</td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Conservative bound</td>
                    </tr>
                    <tr style="background-color: #f8f9fa;">
                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>Lower Sum</strong></td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Need guaranteed underestimate</td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Conservative bound</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #ddd;"><strong>Midpoint</strong></td>
                        <td style="padding: 10px; border: 1px solid #ddd;">General approximation (not covered in detail)</td>
                        <td style="padding: 10px; border: 1px solid #ddd;">Often most accurate</td>
                    </tr>
                </table>
            </div>
            
            <h3>🌐 Comprehensive Concept Map</h3>
            
            <svg width="100%" height="700" viewBox="0 0 1000 700" style="background: white; border-radius: 8px; margin: 20px 0;">
                <!-- Title -->
                <text x="500" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#007bff">Integration Concepts Mind Map</text>
                
                <!-- Central concept -->
                <ellipse cx="500" cy="350" rx="80" ry="40" fill="#007bff" stroke="#007bff" stroke-width="3"/>
                <text x="500" y="345" text-anchor="middle" font-size="14" fill="white" font-weight="bold">AREA UNDER</text>
                <text x="500" y="360" text-anchor="middle" font-size="14" fill="white" font-weight="bold">CURVE</text>
                
                <!-- Historical Context -->
                <ellipse cx="200" cy="150" rx="70" ry="30" fill="#ff8a65" stroke="#ff5722" stroke-width="2"/>
                <text x="200" y="145" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Archimedes</text>
                <text x="200" y="158" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Method</text>
                <line x1="270" y1="150" x2="430" y2="320" stroke="#ff5722" stroke-width="2"/>
                
                <!-- Sigma Notation -->
                <ellipse cx="150" cy="250" rx="60" ry="25" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                <text x="150" y="247" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Σ Notation</text>
                <text x="150" y="260" text-anchor="middle" font-size="12" fill="white" font-weight="bold">& Formulas</text>
                <line x1="210" y1="250" x2="420" y2="330" stroke="#388e3c" stroke-width="2"/>
                
                <!-- Partitions -->
                <ellipse cx="300" cy="450" rx="50" ry="25" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                <text x="300" y="447" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Partitions</text>
                <text x="300" y="460" text-anchor="middle" font-size="12" fill="white" font-weight="bold">[a,b]</text>
                <line x1="350" y1="435" x2="430" y2="375" stroke="#7b1fa2" stroke-width="2"/>
                
                <!-- Left Endpoint -->
                <ellipse cx="200" cy="550" rx="50" ry="25" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                <text x="200" y="547" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Left</text>
                <text x="200" y="560" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Endpoint</text>
                <line x1="250" y1="540" x2="430" y2="380" stroke="#1976d2" stroke-width="2"/>
                
                <!-- Right Endpoint -->
                <ellipse cx="400" cy="550" rx="50" ry="25" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                <text x="400" y="547" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Right</text>
                <text x="400" y="560" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Endpoint</text>
                <line x1="400" y1="525" x2="470" y2="380" stroke="#1976d2" stroke-width="2"/>
                
                <!-- Riemann Sums -->
                <ellipse cx="700" cy="450" rx="60" ry="25" fill="#ff9800" stroke="#f57c00" stroke-width="2"/>
                <text x="700" y="447" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Riemann</text>
                <text x="700" y="460" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Sums</text>
                <line x1="640" y1="435" x2="570" y2="375" stroke="#f57c00" stroke-width="2"/>
                
                <!-- Upper/Lower Sums -->
                <ellipse cx="800" cy="350" rx="50" ry="25" fill="#795548" stroke="#5d4037" stroke-width="2"/>
                <text x="800" y="347" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Upper/Lower</text>
                <text x="800" y="360" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Sums</text>
                <line x1="720" y1="350" x2="580" y2="350" stroke="#5d4037" stroke-width="2"/>
                
                <!-- Limit Process -->
                <ellipse cx="650" cy="250" rx="60" ry="25" fill="#e91e63" stroke="#c2185b" stroke-width="2"/>
                <text x="650" y="247" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Limit as</text>
                <text x="650" y="260" text-anchor="middle" font-size="12" fill="white" font-weight="bold">n → ∞</text>
                <line x1="590" y1="265" x2="550" y2="320" stroke="#c2185b" stroke-width="2"/>
                
                <!-- Definite Integral -->
                <ellipse cx="750" cy="150" rx="70" ry="30" fill="#673ab7" stroke="#512da8" stroke-width="2"/>
                <text x="750" y="145" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Definite</text>
                <text x="750" y="158" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Integral ∫</text>
                <line x1="680" y1="165" x2="580" y2="320" stroke="#512da8" stroke-width="2"/>
                
                <!-- Applications -->
                <ellipse cx="850" cy="550" rx="60" ry="25" fill="#607d8b" stroke="#455a64" stroke-width="2"/>
                <text x="850" y="547" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Real World</text>
                <text x="850" y="560" text-anchor="middle" font-size="12" fill="white" font-weight="bold">Applications</text>
                <line x1="800" y1="530" x2="570" y2="380" stroke="#455a64" stroke-width="2"/>
                
                <!-- Key relationships arrows -->
                <defs>
                    <marker id="arrowhead4" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                        <polygon points="0 0, 8 3, 0 6" fill="#333"/>
                    </marker>
                </defs>
                
                <!-- Convergence arrow -->
                <path d="M 600 400 Q 650 380 700 400" stroke="#333" stroke-width="3" fill="none" marker-end="url(#arrowhead4)"/>
                <text x="650" y="375" text-anchor="middle" font-size="11" fill="#333">converges to</text>
            </svg>
            
            <h3>🚀 Next Steps in Your Integration Journey</h3>
            
            <div class="warning-box">
                <strong>🔮 Where to Go From Here:</strong>
                <ol style="margin: 15px 0; padding-left: 25px; line-height: 1.8;">
                    <li><strong>Fundamental Theorem of Calculus:</strong> Learn how differentiation and integration are inverse operations</li>
                    <li><strong>Antiderivatives:</strong> Discover techniques for finding exact integrals without limits</li>
                    <li><strong>Integration Techniques:</strong> Master substitution, integration by parts, partial fractions</li>
                    <li><strong>Improper Integrals:</strong> Extend to infinite intervals and discontinuous functions</li>
                    <li><strong>Applications:</strong> Areas between curves, volumes, arc length, work, probability</li>
                    <li><strong>Numerical Integration:</strong> Simpson's Rule, Trapezoid Rule for complex functions</li>
                </ol>
            </div>
            
            <h3>🎓 Final Reflection</h3>
            
            <p>You've just completed a journey that took mathematicians centuries to develop! From Archimedes' geometric insights to Riemann's analytical framework, you've seen how:</p>
            
            <div class="success-box">
                <strong>🌟 The Big Picture:</strong>
                <ul style="margin: 15px 0; padding-left: 25px; line-height: 1.8;">
                    <li><strong>Geometry meets Algebra:</strong> Areas under curves become limits of sums</li>
                    <li><strong>Discrete becomes Continuous:</strong> Finite rectangles lead to infinite precision</li>
                    <li><strong>Approximation becomes Exact:</strong> Riemann sums converge to definite integrals</li>
                    <li><strong>Abstract becomes Practical:</strong> Mathematical theory solves real-world problems</li>
                </ul>
                
                <p style="margin-top: 20px; font-size: 1.1em; text-align: center; font-style: italic;">
                    "Integration is not just a computational tool—it's a bridge between the finite and infinite, 
                    connecting our discrete world of measurements to the continuous realm of mathematics."
                </p>
            </div>
            
            <div class="historical-note">
                <h3>🏆 Congratulations!</h3>
                <p>You now understand one of the most fundamental concepts in all of mathematics. The techniques you've learned form the foundation for advanced calculus, differential equations, physics, engineering, economics, and countless other fields. Keep exploring, keep questioning, and remember that every expert was once a beginner taking exactly the same steps you've just completed!</p>
            </div>
        </div>
    </div>
</body>
</html> 