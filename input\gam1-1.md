g.
Chapter 1
 Linear Models
 Howold is the universe? The standard Big Bang model of the origin of the universe
 says that it expands uniformly, and locally, according to <PERSON><PERSON>’s law,
 y =βx,
 where y is the relative velocity of any two galaxies separated by distance x, and β is
 ‘<PERSON><PERSON>’s constant’ (in standard astrophysical notation y ≡ v, x ≡ d and β ≡ H0).
 β−1 gives the approximateage of the universe, but β is unknown and must somehow
 be estimated from observations of y and x, made for a variety of galaxies at different
 distances from us.
 Figure 1.1 plots velocity against distance for 24 galaxies, according to measure
ments made using the Hubble Space Telescope. Velocities are assessed by measur
ing the Doppler effect red shift in the spectrum of light observed from the galaxies
 concerned, although some correction for ‘local’ velocity components is required.
 Distance measurement is much less direct, and is based on the 1912 discovery, by
 1500
 Velocity (kms−1)
 1000
 500
 5
 10
 Distance (Mpc)
 15
 20
 Figure 1.1 A Hubble diagram showing the relationship between distance, x, and velocity, y,
 for 24 galaxies containing Cepheid stars. The data are from the Hubble Space Telescope key
 project to measure the <PERSON>bble constant as reported in <PERSON><PERSON> et al. (2001).
 1
2
 LINEARMODELS
 <PERSON>, of a relationship between the period of a certain class of variable
 stars, known as the Cepheids, and their luminosity. The intensity of Cepheids varies
 regularly with a period of between 1.5 and something over 50 days, and the mean in
tensity increases predictably with period. This means that, if you can find a Cepheid,
 you can tell how far away it is, by comparing its apparent brightness to its period
 predicted intensity.
 It is clear, from the figure, that the observed data do not follow Hubble’s law
 exactly, but given the measurement process, it would be surprising if they did. Given
 the apparent variability, what can be inferred from these data? In particular: (i) what
 value of β is most consistent with the data? (ii) what range of β values is consistent
 with the dataand(iii) are someparticular,theoretically derived,values of β consistent
 with the data? Statistics is about trying to answer these three sorts of questions.
 One way to proceed is to formulate a linear statistical model of the way that the
 data were generated, and to use this as the basis for inference. Specifically, suppose
 that, rather than being governed directly by Hubble’s law, the observed velocity is
 given by Hubble’s constant multiplied by the observed distance plus a ‘random vari
ability’ term. That is
 yi = βxi +ǫi, i =1...24,
 (1.1)
 where the ǫi terms are independent random variables such that E(ǫi) = 0 and
 E(ǫ2
 i) = σ2. The random component of the model is intended to capture the fact
 that if we gathered a replicate set of data, for a new set of galaxies, Hubble’s law
 would not change, but the apparent random variation from it would be different, as a
 result of different measurement errors. Notice that it is not implied that these errors
 are completely unpredictable: their mean and variance are assumed to be fixed; it is
 only their particular values, for any particular galaxy, that are not known.
 1.1 Asimple linear model
 This section develops statistical methods for a simple linear model of the form (1.1),
 allowing the key conceptsoflinear modellingto be introducedwithoutthe distraction
 of any mathematical difficulty.
 Formally, consider n observations, xi,yi, where yi is an observation on random
 variable, Yi, with expectation, µi ≡ E(Yi). Suppose that an appropriate model for
 the relationship between x and y is:
 Yi = µi +ǫi where µi = xiβ.
 (1.2)
 Here β is an unknown parameter and the ǫi are mutually independent zero mean
 random variables, each with the same variance σ2. So the model says that Y is given
 by x multiplied by a constant plus a random term. Y is an example of a response
 variable, while x is an example of a predictor variable. Figure 1.2 illustrates this
 model for a case where n = 8.
 Simple least squares estimation
 How can β, in model (1.2), be estimated from the xi,yi data? A sensible approach
 is to choose a value of β that makes the model fit closely to the data. To do this we
ASIMPLELINEARMODEL
 3
 Yi
 εi
 xi
 Figure 1.2 Schematic illustration of a simple linear model with one explanatory variable.
 need to define a measure of how well, or how badly, a model with a particular β fits
 the data. One possible measure is the residual sum of squares of the model:
 n
 S =
 i=1
 n
 (yi − µi)2 =
 i=1
 (yi − xiβ)2.
 If we have chosen a good value of β, close to the true value, then the model pre
dicted µi should be relatively close to the yi, so that S should be small, whereas poor
 choices will lead to µi far from their corresponding yi, and high values of S. Hence
 β can be estimated by minimizing S with respect to (w.r.t.) β and this is known as
 the method of least squares.
 To minimize S, differentiate w.r.t. β,
 ∂S
 ∂β =−
 n
 i=1
 2xi(yi − xiβ)
 and set the result to zero to find ˆ β, the least squares estimate of β:
 n
 −
 i=1
 n
 2xi(yi − xi
 ˆ β) = 0 ⇒
 i=1
 1.1.1 Sampling properties of ˆ β
 n
 xiyi − ˆ β
 i=1
 To evaluate the reliability of the least squares estimate, ˆ
 n
 x2
 i = 0 ⇒ ˆ β=
 i=1
 n
 xiyi/
 i=1
 x2
 i.∗
 β, it is useful to consider
 the sampling properties of ˆ β. That is, we should consider some properties of the
 distribution of ˆ
 β valuesthat would beobtainedfromrepeatedindependentreplication
 of the xi,yi data used for estimation. To do this, it is helpful to introduce the concept
 of an estimator, obtained by replacing the observations, yi, in the estimate of ˆ β by
 the random variables, Yi:
 n
 ˆ
 β =
 i=1
 n
 xiYi/
 i=1
 x2
 i.
 βx
 ∗∂2S/∂β2 = 2Px2
 i which is clearly positive, so a minimum of S has been found.
4 LINEARMODELS
 Clearlytheestimator, ˆ β,isarandomvariableandwecanthereforediscussitsdistri
bution.Fornow,consideronlythefirsttwomomentsofthatdistribution.
 Theexpectedvalueof ˆ βisobtainedasfollows:
 E(ˆ β)=E
 n
 i=1
 xiYi/
 n
 i=1
 x2
 i =
 n
 i=1
 xiE(Yi)/
 n
 i=1
 x2
 i=
 n
 i=1
 x2
 iβ/
 n
 i=1
 x2
 i=β.
 So ˆ βisanunbiasedestimator—itsexpectedvalueisequaltothetruevalueofthe
 parameterthatitissupposedtoestimate.
 Unbiasednessisareassuringproperty,butknowingthatanestimatorgetsitright
 onaveragedoesnot tellusmuchabouthowgoodanyoneparticularestimate is
 likelytobe.Forthiswealsoneedtoknowhowmuchestimateswouldvaryfromone
 replicatedatasettothenext—weneedtoknowtheestimatorvariance.
 FromgeneralprobabilitytheoryweknowthatifY1,Y2,...,Ynareindependent
 randomvariablesanda1,a2,...anarerealconstantsthen
 var
 i
 aiYi =
 i
 a2
 ivar(Yi).
 Butwecanwrite
 ˆ β=
 i
 aiYi whereai=xi/
 i
 x2
 i,
 andfromtheoriginalmodelspecificationwehavethatvar(Yi)=σ2foralli.Hence,
 var(ˆ β)=
 i
 x2
 i/
 i
 x2
 i
 2
 σ2=
 i
 x2
 i
 −1
 σ2. (1.3)
 Inmostcircumstancesσ2 isanunknownparameterandmustalsobeestimated.
 Sinceσ2 isthevarianceoftheǫi, itmakessensetoestimateitusingthevarianceof
 the‘estimated’ǫi, themodelresiduals,ˆ ǫi=yi−xi
 ˆ β.Anunbiasedestimatorofσ2
 is:
 ˆ σ2= 1
 n−1 i
 (yi−xi
 ˆ β)2
 (proofofunbiasednessisgivenlaterfor thegeneralcase).Pluggingthisinto(1.3)
 obviouslygivesanunbiasedestimatorofthevarianceof ˆ β.
 1.1.2 Sohowoldistheuniverse?
 Theleast squarescalculationsderivedaboveareavailableaspartof thestatistical
 packageandenvironmentR.Thefunctionlmfits linearmodelstodata, including
 thesimpleexamplecurrentlyunderconsideration.TheCepheiddistance–velocity
 datashowninfigure1.1arestoredinadataframe†hubble.ThefollowingRcode
 fitsthemodelandproducesthe(edited)outputshown.
 †Adataframeisjustatwo-dimensionalarrayofdatainwhichthevaluesofdifferentvariables(which
 mayhavedifferent types)arestoredindifferentnamedcolumns.
ASIMPLELINEARMODEL
 5
 −200 0 200 400 600
 residuals
 −600
 a
 500
 1000
 fitted values
 b
 residuals
 −100 0 100 200
 −300
 1500
 500
 1000
 fitted values
 1500
 Figure 1.3 Residuals against fitted values for (a) the model (1.1) fitted to all the data in figure
 1.1 and (b) the same model fitted to data with two substantial outliers omitted.
 > library(gamair) ## contains ‘hubble’
 > data(hubble)
 > hub.mod <- lm(y ~ x- 1, data=hubble)
 > summary(hub.mod)
 Call:
 lm(formula = y ~ x- 1, data = hubble)
 Coefficients:
 Estimate Std. Error
 x 76.581
 3.965
 The call to lm passed two arguments to the function. The first is a model formula,
 y ~ x- 1,specifyingthe model to be fitted: the name of the response variable is
 to the left of ‘~’ while the predictor variable is specified on the right; the ‘-1’ term
 indicates that the model has no ‘intercept’ term, i.e., that the model is a straight line
 through the origin. The second (optional) argument gives the name of the data frame
 in which the variables are to be found. lm takes this information and uses it to fit the
 model by least squares: the results are returned in a ‘fitted model object’, which in
 this case has been assigned to an object called hub.mod for later examination. ‘<-’
 is the assignment operator, and hub.mod is created by this assignment (overwriting
 any previously existing object of this name).
 Thesummaryfunctionisthenusedto examinethefitted modelobject.Onlypart
 of its output is shown here: ˆ β and the estimate of the standard error of ˆ β (the square
 root of the estimated variance of ˆ β, derived above). Before using these quantities
 it is important to check the model assumptions. In particular we should check the
 plausibility of the assumptions that the ǫi are independent and all have the same
 variance. The way to do this is to examine residual plots.
6
 LINEARMODELS
 The ‘fitted values’ of the model are defined as ˆ µi = ˆ βxi, while the residuals are
 simply ˆ ǫi = yi − ˆ µi. A plot of residuals against fitted values is often useful and the
 following produces the plot in figure 1.3(a).
 plot(fitted(hub.mod),residuals(hub.mod),xlab="fitted values",
 ylab="residuals")
 What we would like to see, in such a plot, is an apparently random scatter of residu
als around zero, with no trend in either the mean of the residuals, or their variability,
 as the fitted values increase. A trend in the mean violates the independence assump
tion, and is usually indicative of something missing in the model structure, while a
 trend in the variability violates the constant variance assumption. The main problem
atic feature of figure 1.3(a) is the presence of two points with very large magnitude
 residuals, suggesting a problem with the constant variance assumption. It is proba
bly prudent to repeat the model fit, with and without these points, to check that they
 are not having undue influence on our conclusions.‡ The following code omits the
 offending points and produces a new residual plot shown in figure 1.3(b).
 > hub.mod1 <- lm(y ~ x- 1,data=hubble[-c(3,15),])
 > summary(hub.mod1)
 Call:
 lm(formula = y ~ x- 1, data = hubble[-c(3, 15), ])
 Coefficients:
 Estimate Std. Error
 x
 77.67
 2.97
 > plot(fitted(hub.mod1),residuals(hub.mod1),
 +
 xlab="fitted values",ylab="residuals")
 The omission of the two large outliers has improved the residuals and changed ˆ β
 somewhat, but not drastically.
 The Hubble constant estimates have units of (km)s−1 (Mpc)−1. A Mega-parsec
 is 3.09×1019km, so we need to divide ˆ βby this amount,in order to obtain Hubble’s
 constant with units of s−1. The approximate age of the universe, in seconds, is then
 given by the reciprocal of ˆ β. Here are the two possible estimates expressed in years:
 > hubble.const <- c(coef(hub.mod),coef(hub.mod1))/3.09e19
 > age <- 1/hubble.const
 > age/(60^2*24*365)
 12794692825 12614854757
 Both fits give an age of around 13 billion years. So we now have an idea of the best
 estimate of the age of the universe, but, given the measurement uncertainties, what
 age range would be consistent with the data?
 ‡The most commonmistake made by students in first courses on regression is simply to drop data with
 large residuals, without further investigation. Beware of this.
ASIMPLELINEARMODEL
 7
 1.1.3 Adding a distributional assumption
 So far everything done with the simple model has been based only on the model
 equations and the two assumptions of independence and equal variance for the re
sponse variable. To go further and find confidence intervals for β, or test hypotheses
 related to the model, a further distributional assumption will be necessary.
 Specifically, assume that ǫi ∼ N(0,σ2) for all i, which is equivalent to assuming
 Yi ∼ N(xiβ,σ2). We have already seen that ˆ β is just a weighted sum of the Yi,
 but the Yi are now assumed to be normal random variables, and a weighted sum of
 normal random variables is itself a normal random variable. Hence the estimator, ˆ β,
 must be a normal random variable. Since we have already established the mean and
 variance of ˆ β, we have that
 ˆ
 β ∼N β, x2
 i
 −1
 σ2 .
 Testing hypotheses about β
 (1.4)
 One thing we might want to do, is to try and evaluate the consistency of some hy
pothesized value of β with the data. For example, some Creation Scientists estimate
 the age of the universe to be 6000 years, based on a reading of the Bible. This would
 imply that β = 163×106.§ The consistency with data of such a hypothesized value
 for β can be based on the probability that we would have observed the ˆ β actually
 obtained, if the true value of β was the hypothetical one.
 Specifically, we can test the null hypothesis, H0 : β = β0, versus the alternative
 hypothesis, H1 : β= β0, for some specified value β0, by examining the probability
 of getting the observed ˆ β, or one further from β0, assuming H0 to be true. If σ2 were
 known then we could work directly from (1.4), as follows.
 The probability required is known as the p-value of the test. It is the probability
 of getting a value of ˆ β at least as favourable to H1 as the one actually observed,
 if H0 is actually true.¶ In this case it helps to distinguish notationally between the
 estimate, ˆ
 βobs , and estimator ˆ β. The p-value is then
 p = Pr |ˆ β−β0|≥|ˆ βobs −β0|H0
 = Pr |ˆ β−β0|/σˆ β ≥ |ˆ βobs −β0|/σˆ β H0
 = Pr(|Z| > |z|)
 where Z ∼ N(0,1), z = (ˆ βobs − β0)/σˆ β and σ2
 ˆ β 
= ( x2
 i)−1σ2. Hence, having
 formedz, the p-valueis easily worked out, using the cumulative distribution function
 for the standard normal built into any statistics package. Small p-values suggest that
 the data are inconsistent with H0, while large values indicate consistency. 0.05 is
 often used as the arbitrary boundary between ‘small’ and ‘large’ in this context.
 §This isn’t really valid, of course, since the Creation Scientists are not postulating a Big Bang theory.
 ¶This definition holds for any hypothesis test, if the specific ‘ˆ
 β’ is replaced by the general ‘a test
 statistic’.
8
 LINEARMODELS
 In reality σ2 is usually unknown. Broadly the same testing procedure can still
 be adopted, by replacing σ with ˆ σ, but we need to somehow allow for the extra
 uncertainty that this introduces (unless the sample size is very large). It turns out that
 if H0 : β = β0 is true then
 ˆ
 T ≡
 β −β0
 ˆ
 σˆ β
 ∼tn−1
 where n is the sample size, ˆ σ2
 ˆ β 
= ( x2
 i)−1ˆ σ2, and tn−1 is the t-distribution with
 n −1degrees of freedom. This result is proven in section 1.3. It is clear that large
 magnitude values of T favour H1, so using T as the test statistic, in place of ˆ β, we
 can calculate a p-value by evaluating
 p =Pr(|T| > |t|)
 where T ∼ tn−1 and t = (ˆ βobs − β0)/ˆ σˆ β. This is easily evaluated using the c.d.f.
 of the t distributions, built into any decent statistics package. Here is some code to
 evaluate the p-value for H0 : ‘the Hubble constant is 163000000’.
 > cs.hubble <- 163000000
 > t.stat <- (coef(hub.mod1)-cs.hubble)/vcov(hub.mod1)[1,1]^0.5
 > pt(t.stat,df=21)*2 # 2 because of |T| in p-value definition
 3.906388e-150
 i.e., as judged using t, the data would be hugely improbable if β = 1.63 × 108. It
 would seem that the hypothesized value can be rejected rather firmly (in this case,
 using the data with the outliers increases the p-value by a factor of 1000 or so).
 Hypothesis testing is useful when there are good reasons to stick with some null
 hypothesis until there are compelling grounds to reject it. This is often the case when
 comparingmodelsofdifferingcomplexity:it is often a goodidea to retain thesimpler
 model until there is quite firm evidence that it is inadequate. Note one interesting
 property of hypothesis testing. If we choose to reject a null hypothesis whenever the
 p-value is less than some fixed level, α (often termed the significance level of a test),
 then we will inevitably reject a proportion, α, of correct null hypotheses. We could
 try and reduce the probability of such mistakes by making α very small, but in that
 case we pay the price of reducing the probability of rejecting H0 when it is false.
 Confidence intervals
 Having seen how to test whether a particular hypothesized value of β is consistent
 with the data, the question naturally arises of what range of values of β would be
 consistent with the data? To answer this, we need to select a definition of ‘consistent’:
 a common choice is to say that any parameter value is consistent with the data if it
 results in a p-value of ≥ 0.05, when used as the null value in a hypothesis test.
 Sticking with the Hubble constant example, and working at a significance level
 of 0.05, we would have rejected any hypothesized value for the constant that re
sulted in a t value outside the range (−2.08,2.08), since these values would result
 in p-values of less than 0.05. The R function qt can be used to find such ranges:
LINEARMODELSINGENERAL
 9
 e.g., qt(c(0.025,0.975),df=21)returns the range of the middle 95% of t21
 random variables. So we would accept any β0 fulfilling:
 ˆ
 −2.08 ≤
 which rearranges to give the interval
 ˆ
 β −β0
 ˆ
 σˆ β
 ≤2.08
 β −2.08ˆ σˆ β ≤ β0 ≤ ˆ β+2.08ˆ σˆ β.
 Such an interval is known as a ‘95% confidence interval’ for β.
 The defining property of a 95% confidence interval is this: if we were to gather
 an infinite sequence of independent replicate data sets, and calculate 95% confidence
 intervals for β from each, then 95% of these intervals would include the true β, and
 5%would not. It is easy to see how this comes about. By construction, a hypothesis
 test with a significance level of 5% rejects the correct null hypothesis for 5% of
 replicate data sets, and accepts it for the other 95% of replicates. Hence 5% of 95%
 confidence intervals must exclude the true parameter, while 95% include it.
 A 95% CI for the Hubble constant (in the usual astrophysicists’ units) is given
 by:
 > sigb <- summary(hub.mod1)$coefficients[2]
 > h.ci <- coef(hub.mod1)+qt(c(0.025,0.975),df=21)*sigb
 > h.ci
 [1] 71.49588 83.84995
 This can be converted to a confidence interval for the age of the universe, in years:
 > h.ci <- h.ci*60^2*24*365.25/3.09e19 # convert to 1/years
 > sort(1/h.ci)
 [1] 11677548698 13695361072
 i.e., the 95% CI is (11.7,13.7) billion years. Actually this ‘Hubble age’ is the age of
 the universe if it has been expanding freely, essentially unfettered by gravitation and
 other effects since the Big Bang. In reality some corrections are needed to get a better
 estimate, and at time of writing this is about 13.8 billion years.
 1.2 Linear models in general
 The simple linear model, introduced above, can be generalized by allowing the re
sponse variable to depend on multiple predictor variables (plus an additive constant).
 These extra predictor variables can themselves be transformationsof the original pre
dictors. Here are some examples, for each of which a response variable datum, yi,
 is treated as an observation on a random variable, Yi, where E(Yi) ≡ µi, the ǫi are
 zero mean random variables, and the βj are model parameters, the values of which
 are unknown and will need to be estimated using data.
 If that makes you feel young, recall that the stuff you are made of is also that old. Feeling small is
 better justified: there are estimated to be something like 1024 stars in the universe, which is approximately
 the number of full stops it would take to cover the surface of the earth (if it was a smooth sphere).
10 LINEARMODELS
 1. µi=β0+xiβ1, Yi=µi+ǫi, isastraight linerelationshipbetweenyand
 predictorvariable,x.
 2. µi=β0+xiβ1+x2
 iβ2+x3
 iβ3, Yi=µi+ǫi,isacubicmodeloftherelationship
 betweenyandx.
 3. µi=β0+xiβ1+ziβ2+log(xizi)β3, Yi=µi+ǫi, isamodel inwhichy
 dependsonpredictorvariablesxandzandonthelogoftheirproduct.
 Eachoftheseisalinearmodelbecausetheǫi termsandthemodelparameters,βj,
 enterthemodelinalinearway.Noticethatthepredictorvariablescanenterthemodel
 non-linearly.Exactlyasforthesimplemodel,theparametersofthesemodelscanbe
 estimatedbyfindingtheβjvalueswhichmakethemodelsbestfittheobserveddata,
 inthesenseofminimizing i(yi−µi)2.Thetheoryfordoingthiswillbedeveloped
 insection1.3,andthatdevelopmentisbasedentirelyonre-writingthelinearmodel
 usingmatricesandvectors.
 Toseehowthisre-writingisdone,considerthestraightlinemodelgivenabove.
 Writingouttheµiequationsforallnpairs,(xi,yi),resultsinalargesystemoflinear
 equations:
 µ1 = β0+x1β1
 µ2 = β0+x2β1
 µ3 = β0+x3β1
 . .
 . .
 µn = β0+xnβ1
 whichcanbere-writteninmatrix-vectorformas
 
       
 µ1
 µ2
 µ3
 .
 .
 µn
 
       
 =
 
       
 1 x1
 1 x2
 1 x3
 . .
 . .
 1 xn
 
       
 β0
 β1
 .
 Sothemodelhasthegeneralformµ=Xβ,i.e.,theexpectedvaluevectorµisgiven
 byamodelmatrix (alsoknownasadesignmatrix),X,multipliedbyaparameter
 vector,β.Alllinearmodelscanbewritteninthisgeneralform.
 Asasecondillustration,thecubicexample,givenabove,canbewritteninmatrix
 vectorformas 
       
 µ1
 µ2
 µ3
 .
 .
 µn
 
       
 =
 
       
 1 x1 x2
 1 x3
 1
 1 x2 x2 2 x3 2
 1 x3 x2
 3 x3
 3
 . . . .
 . . . .
 1 xn x2
 n x3
 n
 
       
 
   
 β0
 β1
 β2
 β3
 
   .
THETHEORYOFLINEARMODELS 11
 Modelsinwhichdataaredividedintodifferentgroups,eachofwhichareassumed
 tohaveadifferentmean,arelessobviouslyof theformµ=Xβ,but infact they
 canbewritteninthisway,byuseofdummyindicatorvariables.Again,thisismost
 easilyseenbyexample.Considerthemodel
 µi=βj ifobservationiisingroupj,
 andsupposethat therearethreegroups,eachwith2data.Thenthemodelcanbe
 re-written 
       
 µ1
 µ2
 µ3
 µ4
 µ5
 µ6
 
       
 =
 
       
 1 0 0
 1 0 0
 0 1 0
 0 1 0
 0 0 1
 0 0 1
 
       
 
 
 β0
 β1
 β2
 
 .
 Variablesindicatingthegrouptowhicharesponseobservationbelongsareknownas
 factorvariables.Somewhatconfusingly,thegroupsthemselvesareknownaslevels
 ofafactor.Sotheabovemodelinvolvesonefactor,‘group’,withthreelevels.Mod
elsof thistype, involvingfactors,arecommonlyusedfor theanalysisofdesigned
 experiments.Inthiscasethemodelmatrixdependsonthedesignoftheexperiment
 (i.e.,onwhichunitsbelongtowhichgroups),andforthisreasontheterms‘design
 matrix’and‘modelmatrix’areoftenusedinterchangeably.Whateveritiscalled,X
 isabsolutelycentraltounderstandingthetheoryoflinearmodels,generalizedlinear
 modelsandgeneralizedadditivemodels.
 1.3 Thetheoryoflinearmodels
 Thissectionshowshowtheparameters,β,ofthelinearmodel
 µ=Xβ, y∼N(µ,Inσ2)
 canbeestimatedbyleast squares. It isassumedthatXisafull rankmatrix,with
 nrowsandpcolumns.Itwillbeshownthattheresultingestimator, ˆ β, isunbiased,
 has the lowestvarianceofanypossible linearestimatorofβ, andthat, giventhe
 normalityofthedata, ˆ β∼N(β,(XTX)−1σ2).Resultsarealsoderivedforsetting
 confidencelimitsonparametersandfortestinghypothesesaboutparameters—in
 particularthehypothesisthatseveralelementsofβaresimultaneouslyzero.
 Inthissectionit is importantnot toconfusethelengthofavectorwithitsdi
mension.Forexample(1,1,1)Thasdimension3andlength√3.Alsonotethatno
 distinctionhasbeenmadenotationallybetweenrandomvariablesandparticularob
servationsof thoserandomvariables: it isusuallyclear fromthecontextwhichis
 meant.
12
 LINEARMODELS
 1.3.1 Least squares estimation of β
 Point estimates of the linear model parameters, β, can be obtained by the method of
 least squares, that is by minimizing
 n
 S =
 i=1
 (yi − µi)2,
 with respect to β. To use least squares with a linear model written in general matrix
vector form, first recall the link between the Euclidean length of a vector and the sum
 of squares of its elements. If v is any vector of dimension, n, then
 n
 v 2 ≡vTv≡
 Hence
 i=1
 v2
 i.
 S = y−µ2= y−Xβ 2.
 Since this expression is simply the squared (Euclidian) length of the vector y −Xβ,
 its value will be unchanged if y − Xβ is rotated. This observation is the basis for a
 practical method for finding ˆ
 β, and for developing the distributional results required
 to use linear models.
 Specifically, like any real matrix, X can always be decomposed
 X=Q R
 0 =QfR
 (1.5)
 where R is a p ×p upper triangular matrix,† and Q is an n × n orthogonal matrix,
 the first p columns of which form Qf (see B.6). Recall that orthogonal matrices
 rotate or reflect vectors, but do not change their length. Orthogonality also means
 that QQT = QTQ=In.ApplyingQT to y−Xβ impliesthat
 y−Xβ 2=QTy−QTXβ 2= QTy− R
 0 β
 2
 .
 Writing QTy = f
 r ,wheref is vector of dimension p, and hence r is a vector of
 dimension n − p, yields
 y−Xβ 2= f
 r − R
 0 β
 2
 = f−Rβ 2+ r 2.‡
 The length of r does not depend on β, while f − Rβ 2 can be reduced to zero by
 choosing β so that Rβ equals f. Hence
 ˆ
 β =R−1f
 †
 ‡
 P
 i.e., Ri,j = 0 if i > j.
 If the last equality isn’t obvious recall that x2 = P
 i x2
 i , so if x = 
(1.6)
 v
 w , x2=P
 iv2
 i+
 i w2
 i = v 2+w2.
THETHEORYOFLINEARMODELS
 13
 is the least squares estimator of β. Notice that r 2 = y−Xˆ β 2, the residual sum
 of squares for the model fit.
 1.3.2 The distribution of ˆ
 β
 The distribution of the estimator, ˆ
 β, follows from that of QTy. Multivariate normal
ity of QTy follows from that of y, and since the covariance matrix of y is Inσ2, the
 covariance matrix of QTy is
 VQTy =QTInQσ2 =Inσ2.
 Furthermore,
 i.e., we have that
 E f
 r =E(QTy)=QTXβ= R
 0 β
 ⇒E(f) =RβandE(r) = 0,
 f ∼N(Rβ,Ipσ2) and r ∼ N(0,In−pσ2)
 with both vectors independent of each other.
 Turning to the properties of ˆ
 β itself, unbiasedness follows immediately:
 E(ˆ β) = R−1E(f) = R−1Rβ = β.
 Since the covariance matrix of f is Ipσ2, it follows that the covariance matrix of ˆ
 Vˆ β = R−1IpR−Tσ2 = R−1R−Tσ2.
 Furthermore, since ˆ
 β is
 (1.7)
 β is just a linear transformation of the normal random variables
 f, it must have a multivariate normal distribution,
 ˆ
 β ∼N(β,Vˆ β).
 The foregoing distributional result is not usually directly useful for making infer
ences about β, since σ2 is generally unknown and must be estimated, thereby intro
ducing an extra component of variability that should be accounted for.
 1.3.3 (ˆ βi − βi)/ˆ σˆ βi 
∼ tn−p
 Since the elements of r are i.i.d. N(0,σ2), the ri/σ are i.i.d. N(0,1) random vari
ables, and hence
 1
 σ2
 r 2 = 1
 σ2 
n−p
 i=1
 r2
 i ∼ χ2
 n−p.
 The mean of a χ2
 n−p r.v. is n − p, so this result is sufficient (but not necessary: see
 exercise 7) to imply that
 ˆ
 σ2 = r 2/(n−p)
 (1.8)
14
 LINEARMODELS
 is an unbiased estimator of σ2.§ The independence of the elements of r and f also
 implies that ˆ
 β and ˆ σ2 are independent.
 Now consider a single parameter estimator, ˆ βi, with standard deviation, σˆ βi
 ,
 given by the square root of element i,i of Vˆ β. An unbiased estimator of Vˆ β is
 ˆ
 Vˆ β = Vˆ βˆ σ2/σ2 = R−1R−Tˆ σ2, so an estimator, ˆ σˆ βi
 , is given by the square root of
 element i,i of ˆ Vˆ β, and it is clear that ˆ σˆ βi 
= σˆ βi
 ˆ σ/σ. Hence
 ˆ
 βi −βi
 ˆ
 σˆ βi
 ˆ
 =
 βi −βi
 σˆ βi 
ˆ
 σ/σ =
 (ˆ βi − βi)/σˆ βi
 1
 σ2 
r 2/(n−p)
 ∼tn−p
 ∼ N(0,1)
 χ2 n−p/(n − p)
 (where the independenceof ˆ βi and ˆ σ2 has been used). This result enables confidence
 intervals for βi to be found, and is the basis for hypothesis tests about individual βi’s
 (for example, H0 : βi = 0).
 1.3.4 F-ratio results I
 Sometimes it is useful to be able to test H0 : Cβ = d, where C is q × p and rank
 q (< p). Under H0 we have Cˆ β − d ∼ N(0,CVˆ βCT,), from basic properties of
 the transformation of normal random vectors. Forming a Cholesky decomposition
 LTL = CVˆ βCT (see B.7), it is then easy to show that L−T(Cˆ β − d) ∼ N(0,I),
 so,
 (Cˆ β −d)T(CVˆ βCT)−1(Cˆ β −d) =
 (Cˆ β −d)TL−1L−T(Cˆ β−d) ∼
 As in the previous section, plugging in ˆ
 q
 i=1
 N(0,1)2 ∼ χ2
 q.
 Vˆ β = Vˆ βˆ σ2/σ2 gives the computable test
 statistic and its distribution under H0:
 1
 q
 (Cˆ β −d)T(Cˆ Vˆ βCT)−1(Cˆ β − d) = σ2
 =
 qˆ σ2 
(C ˆ
 β −d)T(CVˆ βCT)−1(Cˆ β−d)
 (Cˆ β −d)T(CVˆ βCT)−1(Cˆ β −d)/q
 1
 σ2 
r 2/(n−p)
 ∼ χ2
 q/q
 χ2 n−p/(n − p) ∼ Fq,n−p. (1.9)
 This result can be used to compute a p-value for the test.
 1.3.5 F-ratio results II
 An alternative F-ratio test derivation is also useful. Consider testing the simultane
ous equality to zero of several model parameters. Such tests are useful for making
 §Don’t forget that r 2 = y −Xˆ β 2.
THETHEORYOFLINEARMODELS
 15
 inferences about factor variables and their interactions, since each factor or interac
tion is typically represented by several elements of β. More specifically suppose that
 the model matrix is partitioned X = [X0 : X1], where X0 and X1 have p − q and
 q columns, respectively. Let β0 and β1 be the corresponding sub-vectors of β, and
 consider testing
 H0 : β1 = 0 versus H1 : β1=0.
 Any test involving the comparison of a linear model with a simplified null version
 of the model can be written in this form, by re-ordering of the columns of X or by
 re-parameterization. Now
 QTX0 = R0
 0 
where R0 is the first p − q rows and columns of R (Q and R are from (1.5)). So
 rotating y − X0β0 using QT implies that
 y−X0β0 2 =QTy−QTX0β0 2= f0−R0β0 2+ f1 2+ r 2
 where f has been partitioned so that f = f0
 f1
 (f1 being of dimension q). Hence
 f1 2 is the increase in residual sum of squares that results from dropping X1 from
 the model (i.e. setting β1 = 0).
 Now, since E(f) = Rβ andRisuppertriangular,then E(f1) = 0 if β1 = 0 (i.e.
 if H0 is true). Also, we already know that the elements of f1 are independent normal
 r.v.s with variance σ2. Hence, if H0 is true, f1 ∼ N(0,Iqσ2) and consequently
 1
 σ2
 f1 2 ∼χ2
 q.
 So, forming an ‘F-ratio statistic’, F, assuming H0, and recalling the independenceof
 f and r we have
 F = f1 2/q
 ˆ
 σ2
 1
 σ2 
f1 2/q
 =
 1
 σ2 
r 2/(n−p) ∼
 χ2
 q/q
 χ2 n−p/(n − p) ∼ Fq,n−p
 which can be used to compute a p-value for H0, thereby testing the significance of
 model terms. Notice that for comparingany null model with residual sum of squares,
 RSS0, to a full model with residual sum of squares, RSS1, the preceding derivation
 holds, but the test statistic can also be computed (without any initial re-ordering or
 re-parameterization) as
 F = (RSS0 −RSS1)/q
 RSS1/(n −p) .
 In slightly more generality, if β is partitioned into sub-vectors β0,β1 ...,βm
 (each usually relating to a different effect), of dimensions q0,q1,...,qm, then f can
 also be so partitioned, fT = [fT 0,fT 1,...,fT m], and tests of
 H0 : βj = 0versusH1 : βj= 0
16
 LINEARMODELS
 are conducted using the result that under H0
 F = fj 2/qj
 ˆ
 σ2
 ∼Fqj,n−p,
 with F larger than this suggests, if the alternative is true. This is the result used to
 draw upsequential ANOVAtables for a fitted model, of the sort producedby a single
 argument call to anova in R. Note, however, that the hypothesis test about βj is
 only valid in general if βk = 0 for all k such that j < k ≤ m: this follows from
 the way that the test was derived, and is the reason that the ANOVA tables resulting
 from such procedures are referred to as ‘sequential’ tables. The practical upshot is
 that, if models are reduced in a differentorder,the p-valuesobtained will be different.
 The exception to this is if the ˆ
 βj’s are mutually independent, in which case all the
 tests are simultaneously valid, and the ANOVA table for a model is not dependenton
 the order of model terms: such independent ˆ
 βj’s usually arise only in the context of
 balanced data, from designed experiments.
 Notice that sequential ANOVA tablesare veryeasy to calculate: once a modelhas
 been fitted by the QR method,all the relevant ‘sums of squares’ are easily calculated
 directly from the elements of f, with r 2 providing the residual sum of squares.
 1.3.6 The influence matrix
 One matrix that will feature extensively in the discussion of GAMs is the influence
 matrix (or hat matrix) of a linear model. This is the matrix which yields the fitted
 value vector, ˆ µ, when post-multiplied by the data vector, y. Recalling the definition
 of Qf, as being the first p columns of Q, f = QT
 fy and so
 ˆ
 β =R−1QT
 fy.
 Furthermore ˆ µ = Xˆ β and X = QfR so
 ˆ
 µ=QfRR−1QT
 fy = QfQT
 fy
 i.e., the matrix A ≡ QfQT
 f is the influence (hat) matrix such that ˆ µ = Ay.
 The influence matrix has a couple of interesting properties. Firstly, the trace of
 the influence matrix is the number of (identifiable) parameters in the model, since
 tr (A) = trQfQT
 f = tr QT
 fQf = tr(Ip) = p.
 Secondly, AA = A, a property known as idempotency. Again the proof is simple:
 AA=QfQT
 fQfQT
 f = QfIpQT
 f = QfQT
 f = A.
 1.3.7 The residuals, ˆ ǫ, and fitted values, ˆ µ
 The influence matrix is helpful in deriving properties of the fitted values, ˆ µ, and
 residuals, ˆ ǫ. ˆ
 µ is unbiased, since E(ˆ µ) = E(Xˆ β) = XE(ˆ β) = Xβ = µ. The
THETHEORYOFLINEARMODELS
 17
 covariance matrix of the fitted values is obtained from the fact that ˆ µ is a linear
 transformation of the random vector y, which has covariance matrix Inσ2, so that
 Vˆ µ = AInATσ2 = Aσ2,
 by the idempotence (and symmetry) of A. The distribution of ˆ µ is degenerate multi
variate normal.
 Similar arguments apply to the residuals.
 ˆ
 ǫ =(I−A)y,
 so
 E(ˆ ǫ) = E(y) −E(ˆ µ) = µ−µ = 0.
 Proceeding as in the fitted value case we have
 Vˆ ǫ = (In −A)In(In −A)Tσ2 = (In −2A+AA)σ2 =(In −A)σ2.
 Again, the distribution of the residuals will be degenerate normal. The results for the
 residuals are useful for model checking, since they allow the residuals to be stan
dardized so that they should have constant variance if the model is correct.
 1.3.8 Results in terms of X
 The presentation so far has been in terms of the method actually used to fit linear
 models in practice (the QR decomposition approach¶), which also greatly facilitates
 the derivation of the distributional results required for practical modelling. However,
 for historical reasons, these results are more usually presented in terms of the model
 matrix, X, rather than the components of its QR decomposition. For completeness
 some of the results are restated here, in terms of X.
 Firstly consider the covariance matrix of β. This turns out to be (XTX)−1σ2,
 which is easily seen to be equivalent to (1.7) as follows:
 Vˆ β = (XTX)−1σ2 = RTQT
 fQfR−1σ2 = RTR−1σ2 =R−1R−Tσ2.
 Theexpressionforthe least squaresestimatesis ˆ
 β =(XTX)−1XTy,whichisequiv
alent to (1.6):
 ˆ
 β =(XTX)−1XTy =R−1R−TRTQT
 fy =R−1QT
 fy =R−1f.
 Given this last result, it is easy to see that the influence matrix can be written:
 A=X(XTX)−1XT.
 These results are of largely historical and theoretical interest: they should not gener
ally be used for computational purposes, and derivation of the distributional results
 is much more difficult if one starts from these formulae.
 ¶Some software still fits models by solution of XTXˆ
 β =XTy,butthis isless computationally stable
 than the orthogonal decomposition method described here, although it is a bit faster.
18
 LINEARMODELS
 1.3.9 The Gauss Markov Theorem: What’s special about least squares?
 How good are least squares estimators? In particular, might it be possible to find
 better estimators, in the sense of having lower variance while still being unbiased?
 The Gauss Markov theoremshows that least squares estimators have the lowest vari
ance of all unbiased estimators that are linear (meaning that the data only enter the
 estimation process in a linear way).
 Theorem 1. Suppose that µ ≡ E(Y) = Xβ and Vy = σ2I, and let ˜ φ = cTY be
 any unbiased linear estimator of φ = tTβ, where t is an arbitrary vector. Then:
 var(˜ φ) ≥ var(ˆ φ)
 where ˆ φ = tT ˆ β, and ˆ β = (XTX)−1XTY istheleastsquaresestimator ofβ. Notice
 that, since t is arbitrary, this theorem implies that each element of ˆ
 β is a minimum
 variance unbiased estimator.
 Proof. Since ˜ φ is a linear transformation of Y, var(˜ φ) = cTcσ2. To compare vari
ances of ˆ φ and ˜ φ it is also useful to express var(ˆ φ) in terms of c. To do this, note that
 unbiasedness of ˜ φ implies that
 E(cTY) = tTβ ⇒cTE(Y) = tTβ ⇒cTXβ =tTβ ⇒cTX=tT.
 So the variance of ˆ φ can be written as
 var(ˆ φ) = var(tT ˆ
 β) =var(cTXˆ β).
 This is the variance of a linear transformation of ˆ
 is (XTX)−1σ2, so
 β, and the covariance matrix of ˆ β
 var(ˆ φ) = var(cTXˆ β) = cTX(XTX)−1XTcσ2 = cTAcσ2
 (where A is the influenceor hat matrix). Now the variancesof the two estimators can
 be directly compared, and it can be seen that var(˜ φ) ≥ var(ˆ φ) iff
 cT(I −A)c ≥ 0.
 This condition will always be met, because it is equivalent to:
 {(I −A)c}T(I −A)c ≥ 0
 by the idempotency and symmetry of A and henceof (I−A), but this last condition
 is saying that a sum of squares can not be less than 0, which is clearly true.
 Notice that this theorem uses independence and equal variance assumptions, but
 does not assume normality. Of course there is a sense in which the theorem is in
tuitively rather unsurprising, since it says that the minimum variance estimators are
 those obtained by seeking to minimize the residual variance.
THEGEOMETRYOFLINEARMODELLING
 19
 0.8
 0.6
 0.4
 y
 0.2
 0.0
 3
 2
 1
 0.0
 0.2
 0.4
 0.6
 x
 0.8
 1.0
 1
 Figure 1.4 The geometry of least squares. The left panel shows a straight line model fitted to
 3 data by least squares. The right panel gives a geometric interpretation of the fitting process.
 The 3-dimensional space shown is spanned by 3 orthogonal axes: one for each response vari
able. The observed response vector, y, is shown as a point (•) within this space. The columns
 of the model matrix define two directions within the space: the thick and dashed lines from
 the origin. The model states that E(y) could be any linear combination of these vectors, i.e.,
 anywhere in the ‘model subspace’ indicated by the grey plane. Least squares fitting finds the
 closest point in the model subspace to the response data (•): the ‘fitted values’. The short thick
 line joins the response data to the fitted values: it is the ‘residual vector’.