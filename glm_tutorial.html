<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generalized Linear Models Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .outline {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        
        .outline h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        
        .outline-list {
            list-style: none;
            counter-reset: section;
        }
        
        .outline-list li {
            counter-increment: section;
            margin-bottom: 0.8rem;
            padding-left: 2rem;
            position: relative;
        }
        
        .outline-list li::before {
            content: counter(section);
            position: absolute;
            left: 0;
            top: 0;
            background: #667eea;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .outline-list li a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .outline-list li a:hover {
            color: #667eea;
        }
        
        .section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #764ba2;
            margin: 1.5rem 0 1rem 0;
            font-size: 1.5rem;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #e17055;
        }
        
        .formula-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #00b894;
        }
        
        .progress-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="progress-indicator">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <div class="container">
        <header>
            <h1>Generalized Linear Models</h1>
            <p class="subtitle">A Comprehensive Tutorial on GLM Theory and Applications</p>
        </header>
        
        <div class="outline">
            <h2>Tutorial Outline</h2>
            <ol class="outline-list">
                <li><a href="#introduction">Introduction to Generalized Linear Models</a></li>
                <li><a href="#basic-structure">Basic GLM Structure and Components</a></li>
                <li><a href="#example1">Practical Example 1: Disease Epidemic Modeling</a></li>
                <li><a href="#example2">Practical Example 2: Predator-Prey Dynamics</a></li>
                <li><a href="#exponential-family">The Exponential Family of Distributions</a></li>
                <li><a href="#theory">Mathematical Theory and Derivations</a></li>
                <li><a href="#distributions">Distribution Comparison and Properties</a></li>
                <li><a href="#visualization">Conceptual Relationships and Visualizations</a></li>
            </ol>
        </div>
        
        <!-- Introduction Section -->
        <section id="introduction" class="section">
            <h2>Introduction to Generalized Linear Models</h2>
            
            <p>Generalized Linear Models (GLMs), introduced by Nelder and Wedderburn in 1972, represent a powerful extension of ordinary linear regression that allows for:</p>
            
            <div class="highlight-box">
                <ul>
                    <li><strong>Non-normal response distributions</strong> - beyond just Gaussian distributions</li>
                    <li><strong>Non-linear relationships</strong> - through link functions</li>
                    <li><strong>Flexible modeling</strong> - for count data, binary outcomes, and more</li>
                </ul>
            </div>
            
            <h3>Why Do We Need GLMs?</h3>
            
            <p>Traditional linear regression assumes that the response variable follows a normal distribution with constant variance. However, many real-world scenarios violate these assumptions:</p>
            
            <ul>
                <li><strong>Count data</strong> (number of events) - typically follows Poisson distribution</li>
                <li><strong>Binary outcomes</strong> (success/failure) - follows Binomial distribution</li>
                <li><strong>Positive continuous data</strong> - may follow Gamma distribution</li>
                <li><strong>Non-linear relationships</strong> - require transformation of the response scale</li>
            </ul>
            
            <div class="formula-box">
                <h4>Basic GLM Structure</h4>
                <p>A GLM has the fundamental form:</p>
                $$g(\mu_i) = X_i\beta$$
                <p>where:</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>$\mu_i \equiv E(Y_i)$ is the expected value of the response</li>
                    <li>$g$ is the <strong>link function</strong> (smooth and monotonic)</li>
                    <li>$X_i$ is the $i$-th row of the model matrix</li>
                    <li>$\beta$ is the vector of unknown parameters</li>
                </ul>
            </div>
            
            <h3>Key Components of a GLM</h3>
            
            <div style="display: flex; justify-content: center; margin: 2rem 0;">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#667eea">
                        GLM Components
                    </text>
                    
                    <!-- Linear Predictor Box -->
                    <rect x="50" y="60" width="150" height="80" fill="#a8edea" stroke="#00b894" stroke-width="2" rx="10"/>
                    <text x="125" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Linear Predictor
                    </text>
                    <text x="125" y="105" text-anchor="middle" font-size="14" fill="#2d3436">
                        η = Xβ
                    </text>
                    <text x="125" y="125" text-anchor="middle" font-size="10" fill="#636e72">
                        (Linear combination)
                    </text>
                    
                    <!-- Link Function Box -->
                    <rect x="250" y="60" width="150" height="80" fill="#ffeaa7" stroke="#e17055" stroke-width="2" rx="10"/>
                    <text x="325" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Link Function
                    </text>
                    <text x="325" y="105" text-anchor="middle" font-size="14" fill="#2d3436">
                        g(μ) = η
                    </text>
                    <text x="325" y="125" text-anchor="middle" font-size="10" fill="#636e72">
                        (Transformation)
                    </text>
                    
                    <!-- Mean Response Box -->
                    <rect x="450" y="60" width="120" height="80" fill="#fab1a0" stroke="#e84393" stroke-width="2" rx="10"/>
                    <text x="510" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Mean Response
                    </text>
                    <text x="510" y="105" text-anchor="middle" font-size="14" fill="#2d3436">
                        μ = E(Y)
                    </text>
                    <text x="510" y="125" text-anchor="middle" font-size="10" fill="#636e72">
                        (Expected value)
                    </text>
                    
                    <!-- Distribution Box -->
                    <rect x="200" y="200" width="200" height="100" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="10"/>
                    <text x="300" y="225" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Exponential Family Distribution
                    </text>
                    <text x="300" y="245" text-anchor="middle" font-size="11" fill="#2d3436">
                        Y ~ f(y; θ, φ)
                    </text>
                    <text x="300" y="265" text-anchor="middle" font-size="10" fill="#636e72">
                        Normal | Poisson | Binomial | Gamma
                    </text>
                    <text x="300" y="285" text-anchor="middle" font-size="10" fill="#636e72">
                        Inverse Gaussian | ...
                    </text>
                    
                    <!-- Arrows -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                        </marker>
                    </defs>
                    
                    <!-- Linear Predictor to Link Function -->
                    <line x1="200" y1="100" x2="250" y2="100" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Link Function to Mean Response -->
                    <line x1="400" y1="100" x2="450" y2="100" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Mean Response to Distribution -->
                    <line x1="510" y1="140" x2="350" y2="200" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Distribution to Linear Predictor (feedback) -->
                    <line x1="200" y1="250" x2="125" y2="140" stroke="#667eea" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
                    
                    <!-- Labels for arrows -->
                    <text x="225" y="95" text-anchor="middle" font-size="9" fill="#667eea">connects</text>
                    <text x="425" y="95" text-anchor="middle" font-size="9" fill="#667eea">determines</text>
                    <text x="430" y="175" text-anchor="middle" font-size="9" fill="#667eea">parameterizes</text>
                    <text x="160" y="195" text-anchor="middle" font-size="9" fill="#667eea">estimation</text>
                    
                    <!-- Key insight box -->
                    <rect x="50" y="330" width="500" height="50" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                    <text x="300" y="350" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">
                        Key Insight: GLMs separate the linear structure from the distributional assumptions
                    </text>
                    <text x="300" y="365" text-anchor="middle" font-size="10" fill="#155724">
                        This allows modeling of non-normal responses while maintaining linear parameter relationships
                    </text>
                </svg>
            </div>
            
            <div class="highlight-box">
                <h4>The Three Components of a GLM:</h4>
                <ol>
                    <li><strong>Random Component:</strong> Specifies the probability distribution of the response variable from the exponential family</li>
                    <li><strong>Systematic Component:</strong> Specifies the linear predictor η = Xβ</li>
                    <li><strong>Link Function:</strong> Specifies the relationship g(μ) = η between the mean response and linear predictor</li>
                </ol>
            </div>
            
            <h3>Relationship to Linear Models</h3>
            
            <p>GLMs generalize ordinary linear regression. When we choose:</p>
            <ul>
                <li><strong>Identity link function:</strong> g(μ) = μ</li>
                <li><strong>Normal distribution:</strong> Y ~ N(μ, σ²)</li>
            </ul>
            <p>We recover the familiar linear model: <strong>μ = Xβ</strong></p>
            
            <div class="formula-box">
                <h4>Generalized Linear Mixed Models (GLMM)</h4>
                <p>GLMMs extend GLMs to include random effects:</p>
                $$g(\mu_i) = X_i\beta + Z_ib, \quad b \sim N(0, \psi)$$
                <p>where $Z_i$ contains the random effect design and $b$ are the random effects.</p>
                         </div>
         </section>
         
         <!-- Basic Structure Section -->
         <section id="basic-structure" class="section">
             <h2>Basic GLM Structure and Components</h2>
             
             <p>Understanding the mathematical foundation of GLMs is crucial for their proper application. Let's break down each component systematically.</p>
             
             <h3>The Linear Predictor</h3>
             
             <div class="formula-box">
                 <p>The linear predictor is the heart of the GLM:</p>
                 $$\eta = X\beta = \beta_0 + \beta_1 x_1 + \beta_2 x_2 + \ldots + \beta_p x_p$$
                 <p>This maintains the familiar linear structure from ordinary regression.</p>
             </div>
             
             <h3>Link Functions</h3>
             
             <p>The link function connects the linear predictor to the mean of the response distribution. Common link functions include:</p>
             
             <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                 <div class="example-box">
                     <h4>Identity Link</h4>
                     <p>$$g(\mu) = \mu$$</p>
                     <p><strong>Use:</strong> Normal distribution<br>
                     <strong>Result:</strong> Standard linear regression</p>
                 </div>
                 
                 <div class="example-box">
                     <h4>Log Link</h4>
                     <p>$$g(\mu) = \log(\mu)$$</p>
                     <p><strong>Use:</strong> Poisson distribution<br>
                     <strong>Result:</strong> $\mu = e^{X\beta}$ (always positive)</p>
                 </div>
                 
                 <div class="example-box">
                     <h4>Logit Link</h4>
                     <p>$$g(\mu) = \log\left(\frac{\mu}{1-\mu}\right)$$</p>
                     <p><strong>Use:</strong> Binomial distribution<br>
                     <strong>Result:</strong> $\mu = \frac{e^{X\beta}}{1+e^{X\beta}}$ (0 to 1)</p>
                 </div>
                 
                 <div class="example-box">
                     <h4>Reciprocal Link</h4>
                     <p>$$g(\mu) = \frac{1}{\mu}$$</p>
                     <p><strong>Use:</strong> Gamma distribution<br>
                     <strong>Result:</strong> $\mu = \frac{1}{X\beta}$ (positive)</p>
                 </div>
             </div>
             
             <h3>Model Fitting and Inference</h3>
             
             <div class="highlight-box">
                 <h4>Key Differences from Linear Models:</h4>
                 <ul>
                     <li><strong>Iterative fitting:</strong> No closed-form solution like OLS</li>
                     <li><strong>Maximum likelihood estimation:</strong> Parameters estimated via likelihood maximization</li>
                     <li><strong>Approximate inference:</strong> Based on large-sample theory rather than exact distributions</li>
                 </ul>
             </div>
         </section>
         
         <!-- Example 1: Disease Epidemic -->
         <section id="example1" class="section">
             <h2>Practical Example 1: Disease Epidemic Modeling</h2>
             
             <div class="example-box">
                 <h3>Problem Setting</h3>
                 <p>In the early stages of a disease epidemic, the rate at which new cases occur often increases <strong>exponentially</strong> through time. We want to model the expected number of new cases μᵢ on day tᵢ.</p>
             </div>
             
             <h3>Step 1: Initial Model Formulation</h3>
             
             <p>Based on epidemiological theory, we might start with an exponential growth model:</p>
             
             <div class="formula-box">
                 $$\mu_i = \gamma \exp(\delta t_i)$$
                 <p>where:</p>
                 <ul style="text-align: left; display: inline-block;">
                     <li>$\gamma$ = baseline number of cases (unknown parameter)</li>
                     <li>$\delta$ = growth rate (unknown parameter)</li>
                     <li>$t_i$ = day number</li>
                 </ul>
             </div>
             
             <p><strong>Problem:</strong> This model is non-linear in parameters γ and δ, making estimation difficult.</p>
             
             <h3>Step 2: GLM Transformation</h3>
             
             <p>We can transform this into GLM form using a <strong>log link function</strong>:</p>
             
             <div class="formula-box">
                 <p>Taking the logarithm of both sides:</p>
                 $$\log(\mu_i) = \log(\gamma) + \delta t_i$$
                 
                 <p>Reparameterizing with $\beta_0 = \log(\gamma)$ and $\beta_1 = \delta$:</p>
                 $$\log(\mu_i) = \beta_0 + \beta_1 t_i$$
                 
                 <p>This is now <strong>linear in the parameters</strong> β₀ and β₁!</p>
             </div>
             
             <h3>Step 3: Distribution Choice</h3>
             
             <p>Since we're modeling <strong>count data</strong> (number of new cases per day), the Poisson distribution is appropriate:</p>
             
             <div class="formula-box">
                 $$Y_i \sim \text{Poisson}(\mu_i)$$
                 <p>where $E(Y_i) = \mu_i$ and $\text{Var}(Y_i) = \mu_i$</p>
             </div>
             
             <h3>Complete GLM Specification</h3>
             
             <div class="highlight-box">
                 <h4>Disease Epidemic GLM:</h4>
                 <ul>
                     <li><strong>Distribution:</strong> Poisson</li>
                     <li><strong>Link function:</strong> Log link</li>
                     <li><strong>Linear predictor:</strong> $\eta_i = \beta_0 + \beta_1 t_i$</li>
                     <li><strong>Mean response:</strong> $\mu_i = \exp(\beta_0 + \beta_1 t_i)$</li>
                 </ul>
             </div>
             
             <h3>Visualization</h3>
             
             <div style="display: flex; justify-content: center; margin: 2rem 0;">
                 <svg width="700" height="500" viewBox="0 0 700 500">
                     <!-- Background -->
                     <rect width="700" height="500" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                     
                     <!-- Title -->
                     <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                         Disease Epidemic Model: Exponential Growth
                     </text>
                     
                     <!-- Axes -->
                     <line x1="80" y1="400" x2="620" y2="400" stroke="#333" stroke-width="2"/>
                     <line x1="80" y1="400" x2="80" y2="80" stroke="#333" stroke-width="2"/>
                     
                     <!-- Axis labels -->
                     <text x="350" y="440" text-anchor="middle" font-size="12" fill="#333">Time (days)</text>
                     <text x="40" y="240" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 40 240)">Number of Cases</text>
                     
                     <!-- Exponential curve -->
                     <path d="M 80 390 Q 150 380 200 350 Q 300 280 400 200 Q 500 120 580 80" 
                           stroke="#e74c3c" stroke-width="3" fill="none"/>
                     
                     <!-- Data points -->
                     <circle cx="120" cy="385" r="4" fill="#e74c3c"/>
                     <circle cx="180" cy="365" r="4" fill="#e74c3c"/>
                     <circle cx="240" cy="330" r="4" fill="#e74c3c"/>
                     <circle cx="300" cy="280" r="4" fill="#e74c3c"/>
                     <circle cx="360" cy="220" r="4" fill="#e74c3c"/>
                     <circle cx="420" cy="150" r="4" fill="#e74c3c"/>
                     <circle cx="480" cy="110" r="4" fill="#e74c3c"/>
                     
                     <!-- Tick marks and labels -->
                     <line x1="80" y1="400" x2="80" y2="405" stroke="#333" stroke-width="1"/>
                     <text x="80" y="420" text-anchor="middle" font-size="10" fill="#333">0</text>
                     
                     <line x1="200" y1="400" x2="200" y2="405" stroke="#333" stroke-width="1"/>
                     <text x="200" y="420" text-anchor="middle" font-size="10" fill="#333">5</text>
                     
                     <line x1="320" y1="400" x2="320" y2="405" stroke="#333" stroke-width="1"/>
                     <text x="320" y="420" text-anchor="middle" font-size="10" fill="#333">10</text>
                     
                     <line x1="440" y1="400" x2="440" y2="405" stroke="#333" stroke-width="1"/>
                     <text x="440" y="420" text-anchor="middle" font-size="10" fill="#333">15</text>
                     
                     <line x1="560" y1="400" x2="560" y2="405" stroke="#333" stroke-width="1"/>
                     <text x="560" y="420" text-anchor="middle" font-size="10" fill="#333">20</text>
                     
                     <!-- Y-axis labels -->
                     <line x1="75" y1="400" x2="80" y2="400" stroke="#333" stroke-width="1"/>
                     <text x="70" y="405" text-anchor="end" font-size="10" fill="#333">0</text>
                     
                     <line x1="75" y1="320" x2="80" y2="320" stroke="#333" stroke-width="1"/>
                     <text x="70" y="325" text-anchor="end" font-size="10" fill="#333">50</text>
                     
                     <line x1="75" y1="240" x2="80" y2="240" stroke="#333" stroke-width="1"/>
                     <text x="70" y="245" text-anchor="end" font-size="10" fill="#333">100</text>
                     
                     <line x1="75" y1="160" x2="80" y2="160" stroke="#333" stroke-width="1"/>
                     <text x="70" y="165" text-anchor="end" font-size="10" fill="#333">150</text>
                     
                     <line x1="75" y1="80" x2="80" y2="80" stroke="#333" stroke-width="1"/>
                     <text x="70" y="85" text-anchor="end" font-size="10" fill="#333">200</text>
                     
                     <!-- Model equation -->
                     <rect x="450" y="60" width="200" height="80" fill="white" stroke="#667eea" stroke-width="2" rx="10"/>
                     <text x="550" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#667eea">
                         GLM Model
                     </text>
                     <text x="550" y="105" text-anchor="middle" font-size="11" fill="#333">
                         log(μᵢ) = β₀ + β₁tᵢ
                     </text>
                     <text x="550" y="125" text-anchor="middle" font-size="11" fill="#333">
                         Yᵢ ~ Poisson(μᵢ)
                     </text>
                     
                     <!-- Interpretation box -->
                     <rect x="80" y="460" width="540" height="30" fill="#e8f5e8" stroke="#28a745" stroke-width="1" rx="5"/>
                     <text x="350" y="480" text-anchor="middle" font-size="11" fill="#155724">
                         Interpretation: β₁ represents the daily growth rate on the log scale
                     </text>
                 </svg>
             </div>
             
             <h3>Parameter Interpretation</h3>
             
             <div class="formula-box">
                 <h4>Back-transforming Parameters:</h4>
                 <ul style="text-align: left; display: inline-block;">
                     <li>$\gamma = e^{\beta_0}$ (baseline cases)</li>
                     <li>$\delta = \beta_1$ (growth rate per day)</li>
                     <li>$e^{\beta_1}$ = multiplicative factor per day</li>
                 </ul>
                 
                 <p><strong>Example:</strong> If $\beta_1 = 0.1$, then $e^{0.1} \approx 1.105$<br>
                 → Cases increase by about 10.5% per day</p>
             </div>
             
             <div class="highlight-box">
                 <h4>Advantages of the GLM Approach:</h4>
                 <ul>
                     <li><strong>Linear parameters:</strong> Easy to estimate and interpret</li>
                     <li><strong>Appropriate distribution:</strong> Poisson naturally handles count data</li>
                     <li><strong>Automatic constraints:</strong> μᵢ > 0 guaranteed by exp() function</li>
                     <li><strong>Uncertainty quantification:</strong> Standard errors and confidence intervals available</li>
                 </ul>
                           </div>
          </section>
          
          <!-- Example 2: Predator-Prey -->
          <section id="example2" class="section">
              <h2>Practical Example 2: Predator-Prey Dynamics</h2>
              
              <div class="example-box">
                  <h3>Problem Setting</h3>
                  <p>The rate of prey capture by a hunting animal tends to increase with prey density but eventually levels off when the predator reaches its maximum handling capacity. This creates a <strong>saturating relationship</strong>.</p>
              </div>
              
              <h3>Step 1: Functional Response Model</h3>
              
              <p>Ecologists often use a Type II functional response to model this relationship:</p>
              
              <div class="formula-box">
                  $$\mu_i = \frac{\alpha x_i}{h + x_i}$$
                  <p>where:</p>
                  <ul style="text-align: left; display: inline-block;">
                      <li>$\mu_i$ = expected capture rate</li>
                      <li>$x_i$ = prey density</li>
                      <li>$\alpha$ = maximum capture rate (asymptote)</li>
                      <li>$h$ = half-saturation constant (prey density at μ = α/2)</li>
                  </ul>
              </div>
              
              <p><strong>Problem:</strong> This model is non-linear in parameters α and h.</p>
              
              <h3>Step 2: GLM Transformation</h3>
              
              <p>We can linearize this using a <strong>reciprocal link function</strong>:</p>
              
              <div class="formula-box">
                  <p>Taking the reciprocal of both sides:</p>
                  $$\frac{1}{\mu_i} = \frac{h + x_i}{\alpha x_i} = \frac{h}{\alpha x_i} + \frac{1}{\alpha}$$
                  
                  <p>Rearranging:</p>
                  $$\frac{1}{\mu_i} = \frac{1}{\alpha} + \frac{h}{\alpha} \cdot \frac{1}{x_i}$$
                  
                  <p>Defining $\beta_0 = \frac{1}{\alpha}$ and $\beta_1 = \frac{h}{\alpha}$:</p>
                  $$\frac{1}{\mu_i} = \beta_0 + \beta_1 \cdot \frac{1}{x_i}$$
                  
                  <p>This is now <strong>linear in the parameters</strong> β₀ and β₁!</p>
              </div>
              
              <h3>Step 3: Distribution Choice</h3>
              
              <p>Since capture rates are <strong>continuous and positive</strong>, and the variance often increases with the mean, the Gamma distribution is appropriate:</p>
              
              <div class="formula-box">
                  $$Y_i \sim \text{Gamma}(\mu_i, \nu)$$
                  <p>where $E(Y_i) = \mu_i$ and $\text{Var}(Y_i) = \mu_i^2/\nu$</p>
                  <p>(Variance proportional to mean squared)</p>
              </div>
              
              <h3>Complete GLM Specification</h3>
              
              <div class="highlight-box">
                  <h4>Predator-Prey GLM:</h4>
                  <ul>
                      <li><strong>Distribution:</strong> Gamma</li>
                      <li><strong>Link function:</strong> Reciprocal link</li>
                      <li><strong>Linear predictor:</strong> $\eta_i = \beta_0 + \beta_1 \cdot \frac{1}{x_i}$</li>
                      <li><strong>Mean response:</strong> $\mu_i = \frac{1}{\beta_0 + \beta_1/x_i}$</li>
                  </ul>
              </div>
              
              <h3>Visualization</h3>
              
              <div style="display: flex; justify-content: center; margin: 2rem 0;">
                  <svg width="700" height="500" viewBox="0 0 700 500">
                      <!-- Background -->
                      <rect width="700" height="500" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                      
                      <!-- Title -->
                      <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                          Predator-Prey Model: Type II Functional Response
                      </text>
                      
                      <!-- Axes -->
                      <line x1="80" y1="400" x2="620" y2="400" stroke="#333" stroke-width="2"/>
                      <line x1="80" y1="400" x2="80" y2="80" stroke="#333" stroke-width="2"/>
                      
                      <!-- Axis labels -->
                      <text x="350" y="440" text-anchor="middle" font-size="12" fill="#333">Prey Density</text>
                      <text x="40" y="240" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 40 240)">Capture Rate</text>
                      
                      <!-- Saturating curve (Type II functional response) -->
                      <path d="M 80 400 Q 120 350 160 310 Q 220 260 300 220 Q 400 190 500 170 Q 580 160 620 155" 
                            stroke="#27ae60" stroke-width="3" fill="none"/>
                      
                      <!-- Data points -->
                      <circle cx="100" cy="380" r="4" fill="#27ae60"/>
                      <circle cx="140" cy="340" r="4" fill="#27ae60"/>
                      <circle cx="180" cy="300" r="4" fill="#27ae60"/>
                      <circle cx="220" cy="270" r="4" fill="#27ae60"/>
                      <circle cx="280" cy="230" r="4" fill="#27ae60"/>
                      <circle cx="360" cy="200" r="4" fill="#27ae60"/>
                      <circle cx="440" cy="180" r="4" fill="#27ae60"/>
                      <circle cx="520" cy="170" r="4" fill="#27ae60"/>
                      
                      <!-- Asymptote line -->
                      <line x1="80" y1="150" x2="620" y2="150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                      <text x="550" y="145" text-anchor="middle" font-size="10" fill="#e74c3c">α (max rate)</text>
                      
                      <!-- Half-saturation point -->
                      <line x1="200" y1="400" x2="200" y2="225" stroke="#f39c12" stroke-width="2" stroke-dasharray="3,3"/>
                      <line x1="80" y1="225" x2="200" y2="225" stroke="#f39c12" stroke-width="2" stroke-dasharray="3,3"/>
                      <text x="200" y="420" text-anchor="middle" font-size="10" fill="#f39c12">h</text>
                      <text x="70" y="220" text-anchor="end" font-size="10" fill="#f39c12">α/2</text>
                      
                      <!-- Tick marks and labels -->
                      <line x1="80" y1="400" x2="80" y2="405" stroke="#333" stroke-width="1"/>
                      <text x="80" y="420" text-anchor="middle" font-size="10" fill="#333">0</text>
                      
                      <line x1="200" y1="400" x2="200" y2="405" stroke="#333" stroke-width="1"/>
                      <text x="200" y="420" text-anchor="middle" font-size="10" fill="#333">5</text>
                      
                      <line x1="320" y1="400" x2="320" y2="405" stroke="#333" stroke-width="1"/>
                      <text x="320" y="420" text-anchor="middle" font-size="10" fill="#333">10</text>
                      
                      <line x1="440" y1="400" x2="440" y2="405" stroke="#333" stroke-width="1"/>
                      <text x="440" y="420" text-anchor="middle" font-size="10" fill="#333">15</text>
                      
                      <line x1="560" y1="400" x2="560" y2="405" stroke="#333" stroke-width="1"/>
                      <text x="560" y="420" text-anchor="middle" font-size="10" fill="#333">20</text>
                      
                      <!-- Y-axis labels -->
                      <line x1="75" y1="400" x2="80" y2="400" stroke="#333" stroke-width="1"/>
                      <text x="70" y="405" text-anchor="end" font-size="10" fill="#333">0</text>
                      
                      <line x1="75" y1="320" x2="80" y2="320" stroke="#333" stroke-width="1"/>
                      <text x="70" y="325" text-anchor="end" font-size="10" fill="#333">2</text>
                      
                      <line x1="75" y1="240" x2="80" y2="240" stroke="#333" stroke-width="1"/>
                      <text x="70" y="245" text-anchor="end" font-size="10" fill="#333">4</text>
                      
                      <line x1="75" y1="160" x2="80" y2="160" stroke="#333" stroke-width="1"/>
                      <text x="70" y="165" text-anchor="end" font-size="10" fill="#333">6</text>
                      
                      <line x1="75" y1="80" x2="80" y2="80" stroke="#333" stroke-width="1"/>
                      <text x="70" y="85" text-anchor="end" font-size="10" fill="#333">8</text>
                      
                      <!-- Model equation -->
                      <rect x="450" y="60" width="200" height="100" fill="white" stroke="#667eea" stroke-width="2" rx="10"/>
                      <text x="550" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#667eea">
                          GLM Model
                      </text>
                      <text x="550" y="105" text-anchor="middle" font-size="11" fill="#333">
                          1/μᵢ = β₀ + β₁/xᵢ
                      </text>
                      <text x="550" y="125" text-anchor="middle" font-size="11" fill="#333">
                          Yᵢ ~ Gamma(μᵢ, ν)
                      </text>
                      <text x="550" y="145" text-anchor="middle" font-size="10" fill="#636e72">
                          Reciprocal link
                      </text>
                      
                      <!-- Interpretation box -->
                      <rect x="80" y="460" width="540" height="30" fill="#e8f5e8" stroke="#28a745" stroke-width="1" rx="5"/>
                      <text x="350" y="480" text-anchor="middle" font-size="11" fill="#155724">
                          Interpretation: Captures saturation behavior with linear parameters
                      </text>
                  </svg>
              </div>
              
              <h3>Parameter Interpretation</h3>
              
              <div class="formula-box">
                  <h4>Back-transforming Parameters:</h4>
                  <ul style="text-align: left; display: inline-block;">
                      <li>$\alpha = \frac{1}{\beta_0}$ (maximum capture rate)</li>
                      <li>$h = \frac{\beta_1}{\beta_0}$ (half-saturation constant)</li>
                      <li>At high prey density: $\mu_i \approx \alpha$</li>
                      <li>At low prey density: $\mu_i \approx \frac{\alpha x_i}{h}$ (linear)</li>
                  </ul>
              </div>
              
              <h3>Biological Interpretation</h3>
              
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                  <div class="example-box">
                      <h4>Low Prey Density</h4>
                      <p>When $x_i \ll h$:</p>
                      <p>$$\mu_i \approx \frac{\alpha x_i}{h}$$</p>
                      <p><strong>Behavior:</strong> Linear increase with prey density (search-limited)</p>
                  </div>
                  
                  <div class="example-box">
                      <h4>High Prey Density</h4>
                      <p>When $x_i \gg h$:</p>
                      <p>$$\mu_i \approx \alpha$$</p>
                      <p><strong>Behavior:</strong> Constant rate (handling-limited)</p>
                  </div>
              </div>
              
              <div class="highlight-box">
                  <h4>Advantages of the GLM Approach:</h4>
                  <ul>
                      <li><strong>Linearization:</strong> Complex ecological relationship becomes linear in parameters</li>
                      <li><strong>Appropriate distribution:</strong> Gamma distribution handles positive continuous data with increasing variance</li>
                      <li><strong>Biological relevance:</strong> Parameters have clear ecological interpretation</li>
                      <li><strong>Constraint satisfaction:</strong> Reciprocal link ensures μᵢ > 0</li>
                  </ul>
              </div>
              
              <h3>Comparison of Examples</h3>
              
              <div style="display: flex; justify-content: center; margin: 2rem 0;">
                  <svg width="700" height="300" viewBox="0 0 700 300">
                      <!-- Background -->
                      <rect width="700" height="300" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                      
                      <!-- Title -->
                      <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                          Comparison of GLM Examples
                      </text>
                      
                      <!-- Example 1 Box -->
                      <rect x="50" y="50" width="280" height="200" fill="#ffeaa7" stroke="#e17055" stroke-width="2" rx="10"/>
                      <text x="190" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                          Example 1: Disease Epidemic
                      </text>
                      
                      <text x="70" y="100" font-size="11" fill="#2d3436">
                          <tspan x="70" dy="0">• Response: Count data</tspan>
                          <tspan x="70" dy="15">• Distribution: Poisson</tspan>
                          <tspan x="70" dy="15">• Link: Log link</tspan>
                          <tspan x="70" dy="15">• Relationship: Exponential growth</tspan>
                          <tspan x="70" dy="15">• Predictor: Time (continuous)</tspan>
                          <tspan x="70" dy="15">• Interpretation: Growth rate</tspan>
                          <tspan x="70" dy="20">• Model: log(μ) = β₀ + β₁t</tspan>
                          <tspan x="70" dy="15">• Constraint: μ > 0 (counts)</tspan>
                      </text>
                      
                      <!-- Example 2 Box -->
                      <rect x="370" y="50" width="280" height="200" fill="#a8edea" stroke="#00b894" stroke-width="2" rx="10"/>
                      <text x="510" y="75" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                          Example 2: Predator-Prey
                      </text>
                      
                      <text x="390" y="100" font-size="11" fill="#2d3436">
                          <tspan x="390" dy="0">• Response: Continuous positive</tspan>
                          <tspan x="390" dy="15">• Distribution: Gamma</tspan>
                          <tspan x="390" dy="15">• Link: Reciprocal link</tspan>
                          <tspan x="390" dy="15">• Relationship: Saturation</tspan>
                          <tspan x="390" dy="15">• Predictor: Density (continuous)</tspan>
                          <tspan x="390" dy="15">• Interpretation: Ecological parameters</tspan>
                          <tspan x="390" dy="20">• Model: 1/μ = β₀ + β₁/x</tspan>
                          <tspan x="390" dy="15">• Constraint: μ > 0 (rates)</tspan>
                      </text>
                      
                      <!-- Common theme -->
                      <rect x="100" y="270" width="500" height="25" fill="#e8f5e8" stroke="#28a745" stroke-width="1" rx="5"/>
                      <text x="350" y="287" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">
                          Common Theme: Non-linear relationships made linear through appropriate link functions
                      </text>
                  </svg>
                             </div>
           </section>
           
           <!-- Exponential Family Section -->
           <section id="exponential-family" class="section">
               <h2>The Exponential Family of Distributions</h2>
               
               <p>The theoretical foundation of GLMs rests on the <strong>exponential family</strong> of distributions. This family includes many commonly used distributions and provides a unified framework for GLM theory.</p>
               
               <h3>Definition</h3>
               
               <div class="formula-box">
                   <p>A distribution belongs to the exponential family if its probability density (or mass) function can be written as:</p>
                   $$f_\theta(y) = \exp\left[\frac{y\theta - b(\theta)}{a(\phi)} + c(y, \phi)\right]$$
                   <p>where:</p>
                   <ul style="text-align: left; display: inline-block;">
                       <li>$\theta$ = canonical parameter (natural parameter)</li>
                       <li>$\phi$ = scale parameter (dispersion parameter)</li>
                       <li>$a(\phi)$, $b(\theta)$, $c(y, \phi)$ = known functions</li>
                   </ul>
               </div>
               
               <h3>Example: Normal Distribution</h3>
               
               <p>Let's verify that the normal distribution belongs to the exponential family:</p>
               
               <div class="formula-box">
                   <p>Starting with the standard normal density:</p>
                   $$f_\mu(y) = \frac{1}{\sigma\sqrt{2\pi}} \exp\left(-\frac{(y-\mu)^2}{2\sigma^2}\right)$$
                   
                   <p>Expanding the exponent:</p>
                   $$f_\mu(y) = \frac{1}{\sigma\sqrt{2\pi}} \exp\left(-\frac{y^2 - 2y\mu + \mu^2}{2\sigma^2}\right)$$
                   
                   <p>Rearranging:</p>
                   $$f_\mu(y) = \exp\left[\frac{y\mu - \mu^2/2}{\sigma^2} - \frac{y^2}{2\sigma^2} - \log(\sigma\sqrt{2\pi})\right]$$
                   
                   <p>This matches the exponential family form with:</p>
                   <ul style="text-align: left; display: inline-block;">
                       <li>$\theta = \mu$ (canonical parameter)</li>
                       <li>$b(\theta) = \theta^2/2 = \mu^2/2$</li>
                       <li>$a(\phi) = \phi = \sigma^2$ (scale parameter)</li>
                       <li>$c(y, \phi) = -\frac{y^2}{2\phi} - \log(\sqrt{2\pi\phi})$</li>
                   </ul>
               </div>
               
               <h3>Key Properties</h3>
               
               <p>The exponential family form allows us to derive general expressions for mean and variance:</p>
               
               <div class="highlight-box">
                   <h4>Mean-Variance Relationships:</h4>
                   
                   <p>For any exponential family distribution:</p>
                   <div class="formula-box">
                       $$E(Y) = b'(\theta) = \frac{db}{d\theta}$$
                       $$\text{Var}(Y) = b''(\theta) \cdot a(\phi) = \frac{d^2b}{d\theta^2} \cdot a(\phi)$$
                   </div>
                   
                   <p>These relationships are fundamental to GLM theory and parameter estimation.</p>
               </div>
               
               <h3>Derivation of Mean Formula</h3>
               
               <div class="formula-box">
                   <p>The log-likelihood for a single observation is:</p>
                   $$\ell(\theta) = \frac{y\theta - b(\theta)}{a(\phi)} + c(y, \phi)$$
                   
                   <p>Taking the derivative with respect to θ:</p>
                   $$\frac{\partial \ell}{\partial \theta} = \frac{y - b'(\theta)}{a(\phi)}$$
                   
                   <p>Using the fundamental result that $E\left[\frac{\partial \ell}{\partial \theta}\right] = 0$:</p>
                   $$E\left[\frac{y - b'(\theta)}{a(\phi)}\right] = 0$$
                   
                   <p>Therefore:</p>
                   $$E(Y) = b'(\theta)$$
               </div>
               
               <h3>Common Exponential Family Distributions</h3>
               
               <div style="display: flex; justify-content: center; margin: 2rem 0;">
                   <svg width="800" height="600" viewBox="0 0 800 600">
                       <!-- Background -->
                       <rect width="800" height="600" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                       
                       <!-- Title -->
                       <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#667eea">
                           Exponential Family Distributions in GLMs
                       </text>
                       
                       <!-- Normal Distribution -->
                       <rect x="50" y="60" width="180" height="120" fill="#e8f4f8" stroke="#17a2b8" stroke-width="2" rx="10"/>
                       <text x="140" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                           Normal
                       </text>
                       <text x="60" y="105" font-size="10" fill="#2d3436">
                           <tspan x="60" dy="0">θ = μ</tspan>
                           <tspan x="60" dy="12">b(θ) = θ²/2</tspan>
                           <tspan x="60" dy="12">a(φ) = σ²</tspan>
                           <tspan x="60" dy="12">V(μ) = 1</tspan>
                           <tspan x="60" dy="12">Link: Identity</tspan>
                       </text>
                       
                       <!-- Poisson Distribution -->
                       <rect x="280" y="60" width="180" height="120" fill="#f8e8e8" stroke="#dc3545" stroke-width="2" rx="10"/>
                       <text x="370" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                           Poisson
                       </text>
                       <text x="290" y="105" font-size="10" fill="#2d3436">
                           <tspan x="290" dy="0">θ = log(μ)</tspan>
                           <tspan x="290" dy="12">b(θ) = exp(θ)</tspan>
                           <tspan x="290" dy="12">a(φ) = 1</tspan>
                           <tspan x="290" dy="12">V(μ) = μ</tspan>
                           <tspan x="290" dy="12">Link: Log</tspan>
                       </text>
                       
                       <!-- Binomial Distribution -->
                       <rect x="510" y="60" width="180" height="120" fill="#e8f8e8" stroke="#28a745" stroke-width="2" rx="10"/>
                       <text x="600" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                           Binomial
                       </text>
                       <text x="520" y="105" font-size="10" fill="#2d3436">
                           <tspan x="520" dy="0">θ = log(μ/(n-μ))</tspan>
                           <tspan x="520" dy="12">b(θ) = n log(1+eᶿ)</tspan>
                           <tspan x="520" dy="12">a(φ) = 1</tspan>
                           <tspan x="520" dy="12">V(μ) = μ(1-μ/n)</tspan>
                           <tspan x="520" dy="12">Link: Logit</tspan>
                       </text>
                       
                       <!-- Gamma Distribution -->
                       <rect x="165" y="220" width="180" height="120" fill="#fff8e8" stroke="#ffc107" stroke-width="2" rx="10"/>
                       <text x="255" y="245" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                           Gamma
                       </text>
                       <text x="175" y="265" font-size="10" fill="#2d3436">
                           <tspan x="175" dy="0">θ = -1/μ</tspan>
                           <tspan x="175" dy="12">b(θ) = -log(-θ)</tspan>
                           <tspan x="175" dy="12">a(φ) = 1/ν</tspan>
                           <tspan x="175" dy="12">V(μ) = μ²</tspan>
                           <tspan x="175" dy="12">Link: Reciprocal</tspan>
                       </text>
                       
                       <!-- Inverse Gaussian Distribution -->
                       <rect x="395" y="220" width="180" height="120" fill="#f8e8f8" stroke="#6f42c1" stroke-width="2" rx="10"/>
                       <text x="485" y="245" text-anchor="middle" font-size="14" font-weight="bold" fill="#2d3436">
                           Inverse Gaussian
                       </text>
                       <text x="405" y="265" font-size="10" fill="#2d3436">
                           <tspan x="405" dy="0">θ = -1/(2μ²)</tspan>
                           <tspan x="405" dy="12">b(θ) = -√(-2θ)</tspan>
                           <tspan x="405" dy="12">a(φ) = 1/γ</tspan>
                           <tspan x="405" dy="12">V(μ) = μ³</tspan>
                           <tspan x="405" dy="12">Link: μ⁻²</tspan>
                       </text>
                       
                       <!-- Variance Function Diagram -->
                       <rect x="100" y="380" width="600" height="180" fill="white" stroke="#667eea" stroke-width="2" rx="10"/>
                       <text x="400" y="405" text-anchor="middle" font-size="14" font-weight="bold" fill="#667eea">
                           Variance Functions V(μ)
                       </text>
                       
                       <!-- Coordinate system for variance functions -->
                       <line x1="150" y1="520" x2="650" y2="520" stroke="#333" stroke-width="1"/>
                       <line x1="150" y1="520" x2="150" y2="420" stroke="#333" stroke-width="1"/>
                       
                       <!-- Variance function curves -->
                       <!-- Constant (Normal) -->
                       <line x1="150" y1="480" x2="650" y2="480" stroke="#17a2b8" stroke-width="2"/>
                       <text x="160" y="475" font-size="10" fill="#17a2b8">V(μ) = 1 (Normal)</text>
                       
                       <!-- Linear (Poisson) -->
                       <line x1="150" y1="520" x2="650" y2="420" stroke="#dc3545" stroke-width="2"/>
                       <text x="160" y="500" font-size="10" fill="#dc3545">V(μ) = μ (Poisson)</text>
                       
                       <!-- Quadratic (Gamma) -->
                       <path d="M 150 520 Q 300 460 450 420 Q 550 400 650 380" stroke="#ffc107" stroke-width="2" fill="none"/>
                       <text x="160" y="455" font-size="10" fill="#ffc107">V(μ) = μ² (Gamma)</text>
                       
                       <!-- Cubic (Inverse Gaussian) -->
                       <path d="M 150 520 Q 250 480 350 440 Q 450 400 550 360 Q 600 340 650 320" stroke="#6f42c1" stroke-width="2" fill="none"/>
                       <text x="160" y="435" font-size="10" fill="#6f42c1">V(μ) = μ³ (Inverse Gaussian)</text>
                       
                       <!-- Axis labels -->
                       <text x="400" y="545" text-anchor="middle" font-size="10" fill="#333">Mean (μ)</text>
                       <text x="130" y="470" text-anchor="middle" font-size="10" fill="#333" transform="rotate(-90 130 470)">Variance</text>
                   </svg>
               </div>
               
               <h3>Variance Functions</h3>
               
               <p>The variance function $V(\mu)$ characterizes how the variance depends on the mean for each distribution:</p>
               
               <div class="formula-box">
                   $$\text{Var}(Y) = V(\mu) \cdot \phi$$
                   <p>where $\phi$ is the dispersion parameter and $V(\mu) = \frac{b''(\theta)}{\omega}$</p>
               </div>
               
               <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                   <div class="example-box">
                       <h4>Constant Variance: Normal</h4>
                       <p>$V(\mu) = 1$</p>
                       <p>Variance doesn't depend on the mean</p>
                       <p><strong>Use:</strong> Continuous responses with constant variance</p>
                   </div>
                   
                   <div class="example-box">
                       <h4>Linear Variance: Poisson</h4>
                       <p>$V(\mu) = \mu$</p>
                       <p>Variance equals the mean</p>
                       <p><strong>Use:</strong> Count data with equidispersion</p>
                   </div>
                   
                   <div class="example-box">
                       <h4>Quadratic Variance: Gamma</h4>
                       <p>$V(\mu) = \mu^2$</p>
                       <p>Variance proportional to mean squared</p>
                       <p><strong>Use:</strong> Positive continuous data with increasing variance</p>
                   </div>
                   
                   <div class="example-box">
                       <h4>Binomial Variance</h4>
                       <p>$V(\mu) = \mu(1-\mu/n)$</p>
                       <p>Quadratic in mean with upper bound</p>
                       <p><strong>Use:</strong> Proportions and binary data</p>
                   </div>
               </div>
               
               <div class="highlight-box">
                   <h4>Why Exponential Family Matters:</h4>
                   <ul>
                       <li><strong>Unified theory:</strong> Common framework for diverse distributions</li>
                       <li><strong>Maximum likelihood:</strong> Efficient algorithms for parameter estimation</li>
                       <li><strong>Asymptotic properties:</strong> Large-sample inference theory</li>
                       <li><strong>Canonical links:</strong> Natural link functions that simplify computation</li>
                   </ul>
                               </div>
            </section>
            
            <!-- Mathematical Theory Section -->
            <section id="theory" class="section">
                <h2>Mathematical Theory and Derivations</h2>
                
                <p>This section covers the mathematical foundations underlying GLM estimation and inference.</p>
                
                <h3>Maximum Likelihood Estimation</h3>
                
                <p>GLM parameters are estimated using maximum likelihood, but unlike linear regression, there's no closed-form solution. Instead, we use iterative methods.</p>
                
                <div class="formula-box">
                    <h4>The Log-Likelihood Function</h4>
                    <p>For independent observations from an exponential family:</p>
                    $$\ell(\beta) = \sum_{i=1}^n \left[\frac{y_i\theta_i - b(\theta_i)}{a(\phi)} + c(y_i, \phi)\right]$$
                    
                    <p>where $\theta_i$ depends on $\beta$ through the link function:</p>
                    $$\mu_i = b'(\theta_i) = g^{-1}(X_i\beta)$$
                </div>
                
                <h3>Iteratively Reweighted Least Squares (IRLS)</h3>
                
                <p>The standard algorithm for fitting GLMs uses iteratively reweighted least squares:</p>
                
                <div class="formula-box">
                    <h4>IRLS Algorithm</h4>
                    <p>At each iteration:</p>
                    
                    <p>1. Calculate working response:</p>
                    $$z_i = \eta_i + (y_i - \mu_i) \frac{d\eta_i}{d\mu_i}$$
                    
                    <p>2. Calculate weights:</p>
                    $$w_i = \left[\frac{d\eta_i}{d\mu_i}\right]^{-2} \frac{1}{V(\mu_i)}$$
                    
                    <p>3. Update parameters:</p>
                    $$\beta^{(t+1)} = (X^T W X)^{-1} X^T W z$$
                    
                    <p>where $W = \text{diag}(w_1, \ldots, w_n)$ and $z = (z_1, \ldots, z_n)^T$</p>
                </div>
                
                <h3>Asymptotic Properties</h3>
                
                <div class="highlight-box">
                    <h4>Large Sample Results</h4>
                    <p>Under regularity conditions, the maximum likelihood estimator $\hat{\beta}$ has the following asymptotic properties:</p>
                    
                    <div class="formula-box">
                        <p><strong>Consistency:</strong> $\hat{\beta} \xrightarrow{p} \beta$ as $n \to \infty$</p>
                        
                        <p><strong>Asymptotic Normality:</strong></p>
                        $$\sqrt{n}(\hat{\beta} - \beta) \xrightarrow{d} N(0, I^{-1}(\beta))$$
                        
                        <p>where $I(\beta)$ is the Fisher Information Matrix:</p>
                        $$I(\beta) = X^T W X$$
                    </div>
                </div>
                
                <h3>Deviance and Model Comparison</h3>
                
                <div class="formula-box">
                    <h4>Deviance</h4>
                    <p>The deviance measures goodness of fit:</p>
                    $$D = 2\sum_{i=1}^n \left[\ell(\tilde{\theta}_i; y_i) - \ell(\hat{\theta}_i; y_i)\right]$$
                    
                    <p>where:</p>
                    <ul style="text-align: left; display: inline-block;">
                        <li>$\tilde{\theta}_i$ = saturated model parameter (perfect fit)</li>
                        <li>$\hat{\theta}_i$ = fitted model parameter</li>
                    </ul>
                    
                    <p>For large samples: $D \sim \chi^2_{n-p}$ approximately</p>
                </div>
                
                <h3>Residuals and Diagnostics</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                    <div class="example-box">
                        <h4>Pearson Residuals</h4>
                        $$r_{P,i} = \frac{y_i - \hat{\mu}_i}{\sqrt{V(\hat{\mu}_i)\phi}}$$
                        <p>Standardized residuals based on variance function</p>
                    </div>
                    
                    <div class="example-box">
                        <h4>Deviance Residuals</h4>
                        $$r_{D,i} = \text{sign}(y_i - \hat{\mu}_i)\sqrt{d_i}$$
                        <p>where $d_i$ is the contribution to deviance</p>
                    </div>
                </div>
            </section>
            
            <!-- Distribution Comparison Section -->
            <section id="distributions" class="section">
                <h2>Distribution Comparison and Properties</h2>
                
                <p>This comprehensive table summarizes the key properties of exponential family distributions used in GLMs:</p>
                
                <div style="overflow-x: auto; margin: 2rem 0;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                        <thead>
                            <tr style="background: #667eea; color: white;">
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Distribution</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Range</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">θ (Canonical)</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">b(θ)</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">V(μ)</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Canonical Link</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Common Use</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr style="background: #f8f9fa;">
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Normal</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">(-∞, ∞)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">μ</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">θ²/2</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">1</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Identity: g(μ) = μ</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Continuous responses</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Poisson</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">{0, 1, 2, ...}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">log(μ)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">exp(θ)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">μ</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Log: g(μ) = log(μ)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Count data</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Binomial</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">{0, 1, ..., n}</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">log(μ/(n-μ))</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">n log(1+e^θ)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">μ(1-μ/n)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Logit: g(μ) = log(μ/(n-μ))</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Binary/proportion data</td>
                            </tr>
                            <tr>
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Gamma</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">(0, ∞)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">-1/μ</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">-log(-θ)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">μ²</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Reciprocal: g(μ) = 1/μ</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Positive continuous</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Inverse Gaussian</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">(0, ∞)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">-1/(2μ²)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">-√(-2θ)</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">μ³</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Inverse square: g(μ) = 1/μ²</td>
                                <td style="border: 1px solid #ddd; padding: 8px;">Positive with high variance</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h3>Choosing the Right Distribution</h3>
                
                <div class="highlight-box">
                    <h4>Decision Framework:</h4>
                    <ol>
                        <li><strong>Response Type:</strong> Continuous, count, binary, or proportion?</li>
                        <li><strong>Range:</strong> What values can the response take?</li>
                        <li><strong>Variance Pattern:</strong> How does variance relate to the mean?</li>
                        <li><strong>Link Function:</strong> What transformation makes sense?</li>
                    </ol>
                </div>
            </section>
            
            <!-- Visualization Section -->
            <section id="visualization" class="section">
                <h2>Conceptual Relationships and Visualizations</h2>
                
                <p>This final section provides a comprehensive visual summary of GLM concepts and their relationships.</p>
                
                <div style="display: flex; justify-content: center; margin: 2rem 0;">
                    <svg width="900" height="700" viewBox="0 0 900 700">
                        <!-- Background -->
                        <rect width="900" height="700" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                        
                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#667eea">
                            GLM Conceptual Framework
                        </text>
                        
                        <!-- Data Layer -->
                        <rect x="50" y="60" width="800" height="100" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                        <text x="450" y="85" text-anchor="middle" font-size="16" font-weight="bold" fill="#155724">
                            Data Layer
                        </text>
                        <text x="100" y="110" font-size="12" fill="#155724">
                            Response Variable Y: Count, Binary, Continuous, Proportion
                        </text>
                        <text x="100" y="130" font-size="12" fill="#155724">
                            Predictor Variables X: Continuous, Categorical, Interactions
                        </text>
                        <text x="100" y="150" font-size="12" fill="#155724">
                            Model Structure: Linear predictor η = Xβ
                        </text>
                        
                        <!-- Distribution Layer -->
                        <rect x="50" y="180" width="390" height="140" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
                        <text x="245" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#856404">
                            Distribution Choice
                        </text>
                        
                        <!-- Distribution boxes -->
                        <rect x="70" y="220" width="80" height="40" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1" rx="5"/>
                        <text x="110" y="240" text-anchor="middle" font-size="10" fill="#0c5460">Normal</text>
                        <text x="110" y="250" text-anchor="middle" font-size="9" fill="#0c5460">V(μ)=1</text>
                        
                        <rect x="160" y="220" width="80" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="1" rx="5"/>
                        <text x="200" y="240" text-anchor="middle" font-size="10" fill="#721c24">Poisson</text>
                        <text x="200" y="250" text-anchor="middle" font-size="9" fill="#721c24">V(μ)=μ</text>
                        
                        <rect x="250" y="220" width="80" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1" rx="5"/>
                        <text x="290" y="240" text-anchor="middle" font-size="10" fill="#155724">Binomial</text>
                        <text x="290" y="250" text-anchor="middle" font-size="9" fill="#155724">V(μ)=μ(1-μ/n)</text>
                        
                        <rect x="340" y="220" width="80" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" rx="5"/>
                        <text x="380" y="240" text-anchor="middle" font-size="10" fill="#856404">Gamma</text>
                        <text x="380" y="250" text-anchor="middle" font-size="9" fill="#856404">V(μ)=μ²</text>
                        
                        <text x="245" y="280" text-anchor="middle" font-size="11" fill="#856404">
                            Exponential Family: f(y) = exp[(yθ - b(θ))/a(φ) + c(y,φ)]
                        </text>
                        <text x="245" y="295" text-anchor="middle" font-size="10" fill="#856404">
                            Key Properties: E(Y) = b'(θ), Var(Y) = b''(θ)a(φ)
                        </text>
                        
                        <!-- Link Function Layer -->
                        <rect x="460" y="180" width="390" height="140" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="10"/>
                        <text x="655" y="205" text-anchor="middle" font-size="16" font-weight="bold" fill="#721c24">
                            Link Function Choice
                        </text>
                        
                        <!-- Link function boxes -->
                        <rect x="480" y="220" width="80" height="40" fill="#e2e3e5" stroke="#6c757d" stroke-width="1" rx="5"/>
                        <text x="520" y="240" text-anchor="middle" font-size="10" fill="#495057">Identity</text>
                        <text x="520" y="250" text-anchor="middle" font-size="9" fill="#495057">g(μ)=μ</text>
                        
                        <rect x="570" y="220" width="80" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="1" rx="5"/>
                        <text x="610" y="240" text-anchor="middle" font-size="10" fill="#721c24">Log</text>
                        <text x="610" y="250" text-anchor="middle" font-size="9" fill="#721c24">g(μ)=log(μ)</text>
                        
                        <rect x="660" y="220" width="80" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1" rx="5"/>
                        <text x="700" y="240" text-anchor="middle" font-size="10" fill="#155724">Logit</text>
                        <text x="700" y="250" text-anchor="middle" font-size="9" fill="#155724">g(μ)=log(μ/(1-μ))</text>
                        
                        <rect x="750" y="220" width="80" height="40" fill="#fff3cd" stroke="#ffc107" stroke-width="1" rx="5"/>
                        <text x="790" y="240" text-anchor="middle" font-size="10" fill="#856404">Reciprocal</text>
                        <text x="790" y="250" text-anchor="middle" font-size="9" fill="#856404">g(μ)=1/μ</text>
                        
                        <text x="655" y="280" text-anchor="middle" font-size="11" fill="#721c24">
                            Connects linear predictor η = Xβ to mean μ
                        </text>
                        <text x="655" y="295" text-anchor="middle" font-size="10" fill="#721c24">
                            Canonical links: θ = η (computational advantages)
                        </text>
                        
                        <!-- Estimation Layer -->
                        <rect x="50" y="340" width="800" height="100" fill="#e7f3ff" stroke="#007bff" stroke-width="2" rx="10"/>
                        <text x="450" y="365" text-anchor="middle" font-size="16" font-weight="bold" fill="#004085">
                            Estimation and Inference
                        </text>
                        <text x="100" y="390" font-size="12" fill="#004085">
                            Method: Maximum Likelihood Estimation via IRLS (Iteratively Reweighted Least Squares)
                        </text>
                        <text x="100" y="410" font-size="12" fill="#004085">
                            Inference: Asymptotic normality, Wald tests, Likelihood ratio tests
                        </text>
                        <text x="100" y="430" font-size="12" fill="#004085">
                            Diagnostics: Deviance, Pearson residuals, AIC, Model comparison
                        </text>
                        
                        <!-- Applications Layer -->
                        <rect x="50" y="460" width="800" height="120" fill="#f0e6ff" stroke="#6f42c1" stroke-width="2" rx="10"/>
                        <text x="450" y="485" text-anchor="middle" font-size="16" font-weight="bold" fill="#3d1a78">
                            Applications and Examples
                        </text>
                        
                        <!-- Application examples -->
                        <rect x="80" y="500" width="180" height="60" fill="#fff3cd" stroke="#ffc107" stroke-width="1" rx="5"/>
                        <text x="170" y="520" text-anchor="middle" font-size="11" font-weight="bold" fill="#856404">
                            Disease Epidemics
                        </text>
                        <text x="170" y="535" text-anchor="middle" font-size="9" fill="#856404">
                            Poisson + Log link
                        </text>
                        <text x="170" y="550" text-anchor="middle" font-size="9" fill="#856404">
                            Exponential growth modeling
                        </text>
                        
                        <rect x="280" y="500" width="180" height="60" fill="#d4edda" stroke="#28a745" stroke-width="1" rx="5"/>
                        <text x="370" y="520" text-anchor="middle" font-size="11" font-weight="bold" fill="#155724">
                            Predator-Prey
                        </text>
                        <text x="370" y="535" text-anchor="middle" font-size="9" fill="#155724">
                            Gamma + Reciprocal link
                        </text>
                        <text x="370" y="550" text-anchor="middle" font-size="9" fill="#155724">
                            Saturation relationships
                        </text>
                        
                        <rect x="480" y="500" width="180" height="60" fill="#f8d7da" stroke="#dc3545" stroke-width="1" rx="5"/>
                        <text x="570" y="520" text-anchor="middle" font-size="11" font-weight="bold" fill="#721c24">
                            Medical Trials
                        </text>
                        <text x="570" y="535" text-anchor="middle" font-size="9" fill="#721c24">
                            Binomial + Logit link
                        </text>
                        <text x="570" y="550" text-anchor="middle" font-size="9" fill="#721c24">
                            Success/failure outcomes
                        </text>
                        
                        <rect x="680" y="500" width="150" height="60" fill="#e2e3e5" stroke="#6c757d" stroke-width="1" rx="5"/>
                        <text x="755" y="520" text-anchor="middle" font-size="11" font-weight="bold" fill="#495057">
                            Economics
                        </text>
                        <text x="755" y="535" text-anchor="middle" font-size="9" fill="#495057">
                            Various distributions
                        </text>
                        <text x="755" y="550" text-anchor="middle" font-size="9" fill="#495057">
                            Flexible modeling
                        </text>
                        
                        <!-- Arrows connecting layers -->
                        <defs>
                            <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                            </marker>
                        </defs>
                        
                        <!-- Data to Distribution/Link -->
                        <line x1="245" y1="160" x2="245" y2="180" stroke="#667eea" stroke-width="2" marker-end="url(#arrow)"/>
                        <line x1="655" y1="160" x2="655" y2="180" stroke="#667eea" stroke-width="2" marker-end="url(#arrow)"/>
                        
                        <!-- Distribution/Link to Estimation -->
                        <line x1="245" y1="320" x2="350" y2="340" stroke="#667eea" stroke-width="2" marker-end="url(#arrow)"/>
                        <line x1="655" y1="320" x2="550" y2="340" stroke="#667eea" stroke-width="2" marker-end="url(#arrow)"/>
                        
                        <!-- Estimation to Applications -->
                        <line x1="450" y1="440" x2="450" y2="460" stroke="#667eea" stroke-width="2" marker-end="url(#arrow)"/>
                        
                        <!-- Key insight -->
                        <rect x="100" y="600" width="700" height="50" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                        <text x="450" y="625" text-anchor="middle" font-size="14" font-weight="bold" fill="#155724">
                            Key Insight: GLMs provide a unified framework for diverse response types
                        </text>
                        <text x="450" y="645" text-anchor="middle" font-size="12" fill="#155724">
                            Linear structure + Appropriate distribution + Suitable link function = Flexible modeling
                        </text>
                    </svg>
                </div>
                
                <div class="highlight-box">
                    <h4>Summary of GLM Advantages:</h4>
                    <ul>
                        <li><strong>Flexibility:</strong> Handles diverse response types within unified framework</li>
                        <li><strong>Interpretability:</strong> Parameters have clear meaning on appropriate scales</li>
                        <li><strong>Efficiency:</strong> Maximum likelihood provides optimal estimates</li>
                        <li><strong>Extensibility:</strong> Foundation for advanced models (GLMMs, GAMs, etc.)</li>
                        <li><strong>Robustness:</strong> Appropriate distributional assumptions improve reliability</li>
                    </ul>
                </div>
                
                <div class="formula-box">
                    <h4>Final Thought</h4>
                    <p>GLMs represent a powerful generalization of linear regression that maintains the interpretability and computational advantages of linear models while accommodating the diverse nature of real-world data. By carefully choosing the appropriate distribution and link function, researchers can model complex relationships while maintaining statistical rigor.</p>
                </div>
            </section>
            
        </div>
    
    <script>
        // Progress bar functionality
        window.addEventListener('scroll', () => {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
        });
    </script>
</body>
</html> 