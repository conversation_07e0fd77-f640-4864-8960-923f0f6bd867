<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linear Algebra: Orthogonality Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true,
                tags: 'ams'
            },
            options: {
                ignoreHtmlClass: "tex2jax_ignore",
                processHtmlClass: "tex2jax_process"
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            margin-bottom: 30px;
            color: white;
            box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .learning-objectives {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #00c9ff;
        }

        .learning-objectives h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.8em;
        }

        .learning-objectives ul {
            list-style: none;
            padding-left: 0;
        }

        .learning-objectives li {
            margin: 10px 0;
            padding-left: 25px;
            position: relative;
        }

        .learning-objectives li::before {
            content: "🎯";
            position: absolute;
            left: 0;
        }

        .tutorial-outline {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #ff6b6b;
        }

        .tutorial-outline h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.8em;
        }

        .outline-item {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            border-left: 4px solid #ff6b6b;
        }

        .outline-item h3 {
            color: #e74c3c;
            margin-bottom: 8px;
        }

        .section {
            margin: 40px 0;
            padding: 30px;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #3498db;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2.2em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .section h3 {
            color: #34495e;
            margin: 25px 0 15px 0;
            font-size: 1.5em;
        }

        .info-box {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
        }

        .info-box h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .warning-box {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(253, 203, 110, 0.3);
        }

        .example-box {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3);
        }

        .theorem-box {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(162, 155, 254, 0.3);
        }

        .theorem-box h4 {
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }

        .proof-box {
            background: #f8f9fa;
            border-left: 4px solid #6c5ce7;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
        }

        .nav-button {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00f2fe 0%, #4facfe 100%);
            width: 12.5%; /* 1/8 sections completed */
            transition: width 0.3s ease;
        }

        .svg-container {
            text-align: center;
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .formula-highlight {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 5px 15px rgba(255, 234, 167, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .section {
                padding: 20px;
            }

            .navigation {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Linear Algebra: Orthogonality</h1>
            <p>Chapter 8.1 - Orthogonal Complements and Projections</p>
        </div>

        <!-- Learning Objectives -->
        <div class="learning-objectives">
            <h2>🎯 Learning Objectives</h2>
            <ul>
                <li>Understand orthogonal sets and their fundamental properties in ℝⁿ</li>
                <li>Master the Gram-Schmidt orthogonalization algorithm and its applications</li>
                <li>Learn about orthogonal complements and their geometric significance</li>
                <li>Apply orthogonal projections to solve closest point problems</li>
                <li>Understand orthogonal matrices and their special properties</li>
                <li>Develop computational skills for working with orthogonal bases</li>
            </ul>
        </div>

        <!-- Tutorial Outline -->
        <div class="tutorial-outline">
            <h2>📚 Tutorial Outline</h2>
            <div class="outline-item">
                <h3>1. Introduction to Orthogonality</h3>
                <p>Review concepts, motivation, and the importance of orthogonal bases</p>
            </div>
            <div class="outline-item">
                <h3>2. The Orthogonal Lemma</h3>
                <p>Foundation theorem for constructing orthogonal sets</p>
            </div>
            <div class="outline-item">
                <h3>3. Gram-Schmidt Algorithm</h3>
                <p>Systematic method for creating orthogonal bases</p>
            </div>
            <div class="outline-item">
                <h3>4. Orthogonal Complements</h3>
                <p>Understanding U⊥ and its properties</p>
            </div>
            <div class="outline-item">
                <h3>5. Orthogonal Projections</h3>
                <p>Projecting vectors onto subspaces and closest point problems</p>
            </div>
            <div class="outline-item">
                <h3>6. Linear Operators and Projections</h3>
                <p>Projection as linear transformation and dimension relationships</p>
            </div>
            <div class="outline-item">
                <h3>7. Orthogonal Matrices</h3>
                <p>Special matrices with orthonormal rows and columns</p>
            </div>
            <div class="outline-item">
                <h3>8. Summary and Applications</h3>
                <p>Key takeaways and computational techniques</p>
            </div>
        </div>

        <!-- Section 1: Introduction -->
        <div class="section" id="section1">
            <h2>1. Introduction to Orthogonality</h2>
            
            <p>In our journey through linear algebra, we've learned that bases are fundamental to understanding vector spaces. However, not all bases are created equal. Some bases are much more convenient to work with than others, and <strong>orthogonal bases</strong> are the "nice" bases that make computations elegant and intuitive.</p>

            <div class="info-box">
                <h4>🔍 Why Orthogonal Bases Matter</h4>
                <p>When vectors in a basis are orthogonal (perpendicular) to each other, finding the coordinates of any vector becomes straightforward. Instead of solving systems of equations, we can use simple dot product formulas!</p>
            </div>

            <h3>Review: Orthogonal Sets in ℝⁿ</h3>
            
            <p>Recall from earlier chapters that a set $\{f_1, f_2, \ldots, f_m\}$ of nonzero vectors in $\mathbb{R}^n$ is called <strong>orthogonal</strong> if:</p>
            
            <div class="formula-highlight">
                $$f_i \cdot f_j = 0 \text{ for all } i \neq j$$
            </div>

            <div class="svg-container">
                <svg width="400" height="300" viewBox="0 0 400 300">
                    <!-- Background -->
                    <rect width="400" height="300" fill="#f8f9fa"/>
                    
                    <!-- Grid -->
                    <defs>
                        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="0.5"/>
                        </pattern>
                    </defs>
                    <rect width="400" height="300" fill="url(#grid)"/>
                    
                    <!-- Coordinate system -->
                    <line x1="200" y1="50" x2="200" y2="250" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="50" y1="150" x2="350" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrow marker -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                        <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#3498db"/>
                        </marker>
                        <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                    
                    <!-- Orthogonal vectors -->
                    <line x1="200" y1="150" x2="320" y2="150" stroke="#3498db" stroke-width="4" marker-end="url(#arrowhead-blue)"/>
                    <line x1="200" y1="150" x2="200" y2="80" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead-red)"/>
                    
                    <!-- Right angle indicator -->
                    <path d="M 215 150 L 215 135 L 200 135" fill="none" stroke="#2c3e50" stroke-width="2"/>
                    
                    <!-- Labels -->
                    <text x="330" y="145" fill="#3498db" font-size="16" font-weight="bold">f₁</text>
                    <text x="205" y="75" fill="#e74c3c" font-size="16" font-weight="bold">f₂</text>
                    <text x="210" y="170" fill="#2c3e50" font-size="12">90°</text>
                    <text x="355" y="155" fill="#333" font-size="14">x</text>
                    <text x="205" y="45" fill="#333" font-size="14">y</text>
                    
                    <!-- Title -->
                    <text x="200" y="25" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Orthogonal Vectors: f₁ ⊥ f₂</text>
                </svg>
            </div>

            <h3>The Power of Orthogonality</h3>
            
            <p>The key insight is that <strong>every orthogonal set is linearly independent</strong>. This means that if we have an orthogonal set with the right number of vectors, it automatically forms a basis! But even better, if $\{f_1, f_2, \ldots, f_m\}$ is an orthogonal basis for a subspace $U$, then for any vector $x$ in $U$, we can write:</p>

            <div class="formula-highlight">
                $$x = \frac{x \cdot f_1}{\|f_1\|^2} f_1 + \frac{x \cdot f_2}{\|f_2\|^2} f_2 + \cdots + \frac{x \cdot f_m}{\|f_m\|^2} f_m$$
            </div>

            <div class="info-box">
                <h4>💡 Key Insight</h4>
                <p>With orthogonal bases, the coefficients in the linear combination are given by simple formulas involving dot products. No need to solve systems of linear equations!</p>
            </div>

            <h3>Our Mission in This Chapter</h3>
            
            <p>This chapter addresses several fundamental questions:</p>
            
            <div class="example-box">
                <h4>🎯 Chapter Goals</h4>
                <ul style="color: white; margin-left: 20px;">
                    <li><strong>Existence:</strong> Does every subspace of ℝⁿ have an orthogonal basis?</li>
                    <li><strong>Construction:</strong> How can we systematically create orthogonal bases?</li>
                    <li><strong>Applications:</strong> How do we use orthogonal bases to solve geometric problems?</li>
                    <li><strong>Special Matrices:</strong> What matrices have orthogonal eigenvector bases?</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="500" height="200" viewBox="0 0 500 200">
                    <!-- Background -->
                    <rect width="500" height="200" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="250" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Chapter Journey: From Any Basis to Orthogonal Basis</text>
                    
                    <!-- Arbitrary basis -->
                    <g transform="translate(80, 100)">
                        <line x1="0" y1="0" x2="40" y2="-20" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="0" x2="20" y2="30" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <text x="45" y="-15" fill="#e74c3c" font-size="12">x₁</text>
                        <text x="25" y="35" fill="#3498db" font-size="12">x₂</text>
                        <text x="-10" y="50" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Arbitrary Basis</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 150 100 Q 200 60 250 100" fill="none" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <text x="200" y="75" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Gram-Schmidt</text>
                    
                    <!-- Orthogonal basis -->
                    <g transform="translate(320, 100)">
                        <line x1="0" y1="0" x2="40" y2="0" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="0" x2="0" y2="-30" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <path d="M 12 0 L 12 -12 L 0 -12" fill="none" stroke="#2c3e50" stroke-width="1"/>
                        <text x="45" y="5" fill="#e74c3c" font-size="12">f₁</text>
                        <text x="5" y="-35" fill="#3498db" font-size="12">f₂</text>
                        <text x="20" y="50" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Orthogonal Basis</text>
                    </g>
                    
                    <!-- Benefits box -->
                    <rect x="370" y="60" width="120" height="80" fill="#a8edea" stroke="#00c9ff" stroke-width="2" rx="5"/>
                    <text x="430" y="75" text-anchor="middle" fill="#2c3e50" font-size="10" font-weight="bold">Benefits:</text>
                    <text x="430" y="90" text-anchor="middle" fill="#2c3e50" font-size="9">• Easy coefficients</text>
                    <text x="430" y="105" text-anchor="middle" fill="#2c3e50" font-size="9">• Simple projections</text>
                    <text x="430" y="120" text-anchor="middle" fill="#2c3e50" font-size="9">• Elegant formulas</text>
                    <text x="430" y="135" text-anchor="middle" fill="#2c3e50" font-size="9">• Geometric clarity</text>
                </svg>
            </div>

            <div class="warning-box">
                <h4>⚠️ Preview of What's Coming</h4>
                <p>We'll discover that the answer to our existence question is a resounding YES! Every subspace of ℝⁿ has an orthogonal basis, and we'll learn a systematic algorithm (Gram-Schmidt) to construct them. This will lead us to powerful tools for solving geometric problems and understanding special types of matrices.</p>
            </div>
        </div>

        <!-- Section 2: The Orthogonal Lemma -->
        <div class="section" id="section2" style="display: none;">
            <h2>2. The Orthogonal Lemma</h2>
            
            <p>Now we come to the heart of orthogonal construction. The <strong>Orthogonal Lemma</strong> provides the theoretical foundation for building orthogonal sets from existing vectors. This lemma will be the key to proving that every subspace has an orthogonal basis and will lead us directly to the Gram-Schmidt algorithm.</p>

            <div class="theorem-box">
                <h4>📐 Lemma 8.1.1: The Orthogonal Lemma</h4>
                <p>Let $\{f_1, f_2, \ldots, f_m\}$ be an orthogonal set in $\mathbb{R}^n$. Given $x$ in $\mathbb{R}^n$, write:</p>
                <div class="formula-highlight" style="background: rgba(255,255,255,0.2); margin: 15px 0;">
                    $$f_{m+1} = x - \frac{x \cdot f_1}{\|f_1\|^2} f_1 - \frac{x \cdot f_2}{\|f_2\|^2} f_2 - \cdots - \frac{x \cdot f_m}{\|f_m\|^2} f_m$$
                </div>
                <p><strong>Then:</strong></p>
                <ol style="color: white; margin-left: 20px;">
                    <li>$f_{m+1} \cdot f_k = 0$ for $k = 1, 2, \ldots, m$</li>
                    <li>If $x$ is not in $\text{span}\{f_1, \ldots, f_m\}$, then $f_{m+1} \neq 0$ and $\{f_1, \ldots, f_m, f_{m+1}\}$ is orthogonal</li>
                </ol>
            </div>

            <div class="info-box">
                <h4>🔍 Understanding the Construction</h4>
                <p>The formula for $f_{m+1}$ takes the vector $x$ and <strong>subtracts away all its "components" in the directions of the existing orthogonal vectors</strong>. What remains is orthogonal to all of them!</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Orthogonal Lemma: Constructing f₃ from x</text>
                    
                    <!-- Coordinate system -->
                    <g transform="translate(300, 200)">
                        <!-- Existing orthogonal vectors f1, f2 -->
                        <line x1="0" y1="0" x2="120" y2="0" stroke="#3498db" stroke-width="4" marker-end="url(#arrowhead-blue)"/>
                        <line x1="0" y1="0" x2="0" y2="-80" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead-red)"/>
                        
                        <!-- Vector x -->
                        <line x1="0" y1="0" x2="80" y2="-60" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
                        
                        <!-- Projections -->
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#3498db" stroke-width="2" opacity="0.7"/>
                        <line x1="80" y1="0" x2="80" y2="-60" stroke="#e74c3c" stroke-width="2" opacity="0.7"/>
                        <line x1="0" y1="0" x2="0" y2="-60" stroke="#e74c3c" stroke-width="2" opacity="0.7"/>
                        
                        <!-- Resulting orthogonal vector f3 -->
                        <line x1="80" y1="-60" x2="80" y2="-100" stroke="#9b59b6" stroke-width="4" marker-end="url(#arrowhead-purple)"/>
                        
                        <!-- Projection lines (dashed) -->
                        <line x1="80" y1="0" x2="80" y2="-60" stroke="#666" stroke-width="1" stroke-dasharray="2,2"/>
                        <line x1="0" y1="-60" x2="80" y2="-60" stroke="#666" stroke-width="1" stroke-dasharray="2,2"/>
                        
                        <!-- Right angle indicators -->
                        <path d="M 15 0 L 15 -15 L 0 -15" fill="none" stroke="#2c3e50" stroke-width="1"/>
                        <path d="M 80 -15 L 95 -15 L 95 0" fill="none" stroke="#2c3e50" stroke-width="1"/>
                        <path d="M 65 -60 L 65 -75 L 80 -75" fill="none" stroke="#2c3e50" stroke-width="1"/>
                        
                        <!-- Labels -->
                        <text x="125" y="5" fill="#3498db" font-size="16" font-weight="bold">f₁</text>
                        <text x="5" y="-85" fill="#e74c3c" font-size="16" font-weight="bold">f₂</text>
                        <text x="85" y="-55" fill="#2c3e50" font-size="16" font-weight="bold">x</text>
                        <text x="85" y="-105" fill="#9b59b6" font-size="16" font-weight="bold">f₃</text>
                        
                        <!-- Component labels -->
                        <text x="40" y="15" fill="#3498db" font-size="12">proj₁(x)</text>
                        <text x="-25" y="-30" fill="#e74c3c" font-size="12">proj₂(x)</text>
                        
                        <!-- Arrow marker for purple -->
                        <defs>
                            <marker id="arrowhead-purple" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#9b59b6"/>
                            </marker>
                        </defs>
                    </g>
                    
                    <!-- Formula breakdown -->
                    <rect x="20" y="320" width="560" height="70" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                    <text x="300" y="340" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Construction Formula:</text>
                    <text x="300" y="360" text-anchor="middle" fill="#2c3e50" font-size="12">f₃ = x - (x·f₁/||f₁||²)f₁ - (x·f₂/||f₂||²)f₂</text>
                    <text x="300" y="375" text-anchor="middle" fill="#2c3e50" font-size="11">Remove all components in existing orthogonal directions</text>
                </svg>
            </div>

            <h3>Proof of the Orthogonal Lemma</h3>
            
            <div class="proof-box">
                <h4><strong>Proof:</strong></h4>
                <p>For convenience, let's write $t_i = \frac{x \cdot f_i}{\|f_i\|^2}$ for each $i = 1, 2, \ldots, m$.</p>
                
                <p><strong>Part (1):</strong> We need to show that $f_{m+1} \cdot f_k = 0$ for any $k \in \{1, 2, \ldots, m\}$.</p>
                
                <p>Computing the dot product:</p>
                <div class="formula-highlight">
                    \begin{align}
                    f_{m+1} \cdot f_k &= (x - t_1f_1 - t_2f_2 - \cdots - t_mf_m) \cdot f_k \\
                    &= x \cdot f_k - t_1(f_1 \cdot f_k) - t_2(f_2 \cdot f_k) - \cdots - t_k(f_k \cdot f_k) - \cdots - t_m(f_m \cdot f_k)
                    \end{align}
                </div>
                
                <p>Since $\{f_1, f_2, \ldots, f_m\}$ is orthogonal, we have $f_i \cdot f_k = 0$ for all $i \neq k$. The only non-zero term is when $i = k$:</p>
                
                <div class="formula-highlight">
                    $$f_{m+1} \cdot f_k = x \cdot f_k - t_k\|f_k\|^2 = x \cdot f_k - \frac{x \cdot f_k}{\|f_k\|^2} \cdot \|f_k\|^2 = 0$$
                </div>
                
                <p><strong>Part (2):</strong> If $x \notin \text{span}\{f_1, \ldots, f_m\}$, then $f_{m+1} \neq 0$ because otherwise $x$ would be a linear combination of $f_1, \ldots, f_m$, contradicting our assumption. Since $f_{m+1} \neq 0$ and is orthogonal to each $f_i$, the set $\{f_1, \ldots, f_m, f_{m+1}\}$ is orthogonal. ∎</p>
            </div>

            <div class="example-box">
                <h4>🔢 Example: Constructing an Orthogonal Vector</h4>
                <p>Let's see the lemma in action! Suppose we have orthogonal vectors:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li>$f_1 = (1, 0, 1)$</li>
                    <li>$f_2 = (1, 0, -1)$</li>
                </ul>
                <p>And we want to extend this to include $x = (0, 1, 0)$.</p>
                
                <p><strong>Step 1:</strong> Check if $x$ is in $\text{span}\{f_1, f_2\}$</p>
                <p>Since $f_1$ and $f_2$ span vectors of the form $(a, 0, b)$, clearly $x = (0, 1, 0)$ is not in their span.</p>
                
                <p><strong>Step 2:</strong> Apply the orthogonal lemma formula</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    f_3 &= x - \frac{x \cdot f_1}{\|f_1\|^2} f_1 - \frac{x \cdot f_2}{\|f_2\|^2} f_2 \\
                    &= (0,1,0) - \frac{0}{2}(1,0,1) - \frac{0}{2}(1,0,-1) \\
                    &= (0,1,0)
                    \end{align}
                </div>
                
                <p><strong>Verification:</strong> $f_3 \cdot f_1 = 0 \cdot 1 + 1 \cdot 0 + 0 \cdot 1 = 0$ ✓</p>
                <p>$f_3 \cdot f_2 = 0 \cdot 1 + 1 \cdot 0 + 0 \cdot (-1) = 0$ ✓</p>
            </div>

            <h3>Consequences of the Orthogonal Lemma</h3>
            
            <p>This seemingly simple lemma has profound consequences:</p>
            
            <div class="info-box">
                <h4>🎯 Key Implications</h4>
                <ol style="color: white; margin-left: 20px;">
                    <li><strong>Existence:</strong> Every subspace of ℝⁿ has an orthogonal basis</li>
                    <li><strong>Algorithm:</strong> We can systematically convert any basis to an orthogonal one</li>
                    <li><strong>Construction:</strong> We can build larger orthogonal sets from smaller ones</li>
                </ol>
            </div>

            <div class="warning-box">
                <h4>🔮 Coming Next</h4>
                <p>In the next section, we'll see how the Orthogonal Lemma leads directly to the famous <strong>Gram-Schmidt Algorithm</strong>, which allows us to systematically convert any basis into an orthogonal basis. This algorithm is one of the most important computational tools in linear algebra!</p>
            </div>
        </div>

        <!-- Section 3: Gram-Schmidt Algorithm -->
        <div class="section" id="section3" style="display: none;">
            <h2>3. The Gram-Schmidt Orthogonalization Algorithm</h2>
            
            <p>Armed with the Orthogonal Lemma, we can now tackle one of the most important results in linear algebra: <strong>every subspace of ℝⁿ has an orthogonal basis</strong>. Moreover, we can systematically construct such a basis using the famous Gram-Schmidt algorithm.</p>

            <div class="theorem-box">
                <h4>🏗️ Theorem 8.1.1: Existence of Orthogonal Bases</h4>
                <p>Let $U$ be a subspace of $\mathbb{R}^n$. Then:</p>
                <ol style="color: white; margin-left: 20px;">
                    <li>Every orthogonal subset $\{f_1, \ldots, f_m\}$ in $U$ is a subset of an orthogonal basis of $U$</li>
                    <li>$U$ has an orthogonal basis</li>
                </ol>
            </div>

            <div class="info-box">
                <h4>🎯 The Big Picture</h4>
                <p>This theorem tells us that <strong>orthogonal bases always exist</strong>! But even better, it gives us a constructive way to build them using the Orthogonal Lemma repeatedly.</p>
            </div>

            <h3>The Gram-Schmidt Algorithm</h3>
            
            <p>Now comes the practical payoff. The Gram-Schmidt algorithm allows us to convert <em>any</em> basis into an orthogonal basis. Here's how it works:</p>

            <div class="theorem-box">
                <h4>⚙️ Theorem 8.1.2: Gram-Schmidt Orthogonalization Algorithm</h4>
                <p>If $\{x_1, x_2, \ldots, x_m\}$ is any basis of a subspace $U$ of $\mathbb{R}^n$, construct $f_1, f_2, \ldots, f_m$ in $U$ successively as follows:</p>
                
                <div class="formula-highlight" style="background: rgba(255,255,255,0.2); margin: 15px 0;">
                    \begin{align}
                    f_1 &= x_1 \\
                    f_2 &= x_2 - \frac{x_2 \cdot f_1}{\|f_1\|^2} f_1 \\
                    f_3 &= x_3 - \frac{x_3 \cdot f_1}{\|f_1\|^2} f_1 - \frac{x_3 \cdot f_2}{\|f_2\|^2} f_2 \\
                    &\vdots \\
                    f_k &= x_k - \frac{x_k \cdot f_1}{\|f_1\|^2} f_1 - \frac{x_k \cdot f_2}{\|f_2\|^2} f_2 - \cdots - \frac{x_k \cdot f_{k-1}}{\|f_{k-1}\|^2} f_{k-1}
                    \end{align}
                </div>
                
                <p><strong>Then:</strong></p>
                <ol style="color: white; margin-left: 20px;">
                    <li>$\{f_1, f_2, \ldots, f_m\}$ is an orthogonal basis of $U$</li>
                    <li>$\text{span}\{f_1, f_2, \ldots, f_k\} = \text{span}\{x_1, x_2, \ldots, x_k\}$ for each $k$</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="700" height="500" viewBox="0 0 700 500">
                    <!-- Background -->
                    <rect width="700" height="500" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Gram-Schmidt Algorithm: Step by Step</text>
                    
                    <!-- Step 1: Start with x1 -->
                    <g transform="translate(100, 80)">
                        <rect x="-50" y="-20" width="100" height="100" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="0" y="-5" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Step 1</text>
                        <line x1="0" y1="20" x2="40" y2="20" stroke="#3498db" stroke-width="4" marker-end="url(#arrowhead-blue)"/>
                        <text x="0" y="45" text-anchor="middle" fill="#3498db" font-size="12" font-weight="bold">f₁ = x₁</text>
                        <text x="0" y="60" text-anchor="middle" fill="#2c3e50" font-size="10">Simply take the first vector</text>
                    </g>
                    
                    <!-- Arrow to Step 2 -->
                    <path d="M 200 130 Q 250 110 300 130" fill="none" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 2: Orthogonalize x2 -->
                    <g transform="translate(350, 80)">
                        <rect x="-50" y="-20" width="100" height="140" fill="#ffe8e8" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="0" y="-5" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Step 2</text>
                        
                        <!-- Original x2 -->
                        <line x1="0" y1="20" x2="30" y2="40" stroke="#666" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="35" y="45" fill="#666" font-size="10">x₂</text>
                        
                        <!-- f1 -->
                        <line x1="0" y1="20" x2="40" y2="20" stroke="#3498db" stroke-width="2"/>
                        <text x="45" y="25" fill="#3498db" font-size="10">f₁</text>
                        
                        <!-- Projection -->
                        <line x1="0" y1="20" x2="24" y2="20" stroke="#3498db" stroke-width="2" opacity="0.5"/>
                        <line x1="24" y1="20" x2="30" y2="40" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        
                        <!-- Labels -->
                        <text x="0" y="70" text-anchor="middle" fill="#e74c3c" font-size="11" font-weight="bold">f₂ = x₂ - proj₁(x₂)</text>
                        <text x="0" y="85" text-anchor="middle" fill="#2c3e50" font-size="9">Remove component along f₁</text>
                        <text x="0" y="100" text-anchor="middle" fill="#2c3e50" font-size="9">Result is orthogonal to f₁</text>
                    </g>
                    
                    <!-- Arrow to Step 3 -->
                    <path d="M 450 150 Q 500 130 550 150" fill="none" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 3: Orthogonalize x3 -->
                    <g transform="translate(600, 80)">
                        <rect x="-50" y="-20" width="100" height="140" fill="#f0e8ff" stroke="#9b59b6" stroke-width="2" rx="10"/>
                        <text x="0" y="-5" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Step 3</text>
                        
                        <!-- f1 and f2 -->
                        <line x1="0" y1="20" x2="35" y2="20" stroke="#3498db" stroke-width="2"/>
                        <line x1="0" y1="20" x2="20" y2="40" stroke="#e74c3c" stroke-width="2"/>
                        
                        <!-- Original x3 -->
                        <line x1="0" y1="20" x2="15" y2="0" stroke="#666" stroke-width="2" stroke-dasharray="3,3"/>
                        
                        <!-- Result f3 -->
                        <line x1="0" y1="20" x2="-10" y2="0" stroke="#9b59b6" stroke-width="3" marker-end="url(#arrowhead-purple)"/>
                        
                        <!-- Labels -->
                        <text x="0" y="70" text-anchor="middle" fill="#9b59b6" font-size="10" font-weight="bold">f₃ = x₃ - proj₁(x₃) - proj₂(x₃)</text>
                        <text x="0" y="85" text-anchor="middle" fill="#2c3e50" font-size="9">Remove components along</text>
                        <text x="0" y="100" text-anchor="middle" fill="#2c3e50" font-size="9">both f₁ and f₂</text>
                    </g>
                    
                    <!-- Algorithm summary -->
                    <rect x="50" y="280" width="600" height="180" fill="#f8f9fa" stroke="#2c3e50" stroke-width="2" rx="15"/>
                    <text x="350" y="305" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">General Gram-Schmidt Formula</text>
                    
                    <text x="350" y="330" text-anchor="middle" fill="#2c3e50" font-size="14">For each step k = 2, 3, ..., m:</text>
                    
                    <text x="350" y="360" text-anchor="middle" fill="#2c3e50" font-size="13">f_k = x_k - Σ(i=1 to k-1) [(x_k · f_i) / ||f_i||²] f_i</text>
                    
                    <rect x="80" y="380" width="540" height="70" fill="#e8f4fd" stroke="#3498db" stroke-width="1" rx="8"/>
                    <text x="350" y="400" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Key Insight:</text>
                    <text x="350" y="420" text-anchor="middle" fill="#2c3e50" font-size="11">At each step, we remove ALL components of x_k in the directions</text>
                    <text x="350" y="435" text-anchor="middle" fill="#2c3e50" font-size="11">of the previously constructed orthogonal vectors f₁, f₂, ..., f_{k-1}</text>
                </svg>
            </div>

            <h3>Detailed Example: Row Space Orthogonalization</h3>
            
            <div class="example-box">
                <h4>🔢 Example 8.1.1: Orthogonalizing Matrix Rows</h4>
                <p>Find an orthogonal basis of the row space of:</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    $$A = \begin{pmatrix}
                    1 & 1 & -1 & -1 \\
                    3 & 2 & 0 & 1 \\
                    1 & 0 & 1 & 0
                    \end{pmatrix}$$
                </div>
                
                <p><strong>Solution:</strong> Let $x_1, x_2, x_3$ denote the rows of $A$. We can verify that $\{x_1, x_2, x_3\}$ is linearly independent.</p>
                
                <p><strong>Step 1:</strong> $f_1 = x_1 = (1, 1, -1, -1)$</p>
                
                <p><strong>Step 2:</strong> Compute $f_2$</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    f_2 &= x_2 - \frac{x_2 \cdot f_1}{\|f_1\|^2} f_1 \\
                    &= (3, 2, 0, 1) - \frac{(3, 2, 0, 1) \cdot (1, 1, -1, -1)}{4} (1, 1, -1, -1) \\
                    &= (3, 2, 0, 1) - \frac{3 + 2 + 0 - 1}{4} (1, 1, -1, -1) \\
                    &= (3, 2, 0, 1) - \frac{4}{4} (1, 1, -1, -1) \\
                    &= (3, 2, 0, 1) - (1, 1, -1, -1) \\
                    &= (2, 1, 1, 2)
                    \end{align}
                </div>
                
                <p><strong>Step 3:</strong> Compute $f_3$</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    f_3 &= x_3 - \frac{x_3 \cdot f_1}{\|f_1\|^2} f_1 - \frac{x_3 \cdot f_2}{\|f_2\|^2} f_2 \\
                    x_3 \cdot f_1 &= (1, 0, 1, 0) \cdot (1, 1, -1, -1) = 1 + 0 - 1 + 0 = 0 \\
                    x_3 \cdot f_2 &= (1, 0, 1, 0) \cdot (2, 1, 1, 2) = 2 + 0 + 1 + 0 = 3 \\
                    \|f_2\|^2 &= 4 + 1 + 1 + 4 = 10 \\
                    f_3 &= (1, 0, 1, 0) - \frac{0}{4}(1, 1, -1, -1) - \frac{3}{10}(2, 1, 1, 2) \\
                    &= (1, 0, 1, 0) - \frac{3}{10}(2, 1, 1, 2) \\
                    &= (1, 0, 1, 0) - (0.6, 0.3, 0.3, 0.6) \\
                    &= (0.4, -0.3, 0.7, -0.6) \\
                    &= \frac{1}{10}(4, -3, 7, -6)
                    \end{align}
                </div>
                
                <p><strong>Result:</strong> An orthogonal basis for the row space is:</p>
                <p style="text-align: center; color: white;">$\{(1, 1, -1, -1), (2, 1, 1, 2), (4, -3, 7, -6)\}$</p>
            </div>

            <div class="info-box">
                <h4>💡 Practical Tip</h4>
                <p>In hand calculations, you can multiply any vector by a nonzero scalar at any step without affecting the final orthogonal basis. This often helps eliminate fractions and simplify computations!</p>
            </div>

            <h3>Historical Context</h3>
            
            <div class="warning-box">
                <h4>📚 Historical Note</h4>
                <p><strong>Jörgen Pederson Gram (1850-1916)</strong> was a Danish actuary who developed the mathematical foundations. <strong>Erhardt Schmidt (1876-1959)</strong> was a German mathematician who studied under David Hilbert and formalized the algorithm in 1907 as part of his work on Hilbert spaces.</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Gram-Schmidt: Before and After</text>
                    
                    <!-- Before: Non-orthogonal basis -->
                    <g transform="translate(150, 150)">
                        <text x="0" y="-100" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Before: Arbitrary Basis</text>
                        <line x1="0" y1="0" x2="60" y2="-20" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="0" x2="30" y2="50" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <line x1="0" y1="0" x2="-20" y2="-40" stroke="#9b59b6" stroke-width="3" marker-end="url(#arrowhead-purple)"/>
                        
                        <!-- Angle indicators -->
                        <path d="M 15 -5 A 15 15 0 0 1 8 12" fill="none" stroke="#f39c12" stroke-width="2"/>
                        <path d="M 8 12 A 15 15 0 0 1 -5 -10" fill="none" stroke="#f39c12" stroke-width="2"/>
                        
                        <text x="65" y="-15" fill="#e74c3c" font-size="12">x₁</text>
                        <text x="35" y="55" fill="#3498db" font-size="12">x₂</text>
                        <text x="-25" y="-45" fill="#9b59b6" font-size="12">x₃</text>
                        <text x="0" y="80" text-anchor="middle" fill="#e74c3c" font-size="12">Complicated angles</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 250 150 Q 300 120 350 150" fill="none" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <text x="300" y="130" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Gram-Schmidt</text>
                    
                    <!-- After: Orthogonal basis -->
                    <g transform="translate(450, 150)">
                        <text x="0" y="-100" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">After: Orthogonal Basis</text>
                        <line x1="0" y1="0" x2="60" y2="0" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="0" x2="0" y2="-50" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <line x1="0" y1="0" x2="-30" y2="30" stroke="#9b59b6" stroke-width="3" marker-end="url(#arrowhead-purple)"/>
                        
                        <!-- Right angle indicators -->
                        <path d="M 15 0 L 15 -15 L 0 -15" fill="none" stroke="#f39c12" stroke-width="2"/>
                        <path d="M -15 15 L -15 0 L 0 0" fill="none" stroke="#f39c12" stroke-width="2"/>
                        <path d="M -15 15 L 0 15 L 0 0" fill="none" stroke="#f39c12" stroke-width="2"/>
                        
                        <text x="65" y="5" fill="#e74c3c" font-size="12">f₁</text>
                        <text x="5" y="-55" fill="#3498db" font-size="12">f₂</text>
                        <text x="-35" y="35" fill="#9b59b6" font-size="12">f₃</text>
                        <text x="0" y="80" text-anchor="middle" fill="#00b894" font-size="12">All 90° angles!</text>
                    </g>
                </svg>
            </div>

            <div class="warning-box">
                <h4>🔮 What's Next</h4>
                <p>Now that we know how to create orthogonal bases, we can tackle some beautiful geometric problems. In the next section, we'll explore <strong>orthogonal complements</strong> - the set of all vectors orthogonal to a given subspace - and see how they lead to powerful projection formulas.</p>
            </div>
        </div>

        <!-- Section 4: Orthogonal Complements -->
        <div class="section" id="section4" style="display: none;">
            <h2>4. Orthogonal Complements</h2>
            
            <p>Now we explore a fundamental geometric concept: given a subspace $U$ of $\mathbb{R}^n$, what is the set of all vectors that are orthogonal to <em>every</em> vector in $U$? This leads us to the notion of the <strong>orthogonal complement</strong>, a powerful tool for understanding the geometry of vector spaces.</p>

            <div class="info-box">
                <h4>🎯 Geometric Intuition</h4>
                <p>Think of a plane through the origin in ℝ³. The orthogonal complement consists of all vectors perpendicular to this plane - which forms a line through the origin! This geometric insight generalizes beautifully to higher dimensions.</p>
            </div>

            <div class="theorem-box">
                <h4>📐 Definition 8.1: Orthogonal Complement</h4>
                <p>If $U$ is a subspace of $\mathbb{R}^n$, define the <strong>orthogonal complement</strong> $U^{\perp}$ of $U$ (pronounced "U-perp") by:</p>
                <div class="formula-highlight" style="background: rgba(255,255,255,0.2); margin: 15px 0;">
                    $$U^{\perp} = \{x \in \mathbb{R}^n \mid x \cdot y = 0 \text{ for all } y \in U\}$$
                </div>
                <p style="color: white;">In words: $U^{\perp}$ consists of all vectors orthogonal to every vector in $U$.</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Orthogonal Complement in ℝ³</text>
                    
                    <!-- 3D-like coordinate system -->
                    <g transform="translate(300, 200)">
                        <!-- Plane U (represented as an ellipse) -->
                        <ellipse cx="0" cy="20" rx="120" ry="60" fill="#3498db" fill-opacity="0.3" stroke="#3498db" stroke-width="2"/>
                        
                        <!-- Vectors in the plane U -->
                        <line x1="0" y1="20" x2="80" y2="0" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <line x1="0" y1="20" x2="-60" y2="40" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <line x1="0" y1="20" x2="40" y2="60" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        
                        <!-- Orthogonal complement U⊥ (vertical line) -->
                        <line x1="0" y1="20" x2="0" y2="-100" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="20" x2="0" y2="80" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead-red)"/>
                        
                        <!-- Right angle indicators -->
                        <circle cx="0" cy="20" r="15" fill="none" stroke="#f39c12" stroke-width="2" stroke-dasharray="2,2"/>
                        
                        <!-- Labels -->
                        <text x="85" y="-5" fill="#3498db" font-size="14" font-weight="bold">u₁ ∈ U</text>
                        <text x="-80" y="50" fill="#3498db" font-size="14" font-weight="bold">u₂ ∈ U</text>
                        <text x="50" y="70" fill="#3498db" font-size="14" font-weight="bold">u₃ ∈ U</text>
                        <text x="10" y="-110" fill="#e74c3c" font-size="14" font-weight="bold">U⊥</text>
                        <text x="10" y="90" fill="#e74c3c" font-size="14" font-weight="bold">U⊥</text>
                        
                        <!-- Plane label -->
                        <text x="0" y="100" text-anchor="middle" fill="#3498db" font-size="16" font-weight="bold">Subspace U</text>
                        <text x="0" y="115" text-anchor="middle" fill="#2c3e50" font-size="12">(plane through origin)</text>
                        
                        <!-- Orthogonal complement label -->
                        <text x="120" y="-50" fill="#e74c3c" font-size="14" font-weight="bold">Orthogonal Complement U⊥</text>
                        <text x="120" y="-35" fill="#2c3e50" font-size="12">(line through origin)</text>
                    </g>
                    
                    <!-- Key insight box -->
                    <rect x="50" y="320" width="500" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                    <text x="300" y="340" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Key Insight:</text>
                    <text x="300" y="355" text-anchor="middle" fill="#2c3e50" font-size="12">If U is a 2D subspace (plane), then U⊥ is a 1D subspace (line)</text>
                    <text x="300" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">In general: dim(U) + dim(U⊥) = n</text>
                </svg>
            </div>

            <h3>Properties of Orthogonal Complements</h3>
            
            <div class="theorem-box">
                <h4>🔧 Lemma 8.1.2: Properties of Orthogonal Complements</h4>
                <p>Let $U$ be a subspace of $\mathbb{R}^n$. Then:</p>
                <ol style="color: white; margin-left: 20px;">
                    <li>$U^{\perp}$ is a subspace of $\mathbb{R}^n$</li>
                    <li>$\{0\}^{\perp} = \mathbb{R}^n$ and $(\mathbb{R}^n)^{\perp} = \{0\}$</li>
                    <li>If $U = \text{span}\{x_1, x_2, \ldots, x_k\}$, then $U^{\perp} = \{x \in \mathbb{R}^n \mid x \cdot x_i = 0 \text{ for } i = 1, 2, \ldots, k\}$</li>
                </ol>
            </div>

            <div class="proof-box">
                <h4><strong>Proof of Property (3):</strong></h4>
                <p>Let $U = \text{span}\{x_1, x_2, \ldots, x_k\}$. We must show that:</p>
                <p>$U^{\perp} = \{x \in \mathbb{R}^n \mid x \cdot x_i = 0 \text{ for each } i\}$</p>
                
                <p><strong>($\Rightarrow$):</strong> If $x \in U^{\perp}$, then $x \cdot y = 0$ for all $y \in U$. Since each $x_i \in U$, we have $x \cdot x_i = 0$ for all $i$.</p>
                
                <p><strong>($\Leftarrow$):</strong> Suppose $x \cdot x_i = 0$ for all $i$. We must show $x \in U^{\perp}$, i.e., $x \cdot y = 0$ for each $y \in U$.</p>
                
                <p>Any $y \in U$ can be written as $y = r_1x_1 + r_2x_2 + \cdots + r_kx_k$ for some scalars $r_i$. Then:</p>
                
                <div class="formula-highlight">
                    \begin{align}
                    x \cdot y &= x \cdot (r_1x_1 + r_2x_2 + \cdots + r_kx_k) \\
                    &= r_1(x \cdot x_1) + r_2(x \cdot x_2) + \cdots + r_k(x \cdot x_k) \\
                    &= r_1 \cdot 0 + r_2 \cdot 0 + \cdots + r_k \cdot 0 = 0
                    \end{align}
                </div>
                <p>Therefore, $x \in U^{\perp}$. ∎</p>
            </div>

            <h3>Computing Orthogonal Complements</h3>
            
            <div class="example-box">
                <h4>🔢 Example 8.1.2: Finding an Orthogonal Complement</h4>
                <p>Find $U^{\perp}$ if $U = \text{span}\{(1, -1, 2, 0), (1, 0, -2, 3)\}$ in $\mathbb{R}^4$.</p>
                
                <p><strong>Solution:</strong> By Lemma 8.1.2, $x = (x, y, z, w) \in U^{\perp}$ if and only if it is orthogonal to both generators of $U$:</p>
                
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    x \cdot (1, -1, 2, 0) &= 0 \quad \Rightarrow \quad x - y + 2z = 0 \\
                    x \cdot (1, 0, -2, 3) &= 0 \quad \Rightarrow \quad x - 2z + 3w = 0
                    \end{align}
                </div>
                
                <p><strong>Step 1:</strong> Solve the system of homogeneous equations:</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    $$\begin{pmatrix}
                    1 & -1 & 2 & 0 \\
                    1 & 0 & -2 & 3
                    \end{pmatrix}
                    \begin{pmatrix} x \\ y \\ z \\ w \end{pmatrix} = \begin{pmatrix} 0 \\ 0 \end{pmatrix}$$
                </div>
                
                <p><strong>Step 2:</strong> Row reduce the coefficient matrix:</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    $$\begin{pmatrix}
                    1 & -1 & 2 & 0 \\
                    1 & 0 & -2 & 3
                    \end{pmatrix} \sim \begin{pmatrix}
                    1 & -1 & 2 & 0 \\
                    0 & 1 & -4 & 3
                    \end{pmatrix} \sim \begin{pmatrix}
                    1 & 0 & -2 & 3 \\
                    0 & 1 & -4 & 3
                    \end{pmatrix}$$
                </div>
                
                <p><strong>Step 3:</strong> Express the solution in parametric form:</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    x &= 2z - 3w \\
                    y &= 4z - 3w \\
                    z &= z \\
                    w &= w
                    \end{align}
                </div>
                
                <p><strong>Result:</strong> $U^{\perp} = \text{span}\{(2, 4, 1, 0), (3, 3, 0, -1)\}$</p>
                
                <p><strong>Verification:</strong> Check that both basis vectors are orthogonal to the generators of $U$:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li>$(2, 4, 1, 0) \cdot (1, -1, 2, 0) = 2 - 4 + 2 + 0 = 0$ ✓</li>
                    <li>$(2, 4, 1, 0) \cdot (1, 0, -2, 3) = 2 + 0 - 2 + 0 = 0$ ✓</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Background -->
                    <rect width="700" height="300" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Computing Orthogonal Complements: The Process</text>
                    
                    <!-- Step 1: Given subspace -->
                    <g transform="translate(100, 100)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="0" y="-20" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Step 1: Given</text>
                        <text x="0" y="0" text-anchor="middle" fill="#3498db" font-size="11">U = span{u₁, u₂, ...}</text>
                        <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">Subspace generators</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 180 140 Q 230 120 280 140" fill="none" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 2: Set up equations -->
                    <g transform="translate(320, 100)">
                        <rect x="-70" y="-40" width="140" height="80" fill="#ffe8e8" stroke="#e74c3c" stroke-width="2" rx="10"/>
                        <text x="0" y="-20" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Step 2: Equations</text>
                        <text x="0" y="-5" text-anchor="middle" fill="#e74c3c" font-size="11">x · u₁ = 0</text>
                        <text x="0" y="10" text-anchor="middle" fill="#e74c3c" font-size="11">x · u₂ = 0</text>
                        <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10">Orthogonality conditions</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 410 140 Q 460 120 510 140" fill="none" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Step 3: Solve system -->
                    <g transform="translate(560, 100)">
                        <rect x="-70" y="-40" width="140" height="80" fill="#f0e8ff" stroke="#9b59b6" stroke-width="2" rx="10"/>
                        <text x="0" y="-20" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Step 3: Solve</text>
                        <text x="0" y="-5" text-anchor="middle" fill="#9b59b6" font-size="11">Gaussian elimination</text>
                        <text x="0" y="10" text-anchor="middle" fill="#9b59b6" font-size="11">Parametric solution</text>
                        <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10">Find U⊥ basis</text>
                    </g>
                    
                    <!-- Result box -->
                    <rect x="50" y="200" width="600" height="80" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="15"/>
                    <text x="350" y="225" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">General Algorithm for Computing U⊥</text>
                    <text x="350" y="245" text-anchor="middle" fill="#2c3e50" font-size="12">1. Write U = span{u₁, u₂, ..., uₖ}</text>
                    <text x="350" y="260" text-anchor="middle" fill="#2c3e50" font-size="12">2. Set up the system: x · uᵢ = 0 for i = 1, 2, ..., k</text>
                    <text x="350" y="275" text-anchor="middle" fill="#2c3e50" font-size="12">3. Solve the homogeneous system Ax = 0 where A has rows uᵢ</text>
                </svg>
            </div>

            <h3>Fundamental Relationship</h3>
            
            <div class="info-box">
                <h4>💎 Fundamental Theorem</h4>
                <p>For any subspace $U$ of $\mathbb{R}^n$, we have the beautiful relationship:</p>
                <div style="text-align: center; font-size: 1.2em; margin: 15px 0;">
                    <strong>$\dim(U) + \dim(U^{\perp}) = n$</strong>
                </div>
                <p>This means that the dimensions of a subspace and its orthogonal complement always add up to the dimension of the ambient space!</p>
            </div>

            <div class="warning-box">
                <h4>🔮 Looking Ahead</h4>
                <p>The concept of orthogonal complements sets the stage for one of the most powerful applications in linear algebra: <strong>orthogonal projections</strong>. In the next section, we'll see how to project any vector onto a subspace, solving the fundamental "closest point" problem that appears throughout mathematics and engineering.</p>
            </div>
        </div>

        <!-- Section 5: Orthogonal Projections -->
        <div class="section" id="section5" style="display: none;">
            <h2>5. Orthogonal Projections</h2>
            
            <p>We now come to one of the most beautiful and practical applications of orthogonality: <strong>orthogonal projections</strong>. This concept allows us to solve the fundamental geometric problem of finding the point in a subspace that is closest to a given vector.</p>

            <div class="info-box">
                <h4>🎯 The Closest Point Problem</h4>
                <p>Imagine you have a plane in 3D space and a point outside the plane. What point in the plane is closest to your point? Orthogonal projection gives us the answer: drop a perpendicular from the point to the plane!</p>
            </div>

            <h3>Geometric Motivation</h3>
            
            <p>Consider a point $x$ and a plane $U$ through the origin in $\mathbb{R}^3$. We want to find the point $p$ in the plane that is closest to $x$. Our geometric intuition tells us that $p$ must be chosen so that $x - p$ is perpendicular to the plane.</p>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Orthogonal Projection: Finding the Closest Point</text>
                    
                    <!-- 3D-like representation -->
                    <g transform="translate(300, 200)">
                        <!-- Plane U -->
                        <ellipse cx="0" cy="20" rx="150" ry="80" fill="#3498db" fill-opacity="0.2" stroke="#3498db" stroke-width="2"/>
                        
                        <!-- Point x above the plane -->
                        <circle cx="-40" cy="-60" r="4" fill="#e74c3c"/>
                        <text x="-35" y="-70" fill="#e74c3c" font-size="14" font-weight="bold">x</text>
                        
                        <!-- Projection point p on the plane -->
                        <circle cx="-40" cy="20" r="4" fill="#2c3e50"/>
                        <text x="-35" y="35" fill="#2c3e50" font-size="14" font-weight="bold">p</text>
                        
                        <!-- Perpendicular line from x to p -->
                        <line x1="-40" y1="-60" x2="-40" y2="20" stroke="#e74c3c" stroke-width="3" stroke-dasharray="5,5"/>
                        
                        <!-- Vector from origin to p -->
                        <line x1="0" y1="20" x2="-40" y2="20" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                        
                        <!-- Vector from origin to x -->
                        <line x1="0" y1="20" x2="-40" y2="-60" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        
                        <!-- Right angle indicator -->
                        <path d="M -55 20 L -55 5 L -40 5" fill="none" stroke="#f39c12" stroke-width="2"/>
                        
                        <!-- Some vectors in the plane -->
                        <line x1="0" y1="20" x2="80" y2="0" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead-blue)" opacity="0.7"/>
                        <line x1="0" y1="20" x2="-60" y2="40" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead-blue)" opacity="0.7"/>
                        
                        <!-- Labels -->
                        <text x="85" y="5" fill="#3498db" font-size="12">u₁ ∈ U</text>
                        <text x="-80" y="50" fill="#3498db" font-size="12">u₂ ∈ U</text>
                        <text x="0" y="110" text-anchor="middle" fill="#3498db" font-size="16" font-weight="bold">Subspace U</text>
                        
                        <!-- Distance annotations -->
                        <text x="-60" y="-20" fill="#e74c3c" font-size="12">x - p ⊥ U</text>
                        <text x="-70" y="0" fill="#2c3e50" font-size="12">minimum distance</text>
                    </g>
                    
                    <!-- Key insight -->
                    <rect x="50" y="320" width="500" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                    <text x="300" y="340" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Key Geometric Insight:</text>
                    <text x="300" y="355" text-anchor="middle" fill="#2c3e50" font-size="12">The closest point p in U to x is characterized by: x - p ∈ U⊥</text>
                    <text x="300" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">This means x - p is orthogonal to every vector in U</text>
                </svg>
            </div>

            <h3>The Projection Formula</h3>
            
            <p>Now we can generalize this geometric insight. If $\{f_1, f_2, \ldots, f_m\}$ is an orthogonal basis of subspace $U$, we define the projection of $x$ onto $U$ by:</p>

            <div class="theorem-box">
                <h4>📐 Definition 8.2: Projection onto a Subspace</h4>
                <p>Let $U$ be a subspace of $\mathbb{R}^n$ with orthogonal basis $\{f_1, f_2, \ldots, f_m\}$. If $x$ is in $\mathbb{R}^n$, the vector</p>
                <div class="formula-highlight" style="background: rgba(255,255,255,0.2); margin: 15px 0;">
                    $$\text{proj}_U x = \frac{x \cdot f_1}{\|f_1\|^2} f_1 + \frac{x \cdot f_2}{\|f_2\|^2} f_2 + \cdots + \frac{x \cdot f_m}{\|f_m\|^2} f_m$$
                </div>
                <p style="color: white;">is called the <strong>orthogonal projection</strong> of $x$ on $U$.</p>
            </div>

            <div class="info-box">
                <h4>🔍 Understanding the Formula</h4>
                <p>The projection formula takes each orthogonal basis vector $f_i$ and finds how much of $x$ points in that direction (using the dot product), then adds up all these components. The result is the "shadow" of $x$ on the subspace $U$.</p>
            </div>

            <h3>The Projection Theorem</h3>
            
            <div class="theorem-box">
                <h4>🏆 Theorem 8.1.3: The Projection Theorem</h4>
                <p>If $U$ is a subspace of $\mathbb{R}^n$ and $x$ is in $\mathbb{R}^n$, write $p = \text{proj}_U x$. Then:</p>
                <ol style="color: white; margin-left: 20px;">
                    <li>$p \in U$ and $x - p \in U^{\perp}$</li>
                    <li>$p$ is the vector in $U$ closest to $x$ in the sense that $\|x - p\| < \|x - y\|$ for all $y \in U$, $y \neq p$</li>
                </ol>
            </div>

            <div class="proof-box">
                <h4><strong>Proof of Part (2):</strong></h4>
                <p>Let $y$ be any vector in $U$ with $y \neq p$. We want to show that $\|x - p\| < \|x - y\|$.</p>
                
                <p>Write $x - y = (x - p) + (p - y)$. Note that:</p>
                <ul>
                    <li>$p - y \in U$ (since both $p$ and $y$ are in $U$)</li>
                    <li>$x - p \in U^{\perp}$ (by part 1)</li>
                    <li>Therefore $(x - p) \perp (p - y)$</li>
                </ul>
                
                <p>By the Pythagorean theorem:</p>
                <div class="formula-highlight">
                    $$\|x - y\|^2 = \|x - p\|^2 + \|p - y\|^2 > \|x - p\|^2$$
                </div>
                <p>since $p - y \neq 0$. Taking square roots gives $\|x - y\| > \|x - p\|$. ∎</p>
            </div>

            <h3>Detailed Example: Computing Projections</h3>
            
            <div class="example-box">
                <h4>🔢 Example 8.1.3: Projection onto a Subspace</h4>
                <p>Let $U = \text{span}\{x_1, x_2\}$ in $\mathbb{R}^4$ where $x_1 = (1, 1, 0, 1)$ and $x_2 = (0, 1, 1, 2)$. If $x = (3, -1, 0, 2)$, find the vector in $U$ closest to $x$ and express $x$ as the sum of a vector in $U$ and a vector orthogonal to $U$.</p>
                
                <p><strong>Step 1:</strong> Create an orthogonal basis for $U$ using Gram-Schmidt</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    f_1 &= x_1 = (1, 1, 0, 1) \\
                    f_2 &= x_2 - \frac{x_2 \cdot f_1}{\|f_1\|^2} f_1 \\
                    x_2 \cdot f_1 &= (0, 1, 1, 2) \cdot (1, 1, 0, 1) = 0 + 1 + 0 + 2 = 3 \\
                    \|f_1\|^2 &= 1 + 1 + 0 + 1 = 3 \\
                    f_2 &= (0, 1, 1, 2) - \frac{3}{3}(1, 1, 0, 1) = (0, 1, 1, 2) - (1, 1, 0, 1) = (-1, 0, 1, 1)
                    \end{align}
                </div>
                
                <p><strong>Step 2:</strong> Compute the projection using the orthogonal basis $\{f_1, f_2\}$</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    p &= \text{proj}_U x = \frac{x \cdot f_1}{\|f_1\|^2} f_1 + \frac{x \cdot f_2}{\|f_2\|^2} f_2 \\
                    x \cdot f_1 &= (3, -1, 0, 2) \cdot (1, 1, 0, 1) = 3 - 1 + 0 + 2 = 4 \\
                    x \cdot f_2 &= (3, -1, 0, 2) \cdot (-1, 0, 1, 1) = -3 + 0 + 0 + 2 = -1 \\
                    \|f_2\|^2 &= 1 + 0 + 1 + 1 = 3 \\
                    p &= \frac{4}{3}(1, 1, 0, 1) + \frac{-1}{3}(-1, 0, 1, 1) \\
                    &= \frac{1}{3}[(4, 4, 0, 4) + (1, 0, -1, -1)] \\
                    &= \frac{1}{3}(5, 4, -1, 3)
                    \end{align}
                </div>
                
                <p><strong>Step 3:</strong> Find the orthogonal component</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    $$x - p = (3, -1, 0, 2) - \frac{1}{3}(5, 4, -1, 3) = \frac{1}{3}(4, -7, 1, 3)$$
                </div>
                
                <p><strong>Result:</strong> The decomposition is:</p>
                <p style="text-align: center; color: white;">$x = \frac{1}{3}(5, 4, -1, 3) + \frac{1}{3}(4, -7, 1, 3)$</p>
                <p style="text-align: center; color: white;">where the first term is in $U$ and the second is in $U^{\perp}$.</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Vector Decomposition: x = proj_U(x) + (x - proj_U(x))</text>
                    
                    <!-- Main diagram -->
                    <g transform="translate(350, 200)">
                        <!-- Subspace U (represented as a plane) -->
                        <ellipse cx="0" cy="0" rx="180" ry="100" fill="#3498db" fill-opacity="0.15" stroke="#3498db" stroke-width="2"/>
                        
                        <!-- Original vector x -->
                        <line x1="0" y1="0" x2="-80" y2="-120" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrowhead-red)"/>
                        <text x="-85" y="-130" fill="#e74c3c" font-size="16" font-weight="bold">x</text>
                        
                        <!-- Projection proj_U(x) -->
                        <line x1="0" y1="0" x2="-80" y2="0" stroke="#2c3e50" stroke-width="4" marker-end="url(#arrowhead)"/>
                        <text x="-85" y="15" fill="#2c3e50" font-size="16" font-weight="bold">proj_U(x)</text>
                        
                        <!-- Orthogonal component x - proj_U(x) -->
                        <line x1="-80" y1="0" x2="-80" y2="-120" stroke="#9b59b6" stroke-width="4" marker-end="url(#arrowhead-purple)"/>
                        <text x="-75" y="-60" fill="#9b59b6" font-size="14" font-weight="bold">x - proj_U(x)</text>
                        <text x="-75" y="-45" fill="#9b59b6" font-size="12">∈ U⊥</text>
                        
                        <!-- Right angle indicator -->
                        <path d="M -95 0 L -95 -15 L -80 -15" fill="none" stroke="#f39c12" stroke-width="2"/>
                        
                        <!-- Basis vectors of U -->
                        <line x1="0" y1="0" x2="120" y2="-30" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead-blue)" opacity="0.7"/>
                        <line x1="0" y1="0" x2="-60" y2="80" stroke="#3498db" stroke-width="2" marker-end="url(#arrowhead-blue)" opacity="0.7"/>
                        
                        <!-- Labels -->
                        <text x="125" y="-25" fill="#3498db" font-size="12">f₁</text>
                        <text x="-65" y="85" fill="#3498db" font-size="12">f₂</text>
                        <text x="0" y="130" text-anchor="middle" fill="#3498db" font-size="14" font-weight="bold">Subspace U</text>
                    </g>
                    
                    <!-- Formula breakdown -->
                    <rect x="50" y="320" width="600" height="60" fill="#f8f9fa" stroke="#2c3e50" stroke-width="2" rx="10"/>
                    <text x="350" y="340" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Orthogonal Decomposition Formula:</text>
                    <text x="350" y="360" text-anchor="middle" fill="#2c3e50" font-size="13">x = proj_U(x) + (x - proj_U(x))</text>
                    <text x="350" y="375" text-anchor="middle" fill="#2c3e50" font-size="11">vector in U + vector in U⊥</text>
                </svg>
            </div>

            <h3>Applications: Closest Point Problems</h3>
            
            <div class="example-box">
                <h4>🔢 Example 8.1.4: Finding the Closest Point on a Plane</h4>
                <p>Find the point in the plane with equation $2x + y - z = 0$ that is closest to the point $(2, -1, -3)$.</p>
                
                <p><strong>Solution:</strong> The plane is a subspace $U$ whose points $(x, y, z)$ satisfy $z = 2x + y$. Hence:</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    $$U = \{(s, t, 2s + t) \mid s, t \in \mathbb{R}\} = \text{span}\{(1, 0, 2), (0, 1, 1)\}$$
                </div>
                
                <p><strong>Step 1:</strong> Use Gram-Schmidt to create an orthogonal basis</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    f_1 &= (1, 0, 2) \\
                    f_2 &= (0, 1, 1) - \frac{(0, 1, 1) \cdot (1, 0, 2)}{5}(1, 0, 2) \\
                    &= (0, 1, 1) - \frac{2}{5}(1, 0, 2) = (0, 1, 1) - (\frac{2}{5}, 0, \frac{4}{5}) \\
                    &= (-\frac{2}{5}, 1, \frac{1}{5}) = \frac{1}{5}(-2, 5, 1)
                    \end{align}
                </div>
                
                <p>For simplicity, use $f_2 = (-2, 5, 1)$ (multiplying by 5).</p>
                
                <p><strong>Step 2:</strong> Compute the projection of $x = (2, -1, -3)$</p>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    \begin{align}
                    \text{proj}_U x &= \frac{x \cdot f_1}{\|f_1\|^2} f_1 + \frac{x \cdot f_2}{\|f_2\|^2} f_2 \\
                    x \cdot f_1 &= (2, -1, -3) \cdot (1, 0, 2) = 2 + 0 - 6 = -4 \\
                    x \cdot f_2 &= (2, -1, -3) \cdot (-2, 5, 1) = -4 - 5 - 3 = -12 \\
                    \text{proj}_U x &= \frac{-4}{5}(1, 0, 2) + \frac{-12}{30}(-2, 5, 1) \\
                    &= \frac{-4}{5}(1, 0, 2) + \frac{-2}{5}(-2, 5, 1) \\
                    &= \frac{1}{5}[(-4, 0, -8) + (4, -10, -2)] \\
                    &= \frac{1}{5}(0, -10, -10) = (0, -2, -2)
                    \end{align}
                </div>
                
                <p><strong>Result:</strong> The closest point on the plane to $(2, -1, -3)$ is $(0, -2, -2)$.</p>
            </div>

            <div class="warning-box">
                <h4>🔮 Coming Up</h4>
                <p>We've seen how projection works geometrically and computationally. In the next section, we'll explore projection as a <strong>linear operator</strong> and discover its remarkable properties, including the fundamental relationship between subspaces and their orthogonal complements.</p>
            </div>
        </div>

        <!-- Section 6: Linear Operators and Projections -->
        <div class="section" id="section6" style="display: none;">
            <h2>6. Linear Operators and Projections</h2>
            
            <p>In this section, we take a more abstract view of projection and discover that it's actually a <strong>linear operator</strong>. This perspective reveals deep connections between subspaces and their orthogonal complements, leading to fundamental dimension relationships.</p>

            <div class="info-box">
                <h4>🎯 New Perspective</h4>
                <p>Instead of thinking of projection as just a formula for finding the closest point, we can view it as a function $T: \mathbb{R}^n \to \mathbb{R}^n$ that sends each vector to its projection on a fixed subspace. This function turns out to be linear!</p>
            </div>

            <h3>Projection as a Linear Operator</h3>
            
            <div class="theorem-box">
                <h4>🔧 Theorem 8.1.4: Projection is Linear</h4>
                <p>Let $U$ be a fixed subspace of $\mathbb{R}^n$. Define $T : \mathbb{R}^n \to \mathbb{R}^n$ by $T(x) = \text{proj}_U x$ for all $x$ in $\mathbb{R}^n$. Then:</p>
                <ol style="color: white; margin-left: 20px;">
                    <li>$T$ is a linear operator</li>
                    <li>$\text{im } T = U$ and $\ker T = U^{\perp}$</li>
                    <li>$\dim U + \dim U^{\perp} = n$</li>
                </ol>
            </div>

            <div class="proof-box">
                <h4><strong>Proof:</strong></h4>
                <p>Let $\{f_1, f_2, \ldots, f_m\}$ be an orthonormal basis of $U$. Then for any $x \in \mathbb{R}^n$:</p>
                <div class="formula-highlight">
                    $$T(x) = (x \cdot f_1)f_1 + (x \cdot f_2)f_2 + \cdots + (x \cdot f_m)f_m$$
                </div>
                
                <p><strong>Part (1):</strong> $T$ is linear because for any $x, y \in \mathbb{R}^n$ and scalar $r$:</p>
                <div class="formula-highlight">
                    \begin{align}
                    T(x + y) &= ((x + y) \cdot f_1)f_1 + \cdots + ((x + y) \cdot f_m)f_m \\
                    &= (x \cdot f_1 + y \cdot f_1)f_1 + \cdots + (x \cdot f_m + y \cdot f_m)f_m \\
                    &= T(x) + T(y)
                    \end{align}
                </div>
                <p>Similarly, $T(rx) = rT(x)$.</p>
                
                <p><strong>Part (2):</strong> For the image: $\text{im } T \subseteq U$ since $T(x)$ is a linear combination of vectors in $U$. Conversely, if $x \in U$, then $x = T(x)$ (projection of a vector onto a subspace containing it is the vector itself), so $U \subseteq \text{im } T$. Thus $\text{im } T = U$.</p>
                
                <p>For the kernel: If $x \in U^{\perp}$, then $x \cdot f_i = 0$ for all $i$, so $T(x) = 0$, hence $x \in \ker T$. Conversely, if $T(x) = 0$, then $x - T(x) = x \in U^{\perp}$ by the Projection Theorem.</p>
                
                <p><strong>Part (3):</strong> Follows from the dimension theorem: $\dim(\text{im } T) + \dim(\ker T) = n$. ∎</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Projection as Linear Operator: T(x) = proj_U(x)</text>
                    
                    <!-- Left side: Domain ℝⁿ -->
                    <g transform="translate(150, 200)">
                        <ellipse cx="0" cy="0" rx="100" ry="120" fill="#e8f4fd" stroke="#3498db" stroke-width="2"/>
                        <text x="0" y="-140" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Domain: ℝⁿ</text>
                        
                        <!-- Vectors in domain -->
                        <circle cx="20" cy="-40" r="3" fill="#e74c3c"/>
                        <circle cx="-30" cy="30" r="3" fill="#e74c3c"/>
                        <circle cx="40" cy="60" r="3" fill="#e74c3c"/>
                        
                        <!-- Vector labels -->
                        <text x="25" y="-45" fill="#e74c3c" font-size="12">x₁</text>
                        <text x="-25" y="25" fill="#e74c3c" font-size="12">x₂</text>
                        <text x="45" y="55" fill="#e74c3c" font-size="12">x₃</text>
                        
                        <!-- U⊥ representation -->
                        <line x1="-80" y1="-80" x2="80" y2="80" stroke="#9b59b6" stroke-width="3"/>
                        <text x="-90" y="-70" fill="#9b59b6" font-size="12">U⊥</text>
                    </g>
                    
                    <!-- Arrow representing T -->
                    <path d="M 270 200 Q 350 150 430 200" fill="none" stroke="#2c3e50" stroke-width="4" marker-end="url(#arrowhead)"/>
                    <text x="350" y="165" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">T</text>
                    <text x="350" y="185" text-anchor="middle" fill="#2c3e50" font-size="12">proj_U</text>
                    
                    <!-- Right side: Codomain ℝⁿ with image U -->
                    <g transform="translate(550, 200)">
                        <ellipse cx="0" cy="0" rx="100" ry="120" fill="#f0f8ff" stroke="#2c3e50" stroke-width="2"/>
                        <text x="0" y="-140" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Codomain: ℝⁿ</text>
                        
                        <!-- Image U (subspace) -->
                        <ellipse cx="0" cy="0" rx="80" ry="50" fill="#3498db" fill-opacity="0.3" stroke="#3498db" stroke-width="2"/>
                        <text x="0" y="70" text-anchor="middle" fill="#3498db" font-size="14" font-weight="bold">Image = U</text>
                        
                        <!-- Projected points -->
                        <circle cx="15" cy="0" r="3" fill="#2c3e50"/>
                        <circle cx="-25" cy="10" r="3" fill="#2c3e50"/>
                        <circle cx="35" cy="-15" r="3" fill="#2c3e50"/>
                        
                        <!-- Projection labels -->
                        <text x="20" y="-5" fill="#2c3e50" font-size="12">T(x₁)</text>
                        <text x="-20" y="5" fill="#2c3e50" font-size="12">T(x₂)</text>
                        <text x="40" y="-20" fill="#2c3e50" font-size="12">T(x₃)</text>
                    </g>
                    
                    <!-- Properties box -->
                    <rect x="50" y="320" width="600" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                    <text x="350" y="340" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Key Properties of Projection Operator T:</text>
                    <text x="350" y="355" text-anchor="middle" fill="#2c3e50" font-size="12">• Linear: T(ax + by) = aT(x) + bT(y)  • Image: im(T) = U  • Kernel: ker(T) = U⊥</text>
                    <text x="350" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">• Fundamental relation: dim(U) + dim(U⊥) = n</text>
                </svg>
            </div>

            <h3>The Fundamental Dimension Theorem</h3>
            
            <div class="info-box">
                <h4>💎 The Big Picture</h4>
                <p>Theorem 8.1.4 gives us a beautiful relationship that connects the dimension of any subspace to the dimension of its orthogonal complement. This is one of the most important dimension results in linear algebra!</p>
            </div>

            <div class="example-box">
                <h4>🔢 Example: Dimension Verification</h4>
                <p>Let's verify the dimension theorem with a concrete example:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li>In $\mathbb{R}^4$, let $U = \text{span}\{(1, 1, 0, 0), (0, 1, 1, 0)\}$</li>
                    <li>We can verify that $\dim(U) = 2$</li>
                    <li>From our earlier work, $U^{\perp} = \text{span}\{(1, -1, 1, 0), (0, 0, 0, 1)\}$</li>
                    <li>So $\dim(U^{\perp}) = 2$</li>
                    <li>Indeed: $\dim(U) + \dim(U^{\perp}) = 2 + 2 = 4 = \dim(\mathbb{R}^4)$ ✓</li>
                </ul>
            </div>

            <h3>Special Properties of Projection Operators</h3>
            
            <p>Projection operators have several remarkable properties that make them unique among linear transformations:</p>

            <div class="theorem-box">
                <h4>🌟 Special Properties</h4>
                <p>If $T$ is the projection operator onto subspace $U$, then:</p>
                <ol style="color: white; margin-left: 20px;">
                    <li><strong>Idempotent:</strong> $T^2 = T$ (applying projection twice gives the same result)</li>
                    <li><strong>Self-adjoint:</strong> $T(x) \cdot y = x \cdot T(y)$ for all $x, y$</li>
                    <li><strong>Norm property:</strong> $\|T(x)\| \leq \|x\|$ for all $x$ (projection never increases length)</li>
                </ol>
            </div>

            <div class="proof-box">
                <h4><strong>Proof of Idempotent Property:</strong></h4>
                <p>For any $x \in \mathbb{R}^n$, we have $T(x) \in U$. Since the projection of any vector in $U$ onto $U$ is the vector itself:</p>
                <div class="formula-highlight">
                    $$T^2(x) = T(T(x)) = T(x)$$
                </div>
                <p>This means $T^2 = T$. In other words, once you project a vector, projecting it again doesn't change it. ∎</p>
            </div>

            <h3>Decomposition Theorem</h3>
            
            <div class="theorem-box">
                <h4>🧩 Orthogonal Decomposition Theorem</h4>
                <p>For any subspace $U$ of $\mathbb{R}^n$, every vector $x \in \mathbb{R}^n$ can be uniquely written as:</p>
                <div class="formula-highlight" style="background: rgba(255,255,255,0.2); margin: 15px 0;">
                    $$x = \text{proj}_U x + \text{proj}_{U^{\perp}} x$$
                </div>
                <p style="color: white;">This gives us $\mathbb{R}^n = U \oplus U^{\perp}$ (direct sum decomposition).</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="350" viewBox="0 0 600 350">
                    <!-- Background -->
                    <rect width="600" height="350" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Direct Sum Decomposition: ℝⁿ = U ⊕ U⊥</text>
                    
                    <!-- Main decomposition diagram -->
                    <g transform="translate(300, 175)">
                        <!-- Total space ℝⁿ -->
                        <rect x="-200" y="-120" width="400" height="240" fill="#f0f8ff" stroke="#2c3e50" stroke-width="2" rx="20"/>
                        <text x="0" y="-135" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">ℝⁿ</text>
                        
                        <!-- Subspace U -->
                        <ellipse cx="-50" cy="0" rx="100" ry="60" fill="#3498db" fill-opacity="0.3" stroke="#3498db" stroke-width="2"/>
                        <text x="-50" y="80" text-anchor="middle" fill="#3498db" font-size="14" font-weight="bold">Subspace U</text>
                        
                        <!-- Orthogonal complement U⊥ -->
                        <ellipse cx="80" cy="0" rx="80" ry="90" fill="#9b59b6" fill-opacity="0.3" stroke="#9b59b6" stroke-width="2"/>
                        <text x="80" y="80" text-anchor="middle" fill="#9b59b6" font-size="14" font-weight="bold">U⊥</text>
                        
                        <!-- Sample vector x and its components -->
                        <line x1="0" y1="0" x2="-60" y2="-40" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="0" x2="40" y2="30" stroke="#9b59b6" stroke-width="3" marker-end="url(#arrowhead-purple)"/>
                        <line x1="0" y1="0" x2="-20" y2="-10" stroke="#2c3e50" stroke-width="4" marker-end="url(#arrowhead)"/>
                        
                        <!-- Component lines -->
                        <line x1="-20" y1="-10" x2="-60" y2="-40" stroke="#3498db" stroke-width="3" marker-end="url(#arrowhead-blue)"/>
                        <line x1="-20" y1="-10" x2="40" y2="30" stroke="#9b59b6" stroke-width="2" stroke-dasharray="3,3"/>
                        
                        <!-- Labels -->
                        <text x="-25" y="-15" fill="#2c3e50" font-size="12" font-weight="bold">x</text>
                        <text x="-70" y="-50" fill="#3498db" font-size="12">proj_U(x)</text>
                        <text x="50" y="40" fill="#9b59b6" font-size="12">proj_{U⊥}(x)</text>
                        
                        <!-- Intersection point -->
                        <circle cx="0" cy="0" r="3" fill="#f39c12"/>
                        <text x="5" y="15" fill="#f39c12" font-size="12">0</text>
                    </g>
                    
                    <!-- Key properties -->
                    <rect x="50" y="280" width="500" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                    <text x="300" y="300" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Direct Sum Properties:</text>
                    <text x="300" y="315" text-anchor="middle" fill="#2c3e50" font-size="12">• Every x ∈ ℝⁿ has unique decomposition: x = u + v where u ∈ U, v ∈ U⊥</text>
                    <text x="300" y="330" text-anchor="middle" fill="#2c3e50" font-size="12">• U ∩ U⊥ = {0}  •  dim(ℝⁿ) = dim(U) + dim(U⊥)</text>
                </svg>
            </div>

            <h3>Applications and Connections</h3>
            
            <div class="info-box">
                <h4>🔗 Connections to Matrix Theory</h4>
                <p>The projection operator perspective connects beautifully to matrix theory. If $A$ is an $m \times n$ matrix, then:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li>$\text{null}(A) = (\text{row}(A))^{\perp}$</li>
                    <li>$\text{null}(A^T) = (\text{col}(A))^{\perp}$</li>
                    <li>$\dim(\text{row}(A)) + \dim(\text{null}(A)) = n$</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>🔢 Matrix Connection Example</h4>
                <p>For the matrix $A = \begin{pmatrix} 1 & 2 & 1 \\ 2 & 4 & 2 \end{pmatrix}$:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li>$\text{row}(A) = \text{span}\{(1, 2, 1)\}$ has dimension 1</li>
                    <li>$\text{null}(A) = \text{span}\{(-2, 1, 0), (-1, 0, 1)\}$ has dimension 2</li>
                    <li>Indeed: $1 + 2 = 3 = \dim(\mathbb{R}^3)$ ✓</li>
                </ul>
                <p style="color: white;">The null space consists of all vectors orthogonal to the row space!</p>
            </div>

            <div class="warning-box">
                <h4>🔮 Next Up</h4>
                <p>We've seen how projection operators reveal deep structural relationships between subspaces. In our next section, we'll explore <strong>orthogonal matrices</strong> - special matrices whose columns (or rows) form orthonormal sets. These matrices have remarkable properties and appear throughout applications in computer graphics, data analysis, and quantum mechanics.</p>
            </div>
        </div>

        <!-- Section 7: Orthogonal Matrices -->
        <div class="section" id="section7" style="display: none;">
            <h2>7. Orthogonal Matrices</h2>
            
            <p>We now turn to matrices with a very special property: their columns form an orthonormal set. These <strong>orthogonal matrices</strong> have remarkable properties and appear in countless applications throughout mathematics, physics, and engineering.</p>

            <div class="info-box">
                <h4>🎯 Why Orthogonal Matrices Matter</h4>
                <p>Orthogonal matrices represent transformations that preserve distances and angles - rotations and reflections in space. They're fundamental to computer graphics, data analysis (PCA), signal processing, and quantum mechanics. Understanding them opens doors to many advanced topics!</p>
            </div>

            <h3>Definition and Basic Properties</h3>
            
            <div class="theorem-box">
                <h4>📐 Definition: Orthogonal Matrix</h4>
                <p>An $n \times n$ matrix $Q$ is called <strong>orthogonal</strong> if its columns form an orthonormal set in $\mathbb{R}^n$.</p>
                <p style="color: white; margin-top: 10px;">Equivalently, $Q$ is orthogonal if and only if $Q^T Q = I$ (where $I$ is the identity matrix).</p>
            </div>

            <div class="example-box">
                <h4>🔢 Example 8.1.5: A 2×2 Orthogonal Matrix</h4>
                <p>Consider the matrix $Q = \begin{pmatrix} \cos\theta & -\sin\theta \\ \sin\theta & \cos\theta \end{pmatrix}$. Let's verify it's orthogonal:</p>
                
                <p><strong>Column 1:</strong> $c_1 = (\cos\theta, \sin\theta)$ has $\|c_1\|^2 = \cos^2\theta + \sin^2\theta = 1$ ✓</p>
                <p><strong>Column 2:</strong> $c_2 = (-\sin\theta, \cos\theta)$ has $\|c_2\|^2 = \sin^2\theta + \cos^2\theta = 1$ ✓</p>
                <p><strong>Orthogonality:</strong> $c_1 \cdot c_2 = \cos\theta(-\sin\theta) + \sin\theta(\cos\theta) = 0$ ✓</p>
                
                <p style="color: white;"><strong>Geometric interpretation:</strong> This matrix represents a counterclockwise rotation by angle $\theta$!</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">2×2 Rotation Matrix: Orthogonal Transformation</text>
                    
                    <!-- Coordinate system -->
                    <g transform="translate(300, 200)">
                        <!-- Grid lines -->
                        <defs>
                            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
                            </pattern>
                        </defs>
                        <rect x="-200" y="-150" width="400" height="300" fill="url(#grid)"/>
                        
                        <!-- Axes -->
                        <line x1="-200" y1="0" x2="200" y2="0" stroke="#2c3e50" stroke-width="2"/>
                        <line x1="0" y1="-150" x2="0" y2="150" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Unit circle -->
                        <circle cx="0" cy="0" r="80" fill="none" stroke="#3498db" stroke-width="2" stroke-dasharray="5,5"/>
                        
                        <!-- Original unit vectors -->
                        <line x1="0" y1="0" x2="80" y2="0" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <line x1="0" y1="0" x2="0" y2="-80" stroke="#e74c3c" stroke-width="3" marker-end="url(#arrowhead-red)"/>
                        <text x="85" y="5" fill="#e74c3c" font-size="12" font-weight="bold">e₁</text>
                        <text x="5" y="-85" fill="#e74c3c" font-size="12" font-weight="bold">e₂</text>
                        
                        <!-- Rotated vectors (columns of Q) -->
                        <line x1="0" y1="0" x2="56" y2="-56" stroke="#2c3e50" stroke-width="4" marker-end="url(#arrowhead)"/>
                        <line x1="0" y1="0" x2="56" y2="56" stroke="#2c3e50" stroke-width="4" marker-end="url(#arrowhead)"/>
                        <text x="65" y="-45" fill="#2c3e50" font-size="12" font-weight="bold">q₁</text>
                        <text x="65" y="65" fill="#2c3e50" font-size="12" font-weight="bold">q₂</text>
                        
                        <!-- Angle arc -->
                        <path d="M 60 0 A 60 60 0 0 0 42.4 -42.4" fill="none" stroke="#f39c12" stroke-width="2"/>
                        <text x="70" y="-20" fill="#f39c12" font-size="12">θ = 45°</text>
                        
                        <!-- Right angle indicator between columns -->
                        <path d="M 45 -45 L 50 -40 L 45 -35" fill="none" stroke="#f39c12" stroke-width="2"/>
                        
                        <!-- Matrix representation -->
                        <g transform="translate(-150, 100)">
                            <rect x="-50" y="-30" width="100" height="60" fill="#f8f9fa" stroke="#2c3e50" stroke-width="2" rx="5"/>
                            <text x="0" y="-15" text-anchor="middle" fill="#2c3e50" font-size="12">Q = [q₁ q₂]</text>
                            <text x="0" y="0" text-anchor="middle" fill="#2c3e50" font-size="10">Columns are</text>
                            <text x="0" y="15" text-anchor="middle" fill="#2c3e50" font-size="10">orthonormal</text>
                        </g>
                    </g>
                    
                    <!-- Properties box -->
                    <rect x="50" y="320" width="500" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                    <text x="300" y="340" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold">Key Properties of Rotation Matrices:</text>
                    <text x="300" y="355" text-anchor="middle" fill="#2c3e50" font-size="12">• Preserve distances: ||Qx|| = ||x||  • Preserve angles: (Qx)·(Qy) = x·y</text>
                    <text x="300" y="370" text-anchor="middle" fill="#2c3e50" font-size="12">• Determinant = 1 (orientation-preserving)  • Q⁻¹ = Qᵀ</text>
                </svg>
            </div>

            <h3>Fundamental Properties</h3>
            
            <div class="theorem-box">
                <h4>🏆 Theorem 8.1.5: Properties of Orthogonal Matrices</h4>
                <p>If $Q$ is an orthogonal matrix, then:</p>
                <ol style="color: white; margin-left: 20px;">
                    <li>$Q^{-1} = Q^T$ (the transpose is the inverse)</li>
                    <li>$\det(Q) = \pm 1$ (determinant is ±1)</li>
                    <li>$\|Qx\| = \|x\|$ for all $x$ (preserves lengths)</li>
                    <li>$(Qx) \cdot (Qy) = x \cdot y$ for all $x, y$ (preserves dot products)</li>
                    <li>The rows of $Q$ also form an orthonormal set</li>
                </ol>
            </div>

            <div class="proof-box">
                <h4><strong>Proof of Key Properties:</strong></h4>
                <p><strong>Property (1):</strong> Since $Q^T Q = I$, multiplying both sides on the right by $Q^{-1}$ gives $Q^T = Q^{-1}$.</p>
                
                <p><strong>Property (3):</strong> For any vector $x$:</p>
                <div class="formula-highlight">
                    $$\|Qx\|^2 = (Qx) \cdot (Qx) = (Qx)^T(Qx) = x^T Q^T Q x = x^T I x = x^T x = \|x\|^2$$
                </div>
                <p>Taking square roots gives $\|Qx\| = \|x\|$.</p>
                
                <p><strong>Property (4):</strong> Similarly:</p>
                <div class="formula-highlight">
                    $$(Qx) \cdot (Qy) = (Qx)^T(Qy) = x^T Q^T Q y = x^T y = x \cdot y$$
                </div>
            </div>

            <h3>Types of Orthogonal Matrices</h3>
            
            <p>Orthogonal matrices come in two main types, distinguished by their determinant:</p>

            <div class="example-box">
                <h4>🔄 Rotations (det Q = +1)</h4>
                <p><strong>2D Rotation:</strong> $Q = \begin{pmatrix} \cos\theta & -\sin\theta \\ \sin\theta & \cos\theta \end{pmatrix}$</p>
                <p><strong>3D Rotation about z-axis:</strong> $Q = \begin{pmatrix} \cos\theta & -\sin\theta & 0 \\ \sin\theta & \cos\theta & 0 \\ 0 & 0 & 1 \end{pmatrix}$</p>
                <p style="color: white;">Rotations preserve orientation - they don't "flip" space.</p>
            </div>

            <div class="example-box">
                <h4>🪞 Reflections (det Q = -1)</h4>
                <p><strong>Reflection across x-axis:</strong> $Q = \begin{pmatrix} 1 & 0 \\ 0 & -1 \end{pmatrix}$</p>
                <p><strong>Reflection across line y = x:</strong> $Q = \begin{pmatrix} 0 & 1 \\ 1 & 0 \end{pmatrix}$</p>
                <p style="color: white;">Reflections reverse orientation - they "flip" space.</p>
            </div>

            <h3>Applications of Orthogonal Matrices</h3>
            
            <div class="info-box">
                <h4>🔧 Real-World Applications</h4>
                <ul style="color: white; margin-left: 20px;">
                    <li><strong>Computer Graphics:</strong> Rotating 3D objects, camera transformations</li>
                    <li><strong>Principal Component Analysis (PCA):</strong> Finding optimal data representations</li>
                    <li><strong>Quantum Mechanics:</strong> Unitary transformations preserve probability</li>
                    <li><strong>Signal Processing:</strong> Fourier transforms, wavelets</li>
                    <li><strong>Robotics:</strong> Describing orientations and movements</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>🔢 Example: QR Decomposition</h4>
                <p>Every matrix $A$ can be factored as $A = QR$ where $Q$ is orthogonal and $R$ is upper triangular. This is closely related to the Gram-Schmidt process!</p>
                
                <p>For $A = \begin{pmatrix} 1 & 1 \\ 1 & 0 \\ 0 & 1 \end{pmatrix}$, we can find:</p>
                
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 10px 0;">
                    $$Q = \begin{pmatrix} \frac{1}{\sqrt{2}} & \frac{1}{\sqrt{6}} \\ \frac{1}{\sqrt{2}} & -\frac{1}{\sqrt{6}} \\ 0 & \frac{2}{\sqrt{6}} \end{pmatrix}, \quad R = \begin{pmatrix} \sqrt{2} & \frac{1}{\sqrt{2}} \\ 0 & \frac{\sqrt{6}}{2} \end{pmatrix}$$
                </div>
                
                <p style="color: white;">The columns of $Q$ are the orthonormalized columns of $A$ (via Gram-Schmidt)!</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="350" viewBox="0 0 700 350">
                    <!-- Background -->
                    <rect width="700" height="350" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Gallery of Orthogonal Transformations</text>
                    
                    <!-- Identity -->
                    <g transform="translate(100, 120)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#f0f8ff" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Identity</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">I = [1 0; 0 1]</text>
                        
                        <!-- Grid showing no transformation -->
                        <g transform="translate(0, 15)">
                            <line x1="-20" y1="0" x2="20" y2="0" stroke="#e74c3c" stroke-width="2"/>
                            <line x1="0" y1="-15" x2="0" y2="15" stroke="#e74c3c" stroke-width="2"/>
                            <circle cx="0" cy="0" r="1" fill="#2c3e50"/>
                        </g>
                        <text x="0" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">No change</text>
                    </g>
                    
                    <!-- 90° Rotation -->
                    <g transform="translate(250, 120)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">90° Rotation</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">[0 -1; 1 0]</text>
                        
                        <!-- Rotated grid -->
                        <g transform="translate(0, 15)">
                            <line x1="0" y1="-20" x2="0" y2="20" stroke="#27ae60" stroke-width="2"/>
                            <line x1="-15" y1="0" x2="15" y2="0" stroke="#27ae60" stroke-width="2"/>
                            <circle cx="0" cy="0" r="1" fill="#2c3e50"/>
                        </g>
                        <text x="0" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">det = +1</text>
                    </g>
                    
                    <!-- Reflection -->
                    <g transform="translate(400, 120)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#ffeee8" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">x-axis Reflection</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">[1 0; 0 -1]</text>
                        
                        <!-- Reflected grid -->
                        <g transform="translate(0, 15)">
                            <line x1="-20" y1="0" x2="20" y2="0" stroke="#e74c3c" stroke-width="2"/>
                            <line x1="0" y1="-15" x2="0" y2="15" stroke="#e74c3c" stroke-width="2" stroke-dasharray="3,3"/>
                            <circle cx="0" cy="0" r="1" fill="#2c3e50"/>
                        </g>
                        <text x="0" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">det = -1</text>
                    </g>
                    
                    <!-- House Reflection -->
                    <g transform="translate(550, 120)">
                        <rect x="-60" y="-40" width="120" height="80" fill="#f0e8ff" stroke="#9b59b6" stroke-width="2" rx="5"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Householder</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">I - 2vvᵀ</text>
                        
                        <!-- Householder reflection -->
                        <g transform="translate(0, 15)">
                            <line x1="-14" y1="-14" x2="14" y2="14" stroke="#9b59b6" stroke-width="2"/>
                            <line x1="-14" y1="14" x2="14" y2="-14" stroke="#9b59b6" stroke-width="2" stroke-dasharray="3,3"/>
                            <circle cx="0" cy="0" r="1" fill="#2c3e50"/>
                        </g>
                        <text x="0" y="55" text-anchor="middle" fill="#2c3e50" font-size="10">Reflects across hyperplane</text>
                    </g>
                    
                    <!-- Properties summary -->
                    <rect x="50" y="250" width="600" height="80" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="15"/>
                    <text x="350" y="275" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Universal Properties of All Orthogonal Matrices</text>
                    <text x="350" y="295" text-anchor="middle" fill="#2c3e50" font-size="12">✓ Preserve distances and angles  ✓ QᵀQ = I  ✓ Q⁻¹ = Qᵀ  ✓ |det Q| = 1</text>
                    <text x="350" y="310" text-anchor="middle" fill="#2c3e50" font-size="12">✓ Columns form orthonormal basis  ✓ Rows form orthonormal basis</text>
                    <text x="350" y="325" text-anchor="middle" fill="#2c3e50" font-size="12">Applications: Computer graphics, data analysis, quantum mechanics, signal processing</text>
                </svg>
            </div>

            <h3>Connection to Gram-Schmidt</h3>
            
            <div class="info-box">
                <h4>🔗 The Big Connection</h4>
                <p>The Gram-Schmidt algorithm is essentially a method for converting any matrix into an orthogonal matrix! Given linearly independent columns, Gram-Schmidt produces orthonormal columns - exactly what we need for an orthogonal matrix.</p>
            </div>

            <div class="warning-box">
                <h4>🔮 Final Section</h4>
                <p>We've explored the beautiful world of orthogonal matrices and their applications. In our final section, we'll summarize everything we've learned and explore how these concepts connect to solve real-world problems in data science, engineering, and beyond.</p>
            </div>
        </div>

        <!-- Section 8: Summary and Applications -->
        <div class="section" id="section8" style="display: none;">
            <h2>8. Summary and Real-World Applications</h2>
            
            <p>We've completed our journey through the fundamental concepts of orthogonality and projections! Let's now summarize the key insights and explore how these powerful mathematical tools solve real-world problems across science, engineering, and technology.</p>

            <div class="info-box">
                <h4>🎯 Our Journey</h4>
                <p>From basic orthogonality concepts to advanced applications, we've built a complete understanding of how perpendicularity and projection work in vector spaces. These concepts form the backbone of many modern computational methods!</p>
            </div>

            <h3>Key Concepts Mastered</h3>
            
            <div class="svg-container">
                <svg width="700" height="500" viewBox="0 0 700 500">
                    <!-- Background -->
                    <rect width="700" height="500" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Orthogonality Concepts: The Complete Picture</text>
                    
                    <!-- Central hub -->
                    <g transform="translate(350, 250)">
                        <circle cx="0" cy="0" r="40" fill="#3498db" stroke="#2c3e50" stroke-width="3"/>
                        <text x="0" y="-5" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Orthogonality</text>
                        <text x="0" y="10" text-anchor="middle" fill="white" font-size="12" font-weight="bold">&amp; Projections</text>
                        
                        <!-- Concept 1: Orthogonal Sets -->
                        <g transform="translate(-150, -150)">
                            <rect x="-60" y="-30" width="120" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                            <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Orthogonal Sets</text>
                            <text x="0" y="5" text-anchor="middle" fill="#2c3e50" font-size="10">u·v = 0</text>
                            <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">Linear independence</text>
                        </g>
                        <line x1="-40" y1="-25" x2="-90" y2="-120" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Concept 2: Gram-Schmidt -->
                        <g transform="translate(0, -180)">
                            <rect x="-60" y="-30" width="120" height="60" fill="#fff2e8" stroke="#f39c12" stroke-width="2" rx="10"/>
                            <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Gram-Schmidt</text>
                            <text x="0" y="5" text-anchor="middle" fill="#2c3e50" font-size="10">Algorithm</text>
                            <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">Creates orthonormal bases</text>
                        </g>
                        <line x1="0" y1="-40" x2="0" y2="-150" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Concept 3: Orthogonal Complements -->
                        <g transform="translate(150, -150)">
                            <rect x="-60" y="-30" width="120" height="60" fill="#f0e8ff" stroke="#9b59b6" stroke-width="2" rx="10"/>
                            <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Orthogonal</text>
                            <text x="0" y="5" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Complements U⊥</text>
                            <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">dim(U) + dim(U⊥) = n</text>
                        </g>
                        <line x1="40" y1="-25" x2="90" y2="-120" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Concept 4: Projections -->
                        <g transform="translate(-180, 0)">
                            <rect x="-60" y="-30" width="120" height="60" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="10"/>
                            <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Orthogonal</text>
                            <text x="0" y="5" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Projections</text>
                            <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">Closest point problems</text>
                        </g>
                        <line x1="-40" y1="0" x2="-120" y2="0" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Concept 5: Linear Operators -->
                        <g transform="translate(0, 180)">
                            <rect x="-60" y="-30" width="120" height="60" fill="#ffe8e8" stroke="#e74c3c" stroke-width="2" rx="10"/>
                            <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Linear Operators</text>
                            <text x="0" y="5" text-anchor="middle" fill="#2c3e50" font-size="10">T² = T (idempotent)</text>
                            <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">im(T) = U, ker(T) = U⊥</text>
                        </g>
                        <line x1="0" y1="40" x2="0" y2="150" stroke="#2c3e50" stroke-width="2"/>
                        
                        <!-- Concept 6: Orthogonal Matrices -->
                        <g transform="translate(180, 0)">
                            <rect x="-60" y="-30" width="120" height="60" fill="#f5f0ff" stroke="#8e44ad" stroke-width="2" rx="10"/>
                            <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Orthogonal</text>
                            <text x="0" y="5" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">Matrices</text>
                            <text x="0" y="20" text-anchor="middle" fill="#2c3e50" font-size="10">Q^T Q = I, preserve distances</text>
                        </g>
                        <line x1="40" y1="0" x2="120" y2="0" stroke="#2c3e50" stroke-width="2"/>
                    </g>
                    
                    <!-- Foundation box -->
                    <rect x="50" y="420" width="600" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="15"/>
                    <text x="350" y="445" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Foundation: Vector Dot Products and Geometric Intuition</text>
                    <text x="350" y="465" text-anchor="middle" fill="#2c3e50" font-size="12">All these concepts build on the simple idea that perpendicular vectors have dot product zero</text>
                </svg>
            </div>

            <h3>Major Theorems and Results</h3>
            
            <div class="theorem-box">
                <h4>🏆 The Big Three Theorems</h4>
                <ol style="color: white; margin-left: 20px;">
                    <li><strong>Orthogonal Lemma (8.1.1):</strong> Existence of orthogonal vectors in any extension</li>
                    <li><strong>Gram-Schmidt Algorithm (8.1.2):</strong> Every finite-dimensional space has an orthonormal basis</li>
                    <li><strong>Projection Theorem (8.1.3):</strong> Closest point characterization via orthogonality</li>
                </ol>
                <p style="color: white; margin-top: 15px;"><strong>Fundamental Dimension Relationship:</strong> $\dim(U) + \dim(U^{\perp}) = n$ for any subspace $U \subseteq \mathbb{R}^n$</p>
            </div>

            <h3>Real-World Applications</h3>
            
            <div class="example-box">
                <h4>🚀 Data Science & Machine Learning</h4>
                <p><strong>Principal Component Analysis (PCA):</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Uses orthogonal matrices to find optimal data representations</li>
                    <li>Projection onto principal component subspaces reduces dimensionality</li>
                    <li>Fundamental tool in data compression and feature extraction</li>
                </ul>
                
                <p><strong>Least Squares Regression:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Finds best-fit lines/planes by minimizing orthogonal distances</li>
                    <li>Solution is projection of data onto the column space of design matrix</li>
                    <li>Normal equation: $A^T A \hat{x} = A^T b$ comes from projection theory</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>💻 Computer Graphics & Animation</h4>
                <p><strong>3D Transformations:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Rotation matrices (orthogonal with det = +1) rotate objects in 3D space</li>
                    <li>Camera transformations use orthogonal matrices to change viewpoints</li>
                    <li>Preserving distances and angles maintains realistic appearance</li>
                </ul>
                
                <p><strong>Animation and Interpolation:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Smooth rotation interpolation using quaternions (closely related to orthogonal matrices)</li>
                    <li>Gram-Schmidt process ensures consistent coordinate systems in modeling</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>🔬 Physics & Engineering</h4>
                <p><strong>Quantum Mechanics:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Orthonormal basis states represent possible measurement outcomes</li>
                    <li>Projection operators compute probabilities of measurement results</li>
                    <li>Unitary matrices (complex orthogonal matrices) govern time evolution</li>
                </ul>
                
                <p><strong>Signal Processing:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Fourier transforms use orthogonal basis functions (sines and cosines)</li>
                    <li>QR decomposition enables efficient digital filter design</li>
                    <li>Projection removes noise by filtering onto signal subspaces</li>
                </ul>
            </div>

            <div class="example-box">
                <h4>🤖 Modern AI & Deep Learning</h4>
                <p><strong>Neural Network Optimization:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>Orthogonal weight initialization prevents gradient explosion/vanishing</li>
                    <li>Self-attention mechanisms in transformers use orthogonal projections</li>
                    <li>Batch normalization uses projection to standardize activations</li>
                </ul>
                
                <p><strong>Generative Models:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li>GANs use orthogonal regularization to stabilize training</li>
                    <li>Variational autoencoders project data onto lower-dimensional latent spaces</li>
                </ul>
            </div>

            <h3>Advanced Extensions</h3>
            
            <div class="info-box">
                <h4>🔮 Where to Go Next</h4>
                <p>The concepts you've learned open doors to many advanced topics:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li><strong>Singular Value Decomposition (SVD):</strong> Generalizes orthogonal diagonalization</li>
                    <li><strong>Spectral Theory:</strong> Eigenvalues and eigenvectors of symmetric matrices</li>
                    <li><strong>Functional Analysis:</strong> Orthogonality in infinite-dimensional spaces</li>
                    <li><strong>Differential Geometry:</strong> Orthonormal frames on manifolds</li>
                    <li><strong>Optimization:</strong> Constrained minimization using Lagrange multipliers</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" fill="#2c3e50" font-size="18" font-weight="bold">Applications Timeline: From Theory to Practice</text>
                    
                    <!-- Timeline -->
                    <line x1="100" y1="200" x2="600" y2="200" stroke="#2c3e50" stroke-width="3"/>
                    
                    <!-- Historical Development -->
                    <g transform="translate(150, 200)">
                        <circle cx="0" cy="0" r="8" fill="#3498db"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">1850s</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">Gram-Schmidt</text>
                        <text x="0" y="25" text-anchor="middle" fill="#3498db" font-size="10">Mathematical</text>
                        <text x="0" y="40" text-anchor="middle" fill="#3498db" font-size="10">Foundation</text>
                    </g>
                    
                    <!-- Linear Algebra Applications -->
                    <g transform="translate(250, 200)">
                        <circle cx="0" cy="0" r="8" fill="#27ae60"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">1950s</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">Linear Algebra</text>
                        <text x="0" y="25" text-anchor="middle" fill="#27ae60" font-size="10">Numerical</text>
                        <text x="0" y="40" text-anchor="middle" fill="#27ae60" font-size="10">Computing</text>
                    </g>
                    
                    <!-- Computer Graphics -->
                    <g transform="translate(350, 200)">
                        <circle cx="0" cy="0" r="8" fill="#f39c12"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">1970s</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">Computer</text>
                        <text x="0" y="25" text-anchor="middle" fill="#f39c12" font-size="10">3D Graphics</text>
                        <text x="0" y="40" text-anchor="middle" fill="#f39c12" font-size="10">& Animation</text>
                    </g>
                    
                    <!-- Data Science -->
                    <g transform="translate(450, 200)">
                        <circle cx="0" cy="0" r="8" fill="#9b59b6"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">1990s</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">Data Science</text>
                        <text x="0" y="25" text-anchor="middle" fill="#9b59b6" font-size="10">PCA & ML</text>
                        <text x="0" y="40" text-anchor="middle" fill="#9b59b6" font-size="10">Applications</text>
                    </g>
                    
                    <!-- Modern AI -->
                    <g transform="translate(550, 200)">
                        <circle cx="0" cy="0" r="8" fill="#e74c3c"/>
                        <text x="0" y="-25" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">2010s+</text>
                        <text x="0" y="-10" text-anchor="middle" fill="#2c3e50" font-size="10">Deep Learning</text>
                        <text x="0" y="25" text-anchor="middle" fill="#e74c3c" font-size="10">AI & Neural</text>
                        <text x="0" y="40" text-anchor="middle" fill="#e74c3c" font-size="10">Networks</text>
                    </g>
                    
                    <!-- Impact areas -->
                    <rect x="50" y="280" width="600" height="100" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="15"/>
                    <text x="350" y="305" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold">Modern Impact Areas</text>
                    <text x="200" y="325" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">🎮 Gaming</text>
                    <text x="200" y="340" text-anchor="middle" fill="#2c3e50" font-size="10">Real-time 3D rendering</text>
                    
                    <text x="350" y="325" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">📊 Big Data</text>
                    <text x="350" y="340" text-anchor="middle" fill="#2c3e50" font-size="10">Dimensionality reduction</text>
                    
                    <text x="500" y="325" text-anchor="middle" fill="#2c3e50" font-size="12" font-weight="bold">🤖 AI</text>
                    <text x="500" y="340" text-anchor="middle" fill="#2c3e50" font-size="10">Neural architectures</text>
                    
                    <text x="350" y="360" text-anchor="middle" fill="#2c3e50" font-size="12">🚀 Space Technology  🩺 Medical Imaging  🌐 Internet Search  📱 Computer Vision</text>
                </svg>
            </div>

            <h3>Problem-Solving Framework</h3>
            
            <div class="info-box">
                <h4>🛠️ When to Use These Tools</h4>
                <p><strong>Recognize orthogonality problems by these keywords:</strong></p>
                <ul style="color: white; margin-left: 20px;">
                    <li><strong>"Closest point"</strong> → Use orthogonal projection</li>
                    <li><strong>"Best fit" or "least squares"</strong> → Project onto column space</li>
                    <li><strong>"Independent components"</strong> → Apply Gram-Schmidt</li>
                    <li><strong>"Preserve distances/angles"</strong> → Look for orthogonal matrices</li>
                    <li><strong>"Simplify basis"</strong> → Create orthonormal basis</li>
                </ul>
            </div>

            <div class="success-box" style="background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 20px; border-radius: 15px; margin: 20px 0; border: none;">
                <h4>🎉 Congratulations!</h4>
                <p style="font-size: 1.1em;">You've mastered the fundamental concepts of orthogonality and projections! These tools will serve you throughout advanced mathematics, science, and engineering. You now understand:</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>How to create orthogonal and orthonormal bases using Gram-Schmidt</li>
                    <li>How to find orthogonal complements and compute projections</li>
                    <li>How to recognize and work with orthogonal matrices</li>
                    <li>How these concepts solve real problems in data science, graphics, and AI</li>
                </ul>
                <p style="margin-top: 15px; font-style: italic;">Keep exploring - the world of linear algebra has much more to offer!</p>
            </div>

            <div class="warning-box">
                <h4>🚀 Next Steps in Your Learning Journey</h4>
                <p>Ready to dive deeper? Consider exploring these advanced topics:</p>
                <ul style="color: white; margin-left: 20px;">
                    <li><strong>Singular Value Decomposition (SVD)</strong> - The crown jewel of matrix factorizations</li>
                    <li><strong>Eigenvalue decomposition</strong> - Diagonalization and spectral theory</li>
                    <li><strong>Numerical linear algebra</strong> - How computers actually solve these problems</li>
                    <li><strong>Optimization theory</strong> - Where linear algebra meets calculus</li>
                    <li><strong>Machine learning mathematics</strong> - Deep dives into PCA, neural networks, and more</li>
                </ul>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="nav-button" disabled>← Previous</button>
            <div style="text-align: center;">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p style="margin-top: 10px;">Section 1 of 8</p>
            </div>
            <button class="nav-button" onclick="showNextSection()">Next: Orthogonal Lemma →</button>
        </div>
    </div>

    <script>
        let currentSection = 1;
        const totalSections = 8;

        function showSection(sectionNumber) {
            // Hide all sections
            for (let i = 1; i <= totalSections; i++) {
                const section = document.getElementById(`section${i}`);
                if (section) {
                    section.style.display = 'none';
                }
            }
            
            // Show the requested section
            const targetSection = document.getElementById(`section${sectionNumber}`);
            if (targetSection) {
                targetSection.style.display = 'block';
            }
            
            currentSection = sectionNumber;
            updateProgressBar();
            updateNavigation();
        }

        function showNextSection() {
            if (currentSection < totalSections) {
                showSection(currentSection + 1);
            }
        }

        function showPrevSection() {
            if (currentSection > 1) {
                showSection(currentSection - 1);
            }
        }

        function updateProgressBar() {
            const progressFill = document.querySelector('.progress-fill');
            const progressPercent = (currentSection / totalSections) * 100;
            progressFill.style.width = progressPercent + '%';
            
            // Update section counter
            const sectionCounter = document.querySelector('.navigation p');
            if (sectionCounter) {
                sectionCounter.textContent = `Section ${currentSection} of ${totalSections}`;
            }
        }

        function updateNavigation() {
            const prevButton = document.querySelector('.navigation .nav-button:first-child');
            const nextButton = document.querySelector('.navigation .nav-button:last-child');
            
            // Update previous button
            prevButton.disabled = currentSection === 1;
            prevButton.onclick = currentSection > 1 ? showPrevSection : null;
            
            // Update next button
            nextButton.disabled = currentSection === totalSections;
            nextButton.onclick = currentSection < totalSections ? showNextSection : null;
            
            // Update button text
            if (currentSection < totalSections) {
                const sectionTitles = [
                    'Introduction',
                    'Orthogonal Lemma',
                    'Gram-Schmidt Algorithm',
                    'Orthogonal Complements',
                    'Projections',
                    'Linear Operators',
                    'Orthogonal Matrices',
                    'Summary'
                ];
                nextButton.textContent = `Next: ${sectionTitles[currentSection]} →`;
            }
            
            if (currentSection > 1) {
                const sectionTitles = [
                    'Introduction',
                    'Orthogonal Lemma', 
                    'Gram-Schmidt Algorithm',
                    'Orthogonal Complements',
                    'Projections',
                    'Linear Operators',
                    'Orthogonal Matrices',
                    'Summary'
                ];
                prevButton.textContent = `← Previous: ${sectionTitles[currentSection - 2]}`;
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            showSection(1);
        });
    </script>
</body>
</html> 