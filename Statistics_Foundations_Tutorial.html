<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistics Foundations: Independent Random Variables</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #1565c0;
            --secondary-color: #1976d2;
            --accent-color: #2196f3;
            --success-color: #2e7d32;
            --warning-color: #f57c00;
            --danger-color: #d32f2f;
            --light-bg: #f5f5f5;
            --card-bg: #ffffff;
            --text-primary: #212121;
            --text-secondary: #757575;
            --border-color: #e0e0e0;
            --concentration-color: #4a148c;
            --independence-color: #b71c1c;
            --distribution-color: #006064;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #1565c0 0%, #4a148c 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--concentration-color), var(--independence-color));
        }

        .header h1 {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: var(--text-secondary);
            font-weight: 300;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            padding: 8px 16px;
            background: var(--concentration-color);
            color: white;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
        }

        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border-left: 5px solid var(--primary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .formula-box {
            background: #f8f9fa;
            border: 2px solid var(--accent-color);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);
        }

        .concentration-box {
            background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
            border: 2px solid var(--concentration-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .concentration-box::before {
            content: '🎯';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .independence-box {
            background: linear-gradient(135deg, #ffebee 0%, #ef9a9a 100%);
            border: 2px solid var(--independence-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .independence-box::before {
            content: '🔗';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .distribution-box {
            background: linear-gradient(135deg, #e0f2f1 0%, #80cbc4 100%);
            border: 2px solid var(--distribution-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .distribution-box::before {
            content: '📊';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid var(--success-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .example-box::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .definition-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border: 2px solid var(--warning-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .definition-box::before {
            content: '📚';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 200px;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            text-decoration: none;
            color: var(--text-secondary);
            border-radius: 5px;
            margin: 5px 0;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .nav-link:hover {
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Statistics Foundations</h1>
            <p class="subtitle">Understanding the Behavior of Independent Random Variables</p>
            <div>
                <span class="badge">Random Variables</span>
                <span class="badge">Independence</span>
                <span class="badge">Concentration</span>
                <span class="badge">Distributions</span>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <a href="#introduction" class="nav-link">Introduction</a>
            <a href="#random-variables" class="nav-link">Random Variables</a>
            <a href="#expectations" class="nav-link">Expected Values</a>
            <a href="#independence" class="nav-link">Independence</a>
            <a href="#transformations" class="nav-link">Transformations</a>
            <a href="#distributions" class="nav-link">Distributions</a>
            <a href="#multivariate" class="nav-link">Multivariate Normal</a>
            <a href="#sampling" class="nav-link">Sample Statistics</a>
            <a href="#concentration" class="nav-link">Concentration</a>
            <a href="#summary" class="nav-link">Summary</a>
        </div>

        <!-- Introduction Section -->
        <div class="section" id="introduction">
            <h2><span class="step-number">1</span>The Central Problem in Statistics</h2>
            
            <p>Statistics is fundamentally about understanding the behavior of <strong>sums of independent random variables</strong>. This seemingly simple concept underlies virtually all of statistical theory and practice.</p>

            <div class="concentration-box">
                <h3>The Core Question</h3>
                <p>How do <strong>averages of independent random variables concentrate around their expectation?</strong></p>
                <p>This question drives two major areas of statistical theory:</p>
                <ul>
                    <li><strong>Asymptotic Theory:</strong> What happens as $n \to \infty$? (Laws of Large Numbers, Central Limit Theorems)</li>
                    <li><strong>Non-asymptotic Theory:</strong> What happens for finite $n$? (Concentration inequalities)</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 1000 400">
                    <defs>
                        <linearGradient id="introGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="400" fill="url(#introGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#1565c0" font-size="20" font-weight="bold">The Statistical Universe</text>
                    
                    <!-- Individual random variables -->
                    <g id="random-vars">
                        <circle cx="150" cy="150" r="25" fill="#f57c00" opacity="0.8"/>
                        <text x="150" y="155" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X₁</text>
                        
                        <circle cx="250" cy="120" r="25" fill="#f57c00" opacity="0.8"/>
                        <text x="250" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X₂</text>
                        
                        <circle cx="350" cy="180" r="25" fill="#f57c00" opacity="0.8"/>
                        <text x="350" y="185" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X₃</text>
                        
                        <circle cx="450" cy="140" r="25" fill="#f57c00" opacity="0.8"/>
                        <text x="450" y="145" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Xₙ</text>
                        
                        <text x="300" y="220" text-anchor="middle" fill="#f57c00" font-size="14" font-weight="bold">Independent Random Variables</text>
                    </g>
                    
                    <!-- Arrow pointing to average -->
                    <path d="M 480 150 L 580 200" stroke="#4a148c" stroke-width="4" marker-end="url(#arrowhead)"/>
                    <text x="530" y="170" text-anchor="middle" fill="#4a148c" font-size="12" font-weight="bold">Average</text>
                    
                    <!-- Sample mean -->
                    <rect x="600" y="180" width="120" height="60" fill="#4a148c" opacity="0.8" rx="10"/>
                    <text x="660" y="200" text-anchor="middle" fill="white" font-size="14" font-weight="bold">X̄ₙ</text>
                    <text x="660" y="220" text-anchor="middle" fill="white" font-size="12">= (X₁+...+Xₙ)/n</text>
                    
                    <!-- Concentration -->
                    <circle cx="800" cy="210" r="50" fill="none" stroke="#2e7d32" stroke-width="4"/>
                    <circle cx="800" cy="210" r="8" fill="#2e7d32"/>
                    <text x="800" y="280" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Concentrates around E[X]</text>
                    
                    <!-- Key insight box -->
                    <rect x="100" y="300" width="800" height="80" fill="#ffffff" stroke="#1565c0" stroke-width="2" rx="10"/>
                    <text x="500" y="325" text-anchor="middle" fill="#1565c0" font-size="16" font-weight="bold">Key Insight</text>
                    <text x="500" y="350" text-anchor="middle" fill="#424242" font-size="14">Averages concentrate within O(1/√n) of their expectation</text>
                    <text x="500" y="370" text-anchor="middle" fill="#424242" font-size="14">Independence is the key to this remarkable phenomenon</text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#4a148c"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="independence-box">
                <h3>Why Independence Matters</h3>
                <p>The fundamental intuition behind concentration is simple yet profound:</p>
                <blockquote style="font-style: italic; font-size: 1.1em; border-left: 4px solid var(--independence-color); padding-left: 20px; margin: 20px 0;">
                    "In order to pull an average away from its expectation, many independent random variables need to work together simultaneously, which is extremely unlikely."
                </blockquote>
                <p><strong>Independence is the key.</strong> This seemingly simple fact underlies essentially all of statistics.</p>
            </div>
        </div>

        <!-- Random Variables Section -->
        <div class="section" id="random-variables">
            <h2><span class="step-number">2</span>Random Variables: Foundations</h2>
            
            <p>Before diving into the behavior of sums, we need to establish the fundamental building blocks: random variables and their distributions.</p>

            <div class="definition-box">
                <h3>Basic Definitions</h3>
                <p><strong>Sample Space ($\Omega$):</strong> The set of all possible outcomes</p>
                <p><strong>Probability Measure ($P$):</strong> Assigns probabilities to events</p>
                <p><strong>Random Variable ($X$):</strong> A map $X: \Omega \to \mathbb{R}$</p>
                
                <p>We write $P(X \in A) = P(\{\omega \in \Omega : X(\omega) \in A\})$ and $X \sim P$ to mean that $X$ has distribution $P$.</p>
            </div>

            <div class="formula-box">
                <h3>Cumulative Distribution Function (CDF)</h3>
                <div class="math-display">
                    $$F_X(x) = F(x) = P(X \leq x)$$
                </div>
                <p>Every CDF has three essential properties:</p>
                <ol>
                    <li><strong>Right-continuous:</strong> $F(x) = \lim_{y \downarrow x} F(y)$</li>
                    <li><strong>Non-decreasing:</strong> If $x < y$ then $F(x) \leq F(y)$</li>
                    <li><strong>Normalized:</strong> $\lim_{x \to -\infty} F(x) = 0$ and $\lim_{x \to \infty} F(x) = 1$</li>
                </ol>
            </div>

            <div class="example-box">
                <h3>Discrete vs. Continuous Random Variables</h3>
                
                <p><strong>Discrete Random Variables:</strong></p>
                <p>Probability Mass Function (PMF): $p_X(x) = P(X = x)$</p>
                
                <p><strong>Continuous Random Variables:</strong></p>
                <p>Probability Density Function (PDF): $p_X(x) = F'(x)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$P(X \in A) = \int_A p_X(x) dx$$
                    </div>
                </div>
                
                <p><strong>Equivalence:</strong> The notations $X \sim P$, $X \sim F$, and $X \sim p$ are all equivalent.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="350" viewBox="0 0 900 350">
                    <defs>
                        <linearGradient id="rvGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:0.3" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="350" fill="url(#rvGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#f57c00" font-size="18" font-weight="bold">Random Variable Concept</text>
                    
                    <!-- Sample space -->
                    <ellipse cx="200" cy="180" rx="80" ry="60" fill="#2196f3" opacity="0.3" stroke="#2196f3" stroke-width="2"/>
                    <text x="200" y="120" text-anchor="middle" fill="#2196f3" font-size="14" font-weight="bold">Sample Space Ω</text>
                    
                    <!-- Outcomes -->
                    <circle cx="170" cy="160" r="3" fill="#2196f3"/>
                    <circle cx="200" cy="180" r="3" fill="#2196f3"/>
                    <circle cx="230" cy="200" r="3" fill="#2196f3"/>
                    <text x="170" y="175" text-anchor="middle" fill="#2196f3" font-size="10">ω₁</text>
                    <text x="200" y="195" text-anchor="middle" fill="#2196f3" font-size="10">ω₂</text>
                    <text x="230" y="215" text-anchor="middle" fill="#2196f3" font-size="10">ω₃</text>
                    
                    <!-- Mapping arrow -->
                    <path d="M 280 180 L 380 180" stroke="#673ab7" stroke-width="4" marker-end="url(#arrow2)"/>
                    <text x="330" y="170" text-anchor="middle" fill="#673ab7" font-size="14" font-weight="bold">X(ω)</text>
                    
                    <!-- Real line -->
                    <line x1="420" y1="180" x2="650" y2="180" stroke="#424242" stroke-width="3"/>
                    <text x="535" y="200" text-anchor="middle" fill="#424242" font-size="12">ℝ (Real Numbers)</text>
                    
                    <!-- Values -->
                    <circle cx="460" cy="180" r="4" fill="#e91e63"/>
                    <circle cx="520" cy="180" r="4" fill="#e91e63"/>
                    <circle cx="580" cy="180" r="4" fill="#e91e63"/>
                    <text x="460" y="200" text-anchor="middle" fill="#e91e63" font-size="10">x₁</text>
                    <text x="520" y="200" text-anchor="middle" fill="#e91e63" font-size="10">x₂</text>
                    <text x="580" y="200" text-anchor="middle" fill="#e91e63" font-size="10">x₃</text>
                    
                    <!-- CDF illustration -->
                    <g transform="translate(700, 100)">
                        <rect x="0" y="0" width="150" height="120" fill="#ffffff" stroke="#1565c0" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" fill="#1565c0" font-size="12" font-weight="bold">CDF: F(x)</text>
                        
                        <!-- Simple step function -->
                        <line x1="20" y1="100" x2="50" y2="100" stroke="#1565c0" stroke-width="2"/>
                        <line x1="50" y1="100" x2="50" y2="70" stroke="#1565c0" stroke-width="2"/>
                        <line x1="50" y1="70" x2="80" y2="70" stroke="#1565c0" stroke-width="2"/>
                        <line x1="80" y1="70" x2="80" y2="40" stroke="#1565c0" stroke-width="2"/>
                        <line x1="80" y1="40" x2="130" y2="40" stroke="#1565c0" stroke-width="2"/>
                        
                        <text x="10" y="105" text-anchor="middle" fill="#424242" font-size="8">0</text>
                        <text x="10" y="75" text-anchor="middle" fill="#424242" font-size="8">0.5</text>
                        <text x="10" y="45" text-anchor="middle" fill="#424242" font-size="8">1</text>
                    </g>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#673ab7"/>
                        </marker>
                    </defs>
                    
                    <!-- Bottom explanation -->
                    <text x="450" y="280" text-anchor="middle" fill="#f57c00" font-size="14" font-weight="bold">Random Variable: X(ω) maps outcomes to real numbers</text>
                    <text x="450" y="300" text-anchor="middle" fill="#424242" font-size="12">The CDF F(x) = P(X ≤ x) completely characterizes the distribution</text>
                </svg>
            </div>

            <div class="concentration-box">
                <h3>Equal in Distribution</h3>
                <p>Two random variables $X$ and $Y$ are <strong>equal in distribution</strong> (written $X \stackrel{d}{=} Y$) if:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$P(X \in A) = P(Y \in A) \text{ for all sets } A$$
                    </div>
                </div>
                <p><strong>Equivalently:</strong> $F_X(t) = F_Y(t)$ for all $t$</p>
                                 <p>This means they have the same distribution, even if they're defined on different sample spaces!</p>
             </div>
         </div>

        <!-- Expected Values Section -->
        <div class="section" id="expectations">
            <h2><span class="step-number">3</span>Expected Values and Their Properties</h2>
            
            <p>Expected values are the cornerstone of probability theory. They provide a way to summarize the "center" of a distribution and have remarkable mathematical properties.</p>

            <div class="formula-box">
                <h3>Expected Value Definition</h3>
                <p>The <strong>expected value</strong> or <strong>mean</strong> of $g(X)$ is:</p>
                <div class="math-display">
                    $$E[g(X)] = \int g(x) dF(x) = \begin{cases}
                    \int_{-\infty}^{\infty} g(x) p(x) dx & \text{if } X \text{ is continuous} \\
                    \sum_j g(x_j) p(x_j) & \text{if } X \text{ is discrete}
                    \end{cases}$$
                </div>
                <p>We often write $\mu = E[X]$ for the mean of $X$.</p>
            </div>

            <div class="definition-box">
                <h3>Fundamental Properties of Expectation</h3>
                
                <p><strong>1. Linearity of Expectation:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E\left[\sum_{j=1}^k c_j g_j(X)\right] = \sum_{j=1}^k c_j E[g_j(X)]$$
                    </div>
                </div>
                
                <p><strong>2. Independence Property:</strong></p>
                <p>If $X_1, \ldots, X_n$ are independent, then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E\left[\prod_{i=1}^n X_i\right] = \prod_{i=1}^n E[X_i]$$
                    </div>
                </div>
                
                <p><strong>3. Variance:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\sigma^2 = \text{Var}(X) = E[(X - \mu)^2] = E[X^2] - \mu^2$$
                    </div>
                </div>
                
                <p><strong>4. Variance of Sums (Independence):</strong></p>
                <p>If $X_1, \ldots, X_n$ are independent, then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}\left(\sum_{i=1}^n a_i X_i\right) = \sum_{i=1}^n a_i^2 \text{Var}(X_i)$$
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>Covariance and Correlation</h3>
                
                <p><strong>Covariance:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Cov}(X, Y) = E[(X - \mu_X)(Y - \mu_Y)] = E[XY] - \mu_X \mu_Y$$
                    </div>
                </div>
                
                <p><strong>Correlation:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\rho(X, Y) = \frac{\text{Cov}(X, Y)}{\sigma_X \sigma_Y}$$
                    </div>
                </div>
                <p>Note: $-1 \leq \rho(X, Y) \leq 1$ always.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="300" viewBox="0 0 800 300">
                    <defs>
                        <linearGradient id="expGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="800" height="300" fill="url(#expGradient)" rx="15"/>
                    
                    <text x="400" y="30" text-anchor="middle" fill="#2e7d32" font-size="18" font-weight="bold">Properties of Expectation</text>
                    
                    <!-- Linearity -->
                    <rect x="50" y="60" width="150" height="80" fill="#ffffff" stroke="#2e7d32" stroke-width="2" rx="10"/>
                    <text x="125" y="85" text-anchor="middle" fill="#2e7d32" font-size="12" font-weight="bold">Linearity</text>
                    <text x="125" y="105" text-anchor="middle" fill="#424242" font-size="10">E[aX + bY]</text>
                    <text x="125" y="120" text-anchor="middle" fill="#424242" font-size="10">= aE[X] + bE[Y]</text>
                    
                    <!-- Independence -->
                    <rect x="250" y="60" width="150" height="80" fill="#ffffff" stroke="#673ab7" stroke-width="2" rx="10"/>
                    <text x="325" y="85" text-anchor="middle" fill="#673ab7" font-size="12" font-weight="bold">Independence</text>
                    <text x="325" y="105" text-anchor="middle" fill="#424242" font-size="10">E[XY] = E[X]E[Y]</text>
                    <text x="325" y="120" text-anchor="middle" fill="#424242" font-size="10">if X ⊥ Y</text>
                    
                    <!-- Variance -->
                    <rect x="450" y="60" width="150" height="80" fill="#ffffff" stroke="#e91e63" stroke-width="2" rx="10"/>
                    <text x="525" y="85" text-anchor="middle" fill="#e91e63" font-size="12" font-weight="bold">Variance</text>
                    <text x="525" y="105" text-anchor="middle" fill="#424242" font-size="10">Var(X) = E[X²] - μ²</text>
                    <text x="525" y="120" text-anchor="middle" fill="#424242" font-size="10">Measures spread</text>
                    
                    <!-- MGF -->
                    <rect x="150" y="180" width="200" height="80" fill="#ffffff" stroke="#f57c00" stroke-width="2" rx="10"/>
                    <text x="250" y="205" text-anchor="middle" fill="#f57c00" font-size="12" font-weight="bold">Moment Generating Function</text>
                    <text x="250" y="225" text-anchor="middle" fill="#424242" font-size="10">M_X(t) = E[e^{tX}]</text>
                    <text x="250" y="245" text-anchor="middle" fill="#424242" font-size="10">Determines distribution uniquely</text>
                    
                    <!-- Law of Total Expectation -->
                    <rect x="400" y="180" width="200" height="80" fill="#ffffff" stroke="#1565c0" stroke-width="2" rx="10"/>
                    <text x="500" y="205" text-anchor="middle" fill="#1565c0" font-size="12" font-weight="bold">Law of Total Expectation</text>
                    <text x="500" y="225" text-anchor="middle" fill="#424242" font-size="10">E[Y] = E[E[Y|X]]</text>
                    <text x="500" y="245" text-anchor="middle" fill="#424242" font-size="10">Conditioning technique</text>
                </svg>
            </div>

            <div class="concentration-box">
                <h3>Conditional Expectation and Total Laws</h3>
                
                <p><strong>Conditional Expectation:</strong> $E[Y|X]$ is a random variable whose value when $X = x$ is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E[Y|X = x] = \int y p(y|x) dy$$
                    </div>
                </div>
                
                <p><strong>Law of Total Expectation:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E[Y] = E[E[Y|X]] = \int E[Y|X = x] p_X(x) dx$$
                    </div>
                </div>
                
                <p><strong>Law of Total Variance:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(Y) = \text{Var}(E[Y|X]) + E[\text{Var}(Y|X)]$$
                    </div>
                </div>
            </div>

            <div class="definition-box">
                <h3>Moment Generating Function</h3>
                <p>The <strong>moment generating function (MGF)</strong> is a powerful tool:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$M_X(t) = E[e^{tX}]$$
                    </div>
                </div>
                
                <p><strong>Key properties:</strong></p>
                <ul>
                    <li>If $M_X(t) = M_Y(t)$ for all $t$ in an interval around 0, then $X \stackrel{d}{=} Y$</li>
                    <li>The $n$-th moment: $M_X^{(n)}(0) = E[X^n]$</li>
                    <li>MGFs "multiply" for independent sums: $M_{X+Y}(t) = M_X(t) M_Y(t)$ if $X \perp Y$</li>
                </ul>
            </div>
        </div>

        <!-- Independence Section -->
        <div class="section" id="independence">
            <h2><span class="step-number">4</span>Independence: The Key Concept</h2>
            
            <p>Independence is arguably the most important concept in probability and statistics. It's what makes the concentration phenomenon possible.</p>

            <div class="independence-box">
                <h3>Definition of Independence</h3>
                <p>Random variables $X$ and $Y$ are <strong>independent</strong> if and only if:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$P(X \in A, Y \in B) = P(X \in A) P(Y \in B)$$
                    </div>
                </div>
                <p>for all sets $A$ and $B$.</p>
                
                <p><strong>Equivalent condition:</strong> $p_{X,Y}(x,y) = p_X(x) p_Y(y)$ (when densities exist)</p>
            </div>

            <div class="definition-box">
                <h3>Multiple Random Variables</h3>
                <p>Random variables $X_1, \ldots, X_n$ are <strong>independent</strong> if and only if:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$P(X_1 \in A_1, \ldots, X_n \in A_n) = \prod_{i=1}^n P(X_i \in A_i)$$
                    </div>
                </div>
                
                <p><strong>Joint density factorization:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p_{X_1,\ldots,X_n}(x_1, \ldots, x_n) = \prod_{i=1}^n p_{X_i}(x_i)$$
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>Independent and Identically Distributed (i.i.d.)</h3>
                <p>If $X_1, \ldots, X_n$ are <strong>independent and identically distributed</strong>, we write:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$X_1, \ldots, X_n \stackrel{\text{i.i.d.}}{\sim} P \quad \text{or} \quad X_1, \ldots, X_n \stackrel{\text{i.i.d.}}{\sim} F \quad \text{or} \quad X_1, \ldots, X_n \stackrel{\text{i.i.d.}}{\sim} p$$
                    </div>
                </div>
                <p>This is also called a <strong>random sample</strong> from distribution $P$.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 900 400">
                    <defs>
                        <linearGradient id="indepGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ef9a9a;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="400" fill="url(#indepGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#b71c1c" font-size="18" font-weight="bold">Independence: The Magic Property</text>
                    
                    <!-- Independent case -->
                    <g>
                        <text x="200" y="70" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Independent Variables</text>
                        
                        <!-- X variable -->
                        <circle cx="150" cy="120" r="30" fill="#2196f3" opacity="0.7"/>
                        <text x="150" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X</text>
                        
                        <!-- Y variable -->
                        <circle cx="250" cy="120" r="30" fill="#4caf50" opacity="0.7"/>
                        <text x="250" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Y</text>
                        
                        <!-- No connection -->
                        <text x="200" y="170" text-anchor="middle" fill="#2e7d32" font-size="12">P(X,Y) = P(X)P(Y)</text>
                        <text x="200" y="190" text-anchor="middle" fill="#2e7d32" font-size="12">No connection!</text>
                    </g>
                    
                    <!-- Dependent case -->
                    <g>
                        <text x="650" y="70" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Dependent Variables</text>
                        
                        <!-- X variable -->
                        <circle cx="600" cy="120" r="30" fill="#2196f3" opacity="0.7"/>
                        <text x="600" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">X</text>
                        
                        <!-- Y variable -->
                        <circle cx="700" cy="120" r="30" fill="#4caf50" opacity="0.7"/>
                        <text x="700" y="125" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Y</text>
                        
                        <!-- Connection -->
                        <line x1="630" y1="120" x2="670" y2="120" stroke="#d32f2f" stroke-width="4"/>
                        <text x="650" y="170" text-anchor="middle" fill="#d32f2f" font-size="12">P(X,Y) ≠ P(X)P(Y)</text>
                        <text x="650" y="190" text-anchor="middle" fill="#d32f2f" font-size="12">Connected!</text>
                    </g>
                    
                    <!-- Why independence matters -->
                    <rect x="150" y="230" width="600" height="120" fill="#ffffff" stroke="#b71c1c" stroke-width="3" rx="10"/>
                    <text x="450" y="260" text-anchor="middle" fill="#b71c1c" font-size="16" font-weight="bold">Why Independence Enables Concentration</text>
                    
                    <text x="170" y="285" fill="#424242" font-size="13">• Averages of independent variables have variance that decreases as 1/n</text>
                    <text x="170" y="305" fill="#424242" font-size="13">• Individual "fluctuations" cancel each other out</text>
                    <text x="170" y="325" fill="#424242" font-size="13">• No systematic relationship means no cumulative bias</text>
                    <text x="170" y="345" fill="#424242" font-size="13">• Enables Law of Large Numbers and Central Limit Theorem</text>
                </svg>
            </div>

            <div class="concentration-box">
                <h3>The Power of Independence</h3>
                <p>Independence is what makes statistics work. Here's why:</p>
                
                <p><strong>For the sample mean</strong> $\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i$ of i.i.d. variables:</p>
                
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        E[\bar{X}_n] &= E[X_1] = \mu \\
                        \text{Var}(\bar{X}_n) &= \frac{\text{Var}(X_1)}{n} = \frac{\sigma^2}{n}
                        \end{align}
                    </div>
                </div>
                
                <p><strong>Key insight:</strong> The variance decreases as $1/n$, so the standard deviation decreases as $1/\sqrt{n}$!</p>
                <p>This means the sample mean concentrates in a band of width $O(1/\sqrt{n})$ around the true mean.</p>
            </div>

            <div class="independence-box">
                <h3>Practice Problem: Coin Flipping</h3>
                <p>Consider flipping a fair coin $n$ times. Let $X_i = +1$ if heads, $X_i = -1$ if tails.</p>
                
                <p><strong>Sample mean:</strong> $\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i$</p>
                
                <p><strong>Properties:</strong></p>
                <ul>
                    <li>$E[X_i] = 0$ (fair coin)</li>
                    <li>$\text{Var}(X_i) = 1$ (since $X_i^2 = 1$ always)</li>
                    <li>$E[\bar{X}_n] = 0$</li>
                    <li>$\text{Var}(\bar{X}_n) = 1/n$</li>
                </ul>
                
                <p><strong>Concentration:</strong> With high probability, $|\bar{X}_n| \leq C/\sqrt{n}$ for some constant $C$.</p>
                                 <p>Even though individual flips can give $\pm 1$, the average stays close to 0!</p>
             </div>
         </div>

        <!-- Transformations Section -->
        <div class="section" id="transformations">
            <h2><span class="step-number">5</span>Transformations of Random Variables</h2>
            
            <p>Understanding how random variables behave under transformations is crucial for statistical modeling and analysis.</p>

            <div class="formula-box">
                <h3>Single Variable Transformation</h3>
                <p>Let $Y = g(X)$ where $g: \mathbb{R} \to \mathbb{R}$. Then:</p>
                <div class="math-display">
                    $$F_Y(y) = P(Y \leq y) = P(g(X) \leq y) = \int_{A(y)} p_X(x) dx$$
                </div>
                <p>where $A(y) = \{x : g(x) \leq y\}$</p>
                
                <p><strong>For strictly monotonic functions:</strong></p>
                <div class="math-display">
                    $$p_Y(y) = p_X(h(y)) \left|\frac{dh(y)}{dy}\right|$$
                </div>
                <p>where $h = g^{-1}$ is the inverse function.</p>
            </div>

            <div class="example-box">
                <h3>Example: Exponential to Gumbel</h3>
                <p>Let $X \sim \text{Exponential}(1)$, so $p_X(x) = e^{-x}$ for $x > 0$ and $F_X(x) = 1 - e^{-x}$.</p>
                
                <p>Let $Y = \log(X)$. Find the density of $Y$.</p>
                
                <p><strong>Solution:</strong></p>
                <div class="math-display">
                    \begin{align}
                    F_Y(y) &= P(Y \leq y) = P(\log(X) \leq y) \\
                    &= P(X \leq e^y) = F_X(e^y) = 1 - e^{-e^y}
                    \end{align}
                </div>
                
                <p>Therefore: $p_Y(y) = e^y e^{-e^y}$ for $y \in \mathbb{R}$ (Gumbel distribution)</p>
            </div>

            <div class="definition-box">
                <h3>Multivariate Transformations</h3>
                <p>Let $Z = g(X, Y)$. The general process is:</p>
                <ol>
                    <li>For each $z$, find the set $A_z = \{(x,y) : g(x,y) \leq z\}$</li>
                    <li>Find the CDF: $F_Z(z) = P(Z \leq z) = \iint_{A_z} p_{X,Y}(x,y) \, dx \, dy$</li>
                    <li>The PDF is $p_Z(z) = F_Z'(z)$</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="350" viewBox="0 0 800 350">
                    <defs>
                        <linearGradient id="transGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e0f2f1;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#80cbc4;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="800" height="350" fill="url(#transGradient)" rx="15"/>
                    
                    <text x="400" y="30" text-anchor="middle" fill="#006064" font-size="18" font-weight="bold">Transformation Process</text>
                    
                    <!-- Original distribution -->
                    <g>
                        <text x="150" y="70" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Original X</text>
                        <rect x="100" y="80" width="100" height="60" fill="#ffffff" stroke="#2e7d32" stroke-width="2" rx="5"/>
                        <!-- Simple bell curve -->
                        <path d="M 110 120 Q 150 90 190 120" stroke="#2e7d32" stroke-width="3" fill="none"/>
                        <text x="150" y="160" text-anchor="middle" fill="#2e7d32" font-size="12">p_X(x)</text>
                    </g>
                    
                    <!-- Transformation function -->
                    <g>
                        <text x="400" y="120" text-anchor="middle" fill="#673ab7" font-size="16" font-weight="bold">Y = g(X)</text>
                        <rect x="350" y="140" width="100" height="40" fill="#673ab7" opacity="0.8" rx="5"/>
                        <text x="400" y="165" text-anchor="middle" fill="white" font-size="12">g(x)</text>
                        
                        <!-- Arrows -->
                        <path d="M 220 110 L 330 150" stroke="#673ab7" stroke-width="3" marker-end="url(#arrow3)"/>
                        <path d="M 470 150 L 580 110" stroke="#673ab7" stroke-width="3" marker-end="url(#arrow3)"/>
                    </g>
                    
                    <!-- Transformed distribution -->
                    <g>
                        <text x="650" y="70" text-anchor="middle" fill="#e91e63" font-size="14" font-weight="bold">Transformed Y</text>
                        <rect x="600" y="80" width="100" height="60" fill="#ffffff" stroke="#e91e63" stroke-width="2" rx="5"/>
                        <!-- Different shape -->
                        <path d="M 610 120 Q 620 95 630 105 Q 650 85 670 100 Q 680 110 690 120" stroke="#e91e63" stroke-width="3" fill="none"/>
                        <text x="650" y="160" text-anchor="middle" fill="#e91e63" font-size="12">p_Y(y)</text>
                    </g>
                    
                    <!-- Formula box -->
                    <rect x="200" y="220" width="400" height="100" fill="#ffffff" stroke="#006064" stroke-width="2" rx="10"/>
                    <text x="400" y="245" text-anchor="middle" fill="#006064" font-size="14" font-weight="bold">Transformation Formula</text>
                    <text x="400" y="270" text-anchor="middle" fill="#424242" font-size="12">For monotonic g: p_Y(y) = p_X(h(y)) |h'(y)|</text>
                    <text x="400" y="290" text-anchor="middle" fill="#424242" font-size="12">where h = g⁻¹ is the inverse function</text>
                    <text x="400" y="310" text-anchor="middle" fill="#424242" font-size="12">|h'(y)| is the Jacobian (absolute value)</text>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrow3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#673ab7"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="example-box">
                <h3>Practice Problem: Uniform to Quadratic</h3>
                <p><strong>Problem:</strong> Let $X \sim \text{Uniform}(-1, 2)$ and $Y = X^2$. Find the density of $Y$.</p>
                
                <p><strong>Given:</strong> $p_X(x) = \frac{1}{3}$ for $x \in (-1, 2)$</p>
                
                <p><strong>Solution approach:</strong></p>
                <ol>
                    <li>Find the range of $Y$: Since $X \in (-1, 2)$, we have $Y = X^2 \in [0, 4)$</li>
                    <li>For $y \in [0, 1)$: $P(Y \leq y) = P(X^2 \leq y) = P(-\sqrt{y} \leq X \leq \sqrt{y})$</li>
                    <li>For $y \in [1, 4)$: $P(Y \leq y) = P(X^2 \leq y) = P(-1 \leq X \leq \sqrt{y})$</li>
                    <li>Differentiate to get the density</li>
                </ol>
            </div>
        </div>

        <!-- Important Distributions Section -->
        <div class="section" id="distributions">
            <h2><span class="step-number">6</span>Important Probability Distributions</h2>
            
            <p>Certain distributions appear repeatedly in statistics due to their mathematical properties and real-world applicability.</p>

            <div class="distribution-box">
                <h3>Normal Distribution</h3>
                <p><strong>Univariate:</strong> $X \sim N(\mu, \sigma^2)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p(x) = \frac{1}{\sigma\sqrt{2\pi}} e^{-\frac{(x-\mu)^2}{2\sigma^2}}$$
                    </div>
                </div>
                
                <p><strong>Multivariate:</strong> $\mathbf{X} \sim N(\boldsymbol{\mu}, \boldsymbol{\Sigma})$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p(\mathbf{x}) = \frac{1}{(2\pi)^{d/2}|\boldsymbol{\Sigma}|^{1/2}} \exp\left(-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu})\right)$$
                    </div>
                </div>
            </div>

            <div class="distribution-box">
                <h3>Chi-squared Distribution</h3>
                <p><strong>Central:</strong> $X \sim \chi^2_p$ if $X = \sum_{j=1}^p Z_j^2$ where $Z_j \sim N(0,1)$ independent</p>
                
                <p><strong>Non-central:</strong> $X \sim \chi^2_1(\mu^2)$ if $X = Z^2$ where $Z \sim N(\mu, 1)$</p>
                
                <p><strong>Application:</strong> Used in hypothesis testing and confidence intervals</p>
            </div>

            <div class="distribution-box">
                <h3>Discrete Distributions</h3>
                
                <p><strong>Bernoulli:</strong> $X \sim \text{Bernoulli}(\theta)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p(x) = \theta^x (1-\theta)^{1-x}, \quad x \in \{0,1\}$$
                    </div>
                </div>
                
                <p><strong>Binomial:</strong> $X \sim \text{Binomial}(n, \theta)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p(x) = \binom{n}{x} \theta^x (1-\theta)^{n-x}, \quad x \in \{0,1,\ldots,n\}$$
                    </div>
                </div>
                
                <p><strong>Poisson:</strong> $X \sim \text{Poisson}(\lambda)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p(x) = \frac{e^{-\lambda}\lambda^x}{x!}, \quad x \in \{0,1,2,\ldots\}$$
                    </div>
                </div>
                <p>Note: $E[X] = \text{Var}(X) = \lambda$ and $M_X(t) = e^{\lambda(e^t-1)}$</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="450" viewBox="0 0 1000 450">
                    <defs>
                        <linearGradient id="distGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e0f2f1;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#80cbc4;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="450" fill="url(#distGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#006064" font-size="20" font-weight="bold">Key Distribution Families</text>
                    
                    <!-- Normal Distribution -->
                    <g>
                        <rect x="50" y="60" width="200" height="120" fill="#ffffff" stroke="#1976d2" stroke-width="2" rx="10"/>
                        <text x="150" y="85" text-anchor="middle" fill="#1976d2" font-size="14" font-weight="bold">Normal Distribution</text>
                        <!-- Bell curve -->
                        <path d="M 70 140 Q 100 100 130 120 Q 150 90 170 120 Q 200 100 230 140" stroke="#1976d2" stroke-width="3" fill="none"/>
                        <text x="150" y="160" text-anchor="middle" fill="#424242" font-size="11">X ~ N(μ, σ²)</text>
                        <text x="150" y="175" text-anchor="middle" fill="#424242" font-size="10">Central Limit Theorem</text>
                    </g>
                    
                    <!-- Chi-squared -->
                    <g>
                        <rect x="300" y="60" width="200" height="120" fill="#ffffff" stroke="#388e3c" stroke-width="2" rx="10"/>
                        <text x="400" y="85" text-anchor="middle" fill="#388e3c" font-size="14" font-weight="bold">Chi-squared</text>
                        <!-- Right-skewed curve -->
                        <path d="M 320 150 Q 330 110 350 120 Q 380 100 420 130 Q 460 140 480 150" stroke="#388e3c" stroke-width="3" fill="none"/>
                        <text x="400" y="160" text-anchor="middle" fill="#424242" font-size="11">X ~ χ²_p</text>
                        <text x="400" y="175" text-anchor="middle" fill="#424242" font-size="10">Sum of squares</text>
                    </g>
                    
                    <!-- Binomial -->
                    <g>
                        <rect x="550" y="60" width="200" height="120" fill="#ffffff" stroke="#7b1fa2" stroke-width="2" rx="10"/>
                        <text x="650" y="85" text-anchor="middle" fill="#7b1fa2" font-size="14" font-weight="bold">Binomial</text>
                        <!-- Discrete bars -->
                        <line x1="580" y1="150" x2="580" y2="130" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="600" y1="150" x2="600" y2="110" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="620" y1="150" x2="620" y2="125" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="640" y1="150" x2="640" y2="120" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="660" y1="150" x2="660" y2="125" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="680" y1="150" x2="680" y2="135" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="700" y1="150" x2="700" y2="145" stroke="#7b1fa2" stroke-width="4"/>
                        <line x1="720" y1="150" x2="720" y2="150" stroke="#7b1fa2" stroke-width="4"/>
                        <text x="650" y="160" text-anchor="middle" fill="#424242" font-size="11">X ~ Bin(n, p)</text>
                        <text x="650" y="175" text-anchor="middle" fill="#424242" font-size="10">Count successes</text>
                    </g>
                    
                    <!-- Poisson -->
                    <g>
                        <rect x="800" y="60" width="150" height="120" fill="#ffffff" stroke="#d32f2f" stroke-width="2" rx="10"/>
                        <text x="875" y="85" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Poisson</text>
                        <!-- Poisson-like bars -->
                        <line x1="830" y1="150" x2="830" y2="140" stroke="#d32f2f" stroke-width="4"/>
                        <line x1="845" y1="150" x2="845" y2="125" stroke="#d32f2f" stroke-width="4"/>
                        <line x1="860" y1="150" x2="860" y2="110" stroke="#d32f2f" stroke-width="4"/>
                        <line x1="875" y1="150" x2="875" y2="115" stroke="#d32f2f" stroke-width="4"/>
                        <line x1="890" y1="150" x2="890" y2="130" stroke="#d32f2f" stroke-width="4"/>
                        <line x1="905" y1="150" x2="905" y2="140" stroke="#d32f2f" stroke-width="4"/>
                        <line x1="920" y1="150" x2="920" y2="145" stroke="#d32f2f" stroke-width="4"/>
                        <text x="875" y="160" text-anchor="middle" fill="#424242" font-size="11">X ~ Poi(λ)</text>
                        <text x="875" y="175" text-anchor="middle" fill="#424242" font-size="10">Count events</text>
                    </g>
                    
                    <!-- Exponential/Gamma family -->
                    <g>
                        <rect x="150" y="220" width="250" height="120" fill="#ffffff" stroke="#f57c00" stroke-width="2" rx="10"/>
                        <text x="275" y="245" text-anchor="middle" fill="#f57c00" font-size="14" font-weight="bold">Exponential Family</text>
                        <!-- Exponential decay -->
                        <path d="M 170 300 Q 200 260 230 280 Q 260 290 290 305 Q 320 315 350 325 Q 380 330 380 330" stroke="#f57c00" stroke-width="3" fill="none"/>
                        <text x="275" y="315" text-anchor="middle" fill="#424242" font-size="11">Exponential: X ~ Exp(β)</text>
                        <text x="275" y="330" text-anchor="middle" fill="#424242" font-size="11">Gamma: X ~ Γ(α, β)</text>
                    </g>
                    
                    <!-- Multinomial -->
                    <g>
                        <rect x="450" y="220" width="200" height="120" fill="#ffffff" stroke="#795548" stroke-width="2" rx="10"/>
                        <text x="550" y="245" text-anchor="middle" fill="#795548" font-size="14" font-weight="bold">Multinomial</text>
                        <!-- 3D representation -->
                        <circle cx="520" cy="280" r="20" fill="#795548" opacity="0.3"/>
                        <circle cx="540" cy="270" r="15" fill="#795548" opacity="0.5"/>
                        <circle cx="560" cy="285" r="18" fill="#795548" opacity="0.4"/>
                        <circle cx="580" cy="275" r="12" fill="#795548" opacity="0.6"/>
                        <text x="550" y="315" text-anchor="middle" fill="#424242" font-size="11">X ~ Multinomial(n, p)</text>
                        <text x="550" y="330" text-anchor="middle" fill="#424242" font-size="10">Multiple categories</text>
                    </g>
                    
                    <!-- Properties box -->
                    <rect x="150" y="360" width="700" height="70" fill="#ffffff" stroke="#006064" stroke-width="3" rx="10"/>
                    <text x="500" y="385" text-anchor="middle" fill="#006064" font-size="16" font-weight="bold">Distribution Properties</text>
                    <text x="180" y="405" fill="#424242" font-size="12">• Normal: Central Limit Theorem, linear transformations</text>
                    <text x="180" y="420" fill="#424242" font-size="12">• Chi-squared: Hypothesis testing, confidence intervals</text>
                    <text x="520" y="405" fill="#424242" font-size="12">• Poisson: Rare events, can approximate Binomial</text>
                    <text x="520" y="420" fill="#424242" font-size="12">• Exponential: Waiting times, memoryless property</text>
                </svg>
            </div>

            <div class="example-box">
                <h3>Poisson Addition Property</h3>
                <p>If $X_1 \sim \text{Poisson}(\lambda_1)$ and $X_2 \sim \text{Poisson}(\lambda_2)$ are independent, then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$Y = X_1 + X_2 \sim \text{Poisson}(\lambda_1 + \lambda_2)$$
                    </div>
                </div>
                
                <p><strong>Proof using MGFs:</strong></p>
                <ul>
                    <li>$M_{X_1}(t) = e^{\lambda_1(e^t-1)}$</li>
                    <li>$M_{X_2}(t) = e^{\lambda_2(e^t-1)}$</li>
                    <li>$M_Y(t) = M_{X_1}(t) M_{X_2}(t) = e^{(\lambda_1+\lambda_2)(e^t-1)}$</li>
                </ul>
                <p>This is the MGF of a Poisson($\lambda_1 + \lambda_2$) distribution!</p>
            </div>

            <div class="distribution-box">
                <h3>Uniform Distribution</h3>
                <p><strong>Definition:</strong> $X \sim \text{Uniform}(0, \theta)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$p(x) = \frac{1}{\theta} \mathbf{1}(0 \leq x \leq \theta)$$
                    </div>
                </div>
                                 <p>Properties: $E[X] = \theta/2$, $\text{Var}(X) = \theta^2/12$</p>
             </div>
         </div>

        <!-- Multivariate Normal Section -->
        <div class="section" id="multivariate">
            <h2><span class="step-number">7</span>Multivariate Normal Distribution</h2>
            
            <p>The multivariate normal distribution is central to statistical inference and has elegant mathematical properties.</p>

            <div class="formula-box">
                <h3>Definition and Properties</h3>
                <p>Let $\mathbf{Y} \in \mathbb{R}^d$. Then $\mathbf{Y} \sim N(\boldsymbol{\mu}, \boldsymbol{\Sigma})$ if:</p>
                <div class="math-display">
                    $$p(\mathbf{y}) = \frac{1}{(2\pi)^{d/2}|\boldsymbol{\Sigma}|^{1/2}} \exp\left(-\frac{1}{2}(\mathbf{y} - \boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{y} - \boldsymbol{\mu})\right)$$
                </div>
                
                <p><strong>Basic properties:</strong></p>
                <ul>
                    <li>$E[\mathbf{Y}] = \boldsymbol{\mu}$</li>
                    <li>$\text{Cov}(\mathbf{Y}) = \boldsymbol{\Sigma}$</li>
                    <li>MGF: $M(\mathbf{t}) = \exp(\boldsymbol{\mu}^T\mathbf{t} + \frac{1}{2}\mathbf{t}^T\boldsymbol{\Sigma}\mathbf{t})$</li>
                </ul>
            </div>

            <div class="definition-box">
                <h3>Linear Transformations</h3>
                <p><strong>Scaling:</strong> If $\mathbf{Y} \sim N(\boldsymbol{\mu}, \boldsymbol{\Sigma})$ and $c$ is a scalar, then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$c\mathbf{Y} \sim N(c\boldsymbol{\mu}, c^2\boldsymbol{\Sigma})$$
                    </div>
                </div>
                
                <p><strong>Affine transformations:</strong> If $\mathbf{A}$ is $p \times d$ and $\mathbf{b}$ is $p \times 1$, then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\mathbf{A}\mathbf{Y} + \mathbf{b} \sim N(\mathbf{A}\boldsymbol{\mu} + \mathbf{b}, \mathbf{A}\boldsymbol{\Sigma}\mathbf{A}^T)$$
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>Partitioned Multivariate Normal</h3>
                <p>Suppose $\mathbf{Y} \sim N(\boldsymbol{\mu}, \boldsymbol{\Sigma})$ where:</p>
                <div class="math-display">
                    $$\mathbf{Y} = \begin{pmatrix} \mathbf{Y}_1 \\ \mathbf{Y}_2 \end{pmatrix}, \quad 
                    \boldsymbol{\mu} = \begin{pmatrix} \boldsymbol{\mu}_1 \\ \boldsymbol{\mu}_2 \end{pmatrix}, \quad 
                    \boldsymbol{\Sigma} = \begin{pmatrix} \boldsymbol{\Sigma}_{11} & \boldsymbol{\Sigma}_{12} \\ \boldsymbol{\Sigma}_{21} & \boldsymbol{\Sigma}_{22} \end{pmatrix}$$
                </div>
                
                <p><strong>Key results:</strong></p>
                <ul>
                    <li>$\mathbf{Y}_1 \sim N(\boldsymbol{\mu}_1, \boldsymbol{\Sigma}_{11})$ and $\mathbf{Y}_2 \sim N(\boldsymbol{\mu}_2, \boldsymbol{\Sigma}_{22})$</li>
                    <li>$\mathbf{Y}_1$ and $\mathbf{Y}_2$ are independent if and only if $\boldsymbol{\Sigma}_{12} = \mathbf{0}$</li>
                    <li>If $\boldsymbol{\Sigma}_{22} > 0$, then: 
                        $$\mathbf{Y}_1|\mathbf{Y}_2 \sim N(\boldsymbol{\mu}_1 + \boldsymbol{\Sigma}_{12}\boldsymbol{\Sigma}_{22}^{-1}(\mathbf{Y}_2 - \boldsymbol{\mu}_2), \boldsymbol{\Sigma}_{11} - \boldsymbol{\Sigma}_{12}\boldsymbol{\Sigma}_{22}^{-1}\boldsymbol{\Sigma}_{21})$$
                    </li>
                </ul>
            </div>

            <div class="concentration-box">
                <h3>Quadratic Forms and Chi-squared</h3>
                <p>For $\mathbf{Y} \sim N(\boldsymbol{\mu}, \boldsymbol{\Sigma})$:</p>
                
                <p><strong>General quadratic form:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\mathbf{Y}^T\boldsymbol{\Sigma}^{-1}\mathbf{Y} \sim \chi^2_d\left(\boldsymbol{\mu}^T\boldsymbol{\Sigma}^{-1}\boldsymbol{\mu}\right)$$
                    </div>
                </div>
                
                <p><strong>Centered quadratic form:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$(\mathbf{Y} - \boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{Y} - \boldsymbol{\mu}) \sim \chi^2_d(0)$$
                    </div>
                </div>
                
                <p>This connects multivariate normal to chi-squared distributions!</p>
            </div>
        </div>

        <!-- Sample Mean and Variance Section -->
        <div class="section" id="sampling">
            <h2><span class="step-number">8</span>Sample Mean and Variance</h2>
            
            <p>Sample statistics are the bridge between theoretical distributions and real data analysis.</p>

            <div class="definition-box">
                <h3>Sample Statistics</h3>
                <p>Let $X_1, \ldots, X_n \stackrel{\text{i.i.d.}}{\sim} P$ with mean $\mu$ and variance $\sigma^2$.</p>
                
                <p><strong>Sample Mean:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i$$
                    </div>
                </div>
                
                <p><strong>Sample Variance:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$S_n^2 = \frac{1}{n-1}\sum_{i=1}^n (X_i - \bar{X}_n)^2$$
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>Properties of Sample Statistics</h3>
                <p>For i.i.d. random variables $X_1, \ldots, X_n$ with $E[X_i] = \mu$ and $\text{Var}(X_i) = \sigma^2$:</p>
                
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        E[\bar{X}_n] &= \mu \\
                        \text{Var}(\bar{X}_n) &= \frac{\sigma^2}{n} \\
                        E[S_n^2] &= \sigma^2
                        \end{align}
                    </div>
                </div>
                
                <p><strong>Key insight:</strong> The sample mean is an <em>unbiased</em> estimator of $\mu$, and its variance decreases as $1/n$!</p>
            </div>

            <div class="distribution-box">
                <h3>Normal Sample Results</h3>
                <p>If $X_1, \ldots, X_n \stackrel{\text{i.i.d.}}{\sim} N(\mu, \sigma^2)$, then:</p>
                
                <p><strong>(a) Sample mean distribution:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\bar{X}_n \sim N\left(\mu, \frac{\sigma^2}{n}\right)$$
                    </div>
                </div>
                
                <p><strong>(b) Sample variance distribution:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\frac{(n-1)S_n^2}{\sigma^2} \sim \chi^2_{n-1}$$
                    </div>
                </div>
                
                <p><strong>(c) Independence:</strong> $\bar{X}_n$ and $S_n^2$ are independent!</p>
                
                <p>This remarkable independence property is special to the normal distribution and is crucial for t-tests and F-tests.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 900 400">
                    <defs>
                        <linearGradient id="samplingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="400" fill="url(#samplingGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#4a148c" font-size="18" font-weight="bold">Sample Mean Behavior</text>
                    
                    <!-- Population -->
                    <g>
                        <text x="150" y="70" text-anchor="middle" fill="#1976d2" font-size="14" font-weight="bold">Population</text>
                        <ellipse cx="150" cy="120" rx="60" ry="40" fill="#1976d2" opacity="0.3" stroke="#1976d2" stroke-width="2"/>
                        <text x="150" y="125" text-anchor="middle" fill="#1976d2" font-size="12">μ, σ²</text>
                        <text x="150" y="180" text-anchor="middle" fill="#1976d2" font-size="11">Unknown parameters</text>
                    </g>
                    
                    <!-- Sampling -->
                    <g>
                        <text x="450" y="70" text-anchor="middle" fill="#388e3c" font-size="14" font-weight="bold">Random Sample</text>
                        
                        <!-- Individual samples -->
                        <circle cx="400" cy="110" r="15" fill="#388e3c" opacity="0.6"/>
                        <text x="400" y="115" text-anchor="middle" fill="white" font-size="10">X₁</text>
                        
                        <circle cx="430" cy="110" r="15" fill="#388e3c" opacity="0.6"/>
                        <text x="430" y="115" text-anchor="middle" fill="white" font-size="10">X₂</text>
                        
                        <circle cx="460" cy="110" r="15" fill="#388e3c" opacity="0.6"/>
                        <text x="460" y="115" text-anchor="middle" fill="white" font-size="10">X₃</text>
                        
                        <circle cx="490" cy="110" r="15" fill="#388e3c" opacity="0.6"/>
                        <text x="490" y="115" text-anchor="middle" fill="white" font-size="10">Xₙ</text>
                        
                        <text x="450" y="150" text-anchor="middle" fill="#388e3c" font-size="11">X₁, X₂, ..., Xₙ i.i.d.</text>
                    </g>
                    
                    <!-- Sample statistics -->
                    <g>
                        <text x="750" y="70" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Sample Statistics</text>
                        
                        <!-- Sample mean -->
                        <rect x="700" y="90" width="100" height="40" fill="#d32f2f" opacity="0.8" rx="5"/>
                        <text x="750" y="105" text-anchor="middle" fill="white" font-size="12">X̄ₙ</text>
                        <text x="750" y="120" text-anchor="middle" fill="white" font-size="10">Sample Mean</text>
                        
                        <!-- Sample variance -->
                        <rect x="700" y="140" width="100" height="40" fill="#d32f2f" opacity="0.8" rx="5"/>
                        <text x="750" y="155" text-anchor="middle" fill="white" font-size="12">S²ₙ</text>
                        <text x="750" y="170" text-anchor="middle" fill="white" font-size="10">Sample Variance</text>
                    </g>
                    
                    <!-- Arrows -->
                    <path d="M 210 120 L 380 120" stroke="#673ab7" stroke-width="3" marker-end="url(#arrow4)"/>
                    <path d="M 510 120 L 680 120" stroke="#673ab7" stroke-width="3" marker-end="url(#arrow4)"/>
                    
                    <!-- Key properties box -->
                    <rect x="150" y="220" width="600" height="150" fill="#ffffff" stroke="#4a148c" stroke-width="3" rx="10"/>
                    <text x="450" y="250" text-anchor="middle" fill="#4a148c" font-size="16" font-weight="bold">Key Properties</text>
                    
                    <text x="170" y="275" fill="#424242" font-size="13">• Sample mean: E[X̄ₙ] = μ, Var(X̄ₙ) = σ²/n</text>
                    <text x="170" y="295" fill="#424242" font-size="13">• Sample variance: E[S²ₙ] = σ² (unbiased estimator)</text>
                    <text x="170" y="315" fill="#424242" font-size="13">• For normal populations: X̄ₙ ~ N(μ, σ²/n)</text>
                    <text x="170" y="335" fill="#424242" font-size="13">• Concentration: X̄ₙ concentrates around μ as n → ∞</text>
                    <text x="170" y="355" fill="#424242" font-size="13">• Standard error of mean: σ/√n (decreases with sample size!)</text>
                    
                    <defs>
                        <marker id="arrow4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#673ab7"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <!-- Concentration Section -->
        <div class="section" id="concentration">
            <h2><span class="step-number">9</span>Concentration of Measure</h2>
            
            <p>We now return to our central theme: understanding how averages of independent random variables concentrate around their expectation.</p>

            <div class="concentration-box">
                <h3>The Coin Flipping Example Revisited</h3>
                <p>Consider the simple experiment from the lecture:</p>
                <ul>
                    <li>Toss a fair coin $n$ times</li>
                    <li>$X_i = +1$ if heads, $X_i = -1$ if tails</li>
                    <li>Sample mean: $\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i$</li>
                </ul>
                
                <p><strong>Properties:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        E[X_i] &= 0 \quad \text{(fair coin)} \\
                        \text{Var}(X_i) &= 1 \quad \text{(since } X_i^2 = 1 \text{ always)} \\
                        E[\bar{X}_n] &= 0 \\
                        \text{Var}(\bar{X}_n) &= \frac{1}{n}
                        \end{align}
                    </div>
                </div>
            </div>

            <div class="independence-box">
                <h3>The Concentration Phenomenon</h3>
                <p>The remarkable fact is that even though each individual coin flip gives $\pm 1$, the average $\bar{X}_n$ concentrates very close to 0!</p>
                
                <p><strong>Intuition:</strong> For the average to be far from 0, we would need many more heads than tails (or vice versa). But with independent fair coins, such extreme imbalances become exponentially unlikely as $n$ increases.</p>
                
                <div class="formula-box">
                    <h4>The General Principle</h4>
                    <div class="math-display">
                        $$\text{The average of } n \text{ i.i.d. random variables concentrates within } O(1/\sqrt{n}) \text{ of the mean}$$
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="500" viewBox="0 0 1000 500">
                    <defs>
                        <linearGradient id="concentrationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ce93d8;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="500" fill="url(#concentrationGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#4a148c" font-size="20" font-weight="bold">Concentration Phenomenon Visualization</text>
                    
                    <!-- Individual variables (wide spread) -->
                    <g>
                        <text x="200" y="70" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Individual Variables</text>
                        <text x="200" y="90" text-anchor="middle" fill="#d32f2f" font-size="12">X₁, X₂, ..., Xₙ</text>
                        
                        <!-- Wide distribution -->
                        <ellipse cx="200" cy="140" rx="80" ry="30" fill="none" stroke="#d32f2f" stroke-width="3"/>
                        <text x="200" y="145" text-anchor="middle" fill="#d32f2f" font-size="11">Wide spread</text>
                        <text x="200" y="185" text-anchor="middle" fill="#424242" font-size="10">Var(Xᵢ) = σ²</text>
                    </g>
                    
                    <!-- Sample mean (narrow concentration) -->
                    <g>
                        <text x="600" y="70" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Sample Mean</text>
                        <text x="600" y="90" text-anchor="middle" fill="#2e7d32" font-size="12">X̄ₙ = (X₁ + ... + Xₙ)/n</text>
                        
                        <!-- Narrow distribution -->
                        <ellipse cx="600" cy="140" rx="20" ry="30" fill="none" stroke="#2e7d32" stroke-width="3"/>
                        <text x="600" y="145" text-anchor="middle" fill="#2e7d32" font-size="11">Concentrated!</text>
                        <text x="600" y="185" text-anchor="middle" fill="#424242" font-size="10">Var(X̄ₙ) = σ²/n</text>
                    </g>
                    
                    <!-- Arrow showing concentration -->
                    <path d="M 280 140 L 520 140" stroke="#673ab7" stroke-width="5" marker-end="url(#arrow5)"/>
                    <text x="400" y="125" text-anchor="middle" fill="#673ab7" font-size="12" font-weight="bold">Concentration!</text>
                    <text x="400" y="160" text-anchor="middle" fill="#673ab7" font-size="11">Width shrinks as 1/√n</text>
                    
                    <!-- Sample size effect -->
                    <g>
                        <text x="500" y="230" text-anchor="middle" fill="#1976d2" font-size="16" font-weight="bold">Effect of Sample Size</text>
                        
                        <!-- n = 10 -->
                        <g>
                            <text x="200" y="260" text-anchor="middle" fill="#f57c00" font-size="12">n = 10</text>
                            <ellipse cx="200" cy="280" rx="30" ry="15" fill="#f57c00" opacity="0.3" stroke="#f57c00" stroke-width="2"/>
                            <text x="200" y="285" text-anchor="middle" fill="#f57c00" font-size="10">σ/√10</text>
                        </g>
                        
                        <!-- n = 100 -->
                        <g>
                            <text x="400" y="260" text-anchor="middle" fill="#388e3c" font-size="12">n = 100</text>
                            <ellipse cx="400" cy="280" rx="15" ry="15" fill="#388e3c" opacity="0.3" stroke="#388e3c" stroke-width="2"/>
                            <text x="400" y="285" text-anchor="middle" fill="#388e3c" font-size="10">σ/√100</text>
                        </g>
                        
                        <!-- n = 1000 -->
                        <g>
                            <text x="600" y="260" text-anchor="middle" fill="#1976d2" font-size="12">n = 1000</text>
                            <ellipse cx="600" cy="280" rx="8" ry="15" fill="#1976d2" opacity="0.3" stroke="#1976d2" stroke-width="2"/>
                            <text x="600" y="285" text-anchor="middle" fill="#1976d2" font-size="10">σ/√1000</text>
                        </g>
                        
                        <!-- n = 10000 -->
                        <g>
                            <text x="800" y="260" text-anchor="middle" fill="#4a148c" font-size="12">n = 10000</text>
                            <ellipse cx="800" cy="280" rx="4" ry="15" fill="#4a148c" opacity="0.3" stroke="#4a148c" stroke-width="2"/>
                            <text x="800" y="285" text-anchor="middle" fill="#4a148c" font-size="10">σ/√10000</text>
                        </g>
                    </g>
                    
                    <!-- Key insight box -->
                    <rect x="150" y="330" width="700" height="140" fill="#ffffff" stroke="#4a148c" stroke-width="3" rx="15"/>
                    <text x="500" y="360" text-anchor="middle" fill="#4a148c" font-size="18" font-weight="bold">The Fundamental Insight</text>
                    
                    <text x="170" y="385" fill="#424242" font-size="14">"In order to pull an average away from its expectation, many independent</text>
                    <text x="170" y="405" fill="#424242" font-size="14">random variables need to work together simultaneously, which is extremely unlikely."</text>
                    
                    <text x="170" y="435" fill="#4a148c" font-size="16" font-weight="bold">Independence is the key. This underlies essentially all of statistics.</text>
                    
                    <text x="170" y="460" fill="#424242" font-size="12">• Law of Large Numbers: X̄ₙ → μ as n → ∞</text>
                    <text x="400" y="460" fill="#424242" font-size="12">• Central Limit Theorem: √n(X̄ₙ - μ) → N(0, σ²)</text>
                    
                    <defs>
                        <marker id="arrow5" markerWidth="15" markerHeight="10" refX="14" refY="5" orient="auto">
                            <polygon points="0 0, 15 5, 0 10" fill="#673ab7"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="definition-box">
                <h3>Looking Ahead: Laws of Large Numbers and CLT</h3>
                <p>The concentration phenomenon leads to some of the most important results in probability theory:</p>
                
                <p><strong>Weak Law of Large Numbers:</strong></p>
                <p>For any $\epsilon > 0$, $P(|\bar{X}_n - \mu| > \epsilon) \to 0$ as $n \to \infty$</p>
                
                <p><strong>Central Limit Theorem:</strong></p>
                <p>$\sqrt{n}(\bar{X}_n - \mu) \stackrel{d}{\to} N(0, \sigma^2)$ as $n \to \infty$</p>
                
                <p><strong>Concentration Inequalities:</strong></p>
                <p>Give finite-sample bounds like $P(|\bar{X}_n - \mu| > t) \leq 2e^{-nt^2/(2\sigma^2)}$</p>
                
                <p>These results will be covered in detail in subsequent lectures!</p>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="section" id="summary">
            <h2><span class="step-number">10</span>Summary and Key Insights</h2>
            
            <p>We've covered the foundational concepts needed to understand the behavior of sums of independent random variables. Let's summarize the key insights.</p>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="600" viewBox="0 0 1000 600">
                    <defs>
                        <linearGradient id="summaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="600" fill="url(#summaryGradient)" rx="15"/>
                    
                    <text x="500" y="30" text-anchor="middle" fill="#1565c0" font-size="22" font-weight="bold">Statistics Foundations: The Big Picture</text>
                    
                    <!-- Core concepts grid -->
                    <!-- Random Variables -->
                    <rect x="50" y="70" width="200" height="120" fill="#ffffff" stroke="#2196f3" stroke-width="3" rx="10"/>
                    <text x="150" y="95" text-anchor="middle" fill="#2196f3" font-size="14" font-weight="bold">Random Variables</text>
                    <text x="70" y="115" fill="#424242" font-size="11">• Maps: Ω → ℝ</text>
                    <text x="70" y="130" fill="#424242" font-size="11">• CDF: F(x) = P(X ≤ x)</text>
                    <text x="70" y="145" fill="#424242" font-size="11">• PDF/PMF</text>
                    <text x="70" y="160" fill="#424242" font-size="11">• Transformations</text>
                    <text x="70" y="175" fill="#424242" font-size="11">• Equal in distribution</text>
                    
                    <!-- Expected Values -->
                    <rect x="300" y="70" width="200" height="120" fill="#ffffff" stroke="#4caf50" stroke-width="3" rx="10"/>
                    <text x="400" y="95" text-anchor="middle" fill="#4caf50" font-size="14" font-weight="bold">Expected Values</text>
                    <text x="320" y="115" fill="#424242" font-size="11">• E[g(X)]</text>
                    <text x="320" y="130" fill="#424242" font-size="11">• Linearity</text>
                    <text x="320" y="145" fill="#424242" font-size="11">• Variance: E[X²] - μ²</text>
                    <text x="320" y="160" fill="#424242" font-size="11">• MGF: E[e^{tX}]</text>
                    <text x="320" y="175" fill="#424242" font-size="11">• Conditional expectation</text>
                    
                    <!-- Independence -->
                    <rect x="550" y="70" width="200" height="120" fill="#ffffff" stroke="#ff5722" stroke-width="3" rx="10"/>
                    <text x="650" y="95" text-anchor="middle" fill="#ff5722" font-size="14" font-weight="bold">Independence</text>
                    <text x="570" y="115" fill="#424242" font-size="11">• P(X,Y) = P(X)P(Y)</text>
                    <text x="570" y="130" fill="#424242" font-size="11">• Joint density factorizes</text>
                    <text x="570" y="145" fill="#424242" font-size="11">• E[XY] = E[X]E[Y]</text>
                    <text x="570" y="160" fill="#424242" font-size="11">• Var(X+Y) = Var(X)+Var(Y)</text>
                    <text x="570" y="175" fill="#424242" font-size="11">• i.i.d. samples</text>
                    
                    <!-- Distributions -->
                    <rect x="800" y="70" width="150" height="120" fill="#ffffff" stroke="#9c27b0" stroke-width="3" rx="10"/>
                    <text x="875" y="95" text-anchor="middle" fill="#9c27b0" font-size="14" font-weight="bold">Distributions</text>
                    <text x="820" y="115" fill="#424242" font-size="11">• Normal</text>
                    <text x="820" y="130" fill="#424242" font-size="11">• Chi-squared</text>
                    <text x="820" y="145" fill="#424242" font-size="11">• Binomial/Poisson</text>
                    <text x="820" y="160" fill="#424242" font-size="11">• Exponential/Gamma</text>
                    <text x="820" y="175" fill="#424242" font-size="11">• Multinomial</text>
                    
                    <!-- Sample Statistics -->
                    <rect x="50" y="230" width="200" height="120" fill="#ffffff" stroke="#795548" stroke-width="3" rx="10"/>
                    <text x="150" y="255" text-anchor="middle" fill="#795548" font-size="14" font-weight="bold">Sample Statistics</text>
                    <text x="70" y="275" fill="#424242" font-size="11">• Sample mean: X̄ₙ</text>
                    <text x="70" y="290" fill="#424242" font-size="11">• Sample variance: S²ₙ</text>
                    <text x="70" y="305" fill="#424242" font-size="11">• E[X̄ₙ] = μ</text>
                    <text x="70" y="320" fill="#424242" font-size="11">• Var(X̄ₙ) = σ²/n</text>
                    <text x="70" y="335" fill="#424242" font-size="11">• Unbiased estimators</text>
                    
                    <!-- Multivariate Normal -->
                    <rect x="300" y="230" width="200" height="120" fill="#ffffff" stroke="#607d8b" stroke-width="3" rx="10"/>
                    <text x="400" y="255" text-anchor="middle" fill="#607d8b" font-size="14" font-weight="bold">Multivariate Normal</text>
                    <text x="320" y="275" fill="#424242" font-size="11">• Y ~ N(μ, Σ)</text>
                    <text x="320" y="290" fill="#424242" font-size="11">• Linear transformations</text>
                    <text x="320" y="305" fill="#424242" font-size="11">• Conditional distributions</text>
                    <text x="320" y="320" fill="#424242" font-size="11">• Quadratic forms</text>
                    <text x="320" y="335" fill="#424242" font-size="11">• Independence ⟺ Σ₁₂ = 0</text>
                    
                    <!-- Concentration -->
                    <rect x="550" y="230" width="400" height="120" fill="#ffffff" stroke="#e91e63" stroke-width="4" rx="10"/>
                    <text x="750" y="255" text-anchor="middle" fill="#e91e63" font-size="16" font-weight="bold">🎯 CONCENTRATION PHENOMENON</text>
                    <text x="570" y="280" fill="#424242" font-size="13">• Averages of independent variables concentrate around expectation</text>
                    <text x="570" y="295" fill="#424242" font-size="13">• Width of concentration: O(1/√n)</text>
                    <text x="570" y="310" fill="#424242" font-size="13">• Independence is the key to this phenomenon</text>
                    <text x="570" y="325" fill="#424242" font-size="13">• Leads to: LLN, CLT, concentration inequalities</text>
                    <text x="570" y="340" fill="#424242" font-size="13">• Foundation of all statistical inference!</text>
                    
                    <!-- Central message -->
                    <rect x="100" y="380" width="800" height="180" fill="#1565c0" opacity="0.1" stroke="#1565c0" stroke-width="3" rx="15"/>
                    <text x="500" y="410" text-anchor="middle" fill="#1565c0" font-size="20" font-weight="bold">The Central Message</text>
                    
                    <text x="500" y="440" text-anchor="middle" fill="#424242" font-size="16" font-weight="bold">"Averages of independent random variables concentrate around their expectation"</text>
                    
                    <text x="120" y="470" fill="#424242" font-size="14">This fundamental principle explains why:</text>
                    <text x="140" y="490" fill="#424242" font-size="13">• Statistical inference is possible (sample statistics approximate population parameters)</text>
                    <text x="140" y="505" fill="#424242" font-size="13">• Larger samples give more accurate estimates (standard error ∝ 1/√n)</text>
                    <text x="140" y="520" fill="#424242" font-size="13">• Independence assumptions are crucial in statistical models</text>
                    <text x="140" y="535" fill="#424242" font-size="13">• We can quantify uncertainty using probability distributions</text>
                    <text x="140" y="550" fill="#424242" font-size="13">• The normal distribution plays a central role (Central Limit Theorem)</text>
                </svg>
            </div>

            <div class="concentration-box">
                <h3>Looking Forward</h3>
                <p>This lecture has established the foundational concepts. In upcoming lectures, we will explore:</p>
                <ul>
                    <li><strong>Laws of Large Numbers:</strong> Formal statements and proofs of convergence results</li>
                    <li><strong>Central Limit Theorems:</strong> Distributional convergence to normality</li>
                    <li><strong>Concentration Inequalities:</strong> Finite-sample bounds on deviations</li>
                    <li><strong>Statistical Applications:</strong> Confidence intervals, hypothesis testing, estimation theory</li>
                </ul>
            </div>

            <div class="independence-box">
                <h3>Key Takeaways</h3>
                <ol>
                    <li><strong>Independence is fundamental</strong> - it enables the concentration phenomenon that makes statistics possible</li>
                    <li><strong>Sample means concentrate</strong> - with rate $O(1/\sqrt{n})$ around the population mean</li>
                    <li><strong>Mathematical rigor matters</strong> - proper definitions of random variables, expectations, and independence are essential</li>
                    <li><strong>Distributions have structure</strong> - families like normal, exponential, and chi-squared appear repeatedly</li>
                    <li><strong>Linear algebra is useful</strong> - multivariate normal distributions connect to quadratic forms and independence</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0; padding: 30px; background: linear-gradient(135deg, #1565c0 0%, #4a148c 100%); border-radius: 15px;">
                <h3 style="color: white; margin-bottom: 15px;">Thank you for following this tutorial!</h3>
                <p style="color: #e3f2fd; font-size: 1.1em;">You now have the foundational knowledge to understand the remarkable behavior of sums of independent random variables and why this principle underlies essentially all of statistical theory and practice.</p>
            </div>
        </div>
    </div>
</body>
</html>
