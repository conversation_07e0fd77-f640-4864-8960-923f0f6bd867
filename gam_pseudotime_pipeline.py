"""
GAM-Based Pseudotime Lag Detection Pipeline

This module implements a comprehensive pipeline for detecting lag relationships
between genes in pseudotime using Generalized Additive Models (GAMs).

Key Features:
- GAM fitting with Poisson/Negative Binomial distributions
- Lag parameter optimization
- Statistical significance testing
- Confidence interval estimation

Author: AI Assistant
Date: 2024
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize, differential_evolution
from scipy.stats import poisson, nbinom
from sklearn.metrics import mean_squared_error, r2_score
import warnings
from typing import Tuple, Dict, Optional, List, Callable
import matplotlib.pyplot as plt
from pygam import PoissonGAM, LinearGAM, s, l
from pygam.utils import generate_X_grid
import seaborn as sns

warnings.filterwarnings('ignore')

class GAMPseudotimeLagDetector:
    """
    A comprehensive class for detecting lag relationships between genes in pseudotime
    using Generalized Additive Models.
    """
    
    def __init__(self, n_splines: int = 10, lam: float = 0.6, distribution: str = 'poisson'):
        """
        Initialize the GAM Pseudotime Lag Detector.
        
        Parameters:
        -----------
        n_splines : int
            Number of splines for GAM fitting
        lam : float
            Smoothing parameter for GAM
        distribution : str
            Distribution family ('poisson', 'normal', 'nb')
        """
        self.n_splines = n_splines
        self.lam = lam
        self.distribution = distribution
        self.reference_gam = None
        self.reference_gene_data = None
        self.pseudotime = None
        self.optimization_results = {}
        
    def fit_reference_curve(self, pseudotime: np.ndarray, gene_expression: np.ndarray, 
                          gene_name: str = "Reference Gene") -> 'GAMPseudotimeLagDetector':
        """
        Fit GAM to reference gene to establish the shape function f(x).
        
        Parameters:
        -----------
        pseudotime : np.ndarray
            Pseudotime values
        gene_expression : np.ndarray
            Gene expression counts for reference gene
        gene_name : str
            Name of the reference gene
            
        Returns:
        --------
        self : GAMPseudotimeLagDetector
            Returns self for method chaining
        """
        # Store data for later use
        self.pseudotime = pseudotime.copy()
        self.reference_gene_data = gene_expression.copy()
        self.reference_gene_name = gene_name
        
        # Choose appropriate GAM based on distribution
        if self.distribution == 'poisson':
            self.reference_gam = PoissonGAM(s(0, n_splines=self.n_splines, lam=self.lam))
        elif self.distribution == 'normal':
            self.reference_gam = LinearGAM(s(0, n_splines=self.n_splines, lam=self.lam))
        else:
            raise ValueError(f"Distribution '{self.distribution}' not supported")
        
        # Fit the GAM
        self.reference_gam.fit(pseudotime.reshape(-1, 1), gene_expression)
        
        # Calculate fit statistics
        predictions = self.reference_gam.predict(pseudotime.reshape(-1, 1))
        self.reference_fit_stats = {
            'deviance': self.reference_gam.statistics_['deviance'],
            'aic': self.reference_gam.statistics_['AIC'],
            'r_squared': r2_score(gene_expression, predictions),
            'mse': mean_squared_error(gene_expression, predictions)
        }
        
        print(f"Reference curve fitted for {gene_name}")
        print(f"AIC: {self.reference_fit_stats['aic']:.2f}")
        print(f"R²: {self.reference_fit_stats['r_squared']:.3f}")
        
        return self
    
    def _objective_function(self, params: np.ndarray, target_expression: np.ndarray) -> float:
        """
        Objective function for lag optimization.
        
        Parameters:
        -----------
        params : np.ndarray
            Parameters [lag_d, scaling_a, offset_b]
        target_expression : np.ndarray
            Target gene expression data
            
        Returns:
        --------
        float
            Sum of squared errors
        """
        lag_d, scaling_a, offset_b = params
        
        # Calculate shifted pseudotime
        shifted_pseudotime = self.pseudotime + lag_d
        
        # Predict from reference curve at shifted positions
        try:
            # Handle extrapolation by clipping to valid range
            min_time, max_time = self.pseudotime.min(), self.pseudotime.max()
            clipped_time = np.clip(shifted_pseudotime, min_time, max_time)
            
            base_predictions = self.reference_gam.predict(clipped_time.reshape(-1, 1))
            
            # Apply scaling and offset transformation
            final_predictions = scaling_a * base_predictions + offset_b
            
            # Ensure non-negative predictions for count data
            if self.distribution == 'poisson':
                final_predictions = np.maximum(final_predictions, 0.001)
            
            # Calculate sum of squared errors
            sse = np.sum((target_expression - final_predictions) ** 2)
            
            return sse
            
        except Exception as e:
            # Return large error for invalid parameters
            return 1e10
    
    def optimize_lag(self, target_expression: np.ndarray, 
                    target_gene_name: str = "Target Gene",
                    method: str = 'differential_evolution',
                    bounds: Optional[List[Tuple[float, float]]] = None) -> Dict:
        """
        Optimize lag parameters for target gene.
        
        Parameters:
        -----------
        target_expression : np.ndarray
            Target gene expression data
        target_gene_name : str
            Name of target gene
        method : str
            Optimization method ('differential_evolution', 'minimize')
        bounds : List[Tuple[float, float]]
            Parameter bounds [(d_min, d_max), (a_min, a_max), (b_min, b_max)]
            
        Returns:
        --------
        Dict
            Optimization results including lag estimate and fit statistics
        """
        if self.reference_gam is None:
            raise ValueError("Must fit reference curve first using fit_reference_curve()")
        
        # Set default bounds if not provided
        if bounds is None:
            time_range = self.pseudotime.max() - self.pseudotime.min()
            bounds = [
                (-time_range * 0.5, time_range * 0.5),  # lag bounds
                (0.001, 10.0),                          # scaling bounds  
                (-np.max(target_expression), np.max(target_expression))  # offset bounds
            ]
        
        # Choose optimization method
        if method == 'differential_evolution':
            result = differential_evolution(
                self._objective_function,
                bounds=bounds,
                args=(target_expression,),
                seed=42,
                maxiter=1000,
                atol=1e-6
            )
        elif method == 'minimize':
            initial_guess = [0.0, 1.0, 0.0]  # [lag, scaling, offset]
            result = minimize(
                self._objective_function,
                initial_guess,
                args=(target_expression,),
                method='L-BFGS-B',
                bounds=bounds
            )
        else:
            raise ValueError(f"Method '{method}' not supported")
        
        # Extract optimized parameters
        optimal_lag, optimal_scaling, optimal_offset = result.x
        
        # Calculate fitted values and statistics
        shifted_time = self.pseudotime + optimal_lag
        min_time, max_time = self.pseudotime.min(), self.pseudotime.max()
        clipped_time = np.clip(shifted_time, min_time, max_time)
        
        base_predictions = self.reference_gam.predict(clipped_time.reshape(-1, 1))
        fitted_values = optimal_scaling * base_predictions + optimal_offset
        
        if self.distribution == 'poisson':
            fitted_values = np.maximum(fitted_values, 0.001)
        
        # Calculate fit statistics
        sse = result.fun
        mse = sse / len(target_expression)
        r_squared = r2_score(target_expression, fitted_values)
        
        # Store results
        optimization_result = {
            'target_gene': target_gene_name,
            'optimal_lag': optimal_lag,
            'optimal_scaling': optimal_scaling,
            'optimal_offset': optimal_offset,
            'sse': sse,
            'mse': mse,
            'r_squared': r_squared,
            'fitted_values': fitted_values,
            'optimization_success': result.success,
            'optimization_message': result.message if hasattr(result, 'message') else 'Success',
            'n_evaluations': result.nfev if hasattr(result, 'nfev') else result.nit
        }
        
        self.optimization_results[target_gene_name] = optimization_result
        
        print(f"\nLag optimization completed for {target_gene_name}")
        print(f"Estimated lag (d): {optimal_lag:.4f}")
        print(f"Scaling factor (a): {optimal_scaling:.4f}")
        print(f"Offset (b): {optimal_offset:.4f}")
        print(f"R²: {r_squared:.3f}")
        print(f"MSE: {mse:.3f}")
        
        return optimization_result
    
    def permutation_test(self, target_expression: np.ndarray, 
                        n_permutations: int = 1000,
                        target_gene_name: str = "Target Gene") -> Dict:
        """
        Perform permutation test for lag significance.
        
        Parameters:
        -----------
        target_expression : np.ndarray
            Target gene expression data
        n_permutations : int
            Number of permutations for testing
        target_gene_name : str
            Name of target gene
            
        Returns:
        --------
        Dict
            Permutation test results including p-value
        """
        if target_gene_name not in self.optimization_results:
            raise ValueError(f"Must optimize lag for {target_gene_name} first")
        
        # Get original optimization result
        original_result = self.optimization_results[target_gene_name]
        original_sse = original_result['sse']
        
        # Perform permutations
        permuted_sses = []
        
        print(f"Running permutation test with {n_permutations} permutations...")
        
        for i in range(n_permutations):
            if (i + 1) % 100 == 0:
                print(f"Completed {i + 1}/{n_permutations} permutations")
            
            # Permute target expression
            permuted_expression = np.random.permutation(target_expression)
            
            # Optimize with permuted data
            try:
                time_range = self.pseudotime.max() - self.pseudotime.min()
                bounds = [
                    (-time_range * 0.5, time_range * 0.5),
                    (0.001, 10.0),
                    (-np.max(permuted_expression), np.max(permuted_expression))
                ]
                
                result = differential_evolution(
                    self._objective_function,
                    bounds=bounds,
                    args=(permuted_expression,),
                    seed=i,
                    maxiter=300,  # Reduced for speed
                    atol=1e-4
                )
                
                permuted_sses.append(result.fun)
                
            except:
                # Handle optimization failures
                permuted_sses.append(1e10)
        
        # Calculate p-value
        permuted_sses = np.array(permuted_sses)
        p_value = np.mean(permuted_sses <= original_sse)
        
        # Calculate additional statistics
        permutation_results = {
            'p_value': p_value,
            'original_sse': original_sse,
            'permuted_sses': permuted_sses,
            'permuted_mean': np.mean(permuted_sses),
            'permuted_std': np.std(permuted_sses),
            'n_permutations': n_permutations,
            'significant': p_value < 0.05
        }
        
        # Update optimization results
        self.optimization_results[target_gene_name]['permutation_test'] = permutation_results
        
        print(f"\nPermutation test completed for {target_gene_name}")
        print(f"P-value: {p_value:.4f}")
        print(f"Significant at α=0.05: {permutation_results['significant']}")
        
        return permutation_results
    
    def bootstrap_confidence_intervals(self, target_expression: np.ndarray,
                                     n_bootstrap: int = 500,
                                     confidence_level: float = 0.95,
                                     target_gene_name: str = "Target Gene") -> Dict:
        """
        Calculate bootstrap confidence intervals for lag estimate.
        
        Parameters:
        -----------
        target_expression : np.ndarray
            Target gene expression data
        n_bootstrap : int
            Number of bootstrap samples
        confidence_level : float
            Confidence level (default 0.95 for 95% CI)
        target_gene_name : str
            Name of target gene
            
        Returns:
        --------
        Dict
            Bootstrap confidence interval results
        """
        n_samples = len(target_expression)
        bootstrap_lags = []
        bootstrap_scalings = []
        bootstrap_offsets = []
        
        print(f"Computing bootstrap confidence intervals with {n_bootstrap} samples...")
        
        for i in range(n_bootstrap):
            if (i + 1) % 50 == 0:
                print(f"Completed {i + 1}/{n_bootstrap} bootstrap samples")
            
            # Bootstrap sample with replacement
            bootstrap_indices = np.random.choice(n_samples, size=n_samples, replace=True)
            bootstrap_pseudotime = self.pseudotime[bootstrap_indices]
            bootstrap_expression = target_expression[bootstrap_indices]
            
            # Temporarily update data for optimization
            original_pseudotime = self.pseudotime.copy()
            self.pseudotime = bootstrap_pseudotime
            
            try:
                # Optimize with bootstrap sample
                time_range = bootstrap_pseudotime.max() - bootstrap_pseudotime.min()
                bounds = [
                    (-time_range * 0.5, time_range * 0.5),
                    (0.001, 10.0),
                    (-np.max(bootstrap_expression), np.max(bootstrap_expression))
                ]
                
                result = differential_evolution(
                    self._objective_function,
                    bounds=bounds,
                    args=(bootstrap_expression,),
                    seed=i,
                    maxiter=300,
                    atol=1e-4
                )
                
                if result.success:
                    bootstrap_lags.append(result.x[0])
                    bootstrap_scalings.append(result.x[1])
                    bootstrap_offsets.append(result.x[2])
                    
            except:
                pass  # Skip failed optimizations
            
            # Restore original pseudotime
            self.pseudotime = original_pseudotime
        
        # Calculate confidence intervals
        alpha = 1 - confidence_level
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100
        
        if len(bootstrap_lags) > 0:
            lag_ci = np.percentile(bootstrap_lags, [lower_percentile, upper_percentile])
            scaling_ci = np.percentile(bootstrap_scalings, [lower_percentile, upper_percentile])
            offset_ci = np.percentile(bootstrap_offsets, [lower_percentile, upper_percentile])
            
            bootstrap_results = {
                'lag_confidence_interval': lag_ci,
                'scaling_confidence_interval': scaling_ci,
                'offset_confidence_interval': offset_ci,
                'bootstrap_lags': np.array(bootstrap_lags),
                'bootstrap_scalings': np.array(bootstrap_scalings),
                'bootstrap_offsets': np.array(bootstrap_offsets),
                'confidence_level': confidence_level,
                'n_successful_bootstrap': len(bootstrap_lags),
                'n_bootstrap_total': n_bootstrap
            }
            
            # Update optimization results
            if target_gene_name in self.optimization_results:
                self.optimization_results[target_gene_name]['bootstrap_ci'] = bootstrap_results
            
            print(f"\nBootstrap confidence intervals completed for {target_gene_name}")
            print(f"Lag {confidence_level*100}% CI: [{lag_ci[0]:.4f}, {lag_ci[1]:.4f}]")
            print(f"Successful bootstrap samples: {len(bootstrap_lags)}/{n_bootstrap}")
            
            return bootstrap_results
        else:
            print("Bootstrap confidence interval calculation failed - no successful optimizations")
            return {}
    
    def get_summary_statistics(self, target_gene_name: str = None) -> pd.DataFrame:
        """
        Get summary statistics for optimization results.
        
        Parameters:
        -----------
        target_gene_name : str, optional
            Specific target gene name, or None for all genes
            
        Returns:
        --------
        pd.DataFrame
            Summary statistics table
        """
        if not self.optimization_results:
            return pd.DataFrame()
        
        if target_gene_name:
            genes_to_summarize = [target_gene_name]
        else:
            genes_to_summarize = list(self.optimization_results.keys())
        
        summary_data = []
        
        for gene in genes_to_summarize:
            if gene not in self.optimization_results:
                continue
                
            result = self.optimization_results[gene]
            
            # Basic statistics
            row = {
                'Gene': gene,
                'Lag_Estimate': result['optimal_lag'],
                'Scaling_Factor': result['optimal_scaling'],
                'Offset': result['optimal_offset'],
                'R_Squared': result['r_squared'],
                'MSE': result['mse'],
                'SSE': result['sse']
            }
            
            # Add permutation test results if available
            if 'permutation_test' in result:
                perm_test = result['permutation_test']
                row['P_Value'] = perm_test['p_value']
                row['Significant'] = perm_test['significant']
            
            # Add bootstrap confidence intervals if available
            if 'bootstrap_ci' in result:
                boot_ci = result['bootstrap_ci']
                lag_ci = boot_ci['lag_confidence_interval']
                row['Lag_CI_Lower'] = lag_ci[0]
                row['Lag_CI_Upper'] = lag_ci[1]
            
            summary_data.append(row)
        
        return pd.DataFrame(summary_data) 