4.2.1 Uniform Distribution
 We have already seen the uniform distribution. In particular, we have the following
 definition:
 A continuous random variable 
X
 is said to have a Uniform distribution over the
 interval 
[a,b]
 , shown as 
X ∼ Uniform(a,b)
 , if its PDF is given by
 1
 fX(x) =
 {
 b−a
 0
 a <x<b
 x <a or x >b
 We have already found the CDF and the expected value of the uniform distribution. In
 particular, we know that if 
X ∼ Uniform(a,b)
 , then its CDF is given by 
example 4.1, and its mean is given by
 EX=
 a+b
 2
 To find the variance, we can find 
EX2
 using LOTUS:
 
EX2 =∫∞
 −∞x2fX(x)dx
 =∫b
 a x2 
( )dx
 1
 b−a
 a2+ab+b2
 3
 =
 .
 Therefore,
 Var(X) = EX2 −(EX)2
 =
 .
 (b−a)2
 12
 equation 4.1 in
4.2.2 Exponential Distribution
 The exponential distribution is one of the widely used continuous distributions. It is
 often used to model the time elapsed between events. We will now mathematically
 define the exponential distribution, and derive its mean and expected value. Then we
 will develop the intuition for the distribution and discuss several interesting properties
 that it has.
 A continuous random variable 
X
 is said to have an exponential distribution with
 parameter 
λ > 0
 , shown as 
X ∼ Exponential(λ)
 , if its PDF is given by
 fX(x) =
 {λe−λx
 0
 x >0
 otherwise
 Figure 4.5 shows the PDF of exponential distribution for several values of 
λ
 .
 Fig.4.5 - PDF of the exponential random variable.
 It is convenient to use the unit step function defined as
u(x) =
 {1 x≥0
 0
 otherwise
 so we can write the PDF of an 
Exponential(λ)
 random variable as
 fX(x) = λe−λx
 u(x).
 Let us find its CDF, mean and variance. For 
x > 0
 , we have
 FX(x) =
 So we can express the CDF as
 ∫ 
x
 0
 λe−λt
 dt = 1−e−λx
 .
 FX(x) = (1−e−λx
 )u(x).
 Let 
X ∼ Exponential(λ)
 . We can find its expected value as follows, using integration
 by parts:
 1
 λ
 EX=∫∞
 0 xλe−λxdx
 = ∫∞
 0 ye−ydy
 =
 1
 λ
 ∞
 [ −e−y −ye−y
 ]
 0
 1
 = .
 λ
 Now let's find Var
 (X)
 . We have
 EX2 =∫∞
 0 x2λe−λxdx
 = ∫∞
 0 y2e−ydy
 =
 1
 λ2
 1
 λ2
 2
 λ2
 ∞
 [ −2e−y −2ye−y −y2e−y
 ]
 0
 = .
 Thus, we obtain
 Var(X) = EX2 −(EX)2 = − = .
 2
 λ2
 1
 λ2
 1
 λ2
 1
 If 
X ∼ Exponential(λ)
 , then 
EX =
 and Var
 (X) = 
.
 λ
 1
 λ2
An interesting property of the exponential distribution is that it can be viewed as a
 continuous analogue of the geometric distribution. To see this, recall the random
 experiment behind the geometric distribution: you toss a coin (repeat a Bernoulli
 experiment) until you observe the first heads (success). Now, suppose that the coin
 tosses are 
Δ
 seconds apart and in each toss the probability of success is 
p = Δλ
 . Also
 suppose that 
Δ
 is very small, so the coin tosses are very close together in time and
 the probability of success in each trial is very low. Let 
X
 be the time you observe the
 first success. We will show in the Solved Problems section that the distribution of 
X
 converges to 
Exponential(λ)
 as 
Δ
 approaches zero.
 To get some intuition for this interpretation of the exponential distribution, suppose you
 are waiting for an event to happen. For example, you are at a store and are waiting for
 the next customer. In each millisecond, the probability that a new customer enters the
 store is very small. You can imagine that, in each millisecond, a coin (with a very small 
P(H)
 ) is tossed, and if it lands heads a new customers enters. If you toss a coin every
 millisecond, the time until a new customer arrives approximately follows an
 exponential distribution.
 The above interpretation of the exponential is useful in better understanding the
 properties of the exponential distribution. The most important of these properties is
 that the exponential distribution is memoryless. To see this, think of an exponential
 random variable in the sense of tossing a lot of coins until observing the first heads. If
 we toss the coin several times and do not observe a heads, from now on it is like we
 start all over again. In other words, the failed coin tosses do not impact the distribution
 of waiting time from now on. The reason for this is that the coin tosses are
 independent. We can state this formally as follows:
 P(X >x+a|X>a)=P(X>x).
 If 
X
 is exponential with parameter 
λ > 0
 , then 
X
 is a memoryless random
 variable, that is
 P(X >x+a | X>a)=P(X>x), for a,x≥0.
 From the point of view of waiting time until arrival of a customer, the memoryless
 property means that it does not matter how long you have waited so far. If you have
 not observed a customer until time 
a
 , the distribution of waiting time (from time 
a
 ) until
 the next customer is the same as when you started at time zero. Let us prove the
 memoryless property of the exponential distribution.
P(X >x+a|X>a)=
 =
 =
 =
 P(X >x+a,X>a)
 P(X >a)
 P(X >x+a)
 P(X >a)
 1 −FX(x+a)
 1 −FX(a)
 e−λ(x+a)
 e−λa
 =e−λx
 =P(X>x).
4.2.3 Normal (Gaussian) Distribution
 The normal distribution is by far the most important probability distribution. One of the
 main reasons for that is the Central Limit Theorem (CLT) that we will discuss later in
 the book. To give you an idea, the CLT states that if you add a large number of random
 variables, the distribution of the sum will be approximately normal under certain
 conditions. The importance of this result comes from the fact that many random
 variables in real life can be expressed as the sum of a large number of random
 variables and, by the CLT, we can argue that distribution of the sum should be normal.
 The CLT is one of the most important results in probability and we will discuss it later
 on. Here, we will introduce normal random variables.
 We first define the standard normal random variable. We will then see that we can
 obtain other normal random variables by scaling and shifting a standard normal
 random variable.
 A continuous random variable 
Z
 is said to be a standard normal (standard
 Gaussian) random variable, shown as 
Z ∼ N(0,1)
 , if its PDF is given by
 z2
 2
 fZ(z) = exp
 1
 √
 2π
 The 
1
 √
 {− } , for all z∈R.
 is there to make sure that the area under the PDF is equal to one. We will
 2π
 verify that this holds in the solved problems section. Figure 4.6 shows the PDF of the
 standard normal random variable.
Fig.4.6 - PDF of the standard normal random variable.
 Let us find the mean and variance of the standard normal distribution. To do that, we
 will use a simple useful fact. Consider a function 
g(u) : R → R
 . If 
g(u)
 is an odd
 function, i.e., 
g(−u) = −g(u)
 , and 
|∫ ∞
 0 g(u)du| < ∞
 , then
 ∫ 
∞
 −∞
 For our purpose, let
 g(u)du = 0.
 g(u) = u2k+1 exp{− } ,
 u2
 2
 where 
k = 0,1,2,...
 . Then 
g(u)
 is an odd function. Also 
|∫ ∞
 0 g(u)du| < ∞
 . One way to
 see this is to note that 
g(u)
 decays faster than the function 
exp{−u}
 and since
 | ∫ ∞
 0 exp{−u}du| < ∞
 , we conclude that 
|∫ ∞
 0 g(u)du| < ∞
 . Now, let 
Z
 be a standard
 normal random variable. Then, we have
 1
 √
 EZ2k+1 = ∫ 
∞
 −∞
 u2k+1exp{− } du=0,
 2π
 u2
 2
 for all 
k ∈ {0,1,2,..,}
 . Thus, we have shown that for a standard normal random
 variable 
Z
 , we have
 EZ =EZ3 =EZ5 =....=0.
 In particular, the standard normal distribution has zero mean. This is not surprising as
 we can see from Figure 4.6 that the PDF is symmetric around the origin, so we expect
 that 
EZ = 0
 . Next, let's find 
EZ2
 .
 1
 √
 EZ2 = ∫∞
 −∞u2exp{− } du
 2π
 u2
 2
[ −uexp{− }]
 ∞
 −∞
 +
 =
 1
 √
 2π
 1
 √
 2π
 u2
 2
 u2
 2
 + ∫∞
 −∞exp{− } du (integration by parts)
 1
 √
 2π
 =∫∞
 −∞ exp{− } du
 =1.
 u2
 2
 The last equality holds because we are integrating the standard normal PDF from 
−∞
 to 
∞
 . Thus, we conclude that for a standard normal random variable 
Z
 , we have
 Var(Z) = 1.
 So far we have shown the following:
 If 
Z ∼ N(0,1)
 , then 
EZ = 0
 and Var
 (Z) = 1
 .
 CDF of the standard normal
 
To find the CDF of the standard normal distribution, we need to integrate the PDF
 function. In particular, we have
 FZ(z) =
 1
 √
 2π
 ∫ 
z
 −∞ 
exp{− } du.
 u2
 2
 This integral does not have a closed form solution. Nevertheless, because of the
 importance of the normal distribution, the values of 
FZ(z)
 have been tabulated and
 many calculators and software packages have this function. We usually denote the
 standard normal CDF by 
Φ
 .
 The CDF of the standard normal distribution is denoted by the 
Φ
 function:
 Φ(x) =P(Z ≤x)=
 1
 √
 2π
 ∫ 
x
 −∞ 
exp{− } du.
 u2
 2
 As we will see in a moment, the CDF of any normal random variable can be written in
 terms of the 
Φ
 function, so the 
Φ
 function is widely used in probability. Figure 4.7
 shows the 
Φ
 function.
Fig.4.7 - The 
Φ
 function (CDF of standard normal).
 Here are some properties of the 
Φ
 function that can be shown from its definition.
 1. 
lim
 x→−∞Φ(x) = 0
 ;
 1
 2
 x→∞Φ(x) =1, lim
 2. 
Φ(0) = 
;
 3. 
Φ(−x) = 1−Φ(x)
 , for all 
x ∈ R
 .
 Also, since the 
Φ
 function does not have a closed form, it is sometimes useful to use
 upper or lower bounds. In particular we can state the following bounds (see Problem 7
 in the Solved Problems section). For all 
x ≥ 0
 ,
 1
 √
 2π
 x
 x2 +1
 exp
 x2
 2
 1
 √
 1
 {− }≤1−Φ(x)≤ exp{− } (4.7)
 2π
 x
 x2
 2
 As we mentioned earlier, because of the importance of the normal distribution, the
 values of the 
Φ
 function have been tabulated and many calculators and software
 packages have this function. For example, you can use the normcdf command in
 MATLAB to compute 
Φ(x)
 for a given number 
x
 . More specifically, 
normcdf(x)
 returns 
Φ(x)
 . Also, the function 
norminv
 returns 
Φ−1(x)
 . That is, if you run 
x = norminv(y)
 ,
 then 
x
 will be the real number for which 
Φ(x) = y
 .
 Normal random variables
 
Now that we have seen the standard normal random variable, we can obtain any
 normal random variable by shifting and scaling a standard normal random variable. In
 particular, define
 X=σZ+μ, where σ>0.
 Then
 EX=σEZ+μ=μ,
 2
 2
Var(X) = σ2Var(Z) = σ2.
 We say that 
X
 is a normal random variable with mean 
μ
 and variance 
σ2
 . We write
 X∼N(μ,σ2)
 .
 If 
Z
 is a standard normal random variable and 
X = σZ +μ
 , then 
X
 is a normal
 random variable with mean 
μ
 and variance 
σ2
 , i.e,
 X∼N(μ,σ2).
 Conversely, if 
X ∼ N(μ,σ2)
 , the random variable defined by 
Z =
 is a standard
 normal random variable, i.e., 
Z ∼ N(0,1)
 . To find the CDF of 
X ∼ N(μ,σ2)
 , we can
 X−μ
 σ
 write
 FX(x) = P(X ≤x)
 =P(σZ+μ ≤x) (where Z ∼N(0,1))
 =P
 ( Z ≤ )
 x−μ
 σ
 =Φ
 x−μ
 σ
 ( ) .
 To find the PDF, we can take the derivative of 
FX
 ,
 d
 fX(x) = FX(x)
 dx
 d
 dx
 = Φ
 1
 σ
 1
 σ
 x−μ
 σ
 ( )
 x−μ
 σ
 = Φ′
 ( ) (chain rule for derivative)
 = fZ
 x−μ
 σ
 ( )
 1
 σ√
 = exp
 2π
 (x−μ)2
 {− } .
 2σ2
If 
X
 is a normal random variable with mean 
μ
 and variance 
σ2
 , i.e, 
X ∼ N(μ,σ2)
 ,
 then
 fX(x) = exp
 1
 σ√
 2π
 { −
 } ,
 FX(x) = P(X ≤x)=Φ
 (x −μ)2
 2σ2
 x−μ
 σ
 ( ) ,
 P(a <X≤b)=Φ
 b −μ
 σ
 ( )−Φ( ) .
 a−μ
 σ
 Figure 4.8 shows the PDF of the normal distribution for several values of 
μ
 and 
σ
 .
 Fig.4.8 - PDF for normal distribution.
 Example 4.11
 
Let 
X ∼ N(−5,4)
 .
 a. Find 
P(X < 0)
 .
 b. Find 
P(−7 < X < −3)
 .
 c. Find 
P(X > −3|X > −5)
 .
 Solution
 X
 is a normal random variable with 
μ = −5
 and 
σ = √
 4 =2
 , thus we have
a. Find 
P(X < 0)
 :
 P(X <0) =FX(0)
 =Φ
 0−(−5)
 2
 ( )
 =Φ(2.5) ≈ 0.99
 b. Find 
P(−7 < X < −3)
 :
 P(−7 <X<−3)=FX(−3)−FX(−7)
 =Φ
 (−3)−(−5)
 2
 (−7)−(−5)
 2
 ( )−Φ( )
 =Φ(1)−Φ(−1)
 =2Φ(1)−1
 (since Φ(−x) = 1−Φ(x))
 ≈0.68
 c. Find 
P(X > −3|X > −5)
 :
 P(X >−3|X >−5)=
 =
 =
 =
 P(X>−3,X>−5)
 P(X>−5)
 P(X>−3)
 P(X>−5)
 1−Φ
 (−3)−(−5)
 2
 ( )
 1−Φ
 (−5)−(−5)
 2
 ( )
 1−Φ(1)
 1−Φ(0)
 0.1587
 0.5
 ≈ ≈0.32
 An important and useful property of the normal distribution is that a linear
 transformation of a normal random variable is itself a normal random variable. In
 particular, we have the following theorem:
 Theorem 4.3
 
If 
X ∼ N(μX,σ2
 X)
 , and 
Y = aX +b
 , where 
a,b ∈ R
 , then 
Y ∼ N(μY,σ2
 Y)
 where
 μY =aμX+b, σ2
 Y =a2σ2
 X.
 Proof
We can write
 X=σXZ+μX where Z∼N(0,1).
 Thus,
 Therefore,
 Y =aX+b
 =a(σXZ +μX)+b
 =(aσX)Z +(aμX +b).
 Y ∼N(aμX+b,a2σ2
 X).
4.2.4 Gamma Distribution
 The gamma distribution is another widely used distribution. Its importance is largely
 due to its relation to exponential and normal distributions. Here, we will provide an
 introduction to the gamma distribution. In Chapters 6 and 
11, we will discuss more
 properties of the gamma random variables. Before introducing the gamma random
 variable, we need to introduce the gamma function.
 Gamma function: The gamma function [
 10], shown by 
Γ(x)
 , is an extension of the
 factorial function to real (and complex) numbers. Specifically, if 
n ∈ {1,2,3,...}
 , then
 Γ(n) = (n −1)!
 More generally, for any positive real number 
α
 , 
Γ(α)
 is defined as
 Γ(α) =
 ∫ 
∞
 0
 xα−1e−xdx,
 for α >0.
 Figure 4.9 shows the gamma function for positive real values.
Figure 4.9: The Gamma function for some real values of 
α
 .
 Note that for 
α = 1
 , we can write
 Γ(1) =
 ∫ 
∞
 0
 =1.
 e−x
 dx
 Using the change of variable 
x = λy
 , we can show the following equation that is often
 useful when working with the gamma distribution:
 Γ(α) = λα
 ∫ 
∞
 0
 yα−1e−λy
 dy
 Also, using integration by parts it can be shown that
 Γ(α+1) =αΓ(α),
 for α,λ > 0.
 for α >0.
 Note that if 
α = n
 , where 
n
 is a positive integer, the above equation reduces to
 n! =n⋅(n−1)!
Properties of the gamma function
 For any positive real number 
α
 :
 1. 
Γ(α)=∫∞
 0 xα−1e−xdx
 ;
 2. 
∫∞
 0 xα−1e−λxdx= , for λ>0;
 3. 
Γ(α+1)=αΓ(α);
 4. 
Γ(n)=(n−1)!, for n=1,2,3,⋯;
 5. 
Γ( )=√π
 .
 
Example 4.12
 Answer the following questions:
 1. Find 
Γ( ). 2. Find the value of the following integral:
 I=∫
 ∞
 0
 x6e−5x
 dx.
 Solution
 1. To find 
Γ( ),
 we can write
 Γ(α)
 λα
 1
 2
 7
 2
 7
 2
Γ( )= ⋅Γ( ) (using Property 3)
 = ⋅ ⋅Γ( ) (using Property 3)
 = ⋅ ⋅ ⋅Γ( )(using Property 3)
 = ⋅ ⋅ ⋅√π (using Property 5)
 = √π.
 2. Using Property 2 with 
α=7
 and 
λ=5
 , we obtain
 I=∫
 ∞
 0
 x6e−5x
 dx
 =
 = (using Property 4)
 ≈0.0092
 
Gamma Distribution:
 We now define the gamma distribution by providing its PDF: 
A continuous random variable 
X
 is said to have a gamma distribution with
 parameters 
α>0 and λ>0
 , shown as 
X∼Gamma(α,λ)
 , if its PDF is given by
 fX(x)=
 { x>0
 0 otherwise
 
If we let 
α=1
 , we obtain
 fX(x)={λe−λx x>0
 0 otherwise
 Thus, we conclude 
Gamma(1,λ)=Exponential(λ)
 . More generally, if you sum 
n independent 
Exponential(λ)
 random variables, then you will get a 
Gamma(n,λ) random variable. We will prove this later on using the moment generating function.
 The gamma distribution is also related to the normal distribution as will be discussed
 later. Figure 4.10 shows the PDF of the gamma distribution for several values of 
α
 . 
 
7
 2
 5
 2
 5
 2
 5
 2
 3
 2
 3
 2
 5
 2
 3
 2
 1
 2
 1
 2
 5
 2
 3
 2
 1
 2
 15
 8
 Γ(7)
 57
 6!
 57
 λαxα−1e−λx
 Γ(α)
Figure 4.10: PDF of the gamma distribution for some values of 
α
 and 
λ
 .
 Example 4.13
 
Using the properties of the gamma function, show that the gamma PDF integrates to
 1, i.e., show that for 
α,λ > 0
 , we have
 ∫ 
∞
 0
 Solution
 
We can write
 ∫ 
∞
 0
 λαxα−1e−λx
 Γ(α)
 dx =
 λα
 Γ(α)
 λα
 Γ(α)
 ∫ 
∞
 0
 λαxα−1e−λx
 Γ(α)
 xα−1e−λx
 dx
 Γ(α)
 λα
 dx =1.
 = ⋅ (using Property 2 of the gamma function)
 =1.
 In the Solved Problems section, we calculate the mean and variance for the gamma
 distribution. In particular, we find out that if 
X ∼ Gamma(α,λ)
 , then
α
 EX= , Var(X)= .
 λ
 α
 λ2
4.2.5 Other Distributions
 In addition to the special distributions that we discussed previously, there are many
 other continuous random variables that are used in practice. Depending on the
 applications you are interested in you might need to deal with some of them. We have
 provided a list of important distributions in the appendix. In the next chapters, we will
 discuss some of them in more detail. There are also some problems at the end of this
 chapter that discuss some of these distributions. There is no need to try to memorize
 these distributions. When you understand the general theory behind random variables,
 you can essentially work with any distribution.
4.2.6 Solved Problems: 
 
Special Continuous Distributions
 Problem 1
 
Suppose the number of customers arriving at a store obeys a Poisson distribution with
 an average of 
λ
 customers per unit time. That is, if 
Y
 is the number of customers
 arriving in an interval of length 
t
 , then 
Y ∼ Poisson(λt)
 . Suppose that the store opens
 at time 
t = 0
 . Let 
X
 be the arrival time of the first customer. Show that 
X∼Exponential(λ)
 .
 Solution
 We first find 
P(X > t)
 :
 P(X >t) =P(No arrival in [0,t])
 =e−λt
 (λt)0
 0!
 =e−λt
 .
 Thus, the CDF of 
X
 for 
x > 0
 is given by
 FX(x) = 1−P(X >x)=1−e−λx,
 which is the CDF of 
Exponential(λ)
 . Note that by the same argument, the time
 between the first and second customer also has 
Exponential(λ)
 distribution. In
 general, the time between the 
k
 'th and 
k + 1
 'th customer is 
Exponential(λ)
 .
 Problem 2 (Exponential as the limit of Geometric)
 
Let 
Y ∼ Geometric(p)
 , where 
p = λΔ
 . Define 
X = YΔ
 , where 
λ,Δ > 0
 . Prove that for
 any 
x ∈ (0,∞)
 , we have
 lim
 Δ→0
 FX(x) = 1−e−λx
 .
 Solution
If 
Y ∼ Geometric(p)
 and 
q = 1 −p
 , then
 P(Y ≤n)=∑n
 k=1pqk−1
 =p. =1−(1−p)n
 .
 1−qn
 1−q
 Then for any 
y ∈ (0,∞)
 , we can write
 P(Y ≤y)=1−(1−p)⌊y⌋,
 where 
⌊y⌋
 is the largest integer less than or equal to 
y
 . Now, since 
X = YΔ
 , we have
 FX(x) = P(X ≤x)
 =P
 ( Y ≤ )
 x
 Δ
 x
 Δ
 =1−(1−p)⌊ ⌋ 
=1−(1−λΔ)⌊ ⌋.
 Now, we have
 x
 Δ
 x
 limΔ→0 FX(x) = limΔ→0 1−(1−λΔ)⌊ ⌋
 Δ
 x
 =1−limΔ→0(1−λΔ)⌊ ⌋
 =1−e−λx
 .
 Δ
 x
 The last equality holds because 
−1 ≤ ⌊ ⌋ ≤ 
, and we know
 Δ
 lim
 x
 Δ
 x
 Δ
 1
 Δ
 Δ→0+(1 −λΔ) =e−λ
 .
 Problem 3
 
Let 
U ∼ Uniform(0,1)
 and 
X = −ln(1−U)
 . Show that 
X ∼ Exponential(1)
 .
 Solution
 First note that since 
RU = (0,1)
 , 
RX = (0,∞)
 . We will find the CDF of 
X
 . For 
x ∈ (0,∞)
 , we have
 FX(x) = P(X ≤x)
 =P(−ln(1−U) ≤x)
 =P
 1
 1−U
 ( ≤ex
 )
 =P(U ≤1−e−x)=1−e−x
 ,
 
which is the CDF of an 
Exponential(1)
 random variable.
 
Problem 4
 Let 
X∼N(2,4)
 and 
Y=3−2X
 .
 a. Find 
P(X>1)
 .
 b. Find 
P(−2<Y<1)
 .
 c. Find 
P(X>2|Y<1)
 .
 Solution
 a. Find 
P(X>1)
 : We have 
μX=2
 and 
σX=2
 . Thus,
 P(X>1)=1−Φ( ) =1−Φ(−0.5)=Φ(0.5)=0.6915 
b. Find 
P(−2<Y<1)
 : Since 
Y=3−2X
 , using Theorem 4.3, we have 
Y∼N(−1,16)
 . Therefore,
 P(−2<Y<1)=Φ( )−Φ( ) =Φ(0.5)−Φ(−0.25)=0.29 
c. Find 
P(X>2|Y<1)
 :
 P(X>2|Y<1)=P(X>2|3−2X<1)
 =P(X>2|X>1)
 =
 =
 =
 =
 ≈0.72 
1−2
 2
 1−(−1)
 4
 (−2)−(−1)
 4
 P(X>2,X>1)
 P(X>1)
 P(X>2)
 P(X>1)
 1−Φ( ) 2−2
 2
 1−Φ( ) 1−2
 2
 1−Φ(0)
 1−Φ(−0.5)
 
Problem 5
 Let 
X∼N(0,σ2)
 . Find 
E|X|
 Solution
 We can write 
X=σZ
 , where 
Z∼N(0,1)
 . Thus, 
E|X|=σE|Z|
 . We have
 E|Z|= ∫∞
 −∞|t|e− dt
 = ∫∞
 0 |t|e− dt (integral of an even function)
 =√ ∫∞
 0 te− dt
 =√ [ −e−
 ]
 ∞
 0
 =√ 
Thus, we conclude 
E|X|=σE|Z|=σ√ .
 
Problem 6
 Show that the constant in the normal distribution must be . That is, show that
 I=∫
 ∞
 −∞
 e−
 dx=√2π.
 Hint: Write 
I2
 as a double integral in polar coordinates.
 Solution
 Let 
I=∫∞
 −∞e− dx
 . We show that 
I2=2π
 . To see this, note
 I2
 =∫∞
 −∞e− dx∫∞
 −∞e− dy
 =∫∞
 −∞∫∞
 −∞e− dxdy
 .
 
To evaluate this double integral we can switch to polar coordinates. This can be done
 by change of variables 
x=rcosθ,y=rsinθ
 , and 
dxdy=rdrdθ
 . In particular, we have
 I2
 1
 √2π
 t2
 2
 2
 √2π
 t2
 2
 2
 π
 t2
 2
 2
 π
 t2
 2 2
 π
 2
 π
 1
 √2π
 x2
 2
 x2
 2
 x2
 2
 y2
 2
 x2+y2
 2
=∫∞
 −∞∫∞
 −∞e− dxdy
 =∫∞
 0 ∫2π
 0 e− rdθdr
 =2π∫∞
 0 re− dr
 =2π
 [ −e−
 ]
 ∞
 0
 =2π
 .
 
 
Problem 7
 Let 
Z∼N(0,1)
 . Prove for all 
x≥0
 ,
 e−
 ≤P(Z≥x)≤ e−
 .
 Solution
 To show the upper bound, we can write
 P(Z≥x)= ∫∞
 x e− du
 = [−e−
 ]
 ∞
 x
 = e− .
 
To show the lower bound, let 
Q(x)=P(Z≥x)
 , and
 h(x)=Q(x)− e−
 , for all x≥0.
 It suffices to show that
 h(x)≥0, for all x≥0.
 To see this, note that the function 
h
 has the following properties
 1. 
h(0)=
 ;
 2. 
lim x→∞h(x)=0
 ;
 x2+y2
 2
 r2
 2
 r2
 2
 r2
 2
 1
 √2π
 x
 x2+1
 x2
 2 1
 √2π
 1
 x
 x2
 2
 1
 √2π
 u2
 2
 1
 √2π
 1
 x
 u2
 2
 1
 √2π
 1
 x
 x2
 2
 1
 √2π
 x
 x2+1
 x2
 2
 1
 2
−x2
 2
 2
 √
 2π
 e
 (x2+1)
 3. 
h′(x) = − ⎛
 ⎝
 ⎞
 ⎠<0
 , for all 
x ≥0
 .
 Therefore, 
h(x)
 is a strictly decreasing function that starts at 
h(0) =
 and decreases
 as 
x
 increases. It approaches 
0
 as 
x
 goes to infinity. We conclude that 
h(x) ≥ 0
 , for all 
x ≥0
 .
 1
 2
 Problem 8
 
Let 
X ∼ Gamma(α,λ)
 , where 
α,λ > 0
 . Find 
EX
 , and 
Var(X)
 .
 Solution
 To find 
EX
 we can write
 EX=
 =
 ∫ 
∞
 0
 ∫ 
∞
 0
 xfX(x)dx
 x⋅ xα−1e−λxdx
 λα
 Γα
 =
 =
 =
 λα
 Γ(α)
 λα
 Γ(α)
 λα
 Γ(α)
 ∫ 
∞
 0
 ∫ 
∞
 x⋅xα−1e−λxdx
 xαe−λxdx
 =
 0
 Γ(α+1)
 λα+1
 αΓ(α)
 λΓ(α)
 α
 = .
 λ
 Similarly, we can find 
EX2
 :
 (using Property 2 of the gamma function)
 (using Property 3 of the gamma function)
EX2 =∫ 
∞
 2
 dx
 x
 0
 =
 ∫ 
∞
 0
 x
 2
 =
 =
 λα
 Γ(α)
 λα
 Γ(α)
 λα
 Γ(α)
 ⋅
 λα
 Γ(α)
 ∫ 
∞
 x
 2
 xα−1e−λx
 dx
 ⋅ xα−1e−λx
 dx
 0
 ∫ 
∞
 0
 xα+1e−λx
 dx
 Γ(α+2)
 =
 λα+2
 =
 =
 (α +1)Γ(α+1)
 λ2Γ(α)
 (α +1)αΓ(α)
 λ2Γ(α)
 α(α+1)
 λ2
 = .
 So, we conclude
 (using Property 2 of the gamma function)
 (using Property 3 of the gamma function)
 (using Property 3 of the gamma function)
 Var(X) = EX2 −(EX)2
 α(α+1)
 λ2
 = −
 = .
 α
 λ2
 α2
 λ