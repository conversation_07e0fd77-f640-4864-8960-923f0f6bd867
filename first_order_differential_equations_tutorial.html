<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First-Order Differential Equations Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-bg: #34495e;
            --text-color: #2c3e50;
            --light-text: #7f8c8d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .nav-menu {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-menu h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        .nav-menu ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 0.5rem;
        }

        .nav-menu li {
            padding: 0.5rem;
            border-left: 3px solid var(--secondary-color);
            background: var(--light-bg);
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu li:hover {
            background: var(--secondary-color);
            color: white;
            transform: translateX(5px);
        }

        .section {
            background: white;
            margin-bottom: 2rem;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            border-left: 5px solid var(--secondary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--light-bg);
            padding-bottom: 0.5rem;
        }

        .section h3 {
            color: var(--secondary-color);
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem 0;
        }

        .definition-box {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            border: 1px solid var(--secondary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--secondary-color);
        }

        .definition-box h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .example-box {
            background: linear-gradient(135deg, #fff9e6 0%, #fef5e7 100%);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--warning-color);
        }

        .example-box h4 {
            color: var(--warning-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .important-box {
            background: linear-gradient(135deg, #fdf2f2 0%, #fadbd8 100%);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--accent-color);
        }

        .important-box h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .method-box {
            background: linear-gradient(135deg, #e8f8f5 0%, #d5f4e6 100%);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--success-color);
        }

        .method-box h4 {
            color: var(--success-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .math-display {
            text-align: center;
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            margin: 1rem 0;
            padding: 1rem;
            background: var(--light-bg);
            border-radius: 5px;
            border-left: 4px solid var(--secondary-color);
            position: relative;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--secondary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .nav-menu ul {
                grid-template-columns: 1fr;
            }
            
            .section {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>First-Order Differential Equations</h1>
            <p class="subtitle">A Comprehensive Tutorial with Visual Examples</p>
        </header>

        <div class="nav-menu">
            <h3>📚 Tutorial Contents</h3>
            <ul>
                <li>1. Introduction and Basic Concepts</li>
                <li>2. Definition of First-Order DEs</li>
                <li>3. Initial Value Problems</li>
                <li>4. Separable Equations</li>
                <li>5. Growth and Decay Models</li>
                <li>6. Logistic Growth Model</li>
                <li>7. Linear Differential Equations</li>
                <li>8. Solution Methods</li>
            </ul>
        </div>
        
        <!-- Section 1: Introduction -->
        <section class="section" id="introduction">
            <h2>🚀 1. Introduction to First-Order Differential Equations</h2>

            <p>In many fields such as physics, biology, and business, relationships often exist between unknown quantities and their rates of change. These relationships, which don't involve higher derivatives, lead us to study <strong>first-order differential equations</strong>.</p>

            <div class="important-box">
                <h4>🎯 Why Study First-Order DEs?</h4>
                <p>First-order differential equations appear everywhere in science and engineering:</p>
                <ul>
                    <li><strong>Population Growth:</strong> How populations change over time</li>
                    <li><strong>Radioactive Decay:</strong> How substances decay exponentially</li>
                    <li><strong>Newton's Law of Cooling:</strong> How temperature changes</li>
                    <li><strong>Economics:</strong> Interest rates and investment growth</li>
                    <li><strong>Medicine:</strong> Drug concentration in bloodstream</li>
                </ul>
            </div>

            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Applications of First-Order Differential Equations
                    </text>

                    <!-- Population Growth -->
                    <g transform="translate(50, 60)">
                        <rect width="120" height="80" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Population</text>
                        <text x="60" y="35" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Growth</text>
                        <path d="M 20 65 Q 40 55 60 45 Q 80 35 100 25" stroke="#27ae60" stroke-width="3" fill="none"/>
                        <text x="60" y="75" text-anchor="middle" font-size="10" fill="#7f8c8d">dy/dt = ky</text>
                    </g>

                    <!-- Radioactive Decay -->
                    <g transform="translate(200, 60)">
                        <rect width="120" height="80" fill="#fff9e6" stroke="#f39c12" stroke-width="2" rx="5"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Radioactive</text>
                        <text x="60" y="35" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Decay</text>
                        <path d="M 20 25 Q 40 35 60 45 Q 80 55 100 65" stroke="#e74c3c" stroke-width="3" fill="none"/>
                        <text x="60" y="75" text-anchor="middle" font-size="10" fill="#7f8c8d">dy/dt = -ky</text>
                    </g>

                    <!-- Newton's Cooling -->
                    <g transform="translate(350, 60)">
                        <rect width="120" height="80" fill="#fdf2f2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Newton's</text>
                        <text x="60" y="35" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Cooling</text>
                        <path d="M 20 30 Q 40 40 60 50 Q 80 55 100 58" stroke="#9b59b6" stroke-width="3" fill="none"/>
                        <text x="60" y="75" text-anchor="middle" font-size="10" fill="#7f8c8d">dy/dt = k(T-y)</text>
                    </g>

                    <!-- Logistic Growth -->
                    <g transform="translate(125, 180)">
                        <rect width="120" height="80" fill="#e8f8f5" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Logistic</text>
                        <text x="60" y="35" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Growth</text>
                        <path d="M 20 65 Q 30 50 40 40 Q 50 35 60 33 Q 70 32 80 31 Q 90 30 100 30" stroke="#16a085" stroke-width="3" fill="none"/>
                        <text x="60" y="75" text-anchor="middle" font-size="10" fill="#7f8c8d">dy/dt = ry(M-y)</text>
                    </g>

                    <!-- Economics -->
                    <g transform="translate(275, 180)">
                        <rect width="120" height="80" fill="#f4f1fb" stroke="#9b59b6" stroke-width="2" rx="5"/>
                        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Economic</text>
                        <text x="60" y="35" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Models</text>
                        <path d="M 20 60 L 30 55 L 40 50 L 50 45 L 60 40 L 70 35 L 80 30 L 90 25 L 100 20" stroke="#8e44ad" stroke-width="3" fill="none"/>
                        <text x="60" y="75" text-anchor="middle" font-size="10" fill="#7f8c8d">dP/dt = rP</text>
                    </g>

                    <!-- Central arrow -->
                    <g transform="translate(300, 150)">
                        <circle cx="0" cy="0" r="15" fill="#3498db" opacity="0.8"/>
                        <text x="0" y="5" text-anchor="middle" font-size="12" font-weight="bold" fill="white">DE</text>
                    </g>
                </svg>
            </div>
        </section>

        <!-- Section 2: Definition -->
        <section class="section" id="definition">
            <h2>📖 2. Definition of First-Order Differential Equations</h2>

            <div class="definition-box">
                <h4>📝 Definition: First-Order Differential Equation</h4>
                <p>A <strong>first-order differential equation</strong> is an equation of the form:</p>
                <div class="math-display">
                    $$F(t, y, y') = 0$$
                </div>
                <p>where:</p>
                <ul>
                    <li><strong>$t$</strong> is the independent variable (often time)</li>
                    <li><strong>$y$</strong> is the dependent variable (function of $t$)</li>
                    <li><strong>$y'$</strong> is the first derivative of $y$ with respect to $t$</li>
                    <li><strong>$F$</strong> is a function of three variables</li>
                </ul>
            </div>

            <div class="definition-box">
                <h4>🎯 Solution of a Differential Equation</h4>
                <p>A <strong>solution</strong> of a first-order differential equation is a function $f(t)$ that satisfies:</p>
                <div class="math-display">
                    $$F(t, f(t), f'(t)) = 0$$
                </div>
                <p>for every value of $t$ in the domain.</p>
            </div>

            <div class="example-box">
                <h4>💡 Example: Simple First-Order DE</h4>
                <p>Consider the differential equation:</p>
                <div class="math-display">
                    $$y' = t^2 + 1$$
                </div>
                <p>This can be written as $F(t, y, y') = y' - t^2 - 1 = 0$.</p>
                <p><strong>Solution:</strong> All solutions are of the form:</p>
                <div class="math-display">
                    $$y = \frac{t^3}{3} + t + C$$
                </div>
                <p>where $C$ is an arbitrary constant.</p>
            </div>

            <div class="svg-container">
                <svg width="500" height="350" viewBox="0 0 500 350">
                    <!-- Background -->
                    <rect width="500" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="250" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Solution Family: y = t³/3 + t + C
                    </text>

                    <!-- Coordinate system -->
                    <g transform="translate(250, 175)">
                        <!-- Axes -->
                        <line x1="-200" y1="0" x2="200" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-150" x2="0" y2="150" stroke="#34495e" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="190" y="-10" font-size="12" fill="#2c3e50">t</text>
                        <text x="10" y="-140" font-size="12" fill="#2c3e50">y</text>

                        <!-- Grid lines -->
                        <g stroke="#bdc3c7" stroke-width="0.5" opacity="0.5">
                            <line x1="-200" y1="-100" x2="200" y2="-100"/>
                            <line x1="-200" y1="-50" x2="200" y2="-50"/>
                            <line x1="-200" y1="50" x2="200" y2="50"/>
                            <line x1="-200" y1="100" x2="200" y2="100"/>
                            <line x1="-100" y1="-150" x2="-100" y2="150"/>
                            <line x1="-50" y1="-150" x2="-50" y2="150"/>
                            <line x1="50" y1="-150" x2="50" y2="150"/>
                            <line x1="100" y1="-150" x2="100" y2="150"/>
                        </g>

                        <!-- Solution curves for different C values -->
                        <!-- C = 2 -->
                        <path d="M -150 -95 Q -100 -65 -50 -25 Q 0 15 50 65 Q 100 125 150 195"
                              stroke="#e74c3c" stroke-width="3" fill="none"/>
                        <text x="160" y="140" font-size="10" fill="#e74c3c">C = 2</text>

                        <!-- C = 0 -->
                        <path d="M -150 -145 Q -100 -115 -50 -75 Q 0 -35 50 15 Q 100 75 150 145"
                              stroke="#3498db" stroke-width="3" fill="none"/>
                        <text x="160" y="90" font-size="10" fill="#3498db">C = 0</text>

                        <!-- C = -2 -->
                        <path d="M -150 -195 Q -100 -165 -50 -125 Q 0 -85 50 -35 Q 100 25 150 95"
                              stroke="#27ae60" stroke-width="3" fill="none"/>
                        <text x="160" y="40" font-size="10" fill="#27ae60">C = -2</text>

                        <!-- Tick marks -->
                        <g stroke="#2c3e50" stroke-width="1">
                            <line x1="-100" y1="-5" x2="-100" y2="5"/>
                            <line x1="-50" y1="-5" x2="-50" y2="5"/>
                            <line x1="50" y1="-5" x2="50" y2="5"/>
                            <line x1="100" y1="-5" x2="100" y2="5"/>
                            <line x1="-5" y1="-100" x2="5" y2="-100"/>
                            <line x1="-5" y1="-50" x2="5" y2="-50"/>
                            <line x1="-5" y1="50" x2="5" y2="50"/>
                            <line x1="-5" y1="100" x2="5" y2="100"/>
                        </g>

                        <!-- Tick labels -->
                        <text x="-100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-2</text>
                        <text x="-50" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-1</text>
                        <text x="50" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>
                        <text x="100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                        <text x="-15" y="-95" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                        <text x="-15" y="-45" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>
                        <text x="-15" y="55" text-anchor="middle" font-size="10" fill="#2c3e50">-1</text>
                        <text x="-15" y="105" text-anchor="middle" font-size="10" fill="#2c3e50">-2</text>
                    </g>

                    <!-- Legend -->
                    <text x="50" y="320" font-size="12" fill="#7f8c8d">
                        Different values of C give different solution curves
                    </text>
                </svg>
            </div>
        </section>
        <!-- Section 3: Initial Value Problems -->
        <section class="section" id="initial-value">
            <h2>🎯 3. Initial Value Problems</h2>

            <div class="definition-box">
                <h4>📝 Definition: Initial Conditions</h4>
                <p><strong>Initial conditions</strong> are constraints that specify the value of the solution (or its derivatives) at particular points.</p>
                <p>For a function $f(t)$, initial conditions have the form:</p>
                <div class="math-display">
                    $$f(t_0) = f_0, \quad f'(t_0) = f_1, \quad f''(t_0) = f_2, \ldots$$
                </div>
            </div>

            <div class="definition-box">
                <h4>📝 Definition: Initial Value Problem (IVP)</h4>
                <p>An <strong>initial value problem</strong> is a differential equation together with initial conditions.</p>
                <p>For first-order equations, an IVP typically has the form:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = f(t, y), \quad y(t_0) = y_0$$
                </div>
            </div>

            <div class="example-box">
                <h4>💡 Example: Solving an IVP</h4>
                <p>Solve the initial value problem:</p>
                <div class="math-display">
                    $$\frac{dy}{dx} = 2x, \quad y(0) = 2$$
                </div>
                <p><strong>Solution Process:</strong></p>
                <ol class="step-list">
                    <li>First, solve the differential equation: $\frac{dy}{dx} = 2x$</li>
                    <li>Integrate both sides: $y = \int 2x \, dx = x^2 + C$</li>
                    <li>Apply the initial condition: $y(0) = 2$, so $0^2 + C = 2$, thus $C = 2$</li>
                    <li>The specific solution is: $y = x^2 + 2$</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="500" height="350" viewBox="0 0 500 350">
                    <!-- Background -->
                    <rect width="500" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="250" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        IVP Solution: y = x² + 2, y(0) = 2
                    </text>

                    <!-- Coordinate system -->
                    <g transform="translate(250, 200)">
                        <!-- Axes -->
                        <line x1="-200" y1="0" x2="200" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-120" x2="0" y2="120" stroke="#34495e" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="190" y="-10" font-size="12" fill="#2c3e50">x</text>
                        <text x="10" y="-110" font-size="12" fill="#2c3e50">y</text>

                        <!-- Grid lines -->
                        <g stroke="#bdc3c7" stroke-width="0.5" opacity="0.5">
                            <line x1="-200" y1="-80" x2="200" y2="-80"/>
                            <line x1="-200" y1="-40" x2="200" y2="-40"/>
                            <line x1="-200" y1="40" x2="200" y2="40"/>
                            <line x1="-200" y1="80" x2="200" y2="80"/>
                            <line x1="-100" y1="-120" x2="-100" y2="120"/>
                            <line x1="-50" y1="-120" x2="-50" y2="120"/>
                            <line x1="50" y1="-120" x2="50" y2="120"/>
                            <line x1="100" y1="-120" x2="100" y2="120"/>
                        </g>

                        <!-- General solution family (lighter curves) -->
                        <g opacity="0.3">
                            <path d="M -150 115 Q -100 60 -50 15 Q 0 -20 50 15 Q 100 60 150 115"
                                  stroke="#95a5a6" stroke-width="2" fill="none"/>
                            <path d="M -150 95 Q -100 40 -50 -5 Q 0 -40 50 -5 Q 100 40 150 95"
                                  stroke="#95a5a6" stroke-width="2" fill="none"/>
                            <path d="M -150 75 Q -100 20 -50 -25 Q 0 -60 50 -25 Q 100 20 150 75"
                                  stroke="#95a5a6" stroke-width="2" fill="none"/>
                        </g>

                        <!-- Specific solution y = x² + 2 -->
                        <path d="M -150 105 Q -100 50 -50 5 Q 0 -40 50 5 Q 100 50 150 105"
                              stroke="#e74c3c" stroke-width="4" fill="none"/>

                        <!-- Initial condition point -->
                        <circle cx="0" cy="-40" r="6" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                        <text x="15" y="-35" font-size="12" fill="#e67e22" font-weight="bold">(0, 2)</text>

                        <!-- Tick marks -->
                        <g stroke="#2c3e50" stroke-width="1">
                            <line x1="-100" y1="-5" x2="-100" y2="5"/>
                            <line x1="-50" y1="-5" x2="-50" y2="5"/>
                            <line x1="50" y1="-5" x2="50" y2="5"/>
                            <line x1="100" y1="-5" x2="100" y2="5"/>
                            <line x1="-5" y1="-80" x2="5" y2="-80"/>
                            <line x1="-5" y1="-40" x2="5" y2="-40"/>
                            <line x1="-5" y1="40" x2="5" y2="40"/>
                            <line x1="-5" y1="80" x2="5" y2="80"/>
                        </g>

                        <!-- Tick labels -->
                        <text x="-100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-2</text>
                        <text x="-50" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-1</text>
                        <text x="50" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>
                        <text x="100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                        <text x="-15" y="-75" text-anchor="middle" font-size="10" fill="#2c3e50">4</text>
                        <text x="-15" y="-35" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                        <text x="-15" y="45" text-anchor="middle" font-size="10" fill="#2c3e50">-2</text>
                        <text x="-15" y="85" text-anchor="middle" font-size="10" fill="#2c3e50">-4</text>
                    </g>

                    <!-- Legend -->
                    <g transform="translate(50, 310)">
                        <line x1="0" y1="0" x2="20" y2="0" stroke="#95a5a6" stroke-width="2"/>
                        <text x="25" y="5" font-size="12" fill="#7f8c8d">General solution family</text>
                        <line x1="150" y1="0" x2="170" y2="0" stroke="#e74c3c" stroke-width="4"/>
                        <text x="175" y="5" font-size="12" fill="#7f8c8d">Specific solution</text>
                        <circle cx="320" cy="0" r="4" fill="#f39c12"/>
                        <text x="330" y="5" font-size="12" fill="#7f8c8d">Initial condition</text>
                    </g>
                </svg>
            </div>

            <div class="important-box">
                <h4>🔑 Key Insight</h4>
                <p>The initial condition allows us to determine the specific value of the arbitrary constant $C$, giving us a <strong>unique solution</strong> rather than a family of solutions.</p>
            </div>
        </section>

        <!-- Section 4: Separable Equations -->
        <section class="section" id="separable">
            <h2>🔄 4. Separable Differential Equations</h2>

            <div class="definition-box">
                <h4>📝 Definition: Separable Differential Equation</h4>
                <p>A first-order differential equation is <strong>separable</strong> if it can be written in the form:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = f(t) \cdot g(y)$$
                </div>
                <p>where $f(t)$ depends only on $t$ and $g(y)$ depends only on $y$.</p>
            </div>

            <div class="method-box">
                <h4>🛠️ Method: Separation of Variables</h4>
                <p>To solve a separable equation $\frac{dy}{dt} = f(t) \cdot g(y)$:</p>
                <ol class="step-list">
                    <li>Separate the variables: $\frac{dy}{g(y)} = f(t) \, dt$</li>
                    <li>Integrate both sides: $\int \frac{dy}{g(y)} = \int f(t) \, dt$</li>
                    <li>If antiderivatives exist and we can solve for $y$, then: $G(y) = F(t) + C$</li>
                </ol>
                <p>where $G$ and $F$ are antiderivatives of $\frac{1}{g}$ and $f$ respectively.</p>
            </div>

            <div class="example-box">
                <h4>💡 Example: Solving a Separable Equation</h4>
                <p>Solve: $\frac{dy}{dt} = 2t(25 - y)$</p>
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Identify: $f(t) = 2t$ and $g(y) = 25 - y$</li>
                    <li>Separate variables: $\frac{dy}{25 - y} = 2t \, dt$</li>
                    <li>Integrate both sides: $\int \frac{dy}{25 - y} = \int 2t \, dt$</li>
                    <li>Evaluate: $-\ln|25 - y| = t^2 + C$</li>
                    <li>Solve for $y$: $25 - y = Ae^{-t^2}$, so $y = 25 - Ae^{-t^2}$</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Separation of Variables Process
                    </text>

                    <!-- Step 1: Original equation -->
                    <g transform="translate(100, 70)">
                        <rect width="150" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Original Equation</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">dy/dt = f(t)g(y)</text>
                    </g>

                    <!-- Arrow 1 -->
                    <g transform="translate(275, 100)">
                        <path d="M 0 0 L 25 0" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="12" y="-10" text-anchor="middle" font-size="10" fill="#7f8c8d">separate</text>
                    </g>

                    <!-- Step 2: Separated form -->
                    <g transform="translate(325, 70)">
                        <rect width="150" height="60" fill="#fff9e6" stroke="#f39c12" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Separated Form</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">dy/g(y) = f(t)dt</text>
                    </g>

                    <!-- Arrow 2 -->
                    <g transform="translate(400, 155)">
                        <path d="M 0 0 L 0 25" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="20" y="12" text-anchor="middle" font-size="10" fill="#7f8c8d">integrate</text>
                    </g>

                    <!-- Step 3: Integrated form -->
                    <g transform="translate(325, 205)">
                        <rect width="150" height="60" fill="#e8f8f5" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Integrated Form</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">G(y) = F(t) + C</text>
                    </g>

                    <!-- Arrow 3 -->
                    <g transform="translate(275, 235)">
                        <path d="M 25 0 L 0 0" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="12" y="-10" text-anchor="middle" font-size="10" fill="#7f8c8d">solve for y</text>
                    </g>

                    <!-- Step 4: Final solution -->
                    <g transform="translate(100, 205)">
                        <rect width="150" height="60" fill="#fdf2f2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Solution</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">y = solution(t)</text>
                    </g>

                    <!-- Example visualization -->
                    <g transform="translate(50, 320)">
                        <text x="250" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Example: dy/dt = 2t(25-y) → y = 25 - Ae^(-t²)
                        </text>
                    </g>

                    <!-- Solution curves -->
                    <g transform="translate(300, 350)">
                        <!-- Different A values -->
                        <path d="M -100 -10 Q -50 -8 0 -5 Q 50 -2 100 0" stroke="#e74c3c" stroke-width="2" fill="none"/>
                        <path d="M -100 -5 Q -50 -3 0 0 Q 50 3 100 5" stroke="#3498db" stroke-width="2" fill="none"/>
                        <path d="M -100 0 Q -50 2 0 5 Q 50 8 100 10" stroke="#27ae60" stroke-width="2" fill="none"/>

                        <!-- Asymptote y = 25 -->
                        <line x1="-100" y1="15" x2="100" y2="15" stroke="#95a5a6" stroke-width="1" stroke-dasharray="5,5"/>
                        <text x="105" y="18" font-size="10" fill="#7f8c8d">y = 25</text>
                    </g>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="important-box">
                <h4>⚠️ Important Notes</h4>
                <ul>
                    <li><strong>Constant Solutions:</strong> If $g(a) = 0$ for some value $a$, then $y(t) = a$ is a constant solution</li>
                    <li><strong>Domain Considerations:</strong> The solution is valid where $g(y) \neq 0$</li>
                    <li><strong>Integration Constants:</strong> Don't forget the constant of integration!</li>
                </ul>
            </div>
        </section>

        <!-- Section 4: Separable Equations -->
        <section class="section" id="separable">
            <h2>🔄 4. Separable Differential Equations</h2>

            <div class="definition-box">
                <h4>📝 Definition: Separable Differential Equation</h4>
                <p>A first-order differential equation is <strong>separable</strong> if it can be written in the form:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = f(t) \cdot g(y)$$
                </div>
                <p>where $f(t)$ depends only on $t$ and $g(y)$ depends only on $y$.</p>
            </div>

            <div class="method-box">
                <h4>🛠️ Method: Separation of Variables</h4>
                <p>To solve a separable equation $\frac{dy}{dt} = f(t) \cdot g(y)$:</p>
                <ol class="step-list">
                    <li>Separate the variables: $\frac{dy}{g(y)} = f(t) \, dt$</li>
                    <li>Integrate both sides: $\int \frac{dy}{g(y)} = \int f(t) \, dt$</li>
                    <li>If antiderivatives exist and we can solve for $y$, then: $G(y) = F(t) + C$</li>
                </ol>
                <p>where $G$ and $F$ are antiderivatives of $\frac{1}{g}$ and $f$ respectively.</p>
            </div>

            <div class="example-box">
                <h4>💡 Example: Solving a Separable Equation</h4>
                <p>Solve: $\frac{dy}{dt} = 2t(25 - y)$</p>
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Identify: $f(t) = 2t$ and $g(y) = 25 - y$</li>
                    <li>Separate variables: $\frac{dy}{25 - y} = 2t \, dt$</li>
                    <li>Integrate both sides: $\int \frac{dy}{25 - y} = \int 2t \, dt$</li>
                    <li>Evaluate: $-\ln|25 - y| = t^2 + C$</li>
                    <li>Solve for $y$: $25 - y = Ae^{-t^2}$, so $y = 25 - Ae^{-t^2}$</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Separation of Variables Process
                    </text>

                    <!-- Step 1: Original equation -->
                    <g transform="translate(100, 70)">
                        <rect width="150" height="60" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Original Equation</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">dy/dt = f(t)g(y)</text>
                    </g>

                    <!-- Arrow 1 -->
                    <g transform="translate(270, 100)">
                        <path d="M 0 0 L 30 0" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="15" y="-10" text-anchor="middle" font-size="10" fill="#7f8c8d">separate</text>
                    </g>

                    <!-- Step 2: Separated form -->
                    <g transform="translate(320, 70)">
                        <rect width="150" height="60" fill="#fff9e6" stroke="#f39c12" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Separated Form</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">dy/g(y) = f(t)dt</text>
                    </g>

                    <!-- Arrow 2 -->
                    <g transform="translate(220, 150)">
                        <path d="M 0 0 L 0 30" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="-20" y="15" text-anchor="middle" font-size="10" fill="#7f8c8d">integrate</text>
                    </g>

                    <!-- Step 3: Integrated form -->
                    <g transform="translate(150, 200)">
                        <rect width="150" height="60" fill="#e8f8f5" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Integrated Form</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">G(y) = F(t) + C</text>
                    </g>

                    <!-- Arrow 3 -->
                    <g transform="translate(320, 230)">
                        <path d="M 0 0 L 30 0" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="15" y="-10" text-anchor="middle" font-size="10" fill="#7f8c8d">solve for y</text>
                    </g>

                    <!-- Step 4: Final solution -->
                    <g transform="translate(370, 200)">
                        <rect width="150" height="60" fill="#fdf2f2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="75" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Solution</text>
                        <text x="75" y="40" text-anchor="middle" font-size="14" fill="#2c3e50">y = solution(t)</text>
                    </g>

                    <!-- Example visualization -->
                    <g transform="translate(50, 300)">
                        <text x="250" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                            Example: dy/dt = 2t(25-y)
                        </text>

                        <!-- Steps for example -->
                        <g transform="translate(0, 30)">
                            <text x="0" y="0" font-size="12" fill="#3498db">1. dy/(25-y) = 2t dt</text>
                            <text x="200" y="0" font-size="12" fill="#f39c12">2. ∫dy/(25-y) = ∫2t dt</text>
                            <text x="0" y="20" font-size="12" fill="#27ae60">3. -ln|25-y| = t² + C</text>
                            <text x="200" y="20" font-size="12" fill="#e74c3c">4. y = 25 - Ae^(-t²)</text>
                        </g>
                    </g>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <div class="important-box">
                <h4>⚠️ Important Notes</h4>
                <ul>
                    <li><strong>Constant Solutions:</strong> If $g(a) = 0$ for some value $a$, then $y(t) = a$ is a constant solution</li>
                    <li><strong>Domain Restrictions:</strong> Solutions are valid only where $g(y) \neq 0$</li>
                    <li><strong>Integration:</strong> Both integrals must exist for the method to work</li>
                </ul>
            </div>
        </section>

        <!-- Section 5: Growth and Decay Models -->
        <section class="section" id="growth-decay">
            <h2>📈 5. Simple Growth and Decay Models</h2>

            <div class="definition-box">
                <h4>📝 Simple Growth and Decay Model</h4>
                <p>The differential equation:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = ky$$
                </div>
                <p>models simple growth and decay where:</p>
                <ul>
                    <li><strong>$k > 0$:</strong> Exponential growth</li>
                    <li><strong>$k < 0$:</strong> Exponential decay</li>
                    <li><strong>$k$:</strong> Growth rate constant</li>
                </ul>
                <p><strong>General Solution:</strong></p>
                <div class="math-display">
                    $$y(t) = y_0 e^{kt}$$
                </div>
                <p>where $y_0$ is the initial value at $t = 0$.</p>
            </div>

            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Growth and Decay: y = y₀e^(kt)
                    </text>

                    <!-- Coordinate system -->
                    <g transform="translate(300, 200)">
                        <!-- Axes -->
                        <line x1="-250" y1="0" x2="250" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-150" x2="0" y2="150" stroke="#34495e" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="240" y="-10" font-size="12" fill="#2c3e50">t</text>
                        <text x="10" y="-140" font-size="12" fill="#2c3e50">y</text>

                        <!-- Grid lines -->
                        <g stroke="#bdc3c7" stroke-width="0.5" opacity="0.5">
                            <line x1="-250" y1="-100" x2="250" y2="-100"/>
                            <line x1="-250" y1="-50" x2="250" y2="-50"/>
                            <line x1="-250" y1="50" x2="250" y2="50"/>
                            <line x1="-250" y1="100" x2="250" y2="100"/>
                            <line x1="-200" y1="-150" x2="-200" y2="150"/>
                            <line x1="-100" y1="-150" x2="-100" y2="150"/>
                            <line x1="100" y1="-150" x2="100" y2="150"/>
                            <line x1="200" y1="-150" x2="200" y2="150"/>
                        </g>

                        <!-- Growth curves (k > 0) -->
                        <g>
                            <!-- k = 0.5 -->
                            <path d="M -200 -25 Q -150 -35 -100 -45 Q -50 -60 0 -75 Q 50 -95 100 -120 Q 150 -145 200 -175"
                                  stroke="#27ae60" stroke-width="3" fill="none"/>
                            <text x="210" y="-170" font-size="10" fill="#27ae60">k = 0.5</text>

                            <!-- k = 0.3 -->
                            <path d="M -200 -35 Q -150 -40 -100 -50 Q -50 -60 0 -75 Q 50 -90 100 -110 Q 150 -130 200 -155"
                                  stroke="#2ecc71" stroke-width="3" fill="none"/>
                            <text x="210" y="-150" font-size="10" fill="#2ecc71">k = 0.3</text>

                            <!-- k = 0.1 -->
                            <path d="M -200 -65 Q -150 -68 -100 -70 Q -50 -72 0 -75 Q 50 -78 100 -82 Q 150 -86 200 -90"
                                  stroke="#58d68d" stroke-width="3" fill="none"/>
                            <text x="210" y="-85" font-size="10" fill="#58d68d">k = 0.1</text>
                        </g>

                        <!-- Decay curves (k < 0) -->
                        <g>
                            <!-- k = -0.5 -->
                            <path d="M -200 175 Q -150 145 -100 120 Q -50 95 0 75 Q 50 60 100 45 Q 150 35 200 25"
                                  stroke="#e74c3c" stroke-width="3" fill="none"/>
                            <text x="210" y="30" font-size="10" fill="#e74c3c">k = -0.5</text>

                            <!-- k = -0.3 -->
                            <path d="M -200 155 Q -150 130 -100 110 Q -50 90 0 75 Q 50 65 100 55 Q 150 45 200 40"
                                  stroke="#ec7063" stroke-width="3" fill="none"/>
                            <text x="210" y="45" font-size="10" fill="#ec7063">k = -0.3</text>

                            <!-- k = -0.1 -->
                            <path d="M -200 90 Q -150 86 -100 82 Q -50 78 0 75 Q 50 72 100 70 Q 150 68 200 65"
                                  stroke="#f1948a" stroke-width="3" fill="none"/>
                            <text x="210" y="70" font-size="10" fill="#f1948a">k = -0.1</text>
                        </g>

                        <!-- Initial value line -->
                        <line x1="-250" y1="75" x2="250" y2="75" stroke="#f39c12" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="-240" y="70" font-size="10" fill="#f39c12">y₀</text>

                        <!-- Tick marks -->
                        <g stroke="#2c3e50" stroke-width="1">
                            <line x1="-200" y1="-5" x2="-200" y2="5"/>
                            <line x1="-100" y1="-5" x2="-100" y2="5"/>
                            <line x1="100" y1="-5" x2="100" y2="5"/>
                            <line x1="200" y1="-5" x2="200" y2="5"/>
                            <line x1="-5" y1="-100" x2="5" y2="-100"/>
                            <line x1="-5" y1="-50" x2="5" y2="-50"/>
                            <line x1="-5" y1="50" x2="5" y2="50"/>
                            <line x1="-5" y1="100" x2="5" y2="100"/>
                        </g>

                        <!-- Tick labels -->
                        <text x="-200" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-2</text>
                        <text x="-100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-1</text>
                        <text x="100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>
                        <text x="200" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                        <text x="-15" y="-95" text-anchor="middle" font-size="10" fill="#2c3e50">2y₀</text>
                        <text x="-15" y="-45" text-anchor="middle" font-size="10" fill="#2c3e50">1.5y₀</text>
                        <text x="-15" y="55" text-anchor="middle" font-size="10" fill="#2c3e50">0.5y₀</text>
                        <text x="-15" y="105" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                    </g>

                    <!-- Legend -->
                    <g transform="translate(50, 350)">
                        <text x="0" y="0" font-size="12" fill="#27ae60" font-weight="bold">Growth (k > 0)</text>
                        <text x="150" y="0" font-size="12" fill="#e74c3c" font-weight="bold">Decay (k < 0)</text>
                        <line x1="300" y1="-5" x2="320" y2="-5" stroke="#f39c12" stroke-width="2" stroke-dasharray="3,3"/>
                        <text x="325" y="0" font-size="12" fill="#f39c12">Initial value y₀</text>
                    </g>
                </svg>
            </div>

            <div class="example-box">
                <h4>💡 Example: Compound Interest</h4>
                <p>Suppose $5,000 is deposited into an account with continuously compounded interest. After 4 years, the account is worth $7,000.</p>
                <p><strong>Questions:</strong></p>
                <ol>
                    <li>How much is the account worth after 5 years?</li>
                    <li>How long does it take for the balance to double?</li>
                </ol>
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Set up the model: $\frac{dP}{dt} = kP$ with $P(0) = 5000$</li>
                    <li>General solution: $P(t) = 5000e^{kt}$</li>
                    <li>Use given condition: $P(4) = 7000$, so $7000 = 5000e^{4k}$</li>
                    <li>Solve for $k$: $e^{4k} = 1.4$, so $k = \frac{\ln(1.4)}{4} \approx 0.0845$</li>
                    <li>After 5 years: $P(5) = 5000e^{5k} = 5000e^{5 \cdot 0.0845} \approx \$7,649$</li>
                    <li>To double: $10000 = 5000e^{kt}$, so $t = \frac{\ln(2)}{k} \approx 8.2$ years</li>
                </ol>
            </div>

            <div class="important-box">
                <h4>🌟 Applications</h4>
                <ul>
                    <li><strong>Population Growth:</strong> Bacterial cultures, early population growth</li>
                    <li><strong>Radioactive Decay:</strong> Carbon-14 dating, nuclear physics</li>
                    <li><strong>Finance:</strong> Continuous compound interest</li>
                    <li><strong>Physics:</strong> Cooling/heating processes (Newton's law)</li>
                    <li><strong>Chemistry:</strong> First-order reaction kinetics</li>
                </ul>
            </div>
        </section>

        <!-- Section 6: Logistic Growth Model -->
        <section class="section" id="logistic">
            <h2>📊 6. Logistic Growth Model</h2>

            <p>The simple growth model is unrealistic because real populations don't grow without limits. The <strong>logistic growth model</strong> accounts for environmental constraints.</p>

            <div class="definition-box">
                <h4>📝 Logistic Growth Model</h4>
                <p>The differential equation:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = ry(M - y)$$
                </div>
                <p>where:</p>
                <ul>
                    <li><strong>$r > 0$:</strong> Growth rate constant</li>
                    <li><strong>$M > 0$:</strong> Carrying capacity (maximum sustainable population)</li>
                    <li><strong>$y(t)$:</strong> Population at time $t$</li>
                </ul>
                <p><strong>Solution:</strong></p>
                <div class="math-display">
                    $$y(t) = \frac{M}{1 + Ae^{-rMt}}$$
                </div>
                <p>where $A = \frac{M - y_0}{y_0}$ and $y_0$ is the initial population.</p>
            </div>

            <div class="method-box">
                <h4>🛠️ Solving the Logistic Equation</h4>
                <p>The logistic equation is separable. Here's how to solve it:</p>
                <ol class="step-list">
                    <li>Separate variables: $\frac{dy}{y(M-y)} = r \, dt$</li>
                    <li>Use partial fractions: $\frac{1}{y(M-y)} = \frac{1}{M}\left(\frac{1}{y} + \frac{1}{M-y}\right)$</li>
                    <li>Integrate: $\frac{1}{M}\int\left(\frac{1}{y} + \frac{1}{M-y}\right)dy = \int r \, dt$</li>
                    <li>Result: $\frac{1}{M}[\ln|y| - \ln|M-y|] = rt + C$</li>
                    <li>Solve for $y$ to get the final solution</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="600" height="450" viewBox="0 0 600 450">
                    <!-- Background -->
                    <rect width="600" height="450" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Logistic Growth: S-Curve Behavior
                    </text>

                    <!-- Coordinate system -->
                    <g transform="translate(300, 250)">
                        <!-- Axes -->
                        <line x1="-250" y1="0" x2="250" y2="0" stroke="#34495e" stroke-width="2"/>
                        <line x1="0" y1="-180" x2="0" y2="180" stroke="#34495e" stroke-width="2"/>

                        <!-- Axis labels -->
                        <text x="240" y="-10" font-size="12" fill="#2c3e50">t</text>
                        <text x="10" y="-170" font-size="12" fill="#2c3e50">y</text>

                        <!-- Grid lines -->
                        <g stroke="#bdc3c7" stroke-width="0.5" opacity="0.5">
                            <line x1="-250" y1="-150" x2="250" y2="-150"/>
                            <line x1="-250" y1="-100" x2="250" y2="-100"/>
                            <line x1="-250" y1="-50" x2="250" y2="-50"/>
                            <line x1="-250" y1="50" x2="250" y2="50"/>
                            <line x1="-250" y1="100" x2="250" y2="100"/>
                            <line x1="-250" y1="150" x2="250" y2="150"/>
                            <line x1="-200" y1="-180" x2="-200" y2="180"/>
                            <line x1="-100" y1="-180" x2="-100" y2="180"/>
                            <line x1="100" y1="-180" x2="100" y2="180"/>
                            <line x1="200" y1="-180" x2="200" y2="180"/>
                        </g>

                        <!-- Carrying capacity line -->
                        <line x1="-250" y1="-150" x2="250" y2="-150" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                        <text x="-240" y="-155" font-size="12" fill="#e74c3c" font-weight="bold">M (carrying capacity)</text>

                        <!-- Logistic curves for different initial conditions -->
                        <!-- y₀ < M/2 -->
                        <path d="M -200 140 Q -150 120 -100 80 Q -50 20 0 -50 Q 50 -100 100 -130 Q 150 -145 200 -148"
                              stroke="#27ae60" stroke-width="4" fill="none"/>
                        <text x="210" y="-140" font-size="10" fill="#27ae60">y₀ < M/2</text>

                        <!-- y₀ = M/2 -->
                        <path d="M -200 -75 Q -150 -85 -100 -100 Q -50 -120 0 -135 Q 50 -145 100 -148 Q 150 -149 200 -149.5"
                              stroke="#3498db" stroke-width="4" fill="none"/>
                        <text x="210" y="-120" font-size="10" fill="#3498db">y₀ = M/2</text>

                        <!-- y₀ > M/2 -->
                        <path d="M -200 -120 Q -150 -125 -100 -130 Q -50 -135 0 -140 Q 50 -145 100 -148 Q 150 -149 200 -149.5"
                              stroke="#9b59b6" stroke-width="4" fill="none"/>
                        <text x="210" y="-100" font-size="10" fill="#9b59b6">y₀ > M/2</text>

                        <!-- y₀ > M (decay to M) -->
                        <path d="M -200 -170 Q -150 -165 -100 -160 Q -50 -155 0 -152 Q 50 -150 100 -149 Q 150 -148.5 200 -148"
                              stroke="#f39c12" stroke-width="4" fill="none"/>
                        <text x="210" y="-80" font-size="10" fill="#f39c12">y₀ > M</text>

                        <!-- M/2 line -->
                        <line x1="-250" y1="-75" x2="250" y2="-75" stroke="#95a5a6" stroke-width="1" stroke-dasharray="3,3"/>
                        <text x="-240" y="-80" font-size="10" fill="#95a5a6">M/2</text>

                        <!-- Inflection point indicator -->
                        <circle cx="-50" cy="-75" r="4" fill="#e67e22" stroke="#d35400" stroke-width="2"/>
                        <text x="-45" y="-60" font-size="10" fill="#e67e22">Inflection point</text>

                        <!-- Tick marks -->
                        <g stroke="#2c3e50" stroke-width="1">
                            <line x1="-200" y1="-5" x2="-200" y2="5"/>
                            <line x1="-100" y1="-5" x2="-100" y2="5"/>
                            <line x1="100" y1="-5" x2="100" y2="5"/>
                            <line x1="200" y1="-5" x2="200" y2="5"/>
                            <line x1="-5" y1="-150" x2="5" y2="-150"/>
                            <line x1="-5" y1="-75" x2="5" y2="-75"/>
                            <line x1="-5" y1="0" x2="5" y2="0"/>
                            <line x1="-5" y1="150" x2="5" y2="150"/>
                        </g>

                        <!-- Tick labels -->
                        <text x="-200" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-2</text>
                        <text x="-100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">-1</text>
                        <text x="100" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">1</text>
                        <text x="200" y="20" text-anchor="middle" font-size="10" fill="#2c3e50">2</text>
                        <text x="-15" y="-145" text-anchor="middle" font-size="10" fill="#2c3e50">M</text>
                        <text x="-15" y="-70" text-anchor="middle" font-size="10" fill="#2c3e50">M/2</text>
                        <text x="-15" y="5" text-anchor="middle" font-size="10" fill="#2c3e50">0</text>
                    </g>

                    <!-- Phase analysis -->
                    <g transform="translate(50, 400)">
                        <text x="0" y="0" font-size="14" font-weight="bold" fill="#2c3e50">Growth Rate Analysis:</text>
                        <text x="200" y="0" font-size="12" fill="#27ae60">• y < M/2: Accelerating growth</text>
                        <text x="200" y="15" font-size="12" fill="#3498db">• y = M/2: Maximum growth rate</text>
                        <text x="200" y="30" font-size="12" fill="#9b59b6">• y > M/2: Decelerating growth</text>
                    </g>
                </svg>
            </div>

            <div class="important-box">
                <h4>🔍 Key Features of Logistic Growth</h4>
                <ul>
                    <li><strong>S-shaped curve:</strong> Characteristic sigmoid shape</li>
                    <li><strong>Three phases:</strong> Slow start → rapid growth → leveling off</li>
                    <li><strong>Inflection point:</strong> At $y = M/2$, where growth rate is maximum</li>
                    <li><strong>Carrying capacity:</strong> $\lim_{t \to \infty} y(t) = M$</li>
                    <li><strong>Environmental limits:</strong> Growth slows as resources become scarce</li>
                </ul>
            </div>
        </section>

        <!-- Section 7: Linear Differential Equations -->
        <section class="section" id="linear">
            <h2>📐 7. First-Order Linear Differential Equations</h2>

            <div class="definition-box">
                <h4>📝 First-Order Linear DE (Standard Form)</h4>
                <p>A first-order linear differential equation has the form:</p>
                <div class="math-display">
                    $$y' + p(t)y = f(t)$$
                </div>
                <ul>
                    <li><strong>Homogeneous:</strong> $f(t) = 0$ → $y' + p(t)y = 0$</li>
                    <li><strong>Non-homogeneous:</strong> $f(t) \neq 0$ → $y' + p(t)y = f(t)$</li>
                </ul>
            </div>

            <h3>Homogeneous Linear Equations</h3>
            <div class="method-box">
                <h4>🛠️ Solving $y' + p(t)y = 0$</h4>
                <p>This is separable:</p>
                <ol class="step-list">
                    <li>Rewrite as: $\frac{dy}{dt} = -p(t)y$</li>
                    <li>Separate: $\frac{dy}{y} = -p(t) \, dt$</li>
                    <li>Integrate: $\ln|y| = -\int p(t) \, dt + C$</li>
                    <li>Solution: $y = Ae^{-\int p(t) \, dt}$</li>
                </ol>
            </div>

            <h3>Non-Homogeneous Linear Equations</h3>
            <div class="method-box">
                <h4>🛠️ Method 1: Integrating Factor</h4>
                <p>For $y' + p(t)y = f(t)$:</p>
                <ol class="step-list">
                    <li>Find integrating factor: $I(t) = e^{\int p(t) \, dt}$</li>
                    <li>Multiply equation by $I(t)$: $I(t)y' + I(t)p(t)y = I(t)f(t)$</li>
                    <li>Left side becomes: $\frac{d}{dt}[I(t)y] = I(t)f(t)$</li>
                    <li>Integrate: $I(t)y = \int I(t)f(t) \, dt + C$</li>
                    <li>Solve for $y$: $y = \frac{1}{I(t)}\left[\int I(t)f(t) \, dt + C\right]$</li>
                </ol>
            </div>

            <div class="example-box">
                <h4>💡 Example: Using Integrating Factor</h4>
                <p>Solve: $y' + \frac{3y}{t} = t^2$, with $y(1) = \frac{1}{2}$</p>
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Identify: $p(t) = \frac{3}{t}$, $f(t) = t^2$</li>
                    <li>Integrating factor: $I(t) = e^{\int \frac{3}{t} dt} = e^{3\ln|t|} = t^3$</li>
                    <li>Multiply by $I(t)$: $t^3y' + 3t^2y = t^5$</li>
                    <li>Recognize: $\frac{d}{dt}[t^3y] = t^5$</li>
                    <li>Integrate: $t^3y = \int t^5 dt = \frac{t^6}{6} + C$</li>
                    <li>General solution: $y = \frac{t^3}{6} + \frac{C}{t^3}$</li>
                    <li>Apply initial condition: $\frac{1}{2} = \frac{1}{6} + C$, so $C = \frac{1}{3}$</li>
                    <li>Specific solution: $y = \frac{t^3}{6} + \frac{1}{3t^3}$</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="600" height="350" viewBox="0 0 600 350">
                    <!-- Background -->
                    <rect width="600" height="350" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Integrating Factor Method Visualization
                    </text>

                    <!-- Step-by-step process -->
                    <g transform="translate(50, 60)">
                        <!-- Step 1 -->
                        <rect width="120" height="50" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="5"/>
                        <text x="60" y="20" text-anchor="middle" font-size="10" font-weight="bold" fill="#2c3e50">Original Equation</text>
                        <text x="60" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">y' + p(t)y = f(t)</text>

                        <!-- Arrow 1 -->
                        <path d="M 130 25 L 160 25" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="145" y="15" text-anchor="middle" font-size="8" fill="#7f8c8d">find I(t)</text>

                        <!-- Step 2 -->
                        <rect x="170" y="0" width="120" height="50" fill="#fff9e6" stroke="#f39c12" stroke-width="2" rx="5"/>
                        <text x="230" y="20" text-anchor="middle" font-size="10" font-weight="bold" fill="#2c3e50">Integrating Factor</text>
                        <text x="230" y="35" text-anchor="middle" font-size="10" fill="#2c3e50">I(t) = e^∫p(t)dt</text>

                        <!-- Arrow 2 -->
                        <path d="M 230 60 L 230 90" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="250" y="75" text-anchor="middle" font-size="8" fill="#7f8c8d">multiply</text>

                        <!-- Step 3 -->
                        <rect x="170" y="100" width="120" height="50" fill="#e8f8f5" stroke="#27ae60" stroke-width="2" rx="5"/>
                        <text x="230" y="120" text-anchor="middle" font-size="10" font-weight="bold" fill="#2c3e50">Modified Equation</text>
                        <text x="230" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">I(t)y' + I(t)p(t)y = I(t)f(t)</text>

                        <!-- Arrow 3 -->
                        <path d="M 160 125 L 130 125" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="145" y="115" text-anchor="middle" font-size="8" fill="#7f8c8d">recognize</text>

                        <!-- Step 4 -->
                        <rect x="10" y="100" width="120" height="50" fill="#fdf2f2" stroke="#e74c3c" stroke-width="2" rx="5"/>
                        <text x="70" y="120" text-anchor="middle" font-size="10" font-weight="bold" fill="#2c3e50">Product Rule Form</text>
                        <text x="70" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">d/dt[I(t)y] = I(t)f(t)</text>

                        <!-- Arrow 4 -->
                        <path d="M 70 160 L 70 190" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="90" y="175" text-anchor="middle" font-size="8" fill="#7f8c8d">integrate</text>

                        <!-- Step 5 -->
                        <rect x="10" y="200" width="120" height="50" fill="#f4f1fb" stroke="#9b59b6" stroke-width="2" rx="5"/>
                        <text x="70" y="220" text-anchor="middle" font-size="10" font-weight="bold" fill="#2c3e50">Integrated Form</text>
                        <text x="70" y="235" text-anchor="middle" font-size="10" fill="#2c3e50">I(t)y = ∫I(t)f(t)dt + C</text>

                        <!-- Arrow 5 -->
                        <path d="M 140 225 L 170 225" stroke="#34495e" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <text x="155" y="215" text-anchor="middle" font-size="8" fill="#7f8c8d">solve for y</text>

                        <!-- Step 6 -->
                        <rect x="180" y="200" width="120" height="50" fill="#e8f6f3" stroke="#1abc9c" stroke-width="2" rx="5"/>
                        <text x="240" y="220" text-anchor="middle" font-size="10" font-weight="bold" fill="#2c3e50">Final Solution</text>
                        <text x="240" y="235" text-anchor="middle" font-size="10" fill="#2c3e50">y = [∫I(t)f(t)dt + C]/I(t)</text>
                    </g>

                    <!-- Key insight box -->
                    <g transform="translate(350, 80)">
                        <rect width="200" height="120" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="5"/>
                        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">Key Insight</text>
                        <text x="10" y="40" font-size="10" fill="#856404">The integrating factor I(t)</text>
                        <text x="10" y="55" font-size="10" fill="#856404">transforms the left side into</text>
                        <text x="10" y="70" font-size="10" fill="#856404">a perfect derivative:</text>
                        <text x="10" y="90" font-size="10" fill="#856404">I(t)y' + I(t)p(t)y = d/dt[I(t)y]</text>
                        <text x="10" y="110" font-size="10" fill="#856404">This makes integration possible!</text>
                    </g>

                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
                        </marker>
                    </defs>
                </svg>
            </div>
        </section>

        <!-- Section 8: Summary -->
        <section class="section" id="summary">
            <h2>📋 8. Summary and Solution Strategy</h2>

            <div class="method-box">
                <h4>🎯 Solution Strategy for First-Order DEs</h4>
                <ol class="step-list">
                    <li><strong>Identify the type:</strong> Separable, linear, or special form?</li>
                    <li><strong>Choose the method:</strong> Separation of variables, integrating factor, or substitution</li>
                    <li><strong>Solve the equation:</strong> Apply the chosen method systematically</li>
                    <li><strong>Apply initial conditions:</strong> Find specific solutions when given</li>
                    <li><strong>Check your answer:</strong> Substitute back into the original equation</li>
                </ol>
            </div>

            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>

                    <!-- Title -->
                    <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        First-Order DE Classification and Methods
                    </text>

                    <!-- Decision tree -->
                    <g transform="translate(300, 60)">
                        <!-- Root -->
                        <rect x="-60" y="0" width="120" height="40" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                        <text x="0" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="white">First-Order DE</text>

                        <!-- Branches -->
                        <line x1="-30" y1="40" x2="-120" y2="80" stroke="#34495e" stroke-width="2"/>
                        <line x1="30" y1="40" x2="120" y2="80" stroke="#34495e" stroke-width="2"/>

                        <!-- Separable branch -->
                        <rect x="-180" y="80" width="120" height="40" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                        <text x="-120" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Separable</text>
                        <text x="-120" y="140" text-anchor="middle" font-size="10" fill="#2c3e50">dy/dt = f(t)g(y)</text>
                        <text x="-120" y="155" text-anchor="middle" font-size="10" fill="#27ae60" font-weight="bold">→ Separation of Variables</text>

                        <!-- Linear branch -->
                        <rect x="60" y="80" width="120" height="40" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
                        <text x="120" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Linear</text>
                        <text x="120" y="140" text-anchor="middle" font-size="10" fill="#2c3e50">y' + p(t)y = f(t)</text>

                        <!-- Linear sub-branches -->
                        <line x1="90" y1="120" x2="30" y2="160" stroke="#34495e" stroke-width="1"/>
                        <line x1="150" y1="120" x2="210" y2="160" stroke="#34495e" stroke-width="1"/>

                        <!-- Homogeneous -->
                        <rect x="-30" y="160" width="120" height="30" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="3"/>
                        <text x="30" y="180" text-anchor="middle" font-size="10" fill="white">Homogeneous (f=0)</text>
                        <text x="30" y="200" text-anchor="middle" font-size="9" fill="#f39c12">→ Separable</text>

                        <!-- Non-homogeneous -->
                        <rect x="150" y="160" width="120" height="30" fill="#9b59b6" stroke="#8e44ad" stroke-width="1" rx="3"/>
                        <text x="210" y="180" text-anchor="middle" font-size="10" fill="white">Non-homogeneous</text>
                        <text x="210" y="200" text-anchor="middle" font-size="9" fill="#9b59b6">→ Integrating Factor</text>
                    </g>

                    <!-- Examples box -->
                    <g transform="translate(50, 230)">
                        <text x="0" y="0" font-size="12" font-weight="bold" fill="#2c3e50">Common Examples:</text>
                        <text x="0" y="20" font-size="10" fill="#27ae60">• dy/dt = ky (Growth/Decay)</text>
                        <text x="0" y="35" font-size="10" fill="#27ae60">• dy/dt = ry(M-y) (Logistic)</text>
                        <text x="250" y="20" font-size="10" fill="#e74c3c">• y' + ay = b (Linear)</text>
                        <text x="250" y="35" font-size="10" fill="#e74c3c">• ty' + 3y = t² (Linear)</text>
                    </g>
                </svg>
            </div>

            <div class="important-box">
                <h4>🎓 Key Takeaways</h4>
                <ul>
                    <li><strong>Classification is crucial:</strong> Identify the type before choosing a solution method</li>
                    <li><strong>Separable equations:</strong> Use separation of variables when possible</li>
                    <li><strong>Linear equations:</strong> Use integrating factor for systematic solutions</li>
                    <li><strong>Initial conditions:</strong> Determine specific solutions from general families</li>
                    <li><strong>Applications:</strong> Model real-world phenomena with appropriate DE types</li>
                    <li><strong>Verification:</strong> Always check solutions by substitution</li>
                </ul>
            </div>
        </section>

        <footer style="text-align: center; padding: 2rem; background: var(--primary-color); color: white; margin-top: 2rem; border-radius: 10px;">
            <h3>🎉 Congratulations!</h3>
            <p>You've completed the First-Order Differential Equations tutorial!</p>
            <p style="font-size: 0.9rem; opacity: 0.8;">Practice with various problems to master these concepts.</p>
        </footer>
    </div>
</body>
</html>
