<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Variational Autoencoders (VAE) - Complete Tutorial</title>
    
    <!-- MathJax 3 configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <!-- Plotly.js for interactive plots -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 3em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
            padding-left: 20px;
            font-size: 2em;
        }
        
        h3 {
            color: #2980b9;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        p {
            margin-bottom: 15px;
            text-align: justify;
            font-size: 1.1em;
        }
        
        .highlight {
            background: linear-gradient(120deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        
        .math-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .code-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
        }
        
        .visualization {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #667eea;
        }
        
        .key-concept {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(155, 89, 182, 0.1) 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        
        .warning {
            background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(230, 126, 34, 0.1) 100%);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #e74c3c;
        }
        
        .outline {
            background: #f1f2f6;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border: 2px solid #667eea;
        }
        
        .outline h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .outline ul {
            list-style: none;
            padding: 0;
        }
        
        .outline li {
            margin: 8px 0;
            padding: 8px 15px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #667eea;
        }
        
        .section-divider {
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            margin: 40px 0;
            border-radius: 1px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .plotly-graph-div {
            width: 100% !important;
            height: 400px !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Variational Autoencoders (VAE)<br><span style="font-size: 0.6em; color: #7f8c8d;">A Complete Tutorial</span></h1>
        
        <div class="outline">
            <h3>📋 Tutorial Outline</h3>
            <ul>
                <li>🎯 <strong>Introduction & Motivation</strong> - Understanding the need for latent variable models</li>
                <li>📐 <strong>Mathematical Foundation</strong> - Generative processes and probability distributions</li>
                <li>🔢 <strong>The High-Dimensional Challenge</strong> - Why simple approaches fail</li>
                <li>🧮 <strong>Variational Inference</strong> - The core mathematical framework</li>
                <li>🏗️ <strong>VAE Architecture</strong> - Encoder, decoder, and optimization</li>
                <li>⚠️ <strong>Advanced Topics</strong> - Posterior collapse and practical considerations</li>
            </ul>
        </div>
        
        <div class="section-divider"></div>
        
        <h2>🎯 Introduction & Motivation</h2>
        
        <p>Training generative models becomes increasingly challenging as the dependencies among the dimensions grow more complex. Imagine trying to teach a computer to generate realistic images, music, or even biological data - the challenge lies in capturing the intricate relationships between different features.</p>
        
        <div class="highlight">
            <h3>🧬 Real-World Example: Single Cell Analysis</h3>
            <p>Consider the task of generating transcriptomic profiles of single cells. If a partial profile suggests the presence of markers typical of a neuronal cell, the remaining profile cannot align with that of a muscle cell without resulting in an inaccurate or impossible cell type representation.</p>
        </div>
        
        <p>It is beneficial for the model to first determine the cell type it intends to generate before it begins to assign values to specific genes. This decision-making process involves a <strong>latent variable</strong>.</p>
        
        <div class="key-concept">
            <h3>💡 Key Concept: Latent Variables</h3>
            <p>In practice, before simulating any gene expression data, the model would randomly select a cell type or a cell state, represented by a latent variable $z$, ensuring the gene expression aligns consistently with the chosen cell type.</p>
            <p>The term "latent" is used because, from the generated profile alone, we cannot directly discern the latent variable settings that led to the production of that specific cell profile.</p>
        </div>
        
        <div class="visualization">
            <h3>🎨 Conceptual Visualization: Latent Variables in Action</h3>
            <svg width="100%" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="300" fill="url(#bgGrad)"/>
                
                <!-- Latent Space -->
                <circle cx="150" cy="150" r="80" fill="#667eea" opacity="0.3" stroke="#667eea" stroke-width="2"/>
                <text x="150" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">Latent Space (z)</text>
                <text x="150" y="100" text-anchor="middle" font-size="12" fill="#667eea">Cell Type Decision</text>
                
                <!-- Latent points -->
                <circle cx="130" cy="130" r="4" fill="#e74c3c"/>
                <text x="110" y="125" font-size="10" fill="#e74c3c">Neuron</text>
                <circle cx="170" cy="170" r="4" fill="#27ae60"/>
                <text x="175" y="175" font-size="10" fill="#27ae60">Muscle</text>
                <circle cx="150" cy="180" r="4" fill="#f39c12"/>
                <text x="125" y="195" font-size="10" fill="#f39c12">Immune</text>
                
                <!-- Arrow -->
                <path d="M 230 150 Q 350 100 470 150" stroke="#2c3e50" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
                <text x="350" y="120" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Generative Process</text>
                
                <!-- Data Space -->
                <rect x="520" y="70" width="160" height="160" fill="#f8f9fa" stroke="#764ba2" stroke-width="2" rx="10"/>
                <text x="600" y="50" text-anchor="middle" font-size="16" font-weight="bold" fill="#764ba2">Data Space (X)</text>
                <text x="600" y="65" text-anchor="middle" font-size="12" fill="#764ba2">Gene Expression</text>
                
                <!-- Gene expression visualization -->
                <rect x="530" y="90" width="8" height="40" fill="#e74c3c"/>
                <rect x="540" y="90" width="8" height="25" fill="#e74c3c"/>
                <rect x="550" y="90" width="8" height="35" fill="#e74c3c"/>
                <rect x="560" y="90" width="8" height="15" fill="#e74c3c"/>
                <rect x="570" y="90" width="8" height="30" fill="#e74c3c"/>
                <text x="550" y="145" font-size="10" fill="#e74c3c" text-anchor="middle">Neuronal Profile</text>
                
                <rect x="590" y="160" width="8" height="20" fill="#27ae60"/>
                <rect x="600" y="160" width="8" height="45" fill="#27ae60"/>
                <rect x="610" y="160" width="8" height="30" fill="#27ae60"/>
                <rect x="620" y="160" width="8" height="35" fill="#27ae60"/>
                <rect x="630" y="160" width="8" height="25" fill="#27ae60"/>
                <text x="610" y="215" font-size="10" fill="#27ae60" text-anchor="middle">Muscle Profile</text>
                
                <!-- Arrow marker -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                    </marker>
                </defs>
            </svg>
        </div>
        
        <div class="section-divider"></div>
        
        <h2>📐 Mathematical Foundation</h2>
        
        <p>To make this discussion mathematically precise, we need to understand the generative process. The probability of each data point $X$ in our training set can be derived using Bayes' rule:</p>
        
        <div class="math-container">
            $$P(X) = \int P(X, z; \theta)dz = \int P(X | z; \theta)P(z)dz$$
        </div>
        
        <p>During this generative process:</p>
        
        <div class="key-concept">
            <h3>🔄 The Generative Process</h3>
            <ol style="margin-left: 20px;">
                <li><strong>Step 1:</strong> The model first samples a vector of latent variables $z$ in a high-dimensional space $\mathcal{Z}$ according to some probability density function (PDF) $P(z)$</li>
                <li><strong>Step 2:</strong> We then use an output distribution $P(X | z; \theta)$ which depends on the value of $z$ to draw data points</li>
            </ol>
        </div>
        
        <h3>🎲 Choosing the Output Distribution</h3>
        
        <p>In variational autoencoders, the choice of output distribution $P(X | z)$ is often Gaussian:</p>
        
        <div class="math-container">
            $$P(X | z; \theta) = \mathcal{N}(X | f(z;\theta), \sigma^2 \cdot I)$$
        </div>
        
        <p>Where:</p>
        <ul style="margin-left: 30px; margin-bottom: 20px;">
            <li>$f(z;θ)$ is the mean function (typically a neural network)</li>
            <li>$\sigma^2 \cdot I$ is the covariance matrix (identity matrix times a scalar)</li>
            <li>$\theta$ represents the learnable parameters we want to optimize</li>
        </ul>
        
        <div class="warning">
            <h3>⚠️ Important Note</h3>
            <p>$P(X | z; \theta)$ doesn't need to be Gaussian! For binary data, it might be Bernoulli. The key requirement is that $P(X | z)$ can be computed and is continuous in $\theta$.</p>
        </div>
        
        <div class="visualization">
            <h3>🎯 Generative Process Visualization</h3>
            <svg width="100%" height="250" viewBox="0 0 900 250">
                <!-- Prior Distribution -->
                <ellipse cx="120" cy="125" rx="80" ry="40" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2"/>
                <text x="120" y="90" text-anchor="middle" font-size="14" font-weight="bold" fill="#3498db">P(z)</text>
                <text x="120" y="105" text-anchor="middle" font-size="11" fill="#3498db">Prior Distribution</text>
                <text x="120" y="170" text-anchor="middle" font-size="10" fill="#3498db">z ~ N(0, I)</text>
                
                <!-- Sample point -->
                <circle cx="150" cy="125" r="3" fill="#e74c3c"/>
                
                <!-- Arrow 1 -->
                <path d="M 200 125 L 280 125" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="240" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Sample z</text>
                
                <!-- Function f -->
                <rect x="300" y="100" width="120" height="50" fill="#9b59b6" opacity="0.8" rx="10"/>
                <text x="360" y="120" text-anchor="middle" font-size="13" font-weight="bold" fill="white">f(z; θ)</text>
                <text x="360" y="135" text-anchor="middle" font-size="10" fill="white">Neural Network</text>
                
                <!-- Arrow 2 -->
                <path d="M 420 125 L 500 125" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="460" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Transform</text>
                
                <!-- Output Distribution -->
                <ellipse cx="580" cy="125" rx="80" ry="40" fill="#27ae60" opacity="0.3" stroke="#27ae60" stroke-width="2"/>
                <text x="580" y="90" text-anchor="middle" font-size="14" font-weight="bold" fill="#27ae60">P(X|z)</text>
                <text x="580" y="105" text-anchor="middle" font-size="11" fill="#27ae60">Output Distribution</text>
                <text x="580" y="170" text-anchor="middle" font-size="10" fill="#27ae60">X ~ N(f(z), σ²I)</text>
                
                <!-- Arrow 3 -->
                <path d="M 660 125 L 740 125" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <text x="700" y="115" text-anchor="middle" font-size="11" fill="#2c3e50">Sample X</text>
                
                <!-- Generated Data -->
                <rect x="760" y="110" width="30" height="30" fill="#f39c12" opacity="0.8" rx="5"/>
                <text x="775" y="130" text-anchor="middle" font-size="16" fill="white">X</text>
                <text x="775" y="95" text-anchor="middle" font-size="12" fill="#f39c12">Generated Data</text>
                         </svg>
         </div>
         
         <h3>🧠 Key Insights about VAE Design</h3>
         
        <p>There are two remaining problems that VAEs must deal with:</p>
        <ol style="margin-left: 30px; margin-bottom: 20px;">
            <li><strong>How to define the latent variables $z$</strong> (i.e., decide what information they represent)</li>
            <li><strong>How to deal with the integral over $z$</strong></li>
        </ol>
        
        <div class="key-concept">
            <h3>🎯 VAE's Elegant Solution</h3>
            <p>The choice of $P(z)$ is <strong>unimportant</strong> because any distribution in $d$ dimensions can be generated by taking a set of $d$ variables that are normally distributed and mapping them through a sufficiently complicated function.</p>
            <p>Therefore, VAEs assert that samples of $z$ can be drawn from a simple distribution: $z \sim \mathcal{N}(0,I)$</p>
        </div>
        
        <div class="section-divider"></div>
        
        <h2>🔢 The High-Dimensional Challenge</h2>
        
        <p>Given $z \sim \mathcal{N}(0, I)$, all that remains is to maximize the likelihood $P(X)$. Conceptually, it's straightforward to compute $P(X)$ approximately:</p>
        
        <div class="math-container">
            $$P(X) \approx \frac{1}{n} \sum_{i=1}^{n} P(X | z_i)$$
        </div>
        
        <p>We first sample a large number of $z$ values ${z_1,...,z_n}$, and compute the average. Let's see this in action with a simple example:</p>
        
        <div class="highlight">
            <h3>📊 Simple 1D Example</h3>
            <p>Assume a latent space dimensionality of 1 for simplicity, where $z$ is a scalar sampled from $\mathcal{N}(0, 1)$. Let $X$ be one-dimensional observable data, and assume a simple generative model where $X$ given $z$ is normally distributed: $X | z \sim \mathcal{N}(f(z), 1)$.</p>
            <p>For simplicity, let's assume $f$ is linear: $f(z) = az + b$, where $a$ and $b$ are parameters to learn.</p>
        </div>
        
        <div class="code-container">
            <h3>💻 Python Implementation</h3>
            <pre style="margin: 0; color: #e2e8f0;">
<span style="color: #4299e1;">import</span> numpy <span style="color: #4299e1;">as</span> np
<span style="color: #4299e1;">from</span> scipy.stats <span style="color: #4299e1;">import</span> norm
<span style="color: #4299e1;">import</span> matplotlib.pyplot <span style="color: #4299e1;">as</span> plt

np.random.seed(<span style="color: #f6ad55;">0</span>)

<span style="color: #68d391;"># Parameters</span>
X = <span style="color: #f6ad55;">5</span>
n_samples = <span style="color: #f6ad55;">1000</span>
a = <span style="color: #f6ad55;">2.0</span>  <span style="color: #68d391;"># Initial guess for slope</span>
b = <span style="color: #f6ad55;">1.0</span>  <span style="color: #68d391;"># Initial guess for intercept</span>
sigma = <span style="color: #f6ad55;">1.0</span>  <span style="color: #68d391;"># standard deviation</span>

z_samples = np.random.normal(<span style="color: #f6ad55;">0</span>, <span style="color: #f6ad55;">1</span>, n_samples)

L = []
learning_rate = <span style="color: #f6ad55;">0.1</span>
delta = <span style="color: #4299e1;">None</span>
<span style="color: #4299e1;">while</span> delta <span style="color: #4299e1;">is</span> <span style="color: #4299e1;">None</span> <span style="color: #4299e1;">or</span> delta > <span style="color: #f6ad55;">0.000001</span>:
    f_z = a * z_samples + b

    <span style="color: #68d391;"># Compute gradients and update parameters</span>
    grad_a = np.mean((X - f_z) * z_samples / sigma**<span style="color: #f6ad55;">2</span>)
    grad_b = np.mean((X - f_z) / sigma**<span style="color: #f6ad55;">2</span>)
    a += learning_rate * grad_a
    b += learning_rate * grad_b

    log_prob_matrix = norm.logpdf(X, f_z, sigma)
    P_X = np.mean(log_prob_matrix)

    <span style="color: #4299e1;">if</span> len(L) > <span style="color: #f6ad55;">0</span>:
        delta = P_X - L[-<span style="color: #f6ad55;">1</span>]
    L.append(P_X)

<span style="color: #4299e1;">print</span>(<span style="color: #f093a0;">f'a = {a}, b = {b}'</span>)
            </pre>
        </div>
        
        <div class="visualization">
            <h3>📈 Monte Carlo Sampling Visualization</h3>
            <div id="montecarlo-plot" style="width: 100%; height: 400px;"></div>
        </div>
        
        <div class="warning">
            <h3>⚠️ The Curse of Dimensionality</h3>
            <p>The code above works well for 1D, but has severe limitations in high-dimensional data:</p>
            <ul style="margin-left: 20px;">
                <li><strong>Sparse Data:</strong> In high dimensions, data points tend to be sparse - most combinations of feature values are extremely unlikely</li>
                <li><strong>Diluted Probability:</strong> For a fixed amount of data, probability mass spreads thinner across the expanding volume</li>
                <li><strong>High Specificity:</strong> Only a few values of $z$ are good at explaining any given $X$, making $P(X | z)$ very low for most $z$ values</li>
            </ul>
        </div>
        
        <div class="visualization">
            <h3>🎯 Curse of Dimensionality Illustration</h3>
            <svg width="100%" height="350" viewBox="0 0 900 350">
                <!-- 1D Case -->
                <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">1D Case (Works Well)</text>
                <line x1="50" y1="70" x2="250" y2="70" stroke="#3498db" stroke-width="3"/>
                <circle cx="100" cy="70" r="4" fill="#e74c3c"/>
                <circle cx="120" cy="70" r="4" fill="#e74c3c"/>
                <circle cx="140" cy="70" r="4" fill="#e74c3c"/>
                <circle cx="160" cy="70" r="4" fill="#e74c3c"/>
                <circle cx="180" cy="70" r="4" fill="#e74c3c"/>
                <text x="150" y="95" text-anchor="middle" font-size="12" fill="#27ae60">Dense sampling possible</text>
                
                <!-- 2D Case -->
                <text x="450" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">2D Case (Challenging)</text>
                <rect x="350" y="50" width="200" height="120" fill="none" stroke="#3498db" stroke-width="2"/>
                <circle cx="380" cy="80" r="3" fill="#e74c3c"/>
                <circle cx="420" cy="110" r="3" fill="#e74c3c"/>
                <circle cx="460" cy="90" r="3" fill="#e74c3c"/>
                <circle cx="500" cy="140" r="3" fill="#e74c3c"/>
                <circle cx="520" cy="70" r="3" fill="#e74c3c"/>
                <text x="450" y="190" text-anchor="middle" font-size="12" fill="#f39c12">Sparse sampling</text>
                
                <!-- 3D Case -->
                <text x="750" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">High-D Case (Problematic)</text>
                <!-- 3D cube representation -->
                <path d="M 650 50 L 750 50 L 750 150 L 650 150 Z" fill="none" stroke="#3498db" stroke-width="2"/>
                <path d="M 680 20 L 780 20 L 780 120 L 680 120 Z" fill="none" stroke="#3498db" stroke-width="2"/>
                <path d="M 650 50 L 680 20" stroke="#3498db" stroke-width="2"/>
                <path d="M 750 50 L 780 20" stroke="#3498db" stroke-width="2"/>
                <path d="M 750 150 L 780 120" stroke="#3498db" stroke-width="2"/>
                <path d="M 650 150 L 680 120" stroke="#3498db" stroke-width="2"/>
                
                <circle cx="690" cy="100" r="2" fill="#e74c3c"/>
                <circle cx="740" cy="60" r="2" fill="#e74c3c"/>
                <text x="730" y="190" text-anchor="middle" font-size="12" fill="#e74c3c">Very sparse!</text>
                
                <!-- Explanation -->
                <text x="450" y="230" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Problem: Most z values contribute almost nothing to P(X)</text>
                <text x="450" y="250" text-anchor="middle" font-size="12" fill="#7f8c8d">We need to sample z values that are likely to have produced X</text>
                
                <!-- Solution hint -->
                <rect x="250" y="270" width="400" height="40" fill="#27ae60" opacity="0.1" stroke="#27ae60" stroke-width="2" rx="10"/>
                <text x="450" y="290" text-anchor="middle" font-size="13" font-weight="bold" fill="#27ae60">Solution: Sample from P(z|X) instead of P(z)</text>
                                 <text x="450" y="305" text-anchor="middle" font-size="11" fill="#27ae60">This is where Variational Inference comes in!</text>
             </svg>
         </div>
         
         <div class="section-divider"></div>
         
         <h2>🧮 Variational Inference</h2>
         
         <p>The key idea behind the variational autoencoder is to attempt to sample values of $z$ that are likely to have produced $X$, and compute $P(X)$ just from those. This means we need to be able to draw samples from $P(z | X)$ instead of $P(z)$.</p>
         
         <div class="key-concept">
            <h3>🎯 The Challenge</h3>
            <p>$P(z | X)$ is the <strong>posterior probability</strong> which is usually very difficult to compute directly. VAEs use a technique called <strong>variational inference</strong> to approximate this distribution.</p>
        </div>
        
        <h3>🔍 Variational Inference Framework</h3>
        
        <p>In variational inference, we first posit a family of approximate densities $\mathcal{Q}$. This is a set of densities over the latent variables. Then, we try to find the member of that family that minimizes the Kullback-Leibler (KL) divergence to the exact posterior:</p>
        
        <div class="math-container">
            $$Q^*(z) = \arg\min_{Q(z) \in \mathcal{Q}} \mathcal{D}[Q(z) || P(z | X)]$$
        </div>
        
        <h3>📐 Deriving the ELBO</h3>
        
        <p>We now expand the KL divergence term to understand what it's optimizing:</p>
        
        <div class="math-container">
            $$\begin{aligned}
            \mathcal{D}[Q(z)||P(z | X)] &= \mathbb{E}_{z \sim Q}[\log Q(z) - \log P(z | X)] \\
            &= \mathbb{E}_{z \sim Q}\left[\log Q(z) - \log \frac{P(X | z)P(z)}{P(X)}\right] \quad \text{(Bayes's rule)} \\
            &= \mathbb{E}_{z \sim Q}[\log Q(z) - \log P(X | z) - \log P(z)] + \log P(X)
            \end{aligned}$$
        </div>
        
        <p>Here, $\log P(X)$ comes out of the expectation because it doesn't depend on $z$. However, this objective is not computable because it requires computing the evidence $\log P(X)$.</p>
        
        <div class="highlight">
            <h3>💡 The ELBO Breakthrough</h3>
            <p>Because we cannot compute the KL divergence directly, we choose to optimize an alternative objective. Negating both sides, rearranging, and reorganizing yields:</p>
        </div>
        
        <div class="math-container">
            $$\log P(X) - \mathcal{D}[Q(z)||P(z | X)] = \mathbb{E}_{z \sim Q}[\log P(X | z)] - \mathcal{D}[Q(z)||P(z)]$$
        </div>
        
        <div class="key-concept">
            <h3>🎯 Evidence Lower BOund (ELBO)</h3>
            <p>This function is called the <strong>Evidence Lower BOund (ELBO)</strong>. Because $\log P(X)$ is constant with respect to $Q(z)$, maximizing ELBO is equivalent to minimizing the KL divergence to the true posterior.</p>
        </div>
        
        <div class="visualization">
            <h3>🧩 ELBO Intuition</h3>
            <svg width="100%" height="400" viewBox="0 0 1000 400">
                <!-- Background -->
                <rect width="1000" height="400" fill="url(#bgGrad)"/>
                
                <!-- ELBO Components -->
                <rect x="50" y="50" width="400" height="120" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2" rx="10"/>
                <text x="250" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#3498db">Reconstruction Term</text>
                <text x="250" y="100" text-anchor="middle" font-size="14" fill="#3498db">𝔼[log P(X|z)]</text>
                <text x="250" y="125" text-anchor="middle" font-size="12" fill="#2980b9">Encourages configurations of z</text>
                <text x="250" y="140" text-anchor="middle" font-size="12" fill="#2980b9">that explain the observed data X</text>
                <text x="250" y="155" text-anchor="middle" font-size="11" fill="#2980b9">"Make the decoder accurate"</text>
                
                <rect x="550" y="50" width="400" height="120" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2" rx="10"/>
                <text x="750" y="80" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">Regularization Term</text>
                <text x="750" y="100" text-anchor="middle" font-size="14" fill="#e74c3c">-𝒟[Q(z|X)||P(z)]</text>
                <text x="750" y="125" text-anchor="middle" font-size="12" fill="#c0392b">Encourages Q(z|X) to stay</text>
                <text x="750" y="140" text-anchor="middle" font-size="12" fill="#c0392b">close to the prior P(z)</text>
                <text x="750" y="155" text-anchor="middle" font-size="11" fill="#c0392b">"Don't deviate too much from prior"</text>
                
                <!-- Plus sign -->
                <text x="500" y="115" text-anchor="middle" font-size="30" font-weight="bold" fill="#2c3e50">+</text>
                
                <!-- Equals ELBO -->
                <text x="500" y="200" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">= ELBO</text>
                
                <!-- Balance explanation -->
                <rect x="200" y="230" width="600" height="80" fill="#27ae60" opacity="0.2" stroke="#27ae60" stroke-width="2" rx="10"/>
                <text x="500" y="255" text-anchor="middle" font-size="15" font-weight="bold" fill="#27ae60">The ELBO balances two competing objectives:</text>
                <text x="500" y="275" text-anchor="middle" font-size="13" fill="#229954">1. Accurate reconstruction of data (left term)</text>
                <text x="500" y="295" text-anchor="middle" font-size="13" fill="#229954">2. Staying close to prior assumptions (right term)</text>
                
                <!-- Mathematical relationship -->
                <text x="500" y="340" text-anchor="middle" font-size="14" font-weight="bold" fill="#8e44ad">Since log P(X) = 𝒟[Q(z|X)||P(z|X)] + ELBO ≥ ELBO</text>
                <text x="500" y="360" text-anchor="middle" font-size="12" fill="#7d3c98">ELBO provides a lower bound on the log evidence</text>
            </svg>
        </div>
        
        <h3>🔗 Making it Depend on X</h3>
        
        <p>Note that $X$ is fixed, and $Q$ can be any distribution. Since we're interested in inferring $P(X)$, it makes sense to construct a $Q$ which does depend on $X$. In particular, VAEs use multi-layer neural networks to map $X$ into $z$.</p>
        
        <p>We therefore replace $Q(z)$ with $Q(z | X)$ to make this dependency explicit:</p>
        
        <div class="math-container">
            $$\log P(X) - \mathcal{D}[Q(z | X)||P(z | X)] = \mathbb{E}_{z \sim Q}[\log P(X | z)] - \mathcal{D}[Q(z | X)||P(z)]$$
        </div>
        
        <div class="key-concept">
            <h3>🏗️ The VAE Architecture Emerges</h3>
            <p>This equation serves as the core of the variational autoencoder:</p>
            <ul style="margin-left: 20px;">
                <li><strong>Left side:</strong> We maximize $\log P(X)$ while minimizing $\mathcal{D}[Q(z | X) || P(z | X)]$</li>
                <li><strong>Right side:</strong> $Q(z | X)$ is the <strong>encoder</strong> and $P(X | z)$ is the <strong>decoder</strong></li>
                <li>Both can be neural networks optimized via stochastic gradient descent!</li>
            </ul>
        </div>
        
        <div class="visualization">
            <h3>🏗️ VAE Architecture Overview</h3>
            <svg width="100%" height="300" viewBox="0 0 1000 300">
                <!-- Input X -->
                <rect x="50" y="125" width="80" height="50" fill="#f39c12" opacity="0.8" rx="10"/>
                <text x="90" y="145" text-anchor="middle" font-size="16" font-weight="bold" fill="white">X</text>
                <text x="90" y="160" text-anchor="middle" font-size="12" fill="white">Input Data</text>
                
                <!-- Arrow to encoder -->
                <path d="M 130 150 L 180 150" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                
                <!-- Encoder -->
                <rect x="200" y="100" width="120" height="100" fill="#3498db" opacity="0.8" rx="10"/>
                <text x="260" y="130" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Encoder</text>
                <text x="260" y="145" text-anchor="middle" font-size="12" fill="white">Q(z|X)</text>
                <text x="260" y="165" text-anchor="middle" font-size="10" fill="white">Neural Network</text>
                <text x="260" y="180" text-anchor="middle" font-size="10" fill="white">μ(X), σ(X)</text>
                
                <!-- Arrow to latent -->
                <path d="M 320 150 L 370 150" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                <text x="345" y="140" text-anchor="middle" font-size="10" fill="#2c3e50">sample</text>
                
                <!-- Latent space -->
                <circle cx="420" cy="150" r="40" fill="#9b59b6" opacity="0.8" stroke="#8e44ad" stroke-width="2"/>
                <text x="420" y="145" text-anchor="middle" font-size="14" font-weight="bold" fill="white">z</text>
                <text x="420" y="160" text-anchor="middle" font-size="10" fill="white">Latent</text>
                <text x="420" y="175" text-anchor="middle" font-size="10" fill="white">Variables</text>
                
                <!-- Arrow to decoder -->
                <path d="M 460 150 L 510 150" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                
                <!-- Decoder -->
                <rect x="530" y="100" width="120" height="100" fill="#27ae60" opacity="0.8" rx="10"/>
                <text x="590" y="130" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Decoder</text>
                <text x="590" y="145" text-anchor="middle" font-size="12" fill="white">P(X|z)</text>
                <text x="590" y="165" text-anchor="middle" font-size="10" fill="white">Neural Network</text>
                <text x="590" y="180" text-anchor="middle" font-size="10" fill="white">f(z)</text>
                
                <!-- Arrow to output -->
                <path d="M 650 150 L 700 150" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                
                <!-- Reconstructed X -->
                <rect x="720" y="125" width="80" height="50" fill="#e67e22" opacity="0.8" rx="10"/>
                <text x="760" y="145" text-anchor="middle" font-size="16" font-weight="bold" fill="white">X̂</text>
                <text x="760" y="160" text-anchor="middle" font-size="12" fill="white">Reconstructed</text>
                
                <!-- Loss arrows -->
                <path d="M 420 190 Q 420 220 590 220 Q 760 220 760 190" stroke="#e74c3c" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <text x="590" y="235" text-anchor="middle" font-size="12" fill="#e74c3c">Reconstruction Loss: -𝔼[log P(X|z)]</text>
                
                <path d="M 260 50 Q 340 30 420 50" stroke="#e74c3c" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
                <text x="340" y="20" text-anchor="middle" font-size="12" fill="#e74c3c">KL Loss: 𝒟[Q(z|X)||P(z)]</text>
                
                <!-- Total loss -->
                                 <text x="500" y="270" text-anchor="middle" font-size="14" font-weight="bold" fill="#8e44ad">Total Loss = Reconstruction Loss + KL Loss</text>
             </svg>
         </div>
         
         <div class="section-divider"></div>
         
         <h2>🏗️ VAE Implementation Details</h2>
         
         <h3>⚙️ Optimizing the Objective</h3>
         
         <p>How can we perform stochastic gradient descent on the ELBO? We usually set the encoder to output a Gaussian distribution:</p>
         
         <div class="math-container">
             $$Q(z | X) = \mathcal{N}(z | \mu(X; \theta), \Sigma(X; \theta))$$
         </div>
         
         <p>Where $\mu$ and $\Sigma$ are deterministic functions with parameters $\theta$ (implemented as neural networks). In practice, $\Sigma$ is constrained to be diagonal for computational efficiency.</p>
         
         <div class="key-concept">
             <h3>🎯 Computational Advantages</h3>
             <ul style="margin-left: 20px;">
                 <li><strong>KL Term:</strong> $\mathcal{D}[Q(z | X)||P(z)]$ between two multivariate Gaussians can be computed in closed form</li>
                 <li><strong>Reconstruction Term:</strong> We estimate $\mathbb{E}_{z \sim Q}[\log P(X | z)]$ using a single sample (standard in SGD)</li>
             </ul>
         </div>
         
         <h3>🔄 The Reparameterization Trick</h3>
         
         <p>There's a crucial problem: sampling operations are not differentiable! The reparameterization trick solves this by expressing the random variable as a deterministic function of a noise variable.</p>
         
         <div class="highlight">
             <h3>💡 The Key Insight</h3>
             <p>Instead of sampling $z \sim \mathcal{N}(\mu, \sigma^2)$ directly, we:</p>
             <ol style="margin-left: 20px;">
                 <li>Sample $\epsilon \sim \mathcal{N}(0, I)$</li>
                 <li>Compute $z = \mu + \sigma \odot \epsilon$</li>
             </ol>
             <p>Now $z$ is a deterministic function of $\epsilon$, $\mu$, and $\sigma$, making it differentiable!</p>
         </div>
         
         <div class="visualization">
             <h3>🔧 Reparameterization Trick Visualization</h3>
             <svg width="100%" height="250" viewBox="0 0 1000 250">
                 <!-- Before (Non-differentiable) -->
                 <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">❌ Without Reparameterization</text>
                 
                 <rect x="50" y="50" width="80" height="40" fill="#3498db" opacity="0.8" rx="5"/>
                 <text x="90" y="75" text-anchor="middle" font-size="12" fill="white">Encoder</text>
                 
                 <path d="M 130 70 L 170 70" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                 
                 <circle cx="200" cy="70" r="25" fill="#e74c3c" opacity="0.8"/>
                 <text x="200" y="75" text-anchor="middle" font-size="10" fill="white">Sample z</text>
                 
                 <path d="M 225 70 L 265 70" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                 <text x="245" y="60" text-anchor="middle" font-size="10" fill="#e74c3c">Non-differentiable!</text>
                 
                 <rect x="280" y="50" width="80" height="40" fill="#27ae60" opacity="0.8" rx="5"/>
                 <text x="320" y="75" text-anchor="middle" font-size="12" fill="white">Decoder</text>
                 
                 <!-- After (Differentiable) -->
                 <text x="700" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">✅ With Reparameterization</text>
                 
                 <rect x="550" y="50" width="80" height="40" fill="#3498db" opacity="0.8" rx="5"/>
                 <text x="590" y="70" text-anchor="middle" font-size="10" fill="white">Encoder</text>
                 <text x="590" y="80" text-anchor="middle" font-size="8" fill="white">μ, σ</text>
                 
                 <path d="M 630 70 L 670 70" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                 
                 <!-- Noise source -->
                 <circle cx="650" cy="130" r="20" fill="#f39c12" opacity="0.8"/>
                 <text x="650" y="135" text-anchor="middle" font-size="10" fill="white">ε~N(0,I)</text>
                 
                 <path d="M 650 110 L 700 90" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead)"/>
                 
                 <circle cx="700" cy="70" r="25" fill="#9b59b6" opacity="0.8"/>
                 <text x="700" y="70" text-anchor="middle" font-size="8" fill="white">z=μ+σε</text>
                 <text x="700" y="80" text-anchor="middle" font-size="8" fill="white">Deterministic!</text>
                 
                 <path d="M 725 70 L 765 70" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                 
                 <rect x="780" y="50" width="80" height="40" fill="#27ae60" opacity="0.8" rx="5"/>
                 <text x="820" y="75" text-anchor="middle" font-size="12" fill="white">Decoder</text>
                 
                 <!-- Explanation -->
                 <rect x="200" y="150" width="600" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                 <text x="500" y="175" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Insight: z = μ + σ ⊙ ε</text>
                 <text x="500" y="195" text-anchor="middle" font-size="12" fill="#7f8c8d">Now gradients can flow through μ and σ to the encoder parameters!</text>
             </svg>
         </div>
         
         <div class="section-divider"></div>
         
         <h2>⚠️ Advanced Topics & Challenges</h2>
         
         <h3>📉 Posterior Collapse</h3>
         
         <p>Posterior collapse is a phenomenon where the posterior of the latents in a VAE becomes equal to its uninformative prior: $Q(z | X) = P(z)$. This means the latent variables carry no information about the input data!</p>
         
         <div class="warning">
             <h3>🚨 Why Posterior Collapse Happens</h3>
             <ul style="margin-left: 20px;">
                 <li><strong>Decoder too powerful:</strong> Can reconstruct data without using latent information</li>
                 <li><strong>Prior bias:</strong> KL regularization pushes towards uninformative prior</li>
                 <li><strong>Training dynamics:</strong> Order of parameter updates matters</li>
                 <li><strong>Local minima:</strong> Optimization can get stuck in poor solutions</li>
                 <li><strong>Information preference:</strong> Model prefers solutions that ignore latents</li>
             </ul>
         </div>
         
         <div class="visualization">
             <h3>📊 Posterior Collapse Illustration</h3>
             <svg width="100%" height="300" viewBox="0 0 1000 300">
                 <!-- Healthy VAE -->
                 <text x="250" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#27ae60">✅ Healthy VAE</text>
                 
                 <!-- Prior distribution -->
                 <ellipse cx="150" cy="80" rx="40" ry="20" fill="#3498db" opacity="0.3" stroke="#3498db" stroke-width="2"/>
                 <text x="150" y="85" text-anchor="middle" font-size="12" fill="#3498db">P(z)</text>
                 
                 <!-- Posterior distribution (different) -->
                 <ellipse cx="350" cy="80" rx="30" ry="35" fill="#e74c3c" opacity="0.3" stroke="#e74c3c" stroke-width="2"/>
                 <text x="350" y="85" text-anchor="middle" font-size="12" fill="#e74c3c">Q(z|X)</text>
                 
                 <text x="250" y="130" text-anchor="middle" font-size="11" fill="#27ae60">Prior ≠ Posterior</text>
                 <text x="250" y="145" text-anchor="middle" font-size="11" fill="#27ae60">Latents encode information!</text>
                 
                 <!-- Collapsed VAE -->
                 <text x="750" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#e74c3c">❌ Collapsed VAE</text>
                 
                 <!-- Prior and posterior (same) -->
                 <ellipse cx="650" cy="80" rx="40" ry="20" fill="#95a5a6" opacity="0.5" stroke="#7f8c8d" stroke-width="2"/>
                 <ellipse cx="650" cy="80" rx="40" ry="20" fill="none" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
                 
                 <text x="630" y="75" text-anchor="middle" font-size="12" fill="#7f8c8d">P(z)</text>
                 <text x="670" y="85" text-anchor="middle" font-size="12" fill="#7f8c8d">Q(z|X)</text>
                 
                 <ellipse cx="850" cy="80" rx="40" ry="20" fill="#95a5a6" opacity="0.5" stroke="#7f8c8d" stroke-width="2"/>
                 <ellipse cx="850" cy="80" rx="40" ry="20" fill="none" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
                 
                 <text x="750" y="130" text-anchor="middle" font-size="11" fill="#e74c3c">Prior = Posterior</text>
                 <text x="750" y="145" text-anchor="middle" font-size="11" fill="#e74c3c">Latents are ignored!</text>
                 
                 <!-- Solutions -->
                 <rect x="100" y="180" width="800" height="80" fill="#f4f6f7" stroke="#bdc3c7" stroke-width="2" rx="10"/>
                 <text x="500" y="205" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Solutions to Posterior Collapse</text>
                 <text x="300" y="225" text-anchor="middle" font-size="12" fill="#7f8c8d">• β-VAE: Adjust KL weight</text>
                 <text x="300" y="240" text-anchor="middle" font-size="12" fill="#7f8c8d">• Annealing: Gradually increase KL</text>
                 <text x="700" y="225" text-anchor="middle" font-size="12" fill="#7f8c8d">• Skip connections</text>
                 <text x="700" y="240" text-anchor="middle" font-size="12" fill="#7f8c8d">• Architecture modifications</text>
             </svg>
         </div>
         
         <h3>🎯 Key Takeaways</h3>
         
         <div class="key-concept">
             <h3>🧠 What You've Learned</h3>
             <ul style="margin-left: 20px;">
                 <li><strong>Motivation:</strong> VAEs solve the challenge of learning meaningful latent representations</li>
                 <li><strong>Core Idea:</strong> Use variational inference to approximate intractable posteriors</li>
                 <li><strong>ELBO:</strong> Balances reconstruction accuracy with staying close to prior</li>
                 <li><strong>Architecture:</strong> Encoder + Decoder networks with reparameterization trick</li>
                 <li><strong>Challenges:</strong> Posterior collapse and optimization difficulties</li>
             </ul>
         </div>
         
         <div class="highlight">
             <h3>🚀 Next Steps</h3>
             <p>Now that you understand VAEs, you can explore:</p>
             <ul style="margin-left: 20px;">
                 <li>β-VAE for disentangled representations</li>
                 <li>Conditional VAEs (CVAEs)</li>
                 <li>Vector Quantized VAEs (VQ-VAE)</li>
                 <li>Applications in computer vision, NLP, and science</li>
             </ul>
         </div>
         
         <div class="visualization">
             <h3>🌟 VAE Applications</h3>
             <svg width="100%" height="200" viewBox="0 0 1000 200">
                 <!-- Applications -->
                 <rect x="50" y="50" width="180" height="100" fill="#3498db" opacity="0.8" rx="10"/>
                 <text x="140" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Image Generation</text>
                 <text x="140" y="100" text-anchor="middle" font-size="11" fill="white">Faces, Art, Medical</text>
                 <text x="140" y="120" text-anchor="middle" font-size="11" fill="white">Image Synthesis</text>
                 
                 <rect x="270" y="50" width="180" height="100" fill="#27ae60" opacity="0.8" rx="10"/>
                 <text x="360" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Anomaly Detection</text>
                 <text x="360" y="100" text-anchor="middle" font-size="11" fill="white">Fraud Detection</text>
                 <text x="360" y="120" text-anchor="middle" font-size="11" fill="white">Quality Control</text>
                 
                 <rect x="490" y="50" width="180" height="100" fill="#e74c3c" opacity="0.8" rx="10"/>
                 <text x="580" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Data Compression</text>
                 <text x="580" y="100" text-anchor="middle" font-size="11" fill="white">Dimensionality</text>
                 <text x="580" y="120" text-anchor="middle" font-size="11" fill="white">Reduction</text>
                 
                 <rect x="710" y="50" width="180" height="100" fill="#9b59b6" opacity="0.8" rx="10"/>
                 <text x="800" y="80" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Drug Discovery</text>
                 <text x="800" y="100" text-anchor="middle" font-size="11" fill="white">Molecular</text>
                 <text x="800" y="120" text-anchor="middle" font-size="11" fill="white">Generation</text>
             </svg>
         </div>
         
         <div style="text-align: center; margin-top: 40px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px;">
             <h3 style="color: white; margin-bottom: 10px;">🎉 Congratulations!</h3>
             <p style="color: white; font-size: 1.1em; margin: 0;">You now have a solid understanding of Variational Autoencoders!</p>
         </div>
    </div>
    
    <script>
        // Initialize page functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('VAE Tutorial loaded successfully');
            
            // Create Monte Carlo sampling visualization
            createMonteCarloPlot();
        });
        
        function createMonteCarloPlot() {
            // Generate sample data to illustrate Monte Carlo sampling
            const nSamples = 1000;
            const zSamples = [];
            const fzValues = [];
            
            // Parameters (similar to the Python example)
            let a = 2.0;
            let b = 1.0;
            const X = 5;
            
            // Generate z samples from N(0,1)
            for (let i = 0; i < nSamples; i++) {
                const z = normalRandom();
                zSamples.push(z);
                fzValues.push(a * z + b);
            }
            
            // Create histogram of f(z) values
            const trace1 = {
                x: fzValues,
                type: 'histogram',
                name: 'f(z) = az + b',
                opacity: 0.7,
                marker: {
                    color: '#667eea'
                },
                nbinsx: 30
            };
            
            // Add vertical line for target X
            const trace2 = {
                x: [X, X],
                y: [0, 80],
                mode: 'lines',
                name: 'Target X = 5',
                line: {
                    color: '#e74c3c',
                    width: 3,
                    dash: 'dash'
                }
            };
            
            const layout = {
                title: {
                    text: 'Monte Carlo Sampling: Distribution of f(z) values',
                    font: { size: 16 }
                },
                xaxis: {
                    title: 'f(z) = az + b',
                    titlefont: { size: 14 }
                },
                yaxis: {
                    title: 'Frequency',
                    titlefont: { size: 14 }
                },
                showlegend: true,
                plot_bgcolor: '#f8f9fa',
                paper_bgcolor: '#f8f9fa'
            };
            
            Plotly.newPlot('montecarlo-plot', [trace1, trace2], layout, {responsive: true});
        }
        
        // Box-Muller transform for generating normal random numbers
        function normalRandom() {
            let u = 0, v = 0;
            while(u === 0) u = Math.random(); // Converting [0,1) to (0,1)
            while(v === 0) v = Math.random();
            return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        }
    </script>
</body>
</html> 