9.6 Structured Outputs
 Convolutional networks can be used to output a high-dimensional, structured
 object, rather than just predicting a class label for a classification task or a real
 value for a regression task. Typically this object is just a tensor, emitted by a
 standard convolutional layer. For example, the model might emit a tensor S, where
 Si,j,k is the probability that pixel (j,k) of the input to the network belongs to class
 i. This allows the model to label every pixel in an image and draw precise masks
 that follow the outlines of individual objects.
 One issue that often comes up is that the output plane can be smaller than the
 358
CHAPTER 9. CONVOLUTIONALNETWORKS
 ˆ
 ˆ
 Y(1)
 Y(1)
 V
 H(1)
 H(1)
 U U U
 X
 X
 ˆ
 ˆ
 Y(2)
 Y(2)
 W
 V
 H(2)
 H(2)
 ˆ
 ˆ
 Y(3)
 Y(3)
 W
 V
 H(3)
 H(3)
 Figure 9.17: An example of a recurrent convolutional network for pixel labeling. The
 X
 input is an image tensor , with axes corresponding to image rows, image columns, and
 channels (red, green, blue). The goal is to output a tensor of labels ˆ Y, with a probability
 distribution over labels for each pixel. This tensor has axes corresponding to image rows,
 image columns, and the different classes. Rather than outputting ˆ
 Y in a single shot, the
 recurrent network iteratively refines its estimate ˆ Y by using a previous estimate of ˆ Y
 as input for creating a new estimate. The same parameters are used for each updated
 estimate, and the estimate can be refined as many times as we wish. The tensor of
 convolution kernels U is used on each step to compute the hidden representation given the
 input image. The kernel tensorV is used to produce an estimate of the labels given the
 hidden values. On all but the first step, the kernels W are convolved over ˆ Y to provide
 input to the hidden layer. On the first time step, this term is replaced by zero. Because
 the same parameters are used on each step, this is an example of a recurrent network, as
 described in chapter .
 10
 input plane, as shown in figure
 9.13
 . In the kinds of architectures typically used for
 classification of a single object in an image, the greatest reduction in the spatial
 dimensions of the network comes from using pooling layers with large stride. In
 order to produce an output map of similar size as the input, one can avoid pooling
 altogether (
 Jain et al. 2007
 grid of labels (
 ,
 ). Another strategy is to simply emit a lower-resolution
 Pinheiro and Collobert 2014 2015
 ,
 use a pooling operator with unit stride.
 ,
 ). Finally, in principle, one could
 One strategy for pixel-wise labeling of images is to produce an initial guess
 of the image labels, then refine this initial guess using the interactions between
 neighboring pixels. Repeating this refinement step several times corresponds to
 using the same convolutions at each stage, sharing weights between the last layers of
 the deep net (
 Jain et al. 2007
 ,
 ). This makes the sequence of computations performed
 by the successive convolutional layers with weights shared across layers a particular
 kind of recurrent network (
 Pinheiro and Collobert 2014 2015
 ,
 ,
 the architecture of such a recurrent convolutional network.
 ). Figure
 9.17
 shows
 359
CHAPTER 9. CONVOLUTIONALNETWORKS
 Once a prediction for each pixel is made, various methods can be used to
 further process these predictions in order to obtain a segmentation of the image
 into regions (
 Briggman et al. 2009 Turaga
 ,
 ;
 et al.,
 ;
 2010 Farabet
 et al.,
 2013
 ).
 The general idea is to assume that large groups of contiguous pixels tend to be
 associated with the same label. Graphical models can describe the probabilistic
 relationships between neighboring pixels. Alternatively, the convolutional network
 can be trained to maximize an approximation of the graphical model training
 objective (
 ,
 ;
 Ning et al. 2005 Thompson et al. 2014
 ,
 9.7 Data Types
 ).
 The data used with a convolutional network usually consists of several channels,
 each channel being the observation of a different quantity at some point in space
 or time. See table
 9.1
 for examples of data types with different dimensionalities
 and number of channels.
 For an example of convolutional networks applied to video, see Chen et al.
 (
 2010
 ).
 So far we have discussed only the case where every example in the train and test
 data has the same spatial dimensions. One advantage to convolutional networks
 is that they can also process inputs with varying spatial extents. These kinds of
 input simply cannot be represented by traditional, matrix multiplication-based
 neural networks. This provides a compelling reason to use convolutional networks
 even when computational cost and overfitting are not significant issues.
 For example, consider a collection of images, where each image has a different
 width and height. It is unclear how to model such inputs with a weight matrix of
 f
 ixed size. Convolution is straightforward to apply; the kernel is simply applied a
 different number of times depending on the size of the input, and the output of the
 convolution operation scales accordingly. Convolution may be viewed as matrix
 multiplication; the same convolution kernel induces a different size of doubly block
 circulant matrix for each size of input. Sometimes the output of the network is
 allowed to have variable size as well as the input, for example if we want to assign
 a class label to each pixel of the input. In this case, no further design work is
 necessary. In other cases, the network must produce some fixed-size output, for
 example if we want to assign a single class label to the entire image. In this case
 we must make some additional design steps, like inserting a pooling layer whose
 pooling regions scale in size proportional to the size of the input, in order to
 maintain a fixed number of pooled outputs. Some examples of this kind of strategy
 are shown in figure
 9.11
 .
 360
CHAPTER 9. CONVOLUTIONALNETWORKS
 Single channel
 1-D Audio waveform: The axis we
 convolve over corresponds to
 time. We discretize time and
 measure the amplitude of the
 waveform once per time step.
 2-D Audio data that has been prepro
cessed with a Fourier transform:
 Wecan transform the audio wave
form into a 2D tensor with dif
ferent rows corresponding to dif
ferent frequencies and different
 columns corresponding to differ
ent points in time. Using convolu
tion in the time makes the model
 equivariant to shifts in time. Us
ing convolution across the fre
quency axis makes the model
 equivariant to frequency, so that
 the same melody played in a dif
ferent octave produces the same
 representation but at a different
 height in the network’s output.
 3-D Volumetric data: A common
 source of this kind of data is med
ical imaging technology, such as
 CT scans.
 Multi-channel
 Skeleton animation data: Anima
tions of 3-D computer-rendered
 characters are generated by alter
ing the pose of a “skeleton” over
 time. At each point in time, the
 pose of the character is described
 by a specification of the angles of
 each of the joints in the charac
ter’s skeleton. Each channel in
 the data we feed to the convolu
tional model represents the angle
 about one axis of one joint.
 Color image data: One channel
 contains the red pixels, one the
 green pixels, and one the blue
 pixels. The convolution kernel
 moves over both the horizontal
 and vertical axes of the image,
 conferring translation equivari
ance in both directions.
 Color video data: One axis corre
sponds to time, one to the height
 of the video frame, and one to
 the width of the video frame.
 Table 9.1: Examples of different formats of data that can be used with convolutional
 networks.
 361
CHAPTER 9. CONVOLUTIONALNETWORKS
 Note that the use of convolution for processing variable sized inputs only makes
 sense for inputs that have variable size because they contain varying amounts
 of observation of the same kind of thing—different lengths of recordings over
 time, different widths of observations over space, etc. Convolution does not make
 sense if the input has variable size because it can optionally include different
 kinds of observations. For example, if we are processing college applications, and
 our features consist of both grades and standardized test scores, but not every
 applicant took the standardized test, then it does not make sense to convolve the
 same weights over both the features corresponding to the grades and the features
 corresponding to the test scores.
 9.8 Efficient Convolution Algorithms
 Modern convolutional network applications often involve networks containing more
 than one million units. Powerful implementations exploiting parallel computation
 resources, as discussed in section
 12.1
 , are essential. However, in many cases it
 is also possible to speed up convolution by selecting an appropriate convolution
 algorithm.
 Convolution is equivalent to converting both the input and the kernel to the
 frequency domain using a Fourier transform, performing point-wise multiplication
 of the two signals, and converting back to the time domain using an inverse
 Fourier transform. For some problem sizes, this can be faster than the naive
 implementation of discrete convolution.
 When a d-dimensional kernel can be expressed as the outer product of d
 vectors, one vector per dimension, the kernel is called separable. When the
 kernel is separable, naive convolution is inefficient. It is equivalent to composed
 one-dimensional convolutions with each of these vectors. The composed approach
 is significantly faster than performing one d-dimensional convolution with their
 outer product. The kernel also takes fewer parameters to represent as vectors.
 If the kernel is w elements wide in each dimension, then naive multidimensional
 convolution requires O(wd) runtime and parameter storage space, while separable
 convolution requires O(w d
 × )runtime and parameter storage space. Of course,
 not every convolution can be represented in this way.
 Devising faster ways of performing convolution or approximate convolution
 without harming the accuracy of the model is an active area of research. Even tech
niques that improve the efficiency of only forward propagation are useful because
 in the commercial setting, it is typical to devote more resources to deployment of
 a network than to its training