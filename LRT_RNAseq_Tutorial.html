<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Likelihood Ratio Test for RNA-seq Analysis</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <!-- Plotly.js for interactive graphs -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.8em;
            color: #1e40af;
            margin-bottom: 15px;
        }
        
        .section {
            background: white;
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }
        
        .formula-box {
            background: #f8fafc;
            border: 2px solid #2563eb;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #fef3cd 0%, #fff2cd 100%);
            border: 2px solid #d97706;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background: #1e40af;
            color: white;
            padding: 12px;
            text-align: center;
        }
        
        .data-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8fafc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Likelihood Ratio Test for RNA-seq</h1>
            <p>A Step-by-Step Tutorial with Football and Genomics Examples</p>
        </div>
        
        <div class="section">
            <h2>Introduction</h2>
            <p>The Likelihood Ratio Test (LRT) is a powerful statistical method used in RNA-seq analysis to detect differential gene expression. We'll learn this concept through a football example before applying it to genomics.</p>
            
            <div class="formula-box">
                <h3>Core LRT Formula</h3>
                $$D = -2 \log \frac{\text{likelihood of null model}}{\text{likelihood of alternative model}}$$
                                 <p>Under large samples, D follows a χ² distribution</p>
             </div>
        </div>
        
        <div class="section">
            <h2>Football Goals Example: Understanding the Basics</h2>
            <p>Let's start with a concrete example: analyzing the average number of goals per game in World Cup football matches. Our null hypothesis is that there are <strong>3 goals per game</strong>.</p>
            
            <div class="example-box">
                <h3>⚽ The Data</h3>
                <p>We collected data from 380 football games and want to test whether the average goals per game equals 3.</p>
                
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Goals</th>
                            <th>Frequency</th>
                            <th>Goals</th>
                            <th>Frequency</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>0</td><td>30</td><td>5</td><td>24</td></tr>
                        <tr><td>1</td><td>79</td><td>6</td><td>11</td></tr>
                        <tr><td>2</td><td>99</td><td>7</td><td>6</td></tr>
                        <tr><td>3</td><td>67</td><td>8</td><td>2</td></tr>
                        <tr><td>4</td><td>61</td><td>9</td><td>1</td></tr>
                        <tr><td colspan="2"><strong>Total: 380 games</strong></td><td colspan="2"><strong>Total goals: 975</strong></td></tr>
                    </tbody>
                </table>
            </div>

            <div id="footballHistogram" style="width:100%; height:400px;"></div>
            <script>
                // Create histogram of football goals
                const goals = [0,1,2,3,4,5,6,7,8,9];
                const frequencies = [30,79,99,67,61,24,11,6,2,1];
                
                const trace = {
                    x: goals,
                    y: frequencies,
                    type: 'bar',
                    marker: {
                        color: '#2563eb',
                        opacity: 0.8
                    },
                    name: 'Observed Frequency'
                };
                
                const layout = {
                    title: {
                        text: 'Distribution of Goals per Game (380 matches)',
                        font: {size: 18}
                    },
                    xaxis: {title: 'Goals per Game'},
                    yaxis: {title: 'Number of Games'},
                    margin: {t: 60, b: 60, l: 60, r: 60}
                };
                
                Plotly.newPlot('footballHistogram', [trace], layout, {responsive: true});
            </script>
        </div>
        
        <div class="section">
            <h2>Maximum Likelihood Estimation (MLE)</h2>
            <p>Before we can perform LRT, we need to understand Maximum Likelihood Estimation. We model goals per game as a Poisson distribution.</p>
            
            <div class="formula-box">
                <h3>Poisson Likelihood Function</h3>
                <p>For independent observations x₁, x₂, ..., xₙ from Poisson(λ):</p>
                $$L(\lambda|X) = \prod_{i=1}^{N} \frac{e^{-\lambda}\lambda^{x_i}}{x_i!} = \frac{e^{-N\lambda}\lambda^{\sum_{i=1}^N x_i}}{\prod_{i=1}^N x_i!}$$
            </div>
            
            <div class="example-box">
                <h3>📊 Log-Likelihood Calculation</h3>
                <p>It's easier to work with the log-likelihood:</p>
                $$\log L(\lambda|X) = -N\lambda + \sum_{i=1}^N x_i \log \lambda - \sum_{i=1}^N \log x_i!$$
                
                <p><strong>Taking the derivative and setting to zero:</strong></p>
                $$\frac{d}{d\lambda} \log L(\lambda|X) = -N + \frac{\sum_{i=1}^N x_i}{\lambda} = 0$$
                
                <p><strong>This gives us the MLE:</strong></p>
                $$\hat{\lambda} = \frac{\sum_{i=1}^N x_i}{N} = \bar{x}$$
            </div>
            
            <p>For our football data:</p>
            <div class="formula-box">
                $$\hat{\lambda} = \frac{975}{380} = 2.57 \text{ goals per game}$$
            </div>

            <div id="likelihoodCurve" style="width:100%; height:400px;"></div>
            <script>
                // Create likelihood curve
                const lambda_values = [];
                const likelihood_values = [];
                
                for (let lambda = 0.5; lambda <= 5.0; lambda += 0.05) {
                    lambda_values.push(lambda);
                    // Simplified likelihood calculation (normalized)
                    const logL = -380 * lambda + 975 * Math.log(lambda);
                    likelihood_values.push(Math.exp(logL + 3000)); // Normalized for visualization
                }
                
                const trace1 = {
                    x: lambda_values,
                    y: likelihood_values,
                    type: 'scatter',
                    mode: 'lines',
                    line: {color: '#dc2626', width: 3},
                    name: 'Likelihood Function'
                };
                
                // Mark the MLE
                const mle_trace = {
                    x: [2.57],
                    y: [Math.exp(-380 * 2.57 + 975 * Math.log(2.57) + 3000)],
                    type: 'scatter',
                    mode: 'markers',
                    marker: {size: 12, color: '#059669'},
                    name: 'MLE (λ̂ = 2.57)'
                };
                
                // Mark null hypothesis
                const null_trace = {
                    x: [3.0],
                    y: [Math.exp(-380 * 3.0 + 975 * Math.log(3.0) + 3000)],
                    type: 'scatter',
                    mode: 'markers',
                    marker: {size: 12, color: '#d97706'},
                    name: 'Null (λ₀ = 3.0)'
                };
                
                const layout1 = {
                    title: {
                        text: 'Likelihood Function for Football Goals',
                        font: {size: 18}
                    },
                    xaxis: {title: 'λ (goals per game)'},
                    yaxis: {title: 'Likelihood (normalized)'},
                    margin: {t: 60, b: 60, l: 60, r: 60}
                };
                
                Plotly.newPlot('likelihoodCurve', [trace1, mle_trace, null_trace], layout1, {responsive: true});
            </script>
        </div>
        
        <div class="section">
            <h2>LRT Calculation: Step by Step</h2>
            <p>Now we perform the likelihood ratio test to determine if our data supports the null hypothesis (λ₀ = 3) or not.</p>
            
            <div class="example-box">
                <h3>🎯 Hypotheses</h3>
                <ul>
                    <li><strong>H₀:</strong> λ = 3 (null hypothesis - 3 goals per game)</li>
                    <li><strong>Hₐ:</strong> λ = 2.57 (alternative hypothesis - MLE estimate)</li>
                </ul>
            </div>
            
            <div class="formula-box">
                <h3>Step 1: Calculate Log-Likelihoods</h3>
                <p><strong>Under null hypothesis (λ₀ = 3):</strong></p>
                $$\log L(\lambda_0|X) = -380 \times 3 + 975 \times \log 3 - \sum \log x_i! = -68.85$$
                
                <p><strong>Under alternative (λ̂ = 2.57):</strong></p>
                $$\log L(\hat{\lambda}|X) = -380 \times 2.57 + 975 \times \log 2.57 - \sum \log x_i! = -56.29$$
                
                <p><em>Note: The term $\sum \log x_i!$ cancels out in the ratio</em></p>
            </div>
            
            <div class="formula-box">
                <h3>Step 2: Calculate LRT Statistic</h3>
                $$LRT = -2[\log L(\lambda_0|X) - \log L(\hat{\lambda}|X)]$$
                $$LRT = -2[(-68.85) - (-56.29)] = -2 \times (-12.56) = 25.12$$
            </div>
            
            <div class="formula-box">
                <h3>Step 3: Statistical Decision</h3>
                <p>Compare with χ² distribution (1 degree of freedom):</p>
                $$LRT = 25.12 \gg \chi^2_{0.05,1} = 3.84$$
                <p><strong>Conclusion:</strong> Reject H₀ at α = 0.05 level!</p>
                <p>The evidence strongly suggests that the average is NOT 3 goals per game.</p>
            </div>
        </div>
        
        <div class="section">
            <h2>RNA-seq Application</h2>
            <p>Now let's see how the same LRT principle applies to RNA-seq differential expression analysis.</p>
            
            <div class="example-box">
                <h3>🧬 RNA-seq Model Setup</h3>
                <p>For gene expression analysis, we model:</p>
                <ul>
                    <li><strong>x<sub>ijk</sub>:</strong> number of reads mapped to gene j for lane k of sample i</li>
                    <li><strong>x<sub>ijk</sub> ~ Poisson(λ<sub>ijk</sub>)</strong></li>
                    <li><strong>λ<sub>ijk</sub> = c<sub>ik</sub> × ν<sub>ijk</sub></strong></li>
                </ul>
                <p>Where:</p>
                <ul>
                    <li><strong>c<sub>ik</sub>:</strong> total rate of read production for lane k of sample i</li>
                    <li><strong>ν<sub>ijk</sub>:</strong> relative rate for gene j (∑<sub>j</sub> ν<sub>ijk</sub> = 1)</li>
                </ul>
            </div>
            
            <div class="formula-box">
                <h3>RNA-seq Hypotheses</h3>
                <p><strong>Null Hypothesis (No Differential Expression):</strong></p>
                $$H_0: \nu_{ijk} = \nu_j \text{ for all samples (same expression level)}$$
                
                <p><strong>Alternative Hypothesis (Differential Expression):</strong></p>
                $$H_a: \nu_{ijk} = \nu_j^A \text{ for group A, } \nu_{ijk} = \nu_j^B \text{ for group B}$$
                $$\text{where } \nu_j^A \neq \nu_j^B$$
            </div>
            
            <div class="example-box">
                <h3>📈 Real RNA-seq Results</h3>
                <p>Marioni et al. applied LRT to liver vs kidney samples:</p>
                <ul>
                    <li><strong>5 lanes each</strong> of liver and kidney samples</li>
                    <li><strong>11,493 genes</strong> identified as differentially expressed</li>
                    <li><strong>FDR = 0.1%</strong> (very stringent)</li>
                    <li><strong>94%</strong> had |log₂ fold-change| > 0.5</li>
                    <li><strong>71%</strong> had |log₂ fold-change| > 1</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>Statistical Interpretation</h2>
            <p>Understanding the theoretical foundation of LRT is crucial for proper application.</p>
            
            <div class="formula-box">
                <h3>Asymptotic Distribution Theory</h3>
                <p>Under the null hypothesis and regularity conditions:</p>
                $$-2\log\Lambda \xrightarrow{d} \chi^2_k$$
                <p>where k = number of constraints under H₀</p>
                
                <p><strong>For simple cases:</strong></p>
                <ul style="text-align: left; display: inline-block;">
                    <li>Testing one parameter: k = 1</li>
                    <li>Testing two parameters simultaneously: k = 2</li>
                    <li>General rule: k = (free parameters in Hₐ) - (free parameters in H₀)</li>
                </ul>
            </div>

            <div id="chiSquareDistribution" style="width:100%; height:400px;"></div>
            <script>
                // Create chi-square distribution plot
                const x_chi = [];
                const y_chi = [];
                
                for (let x = 0; x <= 15; x += 0.1) {
                    x_chi.push(x);
                    // Chi-square PDF with 1 df: f(x) = (1/2)^(1/2) * x^(1/2-1) * exp(-x/2) / Γ(1/2)
                    if (x === 0) {
                        y_chi.push(0);
                    } else {
                        y_chi.push(0.5 * Math.pow(x, -0.5) * Math.exp(-x/2) / Math.sqrt(Math.PI));
                    }
                }
                
                const chi_trace = {
                    x: x_chi,
                    y: y_chi,
                    type: 'scatter',
                    mode: 'lines',
                    line: {color: '#7c3aed', width: 3},
                    name: 'χ² distribution (df=1)'
                };
                
                // Mark critical value
                const critical_trace = {
                    x: [3.84, 3.84],
                    y: [0, 0.15],
                    type: 'scatter',
                    mode: 'lines',
                    line: {color: '#dc2626', width: 3, dash: 'dash'},
                    name: 'Critical value (α=0.05)'
                };
                
                // Mark our test statistic
                const test_stat_trace = {
                    x: [25.12, 25.12],
                    y: [0, 0.05],
                    type: 'scatter',
                    mode: 'lines',
                    line: {color: '#059669', width: 4},
                    name: 'Our test statistic (25.12)'
                };
                
                const layout_chi = {
                    title: {
                        text: 'Chi-Square Distribution and Test Results',
                        font: {size: 18}
                    },
                    xaxis: {title: 'Test Statistic Value'},
                    yaxis: {title: 'Probability Density'},
                    margin: {t: 60, b: 60, l: 60, r: 60},
                    annotations: [
                        {
                            x: 10,
                            y: 0.1,
                            text: 'p < 0.001<br>Highly Significant!',
                            showarrow: true,
                            arrowhead: 2,
                            arrowsize: 1,
                            arrowwidth: 2,
                            arrowcolor: '#059669'
                        }
                    ]
                };
                
                Plotly.newPlot('chiSquareDistribution', [chi_trace, critical_trace, test_stat_trace], layout_chi, {responsive: true});
            </script>
        </div>
        
        <div class="section">
            <h2>Practical Implementation Guide</h2>
            <p>Here's a step-by-step workflow for applying LRT in your own RNA-seq analysis:</p>
            
            <div class="example-box">
                <h3>🔬 LRT Workflow for RNA-seq</h3>
                <ol>
                    <li><strong>Data Preparation:</strong> Count reads mapped to each gene</li>
                    <li><strong>Model Selection:</strong> Choose appropriate distribution (Poisson, Negative Binomial)</li>
                    <li><strong>Fit Null Model:</strong> Assume no difference between conditions</li>
                    <li><strong>Fit Alternative Model:</strong> Allow different expression levels</li>
                    <li><strong>Calculate LRT:</strong> Compare log-likelihoods</li>
                    <li><strong>Statistical Testing:</strong> Compare to χ² distribution</li>
                    <li><strong>Multiple Testing:</strong> Correct for testing thousands of genes (FDR)</li>
                </ol>
            </div>
            
            <div class="formula-box">
                <h3>Key Considerations</h3>
                <ul style="text-align: left; display: inline-block;">
                    <li><strong>Overdispersion:</strong> Real RNA-seq data often has variance > mean</li>
                    <li><strong>Library Size:</strong> Normalize for different sequencing depths</li>
                    <li><strong>Multiple Testing:</strong> Use FDR control (Benjamini-Hochberg)</li>
                    <li><strong>Effect Size:</strong> Consider biological significance, not just statistical</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h2>Summary and Conclusions</h2>
            <p>The Likelihood Ratio Test provides a rigorous statistical framework for detecting differential gene expression in RNA-seq data.</p>
            
            <div class="example-box">
                <h3>🎯 Key Takeaways</h3>
                <ul>
                    <li><strong>Theoretical Foundation:</strong> LRT compares how well different models explain the data</li>
                    <li><strong>Practical Application:</strong> Widely used in modern RNA-seq analysis pipelines</li>
                    <li><strong>Statistical Rigor:</strong> Well-established asymptotic properties provide reliable p-values</li>
                    <li><strong>Scalability:</strong> Can handle genome-wide analysis efficiently</li>
                    <li><strong>Flexibility:</strong> Adaptable to different experimental designs and distributions</li>
                </ul>
            </div>
            
            <div class="formula-box">
                <h3>Next Steps in Learning</h3>
                <ul style="text-align: left; display: inline-block;">
                    <li>Explore advanced models (DESeq2, edgeR)</li>
                    <li>Learn about negative binomial distributions</li>
                    <li>Study multiple testing correction methods</li>
                    <li>Practice with real RNA-seq datasets</li>
                    <li>Understand experimental design principles</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div style="text-align: center; padding: 40px; background: rgba(255,255,255,0.1); color: white; margin-top: 20px; border-radius: 15px;">
        <h3>Continue Your Journey in Computational Biology!</h3>
        <p>Master LRT and unlock the power of statistical genomics</p>
    </div>
</body>
</html> 