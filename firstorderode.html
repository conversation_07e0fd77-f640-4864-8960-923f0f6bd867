<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>First-Order Differential Equations Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #ecf0f1;
            --dark-bg: #34495e;
            --text-color: #2c3e50;
            --light-text: #7f8c8d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .nav-menu {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-menu h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        .nav-menu ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 0.5rem;
        }

        .nav-menu li {
            padding: 0.5rem;
            border-left: 3px solid var(--secondary-color);
            background: var(--light-bg);
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav-menu li:hover {
            background: var(--secondary-color);
            color: white;
            transform: translateX(5px);
        }

        .section {
            background: white;
            margin-bottom: 2rem;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            border-left: 5px solid var(--secondary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--light-bg);
            padding-bottom: 0.5rem;
        }

        .section h3 {
            color: var(--secondary-color);
            font-size: 1.4rem;
            margin: 1.5rem 0 1rem 0;
        }

        .definition-box {
            background: linear-gradient(135deg, #e8f4fd 0%, #d6eaf8 100%);
            border: 1px solid var(--secondary-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--secondary-color);
        }

        .definition-box h4 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .example-box {
            background: linear-gradient(135deg, #fff9e6 0%, #fef5e7 100%);
            border: 1px solid var(--warning-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--warning-color);
        }

        .example-box h4 {
            color: var(--warning-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .important-box {
            background: linear-gradient(135deg, #fdf2f2 0%, #fadbd8 100%);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--accent-color);
        }

        .important-box h4 {
            color: var(--accent-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .method-box {
            background: linear-gradient(135deg, #e8f8f5 0%, #d5f4e6 100%);
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 5px solid var(--success-color);
        }

        .method-box h4 {
            color: var(--success-color);
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .svg-container {
            text-align: center;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .math-display {
            text-align: center;
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding-left: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            margin: 1rem 0;
            padding: 1rem;
            background: var(--light-bg);
            border-radius: 5px;
            border-left: 4px solid var(--secondary-color);
            position: relative;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--secondary-color);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .conclusion {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
            color: white;
            text-align: center;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }

        .conclusion h2 {
            color: white;
            border-bottom: 2px solid white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .nav-menu ul {
                grid-template-columns: 1fr;
            }
            
            .section {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>First-Order Differential Equations</h1>
            <p class="subtitle">A Comprehensive Tutorial with Visual Examples</p>
        </header>

        <div class="nav-menu">
            <h3>📚 Tutorial Contents</h3>
            <ul>
                <li><a href="#intro">1. Introduction and Basic Concepts</a></li>
                <li><a href="#definition">2. Definition of First-Order DEs</a></li>
                <li><a href="#initial">3. Initial Value Problems</a></li>
                <li><a href="#separable">4. Separable Equations</a></li>
                <li><a href="#growth">5. Growth and Decay Models</a></li>
                <li><a href="#logistic">6. Logistic Growth Model</a></li>
                <li><a href="#linear">7. Linear Differential Equations</a></li>
                <li><a href="#methods">8. Solution Methods</a></li>
            </ul>
        </div>

        <div class="section" id="intro">
            <h2>1. Introduction and Basic Concepts</h2>
            
            <p>Differential equations are mathematical equations that relate a function with its derivatives. They are fundamental tools in modeling real-world phenomena in physics, engineering, biology, economics, and many other fields.</p>
            
            <div class="definition-box">
                <h4>What is a Differential Equation?</h4>
                <p>A differential equation is an equation that involves an unknown function and one or more of its derivatives. The order of a differential equation is determined by the highest derivative present in the equation.</p>
            </div>
            
            <p>In many fields such as physics, biology or business, a relationship is often known or assumed between some unknown quantity and its rate of change, which does not involve any higher derivatives. It is therefore of interest to study first order differential equations in particular.</p>
            
            <div class="svg-container">
                <h3>Example of a Differential Equation</h3>
                <svg width="500" height="300" xmlns="http://www.w3.org/2000/svg">
                    <!-- Coordinate system -->
                    <line x1="50" y1="250" x2="450" y2="250" stroke="black" stroke-width="1"/>
                    <line x1="50" y1="250" x2="50" y2="50" stroke="black" stroke-width="1"/>
                    
                    <!-- Labels -->
                    <text x="440" y="270" font-family="Arial" font-size="16">t</text>
                    <text x="30" y="60" font-family="Arial" font-size="16">y</text>
                    
                    <!-- Function curve -->
                    <path d="M 50 200 Q 150 100 250 120 Q 350 140 450 80" stroke="blue" stroke-width="3" fill="none"/>
                    
                    <!-- Tangent lines at various points -->
                    <line x1="100" y1="150" x2="140" y2="130" stroke="red" stroke-width="2"/>
                    <line x1="200" y1="125" x2="240" y2="135" stroke="red" stroke-width="2"/>
                    <line x1="300" y1="135" x2="340" y2="125" stroke="red" stroke-width="2"/>
                    <line x1="400" y1="100" x2="440" y2="80" stroke="red" stroke-width="2"/>
                    
                    <!-- Points -->
                    <circle cx="100" cy="150" r="4" fill="blue"/>
                    <circle cx="200" cy="125" r="4" fill="blue"/>
                    <circle cx="300" cy="135" r="4" fill="blue"/>
                    <circle cx="400" cy="100" r="4" fill="blue"/>
                    
                    <!-- Annotations -->
                    <text x="110" y="140" font-family="Arial" font-size="14" fill="red">dy/dt</text>
                    <text x="210" y="115" font-family="Arial" font-size="14" fill="red">dy/dt</text>
                    <text x="310" y="125" font-family="Arial" font-size="14" fill="red">dy/dt</text>
                    <text x="410" y="90" font-family="Arial" font-size="14" fill="red">dy/dt</text>
                </svg>
                <p>A differential equation relates a function (blue curve) to its derivative (red tangent lines) at various points.</p>
            </div>
        </div>

        <div class="section" id="definition">
            <h2>2. Definition of First-Order Differential Equations</h2>
            
            <div class="definition-box">
                <h4>Definition: First Order Differential Equation</h4>
                <p>A first order differential equation is an equation of the form:</p>
                <div class="math-display">
                    $$F(t, y, y') = 0$$
                </div>
                <p>where $y'$ represents the first derivative of $y$ with respect to $t$.</p>
            </div>
            
            <p>A solution of a first order differential equation is a function $f(t)$ that makes $F(t, f(t), f'(t)) = 0$ for every value of $t$.</p>
            
            <div class="example-box">
                <h4>Example: Simple First Order Differential Equation</h4>
                <p>The equation $y' = t^2 + 1$ is a first order linear differential equation where:</p>
                <div class="math-display">
                    $$F(t, y, y') = y' - t^2 - 1$$
                </div>
                <p>All solutions to this equation are of the form:</p>
                <div class="math-display">
                    $$y = \frac{t^3}{3} + t + C$$
                </div>
                <p>where $C$ is an arbitrary constant.</p>
            </div>
        </div>

        <div class="section" id="initial">
            <h2>3. Initial Value Problems</h2>
            
            <div class="definition-box">
                <h4>Definition: Initial Conditions</h4>
                <p>Initial condition(s) are a set of points that the solution (or its derivatives) must satisfy.</p>
            </div>
            
            <div class="definition-box">
                <h4>Definition: Initial Value Problem (IVP)</h4>
                <p>An initial value problem (IVP) is a differential equation along with a set of initial conditions.</p>
            </div>
            
            <div class="example-box">
                <h4>Example: First Order Initial Value Problem</h4>
                <p>Solve the initial value problem:</p>
                <div class="math-display">
                    $$\frac{dy}{dx} = 2x, \quad y(0) = 2$$
                </div>
                
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Integrate both sides: $\int dy = \int 2x \, dx$</li>
                    <li>This gives us: $y = x^2 + C$</li>
                    <li>Apply initial condition: $y(0) = 2$ means $2 = 0^2 + C$, so $C = 2$</li>
                    <li>Therefore, the specific solution is: $y = x^2 + 2$</li>
                </ol>
            </div>
        </div>

        <div class="section" id="separable">
            <h2>4. Separable Equations</h2>
            
            <div class="definition-box">
                <h4>Definition: Separable Differential Equation</h4>
                <p>A first order differential equation is separable if it can be written in the form:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = f(t)g(y)$$
                </div>
            </div>
            
            <div class="method-box">
                <h4>Method: Separation of Variables</h4>
                <p>To solve a separable equation, we follow these steps:</p>
                <ol class="step-list">
                    <li>Separate the variables: $\frac{dy}{g(y)} = f(t) dt$</li>
                    <li>Integrate both sides: $\int \frac{dy}{g(y)} = \int f(t) dt$</li>
                    <li>If antiderivatives exist and we can solve for $y$, then we get: $G(y) = F(t) + C$</li>
                </ol>
            </div>
            
            <div class="example-box">
                <h4>Example: Solving a Separable Differential Equation</h4>
                <p>Solve the differential equation: $y' = 2t(25 - y)$</p>
                
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Rewrite: $\frac{dy}{dt} = 2t(25 - y)$</li>
                    <li>Separate variables: $\frac{dy}{25 - y} = 2t \, dt$</li>
                    <li>Integrate both sides: $\int \frac{dy}{25 - y} = \int 2t \, dt$</li>
                    <li>Left side: $\int \frac{dy}{25 - y} = -\ln|25 - y| + C_1$</li>
                    <li>Right side: $\int 2t \, dt = t^2 + C_2$</li>
                    <li>Combine constants: $-\ln|25 - y| = t^2 + C$</li>
                    <li>Solve for $y$: $\ln|25 - y| = -t^2 - C$</li>
                    <li>Exponentiate: $|25 - y| = e^{-t^2 - C} = Ae^{-t^2}$ where $A = e^{-C}$</li>
                    <li>Therefore: $y = 25 - Ae^{-t^2}$</li>
                </ol>
            </div>
            
            <div class="svg-container">
                <h3>Solutions to dy/dx = 2x with different initial conditions</h3>
                <svg width="500" height="400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Coordinate system -->
                    <line x1="50" y1="350" x2="450" y2="350" stroke="black" stroke-width="1"/>
                    <line x1="50" y1="350" x2="50" y2="50" stroke="black" stroke-width="1"/>
                    
                    <!-- Labels -->
                    <text x="440" y="370" font-family="Arial" font-size="16">x</text>
                    <text x="30" y="60" font-family="Arial" font-size="16">y</text>
                    
                    <!-- Multiple solution curves for different C values -->
                    <path d="M 50 300 Q 150 250 250 200 Q 350 150 450 100" stroke="blue" stroke-width="2" fill="none"/>
                    <path d="M 50 325 Q 150 275 250 225 Q 350 175 450 125" stroke="green" stroke-width="2" fill="none"/>
                    <path d="M 50 350 Q 150 300 250 250 Q 350 200 450 150" stroke="red" stroke-width="2" fill="none"/>
                    <path d="M 50 375 Q 150 325 250 275 Q 350 225 450 175" stroke="purple" stroke-width="2" fill="none"/>
                    
                    <!-- Legend -->
                    <rect x="350" y="60" width="120" height="80" fill="white" stroke="black" stroke-width="1"/>
                    <line x1="360" y1="80" x2="380" y2="80" stroke="blue" stroke-width="2"/>
                    <text x="390" y="85" font-family="Arial" font-size="14">C = -5</text>
                    <line x1="360" y1="100" x2="380" y2="100" stroke="green" stroke-width="2"/>
                    <text x="390" y="105" font-family="Arial" font-size="14">C = 0</text>
                    <line x1="360" y1="120" x2="380" y2="120" stroke="red" stroke-width="2"/>
                    <text x="390" y="125" font-family="Arial" font-size="14">C = 5</text>
                    <line x1="360" y1="140" x2="380" y2="140" stroke="purple" stroke-width="2"/>
                    <text x="390" y="145" font-family="Arial" font-size="14">C = 10</text>
                </svg>
                <p>Different values of the constant C produce different solution curves for the same differential equation.</p>
            </div>
        </div>

        <div class="section" id="growth">
            <h2>5. Simple Growth and Decay Models</h2>
            
            <div class="definition-box">
                <h4>Simple Growth and Decay Model</h4>
                <p>The differential equation:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = ky$$
                </div>
                <p>with growth rate constant $k$ models simple growth and decay of a quantity $y$ at time $t$ with solution:</p>
                <div class="math-display">
                    $$y = y_0 e^{kt}$$
                </div>
                <p>where $y_0$ is the initial value at time $t = 0$.</p>
            </div>
            
            <div class="example-box">
                <h4>Example: Simple Growth Model</h4>
                <p>Suppose $5,000 is deposited into an account which earns continuously compounded interest. Under these conditions, the balance in the account grows at a rate proportional to the current balance. Suppose that after 4 years the account is worth $7,000.</p>
                
                <p><strong>Problem 1:</strong> How much is the account worth after 5 years?</p>
                <p><strong>Problem 2:</strong> How many years does it take for the balance to double?</p>
                
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>We model this with: $\frac{dB}{dt} = kB$, so $B(t) = B_0 e^{kt}$</li>
                    <li>Given: $B_0 = 5000$, $B(4) = 7000$</li>
                    <li>Find $k$: $7000 = 5000e^{4k}$ → $1.4 = e^{4k}$ → $k = \frac{\ln(1.4)}{4} \approx 0.084$</li>
                    <li>So $B(t) = 5000e^{0.084t}$</li>
                    <li>After 5 years: $B(5) = 5000e^{0.084(5)} \approx 5000e^{0.42} \approx \$7,540$</li>
                    <li>To double: $10000 = 5000e^{0.084t}$ → $2 = e^{0.084t}$ → $t = \frac{\ln(2)}{0.084} \approx 8.25$ years</li>
                </ol>
            </div>
            
            <div class="svg-container">
                <h3>Exponential Growth and Decay</h3>
                <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Coordinate system -->
                    <line x1="50" y1="350" x2="550" y2="350" stroke="black" stroke-width="1"/>
                    <line x1="50" y1="350" x2="50" y2="50" stroke="black" stroke-width="1"/>
                    
                    <!-- Labels -->
                    <text x="540" y="370" font-family="Arial" font-size="16">t</text>
                    <text x="30" y="60" font-family="Arial" font-size="16">y</text>
                    
                    <!-- Exponential growth curve (k > 0) -->
                    <path d="M 50 300 C 150 250 250 150 350 100 C 450 75 500 60 550 50" stroke="green" stroke-width="3" fill="none"/>
                    <text x="450" y="80" font-family="Arial" font-size="14" fill="green">Growth (k > 0)</text>
                    
                    <!-- Exponential decay curve (k < 0) -->
                    <path d="M 50 100 C 150 150 250 250 350 300 C 450 325 500 340 550 350" stroke="red" stroke-width="3" fill="none"/>
                    <text x="450" y="320" font-family="Arial" font-size="14" fill="red">Decay (k < 0)</text>
                    
                    <!-- Initial value line -->
                    <line x1="50" y1="200" x2="550" y2="200" stroke="blue" stroke-width="1" stroke-dasharray="5,5"/>
                    <text x="450" y="190" font-family="Arial" font-size="14" fill="blue">Initial Value</text>
                </svg>
                <p>Exponential growth (green) and decay (red) curves showing how quantities change over time.</p>
            </div>
        </div>

        <div class="section" id="logistic">
            <h2>6. Logistic Growth Model</h2>
            
            <p>The simple growth model is unrealistic because a quantity that represents something from real life, say, population, does not grow unrestricted. Typically, food resources, competition, predators, or diseases, to name but a few factors, influence the growth of the population, and how much of the population can be sustained in such an environment.</p>
            
            <div class="definition-box">
                <h4>Logistic Growth Model</h4>
                <p>The differential equation:</p>
                <div class="math-display">
                    $$\frac{dy}{dt} = ry(M - y)$$
                </div>
                <p>with positive growth constant $r$ and carrying capacity $M$ models logistic growth of a quantity $y$ at time $t$ with solution:</p>
                <div class="math-display">
                    $$y = \frac{M}{1 + Ae^{-rMt}}$$
                </div>
                <p>where $A = \frac{M - y_0}{y_0}$ at time $t_0 = 0$.</p>
            </div>
            
            <div class="svg-container">
                <h3>Logistic Growth Curve</h3>
                <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Coordinate system -->
                    <line x1="50" y1="350" x2="550" y2="350" stroke="black" stroke-width="1"/>
                    <line x1="50" y1="350" x2="50" y2="50" stroke="black" stroke-width="1"/>
                    
                    <!-- Labels -->
                    <text x="540" y="370" font-family="Arial" font-size="16">t</text>
                    <text x="30" y="60" font-family="Arial" font-size="16">y</text>
                    <text x="50" y="370" font-family="Arial" font-size="14">0</text>
                    <text x="30" y="350" font-family="Arial" font-size="14">0</text>
                    <text x="30" y="100" font-family="Arial" font-size="14">M</text>
                    
                    <!-- Carrying capacity line -->
                    <line x1="50" y1="100" x2="550" y2="100" stroke="blue" stroke-width="1" stroke-dasharray="5,5"/>
                    <text x="480" y="90" font-family="Arial" font-size="14" fill="blue">Carrying Capacity (M)</text>
                    
                    <!-- Logistic curve -->
                    <path d="M 50 300 C 100 280 150 250 200 200 C 250 150 300 120 350 105 C 400 101 450 100 500 100 C 550 100 550 100 550 100" stroke="green" stroke-width="3" fill="none"/>
                    
                    <!-- Inflection point -->
                    <circle cx="250" cy="200" r="5" fill="red"/>
                    <text x="260" y="190" font-family="Arial" font-size="14" fill="red">Inflection Point</text>
                    
                    <!-- Initial value -->
                    <circle cx="50" cy="300" r="5" fill="purple"/>
                    <text x="60" y="290" font-family="Arial" font-size="14" fill="purple">y₀</text>
                </svg>
                <p>The logistic growth curve shows how a population grows rapidly at first, then slows as it approaches the carrying capacity M.</p>
            </div>
            
            <div class="important-box">
                <h4>Key Characteristics of Logistic Growth</h4>
                <ul>
                    <li>When $y$ is small, growth is approximately exponential</li>
                    <li>As $y$ approaches $M$, growth slows down</li>
                    <li>The curve has an S-shape (sigmoid curve)</li>
                    <li>There is an inflection point at $y = M/2$ where growth is maximum</li>
                </ul>
            </div>
        </div>

        <div class="section" id="linear">
            <h2>7. First-Order Linear Differential Equations</h2>
            
            <div class="definition-box">
                <h4>Definition: First Order Linear Differential Equation</h4>
                <p>A first order linear differential equation has the form:</p>
                <div class="math-display">
                    $$y' + p(t)y = f(t)$$
                </div>
                <p>If $f(t) = 0$, the equation is homogeneous; otherwise, it is non-homogeneous.</p>
            </div>
            
            <div class="example-box">
                <h4>Example: First Order Homogeneous Linear DE</h4>
                <p>Solve the initial value problem:</p>
                <div class="math-display">
                    $$y' + y\cos(t) = 0, \quad y(0) = \frac{1}{2}$$
                </div>
                
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Rewrite: $y' = -y\cos(t)$</li>
                    <li>Separate variables: $\frac{dy}{y} = -\cos(t) dt$</li>
                    <li>Integrate: $\ln|y| = -\sin(t) + C$</li>
                    <li>Solve for $y$: $y = Ae^{-\sin(t)}$ where $A = e^C$</li>
                    <li>Apply initial condition: $\frac{1}{2} = Ae^{-\sin(0)} = A$</li>
                    <li>Therefore: $y = \frac{1}{2}e^{-\sin(t)}$</li>
                </ol>
            </div>
        </div>

        <div class="section" id="methods">
            <h2>8. Solution Methods for Linear Equations</h2>
            
            <div class="method-box">
                <h4>Method 1: Integrating Factor</h4>
                <p>For a first order linear differential equation in standard form:</p>
                <div class="math-display">
                    $$y' + p(t)y = f(t)$$
                </div>
                <p>The integrating factor is:</p>
                <div class="math-display">
                    $$I(t) = e^{\int p(t) dt}$$
                </div>
                <p>Multiplying the equation by $I(t)$ allows us to integrate both sides:</p>
                <div class="math-display">
                    $$y(t) = \frac{1}{I(t)} \left[ \int I(t)f(t) dt + C \right]$$
                </div>
            </div>
            
            <div class="method-box">
                <h4>Method 2: Variation of Parameters</h4>
                <p>If we know the solution to the homogeneous equation $y' + p(t)y = 0$ is $y_h = Ae^{P(t)}$ where $P(t) = -\int p(t) dt$, then for the non-homogeneous equation we assume a solution of the form:</p>
                <div class="math-display">
                    $$y = v(t)e^{P(t)}$$
                </div>
                <p>Substituting this into the original equation allows us to solve for $v(t)$.</p>
            </div>
            
            <div class="example-box">
                <h4>Example: Using Integrating Factor</h4>
                <p>Solve the initial value problem:</p>
                <div class="math-display">
                    $$y' + \frac{3}{t}y = t^2, \quad y(1) = \frac{1}{2}$$
                </div>
                
                <p><strong>Solution:</strong></p>
                <ol class="step-list">
                    <li>Identify $p(t) = \frac{3}{t}$ and $f(t) = t^2$</li>
                    <li>Find integrating factor: $I(t) = e^{\int \frac{3}{t} dt} = e^{3\ln(t)} = t^3$</li>
                    <li>Multiply equation by $I(t)$: $t^3 y' + 3t^2 y = t^5$</li>
                    <li>Left side is $\frac{d}{dt}[t^3 y]$, so: $\frac{d}{dt}[t^3 y] = t^5$</li>
                    <li>Integrate: $t^3 y = \frac{t^6}{6} + C$</li>
                    <li>Solve for $y$: $y = \frac{t^3}{6} + \frac{C}{t^3}$</li>
                    <li>Apply initial condition: $\frac{1}{2} = \frac{1}{6} + C$, so $C = \frac{1}{3}$</li>
                    <li>Final solution: $y = \frac{t^3}{6} + \frac{1}{3t^3}$</li>
                </ol>
            </div>
            
            <div class="svg-container">
                <h3>Direction Field for y' = 2x</h3>
                <svg width="500" height="400" xmlns="http://www.w3.org/2000/svg">
                    <!-- Coordinate system -->
                    <line x1="50" y1="350" x2="450" y2="350" stroke="black" stroke-width="1"/>
                    <line x1="50" y1="350" x2="50" y2="50" stroke="black" stroke-width="1"/>
                    
                    <!-- Labels -->
                    <text x="440" y="370" font-family="Arial" font-size="16">x</text>
                    <text x="30" y="60" font-family="Arial" font-size="16">y</text>
                    
                    <!-- Grid of slope indicators -->
                    <!-- We'll draw short line segments representing the slope at various points -->
                    <!-- For y' = 2x, the slope at point (x,y) is 2x -->
                    
                    <!-- Generate grid points and draw slope indicators -->
                    <!-- Row 1 (y = 300) -->
                    <line x1="100" y1="300" x2="110" y2="280" stroke="blue" stroke-width="1"/>
                    <line x1="150" y1="300" x2="160" y2="270" stroke="blue" stroke-width="1"/>
                    <line x1="200" y1="300" x2="210" y2="260" stroke="blue" stroke-width="1"/>
                    <line x1="250" y1="300" x2="260" y2="250" stroke="blue" stroke-width="1"/>
                    <line x1="300" y1="300" x2="310" y2="240" stroke="blue" stroke-width="1"/>
                    <line x1="350" y1="300" x2="360" y2="230" stroke="blue" stroke-width="1"/>
                    <line x1="400" y1="300" x2="410" y2="220" stroke="blue" stroke-width="1"/>
                    
                    <!-- Row 2 (y = 250) -->
                    <line x1="100" y1="250" x2="110" y2="230" stroke="blue" stroke-width="1"/>
                    <line x1="150" y1="250" x2="160" y2="220" stroke="blue" stroke-width="1"/>
                    <line x1="200" y1="250" x2="210" y2="210" stroke="blue" stroke-width="1"/>
                    <line x1="250" y1="250" x2="260" y2="200" stroke="blue" stroke-width="1"/>
                    <line x1="300" y1="250" x2="310" y2="190" stroke="blue" stroke-width="1"/>
                    <line x1="350" y1="250" x2="360" y2="180" stroke="blue" stroke-width="1"/>
                    <line x1="400" y1="250" x2="410" y2="170" stroke="blue" stroke-width="1"/>
                    
                    <!-- Row 3 (y = 200) -->
                    <line x1="100" y1="200" x2="110" y2="180" stroke="blue" stroke-width="1"/>
                    <line x1="150" y1="200" x2="160" y2="170" stroke="blue" stroke-width="1"/>
                    <line x1="200" y1="200" x2="210" y2="160" stroke="blue" stroke-width="1"/>
                    <line x1="250" y1="200" x2="260" y2="150" stroke="blue" stroke-width="1"/>
                    <line x1="300" y1="200" x2="310" y2="140" stroke="blue" stroke-width="1"/>
                    <line x1="350" y1="200" x2="360" y2="130" stroke="blue" stroke-width="1"/>
                    <line x1="400" y1="200" x2="410" y2="120" stroke="blue" stroke-width="1"/>
                    
                    <!-- Row 4 (y = 150) -->
                    <line x1="100" y1="150" x2="110" y2="130" stroke="blue" stroke-width="1"/>
                    <line x1="150" y1="150" x2="160" y2="120" stroke="blue" stroke-width="1"/>
                    <line x1="200" y1="150" x2="210" y2="110" stroke="blue" stroke-width="1"/>
                    <line x1="250" y1="150" x2="260" y2="100" stroke="blue" stroke-width="1"/>
                    <line x1="300" y1="150" x2="310" y2="90" stroke="blue" stroke-width="1"/>
                    <line x1="350" y1="150" x2="360" y2="80" stroke="blue" stroke-width="1"/>
                    <line x1="400" y1="150" x2="410" y2="70" stroke="blue" stroke-width="1"/>
                    
                    <!-- Solution curves -->
                    <path d="M 50 325 Q 150 275 250 225 Q 350 175 450 125" stroke="red" stroke-width="2" fill="none"/>
                    <path d="M 50 350 Q 150 300 250 250 Q 350 200 450 150" stroke="red" stroke-width="2" fill="none"/>
                    <path d="M 50 375 Q 150 325 250 275 Q 350 225 450 175" stroke="red" stroke-width="2" fill="none"/>
                </svg>
                <p>Direction field showing slopes at various points and solution curves for the differential equation y' = 2x.</p>
            </div>
        </div>

        <div class="conclusion">
            <h2>Conclusion</h2>
            <p>First-order differential equations are fundamental tools for modeling real-world phenomena. By understanding the different types of first-order differential equations and their solution methods, we can analyze and predict the behavior of systems in physics, biology, economics, and engineering.</p>
            <p>Key takeaways:</p>
            <ul style="text-align: left; max-width: 800px; margin: 20px auto; padding: 0 20px;">
                <li>Separable equations can be solved by separating variables and integrating both sides</li>
                <li>Linear equations can be solved using integrating factors or variation of parameters</li>
                <li>Exponential models describe unrestricted growth or decay</li>
                <li>Logistic models describe growth with limiting factors</li>
                <li>Initial value problems allow us to find specific solutions from general solutions</li>
            </ul>
            <p>With these tools and techniques, you're now equipped to solve a wide variety of first-order differential equations!</p>
        </div>
    </div>
</body>
</html>
