10.4 Encoder-Decoder Sequence-to-Sequence Architec
tures
 We have seen in figure
 10.5
 how an RNN can map an input sequence to a fixed-size
 vector. We have seen in figure
 10.9
 sequence. We have seen in figures
 how an RNN can map a fixed-size vector to a
 ,
 10.3 10.4 10.10
 ,
 and
 10.11
 how an RNN can
 map an input sequence to an output sequence of the same length.
 Encoder
 x(1)
 x(1)
 x(2)
 x(2)
 …
 ...
 ...
 x( )
 x( )
 C
 C
 Decoder
 y(1)
 y(1)
 y(2)
 y(2)
 …
 ...
 ...
 y( )
 y( )
 x(nx)
 x(nx)
 y(ny)
 y(ny)
 Figure 10.12: Example of an encoder-decoder or sequence-to-sequence RNN architecture,
 for learning to generate an output sequence (y(1),...,y(ny)) given an input sequence
 (x(1),x(2),. .. , x(nx)). It is composed of an encoder RNN that reads the input sequence
 and a decoder RNN that generates the output sequence (or computes the probability of a
 given output sequence). The final hidden state of the encoder RN<PERSON> is used to compute a
 generally fixed-size context variableC which represents a semantic summary of the input
 sequence and is given as input to the decoder RNN.
 Here we discuss how an RNN can be trained to map an input sequence to an
 output sequence which is not necessarily of the same length. This comes up in
 many applications, such as speech recognition, machine translation or question
 396
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 answering, where the input and output sequences in the training set are generally
 not of the same length (although their lengths might be related).
 We often call the input to the RNN the “context.” We want to produce a
 representation of this context,C. The context C might be a vector or sequence of
 = ( (1),...,x(nx)).
 vectors that summarize the input sequence X
 x
 The simplest RNN architecture for mapping a variable-length sequence to
 another variable-length sequence was first proposed by
 Cho et al. 2014a
 (
 shortly after by Sutskever
 et al. (
 2014
 ) and
 ), who independently developed that archi
tecture and were the first to obtain state-of-the-art translation using this approach.
 The former system is based on scoring proposals generated by another machine
 translation system, while the latter uses a standalone recurrent network to generate
 10.12
 the translations. These authors respectively called this architecture, illustrated
 in figure
 , the encoder-decoder or sequence-to-sequence architecture. The
 idea is very simple: (1) anencoder or reader or input RNN processes the input
 sequence. The encoder emits the context C, usually as a simple function of its
 f
 inal hidden state. (2) a decoder or writer or output RNN is conditioned on
 that fixed-length vector (just like in figure
 10.9
 ) to generate the output sequence
 Y = (y(1),...,y(ny)). The innovation of this kind of architecture over those
 presented in earlier sections of this chapter is that the lengths nx and ny can
 vary from each other, while previous architectures constrained nx = ny = τ. In a
 sequence-to-sequence architecture, the two RNNs are trained jointly to maximize
 the average of logP(y(1),...,y(ny) | x(1),...,x(nx)) over all the pairs of x and y
 sequences in the training set. The last state hnx 
of the encoder RNN is typically
 used as a representation C of the input sequence that is provided as input to the
 decoder RNN.
 If the context C is a vector, then the decoder RNN is simply a vector-to
sequence RNN as described in section
 10.2.4
 . As we have seen, there are at least
 two ways for a vector-to-sequence RNN to receive input. The input can be provided
 as the initial state of the RNN, or the input can be connected to the hidden units
 at each time step. These two ways can also be combined.
 There is no constraint that the encoder must have the same size of hidden layer
 as the decoder.
 One clear limitation of this architecture is when the context C output by the
 encoder RNN has a dimension that is too small to properly summarize a long
 sequence. This phenomenon was observed by
 Bahdanau et al. 2015
 (
 ) in the context
 of machine translation. They proposed to make C a variable-length sequence rather
 than a fixed-size vector. Additionally, they introduced an attention mechanism
 that learns to associate elements of the sequence C to elements of the output
 397
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 sequence. See section
 12.4.5.1
 for more details.
 10.5 Deep Recurrent Networks
 The computation in most RNNs can be decomposed into three blocks of parameters
 and associated transformations:
 1. from the input to the hidden state,
 2. from the previous hidden state to the next hidden state, and
 3. from the hidden state to the output.
 With the RNN architecture of figure
 10.3
 , each of these three blocks is associated
 with a single weight matrix. In other words, when the network is unfolded, each
 of these corresponds to a shallow transformation. By a shallow transformation,
 we mean a transformation that would be represented by a single layer within
 a deep MLP. Typically this is a transformation represented by a learned affine
 transformation followed by a fixed nonlinearity.
 Would it be advantageous to introduce depth in each of these operations?
 Experimental evidence (Graves
 et al.,
 ;
 2013 Pascanu
 et al.,
 2014a
 ) strongly suggests
 so. The experimental evidence is in agreement with the idea that we need enough
 depth in order to perform the required mappings. See also Schmidhuber 1992
 (
 El Hihi and Bengio 1996
 (
 Graves
 et al. (
 2013
 ), or
 Jaeger 2007a
 (
 ) for earlier work on deep RNNs.
 ),
 ) were the first to show a significant benefit of decomposing
 the state of an RNN into multiple layers as in figure
 10.13
 of the lower layers in the hierarchy depicted in figure
 (left). We can think
 10.13
 a as playing a role
 in transforming the raw input into a representation that is more appropriate, at
 the higher levels of the hidden state. Pascanu
 et al. (
 2014a
 ) go a step further
 and propose to have a separate MLP (possibly deep) for each of the three blocks
 enumerated above, as illustrated in figure
 10.13
 b. Considerations of representational
 capacity suggest to allocate enough capacity in each of these three steps, but doing
 so by adding depth may hurt learning by making optimization difficult. In general,
 10.13
 it is easier to optimize shallower architectures, and adding the extra depth of
 f
 igure
 b makes the shortest path from a variable in time step
 t to a variable
 in time step t+1 become longer. For example, if an MLP with a single hidden
 layer is used for the state-to-state transition, we have doubled the length of the
 shortest path between variables in any two different time steps, compared with the
 ordinary RNN of figure
 10.3
 . However, as argued by
 Pascanu
 et al. (
 2014a
 ), this
 398
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 y
 y
 z
 h
 x
 (a)
 2014a (a)
 ).
 h
 x
 (b)
 y
 h
 x
 (c)
 Figure 10.13: A recurrent neural network can be made deep in many ways (Pascanu
 et al.,
 The hidden recurrent state can be broken down into groups organized
 hierarchically.
 (b)
 Deeper computation (e.g., an MLP) can be introduced in the input-to
hidden, hidden-to-hidden and hidden-to-output parts. This may lengthen the shortest
 path linking different time steps.
 (c)
 introducing skip connections.
 The path-lengthening effect can be mitigated by
 399
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 can be mitigated by introducing skip connections in the hidden-to-hidden path, as
 illustrated in figure
 10.13
 c.
 10.6 Recursive Neural Networks
 LL
 oo
 U
 U W
 W
 yy
 U W
 V
 x(1)
 x(1)
 V
 x(2)
 x(2)
 V
 x(3)
 x(3)
 V
 x(4)
 x(4)
 Figure 10.14: A recursive network has a computational graph that generalizes that of the
 recurrent network from a chain to a tree. A variable-size sequencex(1),x(2),...,x( ) t can
 be mapped to a fixed-size representation (the outputo), with a fixed set of parameters
 (the weight matrices U, V , W). The figure illustrates a supervised learning case in which
 some target is provided which is associated with the whole sequence.
 y
 Recursive neural networks2 represent yet another generalization of recurrent
 networks, with a different kind of computational graph, which is structured as a
 deep tree, rather than the chain-like structure of RNNs. The typical computational
 graph for a recursive network is illustrated in figure
 10.14
 .
 Recursive neural
 2We suggest to not abbreviate “recursive neural network” as “RNN” to avoid confusion with
 “recurrent neural network.”
 400
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 networks were introduced by Pollack 1990
 (
 reason was described by
 Bottou 2011
 (
 ) and their potential use for learning to
 ). Recursive networks have been successfully
 applied to processing data structures as input to neural nets (Frasconi
 1998
 ), in natural language processing (
 Socher
 et al.,
 , ,
 2011a c 2013a
 et al.,
 1997
 ,
 ) as well as in
 computer vision (
 Socher et al. 2011b
 ,
 ).
 One clear advantage of recursive nets over recurrent nets is that for a sequence
 of the same length τ, the depth (measured as the number of compositions of
 nonlinear operations) can be drastically reduced from τ to O(logτ), which might
 help deal with long-term dependencies. An open question is how to best structure
 the tree. One option is to have a tree structure which does not depend on the data,
 such as a balanced binary tree. In some application domains, external methods
 can suggest the appropriate tree structure. For example, when processing natural
 language sentences, the tree structure for the recursive network can be fixed to
 the structure of the parse tree of the sentence provided by a natural language
 parser (
 ,
 Socher et al. 2011a 2013a
 ,
 ). Ideally, one would like the learner itself to
 discover and infer the tree structure that is appropriate for any given input, as
 suggested by
 1997
 Bottou 2011
 (
 ) and
 ).
 Many variants of the recursive net idea are possible. For example, Frasconi
 et al. (
 Frasconi
 et al. (
 1998
 ) associate the data with a tree structure,
 and associate the inputs and targets with individual nodes of the tree. The
 computation performed by each node does not have to be the traditional artificial
 neuron computation (affine transformation of all inputs followed by a monotone
 nonlinearity). For example,
 Socher et al. 2013a
 (
 ) propose using tensor operations
 and bilinear forms, which have previously been found useful to model relationships
 between concepts (Weston
 et al.,
 ;
 2010 Bordes
 et al.,
 
 ) when the concepts are represented by continuous vectors (embeddings).
 ts not exploding), the difficulty with long-term dependencies arises
 from the exponentially smaller weights given to long-term interactions (involving
 the multiplication of many Jacobians) compared to short-term ones. Many other
 sources provide a deeper treatment (
 ,
 ;
 Hochreiter 1991 Doya 1993 Bengio
 ,
 ;
 et al.,
 401
CHAPTER10. SEQUENCEMODELING:RECURRENTANDRECURSIVENETS
 − − − 60 40 20 0 20 40 60
 Inputcoordinate
 −4
 −3
 −2
 −1
 0
 1
 2
 3
 4
 Projectionofoutput
 0
 1
 2
 3
 4
 5
 Figure10.15:Whencomposingmanynonlinearfunctions(likethelinear-tanhlayershown
 here),theresultishighlynonlinear,typicallywithmostofthevaluesassociatedwithatiny
 derivative,somevalueswithalargederivative,andmanyalternationsbetweenincreasing
 anddecreasing. Inthisplot,weplotalinearprojectionofa100-dimensionalhiddenstate
 downtoasingledimension,plottedonthey-axis. Thex-axis isthecoordinateofthe
 initialstatealongarandomdirectioninthe100-dimensionalspace.Wecanthusviewthis
 plotasalinearcross-sectionofahigh-dimensional function.Theplotsshowthefunction
 aftereachtimestep,orequivalently,aftereachnumberoftimesthetransitionfunction
 hasbeencomposed.
 1994 Pascanu 2013 ; et al., ) . Inthis section,wedescribetheprobleminmore
 detail.Theremainingsectionsdescribeapproachestoovercomingtheproblem.
 Recurrentnetworks involve thecompositionof the same functionmultiple
 times,oncepertimestep.Thesecompositionscanresult inextremelynonlinear
 behavior,as illustratedinfigure . 10.15
 Inparticular, thefunctioncompositionemployedbyrecurrentneuralnetworks
 somewhatresemblesmatrixmultiplication.Wecanthinkoftherecurrencerelation
 h() t = Wh( 1) t− (10.36)
 asaverysimplerecurrentneuralnetworklackinganonlinearactivationfunction,
 and lacking inputsx. As described insection , this recurrence relation 8.2.5
 essentiallydescribesthepowermethod. Itmaybesimplifiedto
 h() t =Wth(0), (10.37)
 andif admitsaneigendecompositionoftheform W
 W QQ = Λ , (10.38)
 402
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 Q
 with orthogonal , the recurrence may be simplified further to
 h( ) t = QΛtQh(0).
 (10.39)
 The eigenvalues are raised to the power of t causing eigenvalues with magnitude
 less than one to decay to zero and eigenvalues with magnitude greater than one to
 explode. Any component of h(0) that is not aligned with the largest eigenvector
 will eventually be discarded.
 This problem is particular to recurrent networks. In the scalar case, imagine
 multiplying a weight w by itself many times. The product wt will either vanish or
 explode depending on the magnitude of w. However, if we make a non-recurrent
 network that has a different weightw( ) t at each time step, the situation is different.
 If the initial state is given by , then the state at time
 1
 t is given by 
 tw( ) t . Suppose
 that the w( ) t values are generated randomly, independently from one another, with
 zero mean and variance v. The variance of the product is O(vn). To obtain some
 desired variance v∗ we may choose the individual weights with variancev = n
 √v∗.
 Very deep feedforward networks with carefully chosen scaling can thus avoid the
 vanishing and exploding gradient problem, as argued by
 Sussillo 2014
 (
 ).
 The vanishing and exploding gradient problem for RNNs was independently
 discovered by separate researchers (
 ,
 ;
 Hochreiter 1991 Bengio et al. 1993 1994
 ,
 ,
 ).
 One may hope that the problem can be avoided simply by staying in a region of
 parameter space where the gradients do not vanish or explode. Unfortunately, in
 order to store memories in a way that is robust to small perturbations, the RNN
 must enter a region of parameter space where gradients vanish (
 Bengio et al. 1993
 ,
 ,
 1994). Specifically, whenever the model is able to represent long term dependencies,
 the gradient of a long term interaction has exponentially smaller magnitude than
 the gradient of a short term interaction. It does not mean that it is impossible
 to learn, but that it might take a very long time to learn long-term dependencies,
 because the signal about these dependencies will tend to be hidden by the smallest
 Bengio et al. 1994
 (
 f
 luctuations arising from short-term dependencies. In practice, the experiments
 in
 ) show that as we increase the span of the dependencies that
 need to be captured, gradient-based optimization becomes increasingly difficult,
 with the probability of successful training of a traditional RNN via SGD rapidly
 reaching 0 for sequences of only length 10 or 20.
 For a deeper treatment of recurrent networks as dynamical systems, see Doya
 (
 ),
 1993 Bengio et al. 1994
 (
 in Pascanu
 et al. (
 2013
 ) and
 Siegelmann and Sontag 1995
 (
 ), with a review
 ). The remaining sections of this chapter discuss various
 approaches that have been proposed to reduce the difficulty of learning long
term dependencies (in some cases allowing an RNN to learn dependencies across
 403
CHAPTER 10. SEQUENCE MODELING: RECURRENT AND RECURSIVE NETS
 hundreds of steps), but the problem of learning long-term dependencies remains
 one of the main challenges in deep learning.