<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maximum Likelihood Estimation Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 2rem 0;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
            font-weight: 300;
        }
        
        .outline {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            backdrop-filter: blur(10px);
        }
        
        .outline h2 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.8rem;
        }
        
        .outline-list {
            list-style: none;
            counter-reset: section;
        }
        
        .outline-list li {
            counter-increment: section;
            margin-bottom: 0.8rem;
            padding-left: 2rem;
            position: relative;
        }
        
        .outline-list li::before {
            content: counter(section);
            position: absolute;
            left: 0;
            top: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .outline-list li a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .outline-list li a:hover {
            color: #667eea;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 1.5rem;
            font-size: 2rem;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .section h3 {
            color: #764ba2;
            margin: 1.5rem 0 1rem 0;
            font-size: 1.5rem;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #e17055;
        }
        
        .formula-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: center;
        }
        
        .theorem-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #00b894;
        }
        
        .proof-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-left: 5px solid #28a745;
        }
        
        .progress-indicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            z-index: 1000;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="progress-indicator">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <div class="container">
        <header>
            <h1>Maximum Likelihood Estimation</h1>
            <p class="subtitle">A Comprehensive Tutorial on MLE Theory and Applications</p>
        </header>
        
        <div class="outline">
            <h2>Tutorial Outline</h2>
            <ol class="outline-list">
                <li><a href="#introduction">Introduction to Maximum Likelihood Estimation</a></li>
                <li><a href="#invariance">Invariance Property of MLEs</a></li>
                <li><a href="#expected-likelihood">Properties of Expected Log-Likelihood</a></li>
                <li><a href="#consistency">Consistency of Maximum Likelihood Estimators</a></li>
                <li><a href="#asymptotic">Large Sample Distribution of MLEs</a></li>
                <li><a href="#likelihood-ratio">Generalized Likelihood Ratio Test</a></li>
                <li><a href="#visualization">Conceptual Framework and Visualizations</a></li>
            </ol>
        </div>
        
        <!-- Introduction Section -->
        <section id="introduction" class="section">
            <h2>Introduction to Maximum Likelihood Estimation</h2>
            
            <p>Maximum Likelihood Estimation (MLE) is one of the most fundamental and widely used methods in statistical inference. It provides a principled approach to estimating parameters of statistical models by finding the parameter values that make the observed data most probable.</p>
            
            <h3>The Core Concept</h3>
            
            <div class="highlight-box">
                <p><strong>Central Idea:</strong> Given observed data and a statistical model, the maximum likelihood estimator chooses the parameter values that maximize the probability (or likelihood) of observing the data we actually observed.</p>
            </div>
            
            <div class="formula-box">
                <h4>Mathematical Definition</h4>
                <p>For observations $y_1, y_2, \ldots, y_n$ from a distribution with probability density function $f(y; \theta)$, the <strong>likelihood function</strong> is:</p>
                $$L(\theta) = \prod_{i=1}^n f(y_i; \theta)$$
                
                <p>The <strong>maximum likelihood estimator</strong> is:</p>
                $$\hat{\theta}_{MLE} = \arg\max_{\theta} L(\theta)$$
                
                <p>Equivalently, using the <strong>log-likelihood</strong>:</p>
                $$\hat{\theta}_{MLE} = \arg\max_{\theta} \ell(\theta) = \arg\max_{\theta} \sum_{i=1}^n \log f(y_i; \theta)$$
            </div>
            
            <h3>Why Use the Log-Likelihood?</h3>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                <div class="theorem-box">
                    <h4>Computational Advantages</h4>
                    <ul>
                        <li><strong>Products → Sums:</strong> Easier to differentiate</li>
                        <li><strong>Numerical Stability:</strong> Avoids underflow issues</li>
                        <li><strong>Monotonic Transformation:</strong> Same maximum point</li>
                    </ul>
                </div>
                
                <div class="theorem-box">
                    <h4>Theoretical Benefits</h4>
                    <ul>
                        <li><strong>Additive Structure:</strong> Facilitates asymptotic analysis</li>
                        <li><strong>Central Limit Theorem:</strong> Enables large-sample theory</li>
                        <li><strong>Information Theory:</strong> Connection to entropy</li>
                    </ul>
                </div>
            </div>
            
            <h3>Key Properties Overview</h3>
            
            <p>This tutorial covers the fundamental theoretical properties of MLEs that make them so valuable in statistical practice:</p>
            
            <div style="display: flex; justify-content: center; margin: 2rem 0;">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#667eea">
                        Maximum Likelihood Estimation: Key Properties
                    </text>
                    
                    <!-- Central MLE concept -->
                    <circle cx="400" cy="250" r="80" fill="#667eea" stroke="#764ba2" stroke-width="3"/>
                    <text x="400" y="245" text-anchor="middle" font-size="14" font-weight="bold" fill="white">
                        Maximum
                    </text>
                    <text x="400" y="260" text-anchor="middle" font-size="14" font-weight="bold" fill="white">
                        Likelihood
                    </text>
                    <text x="400" y="275" text-anchor="middle" font-size="14" font-weight="bold" fill="white">
                        Estimator
                    </text>
                    
                    <!-- Property boxes -->
                    <!-- Invariance -->
                    <rect x="50" y="80" width="140" height="80" fill="#ffeaa7" stroke="#e17055" stroke-width="2" rx="10"/>
                    <text x="120" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Invariance
                    </text>
                    <text x="120" y="125" text-anchor="middle" font-size="10" fill="#2d3436">
                        If θ̂ is MLE of θ,
                    </text>
                    <text x="120" y="140" text-anchor="middle" font-size="10" fill="#2d3436">
                        then g(θ̂) is MLE of g(θ)
                    </text>
                    <text x="120" y="155" text-anchor="middle" font-size="9" fill="#636e72">
                        Flexible parameterization
                    </text>
                    
                    <!-- Expected Log-Likelihood -->
                    <rect x="250" y="80" width="140" height="80" fill="#a8edea" stroke="#00b894" stroke-width="2" rx="10"/>
                    <text x="320" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Expected
                    </text>
                    <text x="320" y="120" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Log-Likelihood
                    </text>
                    <text x="320" y="140" text-anchor="middle" font-size="10" fill="#2d3436">
                        E[∂ℓ/∂θ] = 0
                    </text>
                    <text x="320" y="155" text-anchor="middle" font-size="9" fill="#636e72">
                        Foundation of theory
                    </text>
                    
                    <!-- Consistency -->
                    <rect x="450" y="80" width="140" height="80" fill="#fab1a0" stroke="#e84393" stroke-width="2" rx="10"/>
                    <text x="520" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Consistency
                    </text>
                    <text x="520" y="125" text-anchor="middle" font-size="10" fill="#2d3436">
                        θ̂ₙ → θ₀ as n → ∞
                    </text>
                    <text x="520" y="140" text-anchor="middle" font-size="10" fill="#2d3436">
                        (in probability)
                    </text>
                    <text x="520" y="155" text-anchor="middle" font-size="9" fill="#636e72">
                        Asymptotic unbiasedness
                    </text>
                    
                    <!-- Asymptotic Normality -->
                    <rect x="610" y="80" width="140" height="80" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="10"/>
                    <text x="680" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Asymptotic
                    </text>
                    <text x="680" y="120" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Normality
                    </text>
                    <text x="680" y="140" text-anchor="middle" font-size="10" fill="#2d3436">
                        θ̂ ~ N(θ₀, I⁻¹)
                    </text>
                    <text x="680" y="155" text-anchor="middle" font-size="9" fill="#636e72">
                        Large sample inference
                    </text>
                    
                    <!-- Information -->
                    <rect x="150" y="380" width="140" height="80" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                    <text x="220" y="405" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Fisher
                    </text>
                    <text x="220" y="420" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Information
                    </text>
                    <text x="220" y="440" text-anchor="middle" font-size="10" fill="#2d3436">
                        I = E[(∂ℓ/∂θ)²]
                    </text>
                    <text x="220" y="455" text-anchor="middle" font-size="9" fill="#636e72">
                        Precision measure
                    </text>
                    
                    <!-- Likelihood Ratio Test -->
                    <rect x="510" y="380" width="140" height="80" fill="#f8e8f8" stroke="#6f42c1" stroke-width="2" rx="10"/>
                    <text x="580" y="405" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Likelihood
                    </text>
                    <text x="580" y="420" text-anchor="middle" font-size="12" font-weight="bold" fill="#2d3436">
                        Ratio Test
                    </text>
                    <text x="580" y="440" text-anchor="middle" font-size="10" fill="#2d3436">
                        2λ ~ χ²ᵣ
                    </text>
                    <text x="580" y="455" text-anchor="middle" font-size="9" fill="#636e72">
                        Hypothesis testing
                    </text>
                    
                    <!-- Arrows connecting to central concept -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                        </marker>
                    </defs>
                    
                    <!-- From properties to MLE -->
                    <line x1="190" y1="150" x2="340" y2="210" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="320" y1="160" x2="360" y2="200" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="520" y1="160" x2="460" y2="200" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="650" y1="150" x2="460" y2="210" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="220" y1="380" x2="340" y2="300" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="580" y1="380" x2="460" y2="300" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Applications note -->
                    <rect x="200" y="200" width="400" height="100" fill="none" stroke="#667eea" stroke-width="2" stroke-dasharray="5,5" rx="10"/>
                    <text x="400" y="230" text-anchor="middle" font-size="12" font-weight="bold" fill="#667eea">
                        Applications
                    </text>
                    <text x="400" y="250" text-anchor="middle" font-size="11" fill="#667eea">
                        • Generalized Linear Models (GLMs)
                    </text>
                    <text x="400" y="265" text-anchor="middle" font-size="11" fill="#667eea">
                        • Generalized Additive Models (GAMs)
                    </text>
                    <text x="400" y="280" text-anchor="middle" font-size="11" fill="#667eea">
                        • Mixed Effects Models
                    </text>
                </svg>
            </div>
            
            <h3>Fundamental Statistical Results</h3>
            
            <p>The theory of maximum likelihood estimation relies on two cornerstone results from probability theory:</p>
            
            <div class="theorem-box">
                <h4>Law of Large Numbers (LOLN)</h4>
                <p>For i.i.d. random variables $X_1, X_2, \ldots, X_n$ with mean $\mu$ and finite variance $\sigma^2$:</p>
                $$\bar{X}_n = \frac{1}{n}\sum_{i=1}^n X_i \xrightarrow{p} \mu \text{ as } n \to \infty$$
                <p><strong>Interpretation:</strong> Sample averages converge in probability to the population mean.</p>
            </div>
            
            <div class="theorem-box">
                <h4>Central Limit Theorem (CLT)</h4>
                <p>For the same setup as above:</p>
                $$\sqrt{n}(\bar{X}_n - \mu) \xrightarrow{d} N(0, \sigma^2) \text{ as } n \to \infty$$
                <p><strong>Interpretation:</strong> Properly normalized sample means are asymptotically normal, regardless of the underlying distribution.</p>
            </div>
            
            <div class="highlight-box">
                <h4>Why These Results Matter for MLE</h4>
                <ul>
                    <li><strong>LOLN:</strong> Ensures that the sample log-likelihood converges to its expected value</li>
                    <li><strong>CLT:</strong> Provides the foundation for asymptotic normality of MLEs</li>
                    <li><strong>Combined:</strong> Enable rigorous large-sample inference procedures</li>
                </ul>
            </div>
            
            <h3>Tutorial Structure</h3>
            
            <p>This tutorial systematically develops MLE theory through six main topics:</p>
            
            <div class="formula-box">
                <ol style="text-align: left; display: inline-block;">
                    <li><strong>Invariance:</strong> Flexibility in parameterization</li>
                    <li><strong>Expected Log-Likelihood:</strong> Four fundamental results</li>
                    <li><strong>Consistency:</strong> Convergence to true parameter values</li>
                    <li><strong>Asymptotic Distribution:</strong> Large-sample normality</li>
                    <li><strong>Likelihood Ratio Tests:</strong> Hypothesis testing framework</li>
                    <li><strong>Applications:</strong> GLMs, GAMs, and mixed models</li>
                </ol>
                         </div>
         </section>
         
         <!-- Invariance Section -->
         <section id="invariance" class="section">
             <h2>Invariance Property of MLEs</h2>
             
             <p>One of the most elegant and practically useful properties of maximum likelihood estimators is their <strong>invariance under transformation</strong>. This property allows us to work with the most convenient parameterization during estimation and then transform to the most interpretable parameterization afterward.</p>
             
             <div class="theorem-box">
                 <h4>Invariance Property</h4>
                 <p>Let $\hat{\theta}$ be the MLE of parameter $\theta$. If $\gamma = g(\theta)$ where $g$ is any function, then the MLE of $\gamma$ is:</p>
                 $$\hat{\gamma} = g(\hat{\theta})$$
                 <p><strong>In words:</strong> The MLE of a function of the parameter is the function of the MLE of the parameter.</p>
             </div>
             
             <h3>Mathematical Proof (One-to-One Case)</h3>
             
             <div class="proof-box">
                 <h4>Proof Outline</h4>
                 <p>Consider the case where $g$ is a one-to-one function (bijective), so $g^{-1}$ exists.</p>
                 
                 <p><strong>Step 1:</strong> Express the original parameterization in terms of $\gamma$</p>
                 <p>Since $\gamma = g(\theta)$, we have $\theta = g^{-1}(\gamma)$</p>
                 
                 <p><strong>Step 2:</strong> Write the likelihood in terms of $\gamma$</p>
                 $$L(\gamma) = f(\mathbf{y}; g^{-1}(\gamma))$$
                 
                 <p><strong>Step 3:</strong> Find the maximum</p>
                 <p>We know that $L(\theta)$ achieves its maximum at $\hat{\theta}$ by definition of MLE.</p>
                 <p>Therefore, $L(\gamma)$ achieves its maximum when $g^{-1}(\hat{\gamma}) = \hat{\theta}$</p>
                 
                 <p><strong>Step 4:</strong> Conclude</p>
                 <p>This implies $\hat{\gamma} = g(\hat{\theta})$ ✓</p>
             </div>
             
             <h3>Why Invariance Matters</h3>
             
             <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                 <div class="highlight-box">
                     <h4>Computational Convenience</h4>
                     <ul>
                         <li><strong>Natural Scale:</strong> Work on scale where math is simpler</li>
                         <li><strong>Numerical Stability:</strong> Avoid extreme values during optimization</li>
                         <li><strong>Constraint Handling:</strong> Transform to unconstrained space</li>
                     </ul>
                 </div>
                 
                 <div class="highlight-box">
                     <h4>Interpretive Flexibility</h4>
                     <ul>
                         <li><strong>Meaningful Units:</strong> Present results in interpretable scale</li>
                         <li><strong>Domain Knowledge:</strong> Use parameterization familiar to practitioners</li>
                         <li><strong>Comparison:</strong> Match literature conventions</li>
                     </ul>
                 </div>
             </div>
             
             <h3>Practical Examples</h3>
             
             <div class="theorem-box">
                 <h4>Example 1: Variance Parameterization</h4>
                 <p><strong>Scenario:</strong> Normal distribution with unknown variance</p>
                 <p><strong>Computational:</strong> Estimate $\log(\sigma^2)$ (unconstrained)</p>
                 <p><strong>Interpretation:</strong> Report $\hat{\sigma}^2 = \exp(\widehat{\log(\sigma^2)})$</p>
                 
                 <div class="formula-box">
                     <p>If $\hat{\phi}$ is MLE of $\phi = \log(\sigma^2)$, then:</p>
                     $$\hat{\sigma}^2 = \exp(\hat{\phi})$$
                     <p>is the MLE of $\sigma^2$ by invariance.</p>
                 </div>
             </div>
             
             <div class="theorem-box">
                 <h4>Example 2: Rate vs. Mean Parameterization</h4>
                 <p><strong>Scenario:</strong> Poisson distribution</p>
                 <p><strong>Rate parameterization:</strong> $P(Y = k) = \frac{\lambda^k e^{-\lambda}}{k!}$</p>
                 <p><strong>Mean parameterization:</strong> $\mu = \lambda$ (same thing!)</p>
                 
                 <div class="formula-box">
                     <p>If we estimate $\hat{\lambda}$ directly, then:</p>
                     $$\hat{\mu} = \hat{\lambda}$$
                     <p>But if we estimate $\log(\lambda)$ and get $\hat{\phi}$, then:</p>
                     $$\hat{\lambda} = \exp(\hat{\phi})$$
                 </div>
             </div>
             
             <h3>Vector Parameter Case</h3>
             
             <div class="theorem-box">
                 <h4>Multivariate Invariance</h4>
                 <p>The invariance property extends naturally to vector parameters:</p>
                 <p>If $\hat{\boldsymbol{\theta}}$ is the MLE of $\boldsymbol{\theta}$ and $\boldsymbol{\gamma} = \mathbf{g}(\boldsymbol{\theta})$, then:</p>
                 $$\hat{\boldsymbol{\gamma}} = \mathbf{g}(\hat{\boldsymbol{\theta}})$$
                 
                 <p><strong>Example:</strong> In linear regression with $\boldsymbol{\theta} = (\beta_0, \beta_1, \sigma^2)$</p>
                 <ul style="text-align: left; display: inline-block;">
                     <li>Prediction at $x_0$: $\hat{y}_0 = \hat{\beta}_0 + \hat{\beta}_1 x_0$</li>
                     <li>Standard deviation: $\hat{\sigma} = \sqrt{\hat{\sigma}^2}$</li>
                     <li>Coefficient of variation: $\widehat{CV} = \hat{\sigma}/\hat{\mu}$</li>
                 </ul>
             </div>
             
             <h3>Visualization of Invariance</h3>
             
             <div style="display: flex; justify-content: center; margin: 2rem 0;">
                 <svg width="700" height="400" viewBox="0 0 700 400">
                     <!-- Background -->
                     <rect width="700" height="400" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                     
                     <!-- Title -->
                     <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                         Invariance Property Illustration
                     </text>
                     
                     <!-- Original parameter space -->
                     <rect x="50" y="80" width="250" height="120" fill="#e8f4f8" stroke="#17a2b8" stroke-width="2" rx="10"/>
                     <text x="175" y="105" text-anchor="middle" font-size="14" font-weight="bold" fill="#0c5460">
                         Original Parameter θ
                     </text>
                     
                     <!-- Likelihood curve in original space -->
                     <g transform="translate(70, 120)">
                         <path d="M 0 60 Q 50 40 100 20 Q 150 10 200 30 Q 210 40 210 60" 
                               stroke="#17a2b8" stroke-width="2" fill="none"/>
                         <circle cx="150" cy="10" r="4" fill="#dc3545"/>
                         <text x="150" y="0" text-anchor="middle" font-size="10" fill="#dc3545">θ̂</text>
                         <text x="105" y="85" text-anchor="middle" font-size="12" fill="#0c5460">L(θ)</text>
                     </g>
                     
                     <!-- Transformed parameter space -->
                     <rect x="400" y="80" width="250" height="120" fill="#f8e8e8" stroke="#dc3545" stroke-width="2" rx="10"/>
                     <text x="525" y="105" text-anchor="middle" font-size="14" font-weight="bold" fill="#721c24">
                         Transformed Parameter γ = g(θ)
                     </text>
                     
                     <!-- Likelihood curve in transformed space -->
                     <g transform="translate(420, 120)">
                         <path d="M 0 60 Q 30 50 60 30 Q 120 5 180 25 Q 210 40 210 60" 
                               stroke="#dc3545" stroke-width="2" fill="none"/>
                         <circle cx="120" cy="5" r="4" fill="#17a2b8"/>
                         <text x="120" y="-5" text-anchor="middle" font-size="10" fill="#17a2b8">γ̂</text>
                         <text x="105" y="85" text-anchor="middle" font-size="12" fill="#721c24">L(γ)</text>
                     </g>
                     
                     <!-- Transformation arrow -->
                     <defs>
                         <marker id="transform-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                             <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                         </marker>
                     </defs>
                     
                     <line x1="300" y1="140" x2="400" y2="140" stroke="#667eea" stroke-width="3" marker-end="url(#transform-arrow)"/>
                     <text x="350" y="135" text-anchor="middle" font-size="12" font-weight="bold" fill="#667eea">
                         γ = g(θ)
                     </text>
                     
                     <!-- MLE transformation -->
                     <line x1="225" y1="200" x2="525" y2="200" stroke="#28a745" stroke-width="3" marker-end="url(#transform-arrow)"/>
                     <text x="375" y="195" text-anchor="middle" font-size="12" font-weight="bold" fill="#28a745">
                         γ̂ = g(θ̂)
                     </text>
                     
                     <!-- Key insight -->
                     <rect x="100" y="250" width="500" height="80" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                     <text x="350" y="275" text-anchor="middle" font-size="14" font-weight="bold" fill="#155724">
                         Key Insight: Invariance Property
                     </text>
                     <text x="350" y="295" text-anchor="middle" font-size="12" fill="#155724">
                         The maximum of the likelihood function is preserved under transformation
                     </text>
                     <text x="350" y="315" text-anchor="middle" font-size="12" fill="#155724">
                         MLE of g(θ) = g(MLE of θ)
                     </text>
                     
                     <!-- Benefits boxes -->
                     <rect x="50" y="350" width="150" height="30" fill="#ffeaa7" stroke="#e17055" stroke-width="1" rx="5"/>
                     <text x="125" y="370" text-anchor="middle" font-size="11" fill="#856404">
                         Computational Flexibility
                     </text>
                     
                     <rect x="275" y="350" width="150" height="30" fill="#a8edea" stroke="#00b894" stroke-width="1" rx="5"/>
                     <text x="350" y="370" text-anchor="middle" font-size="11" fill="#00695c">
                         Interpretive Freedom
                     </text>
                     
                     <rect x="500" y="350" width="150" height="30" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1" rx="5"/>
                     <text x="575" y="370" text-anchor="middle" font-size="11" fill="#0c5460">
                         No Additional Computation
                     </text>
                 </svg>
             </div>
             
             <div class="highlight-box">
                 <h4>Practical Implications</h4>
                 <ul>
                     <li><strong>Optimization:</strong> Choose parameterization that makes numerical optimization easier</li>
                     <li><strong>Constraints:</strong> Transform constrained problems to unconstrained ones</li>
                     <li><strong>Interpretation:</strong> Present results in the most meaningful scale</li>
                     <li><strong>Efficiency:</strong> No need to re-optimize for different parameterizations</li>
                 </ul>
             </div>
             
             <h3>Common Transformations in Practice</h3>
             
             <div class="formula-box">
                 <table style="width: 100%; border-collapse: collapse;">
                     <thead>
                         <tr style="background: #667eea; color: white;">
                             <th style="border: 1px solid #ddd; padding: 8px;">Parameter</th>
                             <th style="border: 1px solid #ddd; padding: 8px;">Constraint</th>
                             <th style="border: 1px solid #ddd; padding: 8px;">Transformation</th>
                             <th style="border: 1px solid #ddd; padding: 8px;">Purpose</th>
                         </tr>
                     </thead>
                     <tbody>
                         <tr>
                             <td style="border: 1px solid #ddd; padding: 8px;">Variance σ²</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">σ² > 0</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">φ = log(σ²)</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">Unconstrained</td>
                         </tr>
                         <tr style="background: #f8f9fa;">
                             <td style="border: 1px solid #ddd; padding: 8px;">Probability p</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">0 < p < 1</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">γ = logit(p)</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">Logistic regression</td>
                         </tr>
                         <tr>
                             <td style="border: 1px solid #ddd; padding: 8px;">Rate λ</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">λ > 0</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">η = log(λ)</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">Poisson regression</td>
                         </tr>
                         <tr style="background: #f8f9fa;">
                             <td style="border: 1px solid #ddd; padding: 8px;">Correlation ρ</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">-1 < ρ < 1</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">z = tanh⁻¹(ρ)</td>
                             <td style="border: 1px solid #ddd; padding: 8px;">Fisher's z-transform</td>
                         </tr>
                     </tbody>
                 </table>
                           </div>
          </section>
          
          <!-- Expected Log-Likelihood Section -->
          <section id="expected-likelihood" class="section">
              <h2>Properties of Expected Log-Likelihood</h2>
              
              <p>The theoretical foundation of maximum likelihood estimation rests on understanding the properties of the <strong>expected log-likelihood</strong>. These properties enable us to prove consistency, derive asymptotic distributions, and establish the optimality of MLEs.</p>
              
              <h3>Setup and Notation</h3>
              
              <div class="formula-box">
                  <h4>Basic Framework</h4>
                  <p>Let $y_1, y_2, \ldots, y_n$ be independent observations from a p.d.f. $f(y, \theta)$, where $\theta$ is an unknown parameter with true value $\theta_0$.</p>
                  
                  <p>The <strong>log-likelihood</strong> is:</p>
                  $$\ell(\theta) = \sum_{i=1}^n \log f(y_i, \theta) = \sum_{i=1}^n \ell_i(\theta)$$
                  
                  <p>where $\ell_i(\theta) = \log f(y_i, \theta)$ is the contribution from observation $i$.</p>
              </div>
              
              <div class="highlight-box">
                  <h4>Key Insight</h4>
                  <p>By treating the log-likelihood as a function of random variables $Y_1, Y_2, \ldots, Y_n$, we can analyze its <strong>expected behavior</strong> under the true parameter $\theta_0$. This leads to four fundamental results that form the backbone of MLE theory.</p>
              </div>
              
              <h3>Result 1: Expected Score is Zero</h3>
              
              <div class="theorem-box">
                  <h4>Result 1</h4>
                  <p>The expected value of the score function (first derivative) at the true parameter is zero:</p>
                  $$E_0\left[\frac{\partial \ell}{\partial \theta}\bigg|_{\theta_0}\right] = 0$$
                  
                  <p>where $E_0[\cdot]$ denotes expectation with respect to $f(y, \theta_0)$.</p>
              </div>
              
              <div class="proof-box">
                  <h4>Proof Outline</h4>
                  <p>For a single observation:</p>
                  
                  <p><strong>Step 1:</strong> Write the expectation</p>
                  $$E_0\left[\frac{\partial \ell_i}{\partial \theta}\right] = E_0\left[\frac{\partial}{\partial \theta} \log f(Y, \theta)\right]$$
                  
                  <p><strong>Step 2:</strong> Use the identity $\frac{\partial}{\partial \theta} \log f = \frac{1}{f} \frac{\partial f}{\partial \theta}$</p>
                  $$E_0\left[\frac{1}{f(Y, \theta_0)} \frac{\partial f(Y, \theta)}{\partial \theta}\bigg|_{\theta_0}\right]$$
                  
                  <p><strong>Step 3:</strong> Convert to integral and exchange order of differentiation</p>
                  $$\int \frac{1}{f(y, \theta_0)} \frac{\partial f(y, \theta)}{\partial \theta}\bigg|_{\theta_0} f(y, \theta_0) dy = \frac{\partial}{\partial \theta} \int f(y, \theta) dy\bigg|_{\theta_0}$$
                  
                  <p><strong>Step 4:</strong> Since $\int f(y, \theta) dy = 1$, we have $\frac{\partial}{\partial \theta} \int f(y, \theta) dy = 0$ ✓</p>
              </div>
              
              <h3>Result 2: Variance of Score</h3>
              
              <div class="theorem-box">
                  <h4>Result 2</h4>
                  <p>The variance of the score function equals the expected value of the squared score:</p>
                  $$\text{Var}_0\left[\frac{\partial \ell}{\partial \theta}\bigg|_{\theta_0}\right] = E_0\left[\left(\frac{\partial \ell}{\partial \theta}\bigg|_{\theta_0}\right)^2\right]$$
                  
                  <p>This follows directly from Result 1, since $E_0[\partial \ell/\partial \theta] = 0$.</p>
              </div>
              
              <h3>Result 3: Fisher Information</h3>
              
              <div class="theorem-box">
                  <h4>Result 3 (Fisher Information)</h4>
                  <p>The Fisher Information can be expressed in two equivalent ways:</p>
                  $$I = E_0\left[\left(\frac{\partial \ell}{\partial \theta}\bigg|_{\theta_0}\right)^2\right] = -E_0\left[\frac{\partial^2 \ell}{\partial \theta^2}\bigg|_{\theta_0}\right]$$
                  
                  <p>The Fisher Information $I$ measures the amount of information about $\theta$ contained in the data.</p>
              </div>
              
              <div class="proof-box">
                  <h4>Proof of Fisher Information Identity</h4>
                  <p>Starting from Result 1 for a single observation:</p>
                  $$\frac{\partial}{\partial \theta} \int \frac{\partial \log f}{\partial \theta} f(y, \theta) dy = 0$$
                  
                  <p>Differentiating again:</p>
                  $$\int \left[\frac{\partial^2 \log f}{\partial \theta^2} f + \frac{\partial \log f}{\partial \theta} \frac{\partial f}{\partial \theta}\right] dy = 0$$
                  
                  <p>Since $\frac{\partial \log f}{\partial \theta} = \frac{1}{f} \frac{\partial f}{\partial \theta}$:</p>
                  $$\int \frac{\partial^2 \log f}{\partial \theta^2} f \, dy + \int \left(\frac{\partial \log f}{\partial \theta}\right)^2 f \, dy = 0$$
                  
                  <p>Therefore: $E_0\left[\frac{\partial^2 \ell_i}{\partial \theta^2}\right] = -E_0\left[\left(\frac{\partial \ell_i}{\partial \theta}\right)^2\right]$ ✓</p>
              </div>
              
              <h3>Result 4: Global Maximum Property</h3>
              
              <div class="theorem-box">
                  <h4>Result 4 (Jensen's Inequality)</h4>
                  <p>The expected log-likelihood achieves its global maximum at the true parameter:</p>
                  $$E_0[\ell(\theta_0)] \geq E_0[\ell(\theta)] \quad \text{for all } \theta$$
                  
                  <p>This establishes that the expected log-likelihood has a unique global maximum at $\theta_0$.</p>
              </div>
              
              <div class="proof-box">
                  <h4>Proof Using Jensen's Inequality</h4>
                  <p>Jensen's inequality states that for a concave function $c$ and random variable $Y$:</p>
                  $$E[c(Y)] \leq c(E[Y])$$
                  
                  <p><strong>Step 1:</strong> Apply Jensen's inequality to the concave function $\log$ and the random variable $f(Y, \theta)/f(Y, \theta_0)$:</p>
                  $$E_0\left[\log \frac{f(Y, \theta)}{f(Y, \theta_0)}\right] \leq \log E_0\left[\frac{f(Y, \theta)}{f(Y, \theta_0)}\right]$$
                  
                  <p><strong>Step 2:</strong> Evaluate the right-hand side:</p>
                  $$E_0\left[\frac{f(Y, \theta)}{f(Y, \theta_0)}\right] = \int \frac{f(y, \theta)}{f(y, \theta_0)} f(y, \theta_0) dy = \int f(y, \theta) dy = 1$$
                  
                  <p><strong>Step 3:</strong> Since $\log(1) = 0$, we get:</p>
                  $$E_0[\log f(Y, \theta)] - E_0[\log f(Y, \theta_0)] \leq 0$$
                  
                  <p>Therefore: $E_0[\ell(\theta_0)] \geq E_0[\ell(\theta)]$ ✓</p>
              </div>
              
              <h3>Visualization of Jensen's Inequality</h3>
              
              <div style="display: flex; justify-content: center; margin: 2rem 0;">
                  <svg width="600" height="400" viewBox="0 0 600 400">
                      <!-- Background -->
                      <rect width="600" height="400" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                      
                      <!-- Title -->
                      <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                          Jensen's Inequality Illustration
                      </text>
                      
                      <!-- Axes -->
                      <line x1="80" y1="320" x2="520" y2="320" stroke="#333" stroke-width="2"/>
                      <line x1="80" y1="320" x2="80" y2="80" stroke="#333" stroke-width="2"/>
                      
                      <!-- Concave function (log) -->
                      <path d="M 100 300 Q 200 200 300 150 Q 400 120 500 110" 
                            stroke="#667eea" stroke-width="3" fill="none"/>
                      
                      <!-- Random variable points -->
                      <circle cx="180" cy="240" r="4" fill="#dc3545"/>
                      <circle cx="220" cy="200" r="4" fill="#dc3545"/>
                      <circle cx="280" cy="160" r="4" fill="#dc3545"/>
                      <circle cx="320" cy="140" r="4" fill="#dc3545"/>
                      <circle cx="380" cy="125" r="4" fill="#dc3545"/>
                      
                      <!-- Expected value point -->
                      <circle cx="280" cy="160" r="6" fill="#28a745" stroke="white" stroke-width="2"/>
                      
                      <!-- Vertical lines from points to x-axis -->
                      <line x1="180" y1="240" x2="180" y2="320" stroke="#dc3545" stroke-width="1" stroke-dasharray="2,2"/>
                      <line x1="220" y1="200" x2="220" y2="320" stroke="#dc3545" stroke-width="1" stroke-dasharray="2,2"/>
                      <line x1="280" y1="160" x2="280" y2="320" stroke="#28a745" stroke-width="2" stroke-dasharray="2,2"/>
                      <line x1="320" y1="140" x2="320" y2="320" stroke="#dc3545" stroke-width="1" stroke-dasharray="2,2"/>
                      <line x1="380" y1="125" x2="380" y2="320" stroke="#dc3545" stroke-width="1" stroke-dasharray="2,2"/>
                      
                      <!-- Horizontal line showing E[c(Y)] -->
                      <line x1="80" y1="180" x2="520" y2="180" stroke="#e74c3c" stroke-width="2" stroke-dasharray="5,5"/>
                      
                      <!-- Point showing c(E[Y]) -->
                      <circle cx="280" cy="160" r="8" fill="none" stroke="#28a745" stroke-width="3"/>
                      
                      <!-- Labels -->
                      <text x="300" y="340" text-anchor="middle" font-size="12" fill="#333">Random Variable Y</text>
                      <text x="60" y="200" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 60 200)">c(Y)</text>
                      
                      <text x="280" y="350" text-anchor="middle" font-size="11" fill="#28a745">E[Y]</text>
                      <text x="530" y="185" text-anchor="start" font-size="11" fill="#e74c3c">E[c(Y)]</text>
                      <text x="300" y="145" text-anchor="middle" font-size="11" fill="#28a745">c(E[Y])</text>
                      
                      <!-- Inequality annotation -->
                      <text x="300" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#667eea">
                          E[c(Y)] ≤ c(E[Y])
                      </text>
                      
                      <!-- Curve label -->
                      <text x="450" y="100" text-anchor="middle" font-size="12" fill="#667eea">
                          Concave Function c
                      </text>
                      
                      <!-- Key insight box -->
                      <rect x="100" y="370" width="400" height="25" fill="#e8f5e8" stroke="#28a745" stroke-width="1" rx="5"/>
                      <text x="300" y="387" text-anchor="middle" font-size="11" fill="#155724">
                          For log function: E[log(f(Y,θ)/f(Y,θ₀))] ≤ log(E[f(Y,θ)/f(Y,θ₀)]) = log(1) = 0
                      </text>
                  </svg>
              </div>
              
              <h3>Fisher Information: Intuition and Interpretation</h3>
              
              <div class="highlight-box">
                  <h4>What is Fisher Information?</h4>
                  <p>Fisher Information $I$ quantifies how much information the data contains about the parameter $\theta$:</p>
                  <ul>
                      <li><strong>High $I$:</strong> Data tightly constrains $\theta$ → Sharp likelihood peak → Precise estimates</li>
                      <li><strong>Low $I$:</strong> Data provides little information about $\theta$ → Flat likelihood → Imprecise estimates</li>
                  </ul>
              </div>
              
              <div style="display: flex; justify-content: center; margin: 2rem 0;">
                  <svg width="700" height="300" viewBox="0 0 700 300">
                      <!-- Background -->
                      <rect width="700" height="300" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                      
                      <!-- Title -->
                      <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                          Fisher Information: High vs Low Information
                      </text>
                      
                      <!-- High Information Case -->
                      <g transform="translate(50, 50)">
                          <rect x="0" y="0" width="250" height="200" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                          <text x="125" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#155724">
                              High Fisher Information
                          </text>
                          
                          <!-- Sharp likelihood curve -->
                          <path d="M 50 150 Q 100 120 125 80 Q 150 120 200 150" 
                                stroke="#28a745" stroke-width="3" fill="none"/>
                          
                          <!-- True parameter -->
                          <line x1="125" y1="80" x2="125" y2="170" stroke="#dc3545" stroke-width="2" stroke-dasharray="3,3"/>
                          <text x="125" y="185" text-anchor="middle" font-size="10" fill="#dc3545">θ₀</text>
                          
                          <!-- Annotations -->
                          <text x="125" y="200" text-anchor="middle" font-size="11" fill="#155724">
                              Sharp peak → Precise estimate
                          </text>
                          <text x="125" y="45" text-anchor="middle" font-size="12" fill="#155724">
                              I = Large
                          </text>
                      </g>
                      
                      <!-- Low Information Case -->
                      <g transform="translate(400, 50)">
                          <rect x="0" y="0" width="250" height="200" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="10"/>
                          <text x="125" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#721c24">
                              Low Fisher Information
                          </text>
                          
                          <!-- Flat likelihood curve -->
                          <path d="M 50 130 Q 100 120 125 115 Q 150 120 200 130" 
                                stroke="#dc3545" stroke-width="3" fill="none"/>
                          
                          <!-- True parameter -->
                          <line x1="125" y1="115" x2="125" y2="170" stroke="#28a745" stroke-width="2" stroke-dasharray="3,3"/>
                          <text x="125" y="185" text-anchor="middle" font-size="10" fill="#28a745">θ₀</text>
                          
                          <!-- Annotations -->
                          <text x="125" y="200" text-anchor="middle" font-size="11" fill="#721c24">
                              Flat curve → Imprecise estimate
                          </text>
                          <text x="125" y="45" text-anchor="middle" font-size="12" fill="#721c24">
                              I = Small
                          </text>
                      </g>
                      
                      <!-- Comparison arrow -->
                      <line x1="300" y1="150" x2="400" y2="150" stroke="#667eea" stroke-width="3"/>
                      <text x="350" y="145" text-anchor="middle" font-size="12" fill="#667eea">vs</text>
                      
                      <!-- Bottom summary -->
                      <rect x="100" y="270" width="500" height="25" fill="#e7f3ff" stroke="#007bff" stroke-width="1" rx="5"/>
                      <text x="350" y="287" text-anchor="middle" font-size="12" fill="#004085">
                          Fisher Information = Curvature of log-likelihood = Precision of estimation
                      </text>
                  </svg>
              </div>
              
              <h3>Vector Parameter Generalization</h3>
              
              <div class="theorem-box">
                  <h4>Multivariate Results</h4>
                  <p>For vector parameters $\boldsymbol{\theta} = (\theta_1, \ldots, \theta_p)^T$:</p>
                  
                  <p><strong>Score Vector:</strong> $\mathbf{u} = \frac{\partial \ell}{\partial \boldsymbol{\theta}}$</p>
                  
                  <p><strong>Hessian Matrix:</strong> $\mathbf{H} = \frac{\partial^2 \ell}{\partial \boldsymbol{\theta} \partial \boldsymbol{\theta}^T}$</p>
                  
                  <p><strong>Result 1 (Vector):</strong> $E_0[\mathbf{u}] = \mathbf{0}$</p>
                  
                  <p><strong>Result 3 (Vector):</strong> $\mathbf{I} = E_0[\mathbf{u}\mathbf{u}^T] = -E_0[\mathbf{H}]$</p>
                  
                  <p>where $\mathbf{I}$ is the Fisher Information Matrix.</p>
              </div>
              
              <div class="highlight-box">
                  <h4>Summary: Why These Results Matter</h4>
                  <ul>
                      <li><strong>Result 1:</strong> Provides the foundation for unbiasedness properties</li>
                      <li><strong>Result 2:</strong> Connects variance to Fisher Information</li>
                      <li><strong>Result 3:</strong> Defines Fisher Information and enables asymptotic theory</li>
                      <li><strong>Result 4:</strong> Guarantees that the expected log-likelihood is maximized at the true parameter</li>
                  </ul>
                  <p><strong>Together:</strong> These results enable us to prove consistency and derive the asymptotic distribution of MLEs.</p>
                             </div>
           </section>
           
           <!-- Consistency Section -->
           <section id="consistency" class="section">
               <h2>Consistency of Maximum Likelihood Estimators</h2>
               
               <p>Consistency is one of the most important properties of maximum likelihood estimators. It guarantees that as we collect more data, our estimates get arbitrarily close to the true parameter values.</p>
               
               <div class="theorem-box">
                   <h4>Definition: Consistency</h4>
                   <p>An estimator $\hat{\theta}_n$ based on $n$ observations is <strong>consistent</strong> if:</p>
                   $$\hat{\theta}_n \xrightarrow{p} \theta_0 \text{ as } n \to \infty$$
                   
                   <p>Formally: $\Pr(|\hat{\theta}_n - \theta_0| < \epsilon) \to 1$ as $n \to \infty$ for any $\epsilon > 0$</p>
                   
                   <p><strong>Interpretation:</strong> The probability that the estimator is within any small distance of the true value approaches 1 as sample size increases.</p>
               </div>
               
               <h3>Why MLEs are Consistent</h3>
               
               <div class="highlight-box">
                   <h4>Intuitive Explanation</h4>
                   <p>The key insight is that the <strong>sample log-likelihood converges to the expected log-likelihood</strong> by the Law of Large Numbers, and we know from Result 4 that the expected log-likelihood is maximized at the true parameter $\theta_0$.</p>
               </div>
               
               <div class="proof-box">
                   <h4>Proof Outline</h4>
                   <p>Consider the scaled log-likelihood:</p>
                   $$\ell_n(\theta) = \frac{1}{n} \sum_{i=1}^n \log f(y_i, \theta)$$
                   
                   <p><strong>Step 1:</strong> By the Law of Large Numbers:</p>
                   $$\ell_n(\theta) \xrightarrow{p} E_0[\log f(Y, \theta)] \text{ as } n \to \infty$$
                   
                   <p><strong>Step 2:</strong> From Result 4, we know:</p>
                   $$E_0[\log f(Y, \theta_0)] > E_0[\log f(Y, \theta)] \text{ for } \theta \neq \theta_0$$
                   
                   <p><strong>Step 3:</strong> Therefore, in the limit:</p>
                   $$\ell_n(\theta_0) > \ell_n(\theta) \text{ for } \theta \neq \theta_0$$
                   
                   <p><strong>Step 4:</strong> This means $\hat{\theta}_n \to \theta_0$ as $n \to \infty$</p>
               </div>
               
               <h3>Visualization of Consistency</h3>
               
               <div style="display: flex; justify-content: center; margin: 2rem 0;">
                   <svg width="800" height="500" viewBox="0 0 800 500">
                       <!-- Background -->
                       <rect width="800" height="500" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                       
                       <!-- Title -->
                       <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                           Consistency of MLEs: Convergence Illustration
                       </text>
                       
                       <!-- Expected log-likelihood (solid curve) -->
                       <g transform="translate(100, 80)">
                           <text x="300" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#28a745">
                               Expected Log-Likelihood E₀[ℓ(θ)]
                           </text>
                           
                           <!-- Solid curve representing expected log-likelihood -->
                           <path d="M 50 100 Q 200 60 300 50 Q 400 60 550 100" 
                                 stroke="#28a745" stroke-width="4" fill="none"/>
                           
                           <!-- True parameter marker -->
                           <line x1="300" y1="50" x2="300" y2="120" stroke="#dc3545" stroke-width="3" stroke-dasharray="5,5"/>
                           <text x="300" y="140" text-anchor="middle" font-size="12" fill="#dc3545">θ₀</text>
                           
                           <!-- Maximum point -->
                           <circle cx="300" cy="50" r="6" fill="#dc3545" stroke="white" stroke-width="2"/>
                           <text x="320" y="45" text-anchor="start" font-size="11" fill="#dc3545">Global Maximum</text>
                       </g>
                       
                       <!-- Sample log-likelihoods for different n -->
                       <g transform="translate(100, 200)">
                           <text x="300" y="0" text-anchor="middle" font-size="14" font-weight="bold" fill="#667eea">
                               Sample Log-Likelihood ℓₙ(θ) for Different Sample Sizes
                           </text>
                           
                           <!-- Small n (very noisy) -->
                           <path d="M 50 120 Q 120 80 180 110 Q 250 70 300 65 Q 350 80 420 90 Q 480 110 550 120" 
                                 stroke="#e74c3c" stroke-width="2" fill="none" stroke-dasharray="2,2"/>
                           <text x="570" y="125" text-anchor="start" font-size="10" fill="#e74c3c">n = 10</text>
                           
                           <!-- Medium n (less noisy) -->
                           <path d="M 50 105 Q 150 75 220 68 Q 280 58 300 55 Q 320 58 380 68 Q 450 75 550 105" 
                                 stroke="#f39c12" stroke-width="2" fill="none" stroke-dasharray="4,4"/>
                           <text x="570" y="110" text-anchor="start" font-size="10" fill="#f39c12">n = 100</text>
                           
                           <!-- Large n (close to expected) -->
                           <path d="M 50 100 Q 200 60 300 52 Q 400 60 550 100" 
                                 stroke="#3498db" stroke-width="3" fill="none" stroke-dasharray="6,2"/>
                           <text x="570" y="95" text-anchor="start" font-size="10" fill="#3498db">n = 1000</text>
                           
                           <!-- Convergence arrows -->
                           <defs>
                               <marker id="convergence-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                   <polygon points="0 0, 8 3, 0 6" fill="#667eea"/>
                               </marker>
                           </defs>
                           
                           <!-- Arrows showing convergence -->
                           <line x1="320" y1="65" x2="320" y2="80" stroke="#667eea" stroke-width="2" marker-end="url(#convergence-arrow)"/>
                           <line x1="320" y1="58" x2="320" y2="68" stroke="#667eea" stroke-width="2" marker-end="url(#convergence-arrow)"/>
                           <line x1="320" y1="52" x2="320" y2="58" stroke="#667eea" stroke-width="2" marker-end="url(#convergence-arrow)"/>
                           
                           <text x="340" y="75" text-anchor="start" font-size="11" fill="#667eea">Convergence</text>
                           <text x="340" y="88" text-anchor="start" font-size="11" fill="#667eea">as n → ∞</text>
                       </g>
                       
                       <!-- Parameter space -->
                       <line x1="150" y1="420" x2="650" y2="420" stroke="#333" stroke-width="2"/>
                       <text x="400" y="440" text-anchor="middle" font-size="12" fill="#333">Parameter θ</text>
                       
                       <!-- Tick marks -->
                       <line x1="200" y1="415" x2="200" y2="425" stroke="#333" stroke-width="1"/>
                       <line x1="300" y1="415" x2="300" y2="425" stroke="#333" stroke-width="1"/>
                       <line x1="400" y1="415" x2="400" y2="425" stroke="#333" stroke-width="1"/>
                       <line x1="500" y1="415" x2="500" y2="425" stroke="#333" stroke-width="1"/>
                       <line x1="600" y1="415" x2="600" y2="425" stroke="#333" stroke-width="1"/>
                       
                       <!-- Key insight box -->
                       <rect x="50" y="460" width="700" height="30" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="5"/>
                       <text x="400" y="480" text-anchor="middle" font-size="12" font-weight="bold" fill="#155724">
                           Key: As n increases, sample log-likelihood converges to expected log-likelihood (LOLN)
                       </text>
                   </svg>
               </div>
               
               <h3>Consistency vs. Unbiasedness</h3>
               
               <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                   <div class="theorem-box">
                       <h4>Unbiasedness</h4>
                       <p>An estimator $\hat{\theta}_n$ is <strong>unbiased</strong> if:</p>
                       $$E[\hat{\theta}_n] = \theta_0 \text{ for all } n$$
                       
                       <p><strong>Property:</strong> Expected value equals true value</p>
                       <p><strong>Requirement:</strong> Exact for finite samples</p>
                   </div>
                   
                   <div class="theorem-box">
                       <h4>Consistency</h4>
                       <p>An estimator $\hat{\theta}_n$ is <strong>consistent</strong> if:</p>
                       $$\hat{\theta}_n \xrightarrow{p} \theta_0 \text{ as } n \to \infty$$
                       
                       <p><strong>Property:</strong> Converges to true value</p>
                       <p><strong>Requirement:</strong> Asymptotic property</p>
                   </div>
               </div>
               
               <div class="highlight-box">
                   <h4>Important Distinctions</h4>
                   <ul>
                       <li><strong>MLEs are often biased</strong> for finite samples but are asymptotically unbiased</li>
                       <li><strong>Consistency is weaker than unbiasedness</strong> but more generally achievable</li>
                       <li><strong>Consistency implies asymptotic unbiasedness</strong> but not vice versa</li>
                       <li><strong>For MLEs:</strong> Consistency + decreasing variance → asymptotic optimality</li>
                   </ul>
               </div>
               
               <h3>Practical Implications</h3>
               
               <div class="formula-box">
                   <h4>What Consistency Guarantees</h4>
                   <ol style="text-align: left; display: inline-block;">
                       <li><strong>Convergence:</strong> $\hat{\theta}_n \to \theta_0$ as $n \to \infty$</li>
                       <li><strong>Asymptotic Unbiasedness:</strong> $E[\hat{\theta}_n] \to \theta_0$ as $n \to \infty$</li>
                       <li><strong>Vanishing Variance:</strong> $\text{Var}(\hat{\theta}_n) \to 0$ as $n \to \infty$</li>
                       <li><strong>Reliability:</strong> Large samples give estimates close to truth</li>
                   </ol>
               </div>
               
               <h3>Regularity Conditions</h3>
               
               <div class="theorem-box">
                   <h4>When is Consistency Guaranteed?</h4>
                   <p>MLEs are consistent under mild regularity conditions:</p>
                   <ul style="text-align: left; display: inline-block;">
                       <li><strong>Identifiability:</strong> Different parameter values give different distributions</li>
                       <li><strong>Compactness:</strong> Parameter space is compact (or appropriate growth conditions)</li>
                       <li><strong>Continuity:</strong> Likelihood function is continuous in parameters</li>
                       <li><strong>Uniform Convergence:</strong> Sample likelihood converges uniformly to expected likelihood</li>
                   </ul>
                   
                   <p><strong>Good News:</strong> These conditions hold for most practical applications!</p>
               </div>
               
               <h3>Convergence Rate</h3>
               
               <div class="highlight-box">
                   <h4>How Fast Does Convergence Happen?</h4>
                   <p>While consistency guarantees convergence, it doesn't specify the rate. However, under additional conditions, MLEs typically converge at rate $\sqrt{n}$:</p>
                   
                   <div class="formula-box">
                       $$\sqrt{n}(\hat{\theta}_n - \theta_0) = O_p(1)$$
                       <p>This means the estimation error decreases proportionally to $1/\sqrt{n}$</p>
                   </div>
                   
                   <p><strong>Practical Implication:</strong> To halve the estimation error, you need 4 times as much data.</p>
               </div>
               
               <div class="theorem-box">
                   <h4>Extension to Vector Parameters</h4>
                   <p>For vector parameters $\boldsymbol{\theta} = (\theta_1, \ldots, \theta_p)^T$, consistency means:</p>
                   $$\hat{\boldsymbol{\theta}}_n \xrightarrow{p} \boldsymbol{\theta}_0 \text{ as } n \to \infty$$
                   
                   <p>This is equivalent to component-wise convergence:</p>
                   $$\hat{\theta}_{j,n} \xrightarrow{p} \theta_{j,0} \text{ for all } j = 1, \ldots, p$$
               </div>
           </section>
           
           <!-- Asymptotic Distribution Section -->
           <section id="asymptotic" class="section">
               <h2>Large Sample Distribution of MLEs</h2>
               
               <p>While consistency tells us that MLEs converge to the true parameter values, the <strong>asymptotic distribution</strong> tells us about the behavior of the estimation error and enables us to construct confidence intervals and perform hypothesis tests.</p>
               
               <div class="theorem-box">
                   <h4>Asymptotic Normality of MLEs</h4>
                   <p>Under regularity conditions, the MLE $\hat{\theta}_n$ is asymptotically normally distributed:</p>
                   $$\sqrt{n}(\hat{\theta}_n - \theta_0) \xrightarrow{d} N(0, I^{-1}(\theta_0))$$
                   
                   <p>where $I(\theta_0)$ is the Fisher Information at the true parameter value.</p>
                   
                   <p><strong>Equivalently:</strong> $\hat{\theta}_n \xrightarrow{d} N(\theta_0, I^{-1}(\theta_0)/n)$</p>
               </div>
               
               <h3>Derivation Using Taylor Expansion</h3>
               
               <div class="proof-box">
                   <h4>Proof Outline</h4>
                   <p>The key idea is to use a Taylor expansion of the score function around the true parameter.</p>
                   
                   <p><strong>Step 1:</strong> MLE condition</p>
                   <p>By definition: $\frac{\partial \ell}{\partial \theta}\bigg|_{\hat{\theta}_n} = 0$</p>
                   
                   <p><strong>Step 2:</strong> Taylor expansion</p>
                   $$\frac{\partial \ell}{\partial \theta}\bigg|_{\hat{\theta}_n} \approx \frac{\partial \ell}{\partial \theta}\bigg|_{\theta_0} + (\hat{\theta}_n - \theta_0) \frac{\partial^2 \ell}{\partial \theta^2}\bigg|_{\theta_0}$$
                   
                   <p><strong>Step 3:</strong> Solve for $\hat{\theta}_n - \theta_0$</p>
                   $$\hat{\theta}_n - \theta_0 \approx -\frac{\partial \ell/\partial \theta|_{\theta_0}}{\partial^2 \ell/\partial \theta^2|_{\theta_0}}$$
                   
                   <p><strong>Step 4:</strong> Apply CLT and LLN</p>
                   <ul style="text-align: left; display: inline-block;">
                       <li>Numerator: $\frac{\partial \ell}{\partial \theta}\bigg|_{\theta_0} \xrightarrow{d} N(0, nI(\theta_0))$ (CLT)</li>
                       <li>Denominator: $-\frac{\partial^2 \ell}{\partial \theta^2}\bigg|_{\theta_0} \xrightarrow{p} nI(\theta_0)$ (LLN)</li>
                   </ul>
                   
                   <p><strong>Step 5:</strong> Combine results</p>
                   $$\sqrt{n}(\hat{\theta}_n - \theta_0) \xrightarrow{d} N(0, I^{-1}(\theta_0))$$
               </div>
               
               <h3>Fisher Information and Asymptotic Variance</h3>
               
               <div class="highlight-box">
                   <h4>Key Insight: Cramér-Rao Connection</h4>
                   <p>The asymptotic variance of the MLE achieves the <strong>Cramér-Rao lower bound</strong>:</p>
                   
                   <div class="formula-box">
                       $$\text{Asy Var}(\hat{\theta}_n) = \frac{I^{-1}(\theta_0)}{n}$$
                       <p>This is the smallest possible asymptotic variance for any unbiased estimator!</p>
                   </div>
                   
                   <p><strong>Interpretation:</strong> MLEs are asymptotically efficient (optimal in terms of variance).</p>
               </div>
               
               <h3>Practical Inference</h3>
               
               <div class="theorem-box">
                   <h4>Confidence Intervals</h4>
                   <p>Using the asymptotic normality, we can construct approximate confidence intervals:</p>
                   
                   <div class="formula-box">
                       <p><strong>95% Confidence Interval:</strong></p>
                       $$\hat{\theta}_n \pm 1.96 \sqrt{\frac{\hat{I}^{-1}(\hat{\theta}_n)}{n}}$$
                       
                       <p>where $\hat{I}(\hat{\theta}_n)$ is the observed Fisher Information.</p>
                   </div>
               </div>
               
               <div class="theorem-box">
                   <h4>Hypothesis Testing</h4>
                   <p>For testing $H_0: \theta = \theta_0$ vs $H_1: \theta \neq \theta_0$:</p>
                   
                   <div class="formula-box">
                       <p><strong>Wald Test Statistic:</strong></p>
                       $$W = \frac{(\hat{\theta}_n - \theta_0)^2}{\hat{I}^{-1}(\hat{\theta}_n)/n} \xrightarrow{d} \chi^2_1$$
                       
                       <p>under $H_0$.</p>
                   </div>
               </div>
               
               <h3>Vector Parameter Case</h3>
               
               <div class="theorem-box">
                   <h4>Multivariate Asymptotic Normality</h4>
                   <p>For vector parameters $\boldsymbol{\theta} = (\theta_1, \ldots, \theta_p)^T$:</p>
                   
                   $$\sqrt{n}(\hat{\boldsymbol{\theta}}_n - \boldsymbol{\theta}_0) \xrightarrow{d} N(\mathbf{0}, \mathbf{I}^{-1}(\boldsymbol{\theta}_0))$$
                   
                   <p>where $\mathbf{I}(\boldsymbol{\theta}_0)$ is the Fisher Information Matrix.</p>
                   
                   <p><strong>Confidence Regions:</strong> Elliptical regions based on quadratic forms</p>
                   <p><strong>Hypothesis Testing:</strong> Wald tests with $\chi^2$ distributions</p>
               </div>
           </section>
           
           <!-- Likelihood Ratio Test Section -->
           <section id="likelihood-ratio" class="section">
               <h2>Generalized Likelihood Ratio Test</h2>
               
               <p>The Generalized Likelihood Ratio Test (GLRT) is a fundamental method for hypothesis testing in the maximum likelihood framework. It provides a principled way to compare nested models and test parameter restrictions.</p>
               
               <div class="theorem-box">
                   <h4>GLRT Framework</h4>
                   <p>Consider testing:</p>
                   $$H_0: R(\boldsymbol{\theta}) = \mathbf{0} \quad \text{vs} \quad H_1: R(\boldsymbol{\theta}) \neq \mathbf{0}$$
                   
                   <p>where $R(\boldsymbol{\theta})$ is a vector-valued function imposing $r$ restrictions.</p>
                   
                   <p><strong>Test Statistic:</strong></p>
                   $$\lambda = 2\{\ell(\hat{\boldsymbol{\theta}}_{H_1}) - \ell(\hat{\boldsymbol{\theta}}_{H_0})\}$$
                   
                   <p>where:</p>
                   <ul style="text-align: left; display: inline-block;">
                       <li>$\hat{\boldsymbol{\theta}}_{H_1}$ = unrestricted MLE</li>
                       <li>$\hat{\boldsymbol{\theta}}_{H_0}$ = restricted MLE (satisfying $R(\boldsymbol{\theta}) = \mathbf{0}$)</li>
                   </ul>
               </div>
               
               <div class="theorem-box">
                   <h4>Asymptotic Distribution</h4>
                   <p>Under $H_0$ and regularity conditions:</p>
                   $$\lambda \xrightarrow{d} \chi^2_r \text{ as } n \to \infty$$
                   
                   <p>where $r$ is the number of restrictions imposed by $H_0$.</p>
               </div>
               
               <h3>Intuition Behind GLRT</h3>
               
               <div class="highlight-box">
                   <h4>Why Does GLRT Work?</h4>
                   <ul>
                       <li><strong>Likelihood Comparison:</strong> Compares how well data fits under two hypotheses</li>
                       <li><strong>Nested Models:</strong> $H_0$ model is a special case of $H_1$ model</li>
                       <li><strong>Penalty for Restrictions:</strong> If restrictions are false, likelihood drops significantly</li>
                       <li><strong>Asymptotic Theory:</strong> Chi-squared distribution emerges from quadratic approximation</li>
                   </ul>
               </div>
               
               <h3>Visualization of GLRT</h3>
               
               <div style="display: flex; justify-content: center; margin: 2rem 0;">
                   <svg width="700" height="400" viewBox="0 0 700 400">
                       <!-- Background -->
                       <rect width="700" height="400" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                       
                       <!-- Title -->
                       <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#667eea">
                           Generalized Likelihood Ratio Test
                       </text>
                       
                       <!-- Unrestricted likelihood -->
                       <g transform="translate(50, 80)">
                           <rect x="0" y="0" width="250" height="150" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                           <text x="125" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#155724">
                               Unrestricted Model (H₁)
                           </text>
                           
                           <!-- Likelihood surface -->
                           <ellipse cx="125" cy="85" rx="80" ry="40" fill="none" stroke="#28a745" stroke-width="2"/>
                           <ellipse cx="125" cy="85" rx="60" ry="30" fill="none" stroke="#28a745" stroke-width="2"/>
                           <ellipse cx="125" cy="85" rx="40" ry="20" fill="none" stroke="#28a745" stroke-width="2"/>
                           <ellipse cx="125" cy="85" rx="20" ry="10" fill="none" stroke="#28a745" stroke-width="2"/>
                           
                           <!-- Maximum point -->
                           <circle cx="125" cy="85" r="4" fill="#28a745"/>
                           <text x="125" y="105" text-anchor="middle" font-size="11" fill="#28a745">θ̂_{H₁}</text>
                           
                           <!-- Likelihood value -->
                           <text x="125" y="130" text-anchor="middle" font-size="12" fill="#155724">
                               ℓ(θ̂_{H₁}) = Maximum
                           </text>
                       </g>
                       
                       <!-- Restricted likelihood -->
                       <g transform="translate(400, 80)">
                           <rect x="0" y="0" width="250" height="150" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="10"/>
                           <text x="125" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#721c24">
                               Restricted Model (H₀)
                           </text>
                           
                           <!-- Constraint line -->
                           <line x1="50" y1="50" x2="200" y2="120" stroke="#dc3545" stroke-width="3"/>
                           <text x="220" y="90" text-anchor="start" font-size="10" fill="#dc3545">R(θ) = 0</text>
                           
                           <!-- Likelihood contours -->
                           <ellipse cx="125" cy="85" rx="80" ry="40" fill="none" stroke="#28a745" stroke-width="1" opacity="0.5"/>
                           <ellipse cx="125" cy="85" rx="60" ry="30" fill="none" stroke="#28a745" stroke-width="1" opacity="0.5"/>
                           <ellipse cx="125" cy="85" rx="40" ry="20" fill="none" stroke="#28a745" stroke-width="1" opacity="0.5"/>
                           
                           <!-- Restricted maximum -->
                           <circle cx="140" cy="95" r="4" fill="#dc3545"/>
                           <text x="140" y="115" text-anchor="middle" font-size="11" fill="#dc3545">θ̂_{H₀}</text>
                           
                           <!-- Likelihood value -->
                           <text x="125" y="130" text-anchor="middle" font-size="12" fill="#721c24">
                               ℓ(θ̂_{H₀}) ≤ ℓ(θ̂_{H₁})
                           </text>
                       </g>
                       
                       <!-- Test statistic -->
                       <rect x="150" y="260" width="400" height="80" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
                       <text x="350" y="285" text-anchor="middle" font-size="14" font-weight="bold" fill="#856404">
                           Test Statistic
                       </text>
                       <text x="350" y="305" text-anchor="middle" font-size="13" fill="#856404">
                           λ = 2[ℓ(θ̂_{H₁}) - ℓ(θ̂_{H₀})]
                       </text>
                       <text x="350" y="325" text-anchor="middle" font-size="12" fill="#856404">
                           Under H₀: λ ~ χ²_r as n → ∞
                       </text>
                       
                       <!-- Decision rule -->
                       <rect x="50" y="360" width="600" height="30" fill="#e7f3ff" stroke="#007bff" stroke-width="1" rx="5"/>
                       <text x="350" y="380" text-anchor="middle" font-size="12" fill="#004085">
                           Decision: Reject H₀ if λ > χ²_{r,α} (where α is significance level)
                       </text>
                   </svg>
               </div>
               
               <h3>Applications and Examples</h3>
               
               <div class="theorem-box">
                   <h4>Common Applications</h4>
                   <ul style="text-align: left; display: inline-block;">
                       <li><strong>Parameter Significance:</strong> $H_0: \theta_j = 0$ (single parameter)</li>
                       <li><strong>Model Comparison:</strong> Nested model selection</li>
                       <li><strong>Goodness of Fit:</strong> Compare model to saturated model</li>
                       <li><strong>Linear Restrictions:</strong> $H_0: \mathbf{C}\boldsymbol{\theta} = \mathbf{d}$</li>
                   </ul>
               </div>
               
               <div class="highlight-box">
                   <h4>Advantages of GLRT</h4>
                   <ul>
                       <li><strong>General Framework:</strong> Works for any nested hypothesis</li>
                       <li><strong>Asymptotic Optimality:</strong> Most powerful test under regularity conditions</li>
                       <li><strong>Easy Implementation:</strong> Just compare likelihood values</li>
                       <li><strong>Model Selection:</strong> Natural framework for comparing models</li>
                   </ul>
               </div>
               
               <div class="formula-box">
                   <h4>Relationship to Other Tests</h4>
                   <p>The GLRT is related to other common tests:</p>
                   <ul style="text-align: left; display: inline-block;">
                       <li><strong>Wald Test:</strong> $W = (\hat{\boldsymbol{\theta}} - \boldsymbol{\theta}_0)^T \mathbf{I}(\hat{\boldsymbol{\theta}}) (\hat{\boldsymbol{\theta}} - \boldsymbol{\theta}_0)$</li>
                       <li><strong>Score Test:</strong> $S = \mathbf{u}^T(\boldsymbol{\theta}_0) \mathbf{I}^{-1}(\boldsymbol{\theta}_0) \mathbf{u}(\boldsymbol{\theta}_0)$</li>
                       <li><strong>Asymptotic Equivalence:</strong> All three tests are asymptotically equivalent</li>
                   </ul>
                               </div>
            </section>
            
            <!-- Visualization Section -->
            <section id="visualization" class="section">
                <h2>Conceptual Framework and Visualizations</h2>
                
                <p>This final section provides a comprehensive visual summary of maximum likelihood estimation theory and its interconnected concepts.</p>
                
                <div style="display: flex; justify-content: center; margin: 2rem 0;">
                    <svg width="900" height="800" viewBox="0 0 900 800">
                        <!-- Background -->
                        <rect width="900" height="800" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" rx="15"/>
                        
                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#667eea">
                            Maximum Likelihood Estimation: Complete Theoretical Framework
                        </text>
                        
                        <!-- Core MLE concept -->
                        <circle cx="450" cy="400" r="60" fill="#667eea" stroke="#764ba2" stroke-width="4"/>
                        <text x="450" y="395" text-anchor="middle" font-size="12" font-weight="bold" fill="white">
                            Maximum
                        </text>
                        <text x="450" y="410" text-anchor="middle" font-size="12" font-weight="bold" fill="white">
                            Likelihood
                        </text>
                        <text x="450" y="425" text-anchor="middle" font-size="12" font-weight="bold" fill="white">
                            Estimation
                        </text>
                        
                        <!-- Invariance -->
                        <rect x="100" y="100" width="160" height="90" fill="#ffeaa7" stroke="#e17055" stroke-width="2" rx="10"/>
                        <text x="180" y="125" text-anchor="middle" font-size="13" font-weight="bold" fill="#2d3436">
                            Invariance Property
                        </text>
                        <text x="180" y="145" text-anchor="middle" font-size="11" fill="#2d3436">
                            γ̂ = g(θ̂)
                        </text>
                        <text x="180" y="160" text-anchor="middle" font-size="10" fill="#636e72">
                            Flexible parameterization
                        </text>
                        <text x="180" y="175" text-anchor="middle" font-size="10" fill="#636e72">
                            No re-optimization needed
                        </text>
                        
                        <!-- Expected Log-Likelihood -->
                        <rect x="320" y="100" width="160" height="90" fill="#a8edea" stroke="#00b894" stroke-width="2" rx="10"/>
                        <text x="400" y="125" text-anchor="middle" font-size="13" font-weight="bold" fill="#2d3436">
                            Expected Log-Likelihood
                        </text>
                        <text x="400" y="145" text-anchor="middle" font-size="11" fill="#2d3436">
                            4 Fundamental Results
                        </text>
                        <text x="400" y="160" text-anchor="middle" font-size="10" fill="#636e72">
                            E[∂ℓ/∂θ] = 0
                        </text>
                        <text x="400" y="175" text-anchor="middle" font-size="10" fill="#636e72">
                            Fisher Information I
                        </text>
                        
                        <!-- Consistency -->
                        <rect x="540" y="100" width="160" height="90" fill="#fab1a0" stroke="#e84393" stroke-width="2" rx="10"/>
                        <text x="620" y="125" text-anchor="middle" font-size="13" font-weight="bold" fill="#2d3436">
                            Consistency
                        </text>
                        <text x="620" y="145" text-anchor="middle" font-size="11" fill="#2d3436">
                            θ̂ₙ → θ₀ as n → ∞
                        </text>
                        <text x="620" y="160" text-anchor="middle" font-size="10" fill="#636e72">
                            Law of Large Numbers
                        </text>
                        <text x="620" y="175" text-anchor="middle" font-size="10" fill="#636e72">
                            Asymptotic unbiasedness
                        </text>
                        
                        <!-- Asymptotic Normality -->
                        <rect x="100" y="620" width="160" height="90" fill="#d1ecf1" stroke="#17a2b8" stroke-width="2" rx="10"/>
                        <text x="180" y="645" text-anchor="middle" font-size="13" font-weight="bold" fill="#2d3436">
                            Asymptotic Normality
                        </text>
                        <text x="180" y="665" text-anchor="middle" font-size="11" fill="#2d3436">
                            θ̂ ~ N(θ₀, I⁻¹/n)
                        </text>
                        <text x="180" y="680" text-anchor="middle" font-size="10" fill="#636e72">
                            Central Limit Theorem
                        </text>
                        <text x="180" y="695" text-anchor="middle" font-size="10" fill="#636e72">
                            Confidence intervals
                        </text>
                        
                        <!-- Likelihood Ratio Test -->
                        <rect x="540" y="620" width="160" height="90" fill="#f8e8f8" stroke="#6f42c1" stroke-width="2" rx="10"/>
                        <text x="620" y="645" text-anchor="middle" font-size="13" font-weight="bold" fill="#2d3436">
                            Likelihood Ratio Test
                        </text>
                        <text x="620" y="665" text-anchor="middle" font-size="11" fill="#2d3436">
                            2λ ~ χ²ᵣ
                        </text>
                        <text x="620" y="680" text-anchor="middle" font-size="10" fill="#636e72">
                            Hypothesis testing
                        </text>
                        <text x="620" y="695" text-anchor="middle" font-size="10" fill="#636e72">
                            Model comparison
                        </text>
                        
                        <!-- Fisher Information (central concept) -->
                        <rect x="320" y="620" width="160" height="90" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                        <text x="400" y="645" text-anchor="middle" font-size="13" font-weight="bold" fill="#2d3436">
                            Fisher Information
                        </text>
                        <text x="400" y="665" text-anchor="middle" font-size="11" fill="#2d3436">
                            I = E[(∂ℓ/∂θ)²]
                        </text>
                        <text x="400" y="680" text-anchor="middle" font-size="10" fill="#636e72">
                            Information about θ
                        </text>
                        <text x="400" y="695" text-anchor="middle" font-size="10" fill="#636e72">
                            Precision measure
                        </text>
                        
                        <!-- Arrows and connections -->
                        <defs>
                            <marker id="main-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                            </marker>
                        </defs>
                        
                        <!-- From properties to MLE -->
                        <line x1="250" y1="190" x2="390" y2="350" stroke="#667eea" stroke-width="2" marker-end="url(#main-arrow)"/>
                        <line x1="400" y1="190" x2="430" y2="340" stroke="#667eea" stroke-width="2" marker-end="url(#main-arrow)"/>
                        <line x1="550" y1="190" x2="490" y2="350" stroke="#667eea" stroke-width="2" marker-end="url(#main-arrow)"/>
                        
                        <!-- From MLE to applications -->
                        <line x1="390" y1="450" x2="250" y2="620" stroke="#667eea" stroke-width="2" marker-end="url(#main-arrow)"/>
                        <line x1="430" y1="460" x2="400" y2="620" stroke="#667eea" stroke-width="2" marker-end="url(#main-arrow)"/>
                        <line x1="490" y1="450" x2="550" y2="620" stroke="#667eea" stroke-width="2" marker-end="url(#main-arrow)"/>
                        
                        <!-- Central connections -->
                        <line x1="400" y1="190" x2="400" y2="340" stroke="#28a745" stroke-width="3" marker-end="url(#main-arrow)"/>
                        <line x1="400" y1="460" x2="400" y2="620" stroke="#28a745" stroke-width="3" marker-end="url(#main-arrow)"/>
                        
                        <!-- Mathematical foundations -->
                        <rect x="720" y="250" width="150" height="300" fill="#fff3cd" stroke="#ffc107" stroke-width="2" rx="10"/>
                        <text x="795" y="275" text-anchor="middle" font-size="14" font-weight="bold" fill="#856404">
                            Mathematical
                        </text>
                        <text x="795" y="290" text-anchor="middle" font-size="14" font-weight="bold" fill="#856404">
                            Foundations
                        </text>
                        
                        <text x="795" y="320" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">
                            LOLN
                        </text>
                        <text x="795" y="335" text-anchor="middle" font-size="10" fill="#856404">
                            X̄ₙ → μ
                        </text>
                        <text x="795" y="350" text-anchor="middle" font-size="9" fill="#856404">
                            Sample → Population
                        </text>
                        
                        <text x="795" y="380" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">
                            CLT
                        </text>
                        <text x="795" y="395" text-anchor="middle" font-size="10" fill="#856404">
                            √n(X̄ₙ-μ) ~ N(0,σ²)
                        </text>
                        <text x="795" y="410" text-anchor="middle" font-size="9" fill="#856404">
                            Asymptotic normality
                        </text>
                        
                        <text x="795" y="440" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">
                            Jensen's
                        </text>
                        <text x="795" y="455" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">
                            Inequality
                        </text>
                        <text x="795" y="470" text-anchor="middle" font-size="10" fill="#856404">
                            E[c(Y)] ≤ c(E[Y])
                        </text>
                        <text x="795" y="485" text-anchor="middle" font-size="9" fill="#856404">
                            Global maximum
                        </text>
                        
                        <text x="795" y="515" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">
                            Taylor
                        </text>
                        <text x="795" y="530" text-anchor="middle" font-size="12" font-weight="bold" fill="#856404">
                            Expansion
                        </text>
                        <text x="795" y="545" text-anchor="middle" font-size="10" fill="#856404">
                            f(x) ≈ f(a) + f'(a)(x-a)
                        </text>
                        <text x="795" y="560" text-anchor="middle" font-size="9" fill="#856404">
                            Asymptotic distribution
                        </text>
                        
                        <!-- Applications -->
                        <rect x="30" y="250" width="150" height="300" fill="#e7f3ff" stroke="#007bff" stroke-width="2" rx="10"/>
                        <text x="105" y="275" text-anchor="middle" font-size="14" font-weight="bold" fill="#004085">
                            Applications
                        </text>
                        
                        <text x="105" y="310" text-anchor="middle" font-size="12" font-weight="bold" fill="#004085">
                            GLMs
                        </text>
                        <text x="105" y="325" text-anchor="middle" font-size="10" fill="#004085">
                            Generalized Linear Models
                        </text>
                        
                        <text x="105" y="355" text-anchor="middle" font-size="12" font-weight="bold" fill="#004085">
                            GAMs
                        </text>
                        <text x="105" y="370" text-anchor="middle" font-size="10" fill="#004085">
                            Generalized Additive Models
                        </text>
                        
                        <text x="105" y="400" text-anchor="middle" font-size="12" font-weight="bold" fill="#004085">
                            Mixed Models
                        </text>
                        <text x="105" y="415" text-anchor="middle" font-size="10" fill="#004085">
                            Random Effects
                        </text>
                        
                        <text x="105" y="445" text-anchor="middle" font-size="12" font-weight="bold" fill="#004085">
                            Time Series
                        </text>
                        <text x="105" y="460" text-anchor="middle" font-size="10" fill="#004085">
                            ARIMA, GARCH
                        </text>
                        
                        <text x="105" y="490" text-anchor="middle" font-size="12" font-weight="bold" fill="#004085">
                            Survival Analysis
                        </text>
                        <text x="105" y="505" text-anchor="middle" font-size="10" fill="#004085">
                            Cox models
                        </text>
                        
                        <text x="105" y="535" text-anchor="middle" font-size="12" font-weight="bold" fill="#004085">
                            Machine Learning
                        </text>
                        <text x="105" y="550" text-anchor="middle" font-size="10" fill="#004085">
                            Neural networks
                        </text>
                        
                        <!-- Key insight at bottom -->
                        <rect x="200" y="750" width="500" height="40" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="10"/>
                        <text x="450" y="770" text-anchor="middle" font-size="14" font-weight="bold" fill="#155724">
                            Unified Framework: MLE Theory Connects Mathematics to Practice
                        </text>
                        <text x="450" y="785" text-anchor="middle" font-size="12" fill="#155724">
                            Rigorous theory → Practical inference → Wide applications
                        </text>
                    </svg>
                </div>
                
                <h3>Summary of Key Results</h3>
                
                <div class="theorem-box">
                    <h4>The Complete MLE Story</h4>
                    <ol style="text-align: left; display: inline-block;">
                        <li><strong>Invariance:</strong> Flexible parameterization without re-optimization</li>
                        <li><strong>Expected Log-Likelihood:</strong> Four fundamental results establish theoretical foundation</li>
                        <li><strong>Consistency:</strong> Convergence to true parameter values as sample size increases</li>
                        <li><strong>Asymptotic Normality:</strong> Normal distribution enables confidence intervals and tests</li>
                        <li><strong>Likelihood Ratio Tests:</strong> Principled hypothesis testing framework</li>
                        <li><strong>Optimality:</strong> Asymptotically efficient (minimal variance among unbiased estimators)</li>
                    </ol>
                </div>
                
                <div class="highlight-box">
                    <h4>Why MLE is Fundamental</h4>
                    <ul>
                        <li><strong>Unified Theory:</strong> Single framework for diverse statistical models</li>
                        <li><strong>Optimal Properties:</strong> Consistency, asymptotic normality, efficiency</li>
                        <li><strong>Practical Implementation:</strong> Numerical optimization algorithms</li>
                        <li><strong>Inference Tools:</strong> Confidence intervals, hypothesis tests, model selection</li>
                        <li><strong>Wide Applicability:</strong> Foundation for GLMs, GAMs, mixed models, and more</li>
                    </ul>
                </div>
                
                <div class="formula-box">
                    <h4>The Mathematical Journey</h4>
                    <p>Starting from basic probability theory, we've seen how:</p>
                    <ol style="text-align: left; display: inline-block;">
                        <li>The <strong>Law of Large Numbers</strong> ensures sample quantities converge to population quantities</li>
                        <li>The <strong>Central Limit Theorem</strong> provides asymptotic normality</li>
                        <li><strong>Jensen's Inequality</strong> establishes global optimality properties</li>
                        <li><strong>Taylor Expansions</strong> enable asymptotic distribution derivations</li>
                        <li><strong>Fisher Information</strong> quantifies the precision of estimation</li>
                    </ol>
                    
                    <p><strong>Result:</strong> A complete theoretical framework that bridges pure mathematics and statistical practice.</p>
                </div>
                
                <h3>Practical Takeaways</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1.5rem 0;">
                    <div class="highlight-box">
                        <h4>For Practitioners</h4>
                        <ul>
                            <li><strong>Reliability:</strong> MLEs are consistent and asymptotically optimal</li>
                            <li><strong>Flexibility:</strong> Invariance allows convenient parameterizations</li>
                            <li><strong>Inference:</strong> Standard errors and confidence intervals from Fisher Information</li>
                            <li><strong>Testing:</strong> Likelihood ratio tests for model comparison</li>
                        </ul>
                    </div>
                    
                    <div class="highlight-box">
                        <h4>For Theorists</h4>
                        <ul>
                            <li><strong>Foundation:</strong> Expected log-likelihood properties are fundamental</li>
                            <li><strong>Optimality:</strong> Cramér-Rao efficiency achieved asymptotically</li>
                            <li><strong>Generality:</strong> Framework extends to complex models</li>
                            <li><strong>Connections:</strong> Links to information theory and decision theory</li>
                        </ul>
                    </div>
                </div>
                
                <div class="theorem-box">
                    <h4>Final Thought</h4>
                    <p>Maximum Likelihood Estimation represents one of the most elegant and powerful ideas in statistics: <strong>finding parameter values that make the observed data most probable</strong>. The mathematical theory we've developed shows that this intuitive idea leads to estimators with optimal statistical properties, providing a rigorous foundation for statistical inference across virtually all areas of data analysis.</p>
                </div>
            </section>
            
        </div>
    
    <script>
        // Progress bar functionality
        window.addEventListener('scroll', () => {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            document.getElementById('progressBar').style.width = scrolled + '%';
        });
    </script>
</body>
</html> 