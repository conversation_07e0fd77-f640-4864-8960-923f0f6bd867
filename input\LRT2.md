Likelihood ratio test
The likelihood ratio test is a statistical test that is used by
many RNAseq algorithms to assess differential expression. It
compares the likelihood of the data assuming no differential
expression (null model) against the likelihood of the data assuming differential expression (alternative model).
D = −2 log likelihood of null model
likelihood of alternative model
(7)
It can be shown that D follows a χ
2 distribution, and this
can be used to calculate a p value
We will explain the LRT using an example from football
and then show how it can be applied to RNAseq data
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RP<PERSON><PERSON>’s exact
test
Poisson
LRT
Negative
Binomial
Likelihood ratio test
Let’s say we are interesting in the average number of goals per
game in World Cup football matches. Our null hypothesis is
that there are three goals per game.
0 1 2 3 4 5 6 7 8 9 10
Goals per game
Number of games
0 20 40 60 80 100 120
RNA-seq (2)
<PERSON>.
<PERSON>
RNA-seq
RPK<PERSON>’s exact
test
Poisson
LRT
Negative
Binomial
Goals per Game: MLE
We first decide to model goals per game as a Poisson distribution and to calculate the Maximum Likelihood Estimate (MLE)
of this quantity
Goals Frequency
0 30
1 79
2 99
3 67
4 61
5 24
6 11
7 6
8 2
9 1
10+ 0
Total 380
Likelihood: View a probability distribution as a
function of the parameters given a set of
observed data
L(Θ|X) = Y
N
i=1
Poisson(xi
, λ)
Goal of MLE: find the value of λ that
maximizes this expression for the data we have
observed
RNA-seq (2)
<PERSON> N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
Goals per Game: MLE
L(Θ|X) = Y
N
i=1
Poisson(xi
, λ)
=
Y
N
i=1
e
−λλ
xi
xi!
=
e
−Nλλ
PN
i=1 xi
QN
i=1 xi!
Note we generally maximize the log likelihood, because it is usually easier
to calculate and identifies the same maximum because of the monotonicity
of the logarithm.
log L(Θ|X) = −Nλ +
X
N
i=1
xi
log λ −
X
N
i=1
log xi!
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
Goals per Game: MLE
To find the max, we take the first derivative with respect to λ
and set equal to zero.
d
dλ
log L(Θ|X) = −N +
PN
i=1 xi
λ
− 0
This of course leads to
λˆ =
PN
i=1 xi
N
= x (8)
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
Goals per Game: MLE
Our MLE for the number of goals per game is then simply
λˆ =
PN
i=1 xi
N
=
P380
i=1 xi
380
=
975
380
= 2.57
0 2 4 6 8 10
−2000 −1500 −1000 −500
0
λ
Likelihood
λ
^ = 2.57
The maximum likelihood estimate maximizes the likelihood of the data
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
Likelihood Ratio Test
Evaluate the log-Likelihood under H0
Evaluate the maximum log-Likelihood under Ha
Any terms not involving parameter (here: λ) can be
ignored
Under null hypothesis (and large samples), the following
statistic is approximately χ
2 with 1 degree of freedom
(number of constraints under H0)
LRT = −2
h
log L(θ0, x) − log L(θ, ˆ x)
i
(9)
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: Goals per game
Let’s say that our null hypothesis is that the average number
of goals per game is 3, i.e., λ0 = 3, and the executives of a
private network will only get their performance bonus from the
advertisers if this is true during the cup, because games with
less or more goals are considered boring by many viewers.
Under the null, we have:
H0 : log L(λ0|X) = −380λ0 +
X
380
i=1
xi
log λ0 −
X
380
i=1
xi! (10)
The alternative:
Ha : log L(λˆ|X) = −380λˆ +
X
380
i=1
xi
log λˆ −
X
380
i=1
xi! (11)
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: Goals per game
To calculate the LRT, note that we can ignore the term
P380
i=1 xi!
Recall λ0 = 3 and λˆ = 2.57
log L(λ0|X) = −380 × 3 + 975 × log 3 = −68.85
log L(λˆ|X) = −380 × 2.57 + 975 × log 2.57 = −56.29
Our test statistic is thus
LRT = −2 [−68.85 − (−56.29)] = 25.12
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: Goals per game
Finally, we compare the result from the LRT with the critical
value for the χ
2 distribution with one degree of freedom
25.12  χ
2
0.05,1 = 3.84
Thus, the result of the LRT is clearly
significant at α = 0.05
We can reject the null hypothesis that the
number of goals per game is 3
No bonus this year...
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: RNAseq
Marioni et al. use the LRT to investigate RNAseq samples for
differential expression between two conditions A and B
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: RNAseq
xijk : number of reads mapped to gene j for the k
th lane of
data from sample i
Then we can assume that x ∼ Poisson(λijk )
λijk = cik νijk represents the (unknown) mean of the
Poisson distribution, where cik represents the total rate at
which lane k of sample i produces reads and νijk
represents the rate at which reads map to gene j (in lane
k of sample i) relative to other genes.
Note that P
j
0 νij0
k = 1.
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: RNAseq
The null hypothesis of no differential expression
corresponds to νijk = νj
for gene j in all samples
The alternative hypothesis corresponds to νijk = ν
A
j
for
samples in group A, and ν
B
j
for samples in group B with
ν
A
j
6= ν
B
j
.
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: RNAseq
Under the null, we have:
H0 : log L(λ0|X) = −Nλ0 +
Xn
i=1
xi
log λ0 −
Xn
i=1
xi
! (12)
The alternative:
Ha : log L(λˆ|X) = −NAλˆ
A −NB λˆ
B +
Xna
i=1
xi
log λˆ
A +
Xnb
i=1
xi
log λˆ
B −
Xn
i=1
xi
! (13)
Where the total count for gene i in sample A is NA and
NA + NB = N, and the total number of samples in A and
B is given by na and nb, with the total number of samples
n = na + nb.
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: RNAseq
The authors then used a LRT and calculated p values for
each gene based on a χ
2 distribution with one degree of
freedom, quite analogous to the football example
By comparing five lanes each of liver-versus-kidney
samples. At an FDR of 0.1%, we identified 11,493 genes
as differentially expressed between the samples (94% of
these had an estimated absolute log2
-fold change > 0.5;
71% > 1).
Marioni JC et al. (2008) RNA-seq: an assessment of technical reproducibility and comparison with gene
expression arrays. Genome Res. 18:1509-17.
RNA-seq (2)
Peter N.
Robinson
RNA-seq
RPKM
Fisher’s exact
test
Poisson
LRT
Negative
Binomial
LRT: RNAseq
Newer methods have adapted the LRT or variants thereof
to examine the differential expression of the individual
isoforms of a gene