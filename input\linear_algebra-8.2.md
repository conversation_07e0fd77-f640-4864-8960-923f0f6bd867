424 Orthogonality
8.2 Orthogonal Diagonalization
Recall (Theorem 5.5.3) that an n×n matrix A is diagonalizable if and only if it has n linearly independent
eigenvectors. Moreover, the matrix P with these eigenvectors as columns is a diagonalizing matrix for A,
that is
P
−1AP is diagonal.
As we have seen, the really nice bases of R
n
are the orthogonal ones, so a natural question is: which n×n
matrices have an orthogonal basis of eigenvectors? These turn out to be precisely the symmetric matrices,
and this is the main result of this section.
Before proceeding, recall that an orthogonal set of vectors is called orthonormal if kvk = 1 for each
vector v in the set, and that any orthogonal set {v1, v2, ..., vk} can be “normalized”, that is converted into
an orthonormal set {
1
kv1k
v1,
1
kv2k
v2, ...,
1
kvkk
vk}. In particular, if a matrix A has n orthogonal eigenvectors,
they can (by normalizing) be taken to be orthonormal. The corresponding diagonalizing matrix P has
orthonormal columns, and such matrices are very easy to invert.
Theorem 8.2.1
The following conditions are equivalent for an n×n matrix P.
1. P is invertible and P
−1 = P
T
.
2. The rows of P are orthonormal.
3. The columns of P are orthonormal.
Proof. First recall that condition (1) is equivalent to PPT = I by Corollary 2.4.1 of Theorem 2.4.5. Let
x1, x2, ..., xn denote the rows of P. Then x
T
j
is the jth column of P
T
, so the (i, j)-entry of PPT
is xi
· xj
.
Thus PPT = I means that xi
· xj = 0 if i 6= j and xi
· xj = 1 if i = j. Hence condition (1) is equivalent to
(2). The proof of the equivalence of (1) and (3) is similar.
Definition 8.3 Orthogonal Matrices
An n×n matrix P is called an orthogonal matrix
2
if it satisfies one (and hence all) of the
conditions in Theorem 8.2.1.
Example 8.2.1
The rotation matrix 
cosθ −sinθ
sinθ cosθ

is orthogonal for any angle θ.
These orthogonal matrices have the virtue that they are easy to invert—simply take the transpose. But
they have many other important properties as well. If T : R
n → R
n
is a linear operator, we will prove
2
In view of (2) and (3) of Theorem 8.2.1, orthonormal matrix might be a better name. But orthogonal matrix is standard.
8.2. Orthogonal Diagonalization 425
(Theorem 10.4.3) that T is distance preserving if and only if its matrix is orthogonal. In particular, the
matrices of rotations and reflections about the origin in R
2
and R
3
are all orthogonal (see Example 8.2.1).
It is not enough that the rows of a matrix A are merely orthogonal for A to be an orthogonal matrix.
Here is an example.
Example 8.2.2
The matrix


2 1 1
−1 1 1
0 −1 1

 has orthogonal rows but the columns are not orthogonal. However, if
the rows are normalized, the resulting matrix





√
2
6
√
1
6
√
1
6
√−1
3
√
1
3
√
1
3
0 √−1
2
√
1
2





is orthogonal (so the columns are
now orthonormal as the reader can verify).
Example 8.2.3
If P and Q are orthogonal matrices, then PQ is also orthogonal, as is P
−1 = P
T
.
Solution. P and Q are invertible, so PQ is also invertible and
(PQ)
−1 = Q
−1P
−1 = Q
TP
T = (PQ)
T
Hence PQ is orthogonal. Similarly,
(P
−1
)
−1 = P = (P
T
)
T = (P
−1
)
T
shows that P
−1
is orthogonal.
Definition 8.4 Orthogonally Diagonalizable Matrices
An n×n matrix A is said to be orthogonally diagonalizable when an orthogonal matrix P can be
found such that P
−1AP = P
TAP is diagonal.
This condition turns out to characterize the symmetric matrices.
Theorem 8.2.2: Principal Axes Theorem
The following conditions are equivalent for an n×n matrix A.
1. A has an orthonormal set of n eigenvectors.
2. A is orthogonally diagonalizable.
3. A is symmetric.
426 Orthogonality
Proof. (1) ⇔ (2). Given (1), let x1, x2, ..., xn be orthonormal eigenvectors of A. Then P =

x1 x2 ... xn

is orthogonal, and P
−1AP is diagonal by Theorem 3.3.4. This proves (2). Conversely, given (2) let P
−1AP
be diagonal where P is orthogonal. If x1, x2, ..., xn are the columns of P then {x1, x2, ..., xn} is an
orthonormal basis of R
n
that consists of eigenvectors of A by Theorem 3.3.4. This proves (1).
(2) ⇒ (3). If P
TAP = D is diagonal, where P
−1 = P
T
, then A = PDPT
. But D
T = D, so this gives
A
T = P
T TD
TP
T = PDPT = A.
(3) ⇒ (2). If A is an n × n symmetric matrix, we proceed by induction on n. If n = 1, A is already
diagonal. If n > 1, assume that (3) ⇒ (2) for (n−1)×(n−1) symmetric matrices. By Theorem 5.5.7 let
λ1 be a (real) eigenvalue of A, and let Ax1 = λ1x1, where kx1k = 1. Use the Gram-Schmidt algorithm to
find an orthonormal basis {x1, x2, ..., xn} for R
n
. Let P1 =

x1 x2 ... xn

, so P1 is an orthogonal
matrix and P
T
1 AP1 =

λ1 B
0 A1

in block form by Lemma 5.5.2. But P
T
1 AP1 is symmetric (A is), so it
follows that B = 0 and A1 is symmetric. Then, by induction, there exists an (n−1)×(n−1) orthogonal
matrix Q such that Q
TA1Q = D1 is diagonal. Observe that P2 =

1 0
0 Q

is orthogonal, and compute:
(P1P2)
TA(P1P2) = P
T
2
(P
T
1 AP1)P2
=

1 0
0 Q
T
  λ1 0
0 A1
  1 0
0 Q

=

λ1 0
0 D1

is diagonal. Because P1P2 is orthogonal, this proves (2).
A set of orthonormal eigenvectors of a symmetric matrix A is called a set of principal axes for A. The
name comes from geometry, and this is discussed in Section 8.9. Because the eigenvalues of a (real)
symmetric matrix are real, Theorem 8.2.2 is also called the real spectral theorem, and the set of distinct
eigenvalues is called the spectrum of the matrix. In full generality, the spectral theorem is a similar result
for matrices with complex entries (Theorem 8.7.8).
Example 8.2.4
Find an orthogonal matrix P such that P
−1AP is diagonal, where A =


1 0 −1
0 1 2
−1 2 5

.
Solution. The characteristic polynomial of A is (adding twice row 1 to row 2):
cA(x) = det


x−1 0 1
0 x−1 −2
1 −2 x−5

 = x(x−1)(x−6)
Thus the eigenvalues are λ = 0, 1, and 6, and corresponding eigenvectors are
x1 =


1
−2
1

 x2 =


2
1
0

 x3 =


−1
2
5


8.2. Orthogonal Diagonalization 427
respectively. Moreover, by what appears to be remarkably good luck, these eigenvectors are
orthogonal. We have kx1k
2 = 6, kx2k
2 = 5, and kx3k
2 = 30, so
P =
h
√
1
6
x1 √
1
5
x2 √
1
30 x3
i
= √
1
30


√
5 2√
6 −1
−2
√
5
√
6 2
√
5 0 5


is an orthogonal matrix. Thus P
−1 = P
T
and
P
TAP =


0 0 0
0 1 0
0 0 6


by the diagonalization algorithm.
Actually, the fact that the eigenvectors in Example 8.2.4 are orthogonal is no coincidence. Theorem 5.5.4 guarantees they are linearly independent (they correspond to distinct eigenvalues); the fact that
the matrix is symmetric implies that they are orthogonal. To prove this we need the following useful fact
about symmetric matrices.
Theorem 8.2.3
If A is an n×n symmetric matrix, then
(Ax)· y = x ·(Ay)
for all columns x and y in R
n
.
3
Proof. Recall that x · y = x
T y for all columns x and y. Because A
T = A, we get
(Ax)· y = (Ax)
T
y = x
TA
T
y = x
TAy = x ·(Ay)
Theorem 8.2.4
If A is a symmetric matrix, then eigenvectors of A corresponding to distinct eigenvalues are
orthogonal.
Proof. Let Ax = λx and Ay = µy, where λ 6= µ. Using Theorem 8.2.3, we compute
λ(x · y) = (λx)· y = (Ax)· y = x ·(Ay) = x ·(µy) = µ(x · y)
Hence (λ − µ)(x · y) = 0, and so x · y = 0 because λ 6= µ.
3The converse also holds (Exercise 8.2.15).
428 Orthogonality
Now the procedure for diagonalizing a symmetric n×n matrix is clear. Find the distinct eigenvalues
(all real by Theorem 5.5.7) and find orthonormal bases for each eigenspace (the Gram-Schmidt algorithm
may be needed). Then the set of all these basis vectors is orthonormal (by Theorem 8.2.4) and contains n
vectors. Here is an example.
Example 8.2.5
Orthogonally diagonalize the symmetric matrix A =


8 −2 2
−2 5 4
2 4 5

.
Solution. The characteristic polynomial is
cA(x) = det


x−8 2 −2
2 x−5 −4
−2 −4 x−5

 = x(x−9)
2
Hence the distinct eigenvalues are 0 and 9 of multiplicities 1 and 2, respectively, so dim(E0) = 1
and dim(E9) = 2 by Theorem 5.5.6 (A is diagonalizable, being symmetric). Gaussian elimination
gives
E0(A) = span {x1}, x1 =


1
2
−2

, and E9(A) = span





−2
1
0

,


2
0
1





The eigenvectors in E9 are both orthogonal to x1 as Theorem 8.2.4 guarantees, but not to each
other. However, the Gram-Schmidt process yields an orthogonal basis
{x2, x3} of E9(A) where x2 =


−2
1
0

 and x3 =


2
4
5


Normalizing gives orthonormal vectors {
1
3
x1, √
1
5
x2,
1
3
√
5
x3}, so
P =
h
1
3
x1 √
1
5
x2
1
3
√
5
x3
i
=
1
3
√
5


√
5 −6 2
2
√
5 3 4
−2
√
5 0 5


is an orthogonal matrix such that P
−1AP is diagonal.
It is worth noting that other, more convenient, diagonalizing matrices P exist. For example,
y2 =


2
1
2

 and y3 =


−2
2
1

 lie in E9(A) and they are orthogonal. Moreover, they both have
norm 3 (as does x1), so
Q =

1
3
x1
1
3
y2
1
3
y3

=
1
3


1 2 −2
2 1 2
−2 2 1


is a nicer orthogonal matrix with the property that Q
−1AQ is diagonal.
8.2. Orthogonal Diagonalization 429
O
x1x2 = 1
x1
x2
O y
2
1 −y
2
2 = 1
y2 y1
If A is symmetric and a set of orthogonal eigenvectors of A is given,
the eigenvectors are called principal axes of A. The name comes from
geometry. An expression q = ax2
1+bx1x2+cx2
2
is called a quadratic form
in the variables x1 and x2, and the graph of the equation q = 1 is called a
conic in these variables. For example, if q = x1x2, the graph of q = 1 is
given in the first diagram.
But if we introduce new variables y1 and y2 by setting x1 = y1 +y2 and
x2 = y1 − y2, then q becomes q = y
2
1 − y
2
2
, a diagonal form with no cross
term y1y2 (see the second diagram). Because of this, the y1 and y2 axes
are called the principal axes for the conic (hence the name). Orthogonal
diagonalization provides a systematic method for finding principal axes.
Here is an illustration.
Example 8.2.6
Find principal axes for the quadratic form q = x
2
1 −4x1x2 +x
2
2
.
Solution. In order to utilize diagonalization, we first express q in matrix form. Observe that
q =

x1 x2


1 −4
0 1  x1
x2

The matrix here is not symmetric, but we can remedy that by writing
q = x
2
1 −2x1x2 −2x2x1 +x
2
2
Then we have
q =

x1 x2


1 −2
−2 1  x1
x2

= x
TAx
where x =

x1
x2

and A =

1 −2
−2 1 
is symmetric. The eigenvalues of A are λ1 = 3 and
λ2 = −1, with corresponding (orthogonal) eigenvectors x1 =

1
−1

and x2 =

1
1

. Since
kx1k = kx2k =
√
2, so
P = √
1
2

1 1
−1 1 
is orthogonal and P
TAP = D =

3 0
0 −1

Now define new variables 
y1
y2

= y by y = P
T x, equivalently x = Py (since P
−1 = P
T
). Hence
y1 = √
1
2
(x1 −x2) and y2 = √
1
2
(x1 +x2)
In terms of y1 and y2, q takes the form
q = x
TAx = (Py)
TA(Py) = y
T
(P
TAP)y = y
TDy = 3y
2
1 −y
2
2
Note that y = P
T x is obtained from x by a counterclockwise rotation of π
4
(see Theorem 2.4.6).
430 Orthogonality
Observe that the quadratic form q in Example 8.2.6 can be diagonalized in other ways. For example
q = x
2
1 −4x1x2 +x
2
2 = z
2
1 −
1
3
z
2
2
where z1 = x1 −2x2 and z2 = 3x2. We examine this more carefully in Section 8.9.
If we are willing to replace “diagonal” by “upper triangular” in the principal axes theorem, we can
weaken the requirement that A is symmetric to insisting only that A has real eigenvalues.
Theorem 8.2.5: Triangulation Theorem
If A is an n×n matrix with n real eigenvalues, an orthogonal matrix P exists such that P
TAP is
upper triangular.
4
Proof. We modify the proof of Theorem 8.2.2. If Ax1 = λ1x1 where kx1k = 1, let {x1, x2, ..., xn} be an
orthonormal basis of R
n
, and let P1 =

x1 x2 ··· xn

. Then P1 is orthogonal and P
T
1 AP1 =

λ1 B
0 A1

in block form. By induction, let Q
TA1Q = T1 be upper triangular where Q is of size (n−1)×(n−1) and
orthogonal. Then P2 =

1 0
0 Q

is orthogonal, so P = P1P2 is also orthogonal and P
TAP =

λ1 BQ
0 T1

is upper triangular.
The proof of Theorem 8.2.5 gives no way to construct the matrix P. However, an algorithm will be given in
Section 11.1 where an improved version of Theorem 8.2.5 is presented. In a different direction, a version
of Theorem 8.2.5 holds for an arbitrary matrix with complex entries (Schur’s theorem in Section 8.7).
As for a diagonal matrix, the eigenvalues of an upper triangular matrix are displayed along the main
diagonal. Because A and P
TAP have the same determinant and trace whenever P is orthogonal, Theorem 8.2.5 gives:
Corollary 8.2.1
If A is an n×n matrix with real eigenvalues λ1, λ2, ..., λn (possibly not all distinct), then
det A = λ1λ2 ...λn and tr A = λ1 +λ2 +···+λn.
This corollary remains true even if the eigenvalues are not real (using Schur’s theorem).
Exercises for 8.2
Exercise 8.2.1 Normalize the rows to make each of the
following matrices orthogonal.
A =

1 1
−1 1 
a. A =

3 −4
4 3 
b.
A =

1 2
−4 2 
c.
A =

a b
−b a 
d. , (a, b) 6= (0, 0)
4There is also a lower triangular version.
8.2. Orthogonal Diagonalization 431
A =


cosθ −sinθ 0
sinθ cosθ 0
0 0 2

e. 
A =


2 1 −1
1 −1 1
0 1 1

f. 
A =


−1 2 2
2 −1 2
2 2 −1

g. 
A =


2 6 −3
3 2 6
−6 3 2

h. 
Exercise 8.2.2 If P is a triangular orthogonal matrix,
show that P is diagonal and that all diagonal entries are 1
or −1.
Exercise 8.2.3 If P is orthogonal, show that kP is orthogonal if and only if k = 1 or k = −1.
Exercise 8.2.4 If the first two rows of an orthogonal matrix are (
1
3
,
2
3
,
2
3
) and (
2
3
,
1
3
,
−2
3
), find all possible third
rows.
Exercise 8.2.5 For each matrix A, find an orthogonal
matrix P such that P
−1AP is diagonal.
A =

0 1
1 0 
a. A =

1 −1
−1 1 
b.
A =


3 0 0
0 2 2
0 2 5

c.  A =


3 0 7
0 5 0
7 0 3

d. 
A =


1 1 0
1 1 0
0 0 2

e.  A =


5 −2 −4
−2 8 −2
−4 −2 5

f. 
A =




5 3 0 0
3 5 0 0
0 0 7 1
0 0 1 7




g.
A =




3 5 −1 1
5 3 1 −1
−1 1 3 5
1 −1 5 3




h.
Exercise 8.2.6 Consider A =


0 a 0
a 0 c
0 c 0

 where one
of a, c 6= 0. Show that cA(x) = x(x − k)(x + k), where
k =
√
a
2 +c
2 and find an orthogonal matrix P such that
P
−1AP is diagonal.
Exercise 8.2.7 Consider A =


0 0 a
0 b 0
a 0 0

. Show that
cA(x) = (x−b)(x−a)(x+a) and find an orthogonal matrix P such that P
−1AP is diagonal.
Exercise 8.2.8 Given A =

b a
a b 
, show that
cA(x) = (x−a−b)(x+a−b) and find an orthogonal matrix P such that P
−1AP is diagonal.
Exercise 8.2.9 Consider A =


b 0 a
0 b 0
a 0 b

. Show that
cA(x) = (x−b)(x−b−a)(x−b+a) and find an orthogonal matrix P such that P
−1AP is diagonal.
Exercise 8.2.10 In each case find new variables y1 and
y2 that diagonalize the quadratic form q.
q = x
2
1 +6x1x2 +x
2
2
a. q = x
2
1 +4x1x2 −2x
2
2
b.
Exercise 8.2.11 Show that the following are equivalent
for a symmetric matrix A.
a. A is orthogonal. A b. 2 = I.
c. All eigenvalues of A are ±1.
[Hint: For (b) if and only if (c), use Theorem 8.2.2.]
Exercise 8.2.12 We call matrices A and B orthogonally
similar (and write A
◦∼ B) if B = P
TAP for an orthogonal
matrix P.
a. Show that A
◦∼ A for all A; A
◦∼ B ⇒ B
◦∼ A; and
A
◦∼ B and B
◦∼ C ⇒ A
◦∼ C.
b. Show that the following are equivalent for two
symmetric matrices A and B.
i. A and B are similar.
ii. A and B are orthogonally similar.
iii. A and B have the same eigenvalues.
Exercise 8.2.13 Assume that A and B are orthogonally
similar (Exercise 8.2.12).
a. If A and B are invertible, show that A
−1
and B
−1
are orthogonally similar.
432 Orthogonality
b. Show that A
2
and B
2
are orthogonally similar.
c. Show that, if A is symmetric, so is B.
Exercise 8.2.14 If A is symmetric, show that every
eigenvalue of A is nonnegative if and only if A = B
2
for
some symmetric matrix B.
Exercise 8.2.15 Prove the converse of Theorem 8.2.3:
If (Ax)· y = x ·(Ay) for all n-columns x and y, then
A is symmetric.
Exercise 8.2.16 Show that every eigenvalue of A is zero
if and only if A is nilpotent (A
k = 0 for some k ≥ 1).
Exercise 8.2.17 If A has real eigenvalues, show that
A = B+C where B is symmetric and C is nilpotent.
[Hint: Theorem 8.2.5.]
Exercise 8.2.18 Let P be an orthogonal matrix.
a. Show that det P = 1 or det P = −1.
b. Give 2×2 examples of P such that det P = 1 and
det P = −1.
c. If det P = −1, show that I + P has no inverse.
[Hint: P
T
(I +P) = (I +P)
T
.]
d. If P is n × n and det P 6= (−1)
n
, show that I − P
has no inverse.
[Hint: P
T
(I −P) = −(I −P)
T
.]
Exercise 8.2.19 We call a square matrix E a projection
matrix if E
2 = E = E
T
. (See Exercise 8.1.17.)
a. If E is a projection matrix, show that P = I − 2E
is orthogonal and symmetric.
b. If P is orthogonal and symmetric, show that
E =
1
2
(I −P) is a projection matrix.
c. If U is m × n and U
TU = I (for example, a unit
column in R
n
), show that E =UUT
is a projection
matrix.
Exercise 8.2.20 A matrix that we obtain from the identity matrix by writing its rows in a different order is called
a permutation matrix. Show that every permutation
matrix is orthogonal.
Exercise 8.2.21 If the rows r1, ..., rn of the n×n matrix A = [ai j] are orthogonal, show that the (i, j)-entry of
A
−1
is aji
kr jk
2
.
Exercise 8.2.22
a. Let A be an m×n matrix. Show that the following
are equivalent.
i. A has orthogonal rows.
ii. A can be factored as A = DP, where D is invertible and diagonal and P has orthonormal
rows.
iii. AAT
is an invertible, diagonal matrix.
b. Show that an n× n matrix A has orthogonal rows
if and only if A can be factored as A = DP, where
P is orthogonal and D is diagonal and invertible.
Exercise 8.2.23 Let A be a skew-symmetric matrix; that
is, A
T = −A. Assume that A is an n×n matrix.
a. Show that I + A is invertible. [Hint: By Theorem 2.4.5, it suffices to show that (I + A)x = 0,
x in R
n
, implies x = 0. Compute x · x = x
T x, and
use the fact that Ax = −x and A
2x = x.]
b. Show that P = (I −A)(I +A)
−1
is orthogonal.
c. Show that every orthogonal matrix P such that
I + P is invertible arises as in part (b) from some
skew-symmetric matrix A.
[Hint: Solve P = (I −A)(I +A)
−1
for A.]
