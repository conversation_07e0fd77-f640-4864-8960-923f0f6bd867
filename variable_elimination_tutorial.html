<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Variable Elimination - Exact Inference in Graphical Models</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
            text-align: center;
        }
        
        h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        h2 {
            color: #2c3e50;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            font-size: 1.4em;
        }
        
        .concept-box {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .definition-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .formula-box {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .example-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .algorithm-box {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 2px solid #17a2b8;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .highlight {
            background: linear-gradient(135deg, #dff0d8 0%, #c3e6cb 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        
        .warning {
            background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        
        .outline {
            background: linear-gradient(135deg, #e1ecf4 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .outline ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .outline li {
            margin: 8px 0;
            color: #2c3e50;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .complexity-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .complexity-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .complexity-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .card-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comparison-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .inference-types {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .inference-title {
            color: #d63031;
            font-weight: bold;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Variable Elimination</h1>
            <div class="subtitle">Exact Inference in Graphical Models</div>
        </div>

        <div class="outline">
            <h2>📋 Tutorial Outline</h2>
            <ul>
                <li><strong>Introduction to Inference Problems</strong> - Why inference matters in probabilistic models</li>
                <li><strong>Types of Inference</strong> - Marginal inference and MAP inference</li>
                <li><strong>Chain Example</strong> - Intuitive understanding with a simple case</li>
                <li><strong>Factor Operations</strong> - Product and marginalization operations</li>
                <li><strong>Variable Elimination Algorithm</strong> - General algorithm and examples</li>
                <li><strong>Handling Evidence</strong> - Computing conditional probabilities</li>
                <li><strong>Computational Complexity</strong> - Running time and ordering strategies</li>
                <li><strong>Practical Considerations</strong> - Heuristics and real-world applications</li>
            </ul>
        </div>

        <h2>🎯 Introduction to Inference Problems</h2>
        
        <div class="concept-box">
            <p><strong>What is Inference?</strong></p>
            <p>Given a probabilistic model (Bayesian network or MRF), we want to use it to answer useful questions. For example:</p>
            <ul>
                <li>What's the probability that an email is spam?</li>
                <li>What's the most likely diagnosis given symptoms?</li>
                <li>What's the expected value of a stock tomorrow?</li>
            </ul>
            <p style="text-align: center; font-style: italic; color: #2c3e50;">
                "Inference transforms probabilistic models into actionable insights."
            </p>
        </div>

        <div class="warning">
            <p><strong>The Challenge:</strong> For many probabilities of interest, inference is <strong>NP-hard</strong>. Whether inference is tractable depends critically on the structure of the graph describing the probability distribution.</p>
        </div>

        <h2>📊 Types of Inference</h2>
        
        <div class="inference-types">
            <div class="inference-title">🔍 Two Main Types of Inference</div>
            
            <h3>1. Marginal Inference</h3>
            <p><strong>Question:</strong> What is the probability of a given variable after summing out all others?</p>
            <div class="formula-box">
                $$p(y = 1) = \sum_{x_1} \sum_{x_2} \cdots \sum_{x_n} p(y = 1, x_1, x_2, \ldots, x_n)$$
            </div>
            <p><strong>Example:</strong> Probability of spam vs. non-spam in email classification</p>
            
            <h3>2. Maximum A Posteriori (MAP) Inference</h3>
            <p><strong>Question:</strong> What is the most likely assignment to variables (possibly conditioned on evidence)?</p>
            <div class="formula-box">
                $$\max_{x_1, \ldots, x_n} p(y = 1, x_1, \ldots, x_n)$$
            </div>
            <p><strong>Example:</strong> Most likely configuration of hidden states in a sequence model</p>
        </div>

        <div class="highlight">
            <p><strong>Key Assumption:</strong> Throughout this tutorial, we assume discrete variables $x_i$ taking $k$ possible values each.</p>
        </div>

        <h2>🔗 Chain Example: Intuitive Understanding</h2>

        <div class="concept-box">
            <p><strong>Setting:</strong> Consider a chain Bayesian network with probability:</p>
            <div class="formula-box">
                $$p(x_1, \ldots, x_n) = p(x_1) \prod_{i=2}^n p(x_i | x_{i-1})$$
            </div>
            <p><strong>Goal:</strong> Compute the marginal probability $p(x_n)$</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <defs>
                    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="800" height="300" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Chain Bayesian Network Example
                </text>

                <!-- Chain nodes -->
                <circle cx="100" cy="100" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="100" y="105" text-anchor="middle" font-size="14" fill="white" font-weight="bold">X₁</text>

                <circle cx="200" cy="100" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="200" y="105" text-anchor="middle" font-size="14" fill="white" font-weight="bold">X₂</text>

                <circle cx="300" cy="100" r="25" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="300" y="105" text-anchor="middle" font-size="14" fill="white" font-weight="bold">X₃</text>

                <text x="400" y="105" text-anchor="middle" font-size="16" fill="#2c3e50">...</text>

                <circle cx="500" cy="100" r="25" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="500" y="105" text-anchor="middle" font-size="14" fill="white" font-weight="bold">Xₙ</text>

                <!-- Arrows -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7"
                            refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
                    </marker>
                </defs>
                <line x1="125" y1="100" x2="175" y2="100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                <line x1="225" y1="100" x2="275" y2="100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>
                <line x1="350" y1="100" x2="475" y2="100" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)"/>

                <!-- Naive approach -->
                <text x="400" y="160" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Naive Approach: O(k^n) - Exponential!
                </text>
                <text x="400" y="180" text-anchor="middle" font-size="12" fill="#dc3545">
                    Sum over all k^(n-1) assignments to x₁, ..., x_(n-1)
                </text>

                <!-- Smart approach -->
                <text x="400" y="220" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Variable Elimination: O(nk²) - Linear!
                </text>
                <text x="400" y="240" text-anchor="middle" font-size="12" fill="#28a745">
                    Leverage factorization by "pushing in" summations
                </text>

                <!-- Goal highlight -->
                <rect x="450" y="70" width="100" height="60" fill="none" stroke="#e74c3c" stroke-width="3" stroke-dasharray="5,5" rx="5"/>
                <text x="500" y="50" text-anchor="middle" font-size="12" fill="#e74c3c" font-weight="bold">Goal: p(Xₙ)</text>
            </svg>
        </div>

        <div class="example-box">
            <h4>The Key Insight: Reordering Summations</h4>
            <p><strong>Naive approach:</strong></p>
            <div class="formula-box">
                $$p(x_n) = \sum_{x_1} \cdots \sum_{x_{n-1}} p(x_1, \ldots, x_n)$$
            </div>

            <p><strong>Smart approach - push summations inward:</strong></p>
            <div class="formula-box">
                $$p(x_n) = \sum_{x_{n-1}} p(x_n | x_{n-1}) \sum_{x_{n-2}} p(x_{n-1} | x_{n-2}) \cdots \sum_{x_1} p(x_2 | x_1) p(x_1)$$
            </div>

            <p><strong>Step-by-step elimination:</strong></p>
            <ol>
                <li>Compute $\tau(x_2) = \sum_{x_1} p(x_2 | x_1) p(x_1)$ in $O(k^2)$ time</li>
                <li>Compute $\tau(x_3) = \sum_{x_2} p(x_3 | x_2) \tau(x_2)$ in $O(k^2)$ time</li>
                <li>Continue until we reach $x_n$</li>
            </ol>

            <p><strong>Total complexity:</strong> $O(n)$ steps × $O(k^2)$ per step = $O(nk^2)$</p>
        </div>

        <h2>⚙️ Factor Operations</h2>

        <div class="concept-box">
            <p><strong>Factors as Multi-dimensional Tables:</strong></p>
            <p>A factor $\phi_c(x_c)$ can be viewed as a multi-dimensional table that assigns a value to each assignment of variables $x_c$. In Bayesian networks, factors correspond to CPDs. In MRFs, they encode unnormalized distributions.</p>
        </div>

        <div class="definition-box">
            <h3>Factor Product Operation</h3>
            <p>The product $\phi_3 := \phi_1 \times \phi_2$ of two factors is defined as:</p>
            <div class="formula-box">
                $$\phi_3(x_c) = \phi_1(x_c^{(1)}) \times \phi_2(x_c^{(2)})$$
            </div>
            <p>where:</p>
            <ul>
                <li>The scope of $\phi_3$ is the union of variables in $\phi_1$ and $\phi_2$</li>
                <li>$x_c^{(i)}$ denotes the restriction of $x_c$ to the scope of $\phi_i$</li>
            </ul>

            <p><strong>Example:</strong> $\phi_3(a,b,c) := \phi_1(a,b) \times \phi_2(b,c)$</p>
        </div>

        <div class="definition-box">
            <h3>Factor Marginalization Operation</h3>
            <p>Marginalization "locally" eliminates variables from a factor. Given $\phi(X,Y)$, marginalizing out $Y$ produces:</p>
            <div class="formula-box">
                $$\tau(x) = \sum_y \phi(x,y)$$
            </div>
            <p>where the sum is over all joint assignments to variables in $Y$.</p>

            <p><strong>Important:</strong> The resulting factor $\tau$ doesn't necessarily correspond to a probability distribution, even if $\phi$ was a CPD.</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Factor Operations Visualization
                </text>

                <!-- Factor Product Example -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Factor Product
                </text>

                <!-- Factor 1 table -->
                <rect x="50" y="80" width="120" height="80" fill="white" stroke="#3498db" stroke-width="2" rx="3"/>
                <text x="110" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">φ₁(A,B)</text>
                <text x="80" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">A=0,B=0: 0.6</text>
                <text x="80" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">A=0,B=1: 0.4</text>
                <text x="140" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">A=1,B=0: 0.3</text>
                <text x="140" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">A=1,B=1: 0.7</text>

                <!-- Factor 2 table -->
                <rect x="230" y="80" width="120" height="80" fill="white" stroke="#e74c3c" stroke-width="2" rx="3"/>
                <text x="290" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">φ₂(B,C)</text>
                <text x="260" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">B=0,C=0: 0.8</text>
                <text x="260" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">B=0,C=1: 0.2</text>
                <text x="320" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">B=1,C=0: 0.1</text>
                <text x="320" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">B=1,C=1: 0.9</text>

                <!-- Product result -->
                <text x="175" y="190" text-anchor="middle" font-size="16" fill="#f39c12">×</text>
                <text x="175" y="210" text-anchor="middle" font-size="12" fill="#2c3e50">=</text>

                <rect x="100" y="220" width="150" height="100" fill="white" stroke="#2ecc71" stroke-width="2" rx="3"/>
                <text x="175" y="240" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">φ₃(A,B,C)</text>
                <text x="130" y="260" text-anchor="middle" font-size="9" fill="#2c3e50">A=0,B=0,C=0: 0.48</text>
                <text x="130" y="275" text-anchor="middle" font-size="9" fill="#2c3e50">A=0,B=0,C=1: 0.12</text>
                <text x="130" y="290" text-anchor="middle" font-size="9" fill="#2c3e50">A=0,B=1,C=0: 0.04</text>
                <text x="130" y="305" text-anchor="middle" font-size="9" fill="#2c3e50">A=0,B=1,C=1: 0.36</text>
                <text x="220" y="260" text-anchor="middle" font-size="9" fill="#2c3e50">A=1,B=0,C=0: 0.24</text>
                <text x="220" y="275" text-anchor="middle" font-size="9" fill="#2c3e50">A=1,B=0,C=1: 0.06</text>
                <text x="220" y="290" text-anchor="middle" font-size="9" fill="#2c3e50">A=1,B=1,C=0: 0.07</text>
                <text x="220" y="305" text-anchor="middle" font-size="9" fill="#2c3e50">A=1,B=1,C=1: 0.63</text>

                <!-- Marginalization Example -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Factor Marginalization
                </text>

                <!-- Original factor -->
                <rect x="500" y="80" width="120" height="80" fill="white" stroke="#9b59b6" stroke-width="2" rx="3"/>
                <text x="560" y="100" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">φ(X,Y)</text>
                <text x="530" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">X=0,Y=0: 0.3</text>
                <text x="530" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">X=0,Y=1: 0.7</text>
                <text x="590" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">X=1,Y=0: 0.4</text>
                <text x="590" y="135" text-anchor="middle" font-size="10" fill="#2c3e50">X=1,Y=1: 0.6</text>

                <!-- Marginalization arrow -->
                <text x="560" y="190" text-anchor="middle" font-size="12" fill="#2c3e50">Marginalize out Y</text>
                <text x="560" y="210" text-anchor="middle" font-size="12" fill="#2c3e50">τ(x) = Σᵧ φ(x,y)</text>

                <!-- Result -->
                <rect x="520" y="230" width="80" height="60" fill="white" stroke="#f39c12" stroke-width="2" rx="3"/>
                <text x="560" y="250" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">τ(X)</text>
                <text x="560" y="270" text-anchor="middle" font-size="10" fill="#2c3e50">X=0: 1.0</text>
                <text x="560" y="285" text-anchor="middle" font-size="10" fill="#2c3e50">X=1: 1.0</text>

                <!-- Computation details -->
                <rect x="450" y="310" width="220" height="60" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="3"/>
                <text x="560" y="330" text-anchor="middle" font-size="11" fill="#2c3e50" font-weight="bold">Computation:</text>
                <text x="560" y="345" text-anchor="middle" font-size="10" fill="#2c3e50">τ(X=0) = φ(0,0) + φ(0,1) = 0.3 + 0.7 = 1.0</text>
                <text x="560" y="360" text-anchor="middle" font-size="10" fill="#2c3e50">τ(X=1) = φ(1,0) + φ(1,1) = 0.4 + 0.6 = 1.0</text>
            </svg>
        </div>

        <h2>🔄 Variable Elimination Algorithm</h2>

        <div class="algorithm-box">
            <h3>General Variable Elimination Algorithm</h3>
            <p><strong>Input:</strong> Graphical model as product of factors, elimination ordering $O$</p>
            <p><strong>Output:</strong> Marginal probability</p>

            <p><strong>Algorithm:</strong></p>
            <ol>
                <li><span class="step-number">1</span><strong>For each variable $X_i$ in ordering $O$:</strong></li>
                <li><span class="step-number">2</span><strong>Multiply:</strong> Collect all factors $\Phi_i$ containing $X_i$</li>
                <li><span class="step-number">3</span><strong>Marginalize:</strong> Sum out $X_i$ to obtain new factor $\tau$</li>
                <li><span class="step-number">4</span><strong>Replace:</strong> Replace factors $\Phi_i$ with $\tau$</li>
            </ol>

            <p><strong>Key insight:</strong> This corresponds to "pushing in" summations as far as possible into the product of factors.</p>
        </div>

        <div class="concept-box">
            <p><strong>Elimination Ordering:</strong> The order in which we eliminate variables is crucial!</p>
            <ul>
                <li>Different orderings can dramatically alter running time</li>
                <li>Finding the optimal ordering is NP-hard</li>
                <li>Good heuristics exist for practical problems</li>
            </ul>
        </div>

        <h2>📚 Detailed Example: Student Grade Network</h2>

        <div class="example-box">
            <h4>Student Grade Bayesian Network</h4>
            <p>Consider a network modeling a student's performance:</p>
            <ul>
                <li><strong>I:</strong> Intelligence</li>
                <li><strong>D:</strong> Difficulty of course</li>
                <li><strong>G:</strong> Grade received</li>
                <li><strong>S:</strong> SAT score</li>
                <li><strong>L:</strong> Letter of recommendation</li>
            </ul>

            <div class="formula-box">
                $$p(l,g,i,d,s) = p(l|g) \cdot p(s|i) \cdot p(i) \cdot p(g|i,d) \cdot p(d)$$
            </div>

            <p><strong>Goal:</strong> Compute $p(l)$ using elimination ordering: $d, i, s, g$</p>
        </div>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Student Grade Network: Variable Elimination Steps
                </text>

                <!-- Original network -->
                <text x="150" y="60" text-anchor="middle" font-size="14" fill="#2c3e50" font-weight="bold">
                    Original Network
                </text>

                <!-- Nodes -->
                <circle cx="100" cy="100" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="100" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">I</text>

                <circle cx="200" cy="100" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="200" y="105" text-anchor="middle" font-size="12" fill="white" font-weight="bold">D</text>

                <circle cx="150" cy="160" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="150" y="165" text-anchor="middle" font-size="12" fill="white" font-weight="bold">G</text>

                <circle cx="80" cy="220" r="20" fill="#3498db" stroke="#2c3e50" stroke-width="2"/>
                <text x="80" y="225" text-anchor="middle" font-size="12" fill="white" font-weight="bold">S</text>

                <circle cx="220" cy="220" r="20" fill="#e74c3c" stroke="#2c3e50" stroke-width="2"/>
                <text x="220" y="225" text-anchor="middle" font-size="12" fill="white" font-weight="bold">L</text>

                <!-- Edges -->
                <line x1="115" y1="115" x2="135" y2="145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="185" y1="115" x2="165" y2="145" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="100" y1="120" x2="85" y2="200" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                <line x1="165" y1="175" x2="205" y2="205" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>

                <!-- Step 1: Eliminate D -->
                <text x="400" y="80" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Step 1: Eliminate D
                </text>
                <rect x="320" y="90" width="160" height="40" fill="#fff3cd" stroke="#f39c12" stroke-width="1" rx="3"/>
                <text x="400" y="105" text-anchor="middle" font-size="10" fill="#2c3e50">Factors: p(d), p(g|i,d)</text>
                <text x="400" y="120" text-anchor="middle" font-size="10" fill="#2c3e50">Result: τ₁(g,i) = Σ_d p(g|i,d)p(d)</text>

                <!-- Step 2: Eliminate I -->
                <text x="400" y="150" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Step 2: Eliminate I
                </text>
                <rect x="320" y="160" width="160" height="40" fill="#d1ecf1" stroke="#17a2b8" stroke-width="1" rx="3"/>
                <text x="400" y="175" text-anchor="middle" font-size="10" fill="#2c3e50">Factors: τ₁(g,i), p(i), p(s|i)</text>
                <text x="400" y="190" text-anchor="middle" font-size="10" fill="#2c3e50">Result: τ₂(g,s) = Σ_i τ₁(g,i)p(i)p(s|i)</text>

                <!-- Step 3: Eliminate S -->
                <text x="400" y="220" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Step 3: Eliminate S
                </text>
                <rect x="320" y="230" width="160" height="40" fill="#d4edda" stroke="#28a745" stroke-width="1" rx="3"/>
                <text x="400" y="245" text-anchor="middle" font-size="10" fill="#2c3e50">Factors: τ₂(g,s)</text>
                <text x="400" y="260" text-anchor="middle" font-size="10" fill="#2c3e50">Result: τ₃(g) = Σ_s τ₂(g,s)</text>

                <!-- Step 4: Eliminate G -->
                <text x="400" y="290" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">
                    Step 4: Eliminate G
                </text>
                <rect x="320" y="300" width="160" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="1" rx="3"/>
                <text x="400" y="315" text-anchor="middle" font-size="10" fill="#2c3e50">Factors: τ₃(g), p(l|g)</text>
                <text x="400" y="330" text-anchor="middle" font-size="10" fill="#2c3e50">Result: p(l) = Σ_g τ₃(g)p(l|g)</text>

                <!-- Complexity analysis -->
                <rect x="550" y="90" width="200" height="120" fill="white" stroke="#6c757d" stroke-width="2" rx="5"/>
                <text x="650" y="110" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Complexity Analysis</text>
                <text x="650" y="130" text-anchor="middle" font-size="10" fill="#2c3e50">Each factor ≤ 2 variables</text>
                <text x="650" y="145" text-anchor="middle" font-size="10" fill="#2c3e50">One variable eliminated per step</text>
                <text x="650" y="160" text-anchor="middle" font-size="10" fill="#2c3e50">Max operations: k³ per step</text>
                <text x="650" y="175" text-anchor="middle" font-size="10" fill="#2c3e50">Total: O(nk³) where k ∈ {2,3}</text>
                <text x="650" y="195" text-anchor="middle" font-size="11" fill="#28a745" font-weight="bold">Much better than O(k⁵)!</text>

                <!-- Final result highlight -->
                <rect x="550" y="230" width="200" height="80" fill="#e8f5e8" stroke="#28a745" stroke-width="2" rx="5"/>
                <text x="650" y="250" text-anchor="middle" font-size="12" fill="#2c3e50" font-weight="bold">Final Result</text>
                <text x="650" y="270" text-anchor="middle" font-size="11" fill="#2c3e50">p(l) computed efficiently</text>
                <text x="650" y="285" text-anchor="middle" font-size="11" fill="#2c3e50">by leveraging factorization</text>
                <text x="650" y="300" text-anchor="middle" font-size="11" fill="#2c3e50">and elimination ordering</text>
            </svg>
        </div>

        <div class="highlight">
            <p><strong>Equivalent Summation:</strong> The variable elimination steps correspond to the nested summation:</p>
            <div class="formula-box">
                $$p(l) = \sum_g p(l|g) \sum_s \sum_i p(s|i) p(i) \sum_d p(g|i,d) p(d)$$
            </div>
        </div>

        <h2>🔍 Handling Evidence</h2>

        <div class="concept-box">
            <p><strong>Conditional Probability Queries:</strong> Often we want to compute $P(Y | E = e)$ where:</p>
            <ul>
                <li>$Y$: Query variables</li>
                <li>$E$: Evidence variables (observed)</li>
                <li>$X$: Unobserved variables</li>
            </ul>
        </div>

        <div class="definition-box">
            <h3>Computing Conditional Probabilities</h3>
            <p>To compute $P(Y | E = e)$, we use:</p>
            <div class="formula-box">
                $$P(Y | E = e) = \frac{P(Y, E = e)}{P(E = e)}$$
            </div>

            <p><strong>Algorithm:</strong></p>
            <ol>
                <li>Set evidence variables to their observed values in all relevant factors</li>
                <li>Run variable elimination on $P(Y, E = e)$ to eliminate unobserved variables $X$</li>
                <li>Run variable elimination on $P(E = e)$ to get normalization constant</li>
                <li>Divide: $P(Y | E = e) = \frac{P(Y, E = e)}{P(E = e)}$</li>
            </ol>
        </div>

        <div class="example-box">
            <h4>Evidence Example</h4>
            <p><strong>Query:</strong> $P(\text{Intelligence} | \text{SAT} = \text{high})$</p>

            <p><strong>Step 1:</strong> Set $S = \text{high}$ in factor $p(s|i)$</p>
            <p><strong>Step 2:</strong> Eliminate other variables to get $P(I, S = \text{high})$</p>
            <p><strong>Step 3:</strong> Compute $P(S = \text{high})$ by eliminating all variables</p>
            <p><strong>Step 4:</strong> Normalize to get conditional probability</p>
        </div>

        <h2>⚡ Computational Complexity and Ordering</h2>

        <div class="warning">
            <p><strong>Critical Insight:</strong> The running time of Variable Elimination depends heavily on the elimination ordering!</p>
        </div>

        <div class="concept-box">
            <p><strong>Running Time Formula:</strong></p>
            <div class="formula-box">
                $$\text{Time Complexity} = O(n \cdot k^{M+1})$$
            </div>
            <p>where:</p>
            <ul>
                <li>$n$: number of variables</li>
                <li>$k$: maximum domain size</li>
                <li>$M$: maximum size of any factor formed during elimination</li>
            </ul>
        </div>

        <div class="complexity-grid">
            <div class="complexity-card">
                <div class="card-title">😱 Bad Ordering Example</div>
                <p><strong>Eliminate G first in student network:</strong></p>
                <ul>
                    <li>Must combine: $p(g|i,d)$, $p(l|g)$</li>
                    <li>Creates factor $\tau(d,i,l)$ over 3 variables</li>
                    <li>Requires $O(k^4)$ time per elimination</li>
                    <li>If we had $S \rightarrow G$, would create $\tau(d,i,l,s)$</li>
                    <li>Time becomes $O(k^5)$ - almost as bad as naive!</li>
                </ul>
            </div>

            <div class="complexity-card">
                <div class="card-title">😊 Good Ordering Example</div>
                <p><strong>Topological ordering in student network:</strong></p>
                <ul>
                    <li>Eliminate leaves first: $d, i, s, g$</li>
                    <li>Maximum factor size: 2 variables</li>
                    <li>Each step: $O(k^3)$ time</li>
                    <li>Total: $O(nk^3)$ - much better!</li>
                    <li>Leverages tree-like structure</li>
                </ul>
            </div>
        </div>

        <div class="svg-container">
            <svg width="800" height="300" viewBox="0 0 800 300">
                <!-- Background -->
                <rect width="800" height="300" fill="url(#bgGrad)" rx="10"/>

                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                    Impact of Elimination Ordering
                </text>

                <!-- Bad ordering -->
                <text x="200" y="60" text-anchor="middle" font-size="14" fill="#dc3545" font-weight="bold">
                    Bad Ordering: Eliminate G First
                </text>

                <!-- Network with G highlighted -->
                <circle cx="150" cy="100" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="150" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">I</text>

                <circle cx="250" cy="100" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="250" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">D</text>

                <circle cx="200" cy="140" r="15" fill="#dc3545" stroke="#2c3e50" stroke-width="3"/>
                <text x="200" y="145" text-anchor="middle" font-size="10" fill="white" font-weight="bold">G</text>

                <circle cx="130" cy="180" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="130" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">S</text>

                <circle cx="270" cy="180" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="270" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">L</text>

                <!-- Edges -->
                <line x1="160" y1="110" x2="190" y2="130" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="240" y1="110" x2="210" y2="130" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="150" y1="115" x2="135" y2="165" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="210" y1="150" x2="260" y2="170" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>

                <!-- Factor created -->
                <rect x="120" y="210" width="160" height="40" fill="#f8d7da" stroke="#dc3545" stroke-width="2" rx="3"/>
                <text x="200" y="225" text-anchor="middle" font-size="10" fill="#2c3e50">Creates large factor:</text>
                <text x="200" y="240" text-anchor="middle" font-size="10" fill="#2c3e50">τ(D,I,L) - 3 variables!</text>

                <!-- Good ordering -->
                <text x="600" y="60" text-anchor="middle" font-size="14" fill="#28a745" font-weight="bold">
                    Good Ordering: Topological
                </text>

                <!-- Network with ordering -->
                <circle cx="550" cy="100" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="550" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">I</text>
                <text x="550" y="85" text-anchor="middle" font-size="8" fill="#28a745">②</text>

                <circle cx="650" cy="100" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="650" y="105" text-anchor="middle" font-size="10" fill="white" font-weight="bold">D</text>
                <text x="650" y="85" text-anchor="middle" font-size="8" fill="#28a745">①</text>

                <circle cx="600" cy="140" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="600" y="145" text-anchor="middle" font-size="10" fill="white" font-weight="bold">G</text>
                <text x="600" y="125" text-anchor="middle" font-size="8" fill="#28a745">④</text>

                <circle cx="530" cy="180" r="15" fill="#3498db" stroke="#2c3e50" stroke-width="1"/>
                <text x="530" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">S</text>
                <text x="530" y="165" text-anchor="middle" font-size="8" fill="#28a745">③</text>

                <circle cx="670" cy="180" r="15" fill="#e74c3c" stroke="#2c3e50" stroke-width="1"/>
                <text x="670" y="185" text-anchor="middle" font-size="10" fill="white" font-weight="bold">L</text>

                <!-- Edges -->
                <line x1="560" y1="110" x2="590" y2="130" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="640" y1="110" x2="610" y2="130" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="550" y1="115" x2="535" y2="165" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>
                <line x1="610" y1="150" x2="660" y2="170" stroke="#2c3e50" stroke-width="1" marker-end="url(#arrowhead)"/>

                <!-- Max factor size -->
                <rect x="520" y="210" width="160" height="40" fill="#d4edda" stroke="#28a745" stroke-width="2" rx="3"/>
                <text x="600" y="225" text-anchor="middle" font-size="10" fill="#2c3e50">Max factor size:</text>
                <text x="600" y="240" text-anchor="middle" font-size="10" fill="#2c3e50">2 variables only!</text>
            </svg>
        </div>

        <div class="algorithm-box">
            <h3>Choosing Elimination Orderings</h3>
            <p><strong>Problem:</strong> Finding optimal ordering is NP-hard!</p>

            <p><strong>Practical Heuristics:</strong></p>
            <ol>
                <li><strong>Min-neighbors:</strong> Choose variable with fewest dependent variables</li>
                <li><strong>Min-weight:</strong> Minimize product of cardinalities of dependent variables</li>
                <li><strong>Min-fill:</strong> Minimize size of factor that will be added</li>
            </ol>

            <p><strong>Special Cases:</strong></p>
            <ul>
                <li><strong>Trees:</strong> Any ordering gives $O(nk^2)$ complexity</li>
                <li><strong>Chains:</strong> Natural ordering is optimal</li>
                <li><strong>Dense graphs:</strong> All orderings may be exponential</li>
            </ul>
        </div>

        <h2>📊 Summary and Practical Guidelines</h2>

        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Graph Structure</th>
                    <th>Best Ordering</th>
                    <th>Complexity</th>
                    <th>Example</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Chain</strong></td>
                    <td>Sequential (either direction)</td>
                    <td>$O(nk^2)$</td>
                    <td>HMM, time series</td>
                </tr>
                <tr>
                    <td><strong>Tree</strong></td>
                    <td>From leaves to root</td>
                    <td>$O(nk^2)$</td>
                    <td>Phylogenetic trees</td>
                </tr>
                <tr>
                    <td><strong>Polytree</strong></td>
                    <td>Topological ordering</td>
                    <td>$O(nk^{\text{max degree}})$</td>
                    <td>Bayesian networks</td>
                </tr>
                <tr>
                    <td><strong>Dense/Loopy</strong></td>
                    <td>Heuristic-based</td>
                    <td>Potentially exponential</td>
                    <td>Social networks, grids</td>
                </tr>
            </tbody>
        </table>

        <div class="complexity-grid">
            <div class="complexity-card">
                <div class="card-title">✅ When VE Works Well</div>
                <ul>
                    <li><strong>Tree-structured graphs:</strong> Guaranteed polynomial time</li>
                    <li><strong>Small treewidth:</strong> Bounded factor sizes</li>
                    <li><strong>Sparse networks:</strong> Few dependencies</li>
                    <li><strong>Good domain knowledge:</strong> Natural elimination order</li>
                    <li><strong>Offline computation:</strong> Can precompute optimal ordering</li>
                </ul>
            </div>

            <div class="complexity-card">
                <div class="card-title">⚠️ When VE Struggles</div>
                <ul>
                    <li><strong>Dense graphs:</strong> Large factors created</li>
                    <li><strong>High treewidth:</strong> Exponential complexity</li>
                    <li><strong>Many loops:</strong> Variables become coupled</li>
                    <li><strong>Large domains:</strong> $k$ appears in exponent</li>
                    <li><strong>Online queries:</strong> No time for optimization</li>
                </ul>
            </div>

            <div class="complexity-card">
                <div class="card-title">🔧 Practical Tips</div>
                <ul>
                    <li><strong>Try multiple orderings:</strong> Use heuristics</li>
                    <li><strong>Exploit structure:</strong> Use graph properties</li>
                    <li><strong>Cache factors:</strong> Reuse computations</li>
                    <li><strong>Approximate when needed:</strong> Sampling, variational</li>
                    <li><strong>Preprocess graphs:</strong> Find good orderings offline</li>
                </ul>
            </div>

            <div class="complexity-card">
                <div class="card-title">🎯 Key Applications</div>
                <ul>
                    <li><strong>Medical diagnosis:</strong> Symptom → disease</li>
                    <li><strong>Speech recognition:</strong> Audio → text</li>
                    <li><strong>Computer vision:</strong> Pixels → objects</li>
                    <li><strong>Robotics:</strong> Sensors → world state</li>
                    <li><strong>Finance:</strong> Market data → predictions</li>
                </ul>
            </div>
        </div>

        <div class="highlight">
            <p><strong>Key Takeaways:</strong></p>
            <ul>
                <li><strong>Variable Elimination is exact:</strong> Gives precise answers when tractable</li>
                <li><strong>Ordering matters critically:</strong> Can mean difference between polynomial and exponential</li>
                <li><strong>Exploits factorization:</strong> Much better than naive enumeration</li>
                <li><strong>Foundation for other methods:</strong> Belief propagation, junction trees</li>
                <li><strong>Know when to use:</strong> Great for trees, challenging for dense graphs</li>
            </ul>
        </div>

        <div class="warning">
            <p><strong>Remember:</strong> When Variable Elimination becomes intractable, consider approximate inference methods like sampling (MCMC) or variational inference!</p>
        </div>

    </div>
</body>
</html>
