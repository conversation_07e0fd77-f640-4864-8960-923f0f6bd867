<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bayesian Networks: A Comprehensive Tutorial</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-top: 10px;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-2px);
        }
        
        .section-intro {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }
        
        .section-modeling {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .section-dependencies {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
        
        .section-representation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .section-representation h2 {
            color: white;
            border-bottom: 3px solid rgba(255,255,255,0.7);
        }
        
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        
        .section-representation h3 {
            color: rgba(255,255,255,0.9);
        }
        
        .math-block {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .example-box {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .definition-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .fact-box {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .fact-box::before {
            content: "📝 Fact: ";
            font-weight: bold;
            color: #155724;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .toc {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .toc h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 10px 0;
            padding: 5px 10px;
            background: white;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        
        .toc li:hover {
            background: #e9ecef;
        }
        
        .toc a {
            text-decoration: none;
            color: #495057;
            font-weight: 500;
        }
        
        .learning-objectives {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
        }
        
        .learning-objectives ul {
            margin: 10px 0;
        }
        
        .learning-objectives li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Bayesian Networks</h1>
            <div class="subtitle">A Comprehensive Tutorial on Probabilistic Graphical Models</div>
        </div>
        
        <div class="toc">
            <h3>📚 Table of Contents</h3>
            <ul>
                <li><a href="#introduction">1. Introduction to Bayesian Networks</a></li>
                <li><a href="#modeling">2. Probabilistic Modeling with Bayesian Networks</a></li>
                <li><a href="#dependencies">3. The Dependencies of a Bayes Net</a></li>
                <li><a href="#representation">4. The Representational Power of Directed Graphs</a></li>
            </ul>
        </div>
        
        <div class="section section-intro" id="introduction">
            <h2>1. Introduction to Bayesian Networks</h2>
            
            <p>We begin with a fundamental question in probabilistic modeling: <strong>how do we choose a probability distribution to model some interesting aspect of the world?</strong> Coming up with a good model is not always easy. Consider, for example, a naive model for spam classification that would require us to specify a number of parameters that is exponential in the number of words in the English language!</p>
            
            <div class="learning-objectives">
                <h3>🎯 Learning Objectives</h3>
                <p>In this tutorial, we will learn about one way to avoid these kinds of complications. We are going to:</p>
                <ul>
                    <li>Learn an effective and general technique for parameterizing probability distributions using only a few parameters</li>
                    <li>See how the resulting models can be elegantly described via directed acyclic graphs (DAGs)</li>
                    <li>Study connections between the structure of a DAG and the modeling assumptions made by the distribution that it describes</li>
                    <li>Understand how these insights help us design more efficient inference algorithms</li>
                </ul>
            </div>
            
            <p>The kinds of models that we will see here are referred to as <strong>Bayesian networks</strong>. In contrast to undirected graphs (Markov random fields or MRFs), Bayesian networks effectively show causality, whereas MRFs cannot. Thus, MRFs are preferable for problems where there is no clear causality between random variables.</p>
            
            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e3f2fd"/>
                            <stop offset="100%" style="stop-color:#bbdefb"/>
                        </linearGradient>
                    </defs>
                    <rect width="600" height="300" fill="url(#bgGrad)" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#1565c0">
                        Bayesian Networks vs. Naive Approach
                    </text>
                    
                    <!-- Naive Approach -->
                    <g>
                        <text x="150" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#d32f2f">
                            Naive Approach
                        </text>
                        <rect x="50" y="70" width="200" height="80" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
                        <text x="150" y="90" text-anchor="middle" font-size="12" fill="#d32f2f">
                            Parameters needed:
                        </text>
                        <text x="150" y="110" text-anchor="middle" font-size="14" font-weight="bold" fill="#d32f2f">
                            O(d^n)
                        </text>
                        <text x="150" y="130" text-anchor="middle" font-size="10" fill="#666">
                            Exponential in variables!
                        </text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M270 110 Q300 110 330 110" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                        </marker>
                    </defs>
                    
                    <!-- Bayesian Networks -->
                    <g>
                        <text x="450" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">
                            Bayesian Networks
                        </text>
                        <rect x="350" y="70" width="200" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="450" y="90" text-anchor="middle" font-size="12" fill="#388e3c">
                            Parameters needed:
                        </text>
                        <text x="450" y="110" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">
                            O(n·d^(k+1))
                        </text>
                        <text x="450" y="130" text-anchor="middle" font-size="10" fill="#666">
                            Linear in variables!
                        </text>
                    </g>
                    
                    <!-- Benefits -->
                    <text x="300" y="180" text-anchor="middle" font-size="14" font-weight="bold" fill="#1565c0">
                        Key Benefits of Bayesian Networks
                    </text>
                    
                    <g transform="translate(50, 200)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Compact parameterization</text>
                    </g>
                    
                    <g transform="translate(250, 200)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Clear causal relationships</text>
                    </g>
                    
                    <g transform="translate(50, 240)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Efficient inference algorithms</text>
                    </g>
                    
                    <g transform="translate(250, 240)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Explicit modeling assumptions</text>
                    </g>
                </svg>
            </div>
        </div>
        
        <div class="section section-modeling" id="modeling">
            <h2>2. Probabilistic Modeling with Bayesian Networks</h2>
            
            <p><strong>Directed graphical models</strong> (a.k.a. Bayesian networks) are a family of probability distributions that admit a compact parametrization that can be naturally described using a directed graph.</p>
            
            <h3>The Chain Rule Foundation</h3>
            <p>The general idea behind this parametrization is surprisingly simple. Recall that by the chain rule, we can write any probability $p$ as:</p>
            
            <div class="math-block">
                $$p(x_1, x_2, \ldots, x_n) = p(x_1)p(x_2 \mid x_1) \cdots p(x_n \mid x_{n-1}, \ldots, x_2, x_1)$$
            </div>
            
            <p>A compact Bayesian network is a distribution in which each factor on the right hand side depends only on a small number of ancestor variables $x_{A_i}$:</p>
            
            <div class="math-block">
                $$p(x_i \mid x_{i-1}, \ldots, x_1) = p(x_i \mid x_{A_i})$$
            </div>
            
            <div class="example-box">
                <h4>💡 Example</h4>
                <p>In a model with five variables, we may choose to approximate the factor $p(x_5 \mid x_4, x_3, x_2, x_1)$ with $p(x_5 \mid x_4, x_3)$. In this case, we write $x_{A_5} = \{x_4, x_3\}$.</p>
            </div>
            
            <h3>Computational Efficiency</h3>
            <p>When the variables are discrete, we may think of the factors $p(x_i \mid x_{A_i})$ as <strong>probability tables</strong>, where:</p>
            <ul>
                <li>Rows correspond to assignments to $x_{A_i}$</li>
                <li>Columns correspond to values of $x_i$</li>
                <li>Entries contain the actual probabilities $p(x_i \mid x_{A_i})$</li>
            </ul>
            
            <div class="fact-box">
                If each variable takes $d$ values and has at most $k$ ancestors, then the entire table will contain at most $O(d^{k+1})$ entries. Since we have one table per variable, the entire probability distribution can be compactly described with only $O(nd^{k+1})$ parameters (compared to $O(d^n)$ with a naive approach).
            </div>
            
            <h3>Student Grade Example</h3>
            <p>Consider a model of a student's grade $g$ on an exam. This grade depends on:</p>
            <ul>
                <li>The exam's difficulty $d$</li>
                <li>The student's intelligence $i$</li>
                <li>It also affects the quality $l$ of the reference letter</li>
                <li>The student's intelligence $i$ affects the SAT score $s$ as well</li>
            </ul>
            
            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Student Performance Bayesian Network
                    </text>
                    
                    <!-- Nodes -->
                    <!-- Intelligence -->
                    <circle cx="150" cy="100" r="25" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                    <text x="150" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">i</text>
                    <text x="150" y="135" text-anchor="middle" font-size="10" fill="#666">Intelligence</text>
                    
                    <!-- Difficulty -->
                    <circle cx="350" cy="100" r="25" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
                    <text x="350" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">d</text>
                    <text x="350" y="135" text-anchor="middle" font-size="10" fill="#666">Difficulty</text>
                    
                    <!-- Grade -->
                    <circle cx="250" cy="200" r="25" fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
                    <text x="250" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#388e3c">g</text>
                    <text x="250" y="235" text-anchor="middle" font-size="10" fill="#666">Grade</text>
                    
                    <!-- Letter -->
                    <circle cx="250" cy="300" r="25" fill="#fce4ec" stroke="#c2185b" stroke-width="2"/>
                    <text x="250" y="305" text-anchor="middle" font-size="12" font-weight="bold" fill="#c2185b">l</text>
                    <text x="250" y="335" text-anchor="middle" font-size="10" fill="#666">Letter</text>
                    
                    <!-- SAT -->
                    <circle cx="100" cy="200" r="25" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
                    <text x="100" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#7b1fa2">s</text>
                    <text x="100" y="235" text-anchor="middle" font-size="10" fill="#666">SAT Score</text>
                    
                    <!-- Edges -->
                    <!-- i -> g -->
                    <path d="M170 120 L230 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <!-- d -> g -->
                    <path d="M330 120 L270 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <!-- g -> l -->
                    <path d="M250 225 L250 275" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <!-- i -> s -->
                    <path d="M135 115 L115 185" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    
                    <defs>
                        <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                    
                    <!-- Factorization -->
                    <text x="450" y="150" font-size="12" font-weight="bold" fill="#2c3e50">Factorization:</text>
                    <text x="450" y="170" font-size="11" fill="#333">p(l,g,i,d,s) =</text>
                    <text x="450" y="185" font-size="11" fill="#333">p(l|g) ×</text>
                    <text x="450" y="200" font-size="11" fill="#333">p(g|i,d) ×</text>
                    <text x="450" y="215" font-size="11" fill="#333">p(i) ×</text>
                    <text x="450" y="230" font-size="11" fill="#333">p(d) ×</text>
                    <text x="450" y="245" font-size="11" fill="#333">p(s|i)</text>
                </svg>
            </div>
            
            <p>The joint probability distribution over the 5 variables naturally factorizes as follows:</p>
            
            <div class="math-block">
                $$p(l, g, i, d, s) = p(l \mid g)p(g \mid i, d)p(i)p(d)p(s \mid i)$$
            </div>
            
            <div class="definition-box">
                <h4>📋 Formal Definition</h4>
                <p>Formally, a Bayesian network is a directed graph $G = (V, E)$ together with:</p>
                <ul>
                    <li>A random variable $x_i$ for each node $i \in V$</li>
                    <li>One conditional probability distribution (CPD) $p(x_i \mid x_{A_i})$ per node, specifying the probability of $x_i$ conditioned on its parents' values</li>
                </ul>
                <p>Thus, a Bayesian network defines a probability distribution $p$. Conversely, we say that a probability $p$ <strong>factorizes over</strong> a DAG $G$ if it can be decomposed into a product of factors, as specified by $G$.</p>
            </div>
            
            <h3>Generative Story Interpretation</h3>
            <p>Another way to interpret directed graphs is in terms of <strong>stories for how the data was generated</strong>. In the above example:</p>
            <ol>
                <li>First, we sample an intelligence level and an exam difficulty</li>
                <li>Then, a student's grade is sampled given these parameters</li>
                <li>Finally, the recommendation letter is generated based on that grade</li>
                <li>Separately, the SAT score is generated based on intelligence</li>
            </ol>
            
            <p>Similarly, in spam classification, we implicitly postulated that email is generated according to a two-step process: first, we choose a spam/non-spam label $y$; then we sample independently whether each word is present, conditioned on that label.</p>
        </div>
        
    </div>
</body>
</html> 