<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bayesian Networks: A Comprehensive Tutorial</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px 10px 0 0;
            margin: -20px -20px 30px -20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-top: 10px;
            opacity: 0.9;
        }
        
        .section {
            margin: 40px 0;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-2px);
        }
        
        .section-intro {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }
        
        .section-modeling {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .section-dependencies {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
        
        .section-representation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .section-representation h2 {
            color: white;
            border-bottom: 3px solid rgba(255,255,255,0.7);
        }
        
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        
        .section-representation h3 {
            color: rgba(255,255,255,0.9);
        }
        
        .math-block {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .example-box {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .definition-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .fact-box {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .fact-box::before {
            content: "📝 Fact: ";
            font-weight: bold;
            color: #155724;
        }
        
        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        svg {
            max-width: 100%;
            height: auto;
        }
        
        .toc {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .toc h3 {
            color: #495057;
            margin-top: 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 10px 0;
            padding: 5px 10px;
            background: white;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        
        .toc li:hover {
            background: #e9ecef;
        }
        
        .toc a {
            text-decoration: none;
            color: #495057;
            font-weight: 500;
        }
        
        .learning-objectives {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
        }
        
        .learning-objectives ul {
            margin: 10px 0;
        }
        
        .learning-objectives li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Bayesian Networks</h1>
            <div class="subtitle">A Comprehensive Tutorial on Probabilistic Graphical Models</div>
        </div>
        
        <div class="toc">
            <h3>📚 Table of Contents</h3>
            <ul>
                <li><a href="#introduction">1. Introduction to Bayesian Networks</a></li>
                <li><a href="#modeling">2. Probabilistic Modeling with Bayesian Networks</a></li>
                <li><a href="#dependencies">3. The Dependencies of a Bayes Net</a></li>
                <li><a href="#representation">4. The Representational Power of Directed Graphs</a></li>
            </ul>
        </div>
        
        <div class="section section-intro" id="introduction">
            <h2>1. Introduction to Bayesian Networks</h2>
            
            <p>We begin with a fundamental question in probabilistic modeling: <strong>how do we choose a probability distribution to model some interesting aspect of the world?</strong> Coming up with a good model is not always easy. Consider, for example, a naive model for spam classification that would require us to specify a number of parameters that is exponential in the number of words in the English language!</p>
            
            <div class="learning-objectives">
                <h3>🎯 Learning Objectives</h3>
                <p>In this tutorial, we will learn about one way to avoid these kinds of complications. We are going to:</p>
                <ul>
                    <li>Learn an effective and general technique for parameterizing probability distributions using only a few parameters</li>
                    <li>See how the resulting models can be elegantly described via directed acyclic graphs (DAGs)</li>
                    <li>Study connections between the structure of a DAG and the modeling assumptions made by the distribution that it describes</li>
                    <li>Understand how these insights help us design more efficient inference algorithms</li>
                </ul>
            </div>
            
            <p>The kinds of models that we will see here are referred to as <strong>Bayesian networks</strong>. In contrast to undirected graphs (Markov random fields or MRFs), Bayesian networks effectively show causality, whereas MRFs cannot. Thus, MRFs are preferable for problems where there is no clear causality between random variables.</p>
            
            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <defs>
                        <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e3f2fd"/>
                            <stop offset="100%" style="stop-color:#bbdefb"/>
                        </linearGradient>
                    </defs>
                    <rect width="600" height="300" fill="url(#bgGrad)" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#1565c0">
                        Bayesian Networks vs. Naive Approach
                    </text>
                    
                    <!-- Naive Approach -->
                    <g>
                        <text x="150" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#d32f2f">
                            Naive Approach
                        </text>
                        <rect x="50" y="70" width="200" height="80" fill="#ffebee" stroke="#f44336" stroke-width="2" rx="5"/>
                        <text x="150" y="90" text-anchor="middle" font-size="12" fill="#d32f2f">
                            Parameters needed:
                        </text>
                        <text x="150" y="110" text-anchor="middle" font-size="14" font-weight="bold" fill="#d32f2f">
                            O(d^n)
                        </text>
                        <text x="150" y="130" text-anchor="middle" font-size="10" fill="#666">
                            Exponential in variables!
                        </text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M270 110 Q300 110 330 110" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                        </marker>
                    </defs>
                    
                    <!-- Bayesian Networks -->
                    <g>
                        <text x="450" y="60" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">
                            Bayesian Networks
                        </text>
                        <rect x="350" y="70" width="200" height="80" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                        <text x="450" y="90" text-anchor="middle" font-size="12" fill="#388e3c">
                            Parameters needed:
                        </text>
                        <text x="450" y="110" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">
                            O(n·d^(k+1))
                        </text>
                        <text x="450" y="130" text-anchor="middle" font-size="10" fill="#666">
                            Linear in variables!
                        </text>
                    </g>
                    
                    <!-- Benefits -->
                    <text x="300" y="180" text-anchor="middle" font-size="14" font-weight="bold" fill="#1565c0">
                        Key Benefits of Bayesian Networks
                    </text>
                    
                    <g transform="translate(50, 200)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Compact parameterization</text>
                    </g>
                    
                    <g transform="translate(250, 200)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Clear causal relationships</text>
                    </g>
                    
                    <g transform="translate(50, 240)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Efficient inference algorithms</text>
                    </g>
                    
                    <g transform="translate(250, 240)">
                        <circle cx="20" cy="20" r="5" fill="#4caf50"/>
                        <text x="35" y="25" font-size="12" fill="#333">Explicit modeling assumptions</text>
                    </g>
                </svg>
            </div>
        </div>
        
        <div class="section section-modeling" id="modeling">
            <h2>2. Probabilistic Modeling with Bayesian Networks</h2>
            
            <p><strong>Directed graphical models</strong> (a.k.a. Bayesian networks) are a family of probability distributions that admit a compact parametrization that can be naturally described using a directed graph.</p>
            
            <h3>The Chain Rule Foundation</h3>
            <p>The general idea behind this parametrization is surprisingly simple. Recall that by the chain rule, we can write any probability $p$ as:</p>
            
            <div class="math-block">
                $$p(x_1, x_2, \ldots, x_n) = p(x_1)p(x_2 \mid x_1) \cdots p(x_n \mid x_{n-1}, \ldots, x_2, x_1)$$
            </div>
            
            <p>A compact Bayesian network is a distribution in which each factor on the right hand side depends only on a small number of ancestor variables $x_{A_i}$:</p>
            
            <div class="math-block">
                $$p(x_i \mid x_{i-1}, \ldots, x_1) = p(x_i \mid x_{A_i})$$
            </div>
            
            <div class="example-box">
                <h4>💡 Example</h4>
                <p>In a model with five variables, we may choose to approximate the factor $p(x_5 \mid x_4, x_3, x_2, x_1)$ with $p(x_5 \mid x_4, x_3)$. In this case, we write $x_{A_5} = \{x_4, x_3\}$.</p>
            </div>
            
            <h3>Computational Efficiency</h3>
            <p>When the variables are discrete, we may think of the factors $p(x_i \mid x_{A_i})$ as <strong>probability tables</strong>, where:</p>
            <ul>
                <li>Rows correspond to assignments to $x_{A_i}$</li>
                <li>Columns correspond to values of $x_i$</li>
                <li>Entries contain the actual probabilities $p(x_i \mid x_{A_i})$</li>
            </ul>
            
            <div class="fact-box">
                If each variable takes $d$ values and has at most $k$ ancestors, then the entire table will contain at most $O(d^{k+1})$ entries. Since we have one table per variable, the entire probability distribution can be compactly described with only $O(nd^{k+1})$ parameters (compared to $O(d^n)$ with a naive approach).
            </div>
            
            <h3>Student Grade Example</h3>
            <p>Consider a model of a student's grade $g$ on an exam. This grade depends on:</p>
            <ul>
                <li>The exam's difficulty $d$</li>
                <li>The student's intelligence $i$</li>
                <li>It also affects the quality $l$ of the reference letter</li>
                <li>The student's intelligence $i$ affects the SAT score $s$ as well</li>
            </ul>
            
            <div class="svg-container">
                <svg width="600" height="400" viewBox="0 0 600 400">
                    <!-- Background -->
                    <rect width="600" height="400" fill="#f8f9fa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        Student Performance Bayesian Network
                    </text>
                    
                    <!-- Nodes -->
                    <!-- Intelligence -->
                    <circle cx="150" cy="100" r="25" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
                    <text x="150" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">i</text>
                    <text x="150" y="135" text-anchor="middle" font-size="10" fill="#666">Intelligence</text>
                    
                    <!-- Difficulty -->
                    <circle cx="350" cy="100" r="25" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
                    <text x="350" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">d</text>
                    <text x="350" y="135" text-anchor="middle" font-size="10" fill="#666">Difficulty</text>
                    
                    <!-- Grade -->
                    <circle cx="250" cy="200" r="25" fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
                    <text x="250" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#388e3c">g</text>
                    <text x="250" y="235" text-anchor="middle" font-size="10" fill="#666">Grade</text>
                    
                    <!-- Letter -->
                    <circle cx="250" cy="300" r="25" fill="#fce4ec" stroke="#c2185b" stroke-width="2"/>
                    <text x="250" y="305" text-anchor="middle" font-size="12" font-weight="bold" fill="#c2185b">l</text>
                    <text x="250" y="335" text-anchor="middle" font-size="10" fill="#666">Letter</text>
                    
                    <!-- SAT -->
                    <circle cx="100" cy="200" r="25" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
                    <text x="100" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#7b1fa2">s</text>
                    <text x="100" y="235" text-anchor="middle" font-size="10" fill="#666">SAT Score</text>
                    
                    <!-- Edges -->
                    <!-- i -> g -->
                    <path d="M170 120 L230 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <!-- d -> g -->
                    <path d="M330 120 L270 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <!-- g -> l -->
                    <path d="M250 225 L250 275" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    <!-- i -> s -->
                    <path d="M135 115 L115 185" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead2)"/>
                    
                    <defs>
                        <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                    
                    <!-- Factorization -->
                    <text x="450" y="150" font-size="12" font-weight="bold" fill="#2c3e50">Factorization:</text>
                    <text x="450" y="170" font-size="11" fill="#333">p(l,g,i,d,s) =</text>
                    <text x="450" y="185" font-size="11" fill="#333">p(l|g) ×</text>
                    <text x="450" y="200" font-size="11" fill="#333">p(g|i,d) ×</text>
                    <text x="450" y="215" font-size="11" fill="#333">p(i) ×</text>
                    <text x="450" y="230" font-size="11" fill="#333">p(d) ×</text>
                    <text x="450" y="245" font-size="11" fill="#333">p(s|i)</text>
                </svg>
            </div>
            
            <p>The joint probability distribution over the 5 variables naturally factorizes as follows:</p>
            
            <div class="math-block">
                $$p(l, g, i, d, s) = p(l \mid g)p(g \mid i, d)p(i)p(d)p(s \mid i)$$
            </div>
            
            <div class="definition-box">
                <h4>📋 Formal Definition</h4>
                <p>Formally, a Bayesian network is a directed graph $G = (V, E)$ together with:</p>
                <ul>
                    <li>A random variable $x_i$ for each node $i \in V$</li>
                    <li>One conditional probability distribution (CPD) $p(x_i \mid x_{A_i})$ per node, specifying the probability of $x_i$ conditioned on its parents' values</li>
                </ul>
                <p>Thus, a Bayesian network defines a probability distribution $p$. Conversely, we say that a probability $p$ <strong>factorizes over</strong> a DAG $G$ if it can be decomposed into a product of factors, as specified by $G$.</p>
            </div>
            
            <h3>Generative Story Interpretation</h3>
            <p>Another way to interpret directed graphs is in terms of <strong>stories for how the data was generated</strong>. In the above example:</p>
            <ol>
                <li>First, we sample an intelligence level and an exam difficulty</li>
                <li>Then, a student's grade is sampled given these parameters</li>
                <li>Finally, the recommendation letter is generated based on that grade</li>
                <li>Separately, the SAT score is generated based on intelligence</li>
            </ol>
            
            <p>Similarly, in spam classification, we implicitly postulated that email is generated according to a two-step process: first, we choose a spam/non-spam label $y$; then we sample independently whether each word is present, conditioned on that label.</p>
        </div>
        
        <div class="section section-dependencies" id="dependencies">
            <h2>3. The Dependencies of a Bayes Net</h2>
            
            <p>Bayesian networks represent probability distributions that can be formed via products of smaller, local conditional probability distributions (one for each variable). By expressing a probability in this form, we are introducing into our model assumptions that certain variables are <strong>independent</strong>.</p>
            
            <p>This raises the question: <em>which independence assumptions are we exactly making by using a Bayesian network model with a given structure described by $G$?</em> This question is important for two reasons:</p>
            <ul>
                <li>We should know precisely what model assumptions we are making (and whether they are correct)</li>
                <li>This information will help us design more efficient inference algorithms later on</li>
            </ul>
            
            <div class="definition-box">
                <h4>📋 Independence Notation</h4>
                <p>Let us use the notation $I(p)$ to denote the set of all independencies that hold for a joint distribution $p$. For example, if $p(x,y) = p(x)p(y)$, then we say that $x \perp y \in I(p)$.</p>
            </div>
            
            <h3>Three Fundamental Structures</h3>
            <p>A Bayesian network $p$ very elegantly describes many independencies in $I(p)$; these independencies can be recovered from the graph by looking at <strong>three types of structures</strong>.</p>
            
            <p>For simplicity, let's start by looking at a Bayes net $G$ with three nodes: $X$, $Y$, and $Z$. In this case, $G$ essentially has only three possible structures, each of which leads to different independence assumptions.</p>
            
            <div class="svg-container">
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="#f8f9fa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="18" font-weight="bold" fill="#2c3e50">
                        Three Fundamental Structures in Bayesian Networks
                    </text>
                    
                    <!-- Cascade Structure -->
                    <g transform="translate(50, 80)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#e91e63">
                            1. Cascade Structure
                        </text>
                        <text x="100" y="35" text-anchor="middle" font-size="12" fill="#666">
                            X → Z → Y
                        </text>
                        
                        <!-- Nodes -->
                        <circle cx="30" cy="80" r="20" fill="#ffebee" stroke="#e91e63" stroke-width="2"/>
                        <text x="30" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">X</text>
                        
                        <circle cx="100" cy="80" r="20" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                        <text x="100" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">Z</text>
                        
                        <circle cx="170" cy="80" r="20" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                        <text x="170" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#2196f3">Y</text>
                        
                        <!-- Arrows -->
                        <path d="M50 80 L80 80" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
                        <path d="M120 80 L150 80" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
                        
                        <!-- Independence info -->
                        <text x="100" y="120" text-anchor="middle" font-size="11" fill="#333">
                            If Z observed: X ⊥ Y | Z
                        </text>
                        <text x="100" y="135" text-anchor="middle" font-size="11" fill="#333">
                            If Z unobserved: X ⊄ Y
                        </text>
                    </g>
                    
                    <!-- Common Parent Structure -->
                    <g transform="translate(300, 80)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#ff9800">
                            2. Common Parent
                        </text>
                        <text x="100" y="35" text-anchor="middle" font-size="12" fill="#666">
                            X ← Z → Y
                        </text>
                        
                        <!-- Nodes -->
                        <circle cx="30" cy="100" r="20" fill="#ffebee" stroke="#e91e63" stroke-width="2"/>
                        <text x="30" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">X</text>
                        
                        <circle cx="100" cy="60" r="20" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                        <text x="100" y="65" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">Z</text>
                        
                        <circle cx="170" cy="100" r="20" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                        <text x="170" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#2196f3">Y</text>
                        
                        <!-- Arrows -->
                        <path d="M85 75 L45 90" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
                        <path d="M115 75 L155 90" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
                        
                        <!-- Independence info -->
                        <text x="100" y="140" text-anchor="middle" font-size="11" fill="#333">
                            If Z observed: X ⊥ Y | Z
                        </text>
                        <text x="100" y="155" text-anchor="middle" font-size="11" fill="#333">
                            If Z unobserved: X ⊄ Y
                        </text>
                    </g>
                    
                    <!-- V-Structure -->
                    <g transform="translate(550, 80)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#9c27b0">
                            3. V-Structure
                        </text>
                        <text x="100" y="35" text-anchor="middle" font-size="12" fill="#666">
                            X → Z ← Y
                        </text>
                        
                        <!-- Nodes -->
                        <circle cx="30" cy="80" r="20" fill="#ffebee" stroke="#e91e63" stroke-width="2"/>
                        <text x="30" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">X</text>
                        
                        <circle cx="100" cy="120" r="20" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                        <text x="100" y="125" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">Z</text>
                        
                        <circle cx="170" cy="80" r="20" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                        <text x="170" y="85" text-anchor="middle" font-size="12" font-weight="bold" fill="#2196f3">Y</text>
                        
                        <!-- Arrows -->
                        <path d="M45 90 L85 110" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
                        <path d="M155 90 L115 110" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead3)"/>
                        
                        <!-- Independence info -->
                        <text x="100" y="160" text-anchor="middle" font-size="11" fill="#333">
                            If Z observed: X ⊄ Y | Z
                        </text>
                        <text x="100" y="175" text-anchor="middle" font-size="11" fill="#333">
                            If Z unobserved: X ⊥ Y
                        </text>
                    </g>
                    
                    <defs>
                        <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                    
                    <!-- Explanation -->
                    <text x="400" y="230" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                        Key Insight: V-Structure is Special!
                    </text>
                    <text x="400" y="250" text-anchor="middle" font-size="12" fill="#666">
                        In cascade and common parent: observing Z blocks the path between X and Y
                    </text>
                    <text x="400" y="265" text-anchor="middle" font-size="12" fill="#666">
                        In V-structure: observing Z activates the path between X and Y (explaining away)
                    </text>
                </svg>
            </div>
            
            <div class="example-box">
                <h4>🌧️ The Sprinkler Example (V-Structure)</h4>
                <p>Suppose that $Z$ is a Boolean variable that indicates whether our lawn is wet one morning; $X$ and $Y$ are two explanations for it being wet: either it rained (indicated by $X$), or the sprinkler turned on (indicated by $Y$).</p>
                <p>If we know that the grass is wet ($Z$ is true) and the sprinkler didn't go on ($Y$ is false), then the probability that $X$ is true must be one, because that is the only other possible explanation. Hence, $X$ and $Y$ are not independent given $Z$.</p>
            </div>
            
            <h3>d-Separation: The General Theory</h3>
            <p>These structures clearly describe the independencies encoded by a three-variable Bayesian net. We can extend them to general networks by applying them recursively over any larger graph. This leads to a notion called <strong>$d$-separation</strong> (where $d$ stands for directed).</p>
            
            <div class="definition-box">
                <h4>📋 d-Separation Definition</h4>
                <p>Let $Q$, $W$, and $O$ be three sets of nodes in a Bayesian Network $G$. We say that $Q$ and $W$ are <strong>$d$-separated given $O$</strong> (i.e. the variables $O$ are observed) if $Q$ and $W$ are not connected by an active path.</p>
                
                <p>An undirected path in $G$ is called <strong>active given observed variables $O$</strong> if for every consecutive triple of variables $X,Y,Z$ on the path, one of the following holds:</p>
                <ul>
                    <li>$X \leftarrow Y \leftarrow Z$, and $Y$ is unobserved ($Y \notin O$)</li>
                    <li>$X \rightarrow Y \rightarrow Z$, and $Y$ is unobserved ($Y \notin O$)</li>
                    <li>$X \leftarrow Y \rightarrow Z$, and $Y$ is unobserved ($Y \notin O$)</li>
                    <li>$X \rightarrow Y \leftarrow Z$, and $Y$ or any of its descendants are observed</li>
                </ul>
            </div>
            
            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="#f8f9fa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">
                        d-Separation Example
                    </text>
                    
                    <!-- Nodes -->
                    <circle cx="100" cy="100" r="20" fill="#ffebee" stroke="#e91e63" stroke-width="2"/>
                    <text x="100" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#e91e63">X₁</text>
                    
                    <circle cx="250" cy="100" r="20" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                    <text x="250" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#4caf50">X₂</text>
                    
                    <circle cx="400" cy="100" r="20" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                    <text x="400" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#2196f3">X₃</text>
                    
                    <circle cx="250" cy="200" r="20" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                    <text x="250" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#ff9800">X₄</text>
                    
                    <circle cx="400" cy="200" r="20" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                    <text x="400" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#9c27b0">X₅</text>
                    
                    <circle cx="550" cy="150" r="20" fill="#e0f2f1" stroke="#009688" stroke-width="2"/>
                    <text x="550" y="155" text-anchor="middle" font-size="12" font-weight="bold" fill="#009688">X₆</text>
                    
                    <!-- Edges -->
                    <path d="M120 100 L230 100" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead4)"/>
                    <path d="M270 100 L380 100" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead4)"/>
                    <path d="M250 120 L250 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead4)"/>
                    <path d="M400 120 L400 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead4)"/>
                    <path d="M420 190 L530 160" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead4)"/>
                    <path d="M420 110 L530 140" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead4)"/>
                    
                    <defs>
                        <marker id="arrowhead4" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
                        </marker>
                    </defs>
                    
                    <!-- Observations -->
                    <text x="350" y="280" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                        Case 1: Observing {X₂, X₃}
                    </text>
                    <text x="350" y="300" text-anchor="middle" font-size="12" fill="#333">
                        X₁ and X₆ are d-separated given {X₂, X₃}
                    </text>
                    <text x="350" y="315" text-anchor="middle" font-size="11" fill="#666">
                        Path X₁ → X₂ → X₃ → X₆ is blocked at X₂ and X₃
                    </text>
                    
                    <text x="350" y="345" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">
                        Case 2: Observing {X₁, X₆}
                    </text>
                    <text x="350" y="365" text-anchor="middle" font-size="12" fill="#333">
                        X₂ and X₃ are NOT d-separated given {X₁, X₆}
                    </text>
                    <text x="350" y="380" text-anchor="middle" font-size="11" fill="#666">
                        Active path: X₂ → X₃ → X₆ (V-structure at X₆ is activated)
                    </text>
                </svg>
            </div>
            
            <div class="fact-box">
                If $p$ factorizes over $G$, then $I(G) \subseteq I(p)$. In this case, we say that $G$ is an <strong>$I$-map (independence map)</strong> for $p$.
                
                <p style="margin-top: 10px;">In other words, all the independencies encoded in $G$ are sound: variables that are $d$-separated in $G$ are truly independent in $p$. However, the converse is not true: a distribution may factorize over $G$, yet have independencies that are not captured in $G$.</p>
            </div>
        </div>
        
        <div class="section section-representation" id="representation">
            <h2>4. The Representational Power of Directed Graphs</h2>
            
            <p>This raises our last and perhaps most important question: <strong>can directed graphs express all the independencies of any distribution $p$?</strong> More formally, given a distribution $p$, can we construct a graph $G$ such that $I(G) = I(p)$?</p>
            
            <h3>I-maps: Always Possible</h3>
            <p>First, note that it is easy to construct a $G$ such that $I(G) \subseteq I(p)$. A <strong>fully connected DAG</strong> $G$ is an $I$-map for any distribution since $I(G) = \emptyset$.</p>
            
            <div class="svg-container">
                <svg width="600" height="300" viewBox="0 0 600 300">
                    <!-- Background -->
                    <rect width="600" height="300" fill="#f8f9fa" rx="10"/>
                    
                    <!-- Title -->
                    <text x="300" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        Fully Connected Bayesian Network
                    </text>
                    <text x="300" y="50" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.8)">
                        No independencies: I-map for any distribution
                    </text>
                    
                    <!-- Nodes -->
                    <circle cx="150" cy="100" r="25" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
                    <text x="150" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">X₁</text>
                    
                    <circle cx="450" cy="100" r="25" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
                    <text x="450" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">X₂</text>
                    
                    <circle cx="150" cy="200" r="25" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
                    <text x="150" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">X₃</text>
                    
                    <circle cx="450" cy="200" r="25" fill="rgba(255,255,255,0.9)" stroke="white" stroke-width="2"/>
                    <text x="450" y="205" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">X₄</text>
                    
                    <!-- All possible edges -->
                    <path d="M175 100 L425 100" stroke="white" stroke-width="2" fill="none" marker-end="url(#arrowhead5)"/>
                    <path d="M175 115 L425 185" stroke="white" stroke-width="2" fill="none" marker-end="url(#arrowhead5)"/>
                    <path d="M150 125 L150 175" stroke="white" stroke-width="2" fill="none" marker-end="url(#arrowhead5)"/>
                    <path d="M175 185 L425 115" stroke="white" stroke-width="2" fill="none" marker-end="url(#arrowhead5)"/>
                    <path d="M175 200 L425 200" stroke="white" stroke-width="2" fill="none" marker-end="url(#arrowhead5)"/>
                    <path d="M450 125 L450 175" stroke="white" stroke-width="2" fill="none" marker-end="url(#arrowhead5)"/>
                    
                    <defs>
                        <marker id="arrowhead5" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="white"/>
                        </marker>
                    </defs>
                    
                    <text x="300" y="260" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.9)">
                        Every variable depends on all previous variables in topological order
                    </text>
                </svg>
            </div>
            
            <h3>Minimal I-maps</h3>
            <p>A more interesting question is whether we can find a <strong>minimal $I$-map</strong> $G$ for $p$, i.e. an $I$-map $G$ such that the removal of even a single edge from $G$ will result in it no longer being an $I$-map.</p>
            
            <div class="example-box">
                <h4>💡 Finding Minimal I-maps</h4>
                <p>This is quite easy: we may start with a fully connected $G$ and remove edges until $G$ is no longer an $I$-map. One way to do this is by following the natural topological ordering of the graph, and removing node ancestors until this is no longer possible.</p>
            </div>
            
            <h3>Perfect Maps: Not Always Possible</h3>
            <p>However, what we are truly interested in determining is whether any probability distribution $p$ always admits a <strong>perfect map</strong> $G$ for which $I(p) = I(G)$. Unfortunately, the answer is <strong>no</strong>.</p>
            
            <div class="example-box">
                <h4>🔍 The Noisy-XOR Counterexample</h4>
                <p>Consider the following distribution $p$ over three variables $X$, $Y$, $Z$:</p>
                <ul>
                    <li>We sample $X, Y \sim \text{Ber}(0.5)$ from a Bernoulli distribution</li>
                    <li>We set $Z = X \oplus Y$ (XOR operation)</li>
                </ul>
                <p>One can check using algebra that $\{X \perp Y, Z \perp Y, X \perp Z\} \in I(p)$ but $Z \perp \{Y, X\} \notin I(p)$.</p>
                <p>Thus, $X \rightarrow Z \leftarrow Y$ is an $I$-map for $p$, but none of the 3-node graph structures perfectly describes $I(p)$, and hence this distribution doesn't have a perfect map.</p>
            </div>
            
            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="rgba(255,255,255,0.1)" rx="10"/>
                    
                    <!-- Title -->
                    <text x="350" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        Noisy-XOR: When Perfect Maps Don't Exist
                    </text>
                    
                    <!-- XOR Truth Table -->
                    <g transform="translate(50, 60)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="rgba(255,255,255,0.9)">
                            Truth Table: Z = X ⊕ Y
                        </text>
                        
                        <rect x="20" y="30" width="160" height="120" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
                        
                        <!-- Headers -->
                        <text x="50" y="50" text-anchor="middle" font-size="12" font-weight="bold" fill="white">X</text>
                        <text x="90" y="50" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Y</text>
                        <text x="130" y="50" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Z</text>
                        
                        <!-- Values -->
                        <text x="50" y="70" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">0</text>
                        <text x="90" y="70" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">0</text>
                        <text x="130" y="70" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">0</text>
                        
                        <text x="50" y="90" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">0</text>
                        <text x="90" y="90" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">1</text>
                        <text x="130" y="90" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">1</text>
                        
                        <text x="50" y="110" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">1</text>
                        <text x="90" y="110" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">0</text>
                        <text x="130" y="110" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">1</text>
                        
                        <text x="50" y="130" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">1</text>
                        <text x="90" y="130" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">1</text>
                        <text x="130" y="130" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">0</text>
                    </g>
                    
                    <!-- Best I-map -->
                    <g transform="translate(300, 80)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="rgba(255,255,255,0.9)">
                            Best I-map: X → Z ← Y
                        </text>
                        
                        <circle cx="50" cy="60" r="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="50" y="65" text-anchor="middle" font-size="12" font-weight="bold" fill="white">X</text>
                        
                        <circle cx="100" cy="100" r="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="100" y="105" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Z</text>
                        
                        <circle cx="150" cy="60" r="20" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="150" y="65" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Y</text>
                        
                        <path d="M65 70 L85 90" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead6)"/>
                        <path d="M135 70 L115 90" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead6)"/>
                        
                        <text x="100" y="140" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">
                            Captures: X ⊥ Y
                        </text>
                        <text x="100" y="155" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.8)">
                            Misses: Z ⊥ Y, X ⊥ Z
                        </text>
                    </g>
                    
                    <defs>
                        <marker id="arrowhead6" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="rgba(255,255,255,0.7)"/>
                        </marker>
                    </defs>
                    
                    <!-- Independencies -->
                    <g transform="translate(450, 80)">
                        <text x="100" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="rgba(255,255,255,0.9)">
                            Independencies in I(p)
                        </text>
                        
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.8)">
                            ✓ X ⊥ Y (marginal)
                        </text>
                        <text x="100" y="65" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.8)">
                            ✓ Z ⊥ Y (marginal)
                        </text>
                        <text x="100" y="85" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.8)">
                            ✓ X ⊥ Z (marginal)
                        </text>
                        <text x="100" y="110" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.8)">
                            ✗ Z ⊥ {X, Y} (NOT in I(p))
                        </text>
                        
                        <text x="100" y="140" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.7)">
                            No 3-node DAG can capture
                        </text>
                        <text x="100" y="155" text-anchor="middle" font-size="11" fill="rgba(255,255,255,0.7)">
                            all these independencies!
                        </text>
                    </g>
                    
                    <text x="350" y="250" text-anchor="middle" font-size="14" font-weight="bold" fill="white">
                        Conclusion: Perfect maps don't always exist
                    </text>
                </svg>
            </div>
            
            <h3>I-Equivalence: When Maps Are Not Unique</h3>
            <p>A related question is whether perfect maps are unique when they exist. Again, this is not the case, as $X \rightarrow Y$ and $X \leftarrow Y$ encode the same independencies, yet form different graphs.</p>
            
            <div class="definition-box">
                <h4>📋 I-Equivalence</h4>
                <p>More generally, we say that two Bayes nets $G_1, G_2$ are <strong>$I$-equivalent</strong> if they encode the same dependencies: $I(G_1) = I(G_2)$.</p>
            </div>
            
            <p><strong>When are two Bayesian nets $I$-equivalent?</strong> To answer this, let's return to a simple example with three variables. Each of the graphs below have the same <strong>skeleton</strong>, meaning that if we drop the directionality of the arrows, we obtain the same undirected graph in each case.</p>
            
            <div class="svg-container">
                <svg width="800" height="350" viewBox="0 0 800 350">
                    <!-- Background -->
                    <rect width="800" height="350" fill="rgba(255,255,255,0.1)" rx="10"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="16" font-weight="bold" fill="white">
                        I-Equivalent Networks: Same Skeleton, Same V-Structures
                    </text>
                    
                    <!-- Graph (a) -->
                    <g transform="translate(80, 80)">
                        <text x="80" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="rgba(255,255,255,0.9)">
                            (a) X → Z → Y
                        </text>
                        <circle cx="30" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="30" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">X</text>
                        <circle cx="80" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="80" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Z</text>
                        <circle cx="130" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="130" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Y</text>
                        <path d="M48 60 L62 60" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead7)"/>
                        <path d="M98 60 L112 60" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead7)"/>
                        <text x="80" y="100" text-anchor="middle" font-size="10" fill="rgba(255,255,255,0.8)">I-equivalent</text>
                    </g>
                    
                    <!-- Graph (b) -->
                    <g transform="translate(280, 80)">
                        <text x="80" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="rgba(255,255,255,0.9)">
                            (b) Y → Z → X
                        </text>
                        <circle cx="30" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="30" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Y</text>
                        <circle cx="80" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="80" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Z</text>
                        <circle cx="130" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="130" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">X</text>
                        <path d="M48 60 L62 60" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead7)"/>
                        <path d="M98 60 L112 60" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead7)"/>
                        <text x="80" y="100" text-anchor="middle" font-size="10" fill="rgba(255,255,255,0.8)">I-equivalent</text>
                    </g>
                    
                    <!-- Graph (c) -->
                    <g transform="translate(480, 80)">
                        <text x="80" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="rgba(255,255,255,0.9)">
                            (c) X ← Z → Y
                        </text>
                        <circle cx="30" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="30" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">X</text>
                        <circle cx="80" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="80" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Z</text>
                        <circle cx="130" cy="60" r="18" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>
                        <text x="130" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Y</text>
                        <path d="M62 60 L48 60" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead7)"/>
                        <path d="M98 60 L112 60" stroke="rgba(255,255,255,0.7)" stroke-width="2" fill="none" marker-end="url(#arrowhead7)"/>
                        <text x="80" y="100" text-anchor="middle" font-size="10" fill="rgba(255,255,255,0.8)">I-equivalent</text>
                    </g>
                    
                    <!-- Graph (d) - Different -->
                    <g transform="translate(300, 180)">
                        <text x="80" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#ff6b6b">
                            (d) X → Z ← Y
                        </text>
                        <circle cx="30" cy="60" r="18" fill="rgba(255,107,107,0.2)" stroke="#ff6b6b" stroke-width="2"/>
                        <text x="30" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="#ff6b6b">X</text>
                        <circle cx="80" cy="60" r="18" fill="rgba(255,107,107,0.2)" stroke="#ff6b6b" stroke-width="2"/>
                        <text x="80" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="#ff6b6b">Z</text>
                        <circle cx="130" cy="60" r="18" fill="rgba(255,107,107,0.2)" stroke="#ff6b6b" stroke-width="2"/>
                        <text x="130" y="65" text-anchor="middle" font-size="11" font-weight="bold" fill="#ff6b6b">Y</text>
                        <path d="M48 60 L62 60" stroke="#ff6b6b" stroke-width="2" fill="none" marker-end="url(#arrowhead8)"/>
                        <path d="M112 60 L98 60" stroke="#ff6b6b" stroke-width="2" fill="none" marker-end="url(#arrowhead8)"/>
                        <text x="80" y="100" text-anchor="middle" font-size="10" fill="#ff6b6b">NOT I-equivalent</text>
                        <text x="80" y="115" text-anchor="middle" font-size="9" fill="#ff6b6b">(V-structure!)</text>
                    </g>
                    
                    <defs>
                        <marker id="arrowhead7" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="rgba(255,255,255,0.7)"/>
                        </marker>
                        <marker id="arrowhead8" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#ff6b6b"/>
                        </marker>
                    </defs>
                    
                    <text x="400" y="300" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.9)">
                        Graphs (a), (b), (c) have same skeleton and no V-structures → I-equivalent
                    </text>
                    <text x="400" y="320" text-anchor="middle" font-size="12" fill="rgba(255,255,255,0.9)">
                        Graph (d) has V-structure → Different independencies
                    </text>
                </svg>
            </div>
            
            <div class="fact-box">
                <strong>Fundamental Theorem of I-Equivalence:</strong> If $G, G'$ have the same skeleton and the same v-structures, then $I(G) = I(G')$.
                
                <p style="margin-top: 10px;">Intuition: Two graphs are $I$-equivalent if the $d$-separation between variables is the same. We can flip the directionality of any edge, unless it forms a v-structure, and the $d$-connectivity of the graph will be unchanged.</p>
            </div>
            
            <h3>Summary</h3>
            <p>In this tutorial, we have learned that:</p>
            <ul>
                <li><strong>Bayesian networks</strong> provide a compact way to represent probability distributions using DAGs</li>
                <li><strong>Factorization</strong> according to the graph structure reduces parameter complexity from $O(d^n)$ to $O(nd^{k+1})$</li>
                <li><strong>Independence assumptions</strong> are encoded through three fundamental structures: cascade, common parent, and V-structure</li>
                <li><strong>d-separation</strong> generalizes these local patterns to determine independence in larger networks</li>
                <li><strong>Perfect maps</strong> don't always exist, but minimal I-maps can always be found</li>
                <li><strong>I-equivalent networks</strong> share the same skeleton and V-structures, encoding identical independence assumptions</li>
            </ul>
            
            <p>These principles form the foundation for more advanced topics in probabilistic graphical models, including inference algorithms, parameter learning, and structure learning.</p>
        </div>
        
    </div>
</body>
</html> 