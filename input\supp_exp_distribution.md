Exponential Distribution
The exponential distribution is one of the widely used continuous distributions. It is often used to model the time elapsed between events. We will now mathematically define the exponential distribution, and derive its mean and expected value. Then we will develop the intuition for the distribution and discuss several interesting properties that it has.

A continuous random variable X
 is said to have an exponential distribution with parameter λ>0
, shown as X∼Exponential(λ)
, if its PDF is given by
fX(x)={λe−λx0x>0otherwise

Figure 4.5 shows the PDF of exponential distribution for several values of λ
.
Figure
Fig.4.5 - PDF of the exponential random variable.
It is convenient to use the unit step function defined as
u(x)={10x≥0otherwise
so we can write the PDF of an Exponential(λ)
 random variable as
fX(x)=λe−λxu(x).

Let us find its CDF, mean and variance. For x>0
, we have
FX(x)=∫x0λe−λtdt=1−e−λx.
So we can express the CDF as
FX(x)=(1−e−λx)u(x).

Let X∼Exponential(λ)
. We can find its expected value as follows, using integration by parts:

EX
=∫∞0xλe−λxdx
=1λ∫∞0ye−ydy
choosing y=λx
=1λ[−e−y−ye−y]∞0
=1λ.

Now let's find Var(X)
. We have

EX2
=∫∞0x2λe−λxdx
=1λ2∫∞0y2e−ydy
=1λ2[−2e−y−2ye−y−y2e−y]∞0
=2λ2.

Thus, we obtain
Var(X)=EX2−(EX)2=2λ2−1λ2=1λ2.

If X∼Exponential(λ)
, then EX=1λ
 and Var(X)=1λ2
.

An interesting property of the exponential distribution is that it can be viewed as a continuous analogue of the geometric distribution. To see this, recall the random experiment behind the geometric distribution: you toss a coin (repeat a Bernoulli experiment) until you observe the first heads (success). Now, suppose that the coin tosses are Δ
 seconds apart and in each toss the probability of success is p=Δλ
. Also suppose that Δ
 is very small, so the coin tosses are very close together in time and the probability of success in each trial is very low. Let X
 be the time you observe the first success. We will show in the Solved Problems section that the distribution of X
 converges to Exponential(λ)
 as Δ
 approaches zero.

To get some intuition for this interpretation of the exponential distribution, suppose you are waiting for an event to happen. For example, you are at a store and are waiting for the next customer. In each millisecond, the probability that a new customer enters the store is very small. You can imagine that, in each millisecond, a coin (with a very small P(H)
) is tossed, and if it lands heads a new customers enters. If you toss a coin every millisecond, the time until a new customer arrives approximately follows an exponential distribution.

The above interpretation of the exponential is useful in better understanding the properties of the exponential distribution. The most important of these properties is that the exponential distribution is memoryless. To see this, think of an exponential random variable in the sense of tossing a lot of coins until observing the first heads. If we toss the coin several times and do not observe a heads, from now on it is like we start all over again. In other words, the failed coin tosses do not impact the distribution of waiting time from now on. The reason for this is that the coin tosses are independent. We can state this formally as follows:
P(X>x+a|X>a)=P(X>x).

If X
 is exponential with parameter λ>0
, then X
 is a memoryless random variable, that is
P(X>x+a|X>a)=P(X>x), for a,x≥0.

From the point of view of waiting time until arrival of a customer, the memoryless property means that it does not matter how long you have waited so far. If you have not observed a customer until time a
, the distribution of waiting time (from time a
) until the next customer is the same as when you started at time zero. Let us prove the memoryless property of the exponential distribution.
P(X>x+a|X>a)=P(X>x+a,X>a)P(X>a)=P(X>x+a)P(X>a)=1−FX(x+a)1−FX(a)=e−λ(x+a)e−λa=e−λx=P(X>x).