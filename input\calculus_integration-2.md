Learning Objectives
5.2.1 State the definition of the definite integral.
5.2.2 Explain the terms integrand, limits of integration, and variable of integration.
5.2.3 Explain when a function is integrable.
5.2.4 Describe the relationship between the definite integral and net area.
5.2.5 Use geometry and the properties of definite integrals to evaluate them.
5.2.6 Calculate the average value of a function.
In the preceding section we defined the area under a curve in terms of Riemann sums:

A=limn→∞∑i=1nf(x∗i)Δx.
 
However, this definition came with restrictions. We required  f(x)
  to be continuous and nonnegative. Unfortunately, real-world problems don’t always meet these restrictions. In this section, we look at how to apply the concept of the area under the curve to a broader set of functions through the use of the definite integral.

Definition and Notation
The definite integral generalizes the concept of the area under a curve. We lift the requirements that  f(x)
  be continuous and nonnegative, and define the definite integral as follows.

Definition
If  f(x)
  is a function defined on an interval  [a,b],
  the definite integral of f from a to b is given by

∫baf(x)dx=limn→∞∑i=1nf(x∗i)Δx,
 
(5.8)
provided the limit exists. If this limit exists, the function  f(x)
  is said to be integrable on  [a,b],
  or is an integrable function.

The integral symbol in the previous definition should look familiar. We have seen similar notation in the chapter on Applications of Derivatives, where we used the indefinite integral symbol (without the a and b above and below) to represent an antiderivative. Although the notation for indefinite integrals may look similar to the notation for a definite integral, they are not the same. A definite integral is a number. An indefinite integral is a family of functions. Later in this chapter we examine how these concepts are related. However, close attention should always be paid to notation so we know whether we’re working with a definite integral or an indefinite integral.

Integral notation goes back to the late seventeenth century and is one of the contributions of Gottfried Wilhelm Leibniz, who is often considered to be the codiscoverer of calculus, along with Isaac Newton. The integration symbol ∫ is an elongated S, suggesting sigma or summation. On a definite integral, above and below the summation symbol are the boundaries of the interval,  [a,b].
  The numbers a and b are x-values and are called the limits of integration; specifically, a is the lower limit and b is the upper limit. To clarify, we are using the word limit in two different ways in the context of the definite integral. First, we talk about the limit of a sum as  n→∞.
  Second, the boundaries of the region are called the limits of integration.

We call the function  f(x)
  the integrand, and the dx indicates that  f(x)
  is a function with respect to x, called the variable of integration. Note that, like the index in a sum, the variable of integration is a dummy variable, and has no impact on the computation of the integral. We could use any variable we like as the variable of integration:

∫baf(x)dx=∫baf(t)dt=∫baf(u)du
 
Previously, we discussed the fact that if  f(x)
  is continuous on  [a,b],
  then the limit  limn→∞∑i=1nf(x∗i)Δx
  exists and is unique. This leads to the following theorem, which we state without proof.

Theorem 5.1
Continuous Functions Are Integrable
If  f(x)
  is continuous on  [a,b],
  then f is integrable on  [a,b].
 

Functions that are not continuous on  [a,b]
  may still be integrable, depending on the nature of the discontinuities. For example, functions continuous on a closed interval, apart from a finite number of jump discontinuities, are integrable.

It is also worth noting here that we have retained the use of a regular partition in the Riemann sums. This restriction is not strictly necessary. Any partition can be used to form a Riemann sum. However, if a nonregular partition is used to define the definite integral, it is not sufficient to take the limit as the number of subintervals goes to infinity. Instead, we must take the limit as the width of the largest subinterval goes to zero. This introduces a little more complex notation in our limits and makes the calculations more difficult without really gaining much additional insight, so we stick with regular partitions for the Riemann sums.

Example 5.7
Evaluating an Integral Using the Definition
Use the definition of the definite integral to evaluate  ∫20x2dx.
  Use a right-endpoint approximation to generate the Riemann sum.

Checkpoint 5.7
Use the definition of the definite integral to evaluate  ∫30(2x−1)dx.
  Use a right-endpoint approximation to generate the Riemann sum.

Evaluating Definite Integrals
Evaluating definite integrals this way can be quite tedious because of the complexity of the calculations. Later in this chapter we develop techniques for evaluating definite integrals without taking limits of Riemann sums. However, for now, we can rely on the fact that definite integrals represent the area under the curve, and we can evaluate definite integrals by using geometric formulas to calculate that area. We do this to confirm that definite integrals do, indeed, represent areas, so we can then discuss what to do in the case of a curve of a function dropping below the x-axis.

Example 5.8
Using Geometric Formulas to Calculate Definite Integrals
Use the formula for the area of a circle to evaluate  ∫639−(x−3)2−−−−−−−−−−√dx.
 

Checkpoint 5.8
Use the formula for the area of a trapezoid to evaluate  ∫42(2x+3)dx.
 

Area and the Definite Integral
When we defined the definite integral, we lifted the requirement that  f(x)
  be nonnegative. But how do we interpret “the area under the curve” when  f(x)
  is negative?

Net Signed Area
Let us return to the Riemann sum. Consider, for example, the function  f(x)=2−2x2
  (shown in Figure 5.17) on the interval  [0,2].
  Use  n=8
  and choose  {x∗i}
  as the left endpoint of each interval. Construct a rectangle on each subinterval of height  f(x∗i)
  and width Δx. When  f(x∗i)
  is positive, the product  f(x∗i)Δx
  represents the area of the rectangle, as before. When  f(x∗i)
  is negative, however, the product  f(x∗i)Δx
  represents the negative of the area of the rectangle. The Riemann sum then becomes

∑i=18f(x∗i)Δx=(Area of rectangles above thex-axis)−(Area of rectangles below thex-axis)
 
A graph of a downward opening parabola over [-1, 2] with vertex at (0,2) and x-intercepts at (-1,0) and (1,0). Eight rectangles are drawn evenly over [0,2] with heights determined by the value of the function at the left endpoints of each.
Figure 5.17 For a function that is partly negative, the Riemann sum is the area of the rectangles above the x-axis less the area of the rectangles below the x-axis.
Taking the limit as  n→∞,
  the Riemann sum approaches the area between the curve above the x-axis and the x-axis, less the area between the curve below the x-axis and the x-axis, as shown in Figure 5.18. Then,

∫20f(x)dx=limn→∞∑i=1nf(ci)Δx=A1−A2.
 
The quantity  A1−A2
  is called the net signed area.

A graph of a downward opening parabola over [-2, 2] with vertex at (0,2) and x-intercepts at (-1,0) and (1,0). The area in quadrant one under the curve is shaded blue and labeled A1. The area in quadrant four above the curve and to the left of x=2 is shaded blue and labeled A2.
Figure 5.18 In the limit, the definite integral equals area A1 less area A2, or the net signed area.
Notice that net signed area can be positive, negative, or zero. If the area above the x-axis is larger, the net signed area is positive. If the area below the x-axis is larger, the net signed area is negative. If the areas above and below the x-axis are equal, the net signed area is zero.

Example 5.9
Finding the Net Signed Area
Find the net signed area between the curve of the function  f(x)=2x
  and the x-axis over the interval  [−3,3].
 

Analysis
If A1 is the area above the x-axis and A2 is the area below the x-axis, then the net area is  A1−A2.
  Since the areas of the two triangles are equal, the net area is zero.

Checkpoint 5.9
Find the net signed area of  f(x)=x−2
  over the interval  [0,6],
  illustrated in the following image.

A graph of an increasing line going through (-2,-4), (0,-2), (2,0), (4,2) and (6,4). The area above the curve in quadrant four is shaded blue and labeled A2, and the area under the curve and to the left of x=6 in quadrant one is shaded and labeled A1.
Total Area
One application of the definite integral is finding displacement when given a velocity function. If  v(t)
  represents the velocity of an object as a function of time, then the area under the curve tells us how far the object is from its original position. This is a very important application of the definite integral, and we examine it in more detail later in the chapter. For now, we’re just going to look at some basics to get a feel for how this works by studying constant velocities.

When velocity is a constant, the area under the curve is just velocity times time. This idea is already very familiar. If a car travels away from its starting position in a straight line at a speed of 70 mph for 2 hours, then it is 140 mi away from its original position (Figure 5.20). Using integral notation, we have

∫2070dt=140.
 
A graph in quadrant 1 with the x-axis labeled as t (hours) and y-axis labeled as v (mi/hr). The area under the line v(t) = 70 is shaded blue over [0,2].
Figure 5.20 The area under the curve  v(t)=70
  tells us how far the car is from its starting point at a given time.
In the context of displacement, net signed area allows us to take direction into account. If a car travels straight north at a speed of 60 mph for 2 hours, it is 120 mi north of its starting position. If the car then turns around and travels south at a speed of 40 mph for 3 hours, it will be back at it starting position (Figure 5.21). Again, using integral notation, we have

∫2060dt+∫52−40dt=120−120=0.
 
In this case the displacement is zero.

A graph in quadrants one and four with the x-axis labeled as t (hours) and the y axis labeled as v (mi/hr). The first part of the graph is the line v(t) = 60 over [0,2], and the area under the line in quadrant one is shaded. The second part of the graph is the line v(t) = -40 over [2,5], and the area above the line in quadrant four is shaded.
Figure 5.21 The area above the axis and the area below the axis are equal, so the net signed area is zero.
Suppose we want to know how far the car travels overall, regardless of direction. In this case, we want to know the area between the curve and the x-axis, regardless of whether that area is above or below the axis. This is called the total area.

Graphically, it is easiest to think of calculating total area by adding the areas above the axis and the areas below the axis (rather than subtracting the areas below the axis, as we did with net signed area). To accomplish this mathematically, we use the absolute value function. Thus, the total distance traveled by the car is

∫20|60|dt+∫52|−40|dt=∫2060dt+∫5240dt=120+120=240.
 
Bringing these ideas together formally, we state the following definitions.

Definition
Let  f(x)
  be an integrable function defined on an interval  [a,b].
  Let A1 represent the area between  f(x)
  and the x-axis that lies above the axis and let A2 represent the area between  f(x)
  and the x-axis that lies below the axis. Then, the net signed area between  f(x)
  and the x-axis is given by

∫baf(x)dx=A1−A2.
 
The total area between  f(x)
  and the x-axis is given by

∫ba|f(x)|dx=A1+A2.
 
Example 5.10
Finding the Total Area
Find the total area between  f(x)=x−2
  and the x-axis over the interval  [0,6].
 

Checkpoint 5.10
Find the total area between the function  f(x)=2x
  and the x-axis over the interval  [−3,3].
 

Properties of the Definite Integral
The properties of indefinite integrals apply to definite integrals as well. Definite integrals also have properties that relate to the limits of integration. These properties, along with the rules of integration that we examine later in this chapter, help us manipulate expressions to evaluate definite integrals.

Rule: Properties of the Definite Integral

∫aaf(x)dx=0
 
(5.9)

If the limits of integration are the same, the integral is just a line and contains no area.

∫abf(x)dx=−∫baf(x)dx
 
(5.10)

If the limits are reversed, then place a negative sign in front of the integral.

∫ba[f(x)+g(x)]dx=∫baf(x)dx+∫bag(x)dx
 
(5.11)

The integral of a sum is the sum of the integrals.

∫ba[f(x)−g(x)]dx=∫baf(x)dx−∫bag(x)dx
 
(5.12)

The integral of a difference is the difference of the integrals.

∫bacf(x)dx=c∫baf(x)dx
 
(5.13)

for constant c. The integral of the product of a constant and a function is equal to the constant multiplied by the integral of the function.

∫baf(x)dx=∫caf(x)dx+∫bcf(x)dx
 
(5.14)

Although this formula normally applies when c is between a and b, the formula holds for all values of a, b, and c, provided  f(x)
  is integrable on the largest interval.
Example 5.11
Using the Properties of the Definite Integral
Use the properties of the definite integral to express the definite integral of  f(x)=−3x3+2x+2
  over the interval  [−2,1]
  as the sum of three definite integrals.

Checkpoint 5.11
Use the properties of the definite integral to express the definite integral of  f(x)=6x3−4x2+2x−3
  over the interval  [1,3]
  as the sum of four definite integrals.

Example 5.12
Using the Properties of the Definite Integral
If it is known that  ∫80f(x)dx=10
  and  ∫50f(x)dx=5,
  find the value of  ∫85f(x)dx.
 

Checkpoint 5.12
If it is known that  ∫51f(x)dx=−3
  and  ∫52f(x)dx=4,
  find the value of  ∫21f(x)dx.
 

Comparison Properties of Integrals
A picture can sometimes tell us more about a function than the results of computations. Comparing functions by their graphs as well as by their algebraic expressions can often give new insight into the process of integration. Intuitively, we might say that if a function  f(x)
  is above another function  g(x),
  then the area between  f(x)
  and the x-axis is greater than the area between  g(x)
  and the x-axis. This is true depending on the interval over which the comparison is made. The properties of definite integrals are valid whether  a<b,a=b,
  or  a>b.
  The following properties, however, concern only the case  a≤b,
  and are used when we want to compare the sizes of integrals.

Theorem 5.2
Comparison Theorem
If  f(x)≥0
  for  a≤x≤b,
  then
∫baf(x)dx≥0.
 
If  f(x)≥g(x)
  for  a≤x≤b,
  then
∫baf(x)dx≥∫bag(x)dx.
 
If m and M are constants such that  m≤f(x)≤M
  for  a≤x≤b,
  then
m(b−a)≤∫baf(x)dx≤M(b−a).
 
Example 5.13
Comparing Two Functions over a Given Interval
Compare  f(x)=1+x2−−−−−√
  and  g(x)=1+x−−−−√
  over the interval  [0,1].
 

Average Value of a Function
We often need to find the average of a set of numbers, such as an average test grade. Suppose you received the following test scores in your algebra class: 89, 90, 56, 78, 100, and 69. Your semester grade is your average of test scores and you want to know what grade to expect. We can find the average by adding all the scores and dividing by the number of scores. In this case, there are six test scores. Thus,

89+90+56+78+100+696=4826≈80.33.
 
Therefore, your average test grade is approximately 80.33, which translates to a B− at most schools.

Suppose, however, that we have a function  v(t)
  that gives us the speed of an object at any time t, and we want to find the object’s average speed. The function  v(t)
  takes on an infinite number of values, so we can’t use the process just described. Fortunately, we can use a definite integral to find the average value of a function such as this.

Let  f(x)
  be continuous over the interval  [a,b]
  and let  [a,b]
  be divided into n subintervals of width  Δx=(b−a)/n.
  Choose a representative  x∗i
  in each subinterval and calculate  f(x∗i)
  for  i=1,2,…,n.
  In other words, consider each  f(x∗i)
  as a sampling of the function over each subinterval. The average value of the function may then be approximated as

f(x∗1)+f(x∗2)+⋯+f(x∗n)n,
 
which is basically the same expression used to calculate the average of discrete values.

But we know  Δx=b−an,
  so  n=b−aΔx,
  and we get

f(x∗1)+f(x∗2)+⋯+f(x∗n)n=f(x∗1)+f(x∗2)+⋯+f(x∗n)(b−a)Δx.
 
Following through with the algebra, the numerator is a sum that is represented as  ∑i=1nf(x∗i),
  and we are dividing by a fraction. To divide by a fraction, invert the denominator and multiply. Thus, an approximate value for the average value of the function is given by

∑i=1nf(x∗i)(b−a)Δx=(Δxb−a)∑i=1nf(x∗i)=(1b−a)∑i=1nf(x∗i)Δx.
 
This is a Riemann sum. Then, to get the exact average value, take the limit as n goes to infinity. Thus, the average value of a function is given by

1b−alimn→∞∑i=1nf(xi)Δx=1b−a∫baf(x)dx.
 
Definition
Let  f(x)
  be continuous over the interval  [a,b].
  Then, the average value of the function  f(x)
  (or fave) on  [a,b]
  is given by

fave=1b−a∫baf(x)dx.
 
Example 5.14
Finding the Average Value of a Linear Function
Find the average value of  f(x)=x+1
  over the interval  [0,5].
 

Checkpoint 5.13
Find the average value of  f(x)=6−2x
  over the interval  [0,3].
 

Section 5.2 Exercises
In the following exercises, express the limits as integrals.

60.	 limn→∞∑i=1n(x∗i)Δx
  over  [1,3]
 
61.	 limn→∞∑i=1n(5(x∗i)2−3(x∗i)3)Δx
  over  [0,2]
 
62.	 limn→∞∑i=1nsin2(2πx∗i)Δx
  over  [0,1]
 
63.	 limn→∞∑i=1ncos2(2πx∗i)Δx
  over  [0,1]