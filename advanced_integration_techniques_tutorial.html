<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Integration Techniques - Comprehensive Tutorial</title>
    
    <!-- MathJax 3 Configuration with tex-svg.js for HD rendering -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true,
                tags: 'ams'
            },
            svg: {
                fontCache: 'global',
                displayAlign: 'center',
                displayIndent: '0'
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .notice-box {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border: 2px solid #e67e22;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            color: white;
            border-left: 6px solid #d35400;
        }

        .section {
            margin: 40px 0;
            padding: 30px;
            border-radius: 10px;
            background: #f8f9fa;
            border-left: 5px solid #007bff;
        }

        .section h2 {
            color: #007bff;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .section h3 {
            color: #495057;
            font-size: 1.4em;
            margin: 25px 0 15px 0;
        }

        .theorem-box {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: 2px solid #2980b9;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .theorem-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }

        .technique-box {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            border: 2px solid #8e44ad;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .technique-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
            text-align: center;
        }

        .example-box {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 2px solid #c0392b;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .example-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .strategy-box {
            background: linear-gradient(135deg, #16a085, #138d75);
            border: 2px solid #138d75;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: white;
        }

        .strategy-box h4 {
            color: white;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .warning-box {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: 2px solid #c0392b;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            border-left: 6px solid #f39c12;
        }

        .info-box {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            border-left: 6px solid #f1c40f;
        }

        .svg-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .tutorial-nav {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .tutorial-nav h3 {
            margin-bottom: 15px;
            text-align: center;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .nav-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .technique-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .technique-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-radius: 10px;
            padding: 20px;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>Advanced Integration Techniques</h1>
            <p>Mastering Complex Integration Methods</p>
        </div>

        <!-- Notice about source file -->
        <div class="notice-box">
            <h4>📋 Source File Status</h4>
            <p>The requested file <code>calculus_integration_5.md</code> appears to be empty. I've created a comprehensive tutorial on advanced integration techniques to continue your calculus learning journey.</p>
        </div>

        <!-- Tutorial Navigation -->
        <div class="tutorial-nav">
            <h3>Advanced Integration Tutorial Outline</h3>
            <div class="nav-grid">
                <div class="nav-item">
                    <strong>Section 1:</strong><br>Integration Strategy Overview
                </div>
                <div class="nav-item">
                    <strong>Section 2:</strong><br>U-Substitution Method
                </div>
                <div class="nav-item">
                    <strong>Section 3:</strong><br>Integration by Parts
                </div>
                <div class="nav-item">
                    <strong>Section 4:</strong><br>Trigonometric Integrals
                </div>
                <div class="nav-item">
                    <strong>Section 5:</strong><br>Partial Fractions
                </div>
                <div class="nav-item">
                    <strong>Section 6:</strong><br>Advanced Strategies
                </div>
            </div>
        </div>

        <!-- Section 1: Integration Strategy Overview -->
        <div class="section">
            <h2>Section 1: Integration Strategy Overview</h2>
            
            <p>As we move beyond basic integration formulas and applications, we encounter integrals that require sophisticated techniques. This tutorial equips you with a comprehensive toolkit for tackling complex integration problems systematically.</p>

            <h3>The Integration Hierarchy</h3>
            <p>Successful integration requires recognizing patterns and selecting appropriate techniques. Here's how advanced methods build upon what you already know:</p>

            <div class="svg-container">
                <svg width="800" height="500" viewBox="0 0 800 500">
                    <!-- Background -->
                    <rect width="800" height="500" fill="#f8f9fa"/>
                    
                    <!-- Title -->
                    <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#2c3e50">The Integration Technique Hierarchy</text>
                    
                    <!-- Level 1: Basic Formulas -->
                    <rect x="300" y="60" width="200" height="60" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="10"/>
                    <text x="400" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="white">Basic Formulas</text>
                    <text x="400" y="105" text-anchor="middle" font-size="12" fill="white">Power, exponential, trig</text>
                    
                    <!-- Level 2: Direct Applications -->
                    <rect x="150" y="150" width="150" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="10"/>
                    <text x="225" y="175" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Direct</text>
                    <text x="225" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Applications</text>
                    
                    <rect x="325" y="150" width="150" height="60" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="10"/>
                    <text x="400" y="175" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Simple</text>
                    <text x="400" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Substitution</text>
                    
                    <rect x="500" y="150" width="150" height="60" fill="#27ae60" stroke="#229954" stroke-width="2" rx="10"/>
                    <text x="575" y="175" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Algebraic</text>
                    <text x="575" y="190" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Manipulation</text>
                    
                    <!-- Level 3: Advanced Techniques -->
                    <rect x="50" y="240" width="140" height="60" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="10"/>
                    <text x="120" y="265" text-anchor="middle" font-size="12" font-weight="bold" fill="white">U-Substitution</text>
                    <text x="120" y="280" text-anchor="middle" font-size="11" fill="white">Chain rule inverse</text>
                    
                    <rect x="210" y="240" width="140" height="60" fill="#16a085" stroke="#138d75" stroke-width="2" rx="10"/>
                    <text x="280" y="265" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Integration</text>
                    <text x="280" y="280" text-anchor="middle" font-size="12" font-weight="bold" fill="white">by Parts</text>
                    
                    <rect x="370" y="240" width="140" height="60" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="10"/>
                    <text x="440" y="265" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Trigonometric</text>
                    <text x="440" y="280" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Methods</text>
                    
                    <rect x="530" y="240" width="140" height="60" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="10"/>
                    <text x="600" y="265" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Partial</text>
                    <text x="600" y="280" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Fractions</text>
                    
                    <!-- Level 4: Specialized Methods -->
                    <rect x="150" y="330" width="140" height="60" fill="#7f8c8d" stroke="#95a5a6" stroke-width="2" rx="10"/>
                    <text x="220" y="355" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Trigonometric</text>
                    <text x="220" y="370" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Substitution</text>
                    
                    <rect x="310" y="330" width="140" height="60" fill="#8e44ad" stroke="#7d3c98" stroke-width="2" rx="10"/>
                    <text x="380" y="355" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Numerical</text>
                    <text x="380" y="370" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Methods</text>
                    
                    <rect x="470" y="330" width="140" height="60" fill="#c0392b" stroke="#a93226" stroke-width="2" rx="10"/>
                    <text x="540" y="355" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Special</text>
                    <text x="540" y="370" text-anchor="middle" font-size="12" font-weight="bold" fill="white">Functions</text>
                    
                    <!-- Arrows showing progression -->
                    <path d="M 360 120 L 225 150" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 400 120 L 400 150" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 440 120 L 575 150" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <path d="M 200 210 L 120 240" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 250 210 L 280 240" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 450 210 L 440 240" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 550 210 L 600 240" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <path d="M 200 300 L 220 330" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 360 300 L 380 330" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <path d="M 520 300 L 540 330" stroke="#7f8c8d" stroke-width="2" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
                        </marker>
                    </defs>
                    
                    <!-- Strategy flow -->
                    <text x="50" y="440" font-size="14" font-weight="bold" fill="#2c3e50">Problem-Solving Strategy:</text>
                    <text x="50" y="460" font-size="12" fill="#2c3e50">1. Try basic formulas → 2. Look for substitutions → 3. Consider integration by parts → 4. Apply specialized techniques</text>
                    <text x="50" y="480" font-size="12" fill="#2c3e50">Always start simple and work your way up the hierarchy!</text>
                </svg>
            </div>

            <h3>Pattern Recognition: The Key to Success</h3>
            <p>Advanced integration is fundamentally about pattern recognition. Each technique is designed to handle specific types of integrands:</p>

            <div class="technique-grid">
                <div class="technique-card">
                    <h4>U-Substitution</h4>
                    <p><strong>When to use:</strong> Composite functions, chain rule patterns</p>
                    <p><strong>Pattern:</strong> $\int f(g(x)) \cdot g'(x) dx$</p>
                    <p><strong>Example:</strong> $\int 2x \cos(x^2) dx$</p>
                </div>
                
                <div class="technique-card">
                    <h4>Integration by Parts</h4>
                    <p><strong>When to use:</strong> Products of different function types</p>
                    <p><strong>Pattern:</strong> $\int u \, dv = uv - \int v \, du$</p>
                    <p><strong>Example:</strong> $\int x e^x dx$</p>
                </div>
                
                <div class="technique-card">
                    <h4>Trigonometric Integrals</h4>
                    <p><strong>When to use:</strong> Powers of trig functions</p>
                    <p><strong>Pattern:</strong> $\int \sin^m x \cos^n x dx$</p>
                    <p><strong>Example:</strong> $\int \sin^3 x \cos^2 x dx$</p>
                </div>
                
                <div class="technique-card">
                    <h4>Partial Fractions</h4>
                    <p><strong>When to use:</strong> Rational functions</p>
                    <p><strong>Pattern:</strong> $\int \frac{P(x)}{Q(x)} dx$ where $\deg P < \deg Q$</p>
                    <p><strong>Example:</strong> $\int \frac{x+1}{x^2-1} dx$</p>
                </div>
            </div>

            <h3>Strategic Decision Tree</h3>
            <p>When faced with a new integral, use this decision process:</p>

            <div class="strategy-box">
                <h4>Integration Strategy Flowchart</h4>
                <p><strong>Step 1:</strong> Can I apply a basic formula directly? If yes → Use it!</p>
                <p><strong>Step 2:</strong> Can I simplify algebraically first? If yes → Simplify, then retry</p>
                <p><strong>Step 3:</strong> Do I see a function and its derivative? If yes → U-substitution</p>
                <p><strong>Step 4:</strong> Is it a product of different function types? If yes → Integration by parts</p>
                <p><strong>Step 5:</strong> Is it a rational function? If yes → Partial fractions</p>
                <p><strong>Step 6:</strong> Does it involve trig functions? If yes → Trigonometric techniques</p>
                <p><strong>Step 7:</strong> Consider advanced substitutions or numerical methods</p>
            </div>

            <p>Understanding this hierarchy and strategic approach will make you a confident, systematic integrator. Let's now dive into each technique with detailed examples and applications!</p>
        </div>

        <!-- Section 2: U-Substitution -->
        <div class="section">
            <h2>Section 2: U-Substitution Method</h2>
            
            <p>U-substitution is the integration counterpart to the chain rule in differentiation. It transforms complex integrals into simpler forms by recognizing composite function patterns.</p>

            <div class="theorem-box">
                <h4>U-Substitution Formula</h4>
                <p>If $\int f(g(x)) \cdot g'(x) dx$ can be written as $\int f(u) du$ where $u = g(x)$, then:</p>
                <p>$$\int f(g(x)) \cdot g'(x) dx = \int f(u) du = F(u) + C = F(g(x)) + C$$</p>
                <p><strong>Key insight:</strong> Look for a function and its derivative within the integral!</p>
            </div>

            <div class="example-box">
                <h4>Example 1: Basic U-Substitution</h4>
                <p><strong>Problem:</strong> Evaluate $\int 2x \cos(x^2) dx$</p>
                
                <p><strong>Step 1:</strong> Identify the substitution</p>
                <p>Let $u = x^2$, then $du = 2x dx$</p>
                <p>Notice that $2x$ appears in our integral!</p>
                
                <p><strong>Step 2:</strong> Substitute</p>
                <p>$$\int 2x \cos(x^2) dx = \int \cos(u) du$$</p>
                
                <p><strong>Step 3:</strong> Integrate</p>
                <p>$$= \sin(u) + C$$</p>
                
                <p><strong>Step 4:</strong> Back-substitute</p>
                <p>$$= \sin(x^2) + C$$</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="350" viewBox="0 0 700 350">
                    <!-- Background -->
                    <rect width="700" height="350" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">U-Substitution Visualization</text>
                    
                    <!-- Original integral -->
                    <g transform="translate(50, 60)">
                        <rect x="0" y="0" width="180" height="100" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="10"/>
                        <text x="90" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Original Integral</text>
                        <text x="90" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">∫ 2x cos(x²) dx</text>
                        <text x="90" y="65" text-anchor="middle" font-size="10" fill="#e74c3c">Complex composite function</text>
                        <text x="90" y="85" text-anchor="middle" font-size="10" fill="#27ae60">But contains f(g(x))⋅g'(x)</text>
                    </g>
                    
                    <!-- Arrow -->
                    <path d="M 250 110 L 320 110" stroke="#f39c12" stroke-width="4" marker-end="url(#arrow)"/>
                    <text x="285" y="105" text-anchor="middle" font-size="11" font-weight="bold" fill="#f39c12">Substitute</text>
                    <text x="285" y="125" text-anchor="middle" font-size="10" fill="#2c3e50">u = x²</text>
                    <text x="285" y="140" text-anchor="middle" font-size="10" fill="#2c3e50">du = 2x dx</text>
                    
                    <!-- Substituted integral -->
                    <g transform="translate(350, 60)">
                        <rect x="0" y="0" width="150" height="100" fill="#e8f8f5" stroke="#27ae60" stroke-width="2" rx="10"/>
                        <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">After Substitution</text>
                        <text x="75" y="45" text-anchor="middle" font-size="11" fill="#2c3e50">∫ cos(u) du</text>
                        <text x="75" y="65" text-anchor="middle" font-size="10" fill="#27ae60">Simple basic integral!</text>
                        <text x="75" y="85" text-anchor="middle" font-size="10" fill="#3498db">= sin(u) + C</text>
                    </g>
                    
                    <!-- Arrow back -->
                    <path d="M 470 170 L 400 170" stroke="#e74c3c" stroke-width="4" marker-end="url(#arrow2)"/>
                    <text x="435" y="165" text-anchor="middle" font-size="11" font-weight="bold" fill="#e74c3c">Back-substitute</text>
                    <text x="435" y="185" text-anchor="middle" font-size="10" fill="#2c3e50">Replace u with x²</text>
                    
                    <!-- Final answer -->
                    <g transform="translate(250, 200)">
                        <rect x="0" y="0" width="200" height="80" fill="#fff4e6" stroke="#f39c12" stroke-width="2" rx="10"/>
                        <text x="100" y="25" text-anchor="middle" font-size="12" font-weight="bold" fill="#2c3e50">Final Answer</text>
                        <text x="100" y="45" text-anchor="middle" font-size="12" fill="#2c3e50">sin(x²) + C</text>
                        <text x="100" y="65" text-anchor="middle" font-size="10" fill="#f39c12">Mission accomplished!</text>
                    </g>
                    
                    <!-- Arrow markers -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12"/>
                        </marker>
                        <marker id="arrow2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
                        </marker>
                    </defs>
                    
                    <!-- Step indicators -->
                    <circle cx="90" cy="310" r="15" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                    <text x="90" y="315" text-anchor="middle" font-size="12" font-weight="bold" fill="white">1</text>
                    
                    <circle cx="285" cy="310" r="15" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                    <text x="285" y="315" text-anchor="middle" font-size="12" font-weight="bold" fill="white">2</text>
                    
                    <circle cx="435" cy="310" r="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                    <text x="435" y="315" text-anchor="middle" font-size="12" font-weight="bold" fill="white">3</text>
                    
                    <circle cx="580" cy="310" r="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                    <text x="580" y="315" text-anchor="middle" font-size="12" font-weight="bold" fill="white">4</text>
                </svg>
            </div>

            <div class="example-box">
                <h4>Example 2: Definite Integral with U-Substitution</h4>
                <p><strong>Problem:</strong> Evaluate $\int_0^1 \frac{x}{\sqrt{1+x^2}} dx$</p>
                
                <p><strong>Step 1:</strong> Set up substitution</p>
                <p>Let $u = 1 + x^2$, then $du = 2x dx$, so $x dx = \frac{1}{2}du$</p>
                
                <p><strong>Step 2:</strong> Change limits of integration</p>
                <p>When $x = 0$: $u = 1 + 0^2 = 1$</p>
                <p>When $x = 1$: $u = 1 + 1^2 = 2$</p>
                
                <p><strong>Step 3:</strong> Substitute and integrate</p>
                <p>$$\int_0^1 \frac{x}{\sqrt{1+x^2}} dx = \int_1^2 \frac{1}{\sqrt{u}} \cdot \frac{1}{2} du = \frac{1}{2}\int_1^2 u^{-1/2} du$$</p>
                <p>$$= \frac{1}{2} \left[2u^{1/2}\right]_1^2 = \left[u^{1/2}\right]_1^2 = \sqrt{2} - 1$$</p>
            </div>

            <div class="strategy-box">
                <h4>U-Substitution Strategy Guide</h4>
                <p><strong>Look for these patterns:</strong></p>
                <p>• $\int f(g(x)) \cdot g'(x) dx$ - Composite function with its derivative</p>
                <p>• $\int \frac{g'(x)}{g(x)} dx$ - Derivative over original function</p>
                <p>• $\int g(x)^n \cdot g'(x) dx$ - Power of function times its derivative</p>
                
                <p><strong>Common substitutions:</strong></p>
                <p>• $u = ax + b$ for linear expressions</p>
                <p>• $u = x^n$ for polynomial expressions</p>
                <p>• $u = \sin x, \cos x, \tan x$ for trigonometric expressions</p>
                <p>• $u = e^x, \ln x$ for exponential/logarithmic expressions</p>
            </div>
        </div>

        <!-- Section 3: Integration by Parts -->
        <div class="section">
            <h2>Section 3: Integration by Parts</h2>
            
            <p>Integration by parts is derived from the product rule of differentiation. It's particularly useful for integrals involving products of different types of functions.</p>

            <div class="theorem-box">
                <h4>Integration by Parts Formula</h4>
                <p>$$\int u \, dv = uv - \int v \, du$$</p>
                <p><strong>Strategy:</strong> Choose $u$ and $dv$ such that $\int v \, du$ is simpler than the original integral.</p>
                <p><strong>LIATE Priority:</strong> Choose $u$ from Logarithmic, Inverse trig, Algebraic, Trigonometric, Exponential (in that order)</p>
            </div>

            <div class="example-box">
                <h4>Example 3: Basic Integration by Parts</h4>
                <p><strong>Problem:</strong> Evaluate $\int x e^x dx$</p>
                
                <p><strong>Step 1:</strong> Choose $u$ and $dv$ using LIATE</p>
                <p>Let $u = x$ (Algebraic) and $dv = e^x dx$ (Exponential)</p>
                <p>Then $du = dx$ and $v = e^x$</p>
                
                <p><strong>Step 2:</strong> Apply the formula</p>
                <p>$$\int x e^x dx = x \cdot e^x - \int e^x dx$$</p>
                <p>$$= xe^x - e^x + C = e^x(x-1) + C$$</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="400" viewBox="0 0 700 400">
                    <!-- Background -->
                    <rect width="700" height="400" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Integration by Parts: LIATE Method</text>
                    
                    <!-- LIATE hierarchy -->
                    <g transform="translate(50, 60)">
                        <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Choose u from LIATE (highest priority first):</text>
                        
                        <!-- L - Logarithmic -->
                        <rect x="50" y="40" width="100" height="50" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="5"/>
                        <text x="100" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">L</text>
                        <text x="100" y="75" text-anchor="middle" font-size="10" fill="white">Logarithmic</text>
                        <text x="100" y="105" text-anchor="middle" font-size="9" fill="#2c3e50">ln x, log x</text>
                        
                        <!-- I - Inverse trig -->
                        <rect x="160" y="40" width="100" height="50" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="5"/>
                        <text x="210" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">I</text>
                        <text x="210" y="75" text-anchor="middle" font-size="10" fill="white">Inverse trig</text>
                        <text x="210" y="105" text-anchor="middle" font-size="9" fill="#2c3e50">arcsin x, arctan x</text>
                        
                        <!-- A - Algebraic -->
                        <rect x="270" y="40" width="100" height="50" fill="#9b59b6" stroke="#8e44ad" stroke-width="2" rx="5"/>
                        <text x="320" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">A</text>
                        <text x="320" y="75" text-anchor="middle" font-size="10" fill="white">Algebraic</text>
                        <text x="320" y="105" text-anchor="middle" font-size="9" fill="#2c3e50">x, x², x³, ...</text>
                        
                        <!-- T - Trigonometric -->
                        <rect x="380" y="40" width="100" height="50" fill="#3498db" stroke="#2980b9" stroke-width="2" rx="5"/>
                        <text x="430" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">T</text>
                        <text x="430" y="75" text-anchor="middle" font-size="10" fill="white">Trigonometric</text>
                        <text x="430" y="105" text-anchor="middle" font-size="9" fill="#2c3e50">sin x, cos x, tan x</text>
                        
                        <!-- E - Exponential -->
                        <rect x="490" y="40" width="100" height="50" fill="#27ae60" stroke="#229954" stroke-width="2" rx="5"/>
                        <text x="540" y="60" text-anchor="middle" font-size="12" font-weight="bold" fill="white">E</text>
                        <text x="540" y="75" text-anchor="middle" font-size="10" fill="white">Exponential</text>
                        <text x="540" y="105" text-anchor="middle" font-size="9" fill="#2c3e50">eˣ, aˣ</text>
                    </g>
                    
                    <!-- Example application -->
                    <g transform="translate(100, 180)">
                        <rect x="0" y="0" width="500" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="10"/>
                        <text x="250" y="25" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Example: ∫ x eˣ dx</text>
                        
                        <text x="50" y="50" font-size="12" fill="#2c3e50"><tspan font-weight="bold">Step 1:</tspan> Identify function types</text>
                        <text x="70" y="70" font-size="11" fill="#9b59b6">x = Algebraic (A)</text>
                        <text x="70" y="85" font-size="11" fill="#27ae60">eˣ = Exponential (E)</text>
                        
                        <text x="280" y="50" font-size="12" fill="#2c3e50"><tspan font-weight="bold">Step 2:</tspan> Choose using LIATE</text>
                        <text x="300" y="70" font-size="11" fill="#9b59b6">u = x (higher priority)</text>
                        <text x="300" y="85" font-size="11" fill="#27ae60">dv = eˣ dx (lower priority)</text>
                        
                        <text x="50" y="110" font-size="12" fill="#2c3e50"><tspan font-weight="bold">Step 3:</tspan> Apply formula</text>
                        <text x="70" y="130" font-size="11" fill="#2c3e50">∫ x eˣ dx = x·eˣ - ∫ eˣ dx = eˣ(x-1) + C</text>
                    </g>
                </svg>
            </div>

            <div class="example-box">
                <h4>Example 4: Repeated Integration by Parts</h4>
                <p><strong>Problem:</strong> Evaluate $\int x^2 e^x dx$</p>
                
                <p><strong>First application:</strong></p>
                <p>Let $u = x^2$ and $dv = e^x dx$</p>
                <p>Then $du = 2x dx$ and $v = e^x$</p>
                <p>$$\int x^2 e^x dx = x^2 e^x - \int 2x e^x dx$$</p>
                
                <p><strong>Second application:</strong> For $\int 2x e^x dx$</p>
                <p>Let $u = 2x$ and $dv = e^x dx$</p>
                <p>Then $du = 2 dx$ and $v = e^x$</p>
                <p>$$\int 2x e^x dx = 2x e^x - \int 2 e^x dx = 2x e^x - 2e^x$$</p>
                
                <p><strong>Final result:</strong></p>
                <p>$$\int x^2 e^x dx = x^2 e^x - (2x e^x - 2e^x) = e^x(x^2 - 2x + 2) + C$$</p>
            </div>
        </div>

        <!-- Section 4: Advanced Strategies -->
        <div class="section">
            <h2>Section 4: Advanced Problem-Solving Strategies</h2>
            
            <p>Mastering integration requires developing strategic thinking and pattern recognition. Here are advanced techniques for tackling complex problems.</p>

            <div class="strategy-box">
                <h4>The Integration Decision Tree</h4>
                <p><strong>1. Direct Formula?</strong> Can you apply a basic integration rule immediately?</p>
                <p><strong>2. Algebraic Simplification?</strong> Can you simplify, factor, or use algebra first?</p>
                <p><strong>3. U-Substitution?</strong> Do you see f(g(x))·g'(x) or similar patterns?</p>
                <p><strong>4. Integration by Parts?</strong> Is it a product of different function types?</p>
                <p><strong>5. Trigonometric Identity?</strong> Can trig identities help simplify?</p>
                <p><strong>6. Partial Fractions?</strong> Is it a rational function?</p>
                <p><strong>7. Advanced Substitution?</strong> Try trigonometric or other specialized substitutions</p>
            </div>

            <div class="technique-grid">
                <div class="technique-card">
                    <h4>🎯 When to Use U-Substitution</h4>
                    <p>• Composite functions with derivatives present</p>
                    <p>• $\int f(ax+b) dx$ patterns</p>
                    <p>• Exponential or trig function compositions</p>
                    <p>• Rational functions with polynomial relationships</p>
                </div>
                
                <div class="technique-card">
                    <h4>🔄 When to Use Integration by Parts</h4>
                    <p>• Products of polynomial and exponential</p>
                    <p>• Products of polynomial and trigonometric</p>
                    <p>• Logarithmic functions</p>
                    <p>• Inverse trigonometric functions</p>
                </div>
                
                <div class="technique-card">
                    <h4>⚡ Quick Recognition Tips</h4>
                    <p>• Look for derivatives hiding in plain sight</p>
                    <p>• Check if algebraic manipulation helps first</p>
                    <p>• Consider if the integral "undoes" a differentiation rule</p>
                    <p>• Practice makes pattern recognition automatic</p>
                </div>
            </div>

            <div class="example-box">
                <h4>Challenge Problem: Mixed Techniques</h4>
                <p><strong>Problem:</strong> Evaluate $\int \frac{x \ln x}{(1+x^2)^2} dx$</p>
                
                <p><strong>Analysis:</strong> This requires multiple techniques!</p>
                <p>1. First, we might consider u-substitution with $u = 1+x^2$</p>
                <p>2. But we also have $\ln x$, suggesting integration by parts</p>
                <p>3. The solution involves recognizing the optimal combination</p>
                
                <p><strong>Strategy:</strong> Try $u = 1+x^2$ first, then handle the remaining logarithmic integral with parts</p>
            </div>

            <div class="svg-container">
                <svg width="700" height="300" viewBox="0 0 700 300">
                    <!-- Background -->
                    <rect width="700" height="300" fill="white"/>
                    
                    <!-- Title -->
                    <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#2c3e50">Integration Mastery: The Journey</text>
                    
                    <!-- Progress levels -->
                    <g transform="translate(50, 60)">
                        <!-- Beginner -->
                        <circle cx="60" cy="60" r="40" fill="#e74c3c" stroke="#c0392b" stroke-width="3"/>
                        <text x="60" y="50" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Beginner</text>
                        <text x="60" y="65" text-anchor="middle" font-size="9" fill="white">Basic formulas</text>
                        <text x="60" y="78" text-anchor="middle" font-size="9" fill="white">Direct application</text>
                        
                        <!-- Intermediate -->
                        <circle cx="200" cy="60" r="40" fill="#f39c12" stroke="#e67e22" stroke-width="3"/>
                        <text x="200" y="50" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Intermediate</text>
                        <text x="200" y="65" text-anchor="middle" font-size="9" fill="white">U-substitution</text>
                        <text x="200" y="78" text-anchor="middle" font-size="9" fill="white">Integration by parts</text>
                        
                        <!-- Advanced -->
                        <circle cx="340" cy="60" r="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="3"/>
                        <text x="340" y="50" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Advanced</text>
                        <text x="340" y="65" text-anchor="middle" font-size="9" fill="white">Multiple techniques</text>
                        <text x="340" y="78" text-anchor="middle" font-size="9" fill="white">Pattern recognition</text>
                        
                        <!-- Expert -->
                        <circle cx="480" cy="60" r="40" fill="#27ae60" stroke="#229954" stroke-width="3"/>
                        <text x="480" y="50" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Expert</text>
                        <text x="480" y="65" text-anchor="middle" font-size="9" fill="white">Strategic thinking</text>
                        <text x="480" y="78" text-anchor="middle" font-size="9" fill="white">Creative solutions</text>
                        
                        <!-- Master -->
                        <circle cx="580" cy="60" r="40" fill="#3498db" stroke="#2980b9" stroke-width="3"/>
                        <text x="580" y="50" text-anchor="middle" font-size="10" font-weight="bold" fill="white">Master</text>
                        <text x="580" y="65" text-anchor="middle" font-size="9" fill="white">Intuitive</text>
                        <text x="580" y="78" text-anchor="middle" font-size="9" fill="white">Elegant solutions</text>
                        
                        <!-- Progress arrows -->
                        <path d="M 105 60 L 155 60" stroke="#7f8c8d" stroke-width="3" marker-end="url(#progressarrow)"/>
                        <path d="M 245 60 L 295 60" stroke="#7f8c8d" stroke-width="3" marker-end="url(#progressarrow)"/>
                        <path d="M 385 60 L 435 60" stroke="#7f8c8d" stroke-width="3" marker-end="url(#progressarrow)"/>
                        <path d="M 525 60 L 535 60" stroke="#7f8c8d" stroke-width="3" marker-end="url(#progressarrow)"/>
                        
                        <!-- Arrow marker -->
                        <defs>
                            <marker id="progressarrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#7f8c8d"/>
                            </marker>
                        </defs>
                    </g>
                    
                    <!-- Key skills -->
                    <g transform="translate(50, 150)">
                        <text x="300" y="20" text-anchor="middle" font-size="14" font-weight="bold" fill="#2c3e50">Key Skills for Integration Mastery</text>
                        
                        <rect x="50" y="40" width="120" height="80" fill="#3498db" stroke="#2980b9" stroke-width="1" rx="5"/>
                        <text x="110" y="60" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Pattern</text>
                        <text x="110" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Recognition</text>
                        <text x="110" y="95" text-anchor="middle" font-size="9" fill="white">Spot function types</text>
                        <text x="110" y="110" text-anchor="middle" font-size="9" fill="white">and relationships</text>
                        
                        <rect x="190" y="40" width="120" height="80" fill="#e74c3c" stroke="#c0392b" stroke-width="1" rx="5"/>
                        <text x="250" y="60" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Strategic</text>
                        <text x="250" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Thinking</text>
                        <text x="250" y="95" text-anchor="middle" font-size="9" fill="white">Choose optimal</text>
                        <text x="250" y="110" text-anchor="middle" font-size="9" fill="white">technique first</text>
                        
                        <rect x="330" y="40" width="120" height="80" fill="#f39c12" stroke="#e67e22" stroke-width="1" rx="5"/>
                        <text x="390" y="60" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Algebraic</text>
                        <text x="390" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Fluency</text>
                        <text x="390" y="95" text-anchor="middle" font-size="9" fill="white">Manipulate before</text>
                        <text x="390" y="110" text-anchor="middle" font-size="9" fill="white">integrating</text>
                        
                        <rect x="470" y="40" width="120" height="80" fill="#27ae60" stroke="#229954" stroke-width="1" rx="5"/>
                        <text x="530" y="60" text-anchor="middle" font-size="11" font-weight="bold" fill="white">Persistence</text>
                        <text x="530" y="75" text-anchor="middle" font-size="11" font-weight="bold" fill="white">& Practice</text>
                        <text x="530" y="95" text-anchor="middle" font-size="9" fill="white">Try multiple</text>
                        <text x="530" y="110" text-anchor="middle" font-size="9" fill="white">approaches</text>
                    </g>
                </svg>
            </div>

            <div class="technique-box">
                <h4>🎓 Final Integration Wisdom</h4>
                <p><strong>Remember the fundamentals:</strong> Every advanced technique builds on basic integration rules and the Fundamental Theorem of Calculus.</p>
                <p><strong>Practice pattern recognition:</strong> The more integrals you solve, the faster you'll recognize which technique to use.</p>
                <p><strong>Don't force techniques:</strong> If one method seems too complicated, try a different approach.</p>
                <p><strong>Check your work:</strong> Differentiate your answer to verify it matches the original integrand.</p>
                <p><strong>Think strategically:</strong> Often the key insight is recognizing what NOT to do as much as what TO do.</p>
            </div>

            <p style="text-align: center; font-style: italic; margin-top: 30px; color: #7f8c8d;">
                "Integration is both an art and a science—it rewards both systematic thinking and creative insight."
            </p>
        </div>
    </div>
</body>
</html> 