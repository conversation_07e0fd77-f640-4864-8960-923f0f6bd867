<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MLE Asymptotic Theory Tutorial</title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <!-- Plotly.js for interactive graphs -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        :root {
            --primary-color: #1565c0;
            --secondary-color: #1976d2;
            --accent-color: #2196f3;
            --success-color: #2e7d32;
            --warning-color: #f57c00;
            --danger-color: #d32f2f;
            --light-bg: #f5f5f5;
            --card-bg: #ffffff;
            --text-primary: #212121;
            --text-secondary: #757575;
            --border-color: #e0e0e0;
            --mle-color: #673ab7;
            --stat-color: #00695c;
            --fisher-color: #e91e63;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #1565c0 0%, #673ab7 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--mle-color), var(--accent-color));
        }

        .header h1 {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: var(--text-secondary);
            font-weight: 300;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            padding: 8px 16px;
            background: var(--mle-color);
            color: white;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
        }

        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border-left: 5px solid var(--primary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .highlight-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .mle-box {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border: 2px solid var(--mle-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

                 .formula-box {
             background: #f8f9fa;
             border: 2px solid var(--accent-color);
             border-radius: 10px;
             padding: 20px;
             margin: 15px 0;
             text-align: center;
             box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);
         }

         .example-box {
             background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
             border: 2px solid var(--success-color);
             border-radius: 12px;
             padding: 25px;
             margin: 20px 0;
             position: relative;
         }

         .example-box::before {
             content: '💡';
             position: absolute;
             top: -15px;
             left: 25px;
             background: var(--card-bg);
             padding: 5px 10px;
             font-size: 1.5em;
         }

         .definition-box {
             background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
             border: 2px solid var(--warning-color);
             border-radius: 12px;
             padding: 25px;
             margin: 20px 0;
             position: relative;
         }

         .definition-box::before {
             content: '📚';
             position: absolute;
             top: -15px;
             left: 25px;
             background: var(--card-bg);
             padding: 5px 10px;
             font-size: 1.5em;
         }

         .warning-box {
             background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
             border: 2px solid var(--danger-color);
             border-radius: 12px;
             padding: 25px;
             margin: 20px 0;
             position: relative;
         }

         .warning-box::before {
             content: '⚠️';
             position: absolute;
             top: -15px;
             left: 25px;
             background: var(--card-bg);
             padding: 5px 10px;
             font-size: 1.5em;
         }

         .visualization {
             text-align: center;
             margin: 30px 0;
             padding: 20px;
             background: var(--light-bg);
             border-radius: 10px;
         }

         .step-list {
             counter-reset: step-counter;
             list-style: none;
             padding: 0;
         }

         .step-list li {
             counter-increment: step-counter;
             margin: 20px 0;
             padding: 20px;
             background: var(--light-bg);
             border-radius: 10px;
             border-left: 4px solid var(--accent-color);
             position: relative;
         }

         .step-list li::before {
             content: counter(step-counter);
             position: absolute;
             left: -15px;
             top: 15px;
             background: var(--accent-color);
             color: white;
             width: 30px;
             height: 30px;
             border-radius: 50%;
             display: flex;
             align-items: center;
             justify-content: center;
             font-weight: bold;
         }

         .navigation {
             position: fixed;
             top: 50%;
             right: 20px;
             transform: translateY(-50%);
             background: var(--card-bg);
             border-radius: 10px;
             padding: 15px;
             box-shadow: 0 10px 30px rgba(0,0,0,0.1);
             z-index: 1000;
             max-width: 200px;
         }

         .nav-link {
             display: block;
             padding: 8px 15px;
             text-decoration: none;
             color: var(--text-secondary);
             border-radius: 5px;
             margin: 5px 0;
             transition: all 0.3s ease;
             font-size: 0.9em;
         }

         .nav-link:hover {
             background: var(--primary-color);
             color: white;
         }

         .comparison-table {
             width: 100%;
             border-collapse: collapse;
             margin: 20px 0;
             background: white;
             border-radius: 8px;
             overflow: hidden;
             box-shadow: 0 4px 12px rgba(0,0,0,0.1);
         }

         .comparison-table th {
             background: var(--primary-color);
             color: white;
             padding: 12px;
             text-align: left;
             font-weight: 600;
         }

         .comparison-table td {
             padding: 12px;
             border-bottom: 1px solid var(--border-color);
         }

         .comparison-table tr:nth-child(even) {
             background: var(--light-bg);
         }

         @media (max-width: 768px) {
             .navigation {
                 display: none;
             }
             
             .header h1 {
                 font-size: 2em;
             }
             
             .section {
                 padding: 20px;
             }
         }
     </style>
 </head>
<body>
    <div class="container">
        <div class="header">
            <h1>MLE Asymptotic Theory</h1>
            <p class="subtitle">A Comprehensive Step-by-Step Tutorial</p>
            <div>
                <span class="badge">Maximum Likelihood</span>
                <span class="badge">Asymptotic Theory</span>
                <span class="badge">Fisher Information</span>
                <span class="badge">Central Limit Theorem</span>
            </div>
        </div>

                 <!-- Navigation -->
         <div class="navigation">
             <a href="#introduction" class="nav-link">Introduction</a>
             <a href="#review" class="nav-link">Score & Fisher Info</a>
             <a href="#main-result" class="nav-link">Main Result</a>
             <a href="#counterexample" class="nav-link">Counterexample</a>
             <a href="#regularity" class="nav-link">Regularity & Proof</a>
             <a href="#examples" class="nav-link">Examples</a>
             <a href="#influence" class="nav-link">Influence Functions</a>
             <a href="#efficiency" class="nav-link">Relative Efficiency</a>
             <a href="#multivariate" class="nav-link">Multivariate Case</a>
             <a href="#summary" class="nav-link">Summary</a>
         </div>

         <!-- Introduction Section -->
         <div class="section" id="introduction">
             <h2><span class="step-number">1</span>Introduction to MLE Asymptotic Theory</h2>
             
             <p>The <strong>asymptotic distribution of the Maximum Likelihood Estimator (MLE)</strong> is one of the fundamental results in statistical theory. Just as the Central Limit Theorem tells us about the asymptotic distribution of sample means, we need to understand what happens to MLEs as the sample size grows.</p>

             <div class="highlight-box">
                 <h3>The Central Question</h3>
                 <p>What is the asymptotic distribution of the MLE $\hat{\theta}_n$ as $n \to \infty$?</p>
                 <p>This question is analogous to the CLT which gave us the asymptotic distribution of averages, but MLEs are generally more complex than simple averages.</p>
             </div>

             <div class="definition-box">
                 <h3>Why This Matters</h3>
                 <p>Understanding MLE asymptotics allows us to:</p>
                 <ul>
                     <li><strong>Construct confidence intervals</strong> for parameters</li>
                     <li><strong>Perform hypothesis tests</strong> (like the Wald test)</li>
                     <li><strong>Compare different estimators</strong> via asymptotic efficiency</li>
                     <li><strong>Derive standard errors</strong> for complex models</li>
                     <li><strong>Understand robustness</strong> properties of estimators</li>
                 </ul>
             </div>

             <div class="example-box">
                 <h3>Simple Case: Bernoulli Distribution</h3>
                 <p>When $X_1, \ldots, X_n \sim \text{Ber}(p)$, the MLE is just the sample average:</p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$\hat{p} = \frac{1}{n}\sum_{i=1}^n X_i$$
                     </div>
                 </div>
                 <p>By the Central Limit Theorem, we immediately know:</p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$\frac{\sqrt{n}(\hat{p} - p)}{\sqrt{p(1-p)}} \xrightarrow{d} N(0, 1)$$
                     </div>
                 </div>
                 <p>This tells us the asymptotic distribution directly. But what about more complex cases?</p>
             </div>

             <div class="mle-box">
                 <h3>The General Insight</h3>
                 <p>The key insight is that <em>asymptotically, MLEs often behave like averages</em>, even when they're not actually averages. This is the power of asymptotic theory!</p>
                 
                 <p>We'll see that under regularity conditions:</p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$\sqrt{n}(\hat{\theta} - \theta) \xrightarrow{d} N(0, [I(\theta)]^{-1})$$
                     </div>
                 </div>
                 <p>where $I(\theta)$ is the Fisher Information—a fundamental quantity we'll review next.</p>
             </div>

             <div class="visualization">
                 <svg width="100%" height="400" viewBox="0 0 900 400">
                     <!-- Background -->
                     <defs>
                         <linearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                             <stop offset="0%" style="stop-color:#f5f5f5;stop-opacity:1" />
                             <stop offset="100%" style="stop-color:#e3f2fd;stop-opacity:1" />
                         </linearGradient>
                     </defs>
                     <rect width="900" height="400" fill="url(#bgGradient1)" rx="15"/>
                     
                     <!-- Title -->
                     <text x="450" y="30" text-anchor="middle" fill="#1565c0" font-size="18" font-weight="bold">MLE Asymptotic Theory: From Finite to Infinite Samples</text>
                     
                     <!-- Sample size progression -->
                     <text x="100" y="70" text-anchor="middle" fill="#673ab7" font-size="14" font-weight="bold">n = 10</text>
                     <ellipse cx="100" cy="120" rx="45" ry="25" fill="none" stroke="#673ab7" stroke-width="3"/>
                     <text x="100" y="125" text-anchor="middle" fill="#673ab7" font-size="10">Non-normal</text>
                     <text x="100" y="160" text-anchor="middle" fill="#673ab7" font-size="9">Large variance</text>
                     
                     <text x="300" y="70" text-anchor="middle" fill="#2196f3" font-size="14" font-weight="bold">n = 50</text>
                     <ellipse cx="300" cy="120" rx="35" ry="20" fill="none" stroke="#2196f3" stroke-width="3"/>
                     <text x="300" y="125" text-anchor="middle" fill="#2196f3" font-size="10">≈ Normal</text>
                     <text x="300" y="160" text-anchor="middle" fill="#2196f3" font-size="9">Moderate variance</text>
                     
                     <text x="500" y="70" text-anchor="middle" fill="#00695c" font-size="14" font-weight="bold">n = 200</text>
                     <ellipse cx="500" cy="120" rx="25" ry="15" fill="none" stroke="#00695c" stroke-width="3"/>
                     <text x="500" y="125" text-anchor="middle" fill="#00695c" font-size="10">Normal</text>
                     <text x="500" y="160" text-anchor="middle" fill="#00695c" font-size="9">Small variance</text>
                     
                     <text x="700" y="70" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">n → ∞</text>
                     <ellipse cx="700" cy="120" rx="15" ry="10" fill="none" stroke="#2e7d32" stroke-width="3"/>
                     <text x="700" y="125" text-anchor="middle" fill="#2e7d32" font-size="10">Normal</text>
                     <text x="700" y="160" text-anchor="middle" fill="#2e7d32" font-size="9">Variance → 1/(nI(θ))</text>
                     
                     <!-- True parameter line -->
                     <line x1="50" y1="120" x2="750" y2="120" stroke="#d32f2f" stroke-width="2" stroke-dasharray="5,5"/>
                     <text x="780" y="125" fill="#d32f2f" font-size="12" font-weight="bold">θ</text>
                     
                     <!-- Arrows showing convergence -->
                     <defs>
                         <marker id="arrow1" markerWidth="8" markerHeight="6" 
                                 refX="8" refY="3" orient="auto">
                             <polygon points="0 0, 8 3, 0 6" fill="#757575" />
                         </marker>
                     </defs>
                     <path d="M 150 120 Q 200 100 250 120" stroke="#757575" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                     <path d="M 350 120 Q 400 100 450 120" stroke="#757575" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                     <path d="M 550 120 Q 600 100 650 120" stroke="#757575" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                     
                     <!-- Key results box -->
                     <rect x="50" y="200" width="800" height="180" fill="#ffffff" rx="10" stroke="#1565c0" stroke-width="2"/>
                     <text x="450" y="225" text-anchor="middle" fill="#1565c0" font-size="16" font-weight="bold">Key Asymptotic Results</text>
                     
                     <text x="70" y="255" fill="#212121" font-size="14"><strong>Consistency:</strong> θ̂ₙ → θ (estimator converges to true value)</text>
                     <text x="70" y="280" fill="#212121" font-size="14"><strong>Asymptotic Normality:</strong> √n(θ̂ₙ - θ) → N(0, I⁻¹(θ))</text>
                     <text x="70" y="305" fill="#212121" font-size="14"><strong>Efficiency:</strong> Achieves Cramér-Rao lower bound</text>
                     <text x="70" y="330" fill="#212121" font-size="14"><strong>Robustness:</strong> Generally not robust to outliers</text>
                     
                     <text x="450" y="365" text-anchor="middle" fill="#757575" font-size="12" font-style="italic">
                         The beauty of asymptotic theory: complex estimators become simple in the limit
                     </text>
                 </svg>
             </div>
         </div>

         <!-- Review Section -->
         <div class="section" id="review">
             <h2><span class="step-number">2</span>Review: Score Function and Fisher Information</h2>
             
             <p>Before diving into the main result, let's review two fundamental concepts that are central to understanding MLE asymptotics.</p>

             <div class="definition-box">
                 <h3>Score Function</h3>
                 <p>The <strong>score function</strong> is the gradient of the log-likelihood:</p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$s(\theta) = \sum_{i=1}^n \nabla \log p(X_i; \theta)$$
                     </div>
                 </div>
                 <p><strong>Key Properties:</strong></p>
                 <ul>
                     <li>$E[s(\theta)] = 0$ (the score has mean zero)</li>
                     <li>The score is <em>data-dependent</em> (changes with the observed sample)</li>
                     <li>It measures how much the log-likelihood changes with $\theta$</li>
                 </ul>
             </div>

             <div class="definition-box">
                 <h3>Fisher Information</h3>
                 <p>The <strong>Fisher Information</strong> measures the amount of information the data carries about the parameter:</p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$I(\theta) = E[s(\theta)s(\theta)^T] = \text{Var}(s(\theta))$$
                     </div>
                 </div>
                 <p>Alternatively, it's the expected Hessian of the log-likelihood:</p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$I_n(\theta) = E\left[\sum_{i=1}^n \nabla^2 \log p(X_i; \theta)\right]$$
                     </div>
                 </div>
                 <p><strong>Key Properties:</strong></p>
                 <ul>
                     <li>Fisher Information is <em>not data-dependent</em> (it's an expectation)</li>
                     <li>Higher Fisher Information $\Rightarrow$ more precise estimation</li>
                     <li>Related to the Cramér-Rao lower bound</li>
                 </ul>
             </div>

             <div class="visualization">
                 <svg width="100%" height="350" viewBox="0 0 900 350">
                     <!-- Background -->
                     <defs>
                         <linearGradient id="bgGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                             <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                             <stop offset="100%" style="stop-color:#ffe0b2;stop-opacity:1" />
                         </linearGradient>
                     </defs>
                     <rect width="900" height="350" fill="url(#bgGradient2)" rx="15"/>
                     
                     <!-- Title -->
                     <text x="450" y="30" text-anchor="middle" fill="#1565c0" font-size="18" font-weight="bold">Score Function vs Fisher Information</text>
                     
                     <!-- Score Function -->
                     <rect x="50" y="60" width="350" height="120" fill="#ffffff" rx="10" stroke="#e91e63" stroke-width="2"/>
                     <text x="225" y="85" text-anchor="middle" fill="#e91e63" font-size="16" font-weight="bold">Score Function s(θ)</text>
                     
                     <text x="70" y="110" fill="#212121" font-size="13">• Data-dependent</text>
                     <text x="70" y="130" fill="#212121" font-size="13">• E[s(θ)] = 0</text>
                     <text x="70" y="150" fill="#212121" font-size="13">• Gradient of log-likelihood</text>
                     <text x="70" y="170" fill="#212121" font-size="13">• Random variable</text>
                     
                     <!-- Fisher Information -->
                     <rect x="500" y="60" width="350" height="120" fill="#ffffff" rx="10" stroke="#673ab7" stroke-width="2"/>
                     <text x="675" y="85" text-anchor="middle" fill="#673ab7" font-size="16" font-weight="bold">Fisher Information I(θ)</text>
                     
                     <text x="520" y="110" fill="#212121" font-size="13">• Not data-dependent</text>
                     <text x="520" y="130" fill="#212121" font-size="13">• I(θ) = Var(s(θ))</text>
                     <text x="520" y="150" fill="#212121" font-size="13">• Expected curvature</text>
                     <text x="520" y="170" fill="#212121" font-size="13">• Fixed for given θ</text>
                     
                     <!-- Relationship -->
                     <rect x="150" y="220" width="600" height="100" fill="#f3e5f5" rx="10" stroke="#9c27b0" stroke-width="2"/>
                     <text x="450" y="245" text-anchor="middle" fill="#9c27b0" font-size="16" font-weight="bold">Key Relationship</text>
                     
                     <text x="450" y="275" text-anchor="middle" fill="#212121" font-size="14">The Fisher Information is the variance of the Score Function</text>
                     <text x="450" y="295" text-anchor="middle" fill="#212121" font-size="14" font-weight="bold">I(θ) = Var(s(θ)) = E[s(θ)s(θ)ᵀ]</text>
                     
                     <!-- Arrow connecting them -->
                     <line x1="400" y1="140" x2="500" y2="140" stroke="#9c27b0" stroke-width="3" marker-end="url(#arrow1)"/>
                     <text x="450" y="135" text-anchor="middle" fill="#9c27b0" font-size="12" font-weight="bold">Variance</text>
                 </svg>
             </div>

             <div class="example-box">
                 <h3>Example: Normal Distribution</h3>
                 <p>For $X_1, \ldots, X_n \sim N(\mu, \sigma^2)$ with known $\sigma^2$:</p>
                 
                 <p><strong>Log-likelihood:</strong></p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$\ell(\mu) = -\frac{n}{2}\log(2\pi\sigma^2) - \frac{1}{2\sigma^2}\sum_{i=1}^n (X_i - \mu)^2$$
                     </div>
                 </div>
                 
                 <p><strong>Score function:</strong></p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$s(\mu) = \frac{\partial \ell}{\partial \mu} = \frac{1}{\sigma^2}\sum_{i=1}^n (X_i - \mu)$$
                     </div>
                 </div>
                 
                 <p><strong>Fisher Information:</strong></p>
                 <div class="formula-box">
                     <div class="math-display">
                         $$I(\mu) = \text{Var}(s(\mu)) = \text{Var}\left(\frac{1}{\sigma^2}\sum_{i=1}^n (X_i - \mu)\right) = \frac{n}{\sigma^2}$$
                                           </div>
                  </div>
              </div>
          </div>

          <!-- Main Result Section -->
          <div class="section" id="main-result">
              <h2><span class="step-number">3</span>The Main Asymptotic Result</h2>
              
              <p>Now we're ready to state the fundamental theorem about MLE asymptotic behavior.</p>

              <div class="mle-box">
                  <h3>Theorem: MLE Asymptotic Normality</h3>
                  <p>Under regularity conditions, the Maximum Likelihood Estimator $\hat{\theta}$ satisfies:</p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\sqrt{n}(\hat{\theta} - \theta) \xrightarrow{d} N(0, [I_1(\theta)]^{-1})$$
                      </div>
                  </div>
                  <p>where $I_1(\theta)$ is the Fisher Information for a single observation.</p>
              </div>

              <div class="highlight-box">
                  <h3>What This Means</h3>
                  <ul>
                      <li><strong>Asymptotic Normality:</strong> For large $n$, $\hat{\theta}$ is approximately normal</li>
                      <li><strong>Convergence Rate:</strong> The error $(\hat{\theta} - \theta)$ decreases at rate $1/\sqrt{n}$</li>
                      <li><strong>Optimal Variance:</strong> The asymptotic variance achieves the Cramér-Rao lower bound</li>
                      <li><strong>Practical Use:</strong> We can construct confidence intervals and perform tests</li>
                  </ul>
              </div>

              <div class="definition-box">
                  <h3>Equivalent Forms</h3>
                  <p>This result can be written in several equivalent ways:</p>
                  
                  <p><strong>Form 1 (Standardized):</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\frac{\sqrt{n}(\hat{\theta} - \theta)}{\sqrt{[I_1(\theta)]^{-1}}} \xrightarrow{d} N(0, 1)$$
                      </div>
                  </div>
                  
                  <p><strong>Form 2 (Approximate Distribution):</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\hat{\theta} \approx N\left(\theta, \frac{[I_1(\theta)]^{-1}}{n}\right)$$
                      </div>
                  </div>
                  
                  <p><strong>Form 3 (Using Total Fisher Information):</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\hat{\theta} \approx N\left(\theta, [I_n(\theta)]^{-1}\right)$$
                      </div>
                  </div>
                  <p>where $I_n(\theta) = n \cdot I_1(\theta)$ is the total Fisher Information.</p>
              </div>

              <div class="visualization">
                  <div id="mainResultPlot" style="width:100%; height:400px;"></div>
                  <script>
                      // Create interactive normal distribution plot
                      document.addEventListener('DOMContentLoaded', function() {
                          const n_values = [10, 30, 100, 300];
                          const theta_true = 0;
                          const fisher_info = 1; // I_1(theta) = 1 for simplicity
                          
                          const traces = n_values.map((n, i) => {
                              const variance = 1 / (n * fisher_info);
                              const std = Math.sqrt(variance);
                              
                              // Generate x values
                              const x = [];
                              const y = [];
                              for (let j = -4*std; j <= 4*std; j += 0.01) {
                                  x.push(theta_true + j);
                                  y.push((1/Math.sqrt(2*Math.PI*variance)) * Math.exp(-0.5*(j*j)/variance));
                              }
                              
                              const colors = ['#673ab7', '#2196f3', '#00695c', '#2e7d32'];
                              
                              return {
                                  x: x,
                                  y: y,
                                  type: 'scatter',
                                  mode: 'lines',
                                  name: `n = ${n}`,
                                  line: {
                                      color: colors[i],
                                      width: 3
                                  }
                              };
                          });
                          
                          // Add vertical line at true parameter
                          traces.push({
                              x: [theta_true, theta_true],
                              y: [0, 10],
                              type: 'scatter',
                              mode: 'lines',
                              name: 'True θ',
                              line: {
                                  color: '#d32f2f',
                                  width: 3,
                                  dash: 'dash'
                              }
                          });
                          
                          const layout = {
                              title: {
                                  text: 'MLE Asymptotic Distribution for Different Sample Sizes',
                                  font: { size: 16, color: '#1565c0' }
                              },
                              xaxis: { 
                                  title: 'θ̂ - θ', 
                                  showgrid: true,
                                  gridcolor: '#e0e0e0'
                              },
                              yaxis: { 
                                  title: 'Density',
                                  showgrid: true,
                                  gridcolor: '#e0e0e0'
                              },
                              showlegend: true,
                              legend: {
                                  x: 0.7,
                                  y: 0.95
                              },
                              plot_bgcolor: '#f8f9fa',
                              paper_bgcolor: '#ffffff',
                              margin: { l: 60, r: 60, t: 80, b: 60 }
                          };
                          
                          const config = { 
                              responsive: true,
                              displayModeBar: false
                          };
                          
                          Plotly.newPlot('mainResultPlot', traces, layout, config);
                      });
                  </script>
              </div>

              <div class="example-box">
                  <h3>Practical Application</h3>
                  <p>This result immediately gives us a way to construct confidence intervals!</p>
                  
                  <p><strong>95% Confidence Interval for $\theta$:</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\hat{\theta} \pm 1.96 \sqrt{\frac{[I_1(\theta)]^{-1}}{n}}$$
                      </div>
                  </div>
                  
                  <p>In practice, we replace $I_1(\theta)$ with $I_1(\hat{\theta})$ (the plug-in principle).</p>
                  
                  <p><strong>Standard Error:</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\text{SE}(\hat{\theta}) = \sqrt{\frac{[I_1(\hat{\theta})]^{-1}}{n}}$$
                      </div>
                  </div>
              </div>
          </div>

          <!-- Counterexample Section -->
          <div class="section" id="counterexample">
              <h2><span class="step-number">4</span>Counterexample: Uniform Distribution</h2>
              
              <p>Not all MLEs satisfy the asymptotic normality result. The uniform distribution provides a classic counterexample that shows why regularity conditions are necessary.</p>

              <div class="warning-box">
                  <h3>Problem Setup</h3>
                  <p>Consider $X_1, \ldots, X_n \sim \text{Uniform}[0, \theta]$ where we want to estimate $\theta$.</p>
                  
                  <p><strong>Log-likelihood:</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\ell(\theta) = \log\left(\frac{1}{\theta^n} \mathbf{1}(\theta \geq \max_i X_i)\right) = -n\log\theta + \text{const}$$
                      </div>
                  </div>
                  
                  <p><strong>MLE:</strong></p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\hat{\theta} = \max_{i=1,\ldots,n} X_i$$
                      </div>
                  </div>
              </div>

              <div class="warning-box">
                  <h3>What Goes Wrong</h3>
                  <ol>
                      <li><strong>Non-differentiable likelihood:</strong> The log-likelihood is not differentiable at the MLE</li>
                      <li><strong>Parameter-dependent domain:</strong> The support depends on $\theta$, violating regularity conditions</li>
                      <li><strong>Undefined Fisher Information:</strong> Can't compute Fisher Information at the MLE</li>
                  </ol>
              </div>

              <div class="definition-box">
                  <h3>Actual Asymptotic Distribution</h3>
                  <p>For the uniform distribution, we can derive the exact asymptotic distribution:</p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$n(\theta - \hat{\theta}) \xrightarrow{d} \text{Exp}(1/\theta)$$
                      </div>
                  </div>
                  
                  <p>This means:</p>
                  <div class="formula-box">
                      <div class="math-display">
                          $$\sqrt{n}(\hat{\theta} - \theta) \xrightarrow{d} \delta_0$$
                      </div>
                  </div>
                  <p>where $\delta_0$ is a point mass at zero (degenerate distribution, not normal!).</p>
              </div>

              <div class="visualization">
                  <svg width="100%" height="450" viewBox="0 0 900 450">
                      <!-- Background -->
                      <defs>
                          <linearGradient id="bgGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" style="stop-color:#ffebee;stop-opacity:1" />
                              <stop offset="100%" style="stop-color:#ffcdd2;stop-opacity:1" />
                          </linearGradient>
                      </defs>
                      <rect width="900" height="450" fill="url(#bgGradient3)" rx="15"/>
                      
                      <!-- Title -->
                      <text x="450" y="30" text-anchor="middle" fill="#d32f2f" font-size="18" font-weight="bold">Uniform Distribution: When MLE Theory Breaks Down</text>
                      
                      <!-- Regular MLE vs Uniform MLE -->
                      <rect x="50" y="60" width="350" height="160" fill="#ffffff" rx="10" stroke="#2e7d32" stroke-width="2"/>
                      <text x="225" y="85" text-anchor="middle" fill="#2e7d32" font-size="16" font-weight="bold">Regular MLE</text>
                      
                      <text x="70" y="110" fill="#212121" font-size="13">• Smooth likelihood</text>
                      <text x="70" y="130" fill="#212121" font-size="13">• Differentiable at MLE</text>
                      <text x="70" y="150" fill="#212121" font-size="13">• Fisher Info well-defined</text>
                      <text x="70" y="170" fill="#212121" font-size="13">• Domain independent of θ</text>
                      <text x="70" y="190" fill="#212121" font-size="13">• √n(θ̂-θ) → N(0, I⁻¹)</text>
                      <text x="70" y="210" fill="#212121" font-size="13">• Rate: 1/√n</text>
                      
                      <rect x="500" y="60" width="350" height="160" fill="#ffffff" rx="10" stroke="#d32f2f" stroke-width="2"/>
                      <text x="675" y="85" text-anchor="middle" fill="#d32f2f" font-size="16" font-weight="bold">Uniform MLE</text>
                      
                      <text x="520" y="110" fill="#212121" font-size="13">• Non-smooth likelihood</text>
                      <text x="520" y="130" fill="#212121" font-size="13">• Non-differentiable at MLE</text>
                      <text x="520" y="150" fill="#212121" font-size="13">• Fisher Info undefined</text>
                      <text x="520" y="170" fill="#212121" font-size="13">• Domain depends on θ</text>
                      <text x="520" y="190" fill="#212121" font-size="13">• n(θ-θ̂) → Exp(1/θ)</text>
                      <text x="520" y="210" fill="#212121" font-size="13">• Rate: 1/n (faster!)</text>
                      
                      <!-- Likelihood comparison -->
                      <rect x="100" y="250" width="700" height="150" fill="#f5f5f5" rx="10" stroke="#757575" stroke-width="1"/>
                      <text x="450" y="275" text-anchor="middle" fill="#1565c0" font-size="16" font-weight="bold">Key Insight: Regularity Conditions Matter</text>
                      
                      <text x="120" y="305" fill="#212121" font-size="14"><strong>Why the uniform fails:</strong></text>
                      <text x="140" y="325" fill="#212121" font-size="13">1. Support [0,θ] depends on parameter θ</text>
                      <text x="140" y="345" fill="#212121" font-size="13">2. Can't exchange derivative and integral</text>
                      <text x="140" y="365" fill="#212121" font-size="13">3. MLE occurs at boundary of likelihood surface</text>
                      <text x="140" y="385" fill="#212121" font-size="13">4. Information grows faster than √n rate</text>
                      
                      <!-- VS indicator -->
                      <circle cx="450" cy="140" r="25" fill="#ffc107"/>
                      <text x="450" y="135" text-anchor="middle" fill="#212121" font-size="12" font-weight="bold">VS</text>
                      <text x="450" y="150" text-anchor="middle" fill="#212121" font-size="10">Regular</text>
                      <text x="450" y="160" text-anchor="middle" fill="#212121" font-size="10">Irregular</text>
                  </svg>
              </div>

              <div class="example-box">
                  <h3>Lesson Learned</h3>
                  <p>The uniform distribution counterexample teaches us that:</p>
                  <ul>
                      <li><strong>Regularity conditions are essential</strong> for the standard MLE theory</li>
                      <li><strong>Different distributions can have different convergence rates</strong> (here 1/n vs 1/√n)</li>
                      <li><strong>Parameter-dependent domains are problematic</strong> for standard asymptotic theory</li>
                      <li><strong>Exponential families are well-behaved</strong> and automatically satisfy regularity conditions</li>
                  </ul>
                             </div>
           </div>

           <!-- Regularity Conditions and Proof Section -->
           <div class="section" id="regularity">
               <h2><span class="step-number">5</span>Regularity Conditions and Proof Sketch</h2>
               
               <p>Now let's understand what conditions are needed for the main asymptotic result and see a sketch of why it works.</p>

               <div class="definition-box">
                   <h3>Sufficient Regularity Conditions</h3>
                   <ol class="step-list">
                       <li><strong>Fixed Dimension:</strong> The parameter space dimension $d$ does not change with $n$</li>
                       <li><strong>Smoothness:</strong> $p(x; \theta)$ is thrice differentiable with respect to $\theta$</li>
                       <li><strong>Interchangeability:</strong> Can interchange differentiation and integration (domain doesn't depend on $\theta$)</li>
                       <li><strong>Identifiability:</strong> Different parameter values give different distributions</li>
                       <li><strong>Interior Parameter:</strong> $\theta$ is in the interior of the parameter space (not on boundary)</li>
                   </ol>
               </div>

               <div class="mle-box">
                   <h3>Proof Sketch (One-Dimensional Case)</h3>
                   
                   <p><strong>Step 1: Consistency</strong></p>
                   <p>Under regularity conditions, we can show $\hat{\theta} \xrightarrow{p} \theta$ (the MLE is consistent).</p>
                   
                   <p><strong>Step 2: Score Expansion</strong></p>
                   <p>Since $\hat{\theta}$ maximizes the likelihood, we have $\ell'(\hat{\theta}) = 0$. By Taylor expansion:</p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$0 = \ell'(\hat{\theta}) = \ell'(\theta) + (\hat{\theta} - \theta)\ell''(\tilde{\theta})$$
                       </div>
                   </div>
                   <p>where $\tilde{\theta}$ is between $\hat{\theta}$ and $\theta$.</p>
                   
                   <p><strong>Step 3: Rearrange</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$(\hat{\theta} - \theta) = \frac{\ell'(\theta)}{-\ell''(\tilde{\theta})}$$
                       </div>
                   </div>
                   
                   <p><strong>Step 4: Scale and Analyze Parts</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\sqrt{n}(\hat{\theta} - \theta) = \frac{\ell'(\theta)/\sqrt{n}}{-\ell''(\tilde{\theta})/n}$$
                       </div>
                   </div>
               </div>

               <div class="highlight-box">
                   <h3>Key Insights from the Proof</h3>
                   
                   <p><strong>Numerator Analysis:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\frac{1}{\sqrt{n}}\ell'(\theta) = \frac{1}{\sqrt{n}}\sum_{i=1}^n \nabla \log p(X_i; \theta) \xrightarrow{d} N(0, I(\theta))$$
                       </div>
                   </div>
                   <p>This follows from the CLT since the score has mean 0 and variance $I(\theta)$.</p>
                   
                   <p><strong>Denominator Analysis:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$-\frac{\ell''(\tilde{\theta})}{n} \xrightarrow{p} I(\theta)$$
                       </div>
                   </div>
                   <p>This follows from the Law of Large Numbers and consistency of $\hat{\theta}$.</p>
                   
                   <p><strong>Slutsky's Theorem:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\sqrt{n}(\hat{\theta} - \theta) = \frac{N(0, I(\theta)) + o_p(1)}{I(\theta) + o_p(1)} \xrightarrow{d} N(0, I(\theta)^{-1})$$
                       </div>
                   </div>
               </div>

               <div class="visualization">
                   <svg width="100%" height="500" viewBox="0 0 900 500">
                       <!-- Background -->
                       <defs>
                           <linearGradient id="bgGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                               <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                               <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                           </linearGradient>
                       </defs>
                       <rect width="900" height="500" fill="url(#bgGradient4)" rx="15"/>
                       
                       <!-- Title -->
                       <text x="450" y="30" text-anchor="middle" fill="#2e7d32" font-size="18" font-weight="bold">MLE Asymptotic Theory: Proof Roadmap</text>
                       
                       <!-- Flow diagram -->
                       <!-- Step 1 -->
                       <rect x="50" y="60" width="200" height="80" fill="#ffffff" rx="10" stroke="#2e7d32" stroke-width="2"/>
                       <text x="150" y="85" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Step 1: Consistency</text>
                       <text x="150" y="105" text-anchor="middle" fill="#212121" font-size="12">θ̂ → θ</text>
                       <text x="150" y="125" text-anchor="middle" fill="#212121" font-size="11">(WLLN + Compactness)</text>
                       
                       <!-- Arrow 1 -->
                       <path d="M 250 100 L 300 100" stroke="#2e7d32" stroke-width="3" marker-end="url(#arrow1)"/>
                       
                       <!-- Step 2 -->
                       <rect x="320" y="60" width="200" height="80" fill="#ffffff" rx="10" stroke="#2e7d32" stroke-width="2"/>
                       <text x="420" y="85" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Step 2: Score = 0</text>
                       <text x="420" y="105" text-anchor="middle" fill="#212121" font-size="12">ℓ'(θ̂) = 0</text>
                       <text x="420" y="125" text-anchor="middle" fill="#212121" font-size="11">(First-order condition)</text>
                       
                       <!-- Arrow 2 -->
                       <path d="M 520 100 L 570 100" stroke="#2e7d32" stroke-width="3" marker-end="url(#arrow1)"/>
                       
                       <!-- Step 3 -->
                       <rect x="590" y="60" width="200" height="80" fill="#ffffff" rx="10" stroke="#2e7d32" stroke-width="2"/>
                       <text x="690" y="85" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Step 3: Taylor Expand</text>
                       <text x="690" y="105" text-anchor="middle" fill="#212121" font-size="12">ℓ'(θ̂) ≈ ℓ'(θ) + ℓ''(θ̃)(θ̂-θ)</text>
                       <text x="690" y="125" text-anchor="middle" fill="#212121" font-size="11">(Mean Value Theorem)</text>
                       
                       <!-- Down arrow -->
                       <path d="M 450 150 L 450 200" stroke="#2e7d32" stroke-width="3" marker-end="url(#arrow1)"/>
                       
                       <!-- Step 4 -->
                       <rect x="350" y="220" width="200" height="80" fill="#ffffff" rx="10" stroke="#673ab7" stroke-width="2"/>
                       <text x="450" y="245" text-anchor="middle" fill="#673ab7" font-size="14" font-weight="bold">Step 4: Isolate θ̂-θ</text>
                       <text x="450" y="265" text-anchor="middle" fill="#212121" font-size="12">θ̂-θ = ℓ'(θ)/(-ℓ''(θ̃))</text>
                       <text x="450" y="285" text-anchor="middle" fill="#212121" font-size="11">(Rearrange equation)</text>
                       
                       <!-- Down arrow -->
                       <path d="M 450 310 L 450 360" stroke="#673ab7" stroke-width="3" marker-end="url(#arrow1)"/>
                       
                       <!-- Step 5 -->
                       <rect x="100" y="380" width="300" height="80" fill="#ffffff" rx="10" stroke="#1565c0" stroke-width="2"/>
                       <text x="250" y="405" text-anchor="middle" fill="#1565c0" font-size="14" font-weight="bold">Step 5: Numerator</text>
                       <text x="250" y="425" text-anchor="middle" fill="#212121" font-size="12">ℓ'(θ)/√n → N(0, I(θ))</text>
                       <text x="250" y="445" text-anchor="middle" fill="#212121" font-size="11">(Central Limit Theorem)</text>
                       
                       <!-- Step 6 -->
                       <rect x="500" y="380" width="300" height="80" fill="#ffffff" rx="10" stroke="#1565c0" stroke-width="2"/>
                       <text x="650" y="405" text-anchor="middle" fill="#1565c0" font-size="14" font-weight="bold">Step 6: Denominator</text>
                       <text x="650" y="425" text-anchor="middle" fill="#212121" font-size="12">-ℓ''(θ̃)/n → I(θ)</text>
                       <text x="650" y="445" text-anchor="middle" fill="#212121" font-size="11">(Law of Large Numbers)</text>
                       
                       <!-- Final arrow pointing to result -->
                       <path d="M 250 470 Q 450 490 650 470" stroke="#d32f2f" stroke-width="4" fill="none"/>
                       <text x="450" y="485" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Slutsky's Theorem</text>
                   </svg>
               </div>
           </div>

           <!-- Examples Section -->
           <div class="section" id="examples">
               <h2><span class="step-number">6</span>Detailed Examples</h2>
               
               <p>Let's work through several concrete examples to see the theory in action.</p>

               <div class="example-box">
                   <h3>Example 1: Exponential Distribution</h3>
                   <p>Consider $X_1, \ldots, X_n \sim \text{Exp}(\theta)$ with density $p(x; \theta) = \theta e^{-\theta x}$.</p>
                   
                   <p><strong>Log-likelihood:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\ell(\theta) = n\log\theta - \theta\sum_{i=1}^n X_i$$
                       </div>
                   </div>
                   
                   <p><strong>Score function:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$s(\theta) = \frac{n}{\theta} - \sum_{i=1}^n X_i$$
                       </div>
                   </div>
                   
                   <p><strong>Fisher Information:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$I(\theta) = \text{Var}(s(\theta)) = \text{Var}\left(\frac{n}{\theta} - \sum_{i=1}^n X_i\right) = \frac{n}{\theta^2}$$
                       </div>
                   </div>
                   
                   <p><strong>MLE:</strong> Setting $s(\theta) = 0$ gives $\hat{\theta} = \frac{n}{\sum_{i=1}^n X_i} = \frac{1}{\bar{X}}$</p>
                   
                   <p><strong>Asymptotic Distribution:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\hat{\theta} \approx N\left(\theta, \frac{\theta^2}{n}\right)$$
                       </div>
                   </div>
               </div>

               <div class="example-box">
                   <h3>Example 2: Poisson Distribution</h3>
                   <p>Consider $X_1, \ldots, X_n \sim \text{Poisson}(\lambda)$.</p>
                   
                   <p><strong>Log-likelihood:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\ell(\lambda) = \sum_{i=1}^n [X_i \log\lambda - \lambda - \log(X_i!)]$$
                       </div>
                   </div>
                   
                   <p><strong>Score function:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$s(\lambda) = \frac{\sum_{i=1}^n X_i}{\lambda} - n$$
                       </div>
                   </div>
                   
                   <p><strong>Fisher Information:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$I(\lambda) = \frac{n}{\lambda}$$
                       </div>
                   </div>
                   
                   <p><strong>MLE:</strong> $\hat{\lambda} = \bar{X}$</p>
                   
                   <p><strong>Asymptotic Distribution:</strong></p>
                   <div class="formula-box">
                       <div class="math-display">
                           $$\hat{\lambda} \approx N\left(\lambda, \frac{\lambda}{n}\right)$$
                       </div>
                   </div>
               </div>

               <div class="visualization">
                   <div id="examplesPlot" style="width:100%; height:400px;"></div>
                   <script>
                       document.addEventListener('DOMContentLoaded', function() {
                           // Compare true vs asymptotic distributions for different examples
                           const n = 100;
                           
                           // Exponential example
                           const theta = 2;
                           const exp_var = theta*theta/n;
                           const exp_std = Math.sqrt(exp_var);
                           
                           // Poisson example  
                           const lambda = 5;
                           const pois_var = lambda/n;
                           const pois_std = Math.sqrt(pois_var);
                           
                           const x_exp = [], y_exp = [];
                           const x_pois = [], y_pois = [];
                           
                           // Generate normal approximations
                           for (let i = -4; i <= 4; i += 0.01) {
                               // Exponential
                               const x_val_exp = theta + i * exp_std;
                               const y_val_exp = (1/Math.sqrt(2*Math.PI*exp_var)) * Math.exp(-0.5*(i*i));
                               x_exp.push(x_val_exp);
                               y_exp.push(y_val_exp);
                               
                               // Poisson
                               const x_val_pois = lambda + i * pois_std;
                               const y_val_pois = (1/Math.sqrt(2*Math.PI*pois_var)) * Math.exp(-0.5*(i*i));
                               x_pois.push(x_val_pois);
                               y_pois.push(y_val_pois);
                           }
                           
                           const traces = [
                               {
                                   x: x_exp,
                                   y: y_exp,
                                   type: 'scatter',
                                   mode: 'lines',
                                   name: 'Exponential MLE',
                                   line: { color: '#673ab7', width: 3 }
                               },
                               {
                                   x: x_pois,
                                   y: y_pois,
                                   type: 'scatter',
                                   mode: 'lines',
                                   name: 'Poisson MLE',
                                   line: { color: '#2196f3', width: 3 }
                               }
                           ];
                           
                           const layout = {
                               title: {
                                   text: 'Asymptotic Distributions of MLEs (n=100)',
                                   font: { size: 16, color: '#1565c0' }
                               },
                               xaxis: { title: 'Parameter Value' },
                               yaxis: { title: 'Density' },
                               showlegend: true,
                               plot_bgcolor: '#f8f9fa',
                               paper_bgcolor: '#ffffff'
                           };
                           
                           Plotly.newPlot('examplesPlot', traces, layout, {responsive: true, displayModeBar: false});
                       });
                   </script>
               </div>

               <div class="comparison-table">
                   <table>
                       <tr>
                           <th>Distribution</th>
                           <th>Parameter</th>
                           <th>MLE</th>
                           <th>Fisher Information</th>
                           <th>Asymptotic Variance</th>
                       </tr>
                       <tr>
                           <td>Bernoulli</td>
                           <td>$p$</td>
                           <td>$\bar{X}$</td>
                           <td>$\frac{n}{p(1-p)}$</td>
                           <td>$\frac{p(1-p)}{n}$</td>
                       </tr>
                       <tr>
                           <td>Normal (known $\sigma^2$)</td>
                           <td>$\mu$</td>
                           <td>$\bar{X}$</td>
                           <td>$\frac{n}{\sigma^2}$</td>
                           <td>$\frac{\sigma^2}{n}$</td>
                       </tr>
                       <tr>
                           <td>Exponential</td>
                           <td>$\theta$</td>
                           <td>$\frac{1}{\bar{X}}$</td>
                           <td>$\frac{n}{\theta^2}$</td>
                           <td>$\frac{\theta^2}{n}$</td>
                       </tr>
                       <tr>
                           <td>Poisson</td>
                           <td>$\lambda$</td>
                           <td>$\bar{X}$</td>
                           <td>$\frac{n}{\lambda}$</td>
                           <td>$\frac{\lambda}{n}$</td>
                       </tr>
                                       </table>
                </div>
            </div>

            <!-- Influence Functions Section -->
            <div class="section" id="influence">
                <h2><span class="step-number">7</span>Influence Functions and Robustness</h2>
                
                <p>Understanding how individual observations affect the MLE is crucial for assessing robustness.</p>

                <div class="definition-box">
                    <h3>Influence Function</h3>
                    <p>The MLE can be written as:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\hat{\theta} = \theta + \frac{1}{n}\sum_{i=1}^n \psi(X_i) + \text{Remainder}$$
                        </div>
                    </div>
                    <p>where the <strong>influence function</strong> is:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\psi(x) = \frac{\nabla \log p(x; \theta)}{I(\theta)}$$
                        </div>
                    </div>
                    <p>This measures how much each observation $X_i$ influences the final estimate.</p>
                </div>

                <div class="warning-box">
                    <h3>Robustness Properties</h3>
                    <p>An estimator is considered <strong>robust</strong> if the influence function $\psi(x)$ is bounded.</p>
                    
                    <p><strong>Problem with MLEs:</strong> For most distributions, $\psi(x)$ is unbounded!</p>
                    
                    <p><strong>Example: Normal Distribution</strong></p>
                    <p>For $X_1, \ldots, X_n \sim N(\mu, \sigma^2)$ with known $\sigma^2$:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\hat{\mu} = \mu + \frac{1}{n}\sum_{i=1}^n (X_i - \mu)$$
                        </div>
                    </div>
                    <p>The influence of observation $X_i$ is $(X_i - \mu)$, which is <em>unbounded</em>. A single corrupted observation can make $\hat{\mu}$ arbitrarily bad!</p>
                </div>

                <div class="visualization">
                    <svg width="100%" height="400" viewBox="0 0 900 400">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bgGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="900" height="400" fill="url(#bgGradient5)" rx="15"/>
                        
                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" fill="#1565c0" font-size="18" font-weight="bold">Influence Functions: How Individual Observations Affect MLEs</text>
                        
                        <!-- Normal case -->
                        <rect x="50" y="60" width="380" height="280" fill="#ffffff" rx="10" stroke="#2e7d32" stroke-width="2"/>
                        <text x="240" y="85" text-anchor="middle" fill="#2e7d32" font-size="16" font-weight="bold">Normal Distribution Case</text>
                        
                        <!-- Scatter plot showing influence -->
                        <rect x="70" y="100" width="340" height="200" fill="#f8f9fa" stroke="#e0e0e0"/>
                        
                        <!-- Data points -->
                        <circle cx="120" cy="200" r="4" fill="#2196f3"/>
                        <circle cx="140" cy="190" r="4" fill="#2196f3"/>
                        <circle cx="160" cy="195" r="4" fill="#2196f3"/>
                        <circle cx="180" cy="205" r="4" fill="#2196f3"/>
                        <circle cx="200" cy="200" r="4" fill="#2196f3"/>
                        <circle cx="220" cy="198" r="4" fill="#2196f3"/>
                        <circle cx="240" cy="202" r="4" fill="#2196f3"/>
                        <circle cx="260" cy="195" r="4" fill="#2196f3"/>
                        <circle cx="280" cy="190" r="4" fill="#2196f3"/>
                        
                        <!-- Outlier -->
                        <circle cx="350" cy="130" r="6" fill="#d32f2f"/>
                        <text x="365" y="135" fill="#d32f2f" font-size="12" font-weight="bold">Outlier</text>
                        
                        <!-- MLE without outlier -->
                        <line x1="200" y1="120" x2="200" y2="280" stroke="#2e7d32" stroke-width="3" stroke-dasharray="5,5"/>
                        <text x="210" y="130" fill="#2e7d32" font-size="11">MLE without outlier</text>
                        
                        <!-- MLE with outlier -->
                        <line x1="250" y1="120" x2="250" y2="280" stroke="#d32f2f" stroke-width="3"/>
                        <text x="260" y="130" fill="#d32f2f" font-size="11">MLE with outlier</text>
                        
                        <text x="240" y="325" text-anchor="middle" fill="#212121" font-size="13">One outlier can shift the MLE significantly!</text>
                        
                        <!-- Robust case -->
                        <rect x="470" y="60" width="380" height="280" fill="#ffffff" rx="10" stroke="#673ab7" stroke-width="2"/>
                        <text x="660" y="85" text-anchor="middle" fill="#673ab7" font-size="16" font-weight="bold">Robust Alternative</text>
                        
                        <!-- Similar plot but with bounded influence -->
                        <rect x="490" y="100" width="340" height="200" fill="#f8f9fa" stroke="#e0e0e0"/>
                        
                        <!-- Data points -->
                        <circle cx="540" cy="200" r="4" fill="#2196f3"/>
                        <circle cx="560" cy="190" r="4" fill="#2196f3"/>
                        <circle cx="580" cy="195" r="4" fill="#2196f3"/>
                        <circle cx="600" cy="205" r="4" fill="#2196f3"/>
                        <circle cx="620" cy="200" r="4" fill="#2196f3"/>
                        <circle cx="640" cy="198" r="4" fill="#2196f3"/>
                        <circle cx="660" cy="202" r="4" fill="#2196f3"/>
                        <circle cx="680" cy="195" r="4" fill="#2196f3"/>
                        <circle cx="700" cy="190" r="4" fill="#2196f3"/>
                        
                        <!-- Same outlier -->
                        <circle cx="770" cy="130" r="6" fill="#d32f2f"/>
                        <text x="785" y="135" fill="#d32f2f" font-size="12" font-weight="bold">Outlier</text>
                        
                        <!-- Robust estimator barely moves -->
                        <line x1="620" y1="120" x2="620" y2="280" stroke="#2e7d32" stroke-width="3" stroke-dasharray="5,5"/>
                        <text x="525" y="130" fill="#2e7d32" font-size="11">Robust estimator</text>
                        <text x="525" y="145" fill="#2e7d32" font-size="11">without outlier</text>
                        
                        <line x1="630" y1="120" x2="630" y2="280" stroke="#673ab7" stroke-width="3"/>
                        <text x="635" y="130" fill="#673ab7" font-size="11">Robust estimator</text>
                        <text x="635" y="145" fill="#673ab7" font-size="11">with outlier</text>
                        
                        <text x="660" y="325" text-anchor="middle" fill="#212121" font-size="13">Bounded influence function resists outliers</text>
                    </svg>
                </div>

                <div class="example-box">
                    <h3>Real-World Implications</h3>
                    <p>The non-robustness of MLEs has important practical consequences:</p>
                    <ul>
                        <li><strong>Data Quality:</strong> MLEs are sensitive to data entry errors and measurement mistakes</li>
                        <li><strong>Model Assumptions:</strong> Violations of distributional assumptions can severely affect MLEs</li>
                        <li><strong>Outlier Detection:</strong> A few extreme observations can dominate the analysis</li>
                        <li><strong>Alternative Approaches:</strong> Consider robust estimators like M-estimators or trimmed means</li>
                    </ul>
                </div>
            </div>

            <!-- Asymptotic Relative Efficiency Section -->
            <div class="section" id="efficiency">
                <h2><span class="step-number">8</span>Asymptotic Relative Efficiency</h2>
                
                <p>How do we compare different estimators? Asymptotic Relative Efficiency (ARE) provides a standardized way to compare estimator performance in large samples.</p>

                <div class="definition-box">
                    <h3>Asymptotic Relative Efficiency</h3>
                    <p>For two estimators $W_n$ and $V_n$ of the same parameter $\tau(\theta)$:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\sqrt{n}(W_n - \tau(\theta)) \xrightarrow{d} N(0, \sigma_W^2)$$
                        </div>
                    </div>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\sqrt{n}(V_n - \tau(\theta)) \xrightarrow{d} N(0, \sigma_V^2)$$
                        </div>
                    </div>
                    <p>The <strong>ARE</strong> of $V_n$ relative to $W_n$ is:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\text{ARE}(V_n, W_n) = \frac{\sigma_W^2}{\sigma_V^2}$$
                        </div>
                    </div>
                </div>

                <div class="example-box">
                    <h3>Example: Estimating P(X = 0) for Poisson Distribution</h3>
                    <p>Let $X_1, \ldots, X_n \sim \text{Poisson}(\lambda)$ and $\tau = P(X_i = 0) = e^{-\lambda}$.</p>
                    
                    <p><strong>Estimator 1:</strong> Simple proportion</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$W_n = \frac{1}{n}\sum_{i=1}^n \mathbf{1}(X_i = 0)$$
                        </div>
                    </div>
                    <p>Asymptotic variance: $\sigma_W^2 = e^{-\lambda}(1 - e^{-\lambda})$</p>
                    
                    <p><strong>Estimator 2:</strong> MLE-based</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$V_n = e^{-\hat{\lambda}} = e^{-\bar{X}}$$
                        </div>
                    </div>
                    <p>By delta method: $\sigma_V^2 = \lambda e^{-2\lambda}$</p>
                    
                    <p><strong>ARE Calculation:</strong></p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\text{ARE}(V_n, W_n) = \frac{e^{-\lambda}(1 - e^{-\lambda})}{\lambda e^{-2\lambda}} = \frac{e^{\lambda} - 1}{\lambda} \leq 1$$
                        </div>
                    </div>
                    <p>The MLE-based estimator is always more efficient!</p>
                </div>

                <div class="visualization">
                    <div id="efficiencyPlot" style="width:100%; height:400px;"></div>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // Plot ARE as function of lambda
                            const lambda_values = [];
                            const are_values = [];
                            
                            for (let lambda = 0.1; lambda <= 5; lambda += 0.1) {
                                lambda_values.push(lambda);
                                const are = (Math.exp(lambda) - 1) / lambda;
                                are_values.push(are);
                            }
                            
                            const trace = {
                                x: lambda_values,
                                y: are_values,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'ARE(MLE, Simple)',
                                line: { color: '#673ab7', width: 4 }
                            };
                            
                            // Add reference line at y=1
                            const reference = {
                                x: [0, 5],
                                y: [1, 1],
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Equal Efficiency',
                                line: { color: '#d32f2f', width: 2, dash: 'dash' }
                            };
                            
                            const layout = {
                                title: {
                                    text: 'Asymptotic Relative Efficiency: MLE vs Simple Proportion',
                                    font: { size: 16, color: '#1565c0' }
                                },
                                xaxis: { 
                                    title: 'λ (Poisson parameter)',
                                    range: [0, 5]
                                },
                                yaxis: { 
                                    title: 'ARE(MLE, Simple)',
                                    range: [0, 2.5]
                                },
                                showlegend: true,
                                plot_bgcolor: '#f8f9fa',
                                paper_bgcolor: '#ffffff',
                                annotations: [{
                                    x: 2.5,
                                    y: 0.5,
                                    text: 'MLE is always more efficient<br>(ARE ≤ 1)',
                                    showarrow: true,
                                    arrowhead: 2,
                                    arrowsize: 1,
                                    arrowwidth: 2,
                                    arrowcolor: '#673ab7'
                                }]
                            };
                            
                            Plotly.newPlot('efficiencyPlot', [trace, reference], layout, {responsive: true, displayModeBar: false});
                        });
                    </script>
                </div>

                <div class="highlight-box">
                    <h3>Key Insights About ARE</h3>
                    <ul>
                        <li><strong>MLE Optimality:</strong> Among regular asymptotically linear estimators, the MLE achieves the lowest possible asymptotic variance</li>
                        <li><strong>Comparison Tool:</strong> ARE provides a single number to compare estimator efficiency</li>
                        <li><strong>Large Sample Property:</strong> ARE is about asymptotic behavior—finite sample performance may differ</li>
                        <li><strong>Le Cam's Result:</strong> Any "reasonable" estimator has ARE ≤ 1 relative to the MLE</li>
                    </ul>
                </div>
            </div>

            <!-- Multivariate Case Section -->
            <div class="section" id="multivariate">
                <h2><span class="step-number">9</span>Multivariate Case and Delta Method</h2>
                
                <p>The theory extends naturally to multiple parameters, opening up powerful applications.</p>

                <div class="mle-box">
                    <h3>Multivariate MLE Asymptotic Result</h3>
                    <p>For $\theta = (\theta_1, \ldots, \theta_k)$, the MLE satisfies:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\sqrt{n}(\hat{\theta} - \theta) \xrightarrow{d} N(0, I^{-1}(\theta))$$
                        </div>
                    </div>
                    <p>where $I(\theta)$ is the $k \times k$ Fisher Information Matrix:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$I_{jl}(\theta) = E\left[-\frac{\partial^2}{\partial \theta_j \partial \theta_l} \log p(X; \theta)\right]$$
                        </div>
                    </div>
                    
                    <p><strong>Standard Errors:</strong> The approximate standard error of $\hat{\theta}_j$ is:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\text{SE}(\hat{\theta}_j) = \sqrt{\frac{[I^{-1}(\hat{\theta})]_{jj}}{n}}$$
                        </div>
                    </div>
                </div>

                <div class="definition-box">
                    <h3>Delta Method</h3>
                    <p>For a function $\tau = g(\theta)$ where $g: \mathbb{R}^k \to \mathbb{R}$:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\sqrt{n}(\hat{\tau} - \tau) \xrightarrow{d} N(0, (g')^T I^{-1}(\theta) g')$$
                        </div>
                    </div>
                    <p>where $g'$ is the gradient of $g$ evaluated at $\theta$ and $\hat{\tau} = g(\hat{\theta})$.</p>
                    
                    <p><strong>Standard Error:</strong></p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\text{SE}(\hat{\tau}) = \sqrt{\frac{(g')^T I^{-1}(\hat{\theta}) g'}{n}}$$
                        </div>
                    </div>
                </div>

                <div class="example-box">
                    <h3>Example: Normal Distribution with Both Parameters Unknown</h3>
                    <p>For $X_1, \ldots, X_n \sim N(\mu, \sigma^2)$ with both $\mu$ and $\sigma^2$ unknown:</p>
                    
                    <p><strong>MLEs:</strong></p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\hat{\mu} = \bar{X}, \quad \hat{\sigma}^2 = \frac{1}{n}\sum_{i=1}^n (X_i - \bar{X})^2$$
                        </div>
                    </div>
                    
                    <p><strong>Fisher Information Matrix:</strong></p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$I(\mu, \sigma^2) = n \begin{pmatrix} 
                            \frac{1}{\sigma^2} & 0 \\
                            0 & \frac{1}{2\sigma^4}
                            \end{pmatrix}$$
                        </div>
                    </div>
                    
                    <p><strong>Asymptotic Distribution:</strong></p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\sqrt{n} \begin{pmatrix} \hat{\mu} - \mu \\ \hat{\sigma}^2 - \sigma^2 \end{pmatrix} \xrightarrow{d} N\left(0, \begin{pmatrix} \sigma^2 & 0 \\ 0 & 2\sigma^4 \end{pmatrix}\right)$$
                        </div>
                    </div>
                    
                    <p><strong>Delta Method Application:</strong> For the coefficient of variation $\tau = \sigma/\mu$:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$g'(\mu, \sigma^2) = \begin{pmatrix} -\frac{\sigma}{\mu^2} \\ \frac{1}{2\mu\sigma} \end{pmatrix}$$
                        </div>
                    </div>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\text{Var}(\hat{\tau}) \approx \frac{1}{n} \cdot \frac{\sigma^2}{2\mu^2}\left(1 + 2\left(\frac{\sigma}{\mu}\right)^2\right)$$
                        </div>
                    </div>
                </div>

                <div class="visualization">
                    <svg width="100%" height="350" viewBox="0 0 900 350">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bgGradient6" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#e1f5fe;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#b3e5fc;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="900" height="350" fill="url(#bgGradient6)" rx="15"/>
                        
                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" fill="#1565c0" font-size="18" font-weight="bold">Multivariate MLE Theory: From Parameters to Functions</text>
                        
                        <!-- Parameter space -->
                        <rect x="50" y="60" width="250" height="200" fill="#ffffff" rx="10" stroke="#1565c0" stroke-width="2"/>
                        <text x="175" y="85" text-anchor="middle" fill="#1565c0" font-size="14" font-weight="bold">Parameter Space</text>
                        
                        <!-- 2D parameter visualization -->
                        <ellipse cx="175" cy="160" rx="60" ry="40" fill="none" stroke="#2196f3" stroke-width="3"/>
                        <circle cx="175" cy="160" r="3" fill="#d32f2f"/>
                        <text x="185" y="165" fill="#d32f2f" font-size="12">θ</text>
                        
                        <text x="125" y="140" fill="#212121" font-size="11">θ₁</text>
                        <text x="180" y="200" fill="#212121" font-size="11">θ₂</text>
                        
                        <text x="175" y="240" text-anchor="middle" fill="#212121" font-size="12">Joint asymptotic</text>
                        <text x="175" y="255" text-anchor="middle" fill="#212121" font-size="12">normality</text>
                        
                        <!-- Arrow -->
                        <path d="M 300 160 L 350 160" stroke="#673ab7" stroke-width="4" marker-end="url(#arrow1)"/>
                        <text x="325" y="150" text-anchor="middle" fill="#673ab7" font-size="12" font-weight="bold">Delta Method</text>
                        
                        <!-- Function space -->
                        <rect x="370" y="60" width="250" height="200" fill="#ffffff" rx="10" stroke="#673ab7" stroke-width="2"/>
                        <text x="495" y="85" text-anchor="middle" fill="#673ab7" font-size="14" font-weight="bold">Function Space</text>
                        
                        <!-- 1D function visualization -->
                        <ellipse cx="495" cy="160" rx="80" ry="20" fill="none" stroke="#9c27b0" stroke-width="3"/>
                        <circle cx="495" cy="160" r="3" fill="#d32f2f"/>
                        <text x="505" y="165" fill="#d32f2f" font-size="12">τ = g(θ)</text>
                        
                        <text x="495" y="240" text-anchor="middle" fill="#212121" font-size="12">Derived asymptotic</text>
                        <text x="495" y="255" text-anchor="middle" fill="#212121" font-size="12">normality</text>
                        
                        <!-- Arrow -->
                        <path d="M 620 160 L 670 160" stroke="#2e7d32" stroke-width="4" marker-end="url(#arrow1)"/>
                        <text x="645" y="150" text-anchor="middle" fill="#2e7d32" font-size="12" font-weight="bold">Applications</text>
                        
                        <!-- Applications -->
                        <rect x="690" y="60" width="160" height="200" fill="#ffffff" rx="10" stroke="#2e7d32" stroke-width="2"/>
                        <text x="770" y="85" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Uses</text>
                        
                        <text x="710" y="110" fill="#212121" font-size="11">• Confidence intervals</text>
                        <text x="710" y="130" fill="#212121" font-size="11">• Hypothesis tests</text>
                        <text x="710" y="150" fill="#212121" font-size="11">• Standard errors</text>
                        <text x="710" y="170" fill="#212121" font-size="11">• Model comparisons</text>
                        <text x="710" y="190" fill="#212121" font-size="11">• Prediction intervals</text>
                        <text x="710" y="210" fill="#212121" font-size="11">• Goodness of fit</text>
                        <text x="710" y="230" fill="#212121" font-size="11">• Variable selection</text>
                        
                        <!-- Formula at bottom -->
                        <text x="450" y="300" text-anchor="middle" fill="#1565c0" font-size="14" font-weight="bold">
                            Key Formula: Var(ĝ(θ̂)) ≈ (g')ᵀ I⁻¹(θ) g' / n
                        </text>
                    </svg>
                </div>
            </div>

            <!-- Summary Section -->
            <div class="section" id="summary">
                <h2><span class="step-number">10</span>Summary and Key Takeaways</h2>
                
                <p>We've covered the fundamental theory of MLE asymptotics. Let's consolidate the key insights and practical implications.</p>

                <div class="mle-box">
                    <h3>The Big Picture</h3>
                    <p>MLE asymptotic theory provides a unified framework for understanding estimator behavior in large samples. The key result:</p>
                    <div class="formula-box">
                        <div class="math-display">
                            $$\sqrt{n}(\hat{\theta} - \theta) \xrightarrow{d} N(0, I^{-1}(\theta))$$
                        </div>
                    </div>
                    <p>This simple statement has profound implications for statistical practice.</p>
                </div>

                <div class="comparison-table">
                    <table>
                        <tr>
                            <th>Property</th>
                            <th>Description</th>
                            <th>Practical Implication</th>
                        </tr>
                        <tr>
                            <td><strong>Consistency</strong></td>
                            <td>$\hat{\theta} \xrightarrow{p} \theta$</td>
                            <td>Larger samples give better estimates</td>
                        </tr>
                        <tr>
                            <td><strong>Asymptotic Normality</strong></td>
                            <td>$\hat{\theta} \approx N(\theta, \sigma^2/n)$</td>
                            <td>Can use normal-based confidence intervals</td>
                        </tr>
                        <tr>
                            <td><strong>Efficiency</strong></td>
                            <td>Achieves Cramér-Rao bound</td>
                            <td>No regular estimator has smaller variance</td>
                        </tr>
                        <tr>
                            <td><strong>Rate $1/\sqrt{n}$</strong></td>
                            <td>Standard error decreases as $1/\sqrt{n}$</td>
                            <td>Need 4× data for 2× precision</td>
                        </tr>
                        <tr>
                            <td><strong>Non-robustness</strong></td>
                            <td>Unbounded influence function</td>
                            <td>Sensitive to outliers and model violations</td>
                        </tr>
                    </table>
                </div>

                <div class="highlight-box">
                    <h3>When to Use MLE Asymptotic Theory</h3>
                    <ul>
                        <li><strong>Large samples</strong> (typically $n \geq 30$, better if $n \geq 100$)</li>
                        <li><strong>Regular parametric models</strong> (exponential families are ideal)</li>
                        <li><strong>Interior parameters</strong> (not on boundary of parameter space)</li>
                        <li><strong>Well-specified models</strong> (distributional assumptions approximately correct)</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <h3>When to Be Careful</h3>
                    <ul>
                        <li><strong>Small samples:</strong> Asymptotic approximations may be poor</li>
                        <li><strong>Boundary parameters:</strong> Different asymptotic behavior (e.g., variance components)</li>
                        <li><strong>Model misspecification:</strong> MLEs may be inconsistent</li>
                        <li><strong>Heavy-tailed distributions:</strong> Slower convergence, poor finite-sample performance</li>
                        <li><strong>Presence of outliers:</strong> Consider robust alternatives</li>
                    </ul>
                </div>

                <div class="definition-box">
                    <h3>Practical Workflow</h3>
                    <ol class="step-list">
                        <li><strong>Check regularity conditions</strong> for your model</li>
                        <li><strong>Compute the MLE</strong> $\hat{\theta}$ (numerically if necessary)</li>
                        <li><strong>Calculate Fisher Information</strong> $I(\hat{\theta})$ or use observed information</li>
                        <li><strong>Compute standard errors</strong> $\text{SE}(\hat{\theta}_j) = \sqrt{[I^{-1}(\hat{\theta})]_{jj}/n}$</li>
                        <li><strong>Use delta method</strong> for functions of parameters</li>
                        <li><strong>Construct confidence intervals</strong> and perform tests</li>
                        <li><strong>Check assumptions</strong> and consider robustness</li>
                    </ol>
                </div>

                <div class="visualization">
                    <svg width="100%" height="500" viewBox="0 0 900 500">
                        <!-- Background -->
                        <defs>
                            <linearGradient id="bgGradient7" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <rect width="900" height="500" fill="url(#bgGradient7)" rx="15"/>
                        
                        <!-- Title -->
                        <text x="450" y="30" text-anchor="middle" fill="#673ab7" font-size="20" font-weight="bold">MLE Asymptotic Theory: The Complete Picture</text>
                        
                        <!-- Central theorem -->
                        <rect x="300" y="60" width="300" height="80" fill="#ffffff" rx="10" stroke="#673ab7" stroke-width="3"/>
                        <text x="450" y="85" text-anchor="middle" fill="#673ab7" font-size="16" font-weight="bold">Central Result</text>
                        <text x="450" y="110" text-anchor="middle" fill="#212121" font-size="14">√n(θ̂ - θ) → N(0, I⁻¹(θ))</text>
                        <text x="450" y="130" text-anchor="middle" fill="#212121" font-size="12">(under regularity conditions)</text>
                        
                        <!-- Prerequisites -->
                        <rect x="50" y="180" width="200" height="120" fill="#ffffff" rx="10" stroke="#2196f3" stroke-width="2"/>
                        <text x="150" y="205" text-anchor="middle" fill="#2196f3" font-size="14" font-weight="bold">Prerequisites</text>
                        <text x="70" y="230" fill="#212121" font-size="11">• Regularity conditions</text>
                        <text x="70" y="245" fill="#212121" font-size="11">• Large sample size</text>
                        <text x="70" y="260" fill="#212121" font-size="11">• Correct model</text>
                        <text x="70" y="275" fill="#212121" font-size="11">• Interior parameters</text>
                        <text x="70" y="290" fill="#212121" font-size="11">• Clean data</text>
                        
                        <!-- Tools -->
                        <rect x="300" y="180" width="300" height="120" fill="#ffffff" rx="10" stroke="#00695c" stroke-width="2"/>
                        <text x="450" y="205" text-anchor="middle" fill="#00695c" font-size="14" font-weight="bold">Theoretical Tools</text>
                        <text x="320" y="230" fill="#212121" font-size="11">• Score function: E[s(θ)] = 0</text>
                        <text x="320" y="245" fill="#212121" font-size="11">• Fisher Information: I(θ) = Var(s(θ))</text>
                        <text x="320" y="260" fill="#212121" font-size="11">• Central Limit Theorem</text>
                        <text x="320" y="275" fill="#212121" font-size="11">• Taylor expansions</text>
                        <text x="320" y="290" fill="#212121" font-size="11">• Slutsky's theorem</text>
                        
                        <!-- Applications -->
                        <rect x="650" y="180" width="200" height="120" fill="#ffffff" rx="10" stroke="#f57c00" stroke-width="2"/>
                        <text x="750" y="205" text-anchor="middle" fill="#f57c00" font-size="14" font-weight="bold">Applications</text>
                        <text x="670" y="230" fill="#212121" font-size="11">• Confidence intervals</text>
                        <text x="670" y="245" fill="#212121" font-size="11">• Hypothesis testing</text>
                        <text x="670" y="260" fill="#212121" font-size="11">• Standard errors</text>
                        <text x="670" y="275" fill="#212121" font-size="11">• Delta method</text>
                        <text x="670" y="290" fill="#212121" font-size="11">• Model comparison</text>
                        
                        <!-- Extensions -->
                        <rect x="50" y="340" width="200" height="120" fill="#ffffff" rx="10" stroke="#e91e63" stroke-width="2"/>
                        <text x="150" y="365" text-anchor="middle" fill="#e91e63" font-size="14" font-weight="bold">Extensions</text>
                        <text x="70" y="390" fill="#212121" font-size="11">• Multivariate parameters</text>
                        <text x="70" y="405" fill="#212121" font-size="11">• Constrained parameters</text>
                        <text x="70" y="420" fill="#212121" font-size="11">• Semiparametric models</text>
                        <text x="70" y="435" fill="#212121" font-size="11">• Time series</text>
                        <text x="70" y="450" fill="#212121" font-size="11">• Missing data</text>
                        
                        <!-- Limitations -->
                        <rect x="300" y="340" width="300" height="120" fill="#ffffff" rx="10" stroke="#d32f2f" stroke-width="2"/>
                        <text x="450" y="365" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">Limitations & Alternatives</text>
                        <text x="320" y="390" fill="#212121" font-size="11">• Non-robustness → Robust estimators</text>
                        <text x="320" y="405" fill="#212121" font-size="11">• Small samples → Exact methods</text>
                        <text x="320" y="420" fill="#212121" font-size="11">• Model uncertainty → Model averaging</text>
                        <text x="320" y="435" fill="#212121" font-size="11">• Computation → Approximate Bayesian</text>
                        <text x="320" y="450" fill="#212121" font-size="11">• Complex models → Bootstrap</text>
                        
                        <!-- Modern developments -->
                        <rect x="650" y="340" width="200" height="120" fill="#ffffff" rx="10" stroke="#673ab7" stroke-width="2"/>
                        <text x="750" y="365" text-anchor="middle" fill="#673ab7" font-size="14" font-weight="bold">Modern Topics</text>
                        <text x="670" y="390" fill="#212121" font-size="11">• High-dimensional MLE</text>
                        <text x="670" y="405" fill="#212121" font-size="11">• Machine learning</text>
                        <text x="670" y="420" fill="#212121" font-size="11">• Computational methods</text>
                        <text x="670" y="435" fill="#212121" font-size="11">• Causal inference</text>
                        <text x="670" y="450" fill="#212121" font-size="11">• Deep learning theory</text>
                        
                        <!-- Connecting arrows -->
                        <path d="M 150 180 Q 225 155 300 140" stroke="#2196f3" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                        <path d="M 450 180 L 450 140" stroke="#00695c" stroke-width="2" marker-end="url(#arrow1)"/>
                        <path d="M 600 140 Q 675 155 750 180" stroke="#f57c00" stroke-width="2" fill="none" marker-end="url(#arrow1)"/>
                        
                        <path d="M 150 340 Q 225 315 300 300" stroke="#e91e63" stroke-width="2" fill="none"/>
                        <path d="M 450 300 L 450 340" stroke="#d32f2f" stroke-width="2"/>
                        <path d="M 600 300 Q 675 315 750 340" stroke="#673ab7" stroke-width="2" fill="none"/>
                    </svg>
                </div>

                <div class="mle-box">
                    <h3>Final Thoughts</h3>
                    <p>MLE asymptotic theory is a cornerstone of modern statistics. It provides:</p>
                    <ul>
                        <li><strong>Theoretical foundation</strong> for understanding estimator properties</li>
                        <li><strong>Practical tools</strong> for inference in complex models</li>
                        <li><strong>Unified framework</strong> that works across many distributions</li>
                        <li><strong>Bridge to advanced topics</strong> in statistical theory</li>
                    </ul>
                    
                    <p>While MLEs aren't perfect (non-robust, may be biased in small samples), their optimal asymptotic properties make them the default choice for parametric inference. Understanding when and how to apply this theory is essential for any practicing statistician.</p>
                    
                    <p><em>Remember: All models are wrong, but some are useful. MLE asymptotic theory helps us extract maximum value from our imperfect models!</em></p>
                </div>
            </div>
        </div>
    </body>
    </html>