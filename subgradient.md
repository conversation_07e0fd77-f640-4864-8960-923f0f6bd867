Subgradients
<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> for EE364b, Stanford University, Spring 2021-22
April 13, 2022
1 Definition
We say a vector g ∈ R
n
is a subgradient of f : R
n → R at x ∈ dom f if for all z ∈ dom f,
f(z) ≥ f(x) + g
T
(z − x). (1)
If f is convex and differentiable, then its gradient at x is a subgradient. But a subgradient
can exist even when f is not differentiable at x, as illustrated in figure 1. The same example
shows that there can be more than one subgradient of a function f at a point x.
There are several ways to interpret a subgradient. A vector g is a subgradient of f at x
if the affine function (of z) f(x) + g
T
(z − x) is a global underestimator of f. Geometrically,
g is a subgradient of f at x if (g, −1) supports epi f at (x, f(x)), as illustrated in figure 2.
A function f is called subdifferentiable at x if there exists at least one subgradient at
x. The set of subgradients of f at the point x is called the subdifferential of f at x, and
is denoted ∂f(x). A function f is called subdifferentiable if it is subdifferentiable at all
x ∈ dom f.
Example. Absolute value. Consider f(z) = |z|. For x < 0 the subgradient is unique:
∂f(x) = {−1}. Similarly, for x > 0 we have ∂f(x) = {1}. At x = 0 the subdifferential
is defined by the inequality |z| ≥ gz for all z, which is satisfied if and only if g ∈ [−1, 1].
Therefore we have ∂f(0) = [−1, 1]. This is illustrated in figure 3.
2 Basic properties
The subdifferential ∂f(x) is always a closed convex set, even if f is not convex. This follows
from the fact that it is the intersection of an infinite set of halfspaces:
∂f(x) = \
z∈dom f
{g | f(z) ≥ f(x) + g
T
(z − x)}.
In addition, if f is continuous at x, then the subdifferential ∂f(x) is bounded. Indeed, choose
some  > 0 such that that −∞ < f ≤ f(y) ≤ f < ∞ for all y ∈ R
n
such that ky − xk2 ≤ .
1
x1 x2
g1
g2
g3
f
Figure 1: At x1, the convex function f is differentiable, and g1 (which is the
derivative of f at x1) is the unique subgradient at x1. At the point x2, f is not
differentiable. At this point, f has many subgradients: two subgradients, g2 and g3,
are shown.
g
e
Figure 2: A vector g ∈ Rn
is a subgradient of f at x if and only if (g, −1) defines
a supporting hyperplane to epi f at (x, f(x)).
x1
f
x2
s
y1
y2
Figure 3: The absolute value function (left), and its subdifferential ∂f(x) as a
function of x (right).
2
If ∂f(x) is unbounded, then there is a sequence gn ∈ ∂f(x) such that kgnk2 → ∞. Taking the
sequence yn = x+gn/ kgnk2
, we find that f(yn) ≥ f(x) +g
T
n
(yn −x) = f(x) + kgnk2 → ∞,
which is a contradiction to f(yn) being bounded.
2.1 Existence of subgradients
If f is convex and x ∈ int dom f, then ∂f(x) is nonempty and bounded. To establish that
∂f(x) 6= ∅, we apply the supporting hyperplane theorem to the convex set epi f at the
boundary point (x, f(x)), to conclude the existence of a ∈ R
n
and b ∈ R, not both zero,
such that

a
b
T  z
t

−

x
f(x)
 = a
T
(z − x) + b(t − f(x)) ≤ 0
for all (z, t) ∈ epi f. This implies b ≤ 0, and that
a
T
(z − x) + b(f(z) − f(x)) ≤ 0
for all z. If b 6= 0, we can divide by b to obtain
f(z) ≥ f(x) − (a/b)
T
(z − x),
which shows that −a/b ∈ ∂f(x). Now we show that b 6= 0, i.e., that the supporting
hyperplane cannot be vertical. If b = 0 we conclude that a
T
(z − x) ≤ 0 for all z ∈ dom f.
This is impossible since x ∈ int dom f.
This discussion shows that a convex function has a subgradient at x if there is at least
one nonvertical supporting hyperplane to epi f at (x, f(x)). This is the case, for example, if
f is continuous. There are pathological convex functions which do not have subgradients at
some points, but we will assume in the sequel that all convex functions are subdifferentiable
(at every point in dom f).
2.2 Subgradients of differentiable functions
If f is convex and differentiable at x, then ∂f(x) = {∇f(x)}, i.e., its gradient is its only
subgradient. Conversely, if f is convex and ∂f(x) = {g}, then f is differentiable at x and
g = ∇f(x).
2.3 The minimum of a nondifferentiable function
A point x
?
is a minimizer of a function f (not necessarily convex) if and only if f is subdifferentiable at x
? and
0 ∈ ∂f(x
?
),
i.e., g = 0 is a subgradient of f at x
?
. This follows directly from the fact that f(x) ≥ f(x
?
)
for all x ∈ dom f. And clearly if f is subdifferentiable at x
? with 0 ∈ ∂f(x
?
), then f(x) ≥
f(x
?
) + 0T
(x − x
?
) = f(x
?
) for all x.
3
While this simple characterization of optimality via the subdifferential holds for nonconvex functions, it is not particularly useful in that case, since we generally cannot find the
subdifferential of a nonconvex function.
The condition 0 ∈ ∂f(x
?
) reduces to ∇f(x
?
) = 0 when f is convex and differentiable at
x
?
.
2.4 Directional derivatives and subgradients
For convex functions f, the directional derivative of f at the point x ∈ R
n
in the direction
v is
f
0
(x; v)
∆= lim
t&0
f(x + tv) − f(x)
t
.
This quantity always exists for convex f, though it may be +∞ or −∞. To see the existence
of the limit, we use that the ratio (f(x+tv)−f(x))/t is non-decreasing in t. For 0 < t1 ≤ t2,
we have 0 ≤ t1/t2 ≤ 1, and
f(x + t1v) − f(x)
t1
=
f(
t1
t2
(x + t2v) + (1 −
t1
t2
)x) − f(x)
t1
≤
t1
t2
f(x + t2v)
t1
+
(1 −
t1
t2
)f(x) − f(x)
t1
=
f(x + t2v) − f(x)
t2
,
so the limit in the definition of f
0
(x; v) exists.
The directional derivative f
0
(x; v) possesses several interesting properties as well. First,
it is convex in v, and if f is finite in a neighborhood of x, then f
0
(x; v) exists. Additionally,
f is differentiable at x if and only if for some g (which is ∇f(x)) and all v ∈ R
n we have
f
0
(x; v) = g
T
v, that is, if and only if f
0
(x; v) is a linear function of v.
1 For general convex f,
f
0
(x; v) is positively homogeneous in v, meaning that for α ≥ 0, we have f
0
(x; αv) = αf0
(x; v)
(replace t by t/α in the defining limit).
The directional derivative f
0
(x; v) satisfies the following general formula for convex f:
f
0
(x; v) = sup
g∈∂f(x)
g
T
v. (2)
To see this inequality, note that f
0
(x; v) ≥ supg∈∂f(x)
g
T
v by the definition of a subgradient:
f(x + tv) − f(x) ≥ tgT
v for any t ∈ R and g ∈ ∂f(x), so f
0
(x; v) ≥ supg∈∂f(x)
g
T
v. For the
other direction, we claim that all affine functions that are below the function v 7→ f
0
(x; v)
may be taken to be linear. Specifically, suppose that (g, r) ∈ R
n × R and g
T
v − r ≤ f
0
(x; v)
for all v. Then r ≥ 0, as taking v = 0 gives −r ≤ f
0
(x; 0) = 0. By the positive homogeneity
of f
0
(x; v), we see that for any t ≥ 0 we have tgT
v − r ≤ f
0
(x;tv) = tf0
(x; v), and thus we
have
g
T
v −
r
t
≤ f
0
(x; v) for all t > 0.
1This is simply the standard definition of differentiability.
4
x
?
y
−g
X
Figure 4: The point x
? minimizes f over X (the shown level curves) if and only if
for some g ∈ ∂f(x
?
), g
T
(y − x
?
) ≥ 0 for all y ∈ X. Note that not all subgradients
satisfy this inequality.
Taking t → +∞ gives that any affine minorizer of f
0
(x; v) may be taken to be linear. As any
(closed) convex function can be written as the supremum of its affine minorants, we have
f
0
(x; v) = sup 
g
T
v | g
T ∆ ≤ f
0
(x; ∆) for all ∆ ∈ R
n
	
.
On the other hand, if g
T ∆ ≤ f
0
(x; ∆) for all ∆ ∈ R
n
, then we have g
T ∆ ≤ f(x+∆)−f(x), so
that g ∈ ∂f(x), and we may as well have taken the preceding supremum only over g ∈ ∂f(x).
2.5 Constrained minimizers of nondifferentiable functions
There is a somewhat more complex version of the result that 0 ∈ ∂f(x) if and only if x minimizes f for constrained minimization. Consider finding the minimizer of a subdifferentiable
function f over a (closed) convex set X. Then we have that x
? minimizes f if and only if
there exists a subgradient g ∈ ∂f(x
?
) such that
g
T
(y − x
?
) ≥ 0 for all y ∈ X.
See Fig. 4 for an illustration of this condition.
To see this result, first suppose that g ∈ ∂f(x
?
) satisfies the preceding condition. Then
by definition, f(x) ≥ f(x
?
)+g
T
(x−x
?
) ≥ f(x
?
) for x ∈ X. The converse is more subtle, and
we show it under the assumption that x
? ∈ int dom f, though x
? may be on the boundary of
5
X. We suppose that f(x) ≥ f(x
?
) for all x ∈ X. In this case, for any x ∈ X, the directional
derivative
f
0
(x
?
; x − x
?
) = lim
t&0
f(x
? + t(x − x
?
)) − f(x
?
)
t
≥ 0,
that is, for any x, the direction ∆ = x − x
? pointing into X satisfies f
0
(x
?
; ∆) ≥ 0.
By our characterization of the directional derivative earlier, we know that f
0
(x
?
; ∆) =
supg∈∂f(x?)
g
T ∆ ≥ 0. Thus, defining the ball B = {y + x
? ∈ R
n
| kyk2 ≤ }, we have
inf
x∈X∩B
sup
g∈∂f(x?)
g
T
(x − x
?
) ≥ 0.
As ∂f(x
?
) is bounded, we may swap the min and max (see, for example, Exercise 5.25 of
[BV04]), finding that there must exist some g ∈ ∂f(x
?
) such that
inf
x∈X∩B
g
T
(x − x
?
) ≥ 0.
But any y ∈ X may be written as t(x − x
?
) + x
?
for some t ≥ 0 and x ∈ X ∩ B
, which gives
the result.
For fuller explanations of these inequalities and derivations, see also the books by HiriartUrruty and Lemar´echal [HUL93, HUL01].
3 Calculus of subgradients
In this section we describe rules for constructing subgradients of convex functions. We
will distinguish two levels of detail. In the ‘weak’ calculus of subgradients the goal is to
produce one subgradient, even if more subgradients exist. This is sufficient in practice, since
subgradient, localization, and cutting-plane methods require only a subgradient at any point.
A second and much more difficult task is to describe the complete set of subgradients
∂f(x) as a function of x. We will call this the ‘strong’ calculus of subgradients. It is useful
in theoretical investigations, for example, when describing the precise optimality conditions.
3.1 Nonnegative scaling
For α ≥ 0, ∂(αf)(x) = α∂f(x).
3.2 Sum and integral
Suppose f = f1 + · · · + fm, where f1, . . . , fm are convex functions. Then we have
∂f(x) = ∂f1(x) + · · · + ∂fm(x).
This property extends to infinite sums, integrals, and expectations (provided they exist).
6
3.3 Affine transformations of domain
Suppose f is convex, and let h(x) = f(Ax + b). Then ∂h(x) = AT ∂f(Ax + b).
3.4 Pointwise maximum
Suppose f is the pointwise maximum of convex functions f1, . . . , fm, i.e.,
f(x) = max
i=1,...,m
fi(x),
where the functions fi are subdifferentiable. We first show how to construct a subgradient
of f at x.
Let k be any index for which fk(x) = f(x), and let g ∈ ∂fk(x). Then g ∈ ∂f(x). In other
words, to find a subgradient of the maximum of functions, we can choose one of the functions
that achieves the maximum at the point, and choose any subgradient of that function at the
point. This follows from
f(z) ≥ fk(z) ≥ fk(x) + g
T
(z − x) = f(x) + g
T
(y − x).
More generally, we have
∂f(x) = Co ∪ {∂fi(x) | fi(x) = f(x)},
i.e., the subdifferential of the maximum of functions is the convex hull of the union of
subdifferentials of the ‘active’ functions at x.
Example. Maximum of differentiable functions. Suppose f(x) = maxi=1,...,m fi(x),
where fi are convex and differentiable. Then we have
∂f(x) = Co{∇fi(x) | fi(x) = f(x)}.
At a point x where only one of the functions, say fk, is active, f is differentiable and
has gradient ∇fk(x). At a point x where several of the functions are active, ∂f(x) is
a polyhedron.
Example. `1-norm. The `1-norm
f(x) = kxk1 = |x1| + · · · + |xn|
is a nondifferentiable convex function of x. To find its subgradients, we note that f
can expressed as the maximum of 2n
linear functions:
kxk1 = max{s
T x | si ∈ {−1, 1}},
so we can apply the rules for the subgradient of the maximum. The first step is to
identify an active function s
T x, i.e., find an s ∈ {−1, +1}
n
such that s
T x = kxk1. We
can choose si = +1 if xi > 0, and si = −1 if xi < 0. If xi = 0, more than one function
7
is active, and both si = +1, and si = −1 work. The function s
T x is differentiable and
has a unique subgradient s. We can therefore take
gi =



+1 xi > 0
−1 xi < 0
−1 or + 1 xi = 0.
The subdifferential is the convex hull of all subgradients that can be generated this
way:
∂f(x) = {g | kgk∞ ≤ 1, gT x = kxk1}.
3.5 Supremum
Next we consider the extension to the supremum over an infinite number of functions, i.e.,
we consider
f(x) = sup
α∈A
fα(x),
where the functions fα are subdifferentiable. We only discuss the weak property.
Suppose the supremum in the definition of f(x) is attained. Let β ∈ A be an index for
which fβ(x) = f(x), and let g ∈ ∂fβ(x). Then g ∈ ∂f(x). If the supremum in the definition
is not attained, the function may or may not be subdifferentiable at x, depending on the
index set A.
Assume however that A is compact (in some metric), and that the function α 7→ fα(x)
is upper semi-continuous for each x. Then
∂f(x) = Co ∪ {∂fα(x) | fα(x) = f(x)}.
Example. Maximum eigenvalue of a symmetric matrix. Let f(x) = λmax(A(x)),
where A(x) = A0 + x1A1 + · · · + xnAn, and Ai ∈ S
m. We can express f as the
pointwise supremum of convex functions,
f(x) = λmax(A(x)) = sup
kyk2=1
y
T A(x)y.
Here the index set A is A = {y ∈ Rn
| ky2k1 ≤ 1}.
Each of the functions fy(x) = y
T A(x)y is affine in x for fixed y, as can be easily seen
from
y
T A(x)y = y
T A0y + x1y
T A1y + · · · + xny
T Any,
so it is differentiable with gradient ∇fy(x) = (y
T A1y, . . . , yT Any).
The active functions y
T A(x)y are those associated with the eigenvectors corresponding
to the maximum eigenvalue. Hence to find a subgradient, we compute an eigenvector
y with eigenvalue λmax, normalized to have unit norm, and take
g = (y
T A1y, yT A2y, . . . , yT Any).
The ‘index set’ in this example is {y | kyk = 1} is a compact set. Therefore
∂f(x) = Co {∇fy | A(x)y = λmax(A(x))y, kyk = 1} .
8
Example. Maximum eigenvalue of a symmetric matrix, revisited. Let f(A) = λmax(A),
where A ∈ S
n
, the symmetric n-by-n matrices. Then as above, f(A) = λmax(A) =
supkyk2=1 y
T Ay, but we note that y
T Ay = Tr(AyyT
), so that each of the functions
fy(A) = y
T Ay is linear in A with gradient ∇fy(A) = yyT
. Then using an identical
argument to that above, we find that
∂f(A) = Co 
yyT
| kyk2 = 1, yT Ay = λmax(A)
	
= Co 
yyT
| kyk2 = 1, Ay = λmax(A)y
	
,
the convex hull of the outer products of maximum eigenvectors of the matrix A.
3.6 Minimization over some variables
The next subgradient calculus rule concerns functions of the form
f(x) = inf
y
F(x, y)
where F(x, y) is subdifferentiable and jointly convex in x ∈ R
n
and y ∈ R
m.
Suppose that the infimum over y in the definition of f(x) is attained on the set Yx ⊂ R
m
(where Yx 6= ∅), so that F(x, y) = f(x) for y ∈ Yx. By definition, a vector g ∈ R
n
is a
subgradient of f if and only if
f(x
0
) ≥ f(x) + g
T
(x
0 − x) = F(x, y) + g
T
(x
0 − x)
for all x
0 ∈ R
n
and any y ∈ Yx. This is equivalent to
F(x
0
, y0
) ≥ F(x, y) + g
T
(x
0 − x) = F(x, y) + 
g
0
T x
0
y
0

−

x
y

for all (x
0
, y0
) ∈ R
n × R
m and x, y ∈ Yx. In particular, we have the result that
∂f(x) = {g ∈ R
n
| (g, 0) ∈ ∂F(x, y) for some y ∈ Yx} .
That is, there exist g ∈ R
n
such that (g, 0) ∈ ∂F(x, y) for some y ∈ Yx, and any such g is a
subgradient of f at x (as long as the infimum is attained and x ∈ int dom f).
3.7 Optimal value function of a convex optimization problem
Suppose f : R
m × R
p → R is defined as the optimal value of a convex optimization problem
in standard form, with z ∈ R
n
as optimization variable,
minimize f0(z)
subject to fi(z) ≤ xi
, i = 1, . . . , m
Az = y.
(3)
In other words, f(x, y) = infz F(x, y, z) where
F(x, y, z) = 
f0(z) fi(z) ≤ xi
, i = 1, . . . , m, Az = y
+∞ otherwise,
9
which is jointly convex in x, y, z. Subgradients of f can be related to the dual problem
of (3) as follows.
Suppose we are interested in subdifferentiating f at (ˆx, yˆ). We can express the dual
problem of (3) as
maximize g(λ) − x
T λ − y
T
ν
subject to λ  0.
(4)
where
g(λ) = inf
z

f0(z) +Xm
i=1
λifi(z) + ν
TAz!
.
Suppose strong duality holds for problems (3) and (4) at x = ˆx and y = ˆy, and that the
dual optimum is attained at λ
?
, ν
?
(for example, because Slater’s condition holds). From
the global perturbation inequalities we know that
f(x, y) ≥ f(ˆx, yˆ) − λ
?T
(x − xˆ) − ν
?T
(y − yˆ)
In other words, the dual optimal solution provides a subgradient:
−(λ
?
, ν?
) ∈ ∂f(ˆx, yˆ).
4 Quasigradients
If f(x) is quasiconvex, then g is a quasigradient at x0 if
g
T
(x − x0) ≥ 0 ⇒ f(x) ≥ f(x0),
Geometrically, g defines a supporting hyperplane to the sublevel set {x | f(x) ≤ f(x0)}.
Note that the set of quasigradients at x0 form a cone.
Example. Linear fractional function. f(x) = a
T x+b
c
T x+d
. Let c
T x0 + d > 0. Then
g = a − f(x0)c is a quasigradient at x0. If c
T x + d > 0, we have
a
T
(x − x0) ≥ f(x0)c
T
(x − x0) =⇒ f(x) ≥ f(x0).
Example. Degree of a polynomial. Define f : Rn → R by
f(a) = min{i | ai+2 = · · · = an = 0},
i.e., the degree of the polynomial a1 + a2t + · · · + ant
n−1
. Let a 6= 0, and k = f(a),
then g = sign(ak+1)ek+1 is a quasigradient at a
To see this, we note that
g
T
(b − a) = sign(ak+1)bk+1 − |ak+1| ≥ 0
implies bk+1 6= 0.
10
5 Clarke Subdifferential
Now we explore a generalization of the notion of subdifferential that enables the analysis of
non-convex and non-smooth functions through convex analysis. We will introduce Clarke
Subdifferential, which is a natural generalization [Cla90] of the subdifferential set in terms
of convex hulls.
5.1 Locally Lipschitz Functions
As we move beyond convex functions, an important class of functions is locally Lipschitz
functions. Let us recall the definition:
Definition 1. A function f : R
n → R is locally Lipschitz if for any bounded S ⊆ R
n
, there
exists a constant L > 0 such that
|f(x) − f(y)| ≤ Lkx − yk2 for all x, y ∈ S.
A well-known result due to Rademacher states that a locally Lipschitz function is differentiable almost everywhere (see e.g., Theorem 9.60 in [RW09]). In particular, every neighborhood of x contains a point y for which ∇f(y) exists. This motivates the following construction
known as the Clarke subdifferential
∂Cf(x) = Con
s ∈ R
n
: ∃x
k → x, ∇f(x
k
) exists, and ∇f(x
k
) → s
o
.
We can check that the absolute value function f(x) = |x| satisfies ∇Cf(0) = [−1, 1]. Likewise, the function −f(x) satisfies ∇C(−f(0)) = [−1, 1]. For convex functions, we will see
that the Clarke subdifferential reduces to the ordinary subdifferential defined in Section 1.
5.2 Clarke Directional Derivative
Remarkably, it can be shown that Clarke subdifferentials can by described by support functions even for non-convex functions. In order to show this, we need to generalize the notion
of directional derivatives from Section 2.4. We now define the Clarke directional derivative
of f at x in the direction d as follows
f
◦
(x, d)
∆= lim sup
x0→x, t&0
f(x
0 + td) − f(x
0
)
t
. (5)
Compared to the usual directional derivative (2), Clarke directional derivative (5) is able to
capture the behavior of the function in a neighborbood of x rather than just along a ray
emanating from x.
We have the following generalization of (2)
f
◦
(x, d) = max
s∈∇C f(x)
s
T
d,
11
>0,
wise.
derivative
g( 1
x ))
 this and
s locally
e of f at
 quotient
e, as can
(n+ 1
2 )⇡
s even,
wise.
f directional
 of view. The
anating from
with tk & 0),
the direction
k+ tkd) vs.
 the latter is
d of x rather
y, f (x, d) is
eighborhood
 very fruitful
 functions.
difference in
ons and non subdifferend; in the nonnvexification
nvex analysis
ions.
veral properossess. Now,
ssesses those
continuously
x)T d for all
Propositionin general. Consider the function f : R ! R given by
f(x) = max{x, 0}+min{0, x}. Let us compute @C f1(0),
@C f2(0), and @C f(0):
f(x) =f1(x)+f2(x)
@C f(0) = {1}
f2(x) = min{x,0}
@C f1(0) = [0,1] @C f2(0) = [0,1]
f1(x) = max{x,0}
Observe that
@C f(0) = {1} ( @C f1(0) + @C f2(0) = [0, 2].
The failure of the sum rule is one of the obstacles to
computing the Clarke subgradient. Nevertheless, not all
is lost, as we still have the following weaker version of
the sum rule:
@C (f1 + f2) ✓ @C f1 + @C f2;
see [28, Proposition 1.12].
– (Tightness). It is known that if f attains a local minimum
at x¯, then 0 2 @C f(x¯); see [30, Proposition 2.3.2]. By
Fact 3, this is equivalent to f (x¯, d)  0 for all d 2 Rn.
However, the Clarke subdifferential may contain stationary points that are not local minima. For instance,
consider the function R 3 x 7! f(x) = |x|. It is easy
to see that @C f(0) = [1, 1]. It follows that x¯ = 0 is
a stationary point (as 0 2 @C f(0)). However, the point
x¯ = 0 is clearly not a local minimum (in fact, it is a global
maximum). Moreover, observe that the corresponding
Clarke directional derivatives are f (0, 1) = f (0, 1) =
1, which shows that neither d = 1 nor d = 1
is a descent direction according to Clarke’s definition.
However, the ordinary directional derivatives exist and
are given by f0
(0, 1) = f0
(0, 1) = 1. It follows that
both d = 1 and d = 1 are descent directions. One may
argue that the above example is not persuasive enough,
as similar phenomena occur in the smooth case (e.g.,
R 3 x 7! f(x) = x2). Hence, let us provide another,
hiilFigure 5: Clarke subdifferential of the sum of two non-differentiable functions.
Note that the addition rule does not hold in this example since the function f2(x) =
min{x, 0} is not subdifferentially regular [LSM20].
which shows that the support function of the Clarke subdifferential at any point x evaluated
at d is equal to the Clarke directional derivative at x in the direction d.
Note that the Clarke subdifferential of the sum of two functions is not equal to the sum of
the Clarke subdifferentials (see Figure 5 for an example). Nevertheless, we have the weaker
sum rule
∇C(f1 + f2) ⊆ ∇Cf1 + ∇Cf2.
It can be shown that the sum rule holds with equality if the functions are subdifferentially
regular, which are locally Lipschitz functions for which the ordinary directional derivative (2)
and Clarke directional derivative (5) coincide, i.e., f
0
(x, d) = f
◦
(x, d) ∀x, d. It follows that
convex functions are subdifferentially regular. This implies that the Clarke subdifferential
is identical to the ordinary subdifferential for convex functions. Furthermore, smooth functions and maximum of smooth functions, i.e., f = maxi∈{1,...,m} gi
, where gi are smooth are
subdifferentially regular. It can also be shown that chain rule also holds for subdifferentially
regular functions [LSM20].
Example. Subdifferential sum rule. Consider the functions f1(x) = max{x, 0},
f2(x) = min{x, 0} and f(x) = f1(x)+f2(x) as shown in Figure 5. It can be verified that
the weak addition rule ∇Cf1 + f2 ⊆ ∇Cf1 + ∇Cf2 holds, e.g., ∇Cf1(0) + ∇Cf2(0) =
[0, 1] + [0, 1] = [0, 2] ⊇ ∇Cf(0) = {1}. The sum rule does not hold with equality here
since f2(x) = min{x, 0} is not subdifferentially regular. Note that non-smooth concave
functions are not subdifferentially regular in general.
Finally, we have the following result that characterizes local minima and maxima of locally
Lipschitz functions in terms of stationarity in the sense of Clarke subdifferential [RW09].
x is a local minimum or maximum of f(x) =⇒ 0 ∈ ∂Cf(x).
Note that the reverse implication does not hold in general.
12
Example. Local minimum and maximum. Consider the non-convex one-dimensional
function f(x) = max{−|x|, x − 1}. It can be verified that x = 0 and x =
1
2
are local
maximum and local minimum respectively. Note that we have 0 ∈ ∂Cf(0) = [−1, 1]
and 0 ∈ ∂Cf(
1
2
) = [−1, 1].
Example. Stationary points. Consider the function f(x) = min{x, 0}. It can be seen
that ∇Cf(x) = {0} for x > 0, and such points are neither local minima nor local
maxima.
References
[BV04] S. Boyd and L. Vandenberghe. Convex Optimization. Cambridge University Press,
2004.
[Cla90] F. H. Clarke. Optimization and nonsmooth analysis. SIAM, 1990.
[HUL93] J. Hiriart-Urruty and C. Lemar´echal. Convex Analysis and Minimization Algorithms I & II. Springer, New York, 1993.
[HUL01] J. Hiriart-Urruty and C. Lemar´echal. Fundamentals of Convex Analysis. Springer,
2001.
[LSM20] J. Li, A. M. So, and W. Ma. Understanding notions of stationarity in nonsmooth
optimization: A guided tour of various constructions of subdifferential for nonsmooth functions. IEEE Signal Processing Magazine, 37(5):18–31, 2020.
[RW09] R. T. Rockafellar and T. J-B. Wets. Variational analysis, volume 317. Springer
Science & Business Media, 2009.
13