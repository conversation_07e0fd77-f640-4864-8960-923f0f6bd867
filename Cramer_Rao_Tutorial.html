<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C<PERSON><PERSON>r-<PERSON></title>
    
    <!-- MathJax 3 Configuration -->
    <script>
        MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js">
    </script>
    
    <!-- Plotly.js for interactive graphs -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        :root {
            --primary-color: #1565c0;
            --secondary-color: #1976d2;
            --accent-color: #2196f3;
            --success-color: #2e7d32;
            --warning-color: #f57c00;
            --danger-color: #d32f2f;
            --light-bg: #f5f5f5;
            --card-bg: #ffffff;
            --text-primary: #212121;
            --text-secondary: #757575;
            --border-color: #e0e0e0;
            --cramér-color: #673ab7;
            --fisher-color: #e91e63;
            --mse-color: #00695c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #1565c0 0%, #673ab7 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--cramér-color), var(--fisher-color));
        }

        .header h1 {
            font-size: 3em;
            color: var(--primary-color);
            margin-bottom: 15px;
            font-weight: 700;
        }

        .header .subtitle {
            font-size: 1.3em;
            color: var(--text-secondary);
            font-weight: 300;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            padding: 8px 16px;
            background: var(--cramér-color);
            color: white;
            border-radius: 20px;
            font-size: 0.9em;
            margin: 5px;
        }

        .section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            border-left: 5px solid var(--primary-color);
        }

        .section h2 {
            color: var(--primary-color);
            font-size: 2.2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .step-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .formula-box {
            background: #f8f9fa;
            border: 2px solid var(--accent-color);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            text-align: center;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);
        }

        .cramér-box {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border: 2px solid var(--cramér-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .cramér-box::before {
            content: '⚡';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .fisher-box {
            background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
            border: 2px solid var(--fisher-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .fisher-box::before {
            content: '🎯';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .mse-box {
            background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
            border: 2px solid var(--mse-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .mse-box::before {
            content: '📊';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .example-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid var(--success-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .example-box::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .definition-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 30%);
            border: 2px solid var(--warning-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .definition-box::before {
            content: '📚';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .warning-box {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            border: 2px solid var(--danger-color);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .warning-box::before {
            content: '⚠️';
            position: absolute;
            top: -15px;
            left: 25px;
            background: var(--card-bg);
            padding: 5px 10px;
            font-size: 1.5em;
        }

        .navigation {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 200px;
        }

        .nav-link {
            display: block;
            padding: 8px 15px;
            text-decoration: none;
            color: var(--text-secondary);
            border-radius: 5px;
            margin: 5px 0;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .nav-link:hover {
            background: var(--primary-color);
            color: white;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .comparison-table th {
            background: var(--primary-color);
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
        }

        .comparison-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table tr:nth-child(even) {
            background: var(--light-bg);
        }

        @media (max-width: 768px) {
            .navigation {
                display: none;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Cramér-Rao Bound</h1>
            <p class="subtitle">A Comprehensive Tutorial on Point Estimation Evaluation</p>
            <div>
                <span class="badge">Fisher Information</span>
                <span class="badge">MSE Analysis</span>
                <span class="badge">Optimal Estimation</span>
                <span class="badge">Decision Theory</span>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <a href="#introduction" class="nav-link">Introduction</a>
            <a href="#mse" class="nav-link">MSE & Bias-Variance</a>
            <a href="#fisher" class="nav-link">Fisher Information</a>
            <a href="#cramer-rao" class="nav-link">Cramér-Rao Bound</a>
            <a href="#decision" class="nav-link">Decision Theory</a>
            <a href="#examples" class="nav-link">Examples</a>
            <a href="#summary" class="nav-link">Summary</a>
        </div>

        <!-- Introduction Section -->
        <div class="section" id="introduction">
            <h2><span class="step-number">1</span>Introduction to Point Estimation Evaluation</h2>
            
            <p>When we have data and want to estimate unknown parameters, how do we know if our estimator is good? This fundamental question drives the development of sophisticated tools for evaluating point estimators. The <strong>Cramér-Rao bound</strong> provides a fundamental limit on how well we can estimate parameters.</p>

            <div class="definition-box">
                <h3>The Central Questions</h3>
                <ul>
                    <li><strong>Accuracy:</strong> How close is our estimate to the true parameter value?</li>
                    <li><strong>Precision:</strong> How much does our estimate vary across different samples?</li>
                    <li><strong>Optimality:</strong> Can we do better than our current estimator?</li>
                    <li><strong>Trade-offs:</strong> What compromises must we make in estimation?</li>
                </ul>
            </div>

            <div class="cramér-box">
                <h3>Tutorial Overview</h3>
                <p>This tutorial will guide you through:</p>
                <ol>
                    <li><strong>Mean Squared Error (MSE)</strong> and the bias-variance decomposition</li>
                    <li><strong>Fisher Information</strong> as a measure of parameter informativeness</li>
                    <li><strong>The Cramér-Rao bound</strong> and its profound implications</li>
                    <li><strong>Decision theory</strong> framework for optimal estimation</li>
                    <li><strong>Practical examples</strong> showing these concepts in action</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="300" viewBox="0 0 900 300">
                    <defs>
                        <linearGradient id="bgGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f5f5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#e3f2fd;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="300" fill="url(#bgGradient1)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#1565c0" font-size="18" font-weight="bold">The Estimation Quality Landscape</text>
                    
                    <!-- True parameter -->
                    <circle cx="450" cy="150" r="8" fill="#d32f2f"/>
                    <text x="450" y="175" text-anchor="middle" fill="#d32f2f" font-size="14" font-weight="bold">True θ</text>
                    
                    <!-- Estimator 1: Unbiased, high variance -->
                    <circle cx="200" cy="100" r="60" fill="none" stroke="#2196f3" stroke-width="3" stroke-dasharray="5,5"/>
                    <text x="200" y="50" text-anchor="middle" fill="#2196f3" font-size="12" font-weight="bold">Unbiased</text>
                    <text x="200" y="65" text-anchor="middle" fill="#2196f3" font-size="12">High Variance</text>
                    
                    <!-- Estimator 2: Biased, low variance -->
                    <circle cx="650" cy="200" r="25" fill="none" stroke="#673ab7" stroke-width="3"/>
                    <text x="650" y="250" text-anchor="middle" fill="#673ab7" font-size="12" font-weight="bold">Biased</text>
                    <text x="650" y="265" text-anchor="middle" fill="#673ab7" font-size="12">Low Variance</text>
                    
                    <!-- Cramér-Rao bound circle -->
                    <circle cx="450" cy="150" r="35" fill="none" stroke="#e91e63" stroke-width="4"/>
                    <text x="520" y="140" fill="#e91e63" font-size="12" font-weight="bold">Cramér-Rao</text>
                    <text x="520" y="155" fill="#e91e63" font-size="12">Lower Bound</text>
                    
                    <!-- Scatter points showing estimates -->
                    <g id="estimates1">
                        <circle cx="180" cy="80" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="220" cy="120" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="160" cy="140" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="240" cy="90" r="3" fill="#2196f3" opacity="0.7"/>
                        <circle cx="190" cy="110" r="3" fill="#2196f3" opacity="0.7"/>
                    </g>
                    
                    <g id="estimates2">
                        <circle cx="655" cy="195" r="3" fill="#673ab7" opacity="0.7"/>
                        <circle cx="645" cy="205" r="3" fill="#673ab7" opacity="0.7"/>
                        <circle cx="652" cy="198" r="3" fill="#673ab7" opacity="0.7"/>
                        <circle cx="648" cy="202" r="3" fill="#673ab7" opacity="0.7"/>
                        <circle cx="651" cy="199" r="3" fill="#673ab7" opacity="0.7"/>
                    </g>
                </svg>
            </div>
        </div>

        <!-- MSE Section -->
        <div class="section" id="mse">
            <h2><span class="step-number">2</span>Mean Squared Error and Bias-Variance Tradeoff</h2>
            
            <p>The <strong>Mean Squared Error (MSE)</strong> is the most common way to evaluate point estimators. It captures both the accuracy and precision of our estimates.</p>

            <div class="mse-box">
                <h3>Mean Squared Error Definition</h3>
                <p>For an estimator $\hat{\theta}$ of parameter $\theta$:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{MSE}(\hat{\theta}) = E_\theta[(\hat{\theta} - \theta)^2] = \int \cdots \int (\hat{\theta}(x_1, \ldots, x_n) - \theta)^2 p(x_1; \theta) \cdots p(x_n; \theta) dx_1 \ldots dx_n$$
                    </div>
                </div>
                <p>This measures the expected squared distance between our estimate and the true parameter.</p>
            </div>

            <div class="definition-box">
                <h3>Bias and Variance</h3>
                <p><strong>Bias:</strong> Systematic error in our estimator</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Bias}(\hat{\theta}) = E_\theta[\hat{\theta}] - \theta$$
                    </div>
                </div>
                
                <p><strong>Variance:</strong> Variability of our estimator</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(\hat{\theta}) = E_\theta[(\hat{\theta} - E_\theta[\hat{\theta}])^2]$$
                    </div>
                </div>
            </div>

            <div class="cramér-box">
                <h3>The Fundamental Decomposition</h3>
                <p><strong>Theorem:</strong> MSE can be decomposed as:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{MSE}(\hat{\theta}) = [\text{Bias}(\hat{\theta})]^2 + \text{Var}(\hat{\theta})$$
                    </div>
                </div>
                
                <p><strong>Proof Sketch:</strong></p>
                <p>Let $m = E_\theta[\hat{\theta}]$. Then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        \text{MSE} &= E_\theta[(\hat{\theta} - \theta)^2] \\
                        &= E_\theta[(\hat{\theta} - m + m - \theta)^2] \\
                        &= E_\theta[(\hat{\theta} - m)^2] + (m - \theta)^2 + 2E_\theta[(\hat{\theta} - m)(m - \theta)] \\
                        &= \text{Var}(\hat{\theta}) + [\text{Bias}(\hat{\theta})]^2
                        \end{align}
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>Example: Normal Distribution</h3>
                <p>Let $X_1, \ldots, X_n \sim N(\mu, \sigma^2)$. Consider two estimators:</p>
                
                <p><strong>Sample Mean:</strong> $\bar{X} = \frac{1}{n}\sum_{i=1}^n X_i$</p>
                <ul>
                    <li>Bias: $E[\bar{X}] - \mu = \mu - \mu = 0$ (unbiased)</li>
                    <li>Variance: $\text{Var}(\bar{X}) = \frac{\sigma^2}{n}$</li>
                    <li>MSE: $\frac{\sigma^2}{n}$</li>
                </ul>
                
                <p><strong>Sample Variance:</strong> $S^2 = \frac{1}{n-1}\sum_{i=1}^n (X_i - \bar{X})^2$</p>
                <ul>
                    <li>Bias: $E[S^2] - \sigma^2 = \sigma^2 - \sigma^2 = 0$ (unbiased)</li>
                    <li>Variance: $\text{Var}(S^2) = \frac{2\sigma^4}{n-1}$</li>
                    <li>MSE: $\frac{2\sigma^4}{n-1}$</li>
                </ul>
            </div>

            <div class="warning-box">
                <h3>The Bias-Variance Tradeoff</h3>
                <p>There's often a fundamental tradeoff:</p>
                <ul>
                    <li><strong>Lower bias</strong> can lead to <strong>higher variance</strong></li>
                    <li><strong>Lower variance</strong> might require accepting <strong>some bias</strong></li>
                    <li>The optimal estimator minimizes the <strong>total MSE</strong>, not necessarily bias or variance individually</li>
                </ul>
                
                <p><em>Question for reflection:</em> Why might restricting attention to only unbiased estimators be limiting?</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <div id="biasVariancePlot" style="width:100%; height:400px;"></div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Create bias-variance tradeoff visualization
                        const bias_values = [];
                        const variance_values = [];
                        const mse_values = [];
                        const lambda_values = [];
                        
                        // Simulate estimator family with parameter lambda
                        for (let lambda = 0; lambda <= 2; lambda += 0.1) {
                            lambda_values.push(lambda);
                            const bias = 0.5 * lambda; // Bias increases with lambda
                            const variance = 1 / (1 + lambda); // Variance decreases with lambda
                            const mse = bias * bias + variance;
                            
                            bias_values.push(bias * bias); // Squared bias
                            variance_values.push(variance);
                            mse_values.push(mse);
                        }
                        
                        const traces = [
                            {
                                x: lambda_values,
                                y: bias_values,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Bias²',
                                line: { color: '#d32f2f', width: 3 }
                            },
                            {
                                x: lambda_values,
                                y: variance_values,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Variance',
                                line: { color: '#2196f3', width: 3 }
                            },
                            {
                                x: lambda_values,
                                y: mse_values,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'MSE = Bias² + Variance',
                                line: { color: '#673ab7', width: 4 }
                            }
                        ];
                        
                        const layout = {
                            title: {
                                text: 'Bias-Variance Tradeoff',
                                font: { size: 18, color: '#1565c0' }
                            },
                            xaxis: { 
                                title: 'Regularization Parameter λ',
                                showgrid: true,
                                gridcolor: '#e0e0e0'
                            },
                            yaxis: { 
                                title: 'Error Components',
                                showgrid: true,
                                gridcolor: '#e0e0e0'
                            },
                            showlegend: true,
                            legend: { x: 0.7, y: 0.9 },
                            plot_bgcolor: '#f8f9fa',
                            paper_bgcolor: '#ffffff',
                            annotations: [{
                                x: 0.8,
                                y: 0.6,
                                text: 'Optimal λ minimizes<br>total MSE',
                                showarrow: true,
                                arrowhead: 2,
                                arrowsize: 1,
                                arrowwidth: 2,
                                arrowcolor: '#673ab7'
                            }]
                        };
                        
                        Plotly.newPlot('biasVariancePlot', traces, layout, {responsive: true, displayModeBar: false});
                    });
                                 </script>
             </div>
         </div>

        <!-- Fisher Information Section -->
        <div class="section" id="fisher">
            <h2><span class="step-number">3</span>Fisher Information: Measuring Parameter Informativeness</h2>
            
            <p>Before we can understand the Cramér-Rao bound, we need to understand <strong>Fisher Information</strong> - a fundamental measure of how much information our data contains about unknown parameters.</p>

            <div class="fisher-box">
                <h3>The Score Function</h3>
                <p>For observations $X_1, \ldots, X_n \sim p(X; \theta)$, the <strong>score function</strong> is the gradient of the log-likelihood:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$s(\theta) = \nabla_\theta \ell\ell(\theta) = \sum_{i=1}^n \nabla_\theta \log p(X_i; \theta)$$
                    </div>
                </div>
                <p>This $d$-dimensional vector tells us how the log-likelihood changes with respect to the parameter.</p>
            </div>

            <div class="definition-box">
                <h3>Fisher Information Matrix</h3>
                <p>The <strong>Fisher Information Matrix</strong> is the expected outer product of the score:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$I(\theta) = E[s(\theta)s(\theta)^T]$$
                    </div>
                </div>
                <p>This $d \times d$ matrix measures the amount of information that observable data carries about unknown parameter $\theta$.</p>
            </div>

            <div class="example-box">
                <h3>Example 1: Bernoulli Distribution</h3>
                <p>Let $X \sim \text{Ber}(p)$. The log-likelihood is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\ell\ell(p) = X \log(p) + (1-X)\log(1-p)$$
                    </div>
                </div>
                
                <p>The score function is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$s(p) = \frac{X}{p} - \frac{1-X}{1-p} = \frac{X-p}{p(1-p)}$$
                    </div>
                </div>
                
                <p>The Fisher information is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$I_1(p) = E\left[\frac{(X-p)^2}{p^2(1-p)^2}\right] = \frac{1}{p(1-p)}$$
                    </div>
                </div>
                
                <p>For $n$ observations: $I(p) = \frac{n}{p(1-p)}$</p>
            </div>

            <div class="example-box">
                <h3>Example 2: Normal Distribution (Known Variance)</h3>
                <p>Let $X \sim N(\mu, \sigma^2)$ with $\sigma$ known. The log-likelihood is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\ell\ell(\mu) = -\frac{1}{2\sigma^2}(X-\mu)^2 + \text{constant}$$
                    </div>
                </div>
                
                <p>The score function is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$s(\mu) = \frac{X-\mu}{\sigma^2}$$
                    </div>
                </div>
                
                <p>The Fisher information is:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$I_1(\mu) = E\left[\frac{(X-\mu)^2}{\sigma^4}\right] = \frac{1}{\sigma^2}$$
                    </div>
                </div>
                
                <p>For $n$ observations: $I(\mu) = \frac{n}{\sigma^2}$</p>
            </div>

            <div class="cramér-box">
                <h3>Key Properties of Fisher Information</h3>
                
                <p><strong>Property 1: Score has zero mean</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$E[s(\theta)] = 0$$
                    </div>
                </div>
                
                <p><strong>Proof sketch:</strong> Under regularity conditions, we can interchange expectation and derivative:</p>
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        E[s(\theta)] &= E\left[\nabla_\theta \log p(X; \theta)\right] \\
                        &= \int \nabla_\theta \log p(x; \theta) \cdot p(x; \theta) dx \\
                        &= \int \nabla_\theta p(x; \theta) dx = \nabla_\theta \int p(x; \theta) dx = \nabla_\theta 1 = 0
                        \end{align}
                    </div>
                </div>

                <p><strong>Property 2: Fisher Information as covariance</strong></p>
                <p>Since $E[s(\theta)] = 0$:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$I(\theta) = \text{Cov}(s(\theta)) = E[(s(\theta) - E[s(\theta)])(s(\theta) - E[s(\theta)])^T]$$
                    </div>
                </div>

                <p><strong>Property 3: Alternative representation</strong></p>
                <p>For a single observation:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$I_1(\theta) = -E[\nabla^2_\theta \log p(X; \theta)]$$
                    </div>
                </div>
                <p>This shows Fisher information measures the expected curvature of the log-likelihood.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 900 400">
                    <defs>
                        <linearGradient id="fisherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#fce4ec;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#f8bbd9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="400" fill="url(#fisherGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#e91e63" font-size="18" font-weight="bold">Fisher Information Intuition</text>
                    
                    <!-- High Fisher Information scenario -->
                    <g>
                        <text x="225" y="60" text-anchor="middle" fill="#1565c0" font-size="14" font-weight="bold">High Fisher Information</text>
                        <text x="225" y="80" text-anchor="middle" fill="#757575" font-size="12">Sharp likelihood, easy estimation</text>
                        
                        <!-- Sharp likelihood curve -->
                        <path d="M 125 300 Q 225 120 325 300" stroke="#1565c0" stroke-width="4" fill="none"/>
                        <circle cx="225" cy="120" r="5" fill="#d32f2f"/>
                        <text x="225" y="110" text-anchor="middle" fill="#d32f2f" font-size="12">True θ</text>
                        
                        <!-- Confidence interval -->
                        <line x1="200" y1="320" x2="250" y2="320" stroke="#2e7d32" stroke-width="6"/>
                        <text x="225" y="340" text-anchor="middle" fill="#2e7d32" font-size="12">Narrow CI</text>
                    </g>
                    
                    <!-- Low Fisher Information scenario -->
                    <g>
                        <text x="675" y="60" text-anchor="middle" fill="#f57c00" font-size="14" font-weight="bold">Low Fisher Information</text>
                        <text x="675" y="80" text-anchor="middle" fill="#757575" font-size="12">Flat likelihood, hard estimation</text>
                        
                        <!-- Flat likelihood curve -->
                        <path d="M 525 280 Q 675 180 825 280" stroke="#f57c00" stroke-width="4" fill="none"/>
                        <circle cx="675" cy="180" r="5" fill="#d32f2f"/>
                        <text x="675" y="170" text-anchor="middle" fill="#d32f2f" font-size="12">True θ</text>
                        
                        <!-- Confidence interval -->
                        <line x1="600" y1="320" x2="750" y2="320" stroke="#d32f2f" stroke-width="6"/>
                        <text x="675" y="340" text-anchor="middle" fill="#d32f2f" font-size="12">Wide CI</text>
                    </g>
                    
                    <!-- Axes -->
                    <line x1="100" y1="300" x2="350" y2="300" stroke="#424242" stroke-width="2"/>
                    <line x1="550" y1="280" x2="850" y2="280" stroke="#424242" stroke-width="2"/>
                    
                    <text x="225" y="365" text-anchor="middle" fill="#424242" font-size="12">Parameter θ</text>
                    <text x="675" y="365" text-anchor="middle" fill="#424242" font-size="12">Parameter θ</text>
                    
                    <!-- Key insight -->
                    <rect x="300" y="150" width="300" height="80" fill="#ffffff" stroke="#e91e63" stroke-width="2" rx="10"/>
                    <text x="450" y="175" text-anchor="middle" fill="#e91e63" font-size="14" font-weight="bold">Key Insight</text>
                    <text x="450" y="195" text-anchor="middle" fill="#424242" font-size="12">Higher Fisher Information</text>
                    <text x="450" y="210" text-anchor="middle" fill="#424242" font-size="12">→ More precise estimation</text>
                </svg>
            </div>

            <div class="fisher-box">
                <h3>Fisher Information for Exponential Families</h3>
                <p>For exponential families with log-likelihood:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\ell\ell(\theta; X_1, \ldots, X_n) = \sum_{i=1}^s \theta_i \sum_{j=1}^n T_i(X_j) - nA(\theta)$$
                    </div>
                </div>
                
                <p>The Fisher information matrix is simply:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$I(\theta) = n\nabla^2_\theta A(\theta) = n \cdot \text{Cov}(T(X))$$
                    </div>
                </div>
                
                <p>This elegant result shows Fisher information is just <em>n</em> times the covariance matrix of sufficient statistics!</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <div id="fisherInfoPlot" style="width:100%; height:400px;"></div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Create Fisher Information comparison plot
                        const p_values = [];
                        const bernoulli_fisher = [];
                        const normal_fisher = [];
                        
                        for (let p = 0.05; p <= 0.95; p += 0.05) {
                            p_values.push(p);
                            bernoulli_fisher.push(1 / (p * (1 - p))); // Bernoulli Fisher Info
                        }
                        
                        const sigma_values = [];
                        for (let sigma = 0.5; sigma <= 3; sigma += 0.1) {
                            sigma_values.push(sigma);
                            normal_fisher.push(1 / (sigma * sigma)); // Normal Fisher Info
                        }
                        
                        const traces = [
                            {
                                x: p_values,
                                y: bernoulli_fisher,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Bernoulli: I(p) = 1/[p(1-p)]',
                                line: { color: '#e91e63', width: 3 }
                            },
                            {
                                x: sigma_values,
                                y: normal_fisher,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Normal: I(μ) = 1/σ²',
                                line: { color: '#1565c0', width: 3 },
                                yaxis: 'y2'
                            }
                        ];
                        
                        const layout = {
                            title: {
                                text: 'Fisher Information Examples',
                                font: { size: 18, color: '#1565c0' }
                            },
                            xaxis: { 
                                title: 'Parameter Value',
                                showgrid: true,
                                gridcolor: '#e0e0e0'
                            },
                            yaxis: { 
                                title: 'Fisher Information I(p)',
                                side: 'left',
                                showgrid: true,
                                gridcolor: '#e0e0e0',
                                color: '#e91e63'
                            },
                            yaxis2: {
                                title: 'Fisher Information I(μ)',
                                side: 'right',
                                overlaying: 'y',
                                color: '#1565c0'
                            },
                            showlegend: true,
                            legend: { x: 0.1, y: 0.9 },
                            plot_bgcolor: '#f8f9fa',
                            paper_bgcolor: '#ffffff',
                            annotations: [
                                {
                                    x: 0.5,
                                    y: 4,
                                    text: 'Minimum at p=0.5<br>(most information)',
                                    showarrow: true,
                                    arrowhead: 2,
                                    arrowcolor: '#e91e63',
                                    ax: 0,
                                    ay: -50
                                },
                                {
                                    x: 2.5,
                                    y: 0.16,
                                    text: 'Smaller σ²<br>→ Higher precision',
                                    showarrow: true,
                                    arrowhead: 2,
                                    arrowcolor: '#1565c0',
                                    yref: 'y2',
                                    ax: 50,
                                    ay: -30
                                }
                            ]
                        };
                        
                        Plotly.newPlot('fisherInfoPlot', traces, layout, {responsive: true, displayModeBar: false});
                    });
                                 </script>
             </div>
         </div>

        <!-- Cramér-Rao Bound Section -->
        <div class="section" id="cramer-rao">
            <h2><span class="step-number">4</span>The Cramér-Rao Bound: Fundamental Limit of Estimation</h2>
            
            <p>Now we arrive at the centerpiece: the <strong>Cramér-Rao bound</strong>. This remarkable theorem tells us the fundamental limit on how well we can estimate parameters.</p>

            <div class="cramér-box">
                <h3>The Cramér-Rao Bound (Univariate Case)</h3>
                <p><strong>Theorem:</strong> Suppose $X_1, \ldots, X_n \sim p(X; \theta)$ and $\hat{\theta}$ is an unbiased estimator of $\theta$. Then:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(\hat{\theta}) \geq \frac{1}{nI_1(\theta)}$$
                    </div>
                </div>
                <p>where $I_1(\theta)$ is the Fisher information for a single observation.</p>
                <p><strong>Key insight:</strong> This provides a <em>lower bound</em> on the variance of any unbiased estimator!</p>
            </div>

            <div class="definition-box">
                <h3>Proof of the Cramér-Rao Bound</h3>
                <p>The proof is elegant and instructive. Here are the key steps:</p>
                
                <p><strong>Step 1:</strong> Consider the covariance between $\hat{\theta}$ and the score $s(\theta)$:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Cov}(\hat{\theta}, s(\theta)) = E[(\hat{\theta} - \theta)s(\theta)]$$
                    </div>
                </div>
                <p>Since $E[s(\theta)] = 0$ and $E[\hat{\theta}] = \theta$ (unbiasedness).</p>

                <p><strong>Step 2:</strong> Show this covariance equals 1:</p>
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        E[\hat{\theta} s(\theta)] &= \int \hat{\theta}(x_1, \ldots, x_n) \nabla_\theta \log p(x_1, \ldots, x_n; \theta) p(x_1, \ldots, x_n; \theta) dx_1 \ldots dx_n \\
                        &= \int \hat{\theta}(x_1, \ldots, x_n) \nabla_\theta p(x_1, \ldots, x_n; \theta) dx_1 \ldots dx_n \\
                        &= \nabla_\theta \int \hat{\theta}(x_1, \ldots, x_n) p(x_1, \ldots, x_n; \theta) dx_1 \ldots dx_n \\
                        &= \nabla_\theta E[\hat{\theta}] = \nabla_\theta \theta = 1
                        \end{align}
                    </div>
                </div>

                <p><strong>Step 3:</strong> Apply Cauchy-Schwarz inequality:</p>
                <p>For any random variables $U, V$: $[\text{Cov}(U,V)]^2 \leq \text{Var}(U) \text{Var}(V)$</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$1 = [\text{Cov}(\hat{\theta}, s(\theta))]^2 \leq \text{Var}(\hat{\theta}) \text{Var}(s(\theta)) = \text{Var}(\hat{\theta}) \cdot nI_1(\theta)$$
                    </div>
                </div>

                <p><strong>Step 4:</strong> Rearrange to get the bound:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(\hat{\theta}) \geq \frac{1}{nI_1(\theta)}$$
                    </div>
                </div>
            </div>

            <div class="example-box">
                <h3>Example: Cramér-Rao Bound for Bernoulli</h3>
                <p>Consider $X_1, \ldots, X_n \sim \text{Ber}(p)$ and estimator $\hat{p} = \frac{1}{n}\sum_{i=1}^n X_i$.</p>
                
                <p><strong>Fisher Information:</strong> $I_1(p) = \frac{1}{p(1-p)}$</p>
                
                <p><strong>Cramér-Rao bound:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(\hat{p}) \geq \frac{1}{n \cdot \frac{1}{p(1-p)}} = \frac{p(1-p)}{n}$$
                    </div>
                </div>
                
                <p><strong>Actual variance of sample proportion:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(\hat{p}) = \text{Var}\left(\frac{1}{n}\sum_{i=1}^n X_i\right) = \frac{1}{n^2} \sum_{i=1}^n \text{Var}(X_i) = \frac{p(1-p)}{n}$$
                    </div>
                </div>
                
                <p><strong>Conclusion:</strong> The sample proportion <em>achieves</em> the Cramér-Rao bound! It's <strong>efficient</strong>.</p>
            </div>

            <div class="fisher-box">
                <h3>Efficiency and Optimal Estimators</h3>
                <p><strong>Definition:</strong> An unbiased estimator is <strong>efficient</strong> if it achieves the Cramér-Rao bound:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Var}(\hat{\theta}) = \frac{1}{nI_1(\theta)}$$
                    </div>
                </div>
                
                <p><strong>Important facts about efficiency:</strong></p>
                <ul>
                    <li>Efficient estimators are <em>optimal</em> among unbiased estimators</li>
                    <li>If an efficient estimator exists, it's unique</li>
                    <li>MLEs are often (asymptotically) efficient</li>
                    <li>Not all parameters have efficient estimators</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="500" viewBox="0 0 1000 500">
                    <defs>
                        <linearGradient id="cramerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="1000" height="500" fill="url(#cramerGradient)" rx="15"/>
                    
                    <text x="500" y="35" text-anchor="middle" fill="#673ab7" font-size="20" font-weight="bold">Cramér-Rao Bound Visualization</text>
                    
                    <!-- Y-axis (Variance) -->
                    <line x1="80" y1="80" x2="80" y2="450" stroke="#424242" stroke-width="3"/>
                    <text x="30" y="250" text-anchor="middle" fill="#424242" font-size="14" transform="rotate(-90, 30, 250)">Variance</text>
                    
                    <!-- X-axis (Sample Size) -->
                    <line x1="80" y1="450" x2="920" y2="450" stroke="#424242" stroke-width="3"/>
                    <text x="500" y="485" text-anchor="middle" fill="#424242" font-size="14">Sample Size (n)</text>
                    
                    <!-- Cramér-Rao bound curve -->
                    <path d="M 120 400 Q 300 200 920 120" stroke="#673ab7" stroke-width="6" fill="none"/>
                    <text x="750" y="140" fill="#673ab7" font-size="14" font-weight="bold">Cramér-Rao Lower Bound</text>
                    <text x="750" y="160" fill="#673ab7" font-size="12">Var(θ̂) ≥ 1/[nI(θ)]</text>
                    
                    <!-- Efficient estimator (on the bound) -->
                    <circle cx="300" cy="200" r="8" fill="#2e7d32"/>
                    <circle cx="500" cy="160" r="8" fill="#2e7d32"/>
                    <circle cx="700" cy="135" r="8" fill="#2e7d32"/>
                    <text x="400" y="185" fill="#2e7d32" font-size="12" font-weight="bold">Efficient Estimator</text>
                    <text x="400" y="200" fill="#2e7d32" font-size="12">(achieves the bound)</text>
                    
                    <!-- Inefficient estimator (above the bound) -->
                    <circle cx="300" cy="150" r="8" fill="#d32f2f"/>
                    <circle cx="500" cy="120" r="8" fill="#d32f2f"/>
                    <circle cx="700" cy="105" r="8" fill="#d32f2f"/>
                    <text x="400" y="110" fill="#d32f2f" font-size="12" font-weight="bold">Inefficient Estimator</text>
                    <text x="400" y="125" fill="#d32f2f" font-size="12">(variance above bound)</text>
                    
                    <!-- Impossible region -->
                    <path d="M 120 400 Q 300 200 920 120 L 920 80 L 120 80 Z" fill="#ffebee" opacity="0.5"/>
                    <text x="200" y="110" fill="#d32f2f" font-size="12" font-weight="bold">Impossible Region</text>
                    <text x="200" y="125" fill="#d32f2f" font-size="12">(violates CR bound)</text>
                    
                    <!-- Sample size markers -->
                    <line x1="200" y1="445" x2="200" y2="455" stroke="#424242" stroke-width="2"/>
                    <text x="200" y="470" text-anchor="middle" fill="#424242" font-size="12">n₁</text>
                    
                    <line x1="500" y1="445" x2="500" y2="455" stroke="#424242" stroke-width="2"/>
                    <text x="500" y="470" text-anchor="middle" fill="#424242" font-size="12">n₂</text>
                    
                    <line x1="800" y1="445" x2="800" y2="455" stroke="#424242" stroke-width="2"/>
                    <text x="800" y="470" text-anchor="middle" fill="#424242" font-size="12">n₃</text>
                    
                    <!-- Key insight box -->
                    <rect x="550" y="250" width="420" height="150" fill="#ffffff" stroke="#673ab7" stroke-width="3" rx="10"/>
                    <text x="760" y="280" text-anchor="middle" fill="#673ab7" font-size="16" font-weight="bold">Key Insights</text>
                    <text x="560" y="305" fill="#424242" font-size="13">• Higher Fisher Info → Lower bound</text>
                    <text x="560" y="325" fill="#424242" font-size="13">• Larger sample size → Lower variance</text>
                    <text x="560" y="345" fill="#424242" font-size="13">• Efficient estimators are optimal</text>
                    <text x="560" y="365" fill="#424242" font-size="13">• No unbiased estimator can do better</text>
                    <text x="560" y="385" fill="#424242" font-size="13">• Bound gives estimation difficulty</text>
                </svg>
            </div>

            <div class="cramér-box">
                <h3>Multivariate Cramér-Rao Bound</h3>
                <p>For multivariate parameters $\theta \in \mathbb{R}^d$, the bound becomes a matrix inequality:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{Cov}(\hat{\theta}) \succeq I(\theta)^{-1} = \frac{1}{n}I_1(\theta)^{-1}$$
                    </div>
                </div>
                <p>where $A \succeq B$ means $A - B$ is positive semi-definite. This means:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$v^T \text{Cov}(\hat{\theta}) v \geq v^T I(\theta)^{-1} v \quad \text{for all } v \in \mathbb{R}^d$$
                    </div>
                </div>
            </div>

            <div class="warning-box">
                <h3>When the Cramér-Rao Bound Fails</h3>
                <p>The Cramér-Rao bound requires <strong>regularity conditions</strong>:</p>
                <ul>
                    <li><strong>Differentiability:</strong> The likelihood must be differentiable with respect to $\theta$</li>
                    <li><strong>Support independence:</strong> The support of the distribution shouldn't depend on $\theta$</li>
                    <li><strong>Regularity:</strong> We must be able to interchange differentiation and integration</li>
                </ul>
                
                <p><strong>Example of failure:</strong> For $X_1, \ldots, X_n \sim \text{Uniform}(0, \theta)$:</p>
                <ul>
                    <li>The likelihood is not differentiable at the boundary</li>
                    <li>The support $[0, \theta]$ depends on $\theta$</li>
                    <li>The MLE $\hat{\theta} = \max(X_1, \ldots, X_n)$ has variance $O(1/n^2)$</li>
                    <li>This converges faster than the $O(1/n)$ rate suggested by Cramér-Rao!</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <div id="cramerRaoComparison" style="width:100%; height:400px;"></div>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Cramér-Rao bound comparison for different distributions
                        const n_values = [];
                        const bernoulli_cr = [];
                        const bernoulli_actual = [];
                        const normal_cr = [];
                        const normal_actual = [];
                        
                        // Parameters
                        const p = 0.3; // Bernoulli parameter
                        const sigma = 1.5; // Normal standard deviation
                        
                        for (let n = 5; n <= 100; n += 5) {
                            n_values.push(n);
                            
                            // Bernoulli case
                            const bernoulli_fisher = 1 / (p * (1 - p));
                            bernoulli_cr.push(1 / (n * bernoulli_fisher));
                            bernoulli_actual.push(p * (1 - p) / n); // Sample proportion achieves CR bound
                            
                            // Normal case
                            const normal_fisher = 1 / (sigma * sigma);
                            normal_cr.push(1 / (n * normal_fisher));
                            normal_actual.push(sigma * sigma / n); // Sample mean achieves CR bound
                        }
                        
                        const traces = [
                            {
                                x: n_values,
                                y: bernoulli_cr,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Bernoulli CR Bound',
                                line: { color: '#673ab7', width: 3, dash: 'dash' }
                            },
                            {
                                x: n_values,
                                y: bernoulli_actual,
                                type: 'scatter',
                                mode: 'markers',
                                name: 'Sample Proportion',
                                marker: { color: '#2e7d32', size: 6 }
                            },
                            {
                                x: n_values,
                                y: normal_cr,
                                type: 'scatter',
                                mode: 'lines',
                                name: 'Normal CR Bound',
                                line: { color: '#e91e63', width: 3, dash: 'dash' }
                            },
                            {
                                x: n_values,
                                y: normal_actual,
                                type: 'scatter',
                                mode: 'markers',
                                name: 'Sample Mean',
                                marker: { color: '#1565c0', size: 6 }
                            }
                        ];
                        
                        const layout = {
                            title: {
                                text: 'Cramér-Rao Bound vs. Actual Variance',
                                font: { size: 18, color: '#1565c0' }
                            },
                            xaxis: { 
                                title: 'Sample Size n',
                                showgrid: true,
                                gridcolor: '#e0e0e0'
                            },
                            yaxis: { 
                                title: 'Variance',
                                showgrid: true,
                                gridcolor: '#e0e0e0',
                                type: 'log'
                            },
                            showlegend: true,
                            legend: { x: 0.6, y: 0.9 },
                            plot_bgcolor: '#f8f9fa',
                            paper_bgcolor: '#ffffff',
                            annotations: [{
                                x: 80,
                                y: 0.003,
                                text: 'Efficient estimators<br>achieve the bound!',
                                showarrow: true,
                                arrowhead: 2,
                                arrowsize: 1,
                                arrowwidth: 2,
                                arrowcolor: '#2e7d32'
                            }]
                        };
                        
                        Plotly.newPlot('cramerRaoComparison', traces, layout, {responsive: true, displayModeBar: false});
                    });
                                 </script>
             </div>
         </div>

        <!-- Decision Theory Section -->
        <div class="section" id="decision">
            <h2><span class="step-number">5</span>Beyond Unbiased Estimators: Decision Theory</h2>
            
            <p>While the Cramér-Rao bound is fundamental for unbiased estimators, modern statistics goes beyond this restriction. <strong>Decision theory</strong> provides a more general framework for optimal estimation.</p>

            <div class="definition-box">
                <h3>Decision Theory Framework</h3>
                <p>In the decision theory setup:</p>
                <ul>
                    <li><strong>Data:</strong> $X_1, \ldots, X_n \sim p(X; \theta)$ with $\theta \in \Theta$</li>
                    <li><strong>Action:</strong> Choose estimator $\hat{\theta}$ (our "decision")</li>
                    <li><strong>Loss Function:</strong> $L(\hat{\theta}, \theta)$ measures quality of our decision</li>
                    <li><strong>Risk:</strong> $R(\theta, \hat{\theta}) = E_\theta[L(\hat{\theta}, \theta)]$ (expected loss)</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Common Loss Functions</h3>
                
                <p><strong>1. Squared Loss:</strong> $L(a, \theta) = (a - \theta)^2$</p>
                <ul>
                    <li>Risk = MSE</li>
                    <li>Penalizes large errors quadratically</li>
                    <li>Leads to mean estimators</li>
                </ul>
                
                <p><strong>2. Absolute Loss:</strong> $L(a, \theta) = |a - \theta|$</p>
                <ul>
                    <li>Risk = Mean Absolute Error</li>
                    <li>More robust to outliers</li>
                    <li>Leads to median estimators</li>
                </ul>
                
                <p><strong>3. Relative Loss:</strong> $L(a, \theta) = \frac{(a - \theta)^2}{|\theta| + 1}$</p>
                <ul>
                    <li>Penalizes errors more for small $\theta$</li>
                    <li>Scale-invariant considerations</li>
                </ul>
                
                <p><strong>4. Kullback-Leibler Loss:</strong> $L(a, \theta) = KL(p(\cdot; \theta), p(\cdot; a))$</p>
                <ul>
                    <li>Measures distributional distance</li>
                    <li>Relevant for density estimation</li>
                </ul>
            </div>

            <div class="warning-box">
                <h3>The Impossibility of Universal Optimality</h3>
                <p>A fundamental result in decision theory:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\text{No estimator } \hat{\theta} \text{ can satisfy } R(\theta, \hat{\theta}) \leq R(\theta, \hat{\theta}') \text{ for all } \theta \text{ and all } \hat{\theta}'$$
                    </div>
                </div>
                
                <p><strong>Why?</strong> Consider any two distinct parameter values $\theta_1, \theta_2$:</p>
                <ul>
                    <li>Estimator $\hat{\theta}_1 \equiv \theta_1$ has zero risk at $\theta_1$ but positive risk at $\theta_2$</li>
                    <li>Estimator $\hat{\theta}_2 \equiv \theta_2$ has zero risk at $\theta_2$ but positive risk at $\theta_1$</li>
                    <li>No single estimator can beat both simultaneously</li>
                </ul>
                
                <p>This impossibility drives the need for different optimality criteria!</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="400" viewBox="0 0 900 400">
                    <defs>
                        <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ffcc02;stop-opacity:0.3" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="400" fill="url(#decisionGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#f57c00" font-size="18" font-weight="bold">Decision Theory Components</text>
                    
                    <!-- Data -->
                    <circle cx="150" cy="100" r="40" fill="#2196f3" opacity="0.7"/>
                    <text x="150" y="105" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Data</text>
                    <text x="150" y="150" text-anchor="middle" fill="#2196f3" font-size="12">X₁, ..., Xₙ</text>
                    
                    <!-- Estimator -->
                    <rect x="300" y="70" width="80" height="60" fill="#2e7d32" opacity="0.7" rx="10"/>
                    <text x="340" y="90" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Estimator</text>
                    <text x="340" y="105" text-anchor="middle" fill="white" font-size="12">θ̂</text>
                    <text x="340" y="150" text-anchor="middle" fill="#2e7d32" font-size="12">Decision</text>
                    
                    <!-- True Parameter -->
                    <circle cx="550" cy="100" r="40" fill="#d32f2f" opacity="0.7"/>
                    <text x="550" y="95" text-anchor="middle" fill="white" font-size="12" font-weight="bold">True</text>
                    <text x="550" y="110" text-anchor="middle" fill="white" font-size="12">θ</text>
                    <text x="550" y="150" text-anchor="middle" fill="#d32f2f" font-size="12">Unknown</text>
                    
                    <!-- Loss Function -->
                    <rect x="700" y="70" width="80" height="60" fill="#673ab7" opacity="0.7" rx="10"/>
                    <text x="740" y="90" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Loss</text>
                    <text x="740" y="105" text-anchor="middle" fill="white" font-size="12">L(θ̂,θ)</text>
                    <text x="740" y="150" text-anchor="middle" fill="#673ab7" font-size="12">Penalty</text>
                    
                    <!-- Arrows -->
                    <path d="M 190 100 L 290 100" stroke="#424242" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <path d="M 380 100 L 510 100" stroke="#424242" stroke-width="3" marker-end="url(#arrowhead)"/>
                    <path d="M 590 100 L 690 100" stroke="#424242" stroke-width="3" marker-end="url(#arrowhead)"/>
                    
                    <!-- Arrow marker definition -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#424242"/>
                        </marker>
                    </defs>
                    
                    <!-- Risk calculation -->
                    <rect x="200" y="250" width="500" height="120" fill="#ffffff" stroke="#f57c00" stroke-width="3" rx="15"/>
                    <text x="450" y="280" text-anchor="middle" fill="#f57c00" font-size="16" font-weight="bold">Risk = Expected Loss</text>
                    <text x="450" y="310" text-anchor="middle" fill="#424242" font-size="14">R(θ, θ̂) = E_θ[L(θ̂, θ)]</text>
                    <text x="220" y="340" fill="#424242" font-size="12">• Measures average performance of estimator</text>
                    <text x="220" y="355" fill="#424242" font-size="12">• Depends on true parameter θ (unknown!)</text>
                </svg>
            </div>

            <div class="cramér-box">
                <h3>Key Insights from Decision Theory</h3>
                
                <p><strong>1. Bias-Variance Perspective</strong></p>
                <p>For squared loss, restricting to unbiased estimators can be suboptimal. Sometimes a small amount of bias can lead to large reductions in variance, reducing overall MSE.</p>
                
                <p><strong>2. Le Cam's Extension</strong></p>
                <p>The Cramér-Rao bound extends to more general settings without the unbiasedness requirement. Under appropriate conditions, no estimator can have smaller mean squared error than the Fisher information bound in any uniform sense.</p>
                
                <p><strong>3. Minimax Approach</strong></p>
                <p>Since we can't uniformly minimize risk, we can minimize the <em>maximum</em> risk:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\hat{\theta}_{minimax} = \arg\min_{\hat{\theta}} \max_{\theta \in \Theta} R(\theta, \hat{\theta})$$
                    </div>
                </div>
                
                <p><strong>4. Bayesian Approach</strong></p>
                <p>Put a prior $\pi(\theta)$ on parameters and minimize expected risk:</p>
                <div class="formula-box">
                    <div class="math-display">
                        $$\hat{\theta}_{Bayes} = \arg\min_{\hat{\theta}} \int R(\theta, \hat{\theta}) \pi(\theta) d\theta$$
                    </div>
                </div>
            </div>
        </div>

        <!-- Examples Section -->
        <div class="section" id="examples">
            <h2><span class="step-number">6</span>Comprehensive Examples</h2>
            
            <div class="example-box">
                <h3>Example 1: Gaussian Mean Estimation</h3>
                <p>Let $X_1, \ldots, X_n \sim N(\mu, \sigma^2)$ with $\sigma^2$ known.</p>
                
                <p><strong>Sample Mean:</strong> $\bar{X} = \frac{1}{n}\sum_{i=1}^n X_i$</p>
                <ul>
                    <li>Unbiased: $E[\bar{X}] = \mu$</li>
                    <li>Variance: $\text{Var}(\bar{X}) = \frac{\sigma^2}{n}$</li>
                    <li>Fisher Information: $I_1(\mu) = \frac{1}{\sigma^2}$</li>
                    <li>Cramér-Rao bound: $\frac{1}{n \cdot \frac{1}{\sigma^2}} = \frac{\sigma^2}{n}$ ✓</li>
                    <li><strong>Result:</strong> $\bar{X}$ is efficient!</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Example 2: Exponential Rate Estimation</h3>
                <p>Let $X_1, \ldots, X_n \sim \text{Exp}(\lambda)$ with density $f(x) = \lambda e^{-\lambda x}$.</p>
                
                <p><strong>MLE:</strong> $\hat{\lambda} = \frac{1}{\bar{X}}$</p>
                
                <p><strong>Fisher Information calculation:</strong></p>
                <div class="formula-box">
                    <div class="math-display">
                        \begin{align}
                        \log f(x; \lambda) &= \log \lambda - \lambda x \\
                        s(\lambda) &= \frac{1}{\lambda} - x \\
                        I_1(\lambda) &= \text{Var}(s(\lambda)) = \text{Var}\left(\frac{1}{\lambda} - X\right) = \text{Var}(X) = \frac{1}{\lambda^2}
                        \end{align}
                    </div>
                </div>
                
                <p><strong>Cramér-Rao bound:</strong> $\frac{1}{n \cdot \frac{1}{\lambda^2}} = \frac{\lambda^2}{n}$</p>
                
                <p>The MLE $\hat{\lambda} = 1/\bar{X}$ is biased but asymptotically achieves this bound.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Distribution</th>
                            <th>Parameter</th>
                            <th>Fisher Information I₁(θ)</th>
                            <th>CR Bound</th>
                            <th>Efficient Estimator</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Bernoulli(p)</td>
                            <td>p</td>
                            <td>1/[p(1-p)]</td>
                            <td>p(1-p)/n</td>
                            <td>Sample proportion</td>
                        </tr>
                        <tr>
                            <td>Normal(μ, σ²)</td>
                            <td>μ (σ known)</td>
                            <td>1/σ²</td>
                            <td>σ²/n</td>
                            <td>Sample mean</td>
                        </tr>
                        <tr>
                            <td>Exponential(λ)</td>
                            <td>λ</td>
                            <td>1/λ²</td>
                            <td>λ²/n</td>
                            <td>1/sample mean (asymp.)</td>
                        </tr>
                        <tr>
                            <td>Poisson(λ)</td>
                            <td>λ</td>
                            <td>1/λ</td>
                            <td>λ/n</td>
                            <td>Sample mean</td>
                        </tr>
                        <tr>
                            <td>Uniform(0, θ)</td>
                            <td>θ</td>
                            <td>Undefined</td>
                            <td>N/A</td>
                            <td>Order statistics</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Summary Section -->
        <div class="section" id="summary">
            <h2><span class="step-number">7</span>Summary and Key Takeaways</h2>
            
            <div class="cramér-box">
                <h3>The Big Picture</h3>
                <p>The Cramér-Rao bound is a cornerstone of statistical theory that provides fundamental insights into the limits of parameter estimation. Here's what we've learned:</p>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div class="mse-box">
                        <h4>MSE Decomposition</h4>
                        <p>MSE = Bias² + Variance</p>
                        <p>Reveals fundamental tradeoff in estimation</p>
                    </div>
                    
                    <div class="fisher-box">
                        <h4>Fisher Information</h4>
                        <p>Measures data informativeness</p>
                        <p>Higher I(θ) → Better estimation</p>
                    </div>
                    
                    <div class="cramér-box">
                        <h4>Cramér-Rao Bound</h4>
                        <p>Var(θ̂) ≥ 1/[nI(θ)]</p>
                        <p>Fundamental limit for unbiased estimators</p>
                    </div>
                    
                    <div class="definition-box">
                        <h4>Decision Theory</h4>
                        <p>Beyond unbiasedness</p>
                        <p>Risk minimization framework</p>
                    </div>
                </div>
            </div>

            <div class="definition-box">
                <h3>When to Use Cramér-Rao Theory</h3>
                <p><strong>✅ Use when:</strong></p>
                <ul>
                    <li>You have a parametric model with known density</li>
                    <li>Regularity conditions are satisfied</li>
                    <li>You want to assess estimator efficiency</li>
                    <li>You need theoretical performance bounds</li>
                    <li>Sample size is reasonably large</li>
                </ul>
                
                <p><strong>⚠️ Be careful when:</strong></p>
                <ul>
                    <li>Parameter space has boundaries</li>
                    <li>Likelihood is not differentiable</li>
                    <li>Support depends on parameter</li>
                    <li>Sample size is very small</li>
                    <li>Model assumptions are questionable</li>
                </ul>
            </div>

            <div class="example-box">
                <h3>Practical Workflow</h3>
                <ol>
                    <li><strong>Model specification:</strong> Define your parametric model $p(x; \theta)$</li>
                    <li><strong>Regularity check:</strong> Verify Cramér-Rao conditions hold</li>
                    <li><strong>Fisher Information:</strong> Calculate $I_1(\theta)$ for single observation</li>
                    <li><strong>Bound computation:</strong> Lower bound is $1/[nI_1(\theta)]$</li>
                    <li><strong>Estimator evaluation:</strong> Compare your estimator's variance to bound</li>
                    <li><strong>Efficiency assessment:</strong> Determine if estimator achieves bound</li>
                    <li><strong>Alternative consideration:</strong> If inefficient, look for better estimators</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <svg width="100%" height="350" viewBox="0 0 900 350">
                    <defs>
                        <linearGradient id="summaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="900" height="350" fill="url(#summaryGradient)" rx="15"/>
                    
                    <text x="450" y="30" text-anchor="middle" fill="#2e7d32" font-size="18" font-weight="bold">The Cramér-Rao Legacy</text>
                    
                    <!-- Central concept -->
                    <circle cx="450" cy="175" r="80" fill="#2e7d32" opacity="0.2" stroke="#2e7d32" stroke-width="3"/>
                    <text x="450" y="170" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Cramér-Rao</text>
                    <text x="450" y="185" text-anchor="middle" fill="#2e7d32" font-size="14" font-weight="bold">Bound</text>
                    
                    <!-- Connected concepts -->
                    <circle cx="200" cy="100" r="50" fill="#1565c0" opacity="0.7"/>
                    <text x="200" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Fisher Info</text>
                    
                    <circle cx="700" cy="100" r="50" fill="#673ab7" opacity="0.7"/>
                    <text x="700" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Efficiency</text>
                    
                    <circle cx="200" cy="250" r="50" fill="#e91e63" opacity="0.7"/>
                    <text x="200" y="255" text-anchor="middle" fill="white" font-size="12" font-weight="bold">MSE</text>
                    
                    <circle cx="700" cy="250" r="50" fill="#f57c00" opacity="0.7"/>
                    <text x="700" y="255" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Decision Theory</text>
                    
                    <!-- Connecting lines -->
                    <line x1="250" y1="120" x2="380" y2="160" stroke="#424242" stroke-width="2"/>
                    <line x1="650" y1="120" x2="520" y2="160" stroke="#424242" stroke-width="2"/>
                    <line x1="250" y1="230" x2="380" y2="190" stroke="#424242" stroke-width="2"/>
                    <line x1="650" y1="230" x2="520" y2="190" stroke="#424242" stroke-width="2"/>
                    
                    <!-- Final message -->
                    <text x="450" y="320" text-anchor="middle" fill="#2e7d32" font-size="14" font-style="italic">
                        "A fundamental limit that guides optimal statistical inference"
                    </text>
                </svg>
            </div>

            <div class="warning-box">
                <h3>Final Thoughts</h3>
                <p>The Cramér-Rao bound represents one of the most elegant and powerful results in statistical theory. It connects information theory (Fisher information), optimization (minimum variance), and practical estimation in a beautiful mathematical framework.</p>
                
                <p>While it has limitations (regularity conditions, unbiasedness restriction), its insights extend far beyond these technical constraints. Modern extensions through Le Cam's theory and asymptotic statistics continue to make the core ideas relevant for contemporary statistical practice.</p>
                
                <p><strong>Remember:</strong> The bound doesn't just tell us what's possible - it tells us what's impossible, and that's often more valuable than any specific estimation procedure.</p>
            </div>
        </div>
    </div>

    <!-- Smooth scrolling script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
